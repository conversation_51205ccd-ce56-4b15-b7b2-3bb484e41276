#!/usr/bin/env python3
"""
Quick test to verify the IFC metadata column fix works
"""

import pandas as pd
from pathlib import Path

# Test the IFC metadata loading
site_name = "trino_enel"
base_path = Path('../../..')
processed_path = base_path / 'data' / 'processed' / site_name

# Load IFC metadata
ifc_metadata_file = processed_path / "ifc_metadata" / "GRE.EEC.S.00.IT.P.14353.00.265_enhanced_metadata.csv"

print(f"Loading: {ifc_metadata_file}")
print(f"File exists: {ifc_metadata_file.exists()}")

if ifc_metadata_file.exists():
    ifc_df = pd.read_csv(ifc_metadata_file)
    print(f"\nDataFrame shape: {ifc_df.shape}")
    print(f"Available columns: {list(ifc_df.columns)}")
    
    # Test pile filtering
    if 'Type' in ifc_df.columns:
        pile_df = ifc_df[ifc_df['Type'].str.contains('Pile', case=False, na=False)].copy()
        print(f"\nFound {len(pile_df)} pile elements using 'Type' column")
        
        # Test coordinate columns
        if 'X' in pile_df.columns and 'Y' in pile_df.columns and 'Z' in pile_df.columns:
            pile_df = pile_df.rename(columns={'X': 'x_coord', 'Y': 'y_coord', 'Z': 'z_coord'})
            print(f"Coordinate ranges:")
            print(f"  X: [{pile_df['x_coord'].min():.2f}, {pile_df['x_coord'].max():.2f}]")
            print(f"  Y: [{pile_df['y_coord'].min():.2f}, {pile_df['y_coord'].max():.2f}]")
            print(f"  Z: [{pile_df['z_coord'].min():.2f}, {pile_df['z_coord'].max():.2f}]")
            
            # Test pile_number column
            if 'Tag' in pile_df.columns:
                pile_df['pile_number'] = pile_df['Tag']
                print(f"\nSample pile data:")
                print(pile_df[['pile_number', 'x_coord', 'y_coord', 'z_coord']].head())
                print("\n✅ Column fix successful!")
            else:
                print("❌ No 'Tag' column for pile numbers")
        else:
            print("❌ Missing coordinate columns")
    else:
        print("❌ No 'Type' column found")
else:
    print("❌ IFC metadata file not found")
