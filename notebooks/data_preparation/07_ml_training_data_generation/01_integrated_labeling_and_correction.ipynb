{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Integrated ML Training Data Generation\n", "\n", "This notebook integrates auto-labeling from IFC metadata with ML-based local correction to generate training data for both classic ML and deep learning models.\n", "\n", "**Workflow**:\n", "1. Load aligned point clouds and IFC metadata\n", "2. Apply ML-based local corrections (if available)\n", "3. Generate labeled training patches\n", "4. Export data for Classic ML (Random Forest) and Deep Learning (PointNet++/DGCNN)\n", "\n", "**Author**: <PERSON><PERSON><PERSON>  \n", "**Date**: August 2025  \n", "**Project**: As-Built Foundation Analysis"]}, {"cell_type": "markdown", "metadata": {"tags": ["parameters"]}, "source": ["## Papermill Parameters"]}, {"cell_type": "code", "execution_count": 1, "metadata": {"tags": ["parameters"]}, "outputs": [], "source": ["# Papermill parameters\n", "site_name = \"trino_enel\"\n", "ground_method = \"ransac_pmf\"\n", "use_ml_correction = True\n", "generate_classic_ml_data = True\n", "generate_deep_learning_data = True\n", "patch_size = 2.0  # meters\n", "min_points_per_patch = 50\n", "save_results = True"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Setup and Data Loading"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-08-08 17:59:14,790 - INFO - === INTEGRATED ML TRAINING DATA GENERATION ===\n", "2025-08-08 17:59:14,790 - INFO - Site: trino_enel\n", "2025-08-08 17:59:14,791 - INFO - Ground method: ransac_pmf\n", "2025-08-08 17:59:14,791 - INFO - Use ML correction: True\n", "2025-08-08 17:59:14,791 - INFO - Output path: ../../../data/output_runs/ml_training_data/trino_enel_20250808_175914\n"]}], "source": ["import numpy as np\n", "import pandas as pd\n", "import open3d as o3d\n", "import matplotlib.pyplot as plt\n", "from pathlib import Path\n", "import json\n", "import joblib\n", "from datetime import datetime\n", "from sklearn.neighbors import NearestNeighbors\n", "from sklearn.preprocessing import StandardScaler\n", "import logging\n", "\n", "# Setup logging\n", "logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')\n", "logger = logging.getLogger(__name__)\n", "\n", "# Setup paths\n", "base_path = Path('../../..')\n", "data_path = base_path / 'data'\n", "processed_path = data_path / 'processed' / site_name\n", "output_path = data_path / 'output_runs' / 'ml_training_data' / f\"{site_name}_{datetime.now().strftime('%Y%m%d_%H%M%S')}\"\n", "output_path.mkdir(parents=True, exist_ok=True)\n", "\n", "logger.info(f\"=== INTEGRATED ML TRAINING DATA GENERATION ===\")\n", "logger.info(f\"Site: {site_name}\")\n", "logger.info(f\"Ground method: {ground_method}\")\n", "logger.info(f\"Use ML correction: {use_ml_correction}\")\n", "logger.info(f\"Output path: {output_path}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 1: Load Point Cloud Data"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-08-08 17:59:14,817 - INFO - Loaded drone point cloud: 517002 points\n", "2025-08-08 17:59:14,819 - INFO - Point cloud bounds: X[435220.82, 436795.41], Y[5010813.78, 5012552.58], Z[-0.71, 28.19]\n"]}], "source": ["# Load ground-segmented point cloud\n", "drone_file = processed_path / \"ground_segmentation\" / ground_method / f\"{site_name}_nonground.ply\"\n", "\n", "if not drone_file.exists():\n", "    raise FileNotFoundError(f\"Ground-segmented point cloud not found: {drone_file}\")\n", "\n", "drone_pcd = o3d.io.read_point_cloud(str(drone_file))\n", "drone_points = np.asarray(drone_pcd.points)\n", "\n", "logger.info(f\"Loaded drone point cloud: {len(drone_points)} points\")\n", "logger.info(f\"Point cloud bounds: X[{drone_points[:, 0].min():.2f}, {drone_points[:, 0].max():.2f}], Y[{drone_points[:, 1].min():.2f}, {drone_points[:, 1].max():.2f}], Z[{drone_points[:, 2].min():.2f}, {drone_points[:, 2].max():.2f}]\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 2: Apply ML-Based Local Correction (Optional)"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-08-08 17:59:14,824 - INFO - Loading ML-corrected point cloud...\n", "2025-08-08 17:59:14,848 - INFO - Using ML-corrected points: 517002 points\n", "2025-08-08 17:59:14,953 - INFO - Loaded trained ML model for feature engineering\n"]}], "source": ["if use_ml_correction:\n", "    # Check if ML-corrected data exists\n", "    ml_corrected_file = processed_path / \"ml_local_alignment\" / f\"{site_name}_ml_corrected.ply\"\n", "    ml_model_file = processed_path / \"ml_local_alignment\" / f\"{site_name}_rf_model.joblib\"\n", "    \n", "    if ml_corrected_file.exists():\n", "        logger.info(\"Loading ML-corrected point cloud...\")\n", "        corrected_pcd = o3d.io.read_point_cloud(str(ml_corrected_file))\n", "        corrected_points = np.asarray(corrected_pcd.points)\n", "        \n", "        # Use corrected points for training data generation\n", "        working_points = corrected_points\n", "        logger.info(f\"Using ML-corrected points: {len(working_points)} points\")\n", "        \n", "        # Load the trained model for feature engineering\n", "        if ml_model_file.exists():\n", "            ml_model = joblib.load(ml_model_file)\n", "            logger.info(\"Loaded trained ML model for feature engineering\")\n", "        else:\n", "            ml_model = None\n", "            logger.warning(\"ML model not found - will use basic features only\")\n", "    else:\n", "        logger.warning(\"ML-corrected data not found - using original points\")\n", "        working_points = drone_points\n", "        ml_model = None\n", "else:\n", "    logger.info(\"Using original point cloud (ML correction disabled)\")\n", "    working_points = drone_points\n", "    ml_model = None"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 3: Load IFC Metadata"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-08-08 17:59:14,988 - INFO - Available columns: ['GlobalId', 'Name', 'Type', 'Description', 'Tag', 'Site_Latitude', 'Site_Longitude', 'Site_Elevation', 'X', 'Y', 'Z', 'GeometryExtracted', 'Longitude', 'Latitude']\n", "2025-08-08 17:59:14,992 - INFO - Loaded IFC metadata: 14460 total elements\n", "2025-08-08 17:59:14,992 - INFO - Found pile elements: 0 piles\n"]}, {"ename": "ValueError", "evalue": "No pile elements found in IFC metadata", "output_type": "error", "traceback": ["\u001b[31m---------------------------------------------------------------------------\u001b[39m", "\u001b[31m<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\u001b[39m                                <PERSON><PERSON> (most recent call last)", "\u001b[36mCell\u001b[39m\u001b[36m \u001b[39m\u001b[32mIn[5]\u001b[39m\u001b[32m, line 50\u001b[39m\n\u001b[32m     48\u001b[39m     logger.info(pile_df[[\u001b[33m'\u001b[39m\u001b[33mpile_number\u001b[39m\u001b[33m'\u001b[39m, \u001b[33m'\u001b[39m\u001b[33mx_coord\u001b[39m\u001b[33m'\u001b[39m, \u001b[33m'\u001b[39m\u001b[33my_coord\u001b[39m\u001b[33m'\u001b[39m, \u001b[33m'\u001b[39m\u001b[33mz_coord\u001b[39m\u001b[33m'\u001b[39m]].head())\n\u001b[32m     49\u001b[39m \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[32m---> \u001b[39m\u001b[32m50\u001b[39m     \u001b[38;5;28;01mraise\u001b[39;00m \u001b[38;5;167;01mValueError\u001b[39;00m(\u001b[33m\"\u001b[39m\u001b[33mNo pile elements found in IFC metadata\u001b[39m\u001b[33m\"\u001b[39m)\n", "\u001b[31mValueError\u001b[39m: No pile elements found in IFC metadata"]}], "source": ["# Load IFC pile metadata\n", "ifc_metadata_file = processed_path / \"ifc_metadata\" / \"GRE.EEC.S.00.IT.P.14353.00.265_enhanced_metadata.csv\"\n", "\n", "if not ifc_metadata_file.exists():\n", "    raise FileNotFoundError(f\"IFC metadata not found: {ifc_metadata_file}\")\n", "\n", "ifc_df = pd.read_csv(ifc_metadata_file)\n", "\n", "# Filter for pile elements - check available columns first\n", "logger.info(f\"Available columns: {list(ifc_df.columns)}\")\n", "\n", "# Filter for pile elements using correct column names\n", "if 'Type' in ifc_df.columns:\n", "    pile_df = ifc_df[ifc_df['Type'].str.contains('Pile', case=False, na=False)].copy()\n", "elif 'element_type' in ifc_df.columns:\n", "    pile_df = ifc_df[ifc_df['element_type'].str.contains('Pile', case=False, na=False)].copy()\n", "elif 'Name' in ifc_df.columns:\n", "    pile_df = ifc_df[ifc_df['Name'].str.contains('Pile', case=False, na=False)].copy()\n", "else:\n", "    raise ValueError(\"No suitable column found for pile filtering. Available columns: \" + str(list(ifc_df.columns)))\n", "\n", "# Standardize column names for coordinates\n", "if 'X' in pile_df.columns and 'Y' in pile_df.columns and 'Z' in pile_df.columns:\n", "    pile_df = pile_df.rename(columns={'X': 'x_coord', 'Y': 'y_coord', 'Z': 'z_coord'})\n", "elif 'x_coord' not in pile_df.columns:\n", "    raise ValueError(\"No coordinate columns found. Available columns: \" + str(list(pile_df.columns)))\n", "\n", "logger.info(f\"Loaded IFC metadata: {len(ifc_df)} total elements\")\n", "logger.info(f\"Found pile elements: {len(pile_df)} piles\")\n", "\n", "# Display pile statistics\n", "if len(pile_df) > 0:\n", "    logger.info(f\"Pile coordinate ranges:\")\n", "    logger.info(f\"  X: [{pile_df['x_coord'].min():.2f}, {pile_df['x_coord'].max():.2f}]\")\n", "    logger.info(f\"  Y: [{pile_df['y_coord'].min():.2f}, {pile_df['y_coord'].max():.2f}]\")\n", "    logger.info(f\"  Z: [{pile_df['z_coord'].min():.2f}, {pile_df['z_coord'].max():.2f}]\")\n", "    \n", "    # Add pile_number column if not present\n", "    if 'pile_number' not in pile_df.columns:\n", "        if 'Tag' in pile_df.columns:\n", "            pile_df['pile_number'] = pile_df['Tag']\n", "        elif 'Name' in pile_df.columns:\n", "            pile_df['pile_number'] = pile_df['Name']\n", "        else:\n", "            pile_df['pile_number'] = pile_df.index.astype(str)\n", "    \n", "    logger.info(f\"Sample pile data:\")\n", "    logger.info(pile_df[['pile_number', 'x_coord', 'y_coord', 'z_coord']].head())\n", "else:\n", "    raise ValueError(\"No pile elements found in IFC metadata\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 4: Generate Training Patches"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def extract_patches_around_piles(points, pile_locations, patch_size=2.0, min_points=50):\n", "    \"\"\"Extract point cloud patches around pile locations\"\"\"\n", "    \n", "    patches = []\n", "    labels = []\n", "    pile_ids = []\n", "    \n", "    for idx, pile in pile_locations.iterrows():\n", "        pile_x, pile_y, pile_z = pile['x_coord'], pile['y_coord'], pile['z_coord']\n", "        pile_id = pile.get('pile_number', f\"pile_{idx}\")\n", "        \n", "        # Extract points within patch_size radius\n", "        distances = np.sqrt((points[:, 0] - pile_x)**2 + (points[:, 1] - pile_y)**2)\n", "        patch_mask = distances <= patch_size\n", "        \n", "        patch_points = points[patch_mask]\n", "        \n", "        if len(patch_points) >= min_points:\n", "            # Center the patch around the pile location\n", "            centered_patch = patch_points - np.array([pile_x, pile_y, 0])\n", "            \n", "            patches.append(centered_patch)\n", "            labels.append(1)  # <PERSON><PERSON> present\n", "            pile_ids.append(pile_id)\n", "    \n", "    return patches, labels, pile_ids\n", "\n", "def extract_negative_patches(points, pile_locations, patch_size=2.0, min_points=50, num_negatives=None):\n", "    \"\"\"Extract negative patches (no piles) for training\"\"\"\n", "    \n", "    if num_negatives is None:\n", "        num_negatives = len(pile_locations)  # Same number as positive patches\n", "    \n", "    patches = []\n", "    labels = []\n", "    pile_ids = []\n", "    \n", "    # Get point cloud bounds\n", "    x_min, x_max = points[:, 0].min(), points[:, 0].max()\n", "    y_min, y_max = points[:, 1].min(), points[:, 1].max()\n", "    \n", "    # Generate random locations avoiding pile areas\n", "    attempts = 0\n", "    max_attempts = num_negatives * 10\n", "    \n", "    while len(patches) < num_negatives and attempts < max_attempts:\n", "        # Random location\n", "        rand_x = np.random.uniform(x_min + patch_size, x_max - patch_size)\n", "        rand_y = np.random.uniform(y_min + patch_size, y_max - patch_size)\n", "        \n", "        # Check if too close to any pile\n", "        pile_distances = np.sqrt((pile_locations['x_coord'] - rand_x)**2 + (pile_locations['y_coord'] - rand_y)**2)\n", "        \n", "        if pile_distances.min() > patch_size * 1.5:  # Ensure separation from piles\n", "            # Extract points in this area\n", "            distances = np.sqrt((points[:, 0] - rand_x)**2 + (points[:, 1] - rand_y)**2)\n", "            patch_mask = distances <= patch_size\n", "            \n", "            patch_points = points[patch_mask]\n", "            \n", "            if len(patch_points) >= min_points:\n", "                # Center the patch\n", "                centered_patch = patch_points - np.array([rand_x, rand_y, 0])\n", "                \n", "                patches.append(centered_patch)\n", "                labels.append(0)  # No pile\n", "                pile_ids.append(f\"negative_{len(patches)}\")\n", "        \n", "        attempts += 1\n", "    \n", "    return patches, labels, pile_ids\n", "\n", "# Extract positive patches (around piles)\n", "logger.info(\"Extracting positive patches around piles...\")\n", "pos_patches, pos_labels, pos_ids = extract_patches_around_piles(\n", "    working_points, pile_df, patch_size, min_points_per_patch\n", ")\n", "\n", "# Extract negative patches (no piles)\n", "logger.info(\"Extracting negative patches...\")\n", "neg_patches, neg_labels, neg_ids = extract_negative_patches(\n", "    working_points, pile_df, patch_size, min_points_per_patch, len(pos_patches)\n", ")\n", "\n", "# Combine all patches\n", "all_patches = pos_patches + neg_patches\n", "all_labels = pos_labels + neg_labels\n", "all_ids = pos_ids + neg_ids\n", "\n", "logger.info(f\"Generated training patches:\")\n", "logger.info(f\"  Positive patches (piles): {len(pos_patches)}\")\n", "logger.info(f\"  Negative patches (no piles): {len(neg_patches)}\")\n", "logger.info(f\"  Total patches: {len(all_patches)}\")\n", "logger.info(f\"  Average points per patch: {np.mean([len(p) for p in all_patches]):.1f}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 5: Generate Classic ML Features"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["if generate_classic_ml_data:\n", "    logger.info(\"Generating features for classic ML models...\")\n", "    \n", "    def extract_patch_features(patch):\n", "        \"\"\"Extract engineered features from a point cloud patch\"\"\"\n", "        \n", "        if len(patch) == 0:\n", "            return np.zeros(15)  # Return zero features for empty patches\n", "        \n", "        features = []\n", "        \n", "        # Basic statistics\n", "        features.append(len(patch))  # Point count\n", "        features.extend(patch.mean(axis=0))  # Mean X, Y, Z\n", "        features.extend(patch.std(axis=0))   # Std X, Y, Z\n", "        \n", "        # Height statistics\n", "        z_values = patch[:, 2]\n", "        features.append(z_values.max() - z_values.min())  # Height range\n", "        features.append(np.percentile(z_values, 95) - np.percentile(z_values, 5))  # 90th percentile range\n", "        \n", "        # Spatial distribution\n", "        xy_distances = np.sqrt(patch[:, 0]**2 + patch[:, 1]**2)\n", "        features.append(xy_distances.mean())  # Mean distance from center\n", "        features.append(xy_distances.std())   # Std distance from center\n", "        \n", "        # Density features\n", "        if len(patch) > 1:\n", "            # Nearest neighbor distances\n", "            nbrs = NearestNeighbors(n_neighbors=min(5, len(patch)), algorithm='ball_tree').fit(patch)\n", "            distances, indices = nbrs.kneighbors(patch)\n", "            features.append(distances[:, 1:].mean())  # Mean NN distance (excluding self)\n", "        else:\n", "            features.append(0)\n", "        \n", "        # Geometric features\n", "        if len(patch) >= 3:\n", "            # Covariance matrix eigenvalues (shape descriptors)\n", "            cov_matrix = np.cov(patch.T)\n", "            eigenvals = np.linalg.eigvals(cov_matrix)\n", "            eigenvals = np.sort(eigenvals)[::-1]  # Sort descending\n", "            \n", "            # Linearity, planarity, sphericity\n", "            if eigenvals[0] > 0:\n", "                features.append((eigenvals[0] - eigenvals[1]) / eigenvals[0])  # Linearity\n", "                features.append((eigenvals[1] - eigenvals[2]) / eigenvals[0])  # Planarity\n", "                features.append(eigenvals[2] / eigenvals[0])                   # Sphericity\n", "            else:\n", "                features.extend([0, 0, 0])\n", "        else:\n", "            features.extend([0, 0, 0])\n", "        \n", "        return np.array(features)\n", "    \n", "    # Extract features for all patches\n", "    patch_features = []\n", "    for patch in all_patches:\n", "        features = extract_patch_features(patch)\n", "        patch_features.append(features)\n", "    \n", "    patch_features = np.array(patch_features)\n", "    \n", "    # Normalize features\n", "    scaler = StandardScaler()\n", "    patch_features_normalized = scaler.fit_transform(patch_features)\n", "    \n", "    logger.info(f\"Generated classic ML features: {patch_features.shape[1]} features per patch\")\n", "    \n", "    # Save classic ML data\n", "    if save_results:\n", "        classic_ml_file = output_path / f\"{site_name}_classic_ml_data.npz\"\n", "        np.savez_compressed(\n", "            classic_ml_file,\n", "            features=patch_features_normalized,\n", "            labels=np.array(all_labels),\n", "            patch_ids=all_ids,\n", "            feature_names=[\n", "                'point_count', 'mean_x', 'mean_y', 'mean_z', 'std_x', 'std_y', 'std_z',\n", "                'height_range', 'height_p90_range', 'mean_radial_dist', 'std_radial_dist',\n", "                'mean_nn_dist', 'linearity', 'planarity', 'sphericity'\n", "            ]\n", "        )\n", "        \n", "        # Save scaler\n", "        scaler_file = output_path / f\"{site_name}_feature_scaler.joblib\"\n", "        joblib.dump(scaler, scaler_file)\n", "        \n", "        logger.info(f\"Saved classic ML data: {classic_ml_file}\")\n", "        logger.info(f\"Saved feature scaler: {scaler_file}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 6: Generate Deep Learning Data"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["if generate_deep_learning_data:\n", "    logger.info(\"Generating data for deep learning models (PointNet++/DGCNN)...\")\n", "    \n", "    def normalize_patch_for_dl(patch, target_points=1024):\n", "        \"\"\"Normalize patch for deep learning models\"\"\"\n", "        \n", "        if len(patch) == 0:\n", "            return np.zeros((target_points, 3))\n", "        \n", "        # Resample to target number of points\n", "        if len(patch) > target_points:\n", "            # Downsample\n", "            indices = np.random.choice(len(patch), target_points, replace=False)\n", "            normalized_patch = patch[indices]\n", "        elif len(patch) < target_points:\n", "            # Upsample with repetition\n", "            indices = np.random.choice(len(patch), target_points, replace=True)\n", "            normalized_patch = patch[indices]\n", "        else:\n", "            normalized_patch = patch.copy()\n", "        \n", "        # Normalize coordinates to [-1, 1] range\n", "        if normalized_patch.std() > 0:\n", "            normalized_patch = (normalized_patch - normalized_patch.mean(axis=0)) / (normalized_patch.std(axis=0) + 1e-8)\n", "            normalized_patch = np.clip(normalized_patch, -3, 3)  # Clip outliers\n", "        \n", "        return normalized_patch\n", "    \n", "    # Normalize all patches for deep learning\n", "    target_points = 1024  # Standard for PointNet++/DGCNN\n", "    dl_patches = []\n", "    \n", "    for patch in all_patches:\n", "        normalized_patch = normalize_patch_for_dl(patch, target_points)\n", "        dl_patches.append(normalized_patch)\n", "    \n", "    dl_patches = np.array(dl_patches)  # Shape: (N, 1024, 3)\n", "    dl_labels = np.array(all_labels)   # Shape: (N,)\n", "    \n", "    logger.info(f\"Generated deep learning data: {dl_patches.shape}\")\n", "    \n", "    # Split into train/validation/test\n", "    n_samples = len(dl_patches)\n", "    indices = np.random.permutation(n_samples)\n", "    \n", "    train_split = int(0.7 * n_samples)\n", "    val_split = int(0.85 * n_samples)\n", "    \n", "    train_indices = indices[:train_split]\n", "    val_indices = indices[train_split:val_split]\n", "    test_indices = indices[val_split:]\n", "    \n", "    # Save deep learning data\n", "    if save_results:\n", "        # Save complete dataset\n", "        dl_file = output_path / f\"{site_name}_deep_learning_data.npz\"\n", "        np.savez_compressed(\n", "            dl_file,\n", "            patches=dl_patches,\n", "            labels=dl_labels,\n", "            patch_ids=all_ids,\n", "            train_indices=train_indices,\n", "            val_indices=val_indices,\n", "            test_indices=test_indices\n", "        )\n", "        \n", "        # Save individual splits for convenience\n", "        train_file = output_path / f\"{site_name}_train_data.npz\"\n", "        val_file = output_path / f\"{site_name}_val_data.npz\"\n", "        test_file = output_path / f\"{site_name}_test_data.npz\"\n", "        \n", "        np.savez_compressed(train_file, patches=dl_patches[train_indices], labels=dl_labels[train_indices])\n", "        np.savez_compressed(val_file, patches=dl_patches[val_indices], labels=dl_labels[val_indices])\n", "        np.savez_compressed(test_file, patches=dl_patches[test_indices], labels=dl_labels[test_indices])\n", "        \n", "        logger.info(f\"Saved deep learning data: {dl_file}\")\n", "        logger.info(f\"Train samples: {len(train_indices)}, Val: {len(val_indices)}, Test: {len(test_indices)}\")\n", "        \n", "        # Save some sample patches as PLY files for visualization\n", "        sample_dir = output_path / \"sample_patches\"\n", "        sample_dir.mkdir(exist_ok=True)\n", "        \n", "        for i in range(min(10, len(all_patches))):\n", "            patch = all_patches[i]\n", "            label = all_labels[i]\n", "            patch_id = all_ids[i]\n", "            \n", "            # Save as PLY\n", "            pcd = o3d.geometry.PointCloud()\n", "            pcd.points = o3d.utility.Vector3dVector(patch)\n", "            \n", "            # Color by label (red for pile, blue for non-pile)\n", "            if label == 1:\n", "                pcd.paint_uniform_color([1.0, 0.0, 0.0])  # Red for pile\n", "            else:\n", "                pcd.paint_uniform_color([0.0, 0.0, 1.0])  # Blue for non-pile\n", "            \n", "            sample_file = sample_dir / f\"patch_{i:03d}_{patch_id}_label_{label}.ply\"\n", "            o3d.io.write_point_cloud(str(sample_file), pcd)\n", "        \n", "        logger.info(f\"Saved sample patches: {sample_dir}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 7: Generate Summary Report"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Generate comprehensive summary\n", "summary = {\n", "    \"generation_timestamp\": datetime.now().isoformat(),\n", "    \"site_name\": site_name,\n", "    \"ground_method\": ground_method,\n", "    \"ml_correction_used\": use_ml_correction,\n", "    \"patch_parameters\": {\n", "        \"patch_size_meters\": patch_size,\n", "        \"min_points_per_patch\": min_points_per_patch,\n", "        \"target_points_dl\": 1024\n", "    },\n", "    \"data_statistics\": {\n", "        \"total_point_cloud_points\": len(working_points),\n", "        \"total_ifc_piles\": len(pile_df),\n", "        \"positive_patches\": len(pos_patches),\n", "        \"negative_patches\": len(neg_patches),\n", "        \"total_patches\": len(all_patches),\n", "        \"avg_points_per_patch\": float(np.mean([len(p) for p in all_patches])),\n", "        \"min_points_per_patch\": int(np.min([len(p) for p in all_patches])),\n", "        \"max_points_per_patch\": int(np.max([len(p) for p in all_patches]))\n", "    },\n", "    \"output_files\": {\n", "        \"classic_ml_data\": f\"{site_name}_classic_ml_data.npz\" if generate_classic_ml_data else None,\n", "        \"deep_learning_data\": f\"{site_name}_deep_learning_data.npz\" if generate_deep_learning_data else None,\n", "        \"feature_scaler\": f\"{site_name}_feature_scaler.joblib\" if generate_classic_ml_data else None\n", "    },\n", "    \"data_splits\": {\n", "        \"train_samples\": len(train_indices) if generate_deep_learning_data else None,\n", "        \"val_samples\": len(val_indices) if generate_deep_learning_data else None,\n", "        \"test_samples\": len(test_indices) if generate_deep_learning_data else None\n", "    } if generate_deep_learning_data else None\n", "}\n", "\n", "# Save summary\n", "if save_results:\n", "    summary_file = output_path / f\"{site_name}_training_data_summary.json\"\n", "    with open(summary_file, 'w') as f:\n", "        json.dump(summary, f, indent=2)\n", "    \n", "    logger.info(f\"Saved summary report: {summary_file}\")\n", "\n", "# Display summary\n", "logger.info(\"\\n\" + \"=\"*60)\n", "logger.info(\"TRAINING DATA GENERATION COMPLETE\")\n", "logger.info(\"=\"*60)\n", "logger.info(f\"Site: {site_name}\")\n", "logger.info(f\"Ground segmentation method: {ground_method}\")\n", "logger.info(f\"ML correction applied: {use_ml_correction}\")\n", "logger.info(f\"\")\n", "logger.info(f\"Generated data:\")\n", "logger.info(f\"  Total patches: {len(all_patches)}\")\n", "logger.info(f\"  Positive (pile) patches: {len(pos_patches)}\")\n", "logger.info(f\"  Negative (non-pile) patches: {len(neg_patches)}\")\n", "logger.info(f\"  Average points per patch: {np.mean([len(p) for p in all_patches]):.1f}\")\n", "logger.info(f\"\")\n", "logger.info(f\"Output formats:\")\n", "if generate_classic_ml_data:\n", "    logger.info(f\"  ✓ Classic ML data (Random Forest): {patch_features.shape[1]} features\")\n", "if generate_deep_learning_data:\n", "    logger.info(f\"  ✓ Deep learning data (PointNet++/DGCNN): {dl_patches.shape}\")\n", "logger.info(f\"\")\n", "logger.info(f\"Results saved to: {output_path}\")\n", "logger.info(\"=\"*60)"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 4}