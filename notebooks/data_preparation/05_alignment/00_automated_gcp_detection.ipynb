{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Automated Ground Control Point Detection\n", "\n", "This notebook implements automated detection of Ground Control Points (GCPs) using computer vision techniques for point cloud alignment.\n", "\n", "**Key Features:**\n", "- Corner feature detection using OpenCV\n", "- Isolated high point detection\n", "- Geometric pattern recognition\n", "- Edge feature detection\n", "- Candidate filtering and validation\n", "\n", "**Author**: <PERSON><PERSON><PERSON>  \n", "**Date**: July 2025  \n", "**Project**: As-Built Foundation Analysis"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["=== AUTOMATED GCP DETECTION ===\n", "Ground method: ransac_pmf\n", "Site: trino_enel\n", "Output directory: ../../../data/processed/automated_gcp\n"]}], "source": ["# Parameters (Papermill)\n", "ground_method = \"ransac_pmf\"  # Ground segmentation method\n", "site_name = \"trino_enel\"\n", "output_dir = \"../../../data/processed/automated_gcp\"\n", "save_results = True\n", "\n", "print(\"=== AUTOMATED GCP DETECTION ===\")\n", "print(f\"Ground method: {ground_method}\")\n", "print(f\"Site: {site_name}\")\n", "print(f\"Output directory: {output_dir}\")"]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [], "source": ["# Imports and Setup\n", "import numpy as np\n", "import cv2\n", "import open3d as o3d\n", "import matplotlib.pyplot as plt\n", "from sklearn.cluster import DBSCAN\n", "from scipy.spatial import cKDTree, distance, ConvexHull\n", "import pandas as pd\n", "from pathlib import Path\n", "import warnings\n", "warnings.filterwarnings('ignore')"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Data Loading"]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Loading data for automated GCP detection...\n", "Drone file: ../../../data/processed/trino_enel/ground_segmentation/ransac_pmf/trino_enel_nonground.ply\n", "IFC metadata file: ../../../data/processed/trino_enel/ifc_metadata/GRE.EEC.S.00.IT.P.14353.00.265_enhanced_metadata.csv\n", "IFC point cloud file: ../../../data/processed/trino_enel/ifc_pointclouds/GRE.EEC.S.00.IT.P.14353.00.265_data_driven.ply\n"]}], "source": ["# Define file paths\n", "drone_file = f\"../../../data/processed/{site_name}/ground_segmentation/{ground_method}/{site_name}_nonground.ply\"\n", "ifc_metadata_file = f\"../../../data/processed/{site_name}/ifc_metadata/GRE.EEC.S.00.IT.P.14353.00.265_enhanced_metadata.csv\"\n", "ifc_pointcloud_file = f\"../../../data/processed/{site_name}/ifc_pointclouds/GRE.EEC.S.00.IT.P.14353.00.265_data_driven.ply\"\n", "\n", "print(\"Loading data for automated GCP detection...\")\n", "print(f\"Drone file: {drone_file}\")\n", "print(f\"IFC metadata file: {ifc_metadata_file}\")\n", "print(f\"IFC point cloud file: {ifc_pointcloud_file}\")"]}, {"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Loaded drone points: 504,475\n", "Loaded IFC points: 5,480,340\n"]}], "source": ["# Load drone point cloud\n", "drone_pcd = o3d.io.read_point_cloud(drone_file)\n", "drone_points = np.asarray(drone_pcd.points)\n", "print(f\"Loaded drone points: {len(drone_points):,}\")\n", "\n", "# Load IFC point cloud\n", "ifc_pcd = o3d.io.read_point_cloud(ifc_pointcloud_file)\n", "ifc_points = np.asarray(ifc_pcd.points)\n", "print(f\"Loaded IFC points: {len(ifc_points):,}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Automated GCP Detection Methods"]}, {"cell_type": "code", "execution_count": 19, "metadata": {}, "outputs": [], "source": ["def detect_corner_features(points, grid_size=1.0):\n", "    \"\"\"Detect corner features using OpenCV Harris corner detection\"\"\"\n", "    try:\n", "        # Create 2D grid from point cloud\n", "        x_min, y_min = points[:, :2].min(axis=0)\n", "        x_max, y_max = points[:, :2].max(axis=0)\n", "        \n", "        grid_x = int((x_max - x_min) / grid_size) + 1\n", "        grid_y = int((y_max - y_min) / grid_size) + 1\n", "        \n", "        # Create height grid\n", "        height_grid = np.zeros((grid_y, grid_x), dtype=np.float32)\n", "        \n", "        for point in points:\n", "            x_idx = int((point[0] - x_min) / grid_size)\n", "            y_idx = int((point[1] - y_min) / grid_size)\n", "            if 0 <= x_idx < grid_x and 0 <= y_idx < grid_y:\n", "                height_grid[y_idx, x_idx] = max(height_grid[y_idx, x_idx], point[2])\n", "        \n", "        # Normalize for OpenCV\n", "        height_grid = cv2.normalize(height_grid, None, 0, 255, cv2.NORM_MINMAX).astype(np.uint8)\n", "        \n", "        # Harris corner detection\n", "        corners = cv2.corner<PERSON><PERSON><PERSON>(height_grid, 2, 3, 0.04)\n", "        \n", "        # Find corner coordinates\n", "        corner_coords = np.where(corners > 0.01 * corners.max())\n", "        \n", "        # Convert back to world coordinates\n", "        world_corners = []\n", "        for y_idx, x_idx in zip(corner_coords[0], corner_coords[1]):\n", "            world_x = x_min + x_idx * grid_size\n", "            world_y = y_min + y_idx * grid_size\n", "            world_z = height_grid[y_idx, x_idx]\n", "            world_corners.append([world_x, world_y, world_z])\n", "        \n", "        return np.array(world_corners)\n", "    \n", "    except Exception as e:\n", "        print(f\"Corner detection failed: {e}\")\n", "        return np.array([])"]}, {"cell_type": "code", "execution_count": 20, "metadata": {}, "outputs": [], "source": ["def detect_isolated_high_points(points, height_threshold_percentile=85, cluster_eps=2.0, min_samples=5):\n", "    \"\"\"Detect isolated high points that could be GCPs\"\"\"\n", "    # Filter high points\n", "    height_threshold = np.percentile(points[:, 2], height_threshold_percentile)\n", "    high_points = points[points[:, 2] > height_threshold]\n", "    \n", "    print(f\"Found {len(high_points)} points above {height_threshold:.2f}m\")\n", "    \n", "    if len(high_points) == 0:\n", "        return np.array([])\n", "    \n", "    # Cluster high points\n", "    clustering = DBSCAN(eps=cluster_eps, min_samples=min_samples)\n", "    cluster_labels = clustering.fit_predict(high_points[:, :2])\n", "    \n", "    # Get cluster centers\n", "    unique_labels = set(cluster_labels)\n", "    cluster_centers = []\n", "    \n", "    for label in unique_labels:\n", "        if label != -1:  # Ignore noise points\n", "            cluster_points = high_points[cluster_labels == label]\n", "            center = np.mean(cluster_points, axis=0)\n", "            cluster_centers.append(center)\n", "    \n", "    print(f\"Found {len(cluster_centers)} isolated high point clusters\")\n", "    return np.array(cluster_centers)"]}, {"cell_type": "code", "execution_count": 21, "metadata": {}, "outputs": [], "source": ["def detect_geometric_patterns(points, grid_spacing=3.0, tolerance=0.5):\n", "    \"\"\"Detect points that follow geometric patterns (regular spacing)\"\"\"\n", "    # Sample points for pattern detection\n", "    sample_size = min(10000, len(points))\n", "    sample_indices = np.random.choice(len(points), sample_size, replace=False)\n", "    sample_points = points[sample_indices]\n", "    \n", "    pattern_points = []\n", "    \n", "    # Look for points that align with a regular grid\n", "    x_min, y_min = sample_points[:, :2].min(axis=0)\n", "    \n", "    for point in sample_points:\n", "        # Check if point aligns with grid\n", "        x_grid = round((point[0] - x_min) / grid_spacing) * grid_spacing + x_min\n", "        y_grid = round((point[1] - y_min) / grid_spacing) * grid_spacing + y_min\n", "        \n", "        if (abs(point[0] - x_grid) < tolerance and \n", "            abs(point[1] - y_grid) < tolerance):\n", "            pattern_points.append(point)\n", "    \n", "    print(f\"Found {len(pattern_points)} points in regular patterns\")\n", "    return np.array(pattern_points)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Execute Automated Detection"]}, {"cell_type": "code", "execution_count": 22, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Starting automated GCP detection...\n", "Processing 504,475 drone points\n", "Using methods: corners, high_points, patterns\n", "\n", "=== METHOD 1: CORNER FEATURE DETECTION ===\n", "\n", "=== METHOD 2: ISOLATED HIGH POINT DETECTION ===\n", "Found 75657 points above 2.91m\n", "Found 2858 isolated high point clusters\n", "\n", "=== METHOD 3: GEOMETRIC PATTERN DETECTION ===\n", "Found 1125 points in regular patterns\n", "\n", "=== COMBINING AND FILTERING CANDIDATES ===\n", "Final candidates after filtering: 4552\n", "\n", "=== DRONE DETECTION COMPLETE ===\n", "Found 4552 drone GCP candidates\n"]}], "source": ["def automated_gcp_detection(drone_points, methods=['corners', 'high_points', 'patterns']):\n", "    \"\"\"Run automated GCP detection using multiple methods\"\"\"\n", "    print(f\"Starting automated GCP detection...\")\n", "    print(f\"Processing {len(drone_points):,} drone points\")\n", "    print(f\"Using methods: {', '.join(methods)}\")\n", "    \n", "    all_candidates = []\n", "    \n", "    # Method 1: Corner detection\n", "    if 'corners' in methods:\n", "        print(\"\\n=== METHOD 1: CORNER FEATURE DETECTION ===\")\n", "        corner_candidates = detect_corner_features(drone_points)\n", "        if len(corner_candidates) > 0:\n", "            all_candidates.append(corner_candidates)\n", "    \n", "    # Method 2: High point detection\n", "    if 'high_points' in methods:\n", "        print(\"\\n=== METHOD 2: ISOLATED HIGH POINT DETECTION ===\")\n", "        high_point_candidates = detect_isolated_high_points(drone_points)\n", "        if len(high_point_candidates) > 0:\n", "            all_candidates.append(high_point_candidates)\n", "    \n", "    # Method 3: Pattern detection\n", "    if 'patterns' in methods:\n", "        print(\"\\n=== METHOD 3: GEOMETRIC PATTERN DETECTION ===\")\n", "        pattern_candidates = detect_geometric_patterns(drone_points)\n", "        if len(pattern_candidates) > 0:\n", "            all_candidates.append(pattern_candidates)\n", "    \n", "    # <PERSON><PERSON>ine all candidates\n", "    if all_candidates:\n", "        combined_candidates = np.vstack(all_candidates)\n", "        \n", "        # Remove duplicates using clustering\n", "        print(\"\\n=== COMBINING AND FILTERING CANDIDATES ===\")\n", "        clustering = DBSCAN(eps=1.0, min_samples=1)\n", "        cluster_labels = clustering.fit_predict(combined_candidates[:, :2])\n", "        \n", "        # Get unique candidates (cluster centers)\n", "        unique_labels = set(cluster_labels)\n", "        final_candidates = []\n", "        \n", "        for label in unique_labels:\n", "            if label != -1:\n", "                cluster_points = combined_candidates[cluster_labels == label]\n", "                center = np.mean(cluster_points, axis=0)\n", "                final_candidates.append(center)\n", "        \n", "        final_candidates = np.array(final_candidates)\n", "        print(f\"Final candidates after filtering: {len(final_candidates)}\")\n", "        \n", "        return final_candidates\n", "    else:\n", "        print(\"No candidates found\")\n", "        return np.array([])\n", "\n", "# Run automated detection on drone data\n", "drone_gcp_candidates = automated_gcp_detection(drone_points)\n", "print(f\"\\n=== DRONE DETECTION COMPLETE ===\")\n", "print(f\"Found {len(drone_gcp_candidates)} drone GCP candidates\")\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## IFC Feature Detection"]}, {"cell_type": "code", "execution_count": 23, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "Detecting IFC features from 5,480,340 points...\n", "Using 50,000 sampled IFC points\n", "\n", "=== IFC CORNER FEATURE DETECTION ===\n", "Found 55196 IFC corner features\n", "\n", "=== IFC STRUCTURAL ELEMENT DETECTION ===\n", "Found 12500 points above 158.48m\n", "Found 3 isolated high point clusters\n", "Found 3 IFC structural features\n", "\n", "Final IFC candidates after filtering: 11513\n", "\n", "=== IFC DETECTION COMPLETE ===\n", "Found 11513 IFC GCP candidates\n"]}], "source": ["def detect_ifc_features(ifc_points, methods=['corners', 'high_points'], sample_size=50000):\n", "    \"\"\"Detect features in IFC point cloud (with sampling for performance)\"\"\"\n", "    print(f\"\\nDetecting IFC features from {len(ifc_points):,} points...\")\n", "    \n", "    # Sample IFC points for performance\n", "    if len(ifc_points) > sample_size:\n", "        indices = np.random.choice(len(ifc_points), sample_size, replace=False)\n", "        ifc_sample = ifc_points[indices]\n", "        print(f\"Using {len(ifc_sample):,} sampled IFC points\")\n", "    else:\n", "        ifc_sample = ifc_points\n", "    \n", "    all_candidates = []\n", "    \n", "    # Method 1: Corner detection\n", "    if 'corners' in methods:\n", "        print(\"\\n=== IFC CORNER FEATURE DETECTION ===\")\n", "        corner_candidates = detect_corner_features(ifc_sample, grid_size=2.0)\n", "        if len(corner_candidates) > 0:\n", "            all_candidates.append(corner_candidates)\n", "            print(f\"Found {len(corner_candidates)} IFC corner features\")\n", "    \n", "    # Method 2: High point detection (structural elements)\n", "    if 'high_points' in methods:\n", "        print(\"\\n=== IFC STRUCTURAL ELEMENT DETECTION ===\")\n", "        # Use lower percentile for IFC as it has more uniform distribution\n", "        high_point_candidates = detect_isolated_high_points(\n", "            ifc_sample, height_threshold_percentile=75, cluster_eps=3.0, min_samples=10\n", "        )\n", "        if len(high_point_candidates) > 0:\n", "            all_candidates.append(high_point_candidates)\n", "            print(f\"Found {len(high_point_candidates)} IFC structural features\")\n", "    \n", "    # Combine IFC candidates\n", "    if all_candidates:\n", "        combined_candidates = np.vstack(all_candidates)\n", "        \n", "        # Remove duplicates\n", "        clustering = DBSCAN(eps=2.0, min_samples=1)\n", "        cluster_labels = clustering.fit_predict(combined_candidates[:, :2])\n", "        \n", "        unique_labels = set(cluster_labels)\n", "        final_candidates = []\n", "        \n", "        for label in unique_labels:\n", "            if label != -1:\n", "                cluster_points = combined_candidates[cluster_labels == label]\n", "                center = np.mean(cluster_points, axis=0)\n", "                final_candidates.append(center)\n", "        \n", "        final_candidates = np.array(final_candidates)\n", "        print(f\"\\nFinal IFC candidates after filtering: {len(final_candidates)}\")\n", "        return final_candidates\n", "    else:\n", "        print(\"No IFC candidates found\")\n", "        return np.array([])\n", "\n", "# Detect IFC features\n", "ifc_gcp_candidates = detect_ifc_features(ifc_points)\n", "print(f\"\\n=== IFC DETECTION COMPLETE ===\")\n", "print(f\"Found {len(ifc_gcp_candidates)} IFC GCP candidates\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Spatial Matching with <PERSON><PERSON><PERSON><PERSON>"]}, {"cell_type": "code", "execution_count": 24, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "Matching 4552 drone candidates with 11513 IFC candidates...\n", "Maximum matching distance: 15.0m\n", "Match 1: <PERSON><PERSON> [ 436283.087 5011070.816] <-> IFC [ 436284.16695688 5011077.69142313] (dist: 6.96m, conf: 0.536)\n", "Match 2: <PERSON><PERSON> [ 436280.587 5011072.316] <-> IFC [ 436284.16695688 5011077.69142313] (dist: 6.46m, conf: 0.569)\n", "Match 3: <PERSON><PERSON> [ 436269.78980612 5011079.99388775] <-> IFC [ 436274.16695688 5011087.69142313] (dist: 8.86m, conf: 0.410)\n", "Match 4: <PERSON><PERSON> [ 436061.38555449 5011112.57549679] <-> IFC [ 436066.16695688 5011115.69142313] (dist: 5.71m, conf: 0.620)\n", "Match 5: <PERSON><PERSON> [ 436060.587 5011116.316] <-> IFC [ 436066.16695688 5011115.69142313] (dist: 5.61m, conf: 0.626)\n", "Match 6: <PERSON><PERSON> [ 435485.587 5011583.816] <-> IFC [ 435486.16695688 5011583.69142313] (dist: 0.59m, conf: 0.960)\n", "Match 7: <PERSON><PERSON> [ 435490.587 5011584.816] <-> IFC [ 435486.16695688 5011583.69142313] (dist: 4.56m, conf: 0.696)\n", "Match 8: <PERSON><PERSON> [ 436301.587 5011587.816] <-> IFC [ 436306.16695688 5011591.69142313] (dist: 6.00m, conf: 0.600)\n", "Match 9: <PERSON><PERSON> [ 435486.587 5011588.816] <-> IFC [ 435486.16695688 5011593.69142313] (dist: 4.89m, conf: 0.674)\n", "Match 10: <PERSON><PERSON> [ 436292.587 5011590.816] <-> IFC [ 436296.16695688 5011591.69142313] (dist: 3.69m, conf: 0.754)\n", "Match 11: <PERSON><PERSON> [ 435571.92033333 5011596.48266667] <-> IFC [ 435572.16695688 5011593.69142313] (dist: 2.80m, conf: 0.813)\n", "Match 12: <PERSON><PERSON> [ 435571.82323117 5011600.20764156] <-> IFC [ 435571.16695688 5011605.69142313] (dist: 5.52m, conf: 0.632)\n", "Match 13: <PERSON><PERSON> [ 435570.25366667 5011603.14933333] <-> IFC [ 435571.16695688 5011605.69142313] (dist: 2.70m, conf: 0.820)\n", "Match 14: <PERSON><PERSON> [ 436299.587 5011616.816] <-> IFC [ 436296.16695688 5011617.69142313] (dist: 3.53m, conf: 0.765)\n", "Match 15: <PERSON><PERSON> [ 435834.4514     5011621.87606667] <-> IFC [ 435838.16695688 5011626.69142313] (dist: 6.08m, conf: 0.595)\n", "Match 16: <PERSON><PERSON> [ 435837.587 5011621.816] <-> IFC [ 435838.16695688 5011626.69142313] (dist: 4.91m, conf: 0.673)\n", "Match 17: <PERSON><PERSON> [ 435833.587 5011625.816] <-> IFC [ 435838.16695688 5011626.69142313] (dist: 4.66m, conf: 0.689)\n", "Match 18: <PERSON><PERSON> [ 435769.087 5011631.816] <-> IFC [ 435770.16695688 5011633.69142313] (dist: 2.16m, conf: 0.856)\n", "Match 19: <PERSON><PERSON> [ 435772.087 5011631.816] <-> IFC [ 435770.16695688 5011633.69142313] (dist: 2.68m, conf: 0.821)\n", "Match 20: <PERSON><PERSON> [ 435835.44049702 5011680.74366369] <-> IFC [ 435828.16695688 5011681.69142313] (dist: 7.34m, conf: 0.511)\n", "Match 21: <PERSON><PERSON> [ 435837.92033333 5011682.14933333] <-> IFC [ 435846.16695688 5011683.69142313] (dist: 8.39m, conf: 0.441)\n", "Match 22: <PERSON><PERSON> [ 435949.55598592 5011681.68417606] <-> IFC [ 435942.16695688 5011681.69142313] (dist: 7.39m, conf: 0.507)\n", "Match 23: <PERSON><PERSON> [ 435948.787 5011685.416] <-> IFC [ 435942.16695688 5011681.69142313] (dist: 7.60m, conf: 0.494)\n", "Match 24: <PERSON><PERSON> [ 435957.76558009 5011705.9467922 ] <-> IFC [ 435960.16695688 5011705.69142313] (dist: 2.41m, conf: 0.839)\n", "Match 25: <PERSON><PERSON> [ 435949.25366667 5011704.48266667] <-> IFC [ 435952.16695688 5011707.69142313] (dist: 4.33m, conf: 0.711)\n", "Match 26: <PERSON><PERSON> [ 435950.76070588 5011706.42986765] <-> IFC [ 435952.16695688 5011707.69142313] (dist: 1.89m, conf: 0.874)\n", "Match 27: <PERSON><PERSON> [ 435733.587 5011776.816] <-> IFC [ 435732.16695688 5011769.69142313] (dist: 7.26m, conf: 0.516)\n", "Match 28: <PERSON><PERSON> [ 435338.3563 5011798.9035] <-> IFC [ 435334.16695688 5011797.69142313] (dist: 4.36m, conf: 0.709)\n", "Match 29: <PERSON><PERSON> [ 435338.587 5011800.816] <-> IFC [ 435334.16695688 5011797.69142313] (dist: 5.41m, conf: 0.639)\n", "Match 30: <PERSON><PERSON> [ 435340.587 5011808.816] <-> IFC [ 435344.16695688 5011811.69142313] (dist: 4.59m, conf: 0.694)\n", "Match 31: <PERSON><PERSON> [ 435337.587 5011809.816] <-> IFC [ 435334.16695688 5011805.69142313] (dist: 5.36m, conf: 0.643)\n", "Match 32: <PERSON><PERSON> [ 435339.587 5011809.816] <-> IFC [ 435344.16695688 5011811.69142313] (dist: 4.95m, conf: 0.670)\n", "Match 33: <PERSON><PERSON> [ 435748.587 5011836.816] <-> IFC [ 435752.16695688 5011837.69142313] (dist: 3.69m, conf: 0.754)\n", "Match 34: <PERSON><PERSON> [ 435693.337 5011894.566] <-> IFC [ 435694.16695688 5011895.69142313] (dist: 1.40m, conf: 0.907)\n", "Match 35: <PERSON><PERSON> [ 435686.587 5011896.316] <-> IFC [ 435685.16695688 5011897.69142313] (dist: 1.98m, conf: 0.868)\n", "Match 36: <PERSON><PERSON> [ 435690.587 5011896.816] <-> IFC [ 435694.16695688 5011895.69142313] (dist: 3.75m, conf: 0.750)\n", "Match 37: <PERSON><PERSON> [ 435692.837 5011899.066] <-> IFC [ 435694.16695688 5011895.69142313] (dist: 3.63m, conf: 0.758)\n", "Match 38: <PERSON><PERSON> [ 435695.587 5011897.816] <-> IFC [ 435694.16695688 5011895.69142313] (dist: 2.56m, conf: 0.830)\n", "Match 39: <PERSON><PERSON> [ 435797.587 5011975.816] <-> IFC [ 435800.16695688 5011975.69142313] (dist: 2.58m, conf: 0.828)\n", "Match 40: <PERSON><PERSON> [ 435441.587      5012016.14933333] <-> IFC [ 435448.16695688 5012015.69142313] (dist: 6.60m, conf: 0.560)\n", "Match 41: <PERSON><PERSON> [ 435449.587 5012015.816] <-> IFC [ 435448.16695688 5012015.69142313] (dist: 1.43m, conf: 0.905)\n", "Match 42: <PERSON><PERSON> [ 435452.25366667 5012016.14933333] <-> IFC [ 435448.16695688 5012015.69142313] (dist: 4.11m, conf: 0.726)\n", "Match 43: <PERSON><PERSON> [ 435444.587 5012017.816] <-> IFC [ 435448.16695688 5012015.69142313] (dist: 4.16m, conf: 0.722)\n", "Match 44: <PERSON><PERSON> [ 435451.587 5012019.066] <-> IFC [ 435448.16695688 5012015.69142313] (dist: 4.80m, conf: 0.680)\n", "Match 45: <PERSON><PERSON> [ 435438.587 5012019.816] <-> IFC [ 435438.16695688 5012025.69142313] (dist: 5.89m, conf: 0.607)\n", "Match 46: <PERSON><PERSON> [ 435883.587 5012090.816] <-> IFC [ 435884.16695688 5012091.69142313] (dist: 1.05m, conf: 0.930)\n", "Match 47: <PERSON><PERSON> [ 436413.587 5012215.816] <-> IFC [ 436414.16695688 5012217.69142313] (dist: 1.96m, conf: 0.869)\n", "Match 48: <PERSON><PERSON> [ 436255.79690426 5012267.15268085] <-> IFC [ 436254.16695688 5012263.69142313] (dist: 3.83m, conf: 0.745)\n", "Match 49: <PERSON><PERSON> [ 436144.06485714 5012375.849     ] <-> IFC [ 436140.16695688 5012373.69142313] (dist: 4.46m, conf: 0.703)\n", "Match 50: <PERSON><PERSON> [ 436293.04576316 5012150.52415789] <-> IFC [ 436292.16695688 5012146.69142313] (dist: 3.93m, conf: 0.738)\n", "Match 51: <PERSON><PERSON> [ 435779.45565625 5011269.933875  ] <-> IFC [ 435780.16695688 5011261.69142313] (dist: 8.27m, conf: 0.448)\n", "Match 52: <PERSON><PERSON> [ 436105.45437931 5012330.96298276] <-> IFC [ 436110.16695688 5012331.69142313] (dist: 4.77m, conf: 0.682)\n", "Match 53: <PERSON><PERSON> [ 435713.67537879 5011435.90236364] <-> IFC [ 435714.16695688 5011439.69142313] (dist: 3.82m, conf: 0.745)\n", "Match 54: <PERSON><PERSON> [ 436151.88246429 5012372.78867857] <-> IFC [ 436148.16695688 5012369.69142313] (dist: 4.84m, conf: 0.678)\n", "Match 55: <PERSON><PERSON> [ 435936.52856522 5012232.80376087] <-> IFC [ 435940.16695688 5012235.69142313] (dist: 4.65m, conf: 0.690)\n", "Match 56: <PERSON><PERSON> [ 436312.16389362 5012322.36704255] <-> IFC [ 436310.16695688 5012325.69142313] (dist: 3.88m, conf: 0.741)\n", "Match 57: <PERSON><PERSON> [ 436363.34228571 5011104.94085714] <-> IFC [ 436360.16695688 5011109.69142313] (dist: 5.71m, conf: 0.619)\n", "Match 58: <PERSON><PERSON> [ 435798.3984878  5011332.29617073] <-> IFC [ 435799.16695688 5011327.69142314] (dist: 4.67m, conf: 0.689)\n", "Match 59: <PERSON><PERSON> [ 435506.49672222 5011606.95405556] <-> IFC [ 435504.16695688 5011603.69142313] (dist: 4.01m, conf: 0.733)\n", "Match 60: <PERSON><PERSON> [ 436236.92863095 5012242.97920238] <-> IFC [ 436234.16695688 5012247.69142313] (dist: 5.46m, conf: 0.636)\n", "Match 61: <PERSON><PERSON> [ 436011.92084211 5012294.53936842] <-> IFC [ 436016.16695688 5012295.69142313] (dist: 4.40m, conf: 0.707)\n", "Match 62: <PERSON><PERSON> [ 435451.0214 5011632.4242] <-> IFC [ 435448.16695688 5011631.69142313] (dist: 2.95m, conf: 0.804)\n", "Match 63: <PERSON><PERSON> [ 436324.6485     5011962.06966667] <-> IFC [ 436322.16695688 5011959.69142313] (dist: 3.44m, conf: 0.771)\n", "Match 64: <PERSON><PERSON> [ 436463.00451429 5012076.74992857] <-> IFC [ 436462.16695688 5012073.69142313] (dist: 3.17m, conf: 0.789)\n", "Match 65: <PERSON><PERSON> [ 436434.53233333 5012146.27866667] <-> IFC [ 436434.16695688 5012143.69142313] (dist: 2.61m, conf: 0.826)\n", "Match 66: <PERSON><PERSON> [ 435901.24504878 5011472.40073171] <-> IFC [ 435904.16695688 5011471.69142313] (dist: 3.01m, conf: 0.800)\n", "Match 67: <PERSON><PERSON> [ 436595.83027273 5011799.25172727] <-> IFC [ 436596.16695688 5011801.69142313] (dist: 2.46m, conf: 0.836)\n", "Match 68: <PERSON><PERSON> [ 436227.44622449 5012262.96983673] <-> IFC [ 436224.16695688 5012263.69142313] (dist: 3.36m, conf: 0.776)\n", "Match 69: <PERSON><PERSON> [ 436013.81434211 5011635.05444737] <-> IFC [ 436018.16695688 5011635.69142313] (dist: 4.40m, conf: 0.707)\n", "Match 70: <PERSON><PERSON> [ 435667.07904587 5011408.69236697] <-> IFC [ 435666.16695688 5011405.69142313] (dist: 3.14m, conf: 0.791)\n", "Match 71: <PERSON><PERSON> [ 436274.3344186  5012248.34262791] <-> IFC [ 436272.16695688 5012245.69142313] (dist: 3.42m, conf: 0.772)\n", "Match 72: <PERSON><PERSON> [ 435843.985      5011439.14757143] <-> IFC [ 435846.16695688 5011435.69142313] (dist: 4.09m, conf: 0.728)\n", "Match 73: <PERSON><PERSON> [ 435657.25310811 5011411.03135135] <-> IFC [ 435656.16695688 5011407.69142313] (dist: 3.51m, conf: 0.766)\n", "Match 74: <PERSON><PERSON> [ 436492.75666667 5011920.21      ] <-> IFC [ 436492.16695688 5011915.69142313] (dist: 4.56m, conf: 0.696)\n", "Match 75: <PERSON><PERSON> [ 436444.68776923 5012056.9691282 ] <-> IFC [ 436444.16695688 5012055.69142313] (dist: 1.38m, conf: 0.908)\n", "Match 76: <PERSON><PERSON> [ 435891.2052093  5012099.06722093] <-> IFC [ 435894.16695688 5012095.69142313] (dist: 4.49m, conf: 0.701)\n", "Match 77: <PERSON><PERSON> [ 436462.51338636 5012137.64720455] <-> IFC [ 436462.16695688 5012133.69142313] (dist: 3.97m, conf: 0.735)\n", "Match 78: <PERSON><PERSON> [ 436405.97634375 5012146.4413125 ] <-> IFC [ 436406.16695688 5012145.69142313] (dist: 0.77m, conf: 0.948)\n", "Match 79: <PERSON><PERSON> [ 436444.14750318 5012128.42908917] <-> IFC [ 436444.16695688 5012131.69142313] (dist: 3.26m, conf: 0.783)\n", "Match 80: <PERSON><PERSON> [ 436265.69366667 5012243.07672222] <-> IFC [ 436262.16695688 5012241.69142313] (dist: 3.79m, conf: 0.747)\n", "Match 81: <PERSON><PERSON> [ 436245.75843902 5012423.19681301] <-> IFC [ 436244.16695688 5012425.69142313] (dist: 2.96m, conf: 0.803)\n", "Match 82: <PERSON><PERSON> [ 436087.15335294 5012365.92476471] <-> IFC [ 436082.16695688 5012365.69142313] (dist: 4.99m, conf: 0.667)\n", "Match 83: <PERSON><PERSON> [ 436540.11227273 5011820.40684848] <-> IFC [ 436540.16695688 5011823.69142313] (dist: 3.29m, conf: 0.781)\n", "Match 84: <PERSON><PERSON> [ 435657.99446429 5011489.88089286] <-> IFC [ 435656.16695688 5011487.69142313] (dist: 2.85m, conf: 0.810)\n", "Match 85: <PERSON><PERSON> [ 435761.20121053 5011263.87884211] <-> IFC [ 435760.16695688 5011267.69142313] (dist: 3.95m, conf: 0.737)\n", "Match 86: <PERSON><PERSON> [ 436134.84375  5012194.595375] <-> IFC [ 436140.16695688 5012195.69142313] (dist: 5.43m, conf: 0.638)\n", "Match 87: <PERSON><PERSON> [ 436662.18864 5011821.78066] <-> IFC [ 436662.16695688 5011819.69142313] (dist: 2.09m, conf: 0.861)\n", "Match 88: <PERSON><PERSON> [ 436208.53879032 5012450.62582258] <-> IFC [ 436206.16695688 5012451.69142313] (dist: 2.60m, conf: 0.827)\n", "Match 89: <PERSON><PERSON> [ 436253.4382 5011530.3914] <-> IFC [ 436250.16695688 5011527.69142313] (dist: 4.24m, conf: 0.717)\n", "Match 90: <PERSON><PERSON> [ 436052.89588235 5011015.09252941] <-> IFC [ 436056.16695688 5011017.69142313] (dist: 4.18m, conf: 0.721)\n", "Match 91: <PERSON><PERSON> [ 435928.007875 5012124.6825  ] <-> IFC [ 435922.16695688 5012119.69142313] (dist: 7.68m, conf: 0.488)\n", "Match 92: <PERSON><PERSON> [ 436321.59507143 5012269.91766667] <-> IFC [ 436320.16695688 5012271.69142313] (dist: 2.28m, conf: 0.848)\n", "Match 93: <PERSON><PERSON> [ 436246.2472     5012198.82130909] <-> IFC [ 436244.16695688 5012195.69142313] (dist: 3.76m, conf: 0.749)\n", "Match 94: <PERSON><PERSON> [ 435732.21987179 5011302.96405128] <-> IFC [ 435732.16695688 5011303.69142313] (dist: 0.73m, conf: 0.951)\n", "Match 95: <PERSON><PERSON> [ 435891.15766667 5012186.85519048] <-> IFC [ 435894.16695688 5012189.69142313] (dist: 4.14m, conf: 0.724)\n", "Match 96: <PERSON><PERSON> [ 436180.532 5012380.852] <-> IFC [ 436178.16695688 5012377.69142313] (dist: 3.95m, conf: 0.737)\n", "Match 97: <PERSON><PERSON> [ 436249.02621053 5012011.17015789] <-> IFC [ 436246.16695688 5012013.69142313] (dist: 3.81m, conf: 0.746)\n", "Match 98: <PERSON><PERSON> [ 435938.73884483 5012114.18144828] <-> IFC [ 435942.16695688 5012111.69142313] (dist: 4.24m, conf: 0.718)\n", "Match 99: <PERSON><PERSON> [ 435948.23416667 5012151.9655641 ] <-> IFC [ 435951.16695688 5012155.69142313] (dist: 4.74m, conf: 0.684)\n", "Match 100: <PERSON><PERSON> [ 436146.2462     5011627.40853333] <-> IFC [ 436142.16695688 5011621.69142313] (dist: 7.02m, conf: 0.532)\n", "Match 101: <PERSON><PERSON> [ 435909.77575 5011613.34975] <-> IFC [ 435914.16695688 5011613.69142313] (dist: 4.40m, conf: 0.706)\n", "Match 102: <PERSON><PERSON> [ 436089.33078 5011749.70494] <-> IFC [ 436084.16695688 5011751.69142313] (dist: 5.53m, conf: 0.631)\n", "Match 103: <PERSON><PERSON> [ 435751.14616058 5011287.85756205] <-> IFC [ 435752.16695688 5011289.69142313] (dist: 2.10m, conf: 0.860)\n", "Match 104: <PERSON><PERSON> [ 436153.17418421 5012269.93607895] <-> IFC [ 436148.16695688 5012269.69142313] (dist: 5.01m, conf: 0.666)\n", "Match 105: <PERSON><PERSON> [ 436292.89848148 5012357.96931481] <-> IFC [ 436292.16695688 5012357.69142313] (dist: 0.78m, conf: 0.948)\n", "Match 106: <PERSON><PERSON> [ 436396.83818919 5012203.11524324] <-> IFC [ 436396.16695688 5012199.69142313] (dist: 3.49m, conf: 0.767)\n", "Match 107: <PERSON><PERSON> [ 436236.27443939 5012441.47757576] <-> IFC [ 436234.16695688 5012437.69142313] (dist: 4.33m, conf: 0.711)\n", "Match 108: <PERSON><PERSON> [ 436435.13978125 5012106.5031875 ] <-> IFC [ 436434.16695688 5012109.69142313] (dist: 3.33m, conf: 0.778)\n", "Match 109: <PERSON><PERSON> [ 436461.75525  5012161.768875] <-> IFC [ 436462.16695688 5012159.69142313] (dist: 2.12m, conf: 0.859)\n", "Match 110: <PERSON><PERSON> [ 436527.76225 5012032.456  ] <-> IFC [ 436528.16695688 5012031.69142313] (dist: 0.87m, conf: 0.942)\n", "Match 111: <PERSON><PERSON> [ 436311.71545455 5012107.93854545] <-> IFC [ 436310.16695688 5012105.69142313] (dist: 2.73m, conf: 0.818)\n", "Match 112: <PERSON><PERSON> [ 436096.17954545 5012375.57665455] <-> IFC [ 436092.16695688 5012371.69142313] (dist: 5.59m, conf: 0.628)\n", "Match 113: <PERSON><PERSON> [ 436237.4548125  5012382.68096875] <-> IFC [ 436234.16695688 5012385.69142313] (dist: 4.46m, conf: 0.703)\n", "Match 114: <PERSON><PERSON> [ 435825.89536364 5011446.71662121] <-> IFC [ 435828.16695688 5011447.69142313] (dist: 2.47m, conf: 0.835)\n", "Match 115: <PERSON><PERSON> [ 436236.47059091 5012406.08040909] <-> IFC [ 436234.16695688 5012403.69142313] (dist: 3.32m, conf: 0.779)\n", "Match 116: <PERSON><PERSON> [ 435732.55516667 5011782.564     ] <-> IFC [ 435724.16695688 5011781.69142313] (dist: 8.43m, conf: 0.438)\n", "Match 117: <PERSON><PERSON> [ 436152.65796774 5012403.81164516] <-> IFC [ 436148.16695688 5012403.69142313] (dist: 4.49m, conf: 0.700)\n", "Match 118: <PERSON><PERSON> [ 436099.82222727 5011811.29636364] <-> IFC [ 436103.16695688 5011813.69142313] (dist: 4.11m, conf: 0.726)\n", "Match 119: <PERSON><PERSON> [ 436067.52135714 5012280.65946429] <-> IFC [ 436064.16695688 5012279.69142313] (dist: 3.49m, conf: 0.767)\n", "Match 120: <PERSON><PERSON> [ 435920.16062857 5012156.68997143] <-> IFC [ 435922.16695688 5012153.69142313] (dist: 3.61m, conf: 0.759)\n", "Match 121: <PERSON><PERSON> [ 436518.16585714 5012062.78557143] <-> IFC [ 436520.16695688 5012061.69142313] (dist: 2.28m, conf: 0.848)\n", "Match 122: <PERSON><PERSON> [ 436557.22867797 5012023.28935593] <-> IFC [ 436558.16695688 5012021.69142313] (dist: 1.85m, conf: 0.876)\n", "Match 123: <PERSON><PERSON> [ 436353.557425 5011197.516975] <-> IFC [ 436350.16695688 5011197.69142313] (dist: 3.39m, conf: 0.774)\n", "Match 124: <PERSON><PERSON> [ 435928.46545455 5012015.94463636] <-> IFC [ 435932.16695688 5012019.69142313] (dist: 5.27m, conf: 0.649)\n", "Match 125: <PERSON><PERSON> [ 435629.52351282 5011412.3345641 ] <-> IFC [ 435628.16695688 5011411.69142313] (dist: 1.50m, conf: 0.900)\n", "Match 126: <PERSON><PERSON> [ 436396.8781413  5012224.67579348] <-> IFC [ 436396.16695688 5012225.69142313] (dist: 1.24m, conf: 0.917)\n", "Match 127: <PERSON><PERSON> [ 436171.91073913 5012143.19191304] <-> IFC [ 436168.16695688 5012142.69142313] (dist: 3.78m, conf: 0.748)\n", "Match 128: <PERSON><PERSON> [ 436124.11191111 5012328.9174    ] <-> IFC [ 436120.16695688 5012327.69142313] (dist: 4.13m, conf: 0.725)\n", "Match 129: <PERSON><PERSON> [ 435994.82619178 5012003.59771233] <-> IFC [ 435998.16695688 5012001.69142313] (dist: 3.85m, conf: 0.744)\n", "Match 130: <PERSON><PERSON> [ 436190.06902703 5012224.64489189] <-> IFC [ 436186.16695688 5012223.69142313] (dist: 4.02m, conf: 0.732)\n", "Match 131: <PERSON><PERSON> [ 435948.24684444 5012050.5062    ] <-> IFC [ 435951.16695688 5012055.69142314] (dist: 5.95m, conf: 0.603)\n", "Match 132: <PERSON><PERSON> [ 435779.3707  5011239.20485] <-> IFC [ 435780.16695688 5011235.69142313] (dist: 3.60m, conf: 0.760)\n", "Match 133: <PERSON><PERSON> [ 436068.75438462 5012308.70630769] <-> IFC [ 436072.16695688 5012309.69142313] (dist: 3.55m, conf: 0.763)\n", "Match 134: <PERSON><PERSON> [ 436325.19860714 5011082.05264286] <-> IFC [ 436322.16695688 5011083.69142313] (dist: 3.45m, conf: 0.770)\n", "Match 135: <PERSON><PERSON> [ 436001.93873333 5012243.4322    ] <-> IFC [ 436006.16695688 5012247.69142313] (dist: 6.00m, conf: 0.600)\n", "Match 136: <PERSON><PERSON> [ 435394.138   5011995.02975] <-> IFC [ 435390.16695688 5011991.69142313] (dist: 5.19m, conf: 0.654)\n", "Match 137: <PERSON><PERSON> [ 436633.83314286 5011791.73722857] <-> IFC [ 436634.16695688 5011795.69142313] (dist: 3.97m, conf: 0.735)\n", "Match 138: <PERSON><PERSON> [ 436434.84522034 5012130.84081356] <-> IFC [ 436434.16695688 5012127.69142313] (dist: 3.22m, conf: 0.785)\n", "Match 139: <PERSON><PERSON> [ 436516.806      5011788.49133333] <-> IFC [ 436516.16695688 5011787.69142313] (dist: 1.02m, conf: 0.932)\n", "Match 140: <PERSON><PERSON> [ 436189.8044 5012288.322 ] <-> IFC [ 436186.16695688 5012289.69142313] (dist: 3.89m, conf: 0.741)\n", "Match 141: <PERSON><PERSON> [ 435770.13856164 5011414.58195891] <-> IFC [ 435770.16695688 5011415.69142313] (dist: 1.11m, conf: 0.926)\n", "Match 142: <PERSON><PERSON> [ 435900.1192 5012106.566 ] <-> IFC [ 435904.16695688 5012109.69142313] (dist: 5.11m, conf: 0.659)\n", "Match 143: <PERSON><PERSON> [ 436266.506 5012406.982] <-> IFC [ 436262.16695688 5012409.69142313] (dist: 5.12m, conf: 0.659)\n", "Match 144: <PERSON><PERSON> [ 436406.63660377 5012164.06650943] <-> IFC [ 436406.16695688 5012163.69142313] (dist: 0.60m, conf: 0.960)\n", "Match 145: <PERSON><PERSON> [ 436283.76382813 5012246.10760937] <-> IFC [ 436282.16695688 5012243.69142313] (dist: 2.90m, conf: 0.807)\n", "Match 146: <PERSON><PERSON> [ 436378.19530435 5012238.83565218] <-> IFC [ 436376.16695688 5012241.69142313] (dist: 3.50m, conf: 0.766)\n", "Match 147: <PERSON><PERSON> [ 436483.48312821 5011878.12102564] <-> IFC [ 436482.16695688 5011873.69142313] (dist: 4.62m, conf: 0.692)\n", "Match 148: <PERSON><PERSON> [ 436208.37623256 5012425.80837209] <-> IFC [ 436206.16695688 5012427.69142313] (dist: 2.90m, conf: 0.806)\n", "Match 149: <PERSON><PERSON> [ 435873.81285185 5011493.9867037 ] <-> IFC [ 435876.16695688 5011491.69142313] (dist: 3.29m, conf: 0.781)\n", "Match 150: <PERSON><PERSON> [ 435806.74128571 5012154.47392857] <-> IFC [ 435808.16695688 5012153.69142313] (dist: 1.63m, conf: 0.892)\n", "Match 151: <PERSON><PERSON> [ 435891.49012 5012014.1482 ] <-> IFC [ 435894.16695688 5012015.69142313] (dist: 3.09m, conf: 0.794)\n", "Match 152: <PERSON><PERSON> [ 435482.71330392 5011586.89472549] <-> IFC [ 435486.16695688 5011583.69142313] (dist: 4.71m, conf: 0.686)\n", "Match 153: <PERSON><PERSON> [ 436387.20118519 5012173.25001852] <-> IFC [ 436386.16695688 5012169.69142313] (dist: 3.71m, conf: 0.753)\n", "Match 154: <PERSON><PERSON> [ 435779.44357895 5011253.30639474] <-> IFC [ 435780.16695688 5011251.69142313] (dist: 1.77m, conf: 0.882)\n", "Match 155: <PERSON><PERSON> [ 436002.45833333 5012252.66116667] <-> IFC [ 436006.16695688 5012255.69142313] (dist: 4.79m, conf: 0.681)\n", "Match 156: <PERSON><PERSON> [ 436209.285      5012182.94655555] <-> IFC [ 436206.16695688 5012179.69142313] (dist: 4.51m, conf: 0.699)\n", "Match 157: <PERSON><PERSON> [ 436039.37810169 5012343.20913559] <-> IFC [ 436044.16695688 5012345.69142313] (dist: 5.39m, conf: 0.640)\n", "Match 158: <PERSON><PERSON> [ 435685.55971154 5011383.53621154] <-> IFC [ 435686.16695688 5011381.69142313] (dist: 1.94m, conf: 0.871)\n", "Match 159: <PERSON><PERSON> [ 436352.50031034 5011944.12027586] <-> IFC [ 436350.16695688 5011947.69142313] (dist: 4.27m, conf: 0.716)\n", "Match 160: <PERSON><PERSON> [ 436624.4721875 5011859.22325  ] <-> IFC [ 436624.16695688 5011859.69142313] (dist: 0.56m, conf: 0.963)\n", "Match 161: <PERSON><PERSON> [ 436049.01871429 5012290.1472381 ] <-> IFC [ 436054.16695688 5012291.69142313] (dist: 5.37m, conf: 0.642)\n", "Match 162: <PERSON><PERSON> [ 436151.97539024 5012423.07282927] <-> IFC [ 436158.16695688 5012425.69142313] (dist: 6.72m, conf: 0.552)\n", "Match 163: <PERSON><PERSON> [ 435657.62666667 5011507.20772222] <-> IFC [ 435656.16695688 5011505.69142313] (dist: 2.10m, conf: 0.860)\n", "Match 164: <PERSON><PERSON> [ 436275.2337 5012123.1873] <-> IFC [ 436272.16695688 5012119.69142313] (dist: 4.65m, conf: 0.690)\n", "Match 165: <PERSON><PERSON> [ 436237.04287179 5012146.39930769] <-> IFC [ 436234.16695688 5012145.69142313] (dist: 2.96m, conf: 0.803)\n", "Match 166: <PERSON><PERSON> [ 436077.53510526 5012275.72950877] <-> IFC [ 436082.16695688 5012273.69142313] (dist: 5.06m, conf: 0.663)\n", "Match 167: <PERSON><PERSON> [ 436406.12558 5012197.51246] <-> IFC [ 436406.16695688 5012197.69142313] (dist: 0.18m, conf: 0.988)\n", "Match 168: <PERSON><PERSON> [ 436624.51968571 5011907.88511429] <-> IFC [ 436624.16695688 5011909.69142313] (dist: 1.84m, conf: 0.877)\n", "Match 169: <PERSON><PERSON> [ 436378.54       5012131.51831034] <-> IFC [ 436376.16695688 5012131.69142313] (dist: 2.38m, conf: 0.841)\n", "Match 170: <PERSON><PERSON> [ 435691.8217013  5011896.52101299] <-> IFC [ 435694.16695688 5011895.69142313] (dist: 2.49m, conf: 0.834)\n", "Match 171: <PERSON><PERSON> [ 436283.50921053 5012381.5541579 ] <-> IFC [ 436282.16695688 5012377.69142313] (dist: 4.09m, conf: 0.727)\n", "Match 172: <PERSON><PERSON> [ 436247.1436087  5012259.13734783] <-> IFC [ 436244.16695688 5012255.69142313] (dist: 4.55m, conf: 0.696)\n", "Match 173: <PERSON><PERSON> [ 436114.85987368 5012319.00482105] <-> IFC [ 436120.16695688 5012319.69142313] (dist: 5.35m, conf: 0.643)\n", "Match 174: <PERSON><PERSON> [ 436277.20722222 5011216.17311111] <-> IFC [ 436274.16695688 5011213.69142313] (dist: 3.92m, conf: 0.738)\n", "Match 175: <PERSON><PERSON> [ 436259.2916875 5012014.127    ] <-> IFC [ 436256.16695688 5012013.69142313] (dist: 3.15m, conf: 0.790)\n", "Match 176: <PERSON><PERSON> [ 435525.927625 5011564.4505  ] <-> IFC [ 435524.16695688 5011559.69142313] (dist: 5.07m, conf: 0.662)\n", "Match 177: <PERSON><PERSON> [ 435986.66790625 5011675.04175   ] <-> IFC [ 435980.16695688 5011671.69142313] (dist: 7.31m, conf: 0.512)\n", "Match 178: <PERSON><PERSON> [ 436339.95717949 5012288.28951282] <-> IFC [ 436338.16695688 5012289.69142313] (dist: 2.27m, conf: 0.848)\n", "Match 179: <PERSON><PERSON> [ 435740.86160606 5012024.09736363] <-> IFC [ 435742.16695688 5012023.69142313] (dist: 1.37m, conf: 0.909)\n", "Match 180: <PERSON><PERSON> [ 436359.50530769 5012160.64711539] <-> IFC [ 436358.16695688 5012163.69142313] (dist: 3.33m, conf: 0.778)\n", "Match 181: <PERSON><PERSON> [ 435751.13772222 5011407.77536111] <-> IFC [ 435752.16695688 5011409.69142313] (dist: 2.17m, conf: 0.855)\n", "Match 182: <PERSON><PERSON> [ 435872.27477419 5012137.44745161] <-> IFC [ 435866.16695688 5012141.69142313] (dist: 7.44m, conf: 0.504)\n", "Match 183: <PERSON><PERSON> [ 435807.59888112 5011425.87563636] <-> IFC [ 435808.16695688 5011433.69142313] (dist: 7.84m, conf: 0.478)\n", "Match 184: <PERSON><PERSON> [ 435478.29109091 5012042.13609091] <-> IFC [ 435476.16695688 5012039.69142313] (dist: 3.24m, conf: 0.784)\n", "Match 185: <PERSON><PERSON> [ 436293.74830435 5012295.23580435] <-> IFC [ 436292.16695688 5012297.69142313] (dist: 2.92m, conf: 0.805)\n", "Match 186: <PERSON><PERSON> [ 435880.17014706 5012264.2452353 ] <-> IFC [ 435882.16695688 5012261.69142313] (dist: 3.24m, conf: 0.784)\n", "Match 187: <PERSON><PERSON> [ 436068.17805263 5012272.31884211] <-> IFC [ 436064.16695688 5012271.69142313] (dist: 4.06m, conf: 0.729)\n", "Match 188: <PERSON><PERSON> [ 436368.34658537 5012253.75107927] <-> IFC [ 436368.16695688 5012253.69142313] (dist: 0.19m, conf: 0.987)\n", "Match 189: <PERSON><PERSON> [ 436137.27130303 5010974.54136364] <-> IFC [ 436141.39772611 5010974.92219236] (dist: 4.14m, conf: 0.724)\n", "Match 190: <PERSON><PERSON> [ 436396.74530841 5012157.74628972] <-> IFC [ 436396.16695688 5012157.69142313] (dist: 0.58m, conf: 0.961)\n", "Match 191: <PERSON><PERSON> [ 435619.8079     5011441.27353333] <-> IFC [ 435618.16695688 5011439.69142313] (dist: 2.28m, conf: 0.848)\n", "Match 192: <PERSON><PERSON> [ 436415.647125   5012198.58496591] <-> IFC [ 436414.16695688 5012201.69142313] (dist: 3.44m, conf: 0.771)\n", "Match 193: <PERSON><PERSON> [ 436511.32072222 5011934.15816667] <-> IFC [ 436510.16695688 5011935.69142313] (dist: 1.92m, conf: 0.872)\n", "Match 194: <PERSON><PERSON> [ 436491.54169231 5012071.72492308] <-> IFC [ 436490.16695688 5012071.69142313] (dist: 1.38m, conf: 0.908)\n", "Match 195: <PERSON><PERSON> [ 435770.48397403 5011281.6202987 ] <-> IFC [ 435770.16695688 5011273.69142313] (dist: 7.94m, conf: 0.471)\n", "Match 196: <PERSON><PERSON> [ 436199.08940909 5012408.80936364] <-> IFC [ 436196.16695688 5012405.69142313] (dist: 4.27m, conf: 0.715)\n", "Match 197: <PERSON><PERSON> [ 435760.26960606 5011287.852     ] <-> IFC [ 435761.16695688 5011281.69142313] (dist: 6.23m, conf: 0.585)\n", "Match 198: <PERSON><PERSON> [ 436359.06276404 5012245.18835955] <-> IFC [ 436358.16695688 5012247.69142313] (dist: 2.66m, conf: 0.823)\n", "Match 199: <PERSON><PERSON> [ 436208.47783333 5012343.82966667] <-> IFC [ 436206.16695688 5012343.69142313] (dist: 2.32m, conf: 0.846)\n", "Match 200: <PERSON><PERSON> [ 435845.30957407 5011408.69516667] <-> IFC [ 435846.16695688 5011410.69142313] (dist: 2.17m, conf: 0.855)\n", "Match 201: <PERSON><PERSON> [ 435874.     5011126.1874] <-> IFC [ 435875.16695688 5011127.69142313] (dist: 1.90m, conf: 0.873)\n", "Match 202: <PERSON><PERSON> [ 436077.77113333 5012307.54105555] <-> IFC [ 436082.16695688 5012307.69142313] (dist: 4.40m, conf: 0.707)\n", "Match 203: <PERSON><PERSON> [ 436425.211      5012175.59709524] <-> IFC [ 436424.16695688 5012173.69142313] (dist: 2.17m, conf: 0.855)\n", "Match 204: <PERSON><PERSON> [ 436208.50754762 5012406.80445238] <-> IFC [ 436206.16695688 5012409.69142313] (dist: 3.72m, conf: 0.752)\n", "Match 205: <PERSON><PERSON> [ 436198.74796875 5012317.31071875] <-> IFC [ 436196.16695688 5012320.69142313] (dist: 4.25m, conf: 0.716)\n", "Match 206: <PERSON><PERSON> [ 436189.6755     5012419.06684524] <-> IFC [ 436186.16695688 5012416.69142313] (dist: 4.24m, conf: 0.718)\n", "Match 207: <PERSON><PERSON> [ 436041.47540476 5012050.5585    ] <-> IFC [ 436046.16695688 5012047.69142313] (dist: 5.50m, conf: 0.633)\n", "Match 208: <PERSON><PERSON> [ 436165.093      5011006.34002222] <-> IFC [ 436160.16695688 5011003.69142313] (dist: 5.59m, conf: 0.627)\n", "Match 209: <PERSON><PERSON> [ 436089.66486667 5012054.92253333] <-> IFC [ 436094.16695688 5012051.69142313] (dist: 5.54m, conf: 0.631)\n", "Match 210: <PERSON><PERSON> [ 436286.49363636 5012034.60272727] <-> IFC [ 436284.16695688 5012035.69142313] (dist: 2.57m, conf: 0.829)\n", "Match 211: <PERSON><PERSON> [ 436096.05955556 5012387.29322222] <-> IFC [ 436092.16695688 5012387.69142313] (dist: 3.91m, conf: 0.739)\n", "Match 212: <PERSON><PERSON> [ 436146.92058824 5010957.4072549 ] <-> IFC [ 436150.16695688 5010961.69142313] (dist: 5.38m, conf: 0.642)\n", "Match 213: <PERSON><PERSON> [ 436161.30348387 5012430.35580645] <-> IFC [ 436158.16695688 5012433.69142313] (dist: 4.58m, conf: 0.695)\n", "Match 214: <PERSON><PERSON> [ 436104.53017647 5012230.34917647] <-> IFC [ 436102.16695688 5012233.69142313] (dist: 4.09m, conf: 0.727)\n", "Match 215: <PERSON><PERSON> [ 435600.6715625 5011451.351    ] <-> IFC [ 435600.16695688 5011447.69142313] (dist: 3.69m, conf: 0.754)\n", "Match 216: <PERSON><PERSON> [ 435543.583875 5011583.826625] <-> IFC [ 435542.16695688 5011585.69142313] (dist: 2.34m, conf: 0.844)\n", "Match 217: <PERSON><PERSON> [ 436433.6973125 5012193.956375 ] <-> IFC [ 436434.16695688 5012195.69142313] (dist: 1.80m, conf: 0.880)\n", "Match 218: <PERSON><PERSON> [ 436137.29155319 5011017.01374468] <-> IFC [ 436142.16695688 5011017.69142313] (dist: 4.92m, conf: 0.672)\n", "Match 219: <PERSON><PERSON> [ 436643.25478082 5011894.11463014] <-> IFC [ 436644.16695688 5011893.69142313] (dist: 1.01m, conf: 0.933)\n", "Match 220: <PERSON><PERSON> [ 436011.65930435 5012333.36804348] <-> IFC [ 436006.16695688 5012331.69142313] (dist: 5.74m, conf: 0.617)\n", "Match 221: <PERSON><PERSON> [ 436227.34715909 5012379.50459091] <-> IFC [ 436224.16695688 5012375.69142313] (dist: 4.97m, conf: 0.669)\n", "Match 222: <PERSON><PERSON> [ 435722.91906522 5011424.41854348] <-> IFC [ 435723.16695688 5011425.69142313] (dist: 1.30m, conf: 0.914)\n", "Match 223: <PERSON><PERSON> [ 435731.90351351 5012015.84416216] <-> IFC [ 435732.16695688 5012016.69142313] (dist: 0.89m, conf: 0.941)\n", "Match 224: <PERSON><PERSON> [ 436330.95       5012239.45042857] <-> IFC [ 436330.16695688 5012241.69142313] (dist: 2.37m, conf: 0.842)\n", "Match 225: <PERSON><PERSON> [ 436331.29672727 5012220.517     ] <-> IFC [ 436330.16695688 5012217.69142313] (dist: 3.04m, conf: 0.797)\n", "Match 226: <PERSON><PERSON> [ 436283.27725926 5012311.45137037] <-> IFC [ 436282.16695688 5012309.69142313] (dist: 2.08m, conf: 0.861)\n", "Match 227: <PERSON><PERSON> [ 436274.122875   5012384.25558333] <-> IFC [ 436272.16695688 5012381.69142313] (dist: 3.22m, conf: 0.785)\n", "Match 228: <PERSON><PERSON> [ 435910.04205 5012032.1233 ] <-> IFC [ 435914.16695688 5012029.69142313] (dist: 4.79m, conf: 0.681)\n", "Match 229: <PERSON><PERSON> [ 436662.09034783 5011840.31930435] <-> IFC [ 436662.16695688 5011835.69142313] (dist: 4.63m, conf: 0.691)\n", "Match 230: <PERSON><PERSON> [ 435740.552375 5011975.710875] <-> IFC [ 435742.16695688 5011973.69142313] (dist: 2.59m, conf: 0.828)\n", "Match 231: <PERSON><PERSON> [ 436368.95986765 5012203.82589706] <-> IFC [ 436368.16695688 5012201.69142313] (dist: 2.28m, conf: 0.848)\n", "Match 232: <PERSON><PERSON> [ 435572.49825    5011474.82560417] <-> IFC [ 435572.16695688 5011473.69142313] (dist: 1.18m, conf: 0.921)\n", "Match 233: <PERSON><PERSON> [ 435742.124      5011307.96990909] <-> IFC [ 435742.16695688 5011309.69142313] (dist: 1.72m, conf: 0.885)\n", "Match 234: <PERSON><PERSON> [ 436415.85167857 5012156.38628571] <-> IFC [ 436414.16695688 5012159.69142313] (dist: 3.71m, conf: 0.753)\n", "Match 235: <PERSON><PERSON> [ 435853.77961111 5011427.04452778] <-> IFC [ 435856.16695688 5011427.69142313] (dist: 2.47m, conf: 0.835)\n", "Match 236: <PERSON><PERSON> [ 436434.905625 5012045.0227  ] <-> IFC [ 436434.16695688 5012043.69142313] (dist: 1.52m, conf: 0.899)\n", "Match 237: <PERSON><PERSON> [ 436538.95583333 5012027.67316667] <-> IFC [ 436538.16695688 5012029.69142313] (dist: 2.17m, conf: 0.856)\n", "Match 238: <PERSON><PERSON> [ 436311.88302128 5012347.43906383] <-> IFC [ 436310.16695688 5012349.69142313] (dist: 2.83m, conf: 0.811)\n", "Match 239: <PERSON><PERSON> [ 436633.82675    5011872.56092647] <-> IFC [ 436634.16695688 5011871.69142313] (dist: 0.93m, conf: 0.938)\n", "Match 240: <PERSON><PERSON> [ 436299.22759091 5011595.44004545] <-> IFC [ 436296.16695688 5011591.69142313] (dist: 4.84m, conf: 0.677)\n", "Match 241: <PERSON><PERSON> [ 436052.36954 5011042.6789 ] <-> IFC [ 436056.16695688 5011041.69142313] (dist: 3.92m, conf: 0.738)\n", "Match 242: <PERSON><PERSON> [ 435685.27831707 5011977.108     ] <-> IFC [ 435686.16695688 5011973.69142313] (dist: 3.53m, conf: 0.765)\n", "Match 243: <PERSON><PERSON> [ 436068.73708333 5012104.724     ] <-> IFC [ 436074.16695688 5012105.69142313] (dist: 5.52m, conf: 0.632)\n", "Match 244: <PERSON><PERSON> [ 436156.23493878 5011023.66528571] <-> IFC [ 436160.16695688 5011019.69142313] (dist: 5.59m, conf: 0.627)\n", "Match 245: <PERSON><PERSON> [ 435927.25604878 5012288.49604878] <-> IFC [ 435930.16695688 5012287.69142313] (dist: 3.02m, conf: 0.799)\n", "Match 246: <PERSON><PERSON> [ 436405.60576923 5012217.20723077] <-> IFC [ 436406.16695688 5012213.69142313] (dist: 3.56m, conf: 0.763)\n", "Match 247: <PERSON><PERSON> [ 436274.55819672 5012318.99036066] <-> IFC [ 436272.16695688 5012321.69142313] (dist: 3.61m, conf: 0.760)\n", "Match 248: <PERSON><PERSON> [ 436340.62304167 5012165.43629167] <-> IFC [ 436338.16695688 5012163.69142313] (dist: 3.01m, conf: 0.799)\n", "Match 249: <PERSON><PERSON> [ 436312.7925 5012103.0986] <-> IFC [ 436310.16695688 5012105.69142313] (dist: 3.69m, conf: 0.754)\n", "Match 250: <PERSON><PERSON> [ 435789.05764444 5011252.50077778] <-> IFC [ 435790.16695688 5011249.69142313] (dist: 3.02m, conf: 0.799)\n", "Match 251: <PERSON><PERSON> [ 435788.93178571 5012145.45692857] <-> IFC [ 435790.16695688 5012141.69142313] (dist: 3.96m, conf: 0.736)\n", "Match 252: <PERSON><PERSON> [ 435779.68825    5011443.05667857] <-> IFC [ 435780.16695688 5011445.69142313] (dist: 2.68m, conf: 0.821)\n", "Match 253: <PERSON><PERSON> [ 436671.45375  5011902.239125] <-> IFC [ 436672.16695688 5011899.69142313] (dist: 2.65m, conf: 0.824)\n", "Match 254: <PERSON><PERSON> [ 436246.24884615 5012181.762     ] <-> IFC [ 436244.16695688 5012178.69142313] (dist: 3.71m, conf: 0.753)\n", "Match 255: <PERSON><PERSON> [ 435779.13834 5011428.53096] <-> IFC [ 435780.16695688 5011427.69142313] (dist: 1.33m, conf: 0.911)\n", "Match 256: <PERSON><PERSON> [ 435469.261    5011629.048375] <-> IFC [ 435466.16695688 5011625.69142313] (dist: 4.57m, conf: 0.696)\n", "Match 257: <PERSON><PERSON> [ 435563.85471429 5011529.12957143] <-> IFC [ 435562.16695688 5011525.69142313] (dist: 3.83m, conf: 0.745)\n", "Match 258: <PERSON><PERSON> [ 435772.25843662 5011635.65005634] <-> IFC [ 435770.16695688 5011633.69142313] (dist: 2.87m, conf: 0.809)\n", "Match 259: Dr<PERSON> [ 435506.02767568 5012056.9492973 ] <-> IFC [ 435504.16695688 5012055.69142313] (dist: 2.25m, conf: 0.850)\n", "Match 260: <PERSON><PERSON> [ 435486.33333333 5011624.59694444] <-> IFC [ 435486.16695688 5011626.69142313] (dist: 2.10m, conf: 0.860)\n", "Match 261: <PERSON><PERSON> [ 435552.29871429 5011690.224     ] <-> IFC [ 435552.16695688 5011691.69142313] (dist: 1.47m, conf: 0.902)\n", "Match 262: <PERSON><PERSON> [ 436301.02472727 5011604.235     ] <-> IFC [ 436306.16695688 5011600.69142313] (dist: 6.24m, conf: 0.584)\n", "Match 263: <PERSON><PERSON> [ 436284.24840741 5012129.62885185] <-> IFC [ 436282.16695688 5012133.69142313] (dist: 4.56m, conf: 0.696)\n", "Match 264: <PERSON><PERSON> [ 436154.5516 5011147.73  ] <-> IFC [ 436150.16695688 5011145.69142313] (dist: 4.84m, conf: 0.678)\n", "Match 265: <PERSON><PERSON> [ 436472.24720732 5012094.59282927] <-> IFC [ 436472.16695688 5012095.69142313] (dist: 1.10m, conf: 0.927)\n", "Match 266: <PERSON><PERSON> [ 436088.66266667 5011949.25433333] <-> IFC [ 436084.16695688 5011949.69142313] (dist: 4.52m, conf: 0.699)\n", "Match 267: <PERSON><PERSON> [ 436371.87995833 5011128.06375   ] <-> IFC [ 436370.16695688 5011129.69142313] (dist: 2.36m, conf: 0.842)\n", "Match 268: <PERSON><PERSON> [ 436528.77146667 5012072.14405   ] <-> IFC [ 436528.16695688 5012075.69142313] (dist: 3.60m, conf: 0.760)\n", "Match 269: <PERSON><PERSON> [ 435863.68760606 5012027.74730303] <-> IFC [ 435866.16695688 5012031.69142313] (dist: 4.66m, conf: 0.689)\n", "Match 270: <PERSON><PERSON> [ 436257.96258824 5012008.23170588] <-> IFC [ 436256.16695688 5012013.69142313] (dist: 5.75m, conf: 0.617)\n", "Match 271: <PERSON><PERSON> [ 436267.28724138 5012023.48286207] <-> IFC [ 436264.16695688 5012019.69142313] (dist: 4.91m, conf: 0.673)\n", "Match 272: <PERSON><PERSON> [ 436652.00495833 5011855.74958333] <-> IFC [ 436654.16695688 5011855.69142313] (dist: 2.16m, conf: 0.856)\n", "Match 273: <PERSON><PERSON> [ 435506.44247945 5011667.1929863 ] <-> IFC [ 435504.16695688 5011670.69142313] (dist: 4.17m, conf: 0.722)\n", "Match 274: <PERSON><PERSON> [ 435646.77758824 5011500.45564706] <-> IFC [ 435647.16695688 5011501.69142313] (dist: 1.30m, conf: 0.914)\n", "Match 275: <PERSON><PERSON> [ 436049.36313043 5012277.29847826] <-> IFC [ 436044.16695688 5012277.69142313] (dist: 5.21m, conf: 0.653)\n", "Match 276: <PERSON><PERSON> [ 436255.98542308 5012233.60169231] <-> IFC [ 436254.16695688 5012229.69142313] (dist: 4.31m, conf: 0.713)\n", "Match 277: <PERSON><PERSON> [ 436604.61988889 5011927.72255556] <-> IFC [ 436606.16695688 5011923.69142313] (dist: 4.32m, conf: 0.712)\n", "Match 278: <PERSON><PERSON> [ 435648.32777143 5011442.74548571] <-> IFC [ 435656.16695688 5011445.69142313] (dist: 8.37m, conf: 0.442)\n", "Match 279: <PERSON><PERSON> [ 436255.85575 5012189.11825] <-> IFC [ 436254.16695688 5012186.69142313] (dist: 2.96m, conf: 0.803)\n", "Match 280: <PERSON><PERSON> [ 436303.11541667 5012311.667625  ] <-> IFC [ 436300.16695688 5012311.69142313] (dist: 2.95m, conf: 0.803)\n", "Match 281: <PERSON><PERSON> [ 436462.28510811 5012114.98872973] <-> IFC [ 436462.16695688 5012115.69142313] (dist: 0.71m, conf: 0.952)\n", "Match 282: <PERSON><PERSON> [ 435459.59251969 5011663.13857481] <-> IFC [ 435458.16695688 5011662.69142313] (dist: 1.49m, conf: 0.900)\n", "Match 283: <PERSON><PERSON> [ 435571.83842857 5011587.064     ] <-> IFC [ 435572.16695688 5011593.69142313] (dist: 6.64m, conf: 0.558)\n", "Match 284: <PERSON><PERSON> [ 436127.88365909 5010951.16470455] <-> IFC [ 436132.16695688 5010947.69142313] (dist: 5.51m, conf: 0.632)\n", "Match 285: <PERSON><PERSON> [ 435883.206      5012094.40020833] <-> IFC [ 435884.16695688 5012091.69142313] (dist: 2.87m, conf: 0.808)\n", "Match 286: <PERSON><PERSON> [ 436098.48369767 5011878.15037209] <-> IFC [ 436094.16695688 5011875.69142313] (dist: 4.97m, conf: 0.669)\n", "Match 287: <PERSON><PERSON> [ 435992.4972     5012316.05805714] <-> IFC [ 435996.16695688 5012317.69142313] (dist: 4.02m, conf: 0.732)\n", "Match 288: <PERSON><PERSON> [ 436319.29711111 5011635.22711111] <-> IFC [ 436316.16695688 5011635.69142313] (dist: 3.16m, conf: 0.789)\n", "Match 289: <PERSON><PERSON> [ 436350.11177273 5012159.1605    ] <-> IFC [ 436348.16695688 5012159.69142313] (dist: 2.02m, conf: 0.866)\n", "Match 290: <PERSON><PERSON> [ 435667.1595098  5011384.12860784] <-> IFC [ 435666.16695688 5011387.69142313] (dist: 3.70m, conf: 0.753)\n", "Match 291: <PERSON><PERSON> [ 436165.24005714 5010963.23611428] <-> IFC [ 436160.16695688 5010961.69142313] (dist: 5.30m, conf: 0.646)\n", "Match 292: <PERSON><PERSON> [ 435675.28645455 5011379.37681818] <-> IFC [ 435676.16695688 5011375.69142313] (dist: 3.79m, conf: 0.747)\n", "Match 293: <PERSON><PERSON> [ 435873.425      5012182.64866667] <-> IFC [ 435866.16695688 5012183.69142313] (dist: 7.33m, conf: 0.511)\n", "Match 294: <PERSON><PERSON> [ 436311.99536842 5012257.20684211] <-> IFC [ 436310.16695688 5012257.69142313] (dist: 1.89m, conf: 0.874)\n", "Match 295: <PERSON><PERSON> [ 436672.14633333 5011878.96454167] <-> IFC [ 436672.16695688 5011875.69142313] (dist: 3.27m, conf: 0.782)\n", "Match 296: <PERSON><PERSON> [ 436613.8649 5011866.4017] <-> IFC [ 436616.16695688 5011861.69142313] (dist: 5.24m, conf: 0.650)\n", "Match 297: <PERSON><PERSON> [ 436146.56106579 5011020.14832895] <-> IFC [ 436150.16695688 5011019.69142313] (dist: 3.63m, conf: 0.758)\n", "Match 298: <PERSON><PERSON> [ 436019.476      5012333.02585714] <-> IFC [ 436016.16695688 5012328.69142313] (dist: 5.45m, conf: 0.636)\n", "Match 299: Dr<PERSON> [ 436321.87320513 5012285.27094872] <-> IFC [ 436320.16695688 5012287.69142313] (dist: 2.96m, conf: 0.803)\n", "Match 300: <PERSON><PERSON> [ 436652.81995833 5011887.06675   ] <-> IFC [ 436654.16695688 5011889.69142313] (dist: 2.95m, conf: 0.803)\n", "Match 301: <PERSON><PERSON> [ 436156.02383077 5010949.80075385] <-> IFC [ 436160.16695688 5010953.69142313] (dist: 5.68m, conf: 0.621)\n", "Match 302: <PERSON><PERSON> [ 436237.22638462 5012396.91257692] <-> IFC [ 436234.16695688 5012394.69142313] (dist: 3.78m, conf: 0.748)\n", "Match 303: <PERSON><PERSON> [ 436152.79540741 5012175.2557037 ] <-> IFC [ 436148.16695688 5012177.69142313] (dist: 5.23m, conf: 0.651)\n", "Match 304: <PERSON><PERSON> [ 436077.19575    5012336.78664286] <-> IFC [ 436072.16695688 5012335.69142313] (dist: 5.15m, conf: 0.657)\n", "Match 305: <PERSON><PERSON> [ 436171.8168866  5012210.26906186] <-> IFC [ 436168.16695688 5012209.69142313] (dist: 3.70m, conf: 0.754)\n", "Match 306: <PERSON><PERSON> [ 436349.81976429 5012238.73507143] <-> IFC [ 436348.16695688 5012235.69142313] (dist: 3.46m, conf: 0.769)\n", "Match 307: <PERSON><PERSON> [ 435853.4555     5012205.97777778] <-> IFC [ 435856.16695688 5012203.69142313] (dist: 3.55m, conf: 0.764)\n", "Match 308: <PERSON><PERSON> [ 435572.25738889 5012097.20033333] <-> IFC [ 435572.16695688 5012093.69142313] (dist: 3.51m, conf: 0.766)\n", "Match 309: <PERSON><PERSON> [ 435364.02       5011721.12992857] <-> IFC [ 435362.16695688 5011723.69142313] (dist: 3.16m, conf: 0.789)\n", "Match 310: <PERSON><PERSON> [ 436039.35122222 5012262.35644444] <-> IFC [ 436034.16695688 5012263.69142313] (dist: 5.35m, conf: 0.643)\n", "Match 311: <PERSON><PERSON> [ 436680.83074603 5011831.50150794] <-> IFC [ 436682.16695688 5011831.69142313] (dist: 1.35m, conf: 0.910)\n", "Match 312: <PERSON><PERSON> [ 435798.95254167 5011424.268125  ] <-> IFC [ 435798.16695688 5011419.69142313] (dist: 4.64m, conf: 0.690)\n", "Match 313: <PERSON><PERSON> [ 435872.59166667 5011406.34445455] <-> IFC [ 435875.16695688 5011407.69142313] (dist: 2.91m, conf: 0.806)\n", "Match 314: <PERSON><PERSON> [ 436077.39379167 5012328.089     ] <-> IFC [ 436072.16695688 5012327.69142313] (dist: 5.24m, conf: 0.651)\n", "Match 315: <PERSON><PERSON> [ 435965.546      5012071.73727778] <-> IFC [ 435970.16695688 5012071.69142313] (dist: 4.62m, conf: 0.692)\n", "Match 316: <PERSON><PERSON> [ 435761.07159524 5011334.24238095] <-> IFC [ 435761.16695688 5011325.69142313] (dist: 8.55m, conf: 0.430)\n", "Match 317: <PERSON><PERSON> [ 436030.36617647 5012238.74294118] <-> IFC [ 436034.16695688 5012237.69142313] (dist: 3.94m, conf: 0.737)\n", "Match 318: <PERSON><PERSON> [ 435845.61709524 5011445.82114286] <-> IFC [ 435846.16695688 5011444.69142313] (dist: 1.26m, conf: 0.916)\n", "Match 319: <PERSON><PERSON> [ 436096.80605263 5012355.69894737] <-> IFC [ 436092.16695688 5012353.69142313] (dist: 5.05m, conf: 0.663)\n", "Match 320: <PERSON><PERSON> [ 436406.75629167 5012054.96254167] <-> IFC [ 436406.16695688 5012053.69142313] (dist: 1.40m, conf: 0.907)\n", "Match 321: <PERSON><PERSON> [ 436480.97935714 5012114.34428571] <-> IFC [ 436482.16695688 5012117.69142313] (dist: 3.55m, conf: 0.763)\n", "Match 322: <PERSON><PERSON> [ 435731.86514706 5011775.31955882] <-> IFC [ 435732.16695688 5011769.69142313] (dist: 5.64m, conf: 0.624)\n", "Match 323: <PERSON><PERSON> [ 436634.54577778 5011852.62702778] <-> IFC [ 436634.16695688 5011855.69142313] (dist: 3.09m, conf: 0.794)\n", "Match 324: <PERSON><PERSON> [ 436229.35127273 5011911.64427273] <-> IFC [ 436226.16695688 5011913.69142313] (dist: 3.79m, conf: 0.748)\n", "Match 325: <PERSON><PERSON> [ 436227.65885714 5012160.15377143] <-> IFC [ 436224.16695688 5012162.69142313] (dist: 4.32m, conf: 0.712)\n", "Match 326: <PERSON><PERSON> [ 436274.82533333 5012216.98903704] <-> IFC [ 436272.16695688 5012213.69142313] (dist: 4.24m, conf: 0.718)\n", "Match 327: <PERSON><PERSON> [ 436349.15755556 5012171.19496296] <-> IFC [ 436348.16695688 5012167.69142313] (dist: 3.64m, conf: 0.757)\n", "Match 328: <PERSON><PERSON> [ 436500.54497619 5012057.43595238] <-> IFC [ 436500.16695688 5012059.69142313] (dist: 2.29m, conf: 0.848)\n", "Match 329: <PERSON><PERSON> [ 435968.691  5012049.0526] <-> IFC [ 435970.16695688 5012045.69142313] (dist: 3.67m, conf: 0.755)\n", "Match 330: <PERSON><PERSON> [ 435901.081875 5012175.75175 ] <-> IFC [ 435904.16695688 5012177.69142313] (dist: 3.64m, conf: 0.757)\n", "Match 331: <PERSON><PERSON> [ 436171.080775 5012340.30025 ] <-> IFC [ 436168.16695688 5012338.69142313] (dist: 3.33m, conf: 0.778)\n", "Match 332: <PERSON><PERSON> [ 435459.236      5011778.83792857] <-> IFC [ 435458.16695688 5011785.69142313] (dist: 6.94m, conf: 0.538)\n", "Match 333: <PERSON><PERSON> [ 435676.428      5011405.55082222] <-> IFC [ 435685.39772611 5011408.4606539 ] (dist: 9.43m, conf: 0.371)\n", "Match 334: <PERSON><PERSON> [ 436576.9089375 5011937.9810625] <-> IFC [ 436578.16695688 5011935.69142313] (dist: 2.61m, conf: 0.826)\n", "Match 335: <PERSON><PERSON> [ 436062.14811111 5011013.008     ] <-> IFC [ 436065.30981402 5011006.548566  ] (dist: 7.19m, conf: 0.521)\n", "Match 336: <PERSON><PERSON> [ 435937.93842857 5012242.314     ] <-> IFC [ 435940.16695688 5012243.69142313] (dist: 2.62m, conf: 0.825)\n", "Match 337: <PERSON><PERSON> [ 436270.11685714 5011197.71414286] <-> IFC [ 436274.16695688 5011197.69142313] (dist: 4.05m, conf: 0.730)\n", "Match 338: <PERSON><PERSON> [ 436199.6416 5012272.4761] <-> IFC [ 436196.16695688 5012273.69142313] (dist: 3.68m, conf: 0.755)\n", "Match 339: <PERSON><PERSON> [ 435469.48825   5011620.0556875] <-> IFC [ 435466.16695688 5011617.69142313] (dist: 4.08m, conf: 0.728)\n", "Match 340: <PERSON><PERSON> [ 436441.6082 5011679.2202] <-> IFC [ 436440.16695688 5011673.69142313] (dist: 5.71m, conf: 0.619)\n", "Match 341: <PERSON><PERSON> [ 436472.49230137 5012058.09154795] <-> IFC [ 436472.16695688 5012061.69142313] (dist: 3.61m, conf: 0.759)\n", "Match 342: <PERSON><PERSON> [ 436652.65546154 5011879.00180769] <-> IFC [ 436654.16695688 5011881.69142313] (dist: 3.09m, conf: 0.794)\n", "Match 343: <PERSON><PERSON> [ 436257.75766667 5011979.82252381] <-> IFC [ 436256.16695688 5011979.69142313] (dist: 1.60m, conf: 0.894)\n", "Match 344: <PERSON><PERSON> [ 436481.83131579 5012089.42957895] <-> IFC [ 436482.16695688 5012091.69142313] (dist: 2.29m, conf: 0.848)\n", "Match 345: <PERSON><PERSON> [ 436137.02426667 5011030.22886667] <-> IFC [ 436141.16695688 5011029.69142313] (dist: 4.18m, conf: 0.722)\n", "Match 346: <PERSON><PERSON> [ 436405.53353846 5012209.54169231] <-> IFC [ 436406.16695688 5012205.69142313] (dist: 3.90m, conf: 0.740)\n", "Match 347: <PERSON><PERSON> [ 435975.4597 5012057.1849] <-> IFC [ 435970.16695688 5012055.69142313] (dist: 5.50m, conf: 0.633)\n", "Match 348: <PERSON><PERSON> [ 436180.6164507  5012330.81115493] <-> IFC [ 436178.16695688 5012327.69142313] (dist: 3.97m, conf: 0.736)\n", "Match 349: <PERSON><PERSON> [ 435845.00206667 5011456.37897778] <-> IFC [ 435846.16695688 5011453.69142313] (dist: 2.93m, conf: 0.805)\n", "Match 350: <PERSON><PERSON> [ 436643.263625 5011783.806   ] <-> IFC [ 436644.16695688 5011783.69142313] (dist: 0.91m, conf: 0.939)\n", "Match 351: <PERSON><PERSON> [ 436303.06152381 5012274.515     ] <-> IFC [ 436300.16695688 5012277.69142313] (dist: 4.30m, conf: 0.714)\n", "Match 352: <PERSON><PERSON> [ 435773.34554545 5011601.35495454] <-> IFC [ 435770.16695688 5011599.69142313] (dist: 3.59m, conf: 0.761)\n", "Match 353: <PERSON><PERSON> [ 436372.80786364 5011138.68154545] <-> IFC [ 436370.16695688 5011137.69142313] (dist: 2.82m, conf: 0.812)\n", "Match 354: <PERSON><PERSON> [ 435677.85314286 5011445.41514286] <-> IFC [ 435676.16695688 5011442.69142313] (dist: 3.20m, conf: 0.786)\n", "Match 355: <PERSON><PERSON> [ 436061.56840206 5011082.52157732] <-> IFC [ 436065.16695688 5011081.69142313] (dist: 3.69m, conf: 0.754)\n", "Match 356: <PERSON><PERSON> [ 436303.20711765 5012283.11805882] <-> IFC [ 436300.16695688 5012285.69142313] (dist: 3.98m, conf: 0.734)\n", "Match 357: <PERSON><PERSON> [ 436359.02822222 5012294.36644444] <-> IFC [ 436358.16695688 5012289.69142313] (dist: 4.75m, conf: 0.683)\n", "Match 358: <PERSON><PERSON> [ 435816.5229  5012097.86956] <-> IFC [ 435818.16695688 5012099.69142313] (dist: 2.45m, conf: 0.836)\n", "Match 359: <PERSON><PERSON> [ 436123.7519  5012406.46535] <-> IFC [ 436120.16695688 5012403.69142313] (dist: 4.53m, conf: 0.698)\n", "Match 360: <PERSON><PERSON> [ 436199.19615789 5012337.24505263] <-> IFC [ 436196.16695688 5012337.69142313] (dist: 3.06m, conf: 0.796)\n", "Match 361: <PERSON><PERSON> [ 436643.50733333 5011795.84844445] <-> IFC [ 436644.16695688 5011791.69142313] (dist: 4.21m, conf: 0.719)\n", "Match 362: <PERSON><PERSON> [ 436180.55036667 5012239.94726667] <-> IFC [ 436178.16695688 5012241.69142313] (dist: 2.95m, conf: 0.803)\n", "Match 363: <PERSON><PERSON> [ 436255.12192308 5012382.11457692] <-> IFC [ 436254.16695688 5012379.69142313] (dist: 2.60m, conf: 0.826)\n", "Match 364: <PERSON><PERSON> [ 436300.12327778 5011573.12527778] <-> IFC [ 436296.16695688 5011574.69142313] (dist: 4.26m, conf: 0.716)\n", "Match 365: <PERSON><PERSON> [ 435871.12408333 5012264.9285    ] <-> IFC [ 435874.16695688 5012265.69142313] (dist: 3.14m, conf: 0.791)\n", "Match 366: <PERSON><PERSON> [ 435910.08238462 5012144.09092308] <-> IFC [ 435913.39772611 5012140.4606539 ] (dist: 4.92m, conf: 0.672)\n", "Match 367: <PERSON><PERSON> [ 436312.27916667 5012283.70772222] <-> IFC [ 436310.16695688 5012283.69142313] (dist: 2.11m, conf: 0.859)\n", "Match 368: <PERSON><PERSON> [ 436453.50666667 5012169.42356   ] <-> IFC [ 436452.16695688 5012171.69142313] (dist: 2.63m, conf: 0.824)\n", "Match 369: <PERSON><PERSON> [ 436099.22034043 5011900.20587234] <-> IFC [ 436104.16695688 5011899.69142313] (dist: 4.97m, conf: 0.668)\n", "Match 370: <PERSON><PERSON> [ 436482.59266667 5012035.6819697 ] <-> IFC [ 436482.16695688 5012033.69142313] (dist: 2.04m, conf: 0.864)\n", "Match 371: <PERSON><PERSON> [ 436058.08925   5012196.4658125] <-> IFC [ 436054.16695688 5012197.69142313] (dist: 4.11m, conf: 0.726)\n", "Match 372: <PERSON><PERSON> [ 435898.96276471 5012280.19629412] <-> IFC [ 435902.16695688 5012281.69142313] (dist: 3.54m, conf: 0.764)\n", "Match 373: <PERSON><PERSON> [ 436161.97853125 5012389.44546875] <-> IFC [ 436158.16695688 5012391.69142313] (dist: 4.42m, conf: 0.705)\n", "Match 374: <PERSON><PERSON> [ 436311.481375   5012298.35145833] <-> IFC [ 436310.16695688 5012299.69142313] (dist: 1.88m, conf: 0.875)\n", "Match 375: <PERSON><PERSON> [ 436114.87828814 5012379.0769661 ] <-> IFC [ 436120.16695688 5012379.69142313] (dist: 5.32m, conf: 0.645)\n", "Match 376: <PERSON><PERSON> [ 436113.33707692 5012300.615     ] <-> IFC [ 436110.16695688 5012297.69142313] (dist: 4.31m, conf: 0.713)\n", "Match 377: <PERSON><PERSON> [ 435656.4843 5011467.7374] <-> IFC [ 435656.16695688 5011463.69142313] (dist: 4.06m, conf: 0.729)\n", "Match 378: <PERSON><PERSON> [ 436162.116  5012350.2816] <-> IFC [ 436158.16695688 5012349.69142313] (dist: 3.99m, conf: 0.734)\n", "Match 379: <PERSON><PERSON> [ 436208.06280952 5012389.87019048] <-> IFC [ 436206.16695688 5012393.69142313] (dist: 4.27m, conf: 0.716)\n", "Match 380: <PERSON><PERSON> [ 436146.74523077 5010937.05725641] <-> IFC [ 436150.16695688 5010935.69142313] (dist: 3.68m, conf: 0.754)\n", "Match 381: <PERSON><PERSON> [ 435600.65659091 5011653.43890909] <-> IFC [ 435600.16695688 5011649.69142313] (dist: 3.78m, conf: 0.748)\n", "Match 382: <PERSON><PERSON> [ 435900.5646  5012185.81232] <-> IFC [ 435904.16695688 5012185.69142313] (dist: 3.60m, conf: 0.760)\n", "Match 383: <PERSON><PERSON> [ 436043.21650909 5011038.06158182] <-> IFC [ 436046.16695688 5011037.69142313] (dist: 2.97m, conf: 0.802)\n", "Match 384: <PERSON><PERSON> [ 436434.00642857 5012172.113     ] <-> IFC [ 436434.16695688 5012169.69142313] (dist: 2.43m, conf: 0.838)\n", "Match 385: <PERSON><PERSON> [ 436010.89516667 5012306.91666667] <-> IFC [ 436006.16695688 5012305.69142313] (dist: 4.88m, conf: 0.674)\n", "Match 386: <PERSON><PERSON> [ 436096.2072906  5012333.12529914] <-> IFC [ 436092.16695688 5012329.69142313] (dist: 5.30m, conf: 0.647)\n", "Match 387: <PERSON><PERSON> [ 435506.34958 5011622.09362] <-> IFC [ 435504.16695688 5011619.69142313] (dist: 3.25m, conf: 0.784)\n", "Match 388: <PERSON><PERSON> [ 435431.0189375 5011851.126    ] <-> IFC [ 435428.16695688 5011851.69142313] (dist: 2.91m, conf: 0.806)\n", "Match 389: <PERSON><PERSON> [ 436699.82234286 5011841.07871429] <-> IFC [ 436700.16695688 5011841.69142313] (dist: 0.70m, conf: 0.953)\n", "Match 390: <PERSON><PERSON> [ 435787.94317073 5011451.92202439] <-> IFC [ 435790.16695688 5011455.69142313] (dist: 4.38m, conf: 0.708)\n", "Match 391: <PERSON><PERSON> [ 436539.6054375  5011911.04285417] <-> IFC [ 436540.16695688 5011907.69142313] (dist: 3.40m, conf: 0.773)\n", "Match 392: <PERSON><PERSON> [ 436155.32268571 5010973.75408571] <-> IFC [ 436160.16695688 5010977.69142313] (dist: 6.24m, conf: 0.584)\n", "Match 393: <PERSON><PERSON> [ 436088.64314815 5011771.78081481] <-> IFC [ 436094.16695688 5011771.69142313] (dist: 5.52m, conf: 0.632)\n", "Match 394: <PERSON><PERSON> [ 435797.5669 5012134.7406] <-> IFC [ 435800.16695688 5012131.69142313] (dist: 4.01m, conf: 0.733)\n", "Match 395: <PERSON><PERSON> [ 436321.75038095 5012193.63780952] <-> IFC [ 436320.16695688 5012195.69142313] (dist: 2.59m, conf: 0.827)\n", "Match 396: <PERSON><PERSON> [ 436330.9499     5012255.63903333] <-> IFC [ 436330.16695688 5012259.69142313] (dist: 4.13m, conf: 0.725)\n", "Match 397: <PERSON><PERSON> [ 436011.78994737 5012284.775     ] <-> IFC [ 436016.16695688 5012285.69142313] (dist: 4.47m, conf: 0.702)\n", "Match 398: <PERSON><PERSON> [ 435445.64127778 5012016.32632407] <-> IFC [ 435448.16695688 5012015.69142313] (dist: 2.60m, conf: 0.826)\n", "Match 399: <PERSON><PERSON> [ 436490.94443396 5012030.54164151] <-> IFC [ 436490.16695688 5012029.69142313] (dist: 1.15m, conf: 0.923)\n", "Match 400: <PERSON><PERSON> [ 435898.8244     5012255.32933333] <-> IFC [ 435902.16695688 5012255.69142313] (dist: 3.36m, conf: 0.776)\n", "Match 401: <PERSON><PERSON> [ 435740.24331579 5011936.95331579] <-> IFC [ 435742.16695688 5011939.69142313] (dist: 3.35m, conf: 0.777)\n", "Match 402: <PERSON><PERSON> [ 436445.8523 5011981.6631] <-> IFC [ 436445.16695688 5011983.69142313] (dist: 2.14m, conf: 0.857)\n", "Match 403: <PERSON><PERSON> [ 436303.87633333 5012212.12788889] <-> IFC [ 436300.16695688 5012211.69142313] (dist: 3.73m, conf: 0.751)\n", "Match 404: <PERSON><PERSON> [ 436146.8498125 5010986.9960625] <-> IFC [ 436150.16695688 5010985.69142313] (dist: 3.56m, conf: 0.762)\n", "Match 405: <PERSON><PERSON> [ 435421.62114286 5012013.06      ] <-> IFC [ 435420.16695688 5012015.69142313] (dist: 3.01m, conf: 0.800)\n", "Match 406: <PERSON><PERSON> [ 436586.91633333 5011930.19405555] <-> IFC [ 436586.16695688 5011931.69142313] (dist: 1.67m, conf: 0.888)\n", "Match 407: <PERSON><PERSON> [ 436661.237      5011861.10976923] <-> IFC [ 436662.16695688 5011861.69142313] (dist: 1.10m, conf: 0.927)\n", "Match 408: <PERSON><PERSON> [ 436069.10706667 5012095.43026667] <-> IFC [ 436074.16695688 5012097.69142313] (dist: 5.54m, conf: 0.631)\n", "Match 409: <PERSON><PERSON> [ 436274.00269565 5012343.53328261] <-> IFC [ 436272.16695688 5012339.69142313] (dist: 4.26m, conf: 0.716)\n", "Match 410: <PERSON><PERSON> [ 436344.8838 5011094.4613] <-> IFC [ 436340.16695688 5011093.69142313] (dist: 4.78m, conf: 0.681)\n", "Match 411: <PERSON><PERSON> [ 435882.78459375 5011501.53928125] <-> IFC [ 435884.16695688 5011499.69142313] (dist: 2.31m, conf: 0.846)\n", "Match 412: <PERSON><PERSON> [ 436049.04562162 5012192.42362162] <-> IFC [ 436044.16695688 5012191.69142313] (dist: 4.93m, conf: 0.671)\n", "Match 413: <PERSON><PERSON> [ 436227.18934483 5012246.52348276] <-> IFC [ 436224.16695688 5012247.69142313] (dist: 3.24m, conf: 0.784)\n", "Match 414: <PERSON><PERSON> [ 435854.09556522 5012175.4676087 ] <-> IFC [ 435856.16695688 5012177.69142313] (dist: 3.04m, conf: 0.797)\n", "Match 415: <PERSON><PERSON> [ 436077.85647826 5012239.309     ] <-> IFC [ 436082.16695688 5012239.69142313] (dist: 4.33m, conf: 0.712)\n", "Match 416: <PERSON><PERSON> [ 436427.35366667 5011983.97733333] <-> IFC [ 436426.16695688 5011983.69142313] (dist: 1.22m, conf: 0.919)\n", "Match 417: <PERSON><PERSON> [ 436584.5182381  5011986.78061905] <-> IFC [ 436586.16695688 5011985.69142313] (dist: 1.98m, conf: 0.868)\n", "Match 418: <PERSON><PERSON> [ 435704.59198 5011446.35858] <-> IFC [ 435704.16695688 5011439.69142313] (dist: 6.68m, conf: 0.555)\n", "Match 419: <PERSON><PERSON> [ 435722.59273077 5011441.78857692] <-> IFC [ 435724.16695688 5011443.69142313] (dist: 2.47m, conf: 0.835)\n", "Match 420: <PERSON><PERSON> [ 436133.8178     5012410.03253333] <-> IFC [ 436130.16695688 5012409.69142313] (dist: 3.67m, conf: 0.756)\n", "Match 421: <PERSON><PERSON> [ 435815.9169375 5012221.812625 ] <-> IFC [ 435818.16695688 5012217.69142313] (dist: 4.70m, conf: 0.687)\n", "Match 422: <PERSON><PERSON> [ 436182.1199 5012214.0598] <-> IFC [ 436186.16695688 5012215.69142313] (dist: 4.36m, conf: 0.709)\n", "Match 423: <PERSON><PERSON> [ 436327.792    5011631.788875] <-> IFC [ 436326.16695688 5011635.69142313] (dist: 4.23m, conf: 0.718)\n", "Match 424: <PERSON><PERSON> [ 436283.957625 5012121.875125] <-> IFC [ 436282.16695688 5012124.69142313] (dist: 3.34m, conf: 0.778)\n", "Match 425: <PERSON><PERSON> [ 435975.33437037 5011993.23940741] <-> IFC [ 435980.16695688 5011994.69142313] (dist: 5.05m, conf: 0.664)\n", "Match 426: <PERSON><PERSON> [ 436377.80973404 5012212.541     ] <-> IFC [ 436376.16695688 5012215.69142313] (dist: 3.55m, conf: 0.763)\n", "Match 427: <PERSON><PERSON> [ 435337.00428571 5011783.17407143] <-> IFC [ 435334.16695688 5011778.69142313] (dist: 5.31m, conf: 0.646)\n", "Match 428: <PERSON><PERSON> [ 436481.97993548 5012100.21703226] <-> IFC [ 436482.16695688 5012091.69142313] (dist: 8.53m, conf: 0.431)\n", "Match 429: <PERSON><PERSON> [ 436069.58625   5011863.9111875] <-> IFC [ 436065.16695688 5011865.69142314] (dist: 4.76m, conf: 0.682)\n", "Match 430: <PERSON><PERSON> [ 436321.46915152 5012311.74515151] <-> IFC [ 436320.08683743 5012313.63838892] (dist: 2.34m, conf: 0.844)\n", "Match 431: <PERSON><PERSON> [ 436501.72191667 5012039.7845    ] <-> IFC [ 436500.16695688 5012043.69142313] (dist: 4.20m, conf: 0.720)\n", "Match 432: <PERSON><PERSON> [ 436245.75928 5012396.93376] <-> IFC [ 436244.16695688 5012399.69142313] (dist: 3.18m, conf: 0.788)\n", "Match 433: <PERSON><PERSON> [ 436002.65204762 5012311.6262619 ] <-> IFC [ 436006.16695688 5012314.69142313] (dist: 4.66m, conf: 0.689)\n", "Match 434: <PERSON><PERSON> [ 435449.86866667 5011619.71233333] <-> IFC [ 435448.16695688 5011623.69142313] (dist: 4.33m, conf: 0.711)\n", "Match 435: <PERSON><PERSON> [ 436171.05219118 5012409.68730882] <-> IFC [ 436168.16695688 5012405.69142313] (dist: 4.93m, conf: 0.671)\n", "Match 436: <PERSON><PERSON> [ 436312.22630769 5012266.465     ] <-> IFC [ 436310.16695688 5012265.69142313] (dist: 2.20m, conf: 0.853)\n", "Match 437: <PERSON><PERSON> [ 436237.33275 5012374.74975] <-> IFC [ 436234.16695688 5012377.69142313] (dist: 4.32m, conf: 0.712)\n", "Match 438: <PERSON><PERSON> [ 435862.36      5012173.6294375] <-> IFC [ 435866.16695688 5012174.69142313] (dist: 3.95m, conf: 0.737)\n", "Match 439: <PERSON><PERSON> [ 436547.81197436 5012016.3614359 ] <-> IFC [ 436548.16695688 5012017.69142313] (dist: 1.38m, conf: 0.908)\n", "Match 440: <PERSON><PERSON> [ 436340.57567742 5012155.98667742] <-> IFC [ 436338.16695688 5012153.69142313] (dist: 3.33m, conf: 0.778)\n", "Match 441: <PERSON><PERSON> [ 436096.49883333 5012300.16716667] <-> IFC [ 436092.16695688 5012303.69142313] (dist: 5.58m, conf: 0.628)\n", "Match 442: <PERSON><PERSON> [ 436060.20175 5011854.2425 ] <-> IFC [ 436056.16695688 5011853.69142313] (dist: 4.07m, conf: 0.729)\n", "Match 443: <PERSON><PERSON> [ 435618.279625 5011453.2535  ] <-> IFC [ 435618.16695688 5011455.69142313] (dist: 2.44m, conf: 0.837)\n", "Match 444: <PERSON><PERSON> [ 436275.16364516 5012400.66225806] <-> IFC [ 436272.16695688 5012397.69142313] (dist: 4.22m, conf: 0.719)\n", "Match 445: <PERSON><PERSON> [ 436377.249      5012259.97277551] <-> IFC [ 436376.16695688 5012257.69142313] (dist: 2.52m, conf: 0.832)\n", "Match 446: <PERSON><PERSON> [ 436127.91252 5011003.1554 ] <-> IFC [ 436132.16695688 5011006.69142313] (dist: 5.53m, conf: 0.631)\n", "Match 447: <PERSON><PERSON> [ 436425.49097561 5012164.2047561 ] <-> IFC [ 436424.16695688 5012163.69142313] (dist: 1.42m, conf: 0.905)\n", "Match 448: <PERSON><PERSON> [ 435984.93980952 5012054.70209524] <-> IFC [ 435980.16695688 5012053.69142313] (dist: 4.88m, conf: 0.675)\n", "Match 449: <PERSON><PERSON> [ 435789.54894737 5011429.35515789] <-> IFC [ 435790.16695688 5011429.69142313] (dist: 0.70m, conf: 0.953)\n", "Match 450: <PERSON><PERSON> [ 436174.85077778 5011200.41477778] <-> IFC [ 436170.16695688 5011197.69142313] (dist: 5.42m, conf: 0.639)\n", "Match 451: <PERSON><PERSON> [ 436246.00581818 5012339.40205455] <-> IFC [ 436244.16695688 5012341.69142313] (dist: 2.94m, conf: 0.804)\n", "Match 452: <PERSON><PERSON> [ 436586.95161111 5011919.70466667] <-> IFC [ 436586.16695688 5011923.69142313] (dist: 4.06m, conf: 0.729)\n", "Match 453: <PERSON><PERSON> [ 435742.39109091 5011436.00836364] <-> IFC [ 435742.16695688 5011437.69142313] (dist: 1.70m, conf: 0.887)\n", "Match 454: <PERSON><PERSON> [ 436681.0935     5011813.75985714] <-> IFC [ 436682.16695688 5011813.69142313] (dist: 1.08m, conf: 0.928)\n", "Match 455: <PERSON><PERSON> [ 435619.92238333 5011426.29796667] <-> IFC [ 435618.16695688 5011423.69142313] (dist: 3.14m, conf: 0.790)\n", "Match 456: <PERSON><PERSON> [ 436227.78470833 5012280.82245833] <-> IFC [ 436224.16695688 5012281.69142313] (dist: 3.72m, conf: 0.752)\n", "Match 457: <PERSON><PERSON> [ 436174.38535088 5010990.81910526] <-> IFC [ 436170.16695688 5010989.69142313] (dist: 4.37m, conf: 0.709)\n", "Match 458: <PERSON><PERSON> [ 435544.79983333 5011713.79433333] <-> IFC [ 435542.16695688 5011717.69142313] (dist: 4.70m, conf: 0.686)\n", "Match 459: <PERSON><PERSON> [ 436340.94816129 5012259.72332258] <-> IFC [ 436338.16695688 5012263.69142313] (dist: 4.85m, conf: 0.677)\n", "Match 460: <PERSON><PERSON> [ 436237.13402273 5012269.32443182] <-> IFC [ 436234.16695688 5012271.69142313] (dist: 3.80m, conf: 0.747)\n", "Match 461: <PERSON><PERSON> [ 436265.52205263 5012212.98726316] <-> IFC [ 436262.16695688 5012215.69142313] (dist: 4.31m, conf: 0.713)\n", "Match 462: <PERSON><PERSON> [ 435535.47383333 5011538.75083333] <-> IFC [ 435534.16695688 5011539.69142313] (dist: 1.61m, conf: 0.893)\n", "Match 463: <PERSON><PERSON> [ 436220.93154762 5011989.66678571] <-> IFC [ 436226.16695688 5011989.69142313] (dist: 5.24m, conf: 0.651)\n", "Match 464: <PERSON><PERSON> [ 436690.6941 5011835.3077] <-> IFC [ 436692.16695688 5011835.69142313] (dist: 1.52m, conf: 0.899)\n", "Match 465: <PERSON><PERSON> [ 435910.71333871 5012099.60232258] <-> IFC [ 435912.16695688 5012105.69142313] (dist: 6.26m, conf: 0.583)\n", "Match 466: <PERSON><PERSON> [ 436330.83201724 5012293.38382758] <-> IFC [ 436330.16695688 5012293.69142313] (dist: 0.73m, conf: 0.951)\n", "Match 467: <PERSON><PERSON> [ 436143.10120588 5012188.26414706] <-> IFC [ 436140.16695688 5012187.69142313] (dist: 2.99m, conf: 0.801)\n", "Match 468: <PERSON><PERSON> [ 436088.68529412 5012084.54462745] <-> IFC [ 436084.16695688 5012084.69142313] (dist: 4.52m, conf: 0.699)\n", "Match 469: <PERSON><PERSON> [ 436032.97528571 5011938.399     ] <-> IFC [ 436036.16695688 5011937.69142313] (dist: 3.27m, conf: 0.782)\n", "Match 470: <PERSON><PERSON> [ 436237.29927586 5012284.74265517] <-> IFC [ 436234.16695688 5012289.69142313] (dist: 5.86m, conf: 0.610)\n", "Match 471: <PERSON><PERSON> [ 436490.8795     5012059.60791176] <-> IFC [ 436490.16695688 5012055.69142313] (dist: 3.98m, conf: 0.735)\n", "Match 472: <PERSON><PERSON> [ 436284.83137838 5012231.41113513] <-> IFC [ 436282.16695688 5012233.69142313] (dist: 3.51m, conf: 0.766)\n", "Match 473: <PERSON><PERSON> [ 436283.55297333 5012347.43056   ] <-> IFC [ 436282.16695688 5012343.69142313] (dist: 3.99m, conf: 0.734)\n", "Match 474: <PERSON><PERSON> [ 436153.1815  5012194.70875] <-> IFC [ 436158.16695688 5012193.69142313] (dist: 5.09m, conf: 0.661)\n", "Match 475: <PERSON><PERSON> [ 436296.39363158 5011211.89331579] <-> IFC [ 436302.16695688 5011211.69142313] (dist: 5.78m, conf: 0.615)\n", "Match 476: <PERSON><PERSON> [ 435731.50071429 5011953.77985714] <-> IFC [ 435732.16695688 5011957.69142313] (dist: 3.97m, conf: 0.735)\n", "Match 477: <PERSON><PERSON> [ 436062.40088462 5010992.27873077] <-> IFC [ 436056.16695688 5010991.69142313] (dist: 6.26m, conf: 0.583)\n", "Match 478: <PERSON><PERSON> [ 436189.64646341 5012355.09602439] <-> IFC [ 436186.16695688 5012357.69142313] (dist: 4.34m, conf: 0.711)\n", "Match 479: <PERSON><PERSON> [ 436255.63951515 5012398.26239394] <-> IFC [ 436254.16695688 5012397.69142313] (dist: 1.58m, conf: 0.895)\n", "Match 480: <PERSON><PERSON> [ 436294.501      5012310.77757143] <-> IFC [ 436292.16695688 5012315.69142313] (dist: 5.44m, conf: 0.637)\n", "Match 481: <PERSON><PERSON> [ 435582.22297297 5011462.53367568] <-> IFC [ 435580.16695688 5011461.69142313] (dist: 2.22m, conf: 0.852)\n", "Match 482: <PERSON><PERSON> [ 435947.08314286 5011583.307     ] <-> IFC [ 435942.16695688 5011581.69142313] (dist: 5.17m, conf: 0.655)\n", "Match 483: <PERSON><PERSON> [ 435973.77842529 5012309.51166667] <-> IFC [ 435978.16695688 5012307.69142313] (dist: 4.75m, conf: 0.683)\n", "Match 484: <PERSON><PERSON> [ 436132.3033871  5012354.42570968] <-> IFC [ 436130.16695688 5012350.69142313] (dist: 4.30m, conf: 0.713)\n", "Match 485: <PERSON><PERSON> [ 436387.14425 5012254.19   ] <-> IFC [ 436386.16695688 5012253.69142313] (dist: 1.10m, conf: 0.927)\n", "Match 486: <PERSON><PERSON> [ 435835.5683125 5011358.4058125] <-> IFC [ 435838.16695688 5011361.69142313] (dist: 4.19m, conf: 0.721)\n", "Match 487: <PERSON><PERSON> [ 436194.47192857 5010978.72007143] <-> IFC [ 436198.16695688 5010981.69142313] (dist: 4.74m, conf: 0.684)\n", "Match 488: <PERSON><PERSON> [ 436023.48438095 5011035.746     ] <-> IFC [ 436027.50029021 5011039.02475647] (dist: 5.18m, conf: 0.654)\n", "Match 489: <PERSON><PERSON> [ 435497.47776471 5011631.48070588] <-> IFC [ 435496.16695688 5011631.69142313] (dist: 1.33m, conf: 0.911)\n", "Match 490: <PERSON><PERSON> [ 435816.71141414 5011427.27048485] <-> IFC [ 435818.16695688 5011431.69142313] (dist: 4.65m, conf: 0.690)\n", "Match 491: <PERSON><PERSON> [ 436560.37795833 5011709.826875  ] <-> IFC [ 436554.16695688 5011715.69142313] (dist: 8.54m, conf: 0.431)\n", "Match 492: <PERSON><PERSON> [ 436041.39834146 5012105.17314634] <-> IFC [ 436046.16695688 5012107.69142313] (dist: 5.39m, conf: 0.640)\n", "Match 493: <PERSON><PERSON> [ 436042.31411628 5011851.55734884] <-> IFC [ 436046.16695688 5011855.69142313] (dist: 5.65m, conf: 0.623)\n", "Match 494: <PERSON><PERSON> [ 435985.68592593 5011986.54988889] <-> IFC [ 435990.16695688 5011985.69142313] (dist: 4.56m, conf: 0.696)\n", "Match 495: <PERSON><PERSON> [ 436311.9641  5012309.27595] <-> IFC [ 436310.16695688 5012307.69142313] (dist: 2.40m, conf: 0.840)\n", "Match 496: <PERSON><PERSON> [ 435722.98091549 5011512.35460563] <-> IFC [ 435723.76695688 5011511.29142313] (dist: 1.32m, conf: 0.912)\n", "Match 497: <PERSON><PERSON> [ 435798.55033333 5011437.70113333] <-> IFC [ 435800.16695688 5011429.69142313] (dist: 8.17m, conf: 0.455)\n", "Match 498: <PERSON><PERSON> [ 436189.48488636 5012439.66381818] <-> IFC [ 436186.16695688 5012441.69142313] (dist: 3.89m, conf: 0.741)\n", "Match 499: <PERSON><PERSON> [ 436143.05305714 5012158.94371429] <-> IFC [ 436140.16695688 5012161.69142313] (dist: 3.98m, conf: 0.734)\n", "Match 500: <PERSON><PERSON> [ 436208.40566667 5012436.90366667] <-> IFC [ 436206.16695688 5012435.69142313] (dist: 2.55m, conf: 0.830)\n", "Match 501: <PERSON><PERSON> [ 436368.78525    5012223.07222222] <-> IFC [ 436368.16695688 5012219.69142313] (dist: 3.44m, conf: 0.771)\n", "Match 502: <PERSON><PERSON> [ 435967.22515385 5012031.10676923] <-> IFC [ 435970.16695688 5012029.69142313] (dist: 3.26m, conf: 0.782)\n", "Match 503: <PERSON><PERSON> [ 436165.23063333 5011026.39953333] <-> IFC [ 436160.16695688 5011019.69142313] (dist: 8.40m, conf: 0.440)\n", "Match 504: <PERSON><PERSON> [ 436030.73259259 5012355.42607407] <-> IFC [ 436034.16695688 5012355.69142313] (dist: 3.44m, conf: 0.770)\n", "Match 505: <PERSON><PERSON> [ 435788.65163636 5011233.22643636] <-> IFC [ 435790.16695688 5011231.69142313] (dist: 2.16m, conf: 0.856)\n", "Match 506: <PERSON><PERSON> [ 436108.6563 5011832.7509] <-> IFC [ 436104.16695688 5011833.69142313] (dist: 4.59m, conf: 0.694)\n", "Match 507: <PERSON><PERSON> [ 436245.04442857 5012297.137     ] <-> IFC [ 436244.16695688 5012297.69142313] (dist: 1.04m, conf: 0.931)\n", "Match 508: <PERSON><PERSON> [ 436459.56045455 5011806.83195455] <-> IFC [ 436458.16695688 5011805.69142313] (dist: 1.80m, conf: 0.880)\n", "Match 509: <PERSON><PERSON> [ 436596.66065 5011821.93455] <-> IFC [ 436596.16695688 5011817.69142313] (dist: 4.27m, conf: 0.715)\n", "Match 510: <PERSON><PERSON> [ 436378.42836364 5012190.38281818] <-> IFC [ 436376.16695688 5012190.69142313] (dist: 2.28m, conf: 0.848)\n", "Match 511: <PERSON><PERSON> [ 436398.27316667 5012138.49566667] <-> IFC [ 436396.16695688 5012141.69142313] (dist: 3.83m, conf: 0.745)\n", "Match 512: <PERSON><PERSON> [ 436217.67145 5012451.7936 ] <-> IFC [ 436216.16695688 5012453.69142313] (dist: 2.42m, conf: 0.839)\n", "Match 513: <PERSON><PERSON> [ 436575.87848387 5012018.73035484] <-> IFC [ 436576.16695688 5012015.69142313] (dist: 3.05m, conf: 0.796)\n", "Match 514: <PERSON><PERSON> [ 436011.80018519 5012343.60344444] <-> IFC [ 436016.16695688 5012345.69142313] (dist: 4.84m, conf: 0.677)\n", "Match 515: <PERSON><PERSON> [ 436490.7822     5012131.84446667] <-> IFC [ 436490.16695688 5012131.69142313] (dist: 0.63m, conf: 0.958)\n", "Match 516: <PERSON><PERSON> [ 436077.12663158 5012226.19092105] <-> IFC [ 436072.16695688 5012225.69142313] (dist: 4.98m, conf: 0.668)\n", "Match 517: <PERSON><PERSON> [ 436416.20292 5012087.31772] <-> IFC [ 436414.16695688 5012087.69142313] (dist: 2.07m, conf: 0.862)\n", "Match 518: <PERSON><PERSON> [ 435863.63275    5012166.21491667] <-> IFC [ 435866.16695688 5012165.69142313] (dist: 2.59m, conf: 0.827)\n", "Match 519: <PERSON><PERSON> [ 435346.31827778 5011965.16041667] <-> IFC [ 435344.16695688 5011963.69142313] (dist: 2.61m, conf: 0.826)\n", "Match 520: <PERSON><PERSON> [ 436519.6197037  5012071.06803704] <-> IFC [ 436520.16695688 5012069.69142313] (dist: 1.48m, conf: 0.901)\n", "Match 521: <PERSON><PERSON> [ 435796.88121538 5011976.88464615] <-> IFC [ 435800.16695688 5011975.69142313] (dist: 3.50m, conf: 0.767)\n", "Match 522: <PERSON><PERSON> [ 436105.0501 5012346.8838] <-> IFC [ 436102.16695688 5012343.69142313] (dist: 4.30m, conf: 0.713)\n", "Match 523: <PERSON><PERSON> [ 436012.09388636 5012204.80136364] <-> IFC [ 436016.16695688 5012201.69142313] (dist: 5.12m, conf: 0.658)\n", "Match 524: <PERSON><PERSON> [ 435796.83944444 5011641.57861111] <-> IFC [ 435800.16695688 5011641.69142313] (dist: 3.33m, conf: 0.778)\n", "Match 525: <PERSON><PERSON> [ 436358.49192857 5012146.095     ] <-> IFC [ 436358.16695688 5012147.69142313] (dist: 1.63m, conf: 0.891)\n", "Match 526: <PERSON><PERSON> [ 436067.94797959 5012359.938     ] <-> IFC [ 436072.16695688 5012361.69142313] (dist: 4.57m, conf: 0.695)\n", "Match 527: <PERSON><PERSON> [ 436443.90065  5012176.439925] <-> IFC [ 436444.16695688 5012173.69142313] (dist: 2.76m, conf: 0.816)\n", "Match 528: <PERSON><PERSON> [ 436368.10058824 5012160.84623529] <-> IFC [ 436368.16695688 5012159.69142313] (dist: 1.16m, conf: 0.923)\n", "Match 529: <PERSON><PERSON> [ 436652.1889  5011895.25275] <-> IFC [ 436654.16695688 5011897.69142313] (dist: 3.14m, conf: 0.791)\n", "Match 530: <PERSON><PERSON> [ 436642.7447931 5011864.1527931] <-> IFC [ 436644.16695688 5011867.69142313] (dist: 3.81m, conf: 0.746)\n", "Match 531: <PERSON><PERSON> [ 435853.666875 5011438.434375] <-> IFC [ 435856.16695688 5011435.69142313] (dist: 3.71m, conf: 0.753)\n", "Match 532: <PERSON><PERSON> [ 436349.38021429 5012294.37741428] <-> IFC [ 436348.16695688 5012293.69142313] (dist: 1.39m, conf: 0.907)\n", "Match 533: <PERSON><PERSON> [ 436095.94936765 5012226.40967647] <-> IFC [ 436092.16695688 5012227.69142313] (dist: 3.99m, conf: 0.734)\n", "Match 534: <PERSON><PERSON> [ 436086.56166667 5012235.853875  ] <-> IFC [ 436092.16695688 5012235.69142313] (dist: 5.61m, conf: 0.626)\n", "Match 535: <PERSON><PERSON> [ 436378.00543478 5012274.34047826] <-> IFC [ 436376.16695688 5012275.69142313] (dist: 2.28m, conf: 0.848)\n", "Match 536: <PERSON><PERSON> [ 435788.01177273 5011414.60290909] <-> IFC [ 435790.16695688 5011421.69142313] (dist: 7.41m, conf: 0.506)\n", "Match 537: <PERSON><PERSON> [ 436043.49277778 5011026.41938889] <-> IFC [ 436046.16695688 5011027.69142313] (dist: 2.96m, conf: 0.803)\n", "Match 538: <PERSON><PERSON> [ 436077.28573529 5012378.06761765] <-> IFC [ 436072.16695688 5012377.69142313] (dist: 5.13m, conf: 0.658)\n", "Match 539: <PERSON><PERSON> [ 436406.469225 5012182.249125] <-> IFC [ 436406.16695688 5012179.69142313] (dist: 2.58m, conf: 0.828)\n", "Match 540: <PERSON><PERSON> [ 436208.82347368 5012356.82515789] <-> IFC [ 436206.16695688 5012359.69142313] (dist: 3.91m, conf: 0.739)\n", "Match 541: <PERSON><PERSON> [ 436277.32393548 5011149.06664516] <-> IFC [ 436274.16695688 5011147.69142313] (dist: 3.44m, conf: 0.770)\n", "Match 542: <PERSON><PERSON> [ 435365.56279167 5011787.10966667] <-> IFC [ 435362.16695688 5011783.69142313] (dist: 4.82m, conf: 0.679)\n", "Match 543: <PERSON><PERSON> [ 436605.43353333 5011775.2436    ] <-> IFC [ 436606.16695688 5011773.69142313] (dist: 1.72m, conf: 0.886)\n", "Match 544: <PERSON><PERSON> [ 435975.72057895 5011511.47663158] <-> IFC [ 435980.16695688 5011513.69142313] (dist: 4.97m, conf: 0.669)\n", "Match 545: <PERSON><PERSON> [ 436096.59776923 5012292.81953846] <-> IFC [ 436092.16695688 5012295.69142313] (dist: 5.28m, conf: 0.648)\n", "Match 546: <PERSON><PERSON> [ 435835.37488679 5011426.84562264] <-> IFC [ 435838.16695688 5011429.69142313] (dist: 3.99m, conf: 0.734)\n", "Match 547: <PERSON><PERSON> [ 435836.34072727 5011411.08642424] <-> IFC [ 435836.16695688 5011411.69142313] (dist: 0.63m, conf: 0.958)\n", "Match 548: <PERSON><PERSON> [ 436358.72959259 5012266.47681481] <-> IFC [ 436358.16695688 5012265.69142313] (dist: 0.97m, conf: 0.936)\n", "Match 549: <PERSON><PERSON> [ 435355.70547368 5011970.66989474] <-> IFC [ 435352.16695688 5011969.69142313] (dist: 3.67m, conf: 0.755)\n", "Match 550: <PERSON><PERSON> [ 436298.05483333 5011265.28008333] <-> IFC [ 436293.16695688 5011263.69142313] (dist: 5.14m, conf: 0.657)\n", "Match 551: <PERSON><PERSON> [ 435938.50639583 5012152.1184375 ] <-> IFC [ 435942.16695688 5012154.69142313] (dist: 4.47m, conf: 0.702)\n", "Match 552: <PERSON><PERSON> [ 436200.1109 5012292.7251] <-> IFC [ 436196.16695688 5012295.69142313] (dist: 4.93m, conf: 0.671)\n", "Match 553: <PERSON><PERSON> [ 435638.30938095 5011402.26528571] <-> IFC [ 435638.16695688 5011399.69142313] (dist: 2.58m, conf: 0.828)\n", "Match 554: <PERSON><PERSON> [ 436528.90503922 5012013.86856863] <-> IFC [ 436528.16695688 5012015.69142313] (dist: 1.97m, conf: 0.869)\n", "Match 555: <PERSON><PERSON> [ 436053.6002 5011024.8211] <-> IFC [ 436056.16695688 5011025.69142313] (dist: 2.71m, conf: 0.819)\n", "Match 556: <PERSON><PERSON> [ 436530.02274286 5011939.64231429] <-> IFC [ 436530.16695688 5011935.69142313] (dist: 3.95m, conf: 0.736)\n", "Match 557: <PERSON><PERSON> [ 436549.06490909 5011838.02018182] <-> IFC [ 436548.16695688 5011835.69142313] (dist: 2.50m, conf: 0.834)\n", "Match 558: <PERSON><PERSON> [ 436108.1598 5011720.3102] <-> IFC [ 436112.16695688 5011719.69142313] (dist: 4.05m, conf: 0.730)\n", "Match 559: <PERSON><PERSON> [ 436139.00716667 5011001.99966667] <-> IFC [ 436142.16695688 5011000.69142313] (dist: 3.42m, conf: 0.772)\n", "Match 560: <PERSON><PERSON> [ 436208.88293103 5012278.26575862] <-> IFC [ 436206.16695688 5012281.69142313] (dist: 4.37m, conf: 0.709)\n", "Match 561: <PERSON><PERSON> [ 435534.46328571 5011547.39328571] <-> IFC [ 435534.16695688 5011547.69142313] (dist: 0.42m, conf: 0.972)\n", "Match 562: <PERSON><PERSON> [ 436484.21442857 5011946.88585714] <-> IFC [ 436482.16695688 5011949.69142313] (dist: 3.47m, conf: 0.768)\n", "Match 563: <PERSON><PERSON> [ 435394.19083784 5011708.73305405] <-> IFC [ 435390.16695688 5011706.69142313] (dist: 4.51m, conf: 0.699)\n", "Match 564: <PERSON><PERSON> [ 436066.76941667 5012198.8925    ] <-> IFC [ 436064.16695688 5012195.69142313] (dist: 4.13m, conf: 0.725)\n", "Match 565: <PERSON><PERSON> [ 436321.56430159 5012337.02268254] <-> IFC [ 436320.16695688 5012337.69142313] (dist: 1.55m, conf: 0.897)\n", "Match 566: <PERSON><PERSON> [ 436012.59661538 5012248.703     ] <-> IFC [ 436016.16695688 5012252.69142313] (dist: 5.35m, conf: 0.643)\n", "Match 567: <PERSON><PERSON> [ 435909.92517857 5012185.12414286] <-> IFC [ 435914.16695688 5012181.69142313] (dist: 5.46m, conf: 0.636)\n", "Match 568: <PERSON><PERSON> [ 435421.59060714 5011775.18392857] <-> IFC [ 435420.16695688 5011778.69142313] (dist: 3.79m, conf: 0.748)\n", "Match 569: <PERSON><PERSON> [ 436321.33583333 5012249.29211111] <-> IFC [ 436320.16695688 5012245.69142313] (dist: 3.79m, conf: 0.748)\n", "Match 570: <PERSON><PERSON> [ 436327.96841176 5011655.03141177] <-> IFC [ 436326.16695688 5011653.69142313] (dist: 2.25m, conf: 0.850)\n", "Match 571: <PERSON><PERSON> [ 435742.22225    5011319.72221428] <-> IFC [ 435752.16695688 5011319.69142313] (dist: 9.94m, conf: 0.337)\n", "Match 572: <PERSON><PERSON> [ 436199.73275 5012145.8305 ] <-> IFC [ 436196.16695688 5012145.69142313] (dist: 3.57m, conf: 0.762)\n", "Match 573: <PERSON><PERSON> [ 436372.16166667 5011952.14766667] <-> IFC [ 436370.16695688 5011953.69142313] (dist: 2.52m, conf: 0.832)\n", "Match 574: <PERSON><PERSON> [ 436283.79939394 5012295.73560606] <-> IFC [ 436282.16695688 5012293.69142313] (dist: 2.62m, conf: 0.826)\n", "Match 575: <PERSON><PERSON> [ 436500.55361538 5012046.25315385] <-> IFC [ 436500.16695688 5012043.69142313] (dist: 2.59m, conf: 0.827)\n", "Match 576: <PERSON><PERSON> [ 436378.2605098  5012161.50456863] <-> IFC [ 436376.16695688 5012157.69142313] (dist: 4.35m, conf: 0.710)\n", "Match 577: <PERSON><PERSON> [ 436048.85027451 5012363.58907843] <-> IFC [ 436044.16695688 5012361.69142313] (dist: 5.05m, conf: 0.663)\n", "Match 578: <PERSON><PERSON> [ 436653.0825     5011906.81483333] <-> IFC [ 436654.16695688 5011907.69142313] (dist: 1.39m, conf: 0.907)\n", "Match 579: <PERSON><PERSON> [ 436227.20961194 5012434.20570149] <-> IFC [ 436224.16695688 5012435.69142313] (dist: 3.39m, conf: 0.774)\n", "Match 580: <PERSON><PERSON> [ 436243.82847368 5011635.26726316] <-> IFC [ 436240.16695688 5011637.69142313] (dist: 4.39m, conf: 0.707)\n", "Match 581: <PERSON><PERSON> [ 436095.85341379 5012280.75544828] <-> IFC [ 436092.16695688 5012277.69142313] (dist: 4.79m, conf: 0.680)\n", "Match 582: <PERSON><PERSON> [ 435844.4545  5012081.70175] <-> IFC [ 435846.16695688 5012079.69142313] (dist: 2.64m, conf: 0.824)\n", "Match 583: <PERSON><PERSON> [ 436208.691825 5012374.674125] <-> IFC [ 436206.16695688 5012375.69142313] (dist: 2.72m, conf: 0.819)\n", "Match 584: <PERSON><PERSON> [ 436340.34946296 5012236.24785185] <-> IFC [ 436338.16695688 5012238.69142313] (dist: 3.28m, conf: 0.782)\n", "Match 585: <PERSON><PERSON> [ 435393.70147368 5011739.77336842] <-> IFC [ 435390.16695688 5011740.69142313] (dist: 3.65m, conf: 0.757)\n", "Match 586: <PERSON><PERSON> [ 436368.94235135 5012181.42305405] <-> IFC [ 436368.16695688 5012177.69142313] (dist: 3.81m, conf: 0.746)\n", "Match 587: <PERSON><PERSON> [ 436672.15766667 5011829.1745    ] <-> IFC [ 436672.16695688 5011832.69142313] (dist: 3.52m, conf: 0.766)\n", "Match 588: <PERSON><PERSON> [ 436086.41634 5012299.4102 ] <-> IFC [ 436082.16695688 5012298.69142313] (dist: 4.31m, conf: 0.713)\n", "Match 589: <PERSON><PERSON> [ 436105.30185965 5012387.57340351] <-> IFC [ 436102.16695688 5012385.69142313] (dist: 3.66m, conf: 0.756)\n", "Match 590: <PERSON><PERSON> [ 435900.60157895 5012099.71457895] <-> IFC [ 435904.16695688 5012101.69142313] (dist: 4.08m, conf: 0.728)\n", "Match 591: <PERSON><PERSON> [ 436153.4165625 5012163.0964375] <-> IFC [ 436158.16695688 5012160.69142313] (dist: 5.32m, conf: 0.645)\n", "Match 592: <PERSON><PERSON> [ 435826.16255556 5012216.98894444] <-> IFC [ 435828.16695688 5012213.69142313] (dist: 3.86m, conf: 0.743)\n", "Match 593: <PERSON><PERSON> [ 436299.58751282 5011615.21907692] <-> IFC [ 436296.16695688 5011617.69142313] (dist: 4.22m, conf: 0.719)\n", "Match 594: <PERSON><PERSON> [ 436236.94117391 5012180.65037681] <-> IFC [ 436234.16695688 5012179.69142313] (dist: 2.94m, conf: 0.804)\n", "Match 595: <PERSON><PERSON> [ 435402.68090698 5011759.74374419] <-> IFC [ 435400.16695688 5011763.69142313] (dist: 4.68m, conf: 0.688)\n", "Match 596: <PERSON><PERSON> [ 435807.78066667 5011491.19304762] <-> IFC [ 435808.16695688 5011499.69142313] (dist: 8.51m, conf: 0.433)\n", "Match 597: <PERSON><PERSON> [ 436022.101      5012258.12490909] <-> IFC [ 436026.16695688 5012257.69142313] (dist: 4.09m, conf: 0.727)\n", "Match 598: <PERSON><PERSON> [ 436312.2435283 5012209.0004151] <-> IFC [ 436310.16695688 5012207.69142313] (dist: 2.45m, conf: 0.836)\n", "Match 599: <PERSON><PERSON> [ 436709.20058621 5011830.53796552] <-> IFC [ 436710.16695688 5011829.69142313] (dist: 1.28m, conf: 0.914)\n", "Match 600: <PERSON><PERSON> [ 435966.81127778 5012056.52011111] <-> IFC [ 435970.16695688 5012055.69142313] (dist: 3.46m, conf: 0.770)\n", "Match 601: <PERSON><PERSON> [ 436265.7094 5012169.5261] <-> IFC [ 436262.16695688 5012165.69142313] (dist: 5.22m, conf: 0.652)\n", "Match 602: <PERSON><PERSON> [ 435928.85675    5011637.66666667] <-> IFC [ 435932.16695688 5011637.69142313] (dist: 3.31m, conf: 0.779)\n", "Match 603: <PERSON><PERSON> [ 436269.36358824 5011249.93182353] <-> IFC [ 436274.16695688 5011249.69142313] (dist: 4.81m, conf: 0.679)\n", "Match 604: <PERSON><PERSON> [ 435909.72807692 5012163.60138462] <-> IFC [ 435913.16695688 5012165.69142313] (dist: 4.02m, conf: 0.732)\n", "Match 605: <PERSON><PERSON> [ 435506.61175862 5011788.53206897] <-> IFC [ 435504.16695688 5011789.69142313] (dist: 2.71m, conf: 0.820)\n", "Match 606: <PERSON><PERSON> [ 436172.3894 5012236.81  ] <-> IFC [ 436168.16695688 5012235.69142313] (dist: 4.37m, conf: 0.709)\n", "Match 607: <PERSON><PERSON> [ 436283.82137838 5012324.45040541] <-> IFC [ 436282.16695688 5012327.69142313] (dist: 3.64m, conf: 0.757)\n", "Match 608: <PERSON><PERSON> [ 435770.12375   5011324.7730625] <-> IFC [ 435761.16695688 5011325.69142313] (dist: 9.00m, conf: 0.400)\n", "Match 609: <PERSON><PERSON> [ 435430.4651875 5011796.547    ] <-> IFC [ 435428.16695688 5011799.69142313] (dist: 3.89m, conf: 0.740)\n", "Match 610: <PERSON><PERSON> [ 435675.24703846 5011975.79253846] <-> IFC [ 435676.16695688 5011973.69142313] (dist: 2.29m, conf: 0.847)\n", "Match 611: <PERSON><PERSON> [ 436597.2488 5011907.7004] <-> IFC [ 436596.16695688 5011911.69142313] (dist: 4.14m, conf: 0.724)\n", "Match 612: <PERSON><PERSON> [ 436501.60428571 5011936.78867857] <-> IFC [ 436502.16695688 5011937.69142313] (dist: 1.06m, conf: 0.929)\n", "Match 613: <PERSON><PERSON> [ 436615.62147619 5011897.51323809] <-> IFC [ 436616.16695688 5011903.69142313] (dist: 6.20m, conf: 0.587)\n", "Match 614: <PERSON><PERSON> [ 436227.2302 5012221.4612] <-> IFC [ 436224.16695688 5012221.69142313] (dist: 3.07m, conf: 0.795)\n", "Match 615: <PERSON><PERSON> [ 436068.25892683 5012373.56646341] <-> IFC [ 436064.16695688 5012371.69142313] (dist: 4.50m, conf: 0.700)\n", "Match 616: <PERSON><PERSON> [ 436433.71688889 5012093.889     ] <-> IFC [ 436434.16695688 5012093.69142313] (dist: 0.49m, conf: 0.967)\n", "Match 617: <PERSON><PERSON> [ 436299.88515 5011635.3492 ] <-> IFC [ 436296.16695688 5011633.69142313] (dist: 4.07m, conf: 0.729)\n", "Match 618: <PERSON><PERSON> [ 436217.64633333 5012415.66148889] <-> IFC [ 436216.16695688 5012411.69142313] (dist: 4.24m, conf: 0.718)\n", "Match 619: <PERSON><PERSON> [ 436217.894      5012433.28086274] <-> IFC [ 436216.16695688 5012437.69142313] (dist: 4.74m, conf: 0.684)\n", "Match 620: <PERSON><PERSON> [ 436086.63883333 5012377.10744444] <-> IFC [ 436082.16695688 5012373.69142313] (dist: 5.63m, conf: 0.625)\n", "Match 621: <PERSON><PERSON> [ 436184.35735294 5010979.43405882] <-> IFC [ 436188.16695688 5010979.69142313] (dist: 3.82m, conf: 0.745)\n", "Match 622: <PERSON><PERSON> [ 436462.8163 5012126.3534] <-> IFC [ 436462.16695688 5012125.69142313] (dist: 0.93m, conf: 0.938)\n", "Match 623: <PERSON><PERSON> [ 436455.67109091 5011192.06909091] <-> IFC [ 436454.16695688 5011195.69142313] (dist: 3.92m, conf: 0.739)\n", "Match 624: <PERSON><PERSON> [ 436482.17925 5012122.32375] <-> IFC [ 436482.16695688 5012125.69142313] (dist: 3.37m, conf: 0.775)\n", "Match 625: <PERSON><PERSON> [ 436604.90635 5011914.4833 ] <-> IFC [ 436606.16695688 5011915.69142313] (dist: 1.75m, conf: 0.884)\n", "Match 626: <PERSON><PERSON> [ 436303.03806667 5012262.61916667] <-> IFC [ 436300.16695688 5012261.69142313] (dist: 3.02m, conf: 0.799)\n", "Match 627: <PERSON><PERSON> [ 436624.92710526 5011823.99336842] <-> IFC [ 436624.16695688 5011825.69142313] (dist: 1.86m, conf: 0.876)\n", "Match 628: <PERSON><PERSON> [ 436337.0653913  5011648.81078261] <-> IFC [ 436334.16695688 5011645.69142313] (dist: 4.26m, conf: 0.716)\n", "Match 629: <PERSON><PERSON> [ 435797.32314286 5011382.73514286] <-> IFC [ 435800.16695688 5011386.69142313] (dist: 4.87m, conf: 0.675)\n", "Match 630: <PERSON><PERSON> [ 435787.94921212 5011638.71993939] <-> IFC [ 435790.16695688 5011641.69142313] (dist: 3.71m, conf: 0.753)\n", "Match 631: <PERSON><PERSON> [ 436453.27736 5012093.99308] <-> IFC [ 436452.16695688 5012095.69142313] (dist: 2.03m, conf: 0.865)\n", "Match 632: <PERSON><PERSON> [ 436388.01455556 5012134.86655556] <-> IFC [ 436386.16695688 5012135.69142313] (dist: 2.02m, conf: 0.865)\n", "Match 633: <PERSON><PERSON> [ 436604.49148387 5011987.95574194] <-> IFC [ 436604.16695688 5011987.69142313] (dist: 0.42m, conf: 0.972)\n", "Match 634: <PERSON><PERSON> [ 435327.98965306 5011757.22144898] <-> IFC [ 435324.16695688 5011761.69142313] (dist: 5.88m, conf: 0.608)\n", "Match 635: <PERSON><PERSON> [ 435908.25482051 5012267.348     ] <-> IFC [ 435912.16695688 5012269.69142313] (dist: 4.56m, conf: 0.696)\n", "Match 636: <PERSON><PERSON> [ 435981.902   5012220.80375] <-> IFC [ 435978.16695688 5012221.69142313] (dist: 3.84m, conf: 0.744)\n", "Match 637: <PERSON><PERSON> [ 436236.93095833 5012361.74616667] <-> IFC [ 436234.16695688 5012361.69142313] (dist: 2.76m, conf: 0.816)\n", "Match 638: Drone [ 435816.0882 5011375.05  ] <-> IFC [ 435818.16695688 5011371.69142313] (dist: 3.95m, conf: 0.737)\n", "Match 639: <PERSON><PERSON> [ 435497.25298361 5011659.94157377] <-> IFC [ 435496.16695688 5011657.69142313] (dist: 2.50m, conf: 0.833)\n", "Match 640: <PERSON><PERSON> [ 435582.59530435 5011527.97604348] <-> IFC [ 435580.16695688 5011535.69142313] (dist: 8.09m, conf: 0.461)\n", "Match 641: <PERSON><PERSON> [ 436576.83109677 5011820.99      ] <-> IFC [ 436578.16695688 5011817.69142313] (dist: 3.56m, conf: 0.763)\n", "Match 642: <PERSON><PERSON> [ 436180.20459091 5012428.75464773] <-> IFC [ 436178.16695688 5012427.69142313] (dist: 2.30m, conf: 0.847)\n", "Match 643: <PERSON><PERSON> [ 436548.23771429 5011819.63428571] <-> IFC [ 436548.16695688 5011819.69142313] (dist: 0.09m, conf: 0.994)\n", "Match 644: <PERSON><PERSON> [ 436331.21967647 5012137.97905882] <-> IFC [ 436330.16695688 5012141.69142313] (dist: 3.86m, conf: 0.743)\n", "Match 645: <PERSON><PERSON> [ 435778.99285106 5011684.02225532] <-> IFC [ 435780.16695688 5011681.69142313] (dist: 2.61m, conf: 0.826)\n", "Match 646: <PERSON><PERSON> [ 436012.8314 5012218.1124] <-> IFC [ 436016.16695688 5012218.69142313] (dist: 3.39m, conf: 0.774)\n", "Match 647: <PERSON><PERSON> [ 436691.17937037 5011851.64766667] <-> IFC [ 436692.16695688 5011853.69142313] (dist: 2.27m, conf: 0.849)\n", "Match 648: <PERSON><PERSON> [ 435956.50276923 5011914.45146154] <-> IFC [ 435952.16695688 5011915.69142313] (dist: 4.51m, conf: 0.699)\n", "Match 649: <PERSON><PERSON> [ 436247.77709091 5012279.98390909] <-> IFC [ 436244.16695688 5012279.69142313] (dist: 3.62m, conf: 0.759)\n", "Match 650: <PERSON><PERSON> [ 436171.4256087  5012156.30336956] <-> IFC [ 436168.16695688 5012159.69142313] (dist: 4.70m, conf: 0.687)\n", "Match 651: <PERSON><PERSON> [ 435957.19608 5012048.66794] <-> IFC [ 435960.16695688 5012049.69142313] (dist: 3.14m, conf: 0.791)\n", "Match 652: <PERSON><PERSON> [ 436548.87545455 5011814.32327273] <-> IFC [ 436548.16695688 5011810.69142313] (dist: 3.70m, conf: 0.753)\n", "Match 653: <PERSON><PERSON> [ 435995.04275   5012056.8048125] <-> IFC [ 435998.16695688 5012059.69142313] (dist: 4.25m, conf: 0.716)\n", "Match 654: <PERSON><PERSON> [ 435713.85989474 5011394.04552632] <-> IFC [ 435714.16695688 5011397.69142313] (dist: 3.66m, conf: 0.756)\n", "Match 655: <PERSON><PERSON> [ 435608.5476 5011451.4386] <-> IFC [ 435610.16695688 5011451.69142313] (dist: 1.64m, conf: 0.891)\n", "Match 656: <PERSON><PERSON> [ 436165.330625   5010981.06371875] <-> IFC [ 436170.16695688 5010981.69142313] (dist: 4.88m, conf: 0.675)\n", "Match 657: Dr<PERSON> [ 436208.72021569 5012221.36570588] <-> IFC [ 436206.16695688 5012221.69142313] (dist: 2.57m, conf: 0.828)\n", "Match 658: <PERSON><PERSON> [ 436520.65657143 5011945.15442857] <-> IFC [ 436520.16695688 5011947.69142313] (dist: 2.58m, conf: 0.828)\n", "Match 659: <PERSON><PERSON> [ 436444.8055   5012066.101625] <-> IFC [ 436444.16695688 5012065.69142313] (dist: 0.76m, conf: 0.949)\n", "Match 660: <PERSON><PERSON> [ 435891.394      5012170.82080357] <-> IFC [ 435894.16695688 5012171.69142313] (dist: 2.91m, conf: 0.806)\n", "Match 661: <PERSON><PERSON> [ 436546.5545 5012060.942 ] <-> IFC [ 436548.16695688 5012059.69142313] (dist: 2.04m, conf: 0.864)\n", "Match 662: <PERSON><PERSON> [ 436537.95835714 5012023.5495    ] <-> IFC [ 436538.16695688 5012029.69142313] (dist: 6.15m, conf: 0.590)\n", "Match 663: <PERSON><PERSON> [ 436341.00161111 5012110.44522222] <-> IFC [ 436338.16695688 5012111.69142313] (dist: 3.10m, conf: 0.794)\n", "Match 664: <PERSON><PERSON> [ 436265.19578125 5012204.9399375 ] <-> IFC [ 436262.16695688 5012207.69142313] (dist: 4.09m, conf: 0.727)\n", "Match 665: <PERSON><PERSON> [ 436234.9128 5012424.2278] <-> IFC [ 436234.16695688 5012427.69142313] (dist: 3.54m, conf: 0.764)\n", "Match 666: <PERSON><PERSON> [ 435983.14688372 5012324.33902325] <-> IFC [ 435978.16695688 5012323.69142313] (dist: 5.02m, conf: 0.665)\n", "Match 667: <PERSON><PERSON> [ 436033.61694737 5012066.01294737] <-> IFC [ 436036.16695688 5012065.69142313] (dist: 2.57m, conf: 0.829)\n", "Match 668: <PERSON><PERSON> [ 436328.43256522 5011647.11373913] <-> IFC [ 436326.16695688 5011645.69142313] (dist: 2.68m, conf: 0.822)\n", "Match 669: <PERSON><PERSON> [ 436344.1206 5012026.86  ] <-> IFC [ 436340.16695688 5012023.69142313] (dist: 5.07m, conf: 0.662)\n", "Match 670: <PERSON><PERSON> [ 436236.94597222 5012326.56491667] <-> IFC [ 436234.16695688 5012327.69142313] (dist: 3.00m, conf: 0.800)\n", "Match 671: <PERSON><PERSON> [ 436483.683      5011940.51343478] <-> IFC [ 436482.16695688 5011941.69142313] (dist: 1.92m, conf: 0.872)\n", "Match 672: <PERSON><PERSON> [ 436682.674  5011872.4132] <-> IFC [ 436682.16695688 5011873.69142313] (dist: 1.38m, conf: 0.908)\n", "Match 673: <PERSON><PERSON> [ 435938.24636 5012011.70228] <-> IFC [ 435942.16695688 5012009.69142313] (dist: 4.41m, conf: 0.706)\n", "Match 674: <PERSON><PERSON> [ 435939.53755556 5012140.66605555] <-> IFC [ 435942.16695688 5012137.69142313] (dist: 3.97m, conf: 0.735)\n", "Match 675: <PERSON><PERSON> [ 436264.8965     5012396.33783333] <-> IFC [ 436262.16695688 5012393.69142313] (dist: 3.80m, conf: 0.747)\n", "Match 676: <PERSON><PERSON> [ 436555.29333333 5012049.99116667] <-> IFC [ 436558.16695688 5012047.69142313] (dist: 3.68m, conf: 0.755)\n", "Match 677: <PERSON><PERSON> [ 435752.13457143 5011313.4225    ] <-> IFC [ 435752.16695688 5011319.69142313] (dist: 6.27m, conf: 0.582)\n", "Match 678: <PERSON><PERSON> [ 436330.729675 5012275.499975] <-> IFC [ 436330.16695688 5012275.69142313] (dist: 0.59m, conf: 0.960)\n", "Match 679: <PERSON><PERSON> [ 435341.80358228 5011809.73562025] <-> IFC [ 435344.16695688 5011811.69142313] (dist: 3.07m, conf: 0.795)\n", "Match 680: <PERSON><PERSON> [ 436446.17133333 5011973.66533333] <-> IFC [ 436446.16695688 5011975.69142313] (dist: 2.03m, conf: 0.865)\n", "Match 681: <PERSON><PERSON> [ 436269.2797 5011150.3699] <-> IFC [ 436274.16695688 5011147.69142313] (dist: 5.57m, conf: 0.628)\n", "Match 682: <PERSON><PERSON> [ 435853.4645102  5012038.37810204] <-> IFC [ 435856.16695688 5012034.69142313] (dist: 4.57m, conf: 0.695)\n", "Match 683: <PERSON><PERSON> [ 435714.3791875 5011402.2484375] <-> IFC [ 435714.16695688 5011405.69142313] (dist: 3.45m, conf: 0.770)\n", "Match 684: <PERSON><PERSON> [ 436170.05548485 5012283.64175757] <-> IFC [ 436168.16695688 5012279.69142313] (dist: 4.38m, conf: 0.708)\n", "Match 685: <PERSON><PERSON> [ 435581.20695238 5012102.11604762] <-> IFC [ 435580.16695688 5012099.69142313] (dist: 2.64m, conf: 0.824)\n", "Match 686: <PERSON><PERSON> [ 436679.9172 5011894.1892] <-> IFC [ 436682.16695688 5011889.69142313] (dist: 5.03m, conf: 0.665)\n", "Match 687: <PERSON><PERSON> [ 436197.63535714 5012446.73271429] <-> IFC [ 436196.16695688 5012447.69142313] (dist: 1.75m, conf: 0.883)\n", "Match 688: <PERSON><PERSON> [ 436483.29365 5011951.8537 ] <-> IFC [ 436482.16695688 5011949.69142313] (dist: 2.44m, conf: 0.837)\n", "Match 689: <PERSON><PERSON> [ 435825.63527273 5012151.87118182] <-> IFC [ 435828.16695688 5012155.69142313] (dist: 4.58m, conf: 0.694)\n", "Match 690: <PERSON><PERSON> [ 436387.31236 5012246.34284] <-> IFC [ 436386.16695688 5012245.69142313] (dist: 1.32m, conf: 0.912)\n", "Match 691: <PERSON><PERSON> [ 436215.163125   5011681.55079167] <-> IFC [ 436212.16695688 5011683.69142313] (dist: 3.68m, conf: 0.755)\n", "Match 692: <PERSON><PERSON> [ 436230.79814286 5011304.73604762] <-> IFC [ 436226.16695688 5011305.69142313] (dist: 4.73m, conf: 0.685)\n", "Match 693: <PERSON><PERSON> [ 436189.1287 5012292.6123] <-> IFC [ 436186.16695688 5012289.69142313] (dist: 4.16m, conf: 0.723)\n", "Match 694: <PERSON><PERSON> [ 436246.97447368 5012165.66047368] <-> IFC [ 436244.16695688 5012161.69142313] (dist: 4.86m, conf: 0.676)\n", "Match 695: <PERSON><PERSON> [ 436246.74262069 5012385.594     ] <-> IFC [ 436244.16695688 5012391.69142313] (dist: 6.62m, conf: 0.559)\n", "Match 696: <PERSON><PERSON> [ 436395.8298 5012249.6924] <-> IFC [ 436396.16695688 5012251.69142313] (dist: 2.03m, conf: 0.865)\n", "Match 697: <PERSON><PERSON> [ 436161.96119697 5012290.73007576] <-> IFC [ 436158.16695688 5012291.69142313] (dist: 3.91m, conf: 0.739)\n", "Match 698: <PERSON><PERSON> [ 436292.87304348 5012316.95952174] <-> IFC [ 436292.16695688 5012315.69142313] (dist: 1.45m, conf: 0.903)\n", "Match 699: <PERSON><PERSON> [ 435393.47341667 5011990.09916667] <-> IFC [ 435390.16695688 5011991.69142313] (dist: 3.67m, conf: 0.755)\n", "Match 700: <PERSON><PERSON> [ 436300.24855263 5011584.96310526] <-> IFC [ 436296.16695688 5011583.69142313] (dist: 4.28m, conf: 0.715)\n", "Match 701: <PERSON><PERSON> [ 435450.65852632 5011625.82452632] <-> IFC [ 435448.16695688 5011623.69142313] (dist: 3.28m, conf: 0.781)\n", "Match 702: <PERSON><PERSON> [ 436624.22584 5011843.9794 ] <-> IFC [ 436624.16695688 5011841.69142313] (dist: 2.29m, conf: 0.847)\n", "Match 703: <PERSON><PERSON> [ 436058.554      5012288.96509091] <-> IFC [ 436054.16695688 5012291.69142313] (dist: 5.17m, conf: 0.656)\n", "Match 704: <PERSON><PERSON> [ 436114.75443333 5012345.6059    ] <-> IFC [ 436110.16695688 5012347.69142313] (dist: 5.04m, conf: 0.664)\n", "Match 705: <PERSON><PERSON> [ 436445.8888 5012070.813 ] <-> IFC [ 436444.16695688 5012073.69142313] (dist: 3.35m, conf: 0.776)\n", "Match 706: <PERSON><PERSON> [ 436322.7944 5012132.6762] <-> IFC [ 436320.16695688 5012135.69142313] (dist: 4.00m, conf: 0.733)\n", "Match 707: <PERSON><PERSON> [ 436061.65786441 5011028.0570339 ] <-> IFC [ 436056.16695688 5011025.69142313] (dist: 5.98m, conf: 0.601)\n", "Match 708: <PERSON><PERSON> [ 435731.47672727 5011452.10313636] <-> IFC [ 435732.16695688 5011447.69142313] (dist: 4.47m, conf: 0.702)\n", "Match 709: <PERSON><PERSON> [ 436528.42130645 5012049.74479032] <-> IFC [ 436528.16695688 5012049.69142313] (dist: 0.26m, conf: 0.983)\n", "Match 710: <PERSON><PERSON> [ 436322.92314286 5012082.408     ] <-> IFC [ 436320.16695688 5012085.69142313] (dist: 4.29m, conf: 0.714)\n", "Match 711: <PERSON><PERSON> [ 435300.43758824 5011779.68982353] <-> IFC [ 435296.16695688 5011775.69142313] (dist: 5.85m, conf: 0.610)\n", "Match 712: <PERSON><PERSON> [ 435985.8562  5012003.92798] <-> IFC [ 435989.16695688 5012001.69142313] (dist: 4.00m, conf: 0.734)\n", "Match 713: <PERSON><PERSON> [ 435506.07283333 5012041.10091667] <-> IFC [ 435504.16695688 5012039.69142313] (dist: 2.37m, conf: 0.842)\n", "Match 714: <PERSON><PERSON> [ 436067.62624 5012289.4144 ] <-> IFC [ 436064.16695688 5012287.69142313] (dist: 3.86m, conf: 0.742)\n", "Match 715: <PERSON><PERSON> [ 436227.85136364 5012292.60218182] <-> IFC [ 436224.16695688 5012289.69142313] (dist: 4.70m, conf: 0.687)\n", "Match 716: <PERSON><PERSON> [ 435741.46927586 5011299.20703448] <-> IFC [ 435742.16695688 5011301.69142313] (dist: 2.58m, conf: 0.828)\n", "Match 717: <PERSON><PERSON> [ 436332.89373333 5012010.67273333] <-> IFC [ 436332.16695688 5012009.69142313] (dist: 1.22m, conf: 0.919)\n", "Match 718: <PERSON><PERSON> [ 436133.59340541 5012379.25627027] <-> IFC [ 436130.16695688 5012375.69142313] (dist: 4.94m, conf: 0.670)\n", "Match 719: <PERSON><PERSON> [ 436069.28   5012182.1764] <-> IFC [ 436072.16695688 5012183.69142313] (dist: 3.26m, conf: 0.783)\n", "Match 720: <PERSON><PERSON> [ 436029.46125 5012227.8172 ] <-> IFC [ 436034.16695688 5012229.69142313] (dist: 5.07m, conf: 0.662)\n", "Match 721: <PERSON><PERSON> [ 436048.41938462 5012239.74176923] <-> IFC [ 436054.16695688 5012239.69142313] (dist: 5.75m, conf: 0.617)\n", "Match 722: <PERSON><PERSON> [ 436258.8556     5011168.57653333] <-> IFC [ 436256.16695688 5011165.69142313] (dist: 3.94m, conf: 0.737)\n", "Match 723: <PERSON><PERSON> [ 435573.21257895 5011532.09915789] <-> IFC [ 435572.16695688 5011533.69142313] (dist: 1.90m, conf: 0.873)\n", "Match 724: <PERSON><PERSON> [ 436372.12321053 5011149.34560526] <-> IFC [ 436370.16695688 5011147.69142313] (dist: 2.56m, conf: 0.829)\n", "Match 725: <PERSON><PERSON> [ 436022.56325 5012023.56475] <-> IFC [ 436018.16695688 5012023.69142313] (dist: 4.40m, conf: 0.707)\n", "Match 726: <PERSON><PERSON> [ 436277.72       5012024.66892308] <-> IFC [ 436274.16695688 5012023.69142313] (dist: 3.69m, conf: 0.754)\n", "Match 727: <PERSON><PERSON> [ 436331.18133333 5012316.54955555] <-> IFC [ 436330.16695688 5012317.69142313] (dist: 1.53m, conf: 0.898)\n", "Match 728: <PERSON><PERSON> [ 436302.54483824 5012330.41636765] <-> IFC [ 436300.16695688 5012329.69142313] (dist: 2.49m, conf: 0.834)\n", "Match 729: <PERSON><PERSON> [ 436030.315      5012294.81430435] <-> IFC [ 436034.16695688 5012296.69142313] (dist: 4.28m, conf: 0.714)\n", "Match 730: <PERSON><PERSON> [ 435815.68837037 5012078.25062963] <-> IFC [ 435818.16695688 5012081.69142313] (dist: 4.24m, conf: 0.717)\n", "Match 731: <PERSON><PERSON> [ 436302.46923404 5012294.63276596] <-> IFC [ 436300.16695688 5012295.69142313] (dist: 2.53m, conf: 0.831)\n", "Match 732: <PERSON><PERSON> [ 436643.13276923 5011847.465     ] <-> IFC [ 436644.16695688 5011843.69142313] (dist: 3.91m, conf: 0.739)\n", "Match 733: <PERSON><PERSON> [ 436301.759      5012230.81582759] <-> IFC [ 436300.16695688 5012227.69142313] (dist: 3.51m, conf: 0.766)\n", "Match 734: <PERSON><PERSON> [ 436133.15978947 5012391.12284211] <-> IFC [ 436130.16695688 5012393.69142313] (dist: 3.94m, conf: 0.737)\n", "Match 735: <PERSON><PERSON> [ 436340.06852083 5012302.91225   ] <-> IFC [ 436338.16695688 5012305.69142313] (dist: 3.37m, conf: 0.776)\n", "Match 736: <PERSON><PERSON> [ 436161.38473684 5012406.28810526] <-> IFC [ 436158.16695688 5012409.69142313] (dist: 4.68m, conf: 0.688)\n", "Match 737: <PERSON><PERSON> [ 436161.9605     5012272.02946429] <-> IFC [ 436158.16695688 5012273.69142313] (dist: 4.14m, conf: 0.724)\n", "Match 738: <PERSON><PERSON> [ 436217.76171429 5012212.30942857] <-> IFC [ 436216.16695688 5012213.69142313] (dist: 2.11m, conf: 0.859)\n", "Match 739: <PERSON><PERSON> [ 436118.52606452 5010927.73687097] <-> IFC [ 436122.16695688 5010927.69142313] (dist: 3.64m, conf: 0.757)\n", "Match 740: <PERSON><PERSON> [ 436259.97257143 5011126.60642857] <-> IFC [ 436255.16695688 5011123.69142313] (dist: 5.62m, conf: 0.625)\n", "Match 741: <PERSON><PERSON> [ 436156.97366667 5011239.3025    ] <-> IFC [ 436150.16695688 5011237.69142313] (dist: 6.99m, conf: 0.534)\n", "Match 742: <PERSON><PERSON> [ 436305.483875 5011167.983625] <-> IFC [ 436302.16695688 5011169.69142313] (dist: 3.73m, conf: 0.751)\n", "Match 743: <PERSON><PERSON> [ 435964.7225   5012290.256625] <-> IFC [ 435968.16695688 5012293.69142313] (dist: 4.86m, conf: 0.676)\n", "Match 744: <PERSON><PERSON> [ 436218.0448 5012317.4634] <-> IFC [ 436216.16695688 5012319.69142313] (dist: 2.91m, conf: 0.806)\n", "Match 745: <PERSON><PERSON> [ 436133.541      5012182.57688889] <-> IFC [ 436130.16695688 5012179.69142313] (dist: 4.44m, conf: 0.704)\n", "Match 746: <PERSON><PERSON> [ 436381.34004762 5011177.17561905] <-> IFC [ 436378.16695688 5011175.69142313] (dist: 3.50m, conf: 0.766)\n", "Match 747: <PERSON><PERSON> [ 436115.150125   5012404.08395833] <-> IFC [ 436120.16695688 5012403.69142313] (dist: 5.03m, conf: 0.665)\n", "Match 748: <PERSON><PERSON> [ 436227.64194444 5012370.11877778] <-> IFC [ 436224.16695688 5012367.69142313] (dist: 4.24m, conf: 0.717)\n", "Match 749: <PERSON><PERSON> [ 436189.823      5012200.97066667] <-> IFC [ 436186.16695688 5012197.69142313] (dist: 4.91m, conf: 0.673)\n", "Match 750: <PERSON><PERSON> [ 436362.25494444 5011110.81988889] <-> IFC [ 436360.16695688 5011109.69142313] (dist: 2.37m, conf: 0.842)\n", "Match 751: <PERSON><PERSON> [ 436493.04573585 5011960.08337736] <-> IFC [ 436492.16695688 5011958.69142313] (dist: 1.65m, conf: 0.890)\n", "Match 752: <PERSON><PERSON> [ 435363.91583333 5011976.61      ] <-> IFC [ 435362.16695688 5011973.69142313] (dist: 3.40m, conf: 0.773)\n", "Match 753: <PERSON><PERSON> [ 435834.7424 5011740.1137] <-> IFC [ 435837.16695688 5011735.69142313] (dist: 5.04m, conf: 0.664)\n", "Match 754: <PERSON><PERSON> [ 436416.01615789 5012079.00147368] <-> IFC [ 436414.16695688 5012079.69142313] (dist: 1.97m, conf: 0.868)\n", "Match 755: <PERSON><PERSON> [ 436323.63921429 5012024.97371428] <-> IFC [ 436322.16695688 5012021.69142313] (dist: 3.60m, conf: 0.760)\n", "Match 756: <PERSON><PERSON> [ 435977.1742 5011242.4032] <-> IFC [ 435980.16695688 5011245.69142313] (dist: 4.45m, conf: 0.704)\n", "Match 757: <PERSON><PERSON> [ 435413.69013333 5011664.35453333] <-> IFC [ 435410.16695688 5011667.69142313] (dist: 4.85m, conf: 0.676)\n", "Match 758: <PERSON><PERSON> [ 435967.5755 5012061.9965] <-> IFC [ 435970.16695688 5012063.69142313] (dist: 3.10m, conf: 0.794)\n", "Match 759: <PERSON><PERSON> [ 436309.67751515 5011608.71833333] <-> IFC [ 436306.16695688 5011609.69142313] (dist: 3.64m, conf: 0.757)\n", "Match 760: <PERSON><PERSON> [ 436229.91368 5012000.71892] <-> IFC [ 436226.16695688 5011997.69142313] (dist: 4.82m, conf: 0.679)\n", "Match 761: <PERSON><PERSON> [ 436397.1462093  5012069.39551163] <-> IFC [ 436396.16695688 5012073.69142313] (dist: 4.41m, conf: 0.706)\n", "Match 762: <PERSON><PERSON> [ 436256.48625   5012367.4780625] <-> IFC [ 436254.16695688 5012363.69142313] (dist: 4.44m, conf: 0.704)\n", "Match 763: <PERSON><PERSON> [ 436265.33545455 5012274.22188636] <-> IFC [ 436262.16695688 5012275.69142313] (dist: 3.49m, conf: 0.767)\n", "Match 764: <PERSON><PERSON> [ 435806.47366667 5012137.904     ] <-> IFC [ 435808.16695688 5012135.69142313] (dist: 2.79m, conf: 0.814)\n", "Match 765: <PERSON><PERSON> [ 436123.25925    5012379.86366667] <-> IFC [ 436120.16695688 5012379.69142313] (dist: 3.10m, conf: 0.794)\n", "Match 766: <PERSON><PERSON> [ 436202.49335714 5011807.1685    ] <-> IFC [ 436198.16695688 5011809.69142313] (dist: 5.01m, conf: 0.666)\n", "Match 767: <PERSON><PERSON> [ 436068.19571429 5012189.13896429] <-> IFC [ 436072.16695688 5012191.69142313] (dist: 4.72m, conf: 0.685)\n", "Match 768: <PERSON><PERSON> [ 436291.646  5011617.2775] <-> IFC [ 436288.16695688 5011615.69142313] (dist: 3.82m, conf: 0.745)\n", "Match 769: <PERSON><PERSON> [ 436118.5772381  5010945.70778571] <-> IFC [ 436122.16695688 5010944.69142313] (dist: 3.73m, conf: 0.751)\n", "Match 770: <PERSON><PERSON> [ 436172.29414286 5012357.93042857] <-> IFC [ 436168.16695688 5012355.69142313] (dist: 4.70m, conf: 0.687)\n", "Match 771: <PERSON><PERSON> [ 435695.46879412 5011435.33297059] <-> IFC [ 435694.16695688 5011433.69142313] (dist: 2.10m, conf: 0.860)\n", "Match 772: <PERSON><PERSON> [ 436584.57438462 5012011.82738461] <-> IFC [ 436586.16695688 5012011.69142313] (dist: 1.60m, conf: 0.893)\n", "Match 773: <PERSON><PERSON> [ 436537.50473333 5012016.45586667] <-> IFC [ 436538.16695688 5012011.69142313] (dist: 4.81m, conf: 0.679)\n", "Match 774: <PERSON><PERSON> [ 436339.83213333 5012175.48873333] <-> IFC [ 436338.16695688 5012171.69142313] (dist: 4.15m, conf: 0.724)\n", "Match 775: <PERSON><PERSON> [ 436095.43253333 5012307.27513333] <-> IFC [ 436092.16695688 5012303.69142313] (dist: 4.85m, conf: 0.677)\n", "Match 776: <PERSON><PERSON> [ 436285.08742857 5012111.57685714] <-> IFC [ 436282.16695688 5012107.69142313] (dist: 4.86m, conf: 0.676)\n", "Match 777: <PERSON><PERSON> [ 436098.40575  5012097.423625] <-> IFC [ 436094.16695688 5012097.69142313] (dist: 4.25m, conf: 0.717)\n", "Match 778: <PERSON><PERSON> [ 436434.23056 5012117.01564] <-> IFC [ 436434.16695688 5012119.69142313] (dist: 2.68m, conf: 0.822)\n", "Match 779: Dr<PERSON> [ 436189.96030769 5012179.34192308] <-> IFC [ 436186.16695688 5012181.69142313] (dist: 4.46m, conf: 0.703)\n", "Match 780: <PERSON><PERSON> [ 436156.1806875 5010985.0753125] <-> IFC [ 436160.16695688 5010987.69142313] (dist: 4.77m, conf: 0.682)\n", "Match 781: <PERSON><PERSON> [ 435957.033      5012150.70998305] <-> IFC [ 435960.16695688 5012147.69142313] (dist: 4.35m, conf: 0.710)\n", "Match 782: <PERSON><PERSON> [ 435553.70490909 5011725.41451515] <-> IFC [ 435552.16695688 5011725.69142313] (dist: 1.56m, conf: 0.896)\n", "Match 783: <PERSON><PERSON> [ 436388.0973  5012158.77905] <-> IFC [ 436386.16695688 5012161.69142313] (dist: 3.49m, conf: 0.767)\n", "Match 784: <PERSON><PERSON> [ 436207.69863158 5011627.29673684] <-> IFC [ 436212.16695688 5011623.69142313] (dist: 5.74m, conf: 0.617)\n", "Match 785: <PERSON><PERSON> [ 435965.06613043 5012308.0735    ] <-> IFC [ 435968.16695688 5012309.69142313] (dist: 3.50m, conf: 0.767)\n", "Match 786: <PERSON><PERSON> [ 436011.79981481 5012314.61281481] <-> IFC [ 436016.16695688 5012311.69142313] (dist: 5.25m, conf: 0.650)\n", "Match 787: <PERSON><PERSON> [ 436453.56284211 5012033.59021052] <-> IFC [ 436452.16695688 5012035.69142313] (dist: 2.52m, conf: 0.832)\n", "Match 788: <PERSON><PERSON> [ 436594.48430769 5011999.90265385] <-> IFC [ 436596.16695688 5011999.69142313] (dist: 1.70m, conf: 0.887)\n", "Match 789: <PERSON><PERSON> [ 436250.89875 5011167.3715 ] <-> IFC [ 436246.16695688 5011166.69142313] (dist: 4.78m, conf: 0.681)\n", "Match 790: <PERSON><PERSON> [ 436529.702875 5011925.511375] <-> IFC [ 436530.16695688 5011927.69142313] (dist: 2.23m, conf: 0.851)\n", "Match 791: <PERSON><PERSON> [ 435901.63341667 5012014.025875  ] <-> IFC [ 435904.16695688 5012013.69142313] (dist: 2.56m, conf: 0.830)\n", "Match 792: <PERSON><PERSON> [ 436265.82628571 5012141.55821428] <-> IFC [ 436262.16695688 5012139.69142313] (dist: 4.11m, conf: 0.726)\n", "Match 793: <PERSON><PERSON> [ 435515.413875 5011651.90625 ] <-> IFC [ 435514.16695688 5011649.69142313] (dist: 2.54m, conf: 0.831)\n", "Match 794: <PERSON><PERSON> [ 435477.88280952 5011826.23528572] <-> IFC [ 435476.16695688 5011833.69142313] (dist: 7.65m, conf: 0.490)\n", "Match 795: <PERSON><PERSON> [ 436266.11802632 5012123.40142105] <-> IFC [ 436262.16695688 5012123.69142313] (dist: 3.96m, conf: 0.736)\n", "Match 796: <PERSON><PERSON> [ 435581.55145455 5011539.95036364] <-> IFC [ 435580.16695688 5011543.69142313] (dist: 3.99m, conf: 0.734)\n", "Match 797: <PERSON><PERSON> [ 435977.38133333 5012062.20288889] <-> IFC [ 435980.16695688 5012061.69142313] (dist: 2.83m, conf: 0.811)\n", "Match 798: <PERSON><PERSON> [ 435973.76557143 5012220.86207143] <-> IFC [ 435978.16695688 5012221.69142313] (dist: 4.48m, conf: 0.701)\n", "Match 799: <PERSON><PERSON> [ 436166.7128 5010989.9739] <-> IFC [ 436170.16695688 5010989.69142313] (dist: 3.47m, conf: 0.769)\n", "Match 800: <PERSON><PERSON> [ 436086.23745 5012268.8806 ] <-> IFC [ 436082.16695688 5012265.69142313] (dist: 5.17m, conf: 0.655)\n", "Match 801: <PERSON><PERSON> [ 435722.84184483 5011404.46558621] <-> IFC [ 435723.16695688 5011409.69142313] (dist: 5.24m, conf: 0.651)\n", "Match 802: <PERSON><PERSON> [ 435571.70784211 5011512.51821053] <-> IFC [ 435572.16695688 5011507.69142313] (dist: 4.85m, conf: 0.677)\n", "Match 803: <PERSON><PERSON> [ 435488.321      5011577.03734615] <-> IFC [ 435486.16695688 5011575.69142313] (dist: 2.54m, conf: 0.831)\n", "Match 804: <PERSON><PERSON> [ 435751.41547826 5011433.02521739] <-> IFC [ 435742.16695688 5011429.69142313] (dist: 9.83m, conf: 0.345)\n", "Match 805: <PERSON><PERSON> [ 436032.27959091 5012053.74163636] <-> IFC [ 436028.16695688 5012049.69142313] (dist: 5.77m, conf: 0.615)\n", "Match 806: <PERSON><PERSON> [ 436011.44375 5012299.83175] <-> IFC [ 436006.16695688 5012297.69142313] (dist: 5.69m, conf: 0.620)\n", "Match 807: <PERSON><PERSON> [ 436117.83528571 5010918.66728572] <-> IFC [ 436122.16695688 5010919.69142313] (dist: 4.45m, conf: 0.703)\n", "Match 808: <PERSON><PERSON> [ 435554.58753333 5011522.047     ] <-> IFC [ 435552.16695688 5011521.69142313] (dist: 2.45m, conf: 0.837)\n", "Match 809: <PERSON><PERSON> [ 436012.         5012325.45617647] <-> IFC [ 436016.16695688 5012328.69142313] (dist: 5.28m, conf: 0.648)\n", "Match 810: <PERSON><PERSON> [ 436293.31586667 5012216.16561667] <-> IFC [ 436292.16695688 5012213.69142313] (dist: 2.73m, conf: 0.818)\n", "Match 811: <PERSON><PERSON> [ 435619.1867931  5011744.12796552] <-> IFC [ 435618.16695688 5011743.69142313] (dist: 1.11m, conf: 0.926)\n", "Match 812: <PERSON><PERSON> [ 436281.44661905 5011070.84533333] <-> IFC [ 436284.16695688 5011077.69142313] (dist: 7.37m, conf: 0.509)\n", "Match 813: <PERSON><PERSON> [ 436567.37628571 5011934.33128571] <-> IFC [ 436568.16695688 5011929.69142313] (dist: 4.71m, conf: 0.686)\n", "Match 814: <PERSON><PERSON> [ 436276.85886207 5012000.56531034] <-> IFC [ 436274.16695688 5011997.69142313] (dist: 3.94m, conf: 0.737)\n", "Match 815: <PERSON><PERSON> [ 436245.39637838 5012367.42872973] <-> IFC [ 436244.16695688 5012367.69142313] (dist: 1.26m, conf: 0.916)\n", "Match 816: <PERSON><PERSON> [ 436211.08673684 5011975.89536842] <-> IFC [ 436208.16695688 5011983.69142313] (dist: 8.32m, conf: 0.445)\n", "Match 817: <PERSON><PERSON> [ 436052.11266667 5011080.46423809] <-> IFC [ 436056.16695688 5011083.69142313] (dist: 5.18m, conf: 0.655)\n", "Match 818: <PERSON><PERSON> [ 436453.45478378 5012077.84024324] <-> IFC [ 436452.16695688 5012077.69142313] (dist: 1.30m, conf: 0.914)\n", "Match 819: <PERSON><PERSON> [ 436096.11593103 5012243.15441379] <-> IFC [ 436092.16695688 5012245.69142313] (dist: 4.69m, conf: 0.687)\n", "Match 820: <PERSON><PERSON> [ 435853.83942105 5011355.79715789] <-> IFC [ 435856.16695688 5011359.69142313] (dist: 4.54m, conf: 0.698)\n", "Match 821: <PERSON><PERSON> [ 435815.62375    5012030.89391667] <-> IFC [ 435818.16695688 5012027.69142313] (dist: 4.09m, conf: 0.727)\n", "Match 822: <PERSON><PERSON> [ 436298.066 5011145.631] <-> IFC [ 436294.16695688 5011145.69142313] (dist: 3.90m, conf: 0.740)\n", "Match 823: <PERSON><PERSON> [ 435835.71584211 5011365.84236842] <-> IFC [ 435838.16695688 5011361.69142313] (dist: 4.82m, conf: 0.679)\n", "Match 824: <PERSON><PERSON> [ 436218.11154839 5012128.97132258] <-> IFC [ 436224.16695688 5012128.69142313] (dist: 6.06m, conf: 0.596)\n", "Match 825: <PERSON><PERSON> [ 436184.3611875 5010997.47625  ] <-> IFC [ 436188.16695688 5010997.69142313] (dist: 3.81m, conf: 0.746)\n", "Match 826: <PERSON><PERSON> [ 435505.25275 5011636.68175] <-> IFC [ 435504.16695688 5011636.69142313] (dist: 1.09m, conf: 0.928)\n", "Match 827: <PERSON><PERSON> [ 436539.94188372 5011853.6142093 ] <-> IFC [ 436540.16695688 5011857.69142313] (dist: 4.08m, conf: 0.728)\n", "Match 828: <PERSON><PERSON> [ 436136.96326316 5011042.10973684] <-> IFC [ 436132.16695688 5011039.69142313] (dist: 5.37m, conf: 0.642)\n", "Match 829: <PERSON><PERSON> [ 436106.66288889 5012180.71222222] <-> IFC [ 436110.16695688 5012181.69142313] (dist: 3.64m, conf: 0.757)\n", "Match 830: <PERSON><PERSON> [ 436086.40037255 5012314.89721569] <-> IFC [ 436082.16695688 5012315.69142313] (dist: 4.31m, conf: 0.713)\n", "Match 831: <PERSON><PERSON> [ 436481.67230159 5012074.10315873] <-> IFC [ 436482.16695688 5012075.69142313] (dist: 1.66m, conf: 0.889)\n", "Match 832: <PERSON><PERSON> [ 436264.66105    5012374.56686667] <-> IFC [ 436262.16695688 5012375.69142313] (dist: 2.74m, conf: 0.818)\n", "Match 833: <PERSON><PERSON> [ 436260.1329 5011118.2962] <-> IFC [ 436256.16695688 5011115.69142313] (dist: 4.74m, conf: 0.684)\n", "Match 834: <PERSON><PERSON> [ 436048.85885714 5012338.0924    ] <-> IFC [ 436044.16695688 5012335.69142313] (dist: 5.27m, conf: 0.649)\n", "Match 835: <PERSON><PERSON> [ 436218.41860294 5012195.66482353] <-> IFC [ 436216.16695688 5012197.69142313] (dist: 3.03m, conf: 0.798)\n", "Match 836: <PERSON><PERSON> [ 435826.66972727 5012079.13131818] <-> IFC [ 435828.16695688 5012078.69142313] (dist: 1.56m, conf: 0.896)\n", "Match 837: <PERSON><PERSON> [ 435957.8598     5012167.30603333] <-> IFC [ 435960.16695688 5012163.69142313] (dist: 4.29m, conf: 0.714)\n", "Match 838: Dr<PERSON> [ 435917.57264865 5012283.942     ] <-> IFC [ 435920.16695688 5012283.69142313] (dist: 2.61m, conf: 0.826)\n", "Match 839: <PERSON><PERSON> [ 436557.64836667 5012007.61553333] <-> IFC [ 436558.16695688 5012005.69142313] (dist: 1.99m, conf: 0.867)\n", "Match 840: Dr<PERSON> [ 435929.01778571 5012119.092     ] <-> IFC [ 435932.16695688 5012115.69142313] (dist: 4.63m, conf: 0.691)\n", "Match 841: <PERSON><PERSON> [ 436474.35314286 5011937.77909524] <-> IFC [ 436472.16695688 5011935.69142313] (dist: 3.02m, conf: 0.798)\n", "Match 842: <PERSON><PERSON> [ 436068.9864375 5011845.6975   ] <-> IFC [ 436066.16695688 5011843.69142313] (dist: 3.46m, conf: 0.769)\n", "Match 843: <PERSON><PERSON> [ 436021.65221739 5012266.03930435] <-> IFC [ 436026.16695688 5012265.69142313] (dist: 4.53m, conf: 0.698)\n", "Match 844: <PERSON><PERSON> [ 436283.75321053 5012285.99973684] <-> IFC [ 436282.16695688 5012285.69142313] (dist: 1.62m, conf: 0.892)\n", "Match 845: <PERSON><PERSON> [ 435995.53557143 5011476.29228572] <-> IFC [ 435998.16695688 5011477.69142313] (dist: 2.98m, conf: 0.801)\n", "Match 846: <PERSON><PERSON> [ 436171.14017647 5012321.54094118] <-> IFC [ 436168.16695688 5012321.69142313] (dist: 2.98m, conf: 0.802)\n", "Match 847: <PERSON><PERSON> [ 436152.86744444 5012293.5812963 ] <-> IFC [ 436148.16695688 5012293.69142313] (dist: 4.70m, conf: 0.687)\n", "Match 848: <PERSON><PERSON> [ 435420.972  5011669.0722] <-> IFC [ 435420.16695688 5011667.69142313] (dist: 1.60m, conf: 0.893)\n", "Match 849: Dr<PERSON> [ 436521.1005     5011888.07518182] <-> IFC [ 436520.16695688 5011889.69142313] (dist: 1.87m, conf: 0.876)\n", "Match 850: <PERSON><PERSON> [ 436443.27795455 5012159.37004545] <-> IFC [ 436444.16695688 5012157.69142313] (dist: 1.90m, conf: 0.873)\n", "Match 851: <PERSON><PERSON> [ 436537.96883333 5011921.3005    ] <-> IFC [ 436540.16695688 5011923.69142313] (dist: 3.25m, conf: 0.783)\n", "Match 852: <PERSON><PERSON> [ 436070.1512381 5012088.0607619] <-> IFC [ 436074.16695688 5012087.69142313] (dist: 4.03m, conf: 0.731)\n", "Match 853: <PERSON><PERSON> [ 436105.71212903 5012369.70280645] <-> IFC [ 436102.16695688 5012368.69142313] (dist: 3.69m, conf: 0.754)\n", "Match 854: <PERSON><PERSON> [ 436087.26066667 5012216.63738461] <-> IFC [ 436092.16695688 5012219.69142313] (dist: 5.78m, conf: 0.615)\n", "Match 855: <PERSON><PERSON> [ 436013.37222222 5011607.47177778] <-> IFC [ 436018.16695688 5011609.69142313] (dist: 5.28m, conf: 0.648)\n", "Match 856: <PERSON><PERSON> [ 436284.140875  5012390.8520625] <-> IFC [ 436282.16695688 5012385.69142313] (dist: 5.53m, conf: 0.632)\n", "Match 857: <PERSON><PERSON> [ 435891.85228571 5011508.41714286] <-> IFC [ 435894.16695688 5011505.69142313] (dist: 3.58m, conf: 0.762)\n", "Match 858: <PERSON><PERSON> [ 436127.9485     5011022.79244444] <-> IFC [ 436132.16695688 5011023.69142313] (dist: 4.31m, conf: 0.712)\n", "Match 859: <PERSON><PERSON> [ 436567.552875 5012037.827625] <-> IFC [ 436566.16695688 5012035.69142313] (dist: 2.55m, conf: 0.830)\n", "Match 860: <PERSON><PERSON> [ 436320.26011111 5012213.36433333] <-> IFC [ 436320.16695688 5012211.69142313] (dist: 1.68m, conf: 0.888)\n", "Match 861: <PERSON><PERSON> [ 436492.46275    5011943.05216667] <-> IFC [ 436492.16695688 5011941.69142313] (dist: 1.39m, conf: 0.907)\n", "Match 862: <PERSON><PERSON> [ 436577.92861538 5011839.45107692] <-> IFC [ 436578.16695688 5011843.69142313] (dist: 4.25m, conf: 0.717)\n", "Match 863: <PERSON><PERSON> [ 435450.86322727 5011659.60354545] <-> IFC [ 435448.16695688 5011657.69142313] (dist: 3.31m, conf: 0.780)\n", "Match 864: <PERSON><PERSON> [ 436548.384225 5011927.851725] <-> IFC [ 436548.16695688 5011929.69142313] (dist: 1.85m, conf: 0.877)\n", "Match 865: <PERSON><PERSON> [ 436253.742 5012413.919] <-> IFC [ 436254.16695688 5012413.69142313] (dist: 0.48m, conf: 0.968)\n", "Match 866: <PERSON><PERSON> [ 436171.33259524 5012180.59997619] <-> IFC [ 436168.16695688 5012175.69142313] (dist: 5.84m, conf: 0.611)\n", "Match 867: <PERSON><PERSON> [ 435796.887375 5011377.920375] <-> IFC [ 435790.16695688 5011379.69142313] (dist: 6.95m, conf: 0.537)\n", "Match 868: <PERSON><PERSON> [ 436398.95161538 5011190.04630769] <-> IFC [ 436398.16695688 5011191.69142313] (dist: 1.82m, conf: 0.878)\n", "Match 869: <PERSON><PERSON> [ 435749.66926829 5011834.51965854] <-> IFC [ 435752.16695688 5011837.69142313] (dist: 4.04m, conf: 0.731)\n", "Match 870: <PERSON><PERSON> [ 436324.26276471 5012001.84423529] <-> IFC [ 436322.16695688 5012005.69142313] (dist: 4.38m, conf: 0.708)\n", "Match 871: <PERSON><PERSON> [ 436368.70335 5012285.08845] <-> IFC [ 436368.16695688 5012287.69142313] (dist: 2.66m, conf: 0.823)\n", "Match 872: <PERSON><PERSON> [ 436190.33953488 5012237.95086046] <-> IFC [ 436186.16695688 5012239.69142313] (dist: 4.52m, conf: 0.699)\n", "Match 873: <PERSON><PERSON> [ 435834.91891667 5011628.30372222] <-> IFC [ 435838.16695688 5011626.69142313] (dist: 3.63m, conf: 0.758)\n", "Match 874: <PERSON><PERSON> [ 436199.0511     5012391.13193333] <-> IFC [ 436196.16695688 5012388.69142313] (dist: 3.78m, conf: 0.748)\n", "Match 875: <PERSON><PERSON> [ 436481.64912 5012057.9046 ] <-> IFC [ 436482.16695688 5012059.69142313] (dist: 1.86m, conf: 0.876)\n", "Match 876: <PERSON><PERSON> [ 436245.7324 5012357.1133] <-> IFC [ 436244.16695688 5012357.69142313] (dist: 1.67m, conf: 0.889)\n", "Match 877: Dr<PERSON> [ 436155.72018966 5011004.60918965] <-> IFC [ 436160.16695688 5011003.69142313] (dist: 4.54m, conf: 0.697)\n", "Match 878: <PERSON><PERSON> [ 436236.46346667 5012300.1242    ] <-> IFC [ 436234.16695688 5012297.69142313] (dist: 3.35m, conf: 0.777)\n", "Match 879: <PERSON><PERSON> [ 435874.595625 5011482.96275 ] <-> IFC [ 435876.16695688 5011483.69142313] (dist: 1.73m, conf: 0.885)\n", "Match 880: <PERSON><PERSON> [ 435966.7748 5011511.9864] <-> IFC [ 435970.16695688 5011513.69142313] (dist: 3.80m, conf: 0.747)\n", "Match 881: Dr<PERSON> [ 436160.92608824 5012416.51320588] <-> IFC [ 436158.16695688 5012417.69142313] (dist: 3.00m, conf: 0.800)\n", "Match 882: <PERSON><PERSON> [ 436208.26784444 5012265.22404445] <-> IFC [ 436206.16695688 5012264.69142313] (dist: 2.17m, conf: 0.856)\n", "Match 883: <PERSON><PERSON> [ 435911.04976923 5011526.702     ] <-> IFC [ 435913.16695688 5011529.69142313] (dist: 3.66m, conf: 0.756)\n", "Match 884: <PERSON><PERSON> [ 436095.96225641 5012270.52166667] <-> IFC [ 436092.16695688 5012269.69142313] (dist: 3.89m, conf: 0.741)\n", "Match 885: <PERSON><PERSON> [ 435544.50217647 5011671.53894118] <-> IFC [ 435552.16695688 5011673.69142313] (dist: 7.96m, conf: 0.469)\n", "Match 886: <PERSON><PERSON> [ 435910.30875862 5012024.44075862] <-> IFC [ 435914.16695688 5012029.69142313] (dist: 6.52m, conf: 0.566)\n", "Match 887: <PERSON><PERSON> [ 435694.75645161 5011449.11345161] <-> IFC [ 435694.16695688 5011449.69142313] (dist: 0.83m, conf: 0.945)\n", "Match 888: Dr<PERSON> [ 435788.27590323 5011305.87845161] <-> IFC [ 435790.16695688 5011311.69142313] (dist: 6.11m, conf: 0.592)\n", "Match 889: Dr<PERSON> [ 435826.72946667 5012092.42673333] <-> IFC [ 435828.16695688 5012095.69142313] (dist: 3.57m, conf: 0.762)\n", "Match 890: <PERSON><PERSON> [ 436318.43475    5011610.71441667] <-> IFC [ 436316.16695688 5011609.69142313] (dist: 2.49m, conf: 0.834)\n", "Match 891: <PERSON><PERSON> [ 436198.5926 5012415.3705] <-> IFC [ 436196.16695688 5012413.69142313] (dist: 2.95m, conf: 0.803)\n", "Match 892: <PERSON><PERSON> [ 436264.04935 5012402.8377 ] <-> IFC [ 436262.16695688 5012401.69142313] (dist: 2.20m, conf: 0.853)\n", "Match 893: <PERSON><PERSON> [ 436349.57553571 5012202.59392857] <-> IFC [ 436348.16695688 5012201.69142313] (dist: 1.67m, conf: 0.888)\n", "Match 894: Drone [ 436624.44492308 5011888.13607692] <-> IFC [ 436624.16695688 5011883.69142313] (dist: 4.45m, conf: 0.703)\n", "Match 895: <PERSON><PERSON> [ 436596.14042553 5011836.58721277] <-> IFC [ 436596.16695688 5011835.69142313] (dist: 0.90m, conf: 0.940)\n", "Match 896: <PERSON><PERSON> [ 436132.85552941 5012169.72264706] <-> IFC [ 436130.16695688 5012171.69142313] (dist: 3.33m, conf: 0.778)\n", "Match 897: <PERSON><PERSON> [ 436425.18881818 5012189.33309091] <-> IFC [ 436424.16695688 5012189.69142313] (dist: 1.08m, conf: 0.928)\n", "Match 898: <PERSON><PERSON> [ 436274.64542105 5012235.14636842] <-> IFC [ 436272.16695688 5012237.69142313] (dist: 3.55m, conf: 0.763)\n", "Match 899: <PERSON><PERSON> [ 435610.03226667 5011520.67203333] <-> IFC [ 435610.16695688 5011517.69142313] (dist: 2.98m, conf: 0.801)\n", "Match 900: <PERSON><PERSON> [ 436396.954      5012244.29221428] <-> IFC [ 436396.16695688 5012242.69142313] (dist: 1.78m, conf: 0.881)\n", "Match 901: <PERSON><PERSON> [ 435995.08805882 5011984.50629412] <-> IFC [ 435998.16695688 5011983.69142313] (dist: 3.18m, conf: 0.788)\n", "Match 902: <PERSON><PERSON> [ 436274.16173529 5012367.62480882] <-> IFC [ 436272.16695688 5012363.69142313] (dist: 4.41m, conf: 0.706)\n", "Match 903: <PERSON><PERSON> [ 436500.46255172 5012111.36462069] <-> IFC [ 436500.16695688 5012111.69142313] (dist: 0.44m, conf: 0.971)\n", "Match 904: <PERSON><PERSON> [ 435835.02636 5011462.09592] <-> IFC [ 435837.16695688 5011461.69142313] (dist: 2.18m, conf: 0.855)\n", "Match 905: <PERSON><PERSON> [ 436086.81192405 5012344.4003924 ] <-> IFC [ 436082.16695688 5012341.69142313] (dist: 5.38m, conf: 0.642)\n", "Match 906: <PERSON><PERSON> [ 436199.15328 5012428.6328 ] <-> IFC [ 436196.16695688 5012431.69142313] (dist: 4.27m, conf: 0.715)\n", "Match 907: <PERSON><PERSON> [ 435318.72294118 5011775.74082353] <-> IFC [ 435314.16695688 5011777.69142313] (dist: 4.96m, conf: 0.670)\n", "Match 908: <PERSON><PERSON> [ 435731.70633333 5011464.8438    ] <-> IFC [ 435732.16695688 5011465.69142313] (dist: 0.96m, conf: 0.936)\n", "Match 909: <PERSON><PERSON> [ 436218.4278125 5012405.5829375] <-> IFC [ 436216.16695688 5012403.69142313] (dist: 2.95m, conf: 0.803)\n", "Match 910: <PERSON><PERSON> [ 436208.67   5012149.2082] <-> IFC [ 436206.16695688 5012145.69142313] (dist: 4.32m, conf: 0.712)\n", "Match 911: <PERSON><PERSON> [ 435525.42491304 5011655.55665217] <-> IFC [ 435524.16695688 5011655.69142313] (dist: 1.27m, conf: 0.916)\n", "Match 912: <PERSON><PERSON> [ 436133.1575 5012333.2692] <-> IFC [ 436130.16695688 5012333.69142313] (dist: 3.02m, conf: 0.799)\n", "Match 913: <PERSON><PERSON> [ 436691.39857895 5011879.37636842] <-> IFC [ 436692.16695688 5011877.69142313] (dist: 1.85m, conf: 0.877)\n", "Match 914: <PERSON><PERSON> [ 436473.86135714 5011946.7545    ] <-> IFC [ 436472.16695688 5011943.69142313] (dist: 3.50m, conf: 0.767)\n", "Match 915: <PERSON><PERSON> [ 436264.30246667 5012346.83656667] <-> IFC [ 436262.16695688 5012343.69142313] (dist: 3.80m, conf: 0.747)\n", "Match 916: <PERSON><PERSON> [ 435760.73517391 5011302.08530435] <-> IFC [ 435752.16695688 5011297.69142313] (dist: 9.63m, conf: 0.358)\n", "Match 917: <PERSON><PERSON> [ 436259.53921212 5011139.30833333] <-> IFC [ 436255.16695688 5011139.69142313] (dist: 4.39m, conf: 0.707)\n", "Match 918: <PERSON><PERSON> [ 436301.404875  5012366.5603125] <-> IFC [ 436300.16695688 5012361.69142313] (dist: 5.02m, conf: 0.665)\n", "Match 919: <PERSON><PERSON> [ 436171.16994444 5012436.45772222] <-> IFC [ 436168.16695688 5012439.69142313] (dist: 4.41m, conf: 0.706)\n", "Match 920: <PERSON><PERSON> [ 436079.21307143 5011740.153     ] <-> IFC [ 436074.16695688 5011739.69142313] (dist: 5.07m, conf: 0.662)\n", "Match 921: <PERSON><PERSON> [ 436256.99690909 5012197.63090909] <-> IFC [ 436254.16695688 5012195.69142313] (dist: 3.43m, conf: 0.771)\n", "Match 922: <PERSON><PERSON> [ 436052.41888235 5011092.85639216] <-> IFC [ 436056.16695688 5011092.69142313] (dist: 3.75m, conf: 0.750)\n", "Match 923: <PERSON><PERSON> [ 436256.72833333 5012342.9085    ] <-> IFC [ 436254.16695688 5012345.69142313] (dist: 3.78m, conf: 0.748)\n", "Match 924: <PERSON><PERSON> [ 436265.59386667 5012320.12326667] <-> IFC [ 436262.16695688 5012317.69142313] (dist: 4.20m, conf: 0.720)\n", "Match 925: <PERSON><PERSON> [ 436033.99410345 5011046.48668965] <-> IFC [ 436036.16695688 5011048.69142313] (dist: 3.10m, conf: 0.794)\n", "Match 926: <PERSON><PERSON> [ 435685.06986667 5011436.84766667] <-> IFC [ 435686.16695688 5011433.69142313] (dist: 3.34m, conf: 0.777)\n", "Match 927: <PERSON><PERSON> [ 435760.93752941 5011488.51317647] <-> IFC [ 435762.16695688 5011491.69142313] (dist: 3.41m, conf: 0.773)\n", "Match 928: <PERSON><PERSON> [ 436039.28742857 5012362.48171429] <-> IFC [ 436044.16695688 5012361.69142313] (dist: 4.94m, conf: 0.670)\n", "Match 929: <PERSON><PERSON> [ 436415.51505882 5012226.101     ] <-> IFC [ 436414.16695688 5012227.69142313] (dist: 2.08m, conf: 0.861)\n", "Match 930: Dr<PERSON> [ 436180.97376923 5012224.09157692] <-> IFC [ 436178.16695688 5012225.69142313] (dist: 3.23m, conf: 0.785)\n", "Match 931: <PERSON><PERSON> [ 436023.01955357 5012057.12805357] <-> IFC [ 436027.16695688 5012057.69142313] (dist: 4.19m, conf: 0.721)\n", "Match 932: <PERSON><PERSON> [ 436236.59742308 5012349.42715385] <-> IFC [ 436234.16695688 5012351.69142313] (dist: 3.32m, conf: 0.779)\n", "Match 933: <PERSON><PERSON> [ 436548.63857143 5011997.92942857] <-> IFC [ 436548.16695688 5011999.69142313] (dist: 1.82m, conf: 0.878)\n", "Match 934: <PERSON><PERSON> [ 436539.87033333 5011945.90233333] <-> IFC [ 436540.16695688 5011949.69142313] (dist: 3.80m, conf: 0.747)\n", "Match 935: <PERSON><PERSON> [ 436340.55831343 5012134.92885075] <-> IFC [ 436338.16695688 5012137.69142313] (dist: 3.65m, conf: 0.756)\n", "Match 936: <PERSON><PERSON> [ 435479.86854545 5011627.33918182] <-> IFC [ 435476.16695688 5011629.69142313] (dist: 4.39m, conf: 0.708)\n", "Match 937: <PERSON><PERSON> [ 435411.95688235 5011672.54682353] <-> IFC [ 435410.16695688 5011675.69142313] (dist: 3.62m, conf: 0.759)\n", "Match 938: <PERSON><PERSON> [ 436265.8545625 5012103.6575625] <-> IFC [ 436262.16695688 5012105.69142313] (dist: 4.21m, conf: 0.719)\n", "Match 939: <PERSON><PERSON> [ 436338.64533333 5011488.526     ] <-> IFC [ 436334.16695688 5011485.69142313] (dist: 5.30m, conf: 0.647)\n", "Match 940: <PERSON><PERSON> [ 436127.37956098 5010965.66421951] <-> IFC [ 436132.16695688 5010963.69142313] (dist: 5.18m, conf: 0.655)\n", "Match 941: <PERSON><PERSON> [ 435825.89305882 5011959.60135294] <-> IFC [ 435828.16695688 5011957.69142313] (dist: 2.97m, conf: 0.802)\n", "Match 942: <PERSON><PERSON> [ 435936.2076  5012289.05532] <-> IFC [ 435940.16695688 5012293.69142313] (dist: 6.10m, conf: 0.594)\n", "Match 943: <PERSON><PERSON> [ 436496.25644444 5011805.41444444] <-> IFC [ 436496.16695688 5011803.69142313] (dist: 1.73m, conf: 0.885)\n", "Match 944: <PERSON><PERSON> [ 436529.10678571 5012022.54428571] <-> IFC [ 436528.16695688 5012023.69142313] (dist: 1.48m, conf: 0.901)\n", "Match 945: <PERSON><PERSON> [ 435890.71655556 5012046.34688889] <-> IFC [ 435884.16695688 5012049.69142313] (dist: 7.35m, conf: 0.510)\n", "Match 946: <PERSON><PERSON> [ 435290.0325   5011929.863125] <-> IFC [ 435286.16695688 5011931.69142313] (dist: 4.28m, conf: 0.715)\n", "Match 947: <PERSON><PERSON> [ 436061.33933333 5011000.33      ] <-> IFC [ 436056.16695688 5010999.69142313] (dist: 5.21m, conf: 0.653)\n", "Match 948: <PERSON><PERSON> [ 435874.73390909 5011471.35972727] <-> IFC [ 435875.16695688 5011463.01574746] (dist: 8.36m, conf: 0.443)\n", "Match 949: <PERSON><PERSON> [ 436331.75144444 5012104.599     ] <-> IFC [ 436330.16695688 5012107.69142313] (dist: 3.47m, conf: 0.768)\n", "Match 950: <PERSON><PERSON> [ 436058.00632258 5012275.03925806] <-> IFC [ 436054.16695688 5012273.69142313] (dist: 4.07m, conf: 0.729)\n", "Match 951: <PERSON><PERSON> [ 435768.121  5011606.2216] <-> IFC [ 435770.16695688 5011607.69142313] (dist: 2.52m, conf: 0.832)\n", "Match 952: <PERSON><PERSON> [ 436123.4421875 5012386.0588125] <-> IFC [ 436120.16695688 5012387.69142313] (dist: 3.66m, conf: 0.756)\n", "Match 953: <PERSON><PERSON> [ 436020.36208696 5012325.36895652] <-> IFC [ 436016.16695688 5012328.69142313] (dist: 5.35m, conf: 0.643)\n", "Match 954: <PERSON><PERSON> [ 436574.925875 5011998.59325 ] <-> IFC [ 436576.16695688 5011997.69142313] (dist: 1.53m, conf: 0.898)\n", "Match 955: <PERSON><PERSON> [ 435724.3975   5011304.322875] <-> IFC [ 435724.16695688 5011307.69142313] (dist: 3.38m, conf: 0.775)\n", "Match 956: <PERSON><PERSON> [ 436605.82381818 5011865.05109091] <-> IFC [ 436606.16695688 5011865.69142313] (dist: 0.73m, conf: 0.952)\n", "Match 957: <PERSON><PERSON> [ 435299.11178571 5011786.80771429] <-> IFC [ 435296.16695688 5011785.69142313] (dist: 3.15m, conf: 0.790)\n", "Match 958: <PERSON><PERSON> [ 435571.53461111 5011662.36105555] <-> IFC [ 435572.16695688 5011661.69142313] (dist: 0.92m, conf: 0.939)\n", "Match 959: <PERSON><PERSON> [ 436200.04       5012303.28951852] <-> IFC [ 436196.16695688 5012303.69142313] (dist: 3.89m, conf: 0.740)\n", "Match 960: <PERSON><PERSON> [ 436340.54121429 5012249.91464286] <-> IFC [ 436338.16695688 5012247.69142313] (dist: 3.25m, conf: 0.783)\n", "Match 961: <PERSON><PERSON> [ 435514.4560625 5011667.3586875] <-> IFC [ 435514.16695688 5011667.69142313] (dist: 0.44m, conf: 0.971)\n", "Match 962: <PERSON><PERSON> [ 435928.18       5012143.12928571] <-> IFC [ 435932.16695688 5012141.69142313] (dist: 4.24m, conf: 0.717)\n", "Match 963: <PERSON><PERSON> [ 436152.70638095 5012394.98338095] <-> IFC [ 436148.16695688 5012395.69142313] (dist: 4.59m, conf: 0.694)\n", "Match 964: <PERSON><PERSON> [ 436284.80774194 5012099.16529032] <-> IFC [ 436282.16695688 5012099.69142313] (dist: 2.69m, conf: 0.820)\n", "Match 965: <PERSON><PERSON> [ 436500.94805714 5012090.28811428] <-> IFC [ 436500.16695688 5012093.69142313] (dist: 3.49m, conf: 0.767)\n", "Match 966: <PERSON><PERSON> [ 436359.95714706 5012198.40526471] <-> IFC [ 436358.16695688 5012197.69142313] (dist: 1.93m, conf: 0.872)\n", "Match 967: <PERSON><PERSON> [ 435431.84521429 5011803.001     ] <-> IFC [ 435428.16695688 5011799.69142313] (dist: 4.95m, conf: 0.670)\n", "Match 968: Dr<PERSON> [ 435929.32814516 5012096.45020968] <-> IFC [ 435932.16695688 5012099.69142313] (dist: 4.31m, conf: 0.713)\n", "Match 969: <PERSON><PERSON> [ 436180.64214493 5012169.66124638] <-> IFC [ 436178.16695688 5012165.69142313] (dist: 4.68m, conf: 0.688)\n", "Match 970: <PERSON><PERSON> [ 436137.59251351 5010938.76762162] <-> IFC [ 436141.16695688 5010937.69142314] (dist: 3.73m, conf: 0.751)\n", "Match 971: <PERSON><PERSON> [ 435949.10007143 5011531.86821429] <-> IFC [ 435951.16695688 5011533.69142313] (dist: 2.76m, conf: 0.816)\n", "Match 972: <PERSON><PERSON> [ 436387.74681609 5012223.3988046 ] <-> IFC [ 436386.16695688 5012221.69142313] (dist: 2.33m, conf: 0.845)\n", "Match 973: <PERSON><PERSON> [ 436378.49487805 5012143.90970732] <-> IFC [ 436376.16695688 5012147.69142313] (dist: 4.44m, conf: 0.704)\n", "Match 974: <PERSON><PERSON> [ 436348.53927273 5011753.751     ] <-> IFC [ 436344.16695688 5011755.69142313] (dist: 4.78m, conf: 0.681)\n", "Match 975: <PERSON><PERSON> [ 436293.02283333 5012267.96853704] <-> IFC [ 436292.16695688 5012265.69142313] (dist: 2.43m, conf: 0.838)\n", "Match 976: <PERSON><PERSON> [ 436700.02445455 5011824.94659091] <-> IFC [ 436700.16695688 5011823.69142313] (dist: 1.26m, conf: 0.916)\n", "Match 977: <PERSON><PERSON> [ 436479.5462 5012100.4268] <-> IFC [ 436472.16695688 5012103.69142313] (dist: 8.07m, conf: 0.462)\n", "Match 978: <PERSON><PERSON> [ 436126.80166667 5011017.24155556] <-> IFC [ 436132.16695688 5011015.69142313] (dist: 5.58m, conf: 0.628)\n", "Match 979: <PERSON><PERSON> [ 436385.6301875 5011786.591    ] <-> IFC [ 436382.16695688 5011789.69142313] (dist: 4.65m, conf: 0.690)\n", "Match 980: <PERSON><PERSON> [ 435797.74685417 5012146.86458333] <-> IFC [ 435798.16695688 5012147.69142313] (dist: 0.93m, conf: 0.938)\n", "Match 981: <PERSON><PERSON> [ 435976.29519048 5012050.62671429] <-> IFC [ 435980.16695688 5012053.69142313] (dist: 4.94m, conf: 0.671)\n", "Match 982: <PERSON><PERSON> [ 436548.656      5011875.73983333] <-> IFC [ 436548.16695688 5011878.69142313] (dist: 2.99m, conf: 0.801)\n", "Match 983: <PERSON><PERSON> [ 435967.07987805 5012097.72958537] <-> IFC [ 435970.16695688 5012101.69142313] (dist: 5.02m, conf: 0.665)\n", "Match 984: <PERSON><PERSON> [ 436117.49792308 5010961.79823077] <-> IFC [ 436122.16695688 5010961.69142313] (dist: 4.67m, conf: 0.689)\n", "Match 985: <PERSON><PERSON> [ 436463.32765217 5012094.9613913 ] <-> IFC [ 436462.16695688 5012091.69142313] (dist: 3.47m, conf: 0.769)\n", "Match 986: <PERSON><PERSON> [ 435438.91111765 5011782.01729412] <-> IFC [ 435438.16695688 5011779.69142313] (dist: 2.44m, conf: 0.837)\n", "Match 987: <PERSON><PERSON> [ 436132.36533333 5012325.09908333] <-> IFC [ 436130.16695688 5012325.69142313] (dist: 2.28m, conf: 0.848)\n", "Match 988: <PERSON><PERSON> [ 435563.26266667 5011616.96211111] <-> IFC [ 435562.16695688 5011617.69142313] (dist: 1.32m, conf: 0.912)\n", "Match 989: <PERSON><PERSON> [ 436361.02871429 5011118.26585714] <-> IFC [ 436360.16695688 5011117.69142313] (dist: 1.04m, conf: 0.931)\n", "Match 990: <PERSON><PERSON> [ 436265.845      5012299.79916667] <-> IFC [ 436262.16695688 5012300.69142313] (dist: 3.78m, conf: 0.748)\n", "Match 991: <PERSON><PERSON> [ 436481.83326667 5012050.28313333] <-> IFC [ 436482.16695688 5012049.69142313] (dist: 0.68m, conf: 0.955)\n", "Match 992: <PERSON><PERSON> [ 436080.45414286 5011632.90678571] <-> IFC [ 436084.16695688 5011635.69142313] (dist: 4.64m, conf: 0.691)\n", "Match 993: Dr<PERSON> [ 436105.98005882 5012221.92729412] <-> IFC [ 436110.16695688 5012221.69142313] (dist: 4.19m, conf: 0.720)\n", "Match 994: <PERSON><PERSON> [ 435686.477125 5011396.357875] <-> IFC [ 435694.16695688 5011399.69142313] (dist: 8.38m, conf: 0.441)\n", "Match 995: Dr<PERSON> [ 436212.81366667 5010986.95761905] <-> IFC [ 436208.16695688 5010981.69142313] (dist: 7.02m, conf: 0.532)\n", "Match 996: <PERSON><PERSON> [ 436216.35223529 5011628.46911765] <-> IFC [ 436220.16695688 5011631.69142313] (dist: 4.99m, conf: 0.667)\n", "Match 997: <PERSON><PERSON> [ 436161.635      5012361.65919355] <-> IFC [ 436158.16695688 5012357.69142313] (dist: 5.27m, conf: 0.649)\n", "Match 998: <PERSON><PERSON> [ 436099.02708333 5011767.648     ] <-> IFC [ 436103.16695688 5011767.69142313] (dist: 4.14m, conf: 0.724)\n", "Match 999: <PERSON><PERSON> [ 436443.01125 5012075.0355 ] <-> IFC [ 436444.16695688 5012073.69142313] (dist: 1.77m, conf: 0.882)\n", "Match 1000: <PERSON><PERSON> [ 435412.62291667 5011912.64383333] <-> IFC [ 435410.16695688 5011908.69142313] (dist: 4.65m, conf: 0.690)\n", "Match 1001: <PERSON><PERSON> [ 435759.95317647 5011457.22247059] <-> IFC [ 435762.16695688 5011457.69142313] (dist: 2.26m, conf: 0.849)\n", "Match 1002: <PERSON><PERSON> [ 435947.83625 5012062.10655] <-> IFC [ 435942.16695688 5012059.69142313] (dist: 6.16m, conf: 0.589)\n", "Match 1003: <PERSON><PERSON> [ 436179.88741667 5012149.28141667] <-> IFC [ 436178.16695688 5012149.69142313] (dist: 1.77m, conf: 0.882)\n", "Match 1004: <PERSON><PERSON> [ 436321.7051  5012126.54075] <-> IFC [ 436320.16695688 5012127.69142313] (dist: 1.92m, conf: 0.872)\n", "Match 1005: <PERSON><PERSON> [ 436189.12258333 5012264.41858333] <-> IFC [ 436186.16695688 5012265.69142313] (dist: 3.22m, conf: 0.785)\n", "Match 1006: <PERSON><PERSON> [ 435610.29576923 5011458.68384615] <-> IFC [ 435610.16695688 5011459.69142313] (dist: 1.02m, conf: 0.932)\n", "Match 1007: <PERSON><PERSON> [ 435704.01891667 5011401.02866667] <-> IFC [ 435704.16695688 5011397.69142313] (dist: 3.34m, conf: 0.777)\n", "Match 1008: <PERSON><PERSON> [ 435535.61541667 5011585.04375   ] <-> IFC [ 435534.16695688 5011581.69142313] (dist: 3.65m, conf: 0.757)\n", "Match 1009: <PERSON><PERSON> [ 436493.68644444 5011934.27644444] <-> IFC [ 436492.16695688 5011933.69142313] (dist: 1.63m, conf: 0.891)\n", "Match 1010: Dr<PERSON> [ 435995.81828571 5012108.00542857] <-> IFC [ 435998.16695688 5012101.69142313] (dist: 6.74m, conf: 0.551)\n", "Match 1011: <PERSON><PERSON> [ 435525.95480769 5011556.5075    ] <-> IFC [ 435524.16695688 5011559.69142313] (dist: 3.65m, conf: 0.757)\n", "Match 1012: <PERSON><PERSON> [ 436699.77097015 5011861.24323881] <-> IFC [ 436700.16695688 5011857.69142313] (dist: 3.57m, conf: 0.762)\n", "Match 1013: <PERSON><PERSON> [ 436156.4165     5011153.50145454] <-> IFC [ 436150.16695688 5011153.69142313] (dist: 6.25m, conf: 0.583)\n", "Match 1014: <PERSON><PERSON> [ 436313.5412 5011218.188 ] <-> IFC [ 436312.16695688 5011219.69142313] (dist: 2.04m, conf: 0.864)\n", "Match 1015: <PERSON><PERSON> [ 436614.67994444 5011763.38705556] <-> IFC [ 436616.16695688 5011761.69142313] (dist: 2.26m, conf: 0.850)\n", "Match 1016: <PERSON><PERSON> [ 436162.33175   5012325.1643125] <-> IFC [ 436158.16695688 5012323.69142313] (dist: 4.42m, conf: 0.705)\n", "Match 1017: <PERSON><PERSON> [ 435507.35111765 5011568.95505882] <-> IFC [ 435504.16695688 5011569.69142313] (dist: 3.27m, conf: 0.782)\n", "Match 1018: <PERSON><PERSON> [ 435983.153625 5012308.93725 ] <-> IFC [ 435978.16695688 5012307.69142313] (dist: 5.14m, conf: 0.657)\n", "Match 1019: <PERSON><PERSON> [ 435562.93421429 5011512.92914286] <-> IFC [ 435562.16695688 5011516.69142313] (dist: 3.84m, conf: 0.744)\n", "Match 1020: Dr<PERSON> [ 436049.38888889 5012247.24088889] <-> IFC [ 436054.16695688 5012249.69142313] (dist: 5.37m, conf: 0.642)\n", "Match 1021: <PERSON><PERSON> [ 435937.62283333 5012136.682     ] <-> IFC [ 435942.16695688 5012137.69142313] (dist: 4.65m, conf: 0.690)\n", "Match 1022: <PERSON><PERSON> [ 435442.0919 5011631.569 ] <-> IFC [ 435438.16695688 5011627.69142313] (dist: 5.52m, conf: 0.632)\n", "Match 1023: <PERSON><PERSON> [ 435693.70316667 5011986.94683333] <-> IFC [ 435694.16695688 5011987.69142313] (dist: 0.88m, conf: 0.942)\n", "Match 1024: <PERSON><PERSON> [ 435975.27535714 5011577.03592857] <-> IFC [ 435980.16695688 5011579.69142313] (dist: 5.57m, conf: 0.629)\n", "Match 1025: <PERSON><PERSON> [ 436256.063  5012411.5778] <-> IFC [ 436254.16695688 5012413.69142313] (dist: 2.84m, conf: 0.811)\n", "Match 1026: <PERSON><PERSON> [ 436264.38836842 5012356.40826316] <-> IFC [ 436262.16695688 5012359.69142313] (dist: 3.96m, conf: 0.736)\n", "Match 1027: <PERSON><PERSON> [ 436445.70557143 5012080.58671428] <-> IFC [ 436444.16695688 5012081.69142313] (dist: 1.89m, conf: 0.874)\n", "Match 1028: <PERSON><PERSON> [ 436242.52966667 5011645.64466667] <-> IFC [ 436240.16695688 5011645.69142313] (dist: 2.36m, conf: 0.842)\n", "Match 1029: Drone [ 435987.1426 5011683.4559] <-> IFC [ 435989.16695688 5011687.69142313] (dist: 4.69m, conf: 0.687)\n", "Match 1030: <PERSON><PERSON> [ 436144.23266667 5012171.03844444] <-> IFC [ 436148.16695688 5012169.69142313] (dist: 4.16m, conf: 0.723)\n", "Match 1031: <PERSON><PERSON> [ 436469.34322222 5011750.69544444] <-> IFC [ 436468.16695688 5011751.69142313] (dist: 1.54m, conf: 0.897)\n", "Match 1032: <PERSON><PERSON> [ 435777.921  5011275.7315] <-> IFC [ 435770.16695688 5011273.69142313] (dist: 8.02m, conf: 0.465)\n", "Match 1033: <PERSON><PERSON> [ 435337.76876923 5011792.85592308] <-> IFC [ 435334.16695688 5011789.69142313] (dist: 4.79m, conf: 0.680)\n", "Match 1034: <PERSON><PERSON> [ 435920.631 5012085.683] <-> IFC [ 435922.16695688 5012085.69142313] (dist: 1.54m, conf: 0.898)\n", "Match 1035: <PERSON><PERSON> [ 436549.40723077 5011843.66615385] <-> IFC [ 436548.16695688 5011843.69142313] (dist: 1.24m, conf: 0.917)\n", "Match 1036: <PERSON><PERSON> [ 436330.526 5012167.06 ] <-> IFC [ 436330.16695688 5012165.69142313] (dist: 1.41m, conf: 0.906)\n", "Match 1037: <PERSON><PERSON> [ 436424.497  5012102.7168] <-> IFC [ 436424.16695688 5012103.69142313] (dist: 1.03m, conf: 0.931)\n", "Match 1038: <PERSON><PERSON> [ 436239.54096429 5012006.56203571] <-> IFC [ 436236.16695688 5012005.69142313] (dist: 3.48m, conf: 0.768)\n", "Match 1039: <PERSON><PERSON> [ 436293.82195238 5012342.677     ] <-> IFC [ 436292.16695688 5012341.69142313] (dist: 1.93m, conf: 0.872)\n", "Match 1040: <PERSON><PERSON> [ 435487.5275     5011569.07664286] <-> IFC [ 435486.16695688 5011575.69142313] (dist: 6.75m, conf: 0.550)\n", "Match 1041: <PERSON><PERSON> [ 436105.66604762 5012357.76619048] <-> IFC [ 436102.16695688 5012359.69142313] (dist: 3.99m, conf: 0.734)\n", "Match 1042: <PERSON><PERSON> [ 435628.51385714 5012128.79180952] <-> IFC [ 435628.16695688 5012125.69142313] (dist: 3.12m, conf: 0.792)\n", "Match 1043: <PERSON><PERSON> [ 436226.74451852 5012345.36940741] <-> IFC [ 436224.16695688 5012341.69142313] (dist: 4.49m, conf: 0.701)\n", "Match 1044: <PERSON><PERSON> [ 436303.69209091 5012121.66881818] <-> IFC [ 436300.16695688 5012117.69142313] (dist: 5.31m, conf: 0.646)\n", "Match 1045: <PERSON><PERSON> [ 436292.8772     5012381.60586667] <-> IFC [ 436292.16695688 5012383.69142313] (dist: 2.20m, conf: 0.853)\n", "Match 1046: <PERSON><PERSON> [ 436155.25558333 5010962.24766667] <-> IFC [ 436160.16695688 5010961.69142313] (dist: 4.94m, conf: 0.670)\n", "Match 1047: <PERSON><PERSON> [ 436217.71791667 5012399.31375   ] <-> IFC [ 436216.16695688 5012395.69142313] (dist: 3.94m, conf: 0.737)\n", "Match 1048: <PERSON><PERSON> [ 436443.93307692 5012047.09838462] <-> IFC [ 436444.16695688 5012047.69142313] (dist: 0.64m, conf: 0.958)\n", "Match 1049: <PERSON><PERSON> [ 436259.47673684 5011256.16831579] <-> IFC [ 436255.16695688 5011255.69142313] (dist: 4.34m, conf: 0.711)\n", "Match 1050: <PERSON><PERSON> [ 436349.75869565 5012213.69626087] <-> IFC [ 436348.16695688 5012217.69142313] (dist: 4.30m, conf: 0.713)\n", "Match 1051: <PERSON><PERSON> [ 436152.1323 5012356.3954] <-> IFC [ 436148.16695688 5012353.69142313] (dist: 4.80m, conf: 0.680)\n", "Match 1052: <PERSON><PERSON> [ 436049.32377778 5012201.85355556] <-> IFC [ 436044.16695688 5012201.69142313] (dist: 5.16m, conf: 0.656)\n", "Match 1053: <PERSON><PERSON> [ 436208.39307407 5012314.8772037 ] <-> IFC [ 436206.16695688 5012317.69142313] (dist: 3.59m, conf: 0.761)\n", "Match 1054: <PERSON><PERSON> [ 435450.46203846 5011676.47903846] <-> IFC [ 435448.16695688 5011675.69142313] (dist: 2.43m, conf: 0.838)\n", "Match 1055: <PERSON><PERSON> [ 436643.15381818 5011875.65527273] <-> IFC [ 436644.16695688 5011877.69142313] (dist: 2.27m, conf: 0.848)\n", "Match 1056: <PERSON><PERSON> [ 436481.45659524 5012139.424     ] <-> IFC [ 436482.16695688 5012135.69142313] (dist: 3.80m, conf: 0.747)\n", "Match 1057: <PERSON><PERSON> [ 436500.7995 5012074.8619] <-> IFC [ 436500.16695688 5012077.69142313] (dist: 2.90m, conf: 0.807)\n", "Match 1058: <PERSON><PERSON> [ 436161.6625     5012330.86658333] <-> IFC [ 436158.16695688 5012333.69142313] (dist: 4.49m, conf: 0.700)\n", "Match 1059: <PERSON><PERSON> [ 436218.47084 5012353.28936] <-> IFC [ 436216.16695688 5012352.69142313] (dist: 2.38m, conf: 0.841)\n", "Match 1060: <PERSON><PERSON> [ 436387.59685714 5012186.76131428] <-> IFC [ 436386.16695688 5012187.69142313] (dist: 1.71m, conf: 0.886)\n", "Match 1061: <PERSON><PERSON> [ 436254.54525  5012350.726875] <-> IFC [ 436254.16695688 5012355.69142313] (dist: 4.98m, conf: 0.668)\n", "Match 1062: <PERSON><PERSON> [ 436300.01914706 5011627.4967353 ] <-> IFC [ 436296.16695688 5011625.69142313] (dist: 4.25m, conf: 0.716)\n", "Match 1063: <PERSON><PERSON> [ 436416.1553 5012067.2378] <-> IFC [ 436414.16695688 5012069.69142313] (dist: 3.16m, conf: 0.789)\n", "Match 1064: <PERSON><PERSON> [ 435610.222 5011494.423] <-> IFC [ 435609.16695688 5011500.69142313] (dist: 6.36m, conf: 0.576)\n", "Match 1065: <PERSON><PERSON> [ 435835.17208333 5012198.51491667] <-> IFC [ 435837.16695688 5012201.69142313] (dist: 3.75m, conf: 0.750)\n", "Match 1066: <PERSON><PERSON> [ 435580.919      5011657.25454167] <-> IFC [ 435580.16695688 5011653.69142313] (dist: 3.64m, conf: 0.757)\n", "Match 1067: <PERSON><PERSON> [ 435797.7508  5012097.32135] <-> IFC [ 435799.16695688 5012101.69142313] (dist: 4.59m, conf: 0.694)\n", "Match 1068: <PERSON><PERSON> [ 436171.54166667 5012426.99494444] <-> IFC [ 436168.16695688 5012423.69142313] (dist: 4.72m, conf: 0.685)\n", "Match 1069: <PERSON><PERSON> [ 436044.70533333 5011018.39233333] <-> IFC [ 436046.16695688 5011019.69142313] (dist: 1.96m, conf: 0.870)\n", "Match 1070: <PERSON><PERSON> [ 435534.6975     5011668.05483333] <-> IFC [ 435534.16695688 5011667.69142313] (dist: 0.64m, conf: 0.957)\n", "Match 1071: <PERSON><PERSON> [ 435629.5095     5011440.84577778] <-> IFC [ 435628.16695688 5011443.69142313] (dist: 3.15m, conf: 0.790)\n", "Match 1072: <PERSON><PERSON> [ 436472.44166667 5012034.10103921] <-> IFC [ 436472.16695688 5012037.69142313] (dist: 3.60m, conf: 0.760)\n", "Match 1073: <PERSON><PERSON> [ 436491.10742857 5012105.21342857] <-> IFC [ 436490.16695688 5012105.69142313] (dist: 1.05m, conf: 0.930)\n", "Match 1074: <PERSON><PERSON> [ 436134.56554545 5012416.43154545] <-> IFC [ 436130.16695688 5012417.69142313] (dist: 4.58m, conf: 0.695)\n", "Match 1075: <PERSON><PERSON> [ 436105.7664375 5012309.4880625] <-> IFC [ 436102.16695688 5012309.69142313] (dist: 3.61m, conf: 0.760)\n", "Match 1076: <PERSON><PERSON> [ 436448.3223 5011203.2981] <-> IFC [ 436446.16695688 5011197.69142313] (dist: 6.01m, conf: 0.600)\n", "Match 1077: <PERSON><PERSON> [ 435659.1104 5011370.652 ] <-> IFC [ 435656.16695688 5011365.69142313] (dist: 5.77m, conf: 0.615)\n", "Match 1078: <PERSON><PERSON> [ 436001.42294737 5012322.68736842] <-> IFC [ 436006.16695688 5012323.69142313] (dist: 4.85m, conf: 0.677)\n", "Match 1079: <PERSON><PERSON> [ 436170.85080645 5012367.09580645] <-> IFC [ 436168.16695688 5012363.69142313] (dist: 4.34m, conf: 0.711)\n", "Match 1080: <PERSON><PERSON> [ 436322.47775 5012293.92775] <-> IFC [ 436320.16695688 5012295.69142313] (dist: 2.91m, conf: 0.806)\n", "Match 1081: <PERSON><PERSON> [ 436133.92785714 5012236.87985714] <-> IFC [ 436130.16695688 5012233.69142313] (dist: 4.93m, conf: 0.671)\n", "Match 1082: <PERSON><PERSON> [ 436273.74671429 5012264.62971429] <-> IFC [ 436272.16695688 5012263.69142313] (dist: 1.84m, conf: 0.878)\n", "Match 1083: <PERSON><PERSON> [ 435779.7465625 5011416.5983125] <-> IFC [ 435780.16695688 5011419.69142313] (dist: 3.12m, conf: 0.792)\n", "Match 1084: <PERSON><PERSON> [ 436247.5945 5012217.075 ] <-> IFC [ 436244.16695688 5012212.69142313] (dist: 5.56m, conf: 0.629)\n", "Match 1085: <PERSON><PERSON> [ 436203.70845455 5011297.53872727] <-> IFC [ 436208.16695688 5011297.69142313] (dist: 4.46m, conf: 0.703)\n", "Match 1086: <PERSON><PERSON> [ 436190.18715625 5012313.85190625] <-> IFC [ 436186.16695688 5012315.69142313] (dist: 4.42m, conf: 0.705)\n", "Match 1087: <PERSON><PERSON> [ 436319.7128 5012279.3454] <-> IFC [ 436320.16695688 5012279.69142313] (dist: 0.57m, conf: 0.962)\n", "Match 1088: <PERSON><PERSON> [ 435383.075     5011989.2142381] <-> IFC [ 435382.16695688 5011993.69142313] (dist: 4.57m, conf: 0.695)\n", "Match 1089: <PERSON><PERSON> [ 436311.98266667 5012261.83233333] <-> IFC [ 436310.16695688 5012265.69142313] (dist: 4.26m, conf: 0.716)\n", "Match 1090: <PERSON><PERSON> [ 436152.64228125 5012202.574375  ] <-> IFC [ 436148.16695688 5012203.69142313] (dist: 4.61m, conf: 0.692)\n", "Match 1091: <PERSON><PERSON> [ 436002.2365 5012303.464 ] <-> IFC [ 436006.16695688 5012305.69142313] (dist: 4.52m, conf: 0.699)\n", "Match 1092: <PERSON><PERSON> [ 436246.6206  5012270.17512] <-> IFC [ 436244.16695688 5012271.69142313] (dist: 2.88m, conf: 0.808)\n", "Match 1093: <PERSON><PERSON> [ 436291.6571 5011598.1115] <-> IFC [ 436288.16695688 5011599.69142313] (dist: 3.83m, conf: 0.745)\n", "Match 1094: <PERSON><PERSON> [ 436435.5191 5012062.245 ] <-> IFC [ 436434.16695688 5012059.69142313] (dist: 2.89m, conf: 0.807)\n", "Match 1095: <PERSON><PERSON> [ 436188.96866667 5012364.98366667] <-> IFC [ 436186.16695688 5012365.69142313] (dist: 2.89m, conf: 0.807)\n", "Match 1096: <PERSON><PERSON> [ 436415.56465789 5012170.41997368] <-> IFC [ 436414.16695688 5012167.69142313] (dist: 3.07m, conf: 0.796)\n", "Match 1097: <PERSON><PERSON> [ 435705.02233333 5011433.427     ] <-> IFC [ 435704.16695688 5011431.69142313] (dist: 1.93m, conf: 0.871)\n", "Match 1098: <PERSON><PERSON> [ 436584.627625 5012005.68725 ] <-> IFC [ 436586.16695688 5012003.69142313] (dist: 2.52m, conf: 0.832)\n", "Match 1099: <PERSON><PERSON> [ 436434.76742 5012077.90098] <-> IFC [ 436434.16695688 5012077.69142313] (dist: 0.64m, conf: 0.958)\n", "Match 1100: <PERSON><PERSON> [ 436604.537625  5011922.8149375] <-> IFC [ 436606.16695688 5011923.69142313] (dist: 1.85m, conf: 0.877)\n", "Match 1101: <PERSON><PERSON> [ 436274.33664516 5012300.8922258 ] <-> IFC [ 436272.16695688 5012297.69142313] (dist: 3.87m, conf: 0.742)\n", "Match 1102: <PERSON><PERSON> [ 436247.18705882 5012248.36817647] <-> IFC [ 436244.16695688 5012245.69142313] (dist: 4.04m, conf: 0.731)\n", "Match 1103: <PERSON><PERSON> [ 435900.69575 5011597.391  ] <-> IFC [ 435904.16695688 5011599.69142313] (dist: 4.16m, conf: 0.722)\n", "Match 1104: <PERSON><PERSON> [ 435900.3015  5012133.65475] <-> IFC [ 435904.16695688 5012135.69142313] (dist: 4.37m, conf: 0.709)\n", "Match 1105: <PERSON><PERSON> [ 436218.18767901 5012234.5727037 ] <-> IFC [ 436216.16695688 5012229.69142313] (dist: 5.28m, conf: 0.648)\n", "Match 1106: <PERSON><PERSON> [ 436361.04816667 5011205.29266667] <-> IFC [ 436360.16695688 5011201.69142313] (dist: 3.71m, conf: 0.753)\n", "Match 1107: <PERSON><PERSON> [ 435535.68183333 5011674.42      ] <-> IFC [ 435534.16695688 5011667.69142313] (dist: 6.90m, conf: 0.540)\n", "Match 1108: <PERSON><PERSON> [ 436236.63528571 5012418.97109524] <-> IFC [ 436234.16695688 5012419.69142313] (dist: 2.57m, conf: 0.829)\n", "Match 1109: <PERSON><PERSON> [ 436623.9044 5011878.8527] <-> IFC [ 436624.16695688 5011875.69142313] (dist: 3.17m, conf: 0.789)\n", "Match 1110: <PERSON><PERSON> [ 436077.59494444 5012343.49622222] <-> IFC [ 436082.16695688 5012341.69142313] (dist: 4.92m, conf: 0.672)\n", "Match 1111: <PERSON><PERSON> [ 436193.4346 5010995.8885] <-> IFC [ 436188.16695688 5010997.69142313] (dist: 5.57m, conf: 0.629)\n", "Match 1112: <PERSON><PERSON> [ 436196.58866667 5011616.25613333] <-> IFC [ 436192.16695688 5011617.69142313] (dist: 4.65m, conf: 0.690)\n", "Match 1113: <PERSON><PERSON> [ 435995.31883333 5012030.00225   ] <-> IFC [ 435998.16695688 5012025.69142313] (dist: 5.17m, conf: 0.656)\n", "Match 1114: <PERSON><PERSON> [ 436368.83942857 5012147.381     ] <-> IFC [ 436368.16695688 5012143.69142313] (dist: 3.75m, conf: 0.750)\n", "Match 1115: <PERSON><PERSON> [ 436116.9417 5012101.9501] <-> IFC [ 436112.16695688 5012099.69142313] (dist: 5.28m, conf: 0.648)\n", "Match 1116: <PERSON><PERSON> [ 435581.60879167 5011478.299625  ] <-> IFC [ 435580.16695688 5011479.69142313] (dist: 2.00m, conf: 0.866)\n", "Match 1117: <PERSON><PERSON> [ 436249.5395625 5011193.5004375] <-> IFC [ 436246.16695688 5011191.69142313] (dist: 3.83m, conf: 0.745)\n", "Match 1118: <PERSON><PERSON> [ 436142.56173684 5012390.21552632] <-> IFC [ 436140.16695688 5012389.69142313] (dist: 2.45m, conf: 0.837)\n", "Match 1119: <PERSON><PERSON> [ 436226.78004 5012451.0544 ] <-> IFC [ 436224.16695688 5012443.69142313] (dist: 7.81m, conf: 0.479)\n", "Match 1120: <PERSON><PERSON> [ 436557.344      5012040.93833333] <-> IFC [ 436558.16695688 5012039.69142313] (dist: 1.49m, conf: 0.900)\n", "Match 1121: <PERSON><PERSON> [ 436405.84295652 5012230.87013043] <-> IFC [ 436406.16695688 5012229.69142313] (dist: 1.22m, conf: 0.919)\n", "Match 1122: <PERSON><PERSON> [ 436539.82731707 5011868.36665854] <-> IFC [ 436540.16695688 5011865.69142313] (dist: 2.70m, conf: 0.820)\n", "Match 1123: <PERSON><PERSON> [ 436577.47246154 5011872.76607692] <-> IFC [ 436578.16695688 5011875.69142313] (dist: 3.01m, conf: 0.800)\n", "Match 1124: <PERSON><PERSON> [ 436072.07468421 5011392.71305263] <-> IFC [ 436074.16695688 5011391.69142313] (dist: 2.33m, conf: 0.845)\n", "Match 1125: <PERSON><PERSON> [ 435439.52427273 5011774.86454546] <-> IFC [ 435438.16695688 5011771.69142313] (dist: 3.45m, conf: 0.770)\n", "Match 1126: <PERSON><PERSON> [ 436653.41381818 5011797.81363636] <-> IFC [ 436654.16695688 5011797.69142313] (dist: 0.76m, conf: 0.949)\n", "Match 1127: <PERSON><PERSON> [ 436653.2905     5011871.49728571] <-> IFC [ 436654.16695688 5011873.69142313] (dist: 2.36m, conf: 0.842)\n", "Match 1128: <PERSON><PERSON> [ 436086.51817241 5012259.46475862] <-> IFC [ 436082.16695688 5012255.69142313] (dist: 5.76m, conf: 0.616)\n", "Match 1129: <PERSON><PERSON> [ 436350.44888095 5012146.40652381] <-> IFC [ 436348.16695688 5012141.69142313] (dist: 5.24m, conf: 0.651)\n", "Match 1130: <PERSON><PERSON> [ 436226.82585714 5012457.77328571] <-> IFC [ 436224.16695688 5012458.69142313] (dist: 2.81m, conf: 0.812)\n", "Match 1131: <PERSON><PERSON> [ 436538.515125   5012039.80708333] <-> IFC [ 436538.16695688 5012037.69142313] (dist: 2.14m, conf: 0.857)\n", "Match 1132: <PERSON><PERSON> [ 435985.75686667 5012045.79093333] <-> IFC [ 435980.16695688 5012045.69142313] (dist: 5.59m, conf: 0.627)\n", "Match 1133: <PERSON><PERSON> [ 436265.67525926 5012190.55862963] <-> IFC [ 436262.16695688 5012191.69142313] (dist: 3.69m, conf: 0.754)\n", "Match 1134: <PERSON><PERSON> [ 436331.15376 5012175.20052] <-> IFC [ 436330.16695688 5012175.69142313] (dist: 1.10m, conf: 0.927)\n", "Match 1135: <PERSON><PERSON> [ 435919.05225 5011533.27925] <-> IFC [ 435922.16695688 5011535.69142313] (dist: 3.94m, conf: 0.737)\n", "Match 1136: <PERSON><PERSON> [ 436614.95707143 5011775.7225    ] <-> IFC [ 436616.16695688 5011769.69142313] (dist: 6.15m, conf: 0.590)\n", "Match 1137: <PERSON><PERSON> [ 436031.13477419 5012217.338     ] <-> IFC [ 436026.16695688 5012215.69142313] (dist: 5.23m, conf: 0.651)\n", "Match 1138: <PERSON><PERSON> [ 436162.90863636 5012399.88209091] <-> IFC [ 436158.16695688 5012400.69142313] (dist: 4.81m, conf: 0.679)\n", "Match 1139: <PERSON><PERSON> [ 436565.58166667 5012017.0045    ] <-> IFC [ 436566.16695688 5012017.69142313] (dist: 0.90m, conf: 0.940)\n", "Match 1140: <PERSON><PERSON> [ 436068.05914634 5012339.13580488] <-> IFC [ 436064.16695688 5012337.69142313] (dist: 4.15m, conf: 0.723)\n", "Match 1141: <PERSON><PERSON> [ 435966.59657143 5012087.40085714] <-> IFC [ 435960.16695688 5012087.69142313] (dist: 6.44m, conf: 0.571)\n", "Match 1142: <PERSON><PERSON> [ 436193.53804167 5010985.664125  ] <-> IFC [ 436198.16695688 5010981.69142313] (dist: 6.10m, conf: 0.593)\n", "Match 1143: <PERSON><PERSON> [ 435413.2027037  5011656.11518518] <-> IFC [ 435410.16695688 5011659.69142313] (dist: 4.69m, conf: 0.687)\n", "Match 1144: <PERSON><PERSON> [ 436291.22577778 5011580.10366667] <-> IFC [ 436288.16695688 5011581.69142313] (dist: 3.45m, conf: 0.770)\n", "Match 1145: <PERSON><PERSON> [ 436042.98078824 5011062.6591647 ] <-> IFC [ 436046.16695688 5011061.69142313] (dist: 3.33m, conf: 0.778)\n", "Match 1146: <PERSON><PERSON> [ 435899.20853333 5012260.7984    ] <-> IFC [ 435902.16695688 5012263.69142313] (dist: 4.14m, conf: 0.724)\n", "Match 1147: <PERSON><PERSON> [ 436098.046 5012061.227] <-> IFC [ 436094.16695688 5012059.69142313] (dist: 4.17m, conf: 0.722)\n", "Match 1148: <PERSON><PERSON> [ 435919.86903571 5012170.77921428] <-> IFC [ 435922.16695688 5012169.69142313] (dist: 2.54m, conf: 0.831)\n", "Match 1149: Dr<PERSON> [ 436322.06082353 5012179.43105882] <-> IFC [ 436320.16695688 5012177.69142313] (dist: 2.57m, conf: 0.829)\n", "Match 1150: <PERSON><PERSON> [ 436643.4913  5011804.39535] <-> IFC [ 436644.16695688 5011801.69142313] (dist: 2.79m, conf: 0.814)\n", "Match 1151: <PERSON><PERSON> [ 435855.11173333 5011449.839     ] <-> IFC [ 435856.16695688 5011451.69142313] (dist: 2.13m, conf: 0.858)\n", "Match 1152: <PERSON><PERSON> [ 436463.64866667 5012041.48345833] <-> IFC [ 436462.16695688 5012041.69142313] (dist: 1.50m, conf: 0.900)\n", "Match 1153: <PERSON><PERSON> [ 435798.86518182 5011372.56181818] <-> IFC [ 435799.16695688 5011365.69142314] (dist: 6.88m, conf: 0.542)\n", "Match 1154: <PERSON><PERSON> [ 435864.9434 5012036.2116] <-> IFC [ 435866.16695688 5012039.69142313] (dist: 3.69m, conf: 0.754)\n", "Match 1155: <PERSON><PERSON> [ 435571.4942     5011522.93413333] <-> IFC [ 435571.16695688 5011521.69142313] (dist: 1.29m, conf: 0.914)\n", "Match 1156: <PERSON><PERSON> [ 436256.55055556 5012215.26483333] <-> IFC [ 436254.16695688 5012211.69142313] (dist: 4.30m, conf: 0.714)\n", "Match 1157: <PERSON><PERSON> [ 435967.8002     5011994.16406667] <-> IFC [ 435970.16695688 5011995.69142313] (dist: 2.82m, conf: 0.812)\n", "Match 1158: <PERSON><PERSON> [ 436616.8758 5011866.874 ] <-> IFC [ 436616.16695688 5011871.69142313] (dist: 4.87m, conf: 0.675)\n", "Match 1159: <PERSON><PERSON> [ 436227.55333333 5012148.50183333] <-> IFC [ 436224.16695688 5012145.69142313] (dist: 4.40m, conf: 0.707)\n", "Match 1160: <PERSON><PERSON> [ 436029.67457576 5012281.65357576] <-> IFC [ 436026.16695688 5012283.69142313] (dist: 4.06m, conf: 0.730)\n", "Match 1161: <PERSON><PERSON> [ 435938.63966667 5012128.91688889] <-> IFC [ 435942.16695688 5012129.69142313] (dist: 3.61m, conf: 0.759)\n", "Match 1162: <PERSON><PERSON> [ 436264.57426667 5012416.0306    ] <-> IFC [ 436262.16695688 5012419.69142313] (dist: 4.38m, conf: 0.708)\n", "Match 1163: <PERSON><PERSON> [ 436209.51036364 5012204.74227273] <-> IFC [ 436206.16695688 5012205.69142313] (dist: 3.48m, conf: 0.768)\n", "Match 1164: <PERSON><PERSON> [ 436087.66081818 5012202.58572727] <-> IFC [ 436082.16695688 5012205.69142313] (dist: 6.31m, conf: 0.579)\n", "Match 1165: <PERSON><PERSON> [ 436032.9841 5011061.3049] <-> IFC [ 436036.16695688 5011057.69142313] (dist: 4.82m, conf: 0.679)\n", "Match 1166: <PERSON><PERSON> [ 436098.24941667 5012087.99175   ] <-> IFC [ 436094.16695688 5012089.69142313] (dist: 4.42m, conf: 0.705)\n", "Match 1167: <PERSON><PERSON> [ 436068.17       5012208.17368182] <-> IFC [ 436072.16695688 5012207.69142313] (dist: 4.03m, conf: 0.732)\n", "Match 1168: <PERSON><PERSON> [ 436490.82576471 5012095.87670588] <-> IFC [ 436490.16695688 5012097.69142313] (dist: 1.93m, conf: 0.871)\n", "Match 1169: <PERSON><PERSON> [ 436227.07919355 5012357.64367742] <-> IFC [ 436224.16695688 5012358.69142313] (dist: 3.09m, conf: 0.794)\n", "Match 1170: <PERSON><PERSON> [ 436349.99479167 5012309.68870833] <-> IFC [ 436348.16695688 5012311.69142313] (dist: 2.71m, conf: 0.819)\n", "Match 1171: <PERSON><PERSON> [ 436137.3764186  5010956.14504651] <-> IFC [ 436140.16695688 5010957.69142313] (dist: 3.19m, conf: 0.787)\n", "Match 1172: <PERSON><PERSON> [ 436258.4154 5012019.7106] <-> IFC [ 436264.16695688 5012019.69142313] (dist: 5.75m, conf: 0.617)\n", "Match 1173: <PERSON><PERSON> [ 436171.66230769 5012313.29046154] <-> IFC [ 436168.16695688 5012313.69142313] (dist: 3.52m, conf: 0.765)\n", "Match 1174: <PERSON><PERSON> [ 436323.26322222 5011952.25466667] <-> IFC [ 436322.16695688 5011950.69142313] (dist: 1.91m, conf: 0.873)\n", "Match 1175: <PERSON><PERSON> [ 436276.643125 5011122.54775 ] <-> IFC [ 436274.16695688 5011121.69142313] (dist: 2.62m, conf: 0.825)\n", "Match 1176: <PERSON><PERSON> [ 436596.37711111 5011807.05705556] <-> IFC [ 436596.16695688 5011809.69142313] (dist: 2.64m, conf: 0.824)\n", "Match 1177: <PERSON><PERSON> [ 436387.60523913 5012200.73028261] <-> IFC [ 436386.16695688 5012203.69142313] (dist: 3.29m, conf: 0.781)\n", "Match 1178: <PERSON><PERSON> [ 435608.59     5011656.019625] <-> IFC [ 435600.16695688 5011657.69142313] (dist: 8.59m, conf: 0.428)\n", "Match 1179: <PERSON><PERSON> [ 435460.3664 5011615.0838] <-> IFC [ 435458.16695688 5011619.69142313] (dist: 5.11m, conf: 0.660)\n", "Match 1180: <PERSON><PERSON> [ 435741.15671429 5011896.0492381 ] <-> IFC [ 435742.16695688 5011897.69142313] (dist: 1.93m, conf: 0.871)\n", "Match 1181: <PERSON><PERSON> [ 435947.29645455 5012024.51036364] <-> IFC [ 435951.39772611 5012026.4606539 ] (dist: 4.54m, conf: 0.697)\n", "Match 1182: <PERSON><PERSON> [ 436361.915      5011149.42433333] <-> IFC [ 436360.16695688 5011151.69142313] (dist: 2.86m, conf: 0.809)\n", "Match 1183: <PERSON><PERSON> [ 436302.12977778 5012304.57544444] <-> IFC [ 436300.16695688 5012303.69142313] (dist: 2.15m, conf: 0.856)\n", "Match 1184: <PERSON><PERSON> [ 436198.8225 5012421.7821] <-> IFC [ 436196.16695688 5012421.69142313] (dist: 2.66m, conf: 0.823)\n", "Match 1185: <PERSON><PERSON> [ 436566.241125 5012008.576125] <-> IFC [ 436566.16695688 5012009.69142313] (dist: 1.12m, conf: 0.925)\n", "Match 1186: <PERSON><PERSON> [ 436208.8938 5012366.5526] <-> IFC [ 436206.16695688 5012367.69142313] (dist: 2.96m, conf: 0.803)\n", "Match 1187: <PERSON><PERSON> [ 436285.95936364 5012026.64159091] <-> IFC [ 436284.16695688 5012019.69142313] (dist: 7.18m, conf: 0.521)\n", "Match 1188: <PERSON><PERSON> [ 436088.69363636 5012061.35740909] <-> IFC [ 436084.16695688 5012059.69142313] (dist: 4.82m, conf: 0.678)\n", "Match 1189: <PERSON><PERSON> [ 436286.2174 5011177.6326] <-> IFC [ 436284.16695688 5011179.69142313] (dist: 2.91m, conf: 0.806)\n", "Match 1190: <PERSON><PERSON> [ 435919.339375 5012181.07025 ] <-> IFC [ 435922.16695688 5012177.69142313] (dist: 4.41m, conf: 0.706)\n", "Match 1191: <PERSON><PERSON> [ 436218.1379 5012393.4269] <-> IFC [ 436216.16695688 5012395.69142313] (dist: 3.00m, conf: 0.800)\n", "Match 1192: <PERSON><PERSON> [ 435854.092125  5011443.7828125] <-> IFC [ 435856.16695688 5011443.69142313] (dist: 2.08m, conf: 0.862)\n", "Match 1193: <PERSON><PERSON> [ 435807.04395652 5012105.82726087] <-> IFC [ 435808.16695688 5012103.69142313] (dist: 2.41m, conf: 0.839)\n", "Match 1194: <PERSON><PERSON> [ 436690.91666667 5011861.59838095] <-> IFC [ 436692.16695688 5011861.69142313] (dist: 1.25m, conf: 0.916)\n", "Match 1195: <PERSON><PERSON> [ 436680.95725  5011861.563625] <-> IFC [ 436682.16695688 5011865.69142313] (dist: 4.30m, conf: 0.713)\n", "Match 1196: <PERSON><PERSON> [ 436191.51714286 5012266.49585714] <-> IFC [ 436196.16695688 5012265.69142313] (dist: 4.72m, conf: 0.685)\n", "Match 1197: <PERSON><PERSON> [ 436510.05717391 5012049.34460869] <-> IFC [ 436510.16695688 5012047.69142313] (dist: 1.66m, conf: 0.890)\n", "Match 1198: <PERSON><PERSON> [ 436520.1302 5012016.9756] <-> IFC [ 436520.16695688 5012019.69142313] (dist: 2.72m, conf: 0.819)\n", "Match 1199: <PERSON><PERSON> [ 436352.91977778 5011210.74527778] <-> IFC [ 436350.16695688 5011207.69142313] (dist: 4.11m, conf: 0.726)\n", "Match 1200: <PERSON><PERSON> [ 435543.54286667 5011590.73986667] <-> IFC [ 435542.16695688 5011593.69142313] (dist: 3.26m, conf: 0.783)\n", "Match 1201: <PERSON><PERSON> [ 435318.934625 5011787.6405  ] <-> IFC [ 435314.16695688 5011787.69142313] (dist: 4.77m, conf: 0.682)\n", "Match 1202: <PERSON><PERSON> [ 436444.049      5012189.60146429] <-> IFC [ 436444.16695688 5012191.69142313] (dist: 2.09m, conf: 0.860)\n", "Match 1203: <PERSON><PERSON> [ 435534.70965 5011529.4838 ] <-> IFC [ 435533.16695688 5011531.69142313] (dist: 2.69m, conf: 0.820)\n", "Match 1204: <PERSON><PERSON> [ 435261.61183333 5011922.11966667] <-> IFC [ 435268.16695688 5011921.69142313] (dist: 6.57m, conf: 0.562)\n", "Match 1205: <PERSON><PERSON> [ 435872.62177273 5012192.27754545] <-> IFC [ 435876.16695688 5012195.69142313] (dist: 4.92m, conf: 0.672)\n", "Match 1206: <PERSON><PERSON> [ 435893.5782 5011618.1102] <-> IFC [ 435894.16695688 5011623.69142313] (dist: 5.61m, conf: 0.626)\n", "Match 1207: <PERSON><PERSON> [ 436227.01181818 5012312.837     ] <-> IFC [ 436224.16695688 5012315.69142313] (dist: 4.03m, conf: 0.731)\n", "Match 1208: <PERSON><PERSON> [ 435564.6082 5011490.365 ] <-> IFC [ 435562.16695688 5011491.69142313] (dist: 2.78m, conf: 0.815)\n", "Match 1209: <PERSON><PERSON> [ 436595.7955  5011867.00755] <-> IFC [ 436596.16695688 5011869.69142313] (dist: 2.71m, conf: 0.819)\n", "Match 1210: <PERSON><PERSON> [ 435601.47647059 5011504.62476471] <-> IFC [ 435600.16695688 5011509.69142313] (dist: 5.23m, conf: 0.651)\n", "Match 1211: <PERSON><PERSON> [ 435827.1805     5012155.98833333] <-> IFC [ 435828.16695688 5012155.69142313] (dist: 1.03m, conf: 0.931)\n", "Match 1212: <PERSON><PERSON> [ 435789.061225 5011367.934975] <-> IFC [ 435790.16695688 5011371.69142313] (dist: 3.92m, conf: 0.739)\n", "Match 1213: <PERSON><PERSON> [ 435919.98757143 5012101.245     ] <-> IFC [ 435922.16695688 5012101.69142313] (dist: 2.22m, conf: 0.852)\n", "Match 1214: <PERSON><PERSON> [ 435986.67366667 5011698.34346667] <-> IFC [ 435990.16695688 5011699.69142313] (dist: 3.74m, conf: 0.750)\n", "Match 1215: <PERSON><PERSON> [ 436434.45855 5012155.6674 ] <-> IFC [ 436434.16695688 5012151.69142313] (dist: 3.99m, conf: 0.734)\n", "Match 1216: <PERSON><PERSON> [ 435966.71833333 5012108.38755555] <-> IFC [ 435970.16695688 5012109.69142313] (dist: 3.69m, conf: 0.754)\n", "Match 1217: <PERSON><PERSON> [ 436473.01880435 5012118.84736956] <-> IFC [ 436472.16695688 5012121.69142313] (dist: 2.97m, conf: 0.802)\n", "Match 1218: <PERSON><PERSON> [ 435778.57075    5011982.41716667] <-> IFC [ 435780.16695688 5011979.69142313] (dist: 3.16m, conf: 0.789)\n", "Match 1219: <PERSON><PERSON> [ 436283.35034694 5012370.05073469] <-> IFC [ 436282.16695688 5012369.69142313] (dist: 1.24m, conf: 0.918)\n", "Match 1220: <PERSON><PERSON> [ 436165.26669565 5010951.35873913] <-> IFC [ 436160.16695688 5010953.69142313] (dist: 5.61m, conf: 0.626)\n", "Match 1221: <PERSON><PERSON> [ 436190.00511111 5012211.38544444] <-> IFC [ 436186.16695688 5012215.69142313] (dist: 5.77m, conf: 0.615)\n", "Match 1222: <PERSON><PERSON> [ 436284.38435714 5012219.53957143] <-> IFC [ 436282.16695688 5012217.69142313] (dist: 2.89m, conf: 0.808)\n", "Match 1223: <PERSON><PERSON> [ 436058.45725  5012362.017375] <-> IFC [ 436064.16695688 5012363.69142313] (dist: 5.95m, conf: 0.603)\n", "Match 1224: <PERSON><PERSON> [ 436020.67731579 5012316.47742105] <-> IFC [ 436016.16695688 5012319.69142313] (dist: 5.54m, conf: 0.631)\n", "Match 1225: <PERSON><PERSON> [ 436314.88966667 5011209.5896    ] <-> IFC [ 436312.16695688 5011211.69142313] (dist: 3.44m, conf: 0.771)\n", "Match 1226: <PERSON><PERSON> [ 436034.23214286 5011055.08271428] <-> IFC [ 436036.16695688 5011057.69142313] (dist: 3.25m, conf: 0.783)\n", "Match 1227: <PERSON><PERSON> [ 436163.0165 5012308.159 ] <-> IFC [ 436158.16695688 5012307.69142313] (dist: 4.87m, conf: 0.675)\n", "Match 1228: <PERSON><PERSON> [ 436049.61455263 5012258.80934211] <-> IFC [ 436054.16695688 5012257.69142313] (dist: 4.69m, conf: 0.687)\n", "Match 1229: <PERSON><PERSON> [ 436077.56392 5012212.66652] <-> IFC [ 436082.16695688 5012213.69142313] (dist: 4.72m, conf: 0.686)\n", "Match 1230: <PERSON><PERSON> [ 436521.25453846 5011865.24246154] <-> IFC [ 436520.16695688 5011863.69142313] (dist: 1.89m, conf: 0.874)\n", "Match 1231: <PERSON><PERSON> [ 435768.153  5011853.3686] <-> IFC [ 435770.16695688 5011845.69142313] (dist: 7.94m, conf: 0.471)\n", "Match 1232: <PERSON><PERSON> [ 435789.18684211 5011267.76931579] <-> IFC [ 435790.16695688 5011273.69142313] (dist: 6.00m, conf: 0.600)\n", "Match 1233: <PERSON><PERSON> [ 436586.81525 5011902.5465 ] <-> IFC [ 436586.16695688 5011897.69142313] (dist: 4.90m, conf: 0.673)\n", "Match 1234: <PERSON><PERSON> [ 436071.54771429 5012074.24757143] <-> IFC [ 436074.16695688 5012071.69142313] (dist: 3.66m, conf: 0.756)\n", "Match 1235: <PERSON><PERSON> [ 436462.3626  5012150.93716] <-> IFC [ 436462.16695688 5012149.69142313] (dist: 1.26m, conf: 0.916)\n", "Match 1236: <PERSON><PERSON> [ 436089.4984     5011764.86366667] <-> IFC [ 436094.16695688 5011763.69142313] (dist: 4.81m, conf: 0.679)\n", "Match 1237: <PERSON><PERSON> [ 436356.00035714 5011657.62007143] <-> IFC [ 436354.16695688 5011655.69142313] (dist: 2.66m, conf: 0.823)\n", "Match 1238: <PERSON><PERSON> [ 435789.33   5011971.9844] <-> IFC [ 435790.16695688 5011971.69142313] (dist: 0.89m, conf: 0.941)\n", "Match 1239: <PERSON><PERSON> [ 435327.18604167 5011956.37670833] <-> IFC [ 435324.16695688 5011953.69142313] (dist: 4.04m, conf: 0.731)\n", "Match 1240: <PERSON><PERSON> [ 435872.38275 5012079.59725] <-> IFC [ 435875.16695688 5012077.69142313] (dist: 3.37m, conf: 0.775)\n", "Match 1241: <PERSON><PERSON> [ 436419.6442 5011222.4733] <-> IFC [ 436416.16695688 5011215.69142313] (dist: 7.62m, conf: 0.492)\n", "Match 1242: <PERSON><PERSON> [ 436312.52466667 5012358.77716667] <-> IFC [ 436310.16695688 5012359.69142313] (dist: 2.53m, conf: 0.831)\n", "Match 1243: <PERSON><PERSON> [ 436031.253      5012262.03966667] <-> IFC [ 436034.16695688 5012263.69142313] (dist: 3.35m, conf: 0.777)\n", "Match 1244: <PERSON><PERSON> [ 436508.888875 5012035.069625] <-> IFC [ 436510.16695688 5012031.69142313] (dist: 3.61m, conf: 0.759)\n", "Match 1245: <PERSON><PERSON> [ 435459.56078571 5011625.89489286] <-> IFC [ 435458.16695688 5011629.69142313] (dist: 4.04m, conf: 0.730)\n", "Match 1246: <PERSON><PERSON> [ 436465.33113636 5011941.28781818] <-> IFC [ 436464.16695688 5011943.69142313] (dist: 2.67m, conf: 0.822)\n", "Match 1247: <PERSON><PERSON> [ 435337.68916667 5011744.55416667] <-> IFC [ 435334.16695688 5011741.69142313] (dist: 4.54m, conf: 0.697)\n", "Match 1248: <PERSON><PERSON> [ 436276.852625 5011128.137125] <-> IFC [ 436274.16695688 5011129.69142313] (dist: 3.10m, conf: 0.793)\n", "Match 1249: <PERSON><PERSON> [ 436492.11893333 5012046.5986    ] <-> IFC [ 436490.16695688 5012047.69142313] (dist: 2.24m, conf: 0.851)\n", "Match 1250: <PERSON><PERSON> [ 435845.26346 5011372.14576] <-> IFC [ 435846.16695688 5011367.69142313] (dist: 4.55m, conf: 0.697)\n", "Match 1251: <PERSON><PERSON> [ 435907.110625 5012257.283125] <-> IFC [ 435902.16695688 5012255.69142313] (dist: 5.19m, conf: 0.654)\n", "Match 1252: <PERSON><PERSON> [ 435714.30678571 5011421.35464286] <-> IFC [ 435714.16695688 5011421.69142313] (dist: 0.36m, conf: 0.976)\n", "Match 1253: <PERSON><PERSON> [ 436208.77485714 5012298.45095238] <-> IFC [ 436206.16695688 5012299.69142313] (dist: 2.89m, conf: 0.807)\n", "Match 1254: <PERSON><PERSON> [ 436417.459 5011148.149] <-> IFC [ 436416.16695688 5011147.69142313] (dist: 1.37m, conf: 0.909)\n", "Match 1255: <PERSON><PERSON> [ 436312.46671429 5012123.63042857] <-> IFC [ 436310.16695688 5012122.69142313] (dist: 2.48m, conf: 0.834)\n", "Match 1256: <PERSON><PERSON> [ 436681.40496429 5011848.84560714] <-> IFC [ 436682.16695688 5011847.69142313] (dist: 1.38m, conf: 0.908)\n", "Match 1257: <PERSON><PERSON> [ 436001.60211111 5012336.8682963 ] <-> IFC [ 436006.16695688 5012339.69142313] (dist: 5.37m, conf: 0.642)\n", "Match 1258: <PERSON><PERSON> [ 436634.59409091 5011771.25595454] <-> IFC [ 436634.16695688 5011771.69142313] (dist: 0.61m, conf: 0.959)\n", "Match 1259: <PERSON><PERSON> [ 436162.36307692 5012160.655     ] <-> IFC [ 436158.16695688 5012160.69142313] (dist: 4.20m, conf: 0.720)\n", "Match 1260: <PERSON><PERSON> [ 436268.33074194 5011184.06406452] <-> IFC [ 436264.16695688 5011181.69142313] (dist: 4.79m, conf: 0.681)\n", "Match 1261: <PERSON><PERSON> [ 436058.40845652 5012349.31123913] <-> IFC [ 436054.16695688 5012349.69142313] (dist: 4.26m, conf: 0.716)\n", "Match 1262: <PERSON><PERSON> [ 436022.55308571 5012040.92854286] <-> IFC [ 436018.16695688 5012041.69142313] (dist: 4.45m, conf: 0.703)\n", "Match 1263: <PERSON><PERSON> [ 436033.12666667 5011067.00291667] <-> IFC [ 436036.16695688 5011065.69142313] (dist: 3.31m, conf: 0.779)\n", "Match 1264: <PERSON><PERSON> [ 435384.72640909 5011811.23254545] <-> IFC [ 435382.16695688 5011815.69142313] (dist: 5.14m, conf: 0.657)\n", "Match 1265: <PERSON><PERSON> [ 436290.90884848 5011626.05590909] <-> IFC [ 436288.16695688 5011625.69142313] (dist: 2.77m, conf: 0.816)\n", "Match 1266: <PERSON><PERSON> [ 436218.04416667 5012258.18133333] <-> IFC [ 436216.16695688 5012255.69142313] (dist: 3.12m, conf: 0.792)\n", "Match 1267: <PERSON><PERSON> [ 436671.191  5011885.4388] <-> IFC [ 436672.16695688 5011883.69142313] (dist: 2.00m, conf: 0.867)\n", "Match 1268: <PERSON><PERSON> [ 436391.10176 5011135.4938 ] <-> IFC [ 436388.16695688 5011137.69142313] (dist: 3.67m, conf: 0.756)\n", "Match 1269: <PERSON><PERSON> [ 436244.97044444 5012351.60488889] <-> IFC [ 436244.16695688 5012349.69142313] (dist: 2.08m, conf: 0.862)\n", "Match 1270: <PERSON><PERSON> [ 436076.45957143 5012355.2775    ] <-> IFC [ 436072.16695688 5012351.69142313] (dist: 5.59m, conf: 0.627)\n", "Match 1271: <PERSON><PERSON> [ 436294.30233333 5012368.64833333] <-> IFC [ 436292.16695688 5012365.69142313] (dist: 3.65m, conf: 0.757)\n", "Match 1272: <PERSON><PERSON> [ 436483.574125 5011746.719375] <-> IFC [ 436478.16695688 5011753.69142313] (dist: 8.82m, conf: 0.412)\n", "Match 1273: <PERSON><PERSON> [ 435967.7438125 5012017.916125 ] <-> IFC [ 435970.16695688 5012021.69142313] (dist: 4.49m, conf: 0.701)\n", "Match 1274: <PERSON><PERSON> [ 436502.13351613 5011949.16554839] <-> IFC [ 436502.16695688 5011947.69142313] (dist: 1.47m, conf: 0.902)\n", "Match 1275: <PERSON><PERSON> [ 436058.94038462 5012300.32284615] <-> IFC [ 436054.16695688 5012299.69142313] (dist: 4.82m, conf: 0.679)\n", "Match 1276: <PERSON><PERSON> [ 436203.63416667 5010994.923     ] <-> IFC [ 436198.16695688 5010989.69142313] (dist: 7.57m, conf: 0.496)\n", "Match 1277: <PERSON><PERSON> [ 435973.3684 5012325.5387] <-> IFC [ 435978.16695688 5012323.69142313] (dist: 5.14m, conf: 0.657)\n", "Match 1278: <PERSON><PERSON> [ 435639.1034 5011503.0484] <-> IFC [ 435638.16695688 5011498.69142313] (dist: 4.46m, conf: 0.703)\n", "Match 1279: <PERSON><PERSON> [ 436189.6178125 5012387.305    ] <-> IFC [ 436186.16695688 5012383.69142313] (dist: 5.00m, conf: 0.667)\n", "Match 1280: Dr<PERSON> [ 436681.02276471 5011886.24029412] <-> IFC [ 436682.16695688 5011889.69142313] (dist: 3.64m, conf: 0.758)\n", "Match 1281: <PERSON><PERSON> [ 436465.83010345 5011197.27127586] <-> IFC [ 436464.16695688 5011191.69142313] (dist: 5.82m, conf: 0.612)\n", "Match 1282: <PERSON><PERSON> [ 435779.53811111 5011469.47341667] <-> IFC [ 435770.16695688 5011472.69142313] (dist: 9.91m, conf: 0.339)\n", "Match 1283: <PERSON><PERSON> [ 436126.16903333 5012083.13076667] <-> IFC [ 436122.16695688 5012089.69142313] (dist: 7.68m, conf: 0.488)\n", "Match 1284: <PERSON><PERSON> [ 436548.96472 5011945.13596] <-> IFC [ 436548.16695688 5011945.69142313] (dist: 0.97m, conf: 0.935)\n", "Match 1285: <PERSON><PERSON> [ 436353.253375  5011105.6223125] <-> IFC [ 436350.16695688 5011105.69142313] (dist: 3.09m, conf: 0.794)\n", "Match 1286: <PERSON><PERSON> [ 436198.57744444 5012238.75844444] <-> IFC [ 436196.16695688 5012239.69142313] (dist: 2.58m, conf: 0.828)\n", "Match 1287: <PERSON><PERSON> [ 436434.83881818 5012068.22990909] <-> IFC [ 436434.16695688 5012067.69142313] (dist: 0.86m, conf: 0.943)\n", "Match 1288: <PERSON><PERSON> [ 436577.32125926 5011811.45974074] <-> IFC [ 436578.16695688 5011809.69142313] (dist: 1.96m, conf: 0.869)\n", "Match 1289: <PERSON><PERSON> [ 435863.5264 5011548.7168] <-> IFC [ 435866.16695688 5011551.69142313] (dist: 3.98m, conf: 0.735)\n", "Match 1290: <PERSON><PERSON> [ 436067.67111111 5012261.53666667] <-> IFC [ 436064.16695688 5012261.69142313] (dist: 3.51m, conf: 0.766)\n", "Match 1291: <PERSON><PERSON> [ 436672.34766667 5011893.83793333] <-> IFC [ 436672.16695688 5011891.69142313] (dist: 2.15m, conf: 0.856)\n", "Match 1292: <PERSON><PERSON> [ 436633.3398125 5011842.898    ] <-> IFC [ 436634.16695688 5011846.69142313] (dist: 3.88m, conf: 0.741)\n", "Match 1293: <PERSON><PERSON> [ 436188.72147059 5012301.76158823] <-> IFC [ 436186.16695688 5012297.69142313] (dist: 4.81m, conf: 0.680)\n", "Match 1294: <PERSON><PERSON> [ 436245.66528571 5012145.49590476] <-> IFC [ 436244.16695688 5012143.69142313] (dist: 2.35m, conf: 0.844)\n", "Match 1295: <PERSON><PERSON> [ 435712.3019697  5012001.07839394] <-> IFC [ 435714.16695688 5012001.69142313] (dist: 1.96m, conf: 0.869)\n", "Match 1296: <PERSON><PERSON> [ 436302.47234615 5012096.76511538] <-> IFC [ 436300.16695688 5012100.69142313] (dist: 4.55m, conf: 0.696)\n", "Match 1297: <PERSON><PERSON> [ 436199.28316667 5012379.39183333] <-> IFC [ 436196.16695688 5012379.69142313] (dist: 3.13m, conf: 0.791)\n", "Match 1298: <PERSON><PERSON> [ 435807.80013636 5011475.21677273] <-> IFC [ 435799.16695688 5011471.69142313] (dist: 9.33m, conf: 0.378)\n", "Match 1299: <PERSON><PERSON> [ 435553.06942857 5011630.955     ] <-> IFC [ 435552.16695688 5011631.69142313] (dist: 1.16m, conf: 0.922)\n", "Match 1300: <PERSON><PERSON> [ 436293.67038462 5012279.99138462] <-> IFC [ 436292.16695688 5012281.69142313] (dist: 2.27m, conf: 0.849)\n", "Match 1301: <PERSON><PERSON> [ 435721.94433333 5011778.3015    ] <-> IFC [ 435724.16695688 5011781.69142313] (dist: 4.05m, conf: 0.730)\n", "Match 1302: <PERSON><PERSON> [ 436481.07825    5012128.18458333] <-> IFC [ 436482.16695688 5012125.69142313] (dist: 2.72m, conf: 0.819)\n", "Match 1303: <PERSON><PERSON> [ 435439.60358333 5011860.35366667] <-> IFC [ 435438.16695688 5011857.69142313] (dist: 3.03m, conf: 0.798)\n", "Match 1304: <PERSON><PERSON> [ 435469.512  5011839.1064] <-> IFC [ 435466.16695688 5011843.69142313] (dist: 5.68m, conf: 0.622)\n", "Match 1305: <PERSON><PERSON> [ 435992.41046667 5012337.34993333] <-> IFC [ 435988.16695688 5012337.69142313] (dist: 4.26m, conf: 0.716)\n", "Match 1306: <PERSON><PERSON> [ 436519.12894118 5012023.81105882] <-> IFC [ 436520.16695688 5012027.69142313] (dist: 4.02m, conf: 0.732)\n", "Match 1307: <PERSON><PERSON> [ 436519.141625 5011912.030125] <-> IFC [ 436520.16695688 5011913.69142313] (dist: 1.95m, conf: 0.870)\n", "Match 1308: <PERSON><PERSON> [ 436382.9568     5011184.58706667] <-> IFC [ 436378.16695688 5011183.69142313] (dist: 4.87m, conf: 0.675)\n", "Match 1309: <PERSON><PERSON> [ 435871.48066667 5012269.5905    ] <-> IFC [ 435874.16695688 5012265.69142313] (dist: 4.73m, conf: 0.684)\n", "Match 1310: <PERSON><PERSON> [ 436632.9748 5011818.7686] <-> IFC [ 436634.16695688 5011821.69142313] (dist: 3.16m, conf: 0.790)\n", "Match 1311: <PERSON><PERSON> [ 435920.87946154 5012106.43007692] <-> IFC [ 435922.16695688 5012101.69142313] (dist: 4.91m, conf: 0.673)\n", "Match 1312: <PERSON><PERSON> [ 435694.66703571 5011386.37771429] <-> IFC [ 435694.16695688 5011389.69142313] (dist: 3.35m, conf: 0.777)\n", "Match 1313: <PERSON><PERSON> [ 436284.457 5012210.527] <-> IFC [ 436282.16695688 5012209.69142313] (dist: 2.44m, conf: 0.837)\n", "Match 1314: <PERSON><PERSON> [ 436537.4446     5012048.62853333] <-> IFC [ 436538.16695688 5012045.69142313] (dist: 3.02m, conf: 0.798)\n", "Match 1315: <PERSON><PERSON> [ 435563.095      5011912.78233333] <-> IFC [ 435562.16695688 5011909.69142313] (dist: 3.23m, conf: 0.785)\n", "Match 1316: <PERSON><PERSON> [ 435889.44575    5012264.91117857] <-> IFC [ 435892.16695688 5012267.69142313] (dist: 3.89m, conf: 0.741)\n", "Match 1317: <PERSON><PERSON> [ 436217.50858333 5012251.55475   ] <-> IFC [ 436216.16695688 5012247.69142313] (dist: 4.09m, conf: 0.727)\n", "Match 1318: <PERSON><PERSON> [ 436162.37745455 5012149.70718182] <-> IFC [ 436158.16695688 5012151.69142313] (dist: 4.65m, conf: 0.690)\n", "Match 1319: <PERSON><PERSON> [ 436360.0838 5012153.3931] <-> IFC [ 436358.16695688 5012155.69142313] (dist: 2.99m, conf: 0.800)\n", "Match 1320: <PERSON><PERSON> [ 436115.2714 5012362.123 ] <-> IFC [ 436120.16695688 5012361.69142313] (dist: 4.91m, conf: 0.672)\n", "Match 1321: <PERSON><PERSON> [ 436331.14145 5012125.5557 ] <-> IFC [ 436330.16695688 5012123.69142313] (dist: 2.10m, conf: 0.860)\n", "Match 1322: <PERSON><PERSON> [ 436189.8975 5012215.518 ] <-> IFC [ 436186.16695688 5012215.69142313] (dist: 3.73m, conf: 0.751)\n", "Match 1323: <PERSON><PERSON> [ 436275.49078947 5012131.94568421] <-> IFC [ 436272.16695688 5012127.69142313] (dist: 5.40m, conf: 0.640)\n", "Match 1324: <PERSON><PERSON> [ 435805.8296     5012096.79506667] <-> IFC [ 435808.16695688 5012093.69142313] (dist: 3.89m, conf: 0.741)\n", "Match 1325: <PERSON><PERSON> [ 436198.6688 5012243.4867] <-> IFC [ 436196.16695688 5012239.69142313] (dist: 4.55m, conf: 0.697)\n", "Match 1326: <PERSON><PERSON> [ 436087.74208333 5012326.53625   ] <-> IFC [ 436092.16695688 5012329.69142313] (dist: 5.43m, conf: 0.638)\n", "Match 1327: <PERSON><PERSON> [ 436227.85116667 5012119.36058333] <-> IFC [ 436224.16695688 5012119.69142313] (dist: 3.70m, conf: 0.753)\n", "Match 1328: <PERSON><PERSON> [ 436198.7245     5012452.38433333] <-> IFC [ 436196.16695688 5012455.69142313] (dist: 4.18m, conf: 0.721)\n", "Match 1329: <PERSON><PERSON> [ 436068.97478571 5012317.78264286] <-> IFC [ 436072.16695688 5012318.69142313] (dist: 3.32m, conf: 0.779)\n", "Match 1330: <PERSON><PERSON> [ 435449.89409091 5011697.64763636] <-> IFC [ 435448.16695688 5011699.69142313] (dist: 2.68m, conf: 0.822)\n", "Match 1331: <PERSON><PERSON> [ 436108.8374375 5012074.6165   ] <-> IFC [ 436112.16695688 5012073.69142313] (dist: 3.46m, conf: 0.770)\n", "Match 1332: <PERSON><PERSON> [ 436472.93638889 5012149.05411111] <-> IFC [ 436472.16695688 5012147.69142313] (dist: 1.56m, conf: 0.896)\n", "Match 1333: <PERSON><PERSON> [ 436245.98433333 5012285.554     ] <-> IFC [ 436244.16695688 5012288.69142313] (dist: 3.63m, conf: 0.758)\n", "Match 1334: <PERSON><PERSON> [ 436500.5295     5012103.75171429] <-> IFC [ 436500.16695688 5012101.69142313] (dist: 2.09m, conf: 0.861)\n", "Match 1335: <PERSON><PERSON> [ 436372.4427 5011117.7454] <-> IFC [ 436370.16695688 5011113.69142313] (dist: 4.65m, conf: 0.690)\n", "Match 1336: <PERSON><PERSON> [ 436053.00675 5011062.967  ] <-> IFC [ 436056.16695688 5011059.69142313] (dist: 4.55m, conf: 0.697)\n", "Match 1337: <PERSON><PERSON> [ 436312.96757143 5012146.12671428] <-> IFC [ 436310.16695688 5012147.69142313] (dist: 3.21m, conf: 0.786)\n", "Match 1338: <PERSON><PERSON> [ 435769.93790476 5011255.34683333] <-> IFC [ 435770.16695688 5011255.69142313] (dist: 0.41m, conf: 0.972)\n", "Match 1339: <PERSON><PERSON> [ 435825.86816667 5011431.56183333] <-> IFC [ 435828.16695688 5011429.69142313] (dist: 2.96m, conf: 0.802)\n", "Match 1340: <PERSON><PERSON> [ 435965.27466667 5012115.11516667] <-> IFC [ 435960.16695688 5012113.69142313] (dist: 5.30m, conf: 0.647)\n", "Match 1341: <PERSON><PERSON> [ 435806.76905 5011404.1072 ] <-> IFC [ 435808.16695688 5011407.69142313] (dist: 3.85m, conf: 0.744)\n", "Match 1342: <PERSON><PERSON> [ 435291.36855556 5011846.24166667] <-> IFC [ 435286.16695688 5011847.69142313] (dist: 5.40m, conf: 0.640)\n", "Match 1343: <PERSON><PERSON> [ 435309.6664 5011947.2788] <-> IFC [ 435306.16695688 5011949.69142313] (dist: 4.25m, conf: 0.717)\n", "Match 1344: <PERSON><PERSON> [ 436661.744      5011902.26755556] <-> IFC [ 436662.16695688 5011903.69142313] (dist: 1.49m, conf: 0.901)\n", "Match 1345: <PERSON><PERSON> [ 435394.96316667 5011700.6675    ] <-> IFC [ 435390.16695688 5011697.69142313] (dist: 5.64m, conf: 0.624)\n", "Match 1346: <PERSON><PERSON> [ 436133.96653333 5012397.33713333] <-> IFC [ 436130.16695688 5012393.69142313] (dist: 5.27m, conf: 0.649)\n", "Match 1347: <PERSON><PERSON> [ 435628.82490625 5011425.595625  ] <-> IFC [ 435628.16695688 5011427.69142313] (dist: 2.20m, conf: 0.854)\n", "Match 1348: <PERSON><PERSON> [ 436689.87733333 5011869.83966667] <-> IFC [ 436692.16695688 5011869.69142313] (dist: 2.29m, conf: 0.847)\n", "Match 1349: <PERSON><PERSON> [ 435837.4016 5011437.7976] <-> IFC [ 435837.16695688 5011441.69142313] (dist: 3.90m, conf: 0.740)\n", "Match 1350: <PERSON><PERSON> [ 435779.87066667 5011306.17855556] <-> IFC [ 435780.16695688 5011301.69142313] (dist: 4.50m, conf: 0.700)\n", "Match 1351: <PERSON><PERSON> [ 436040.31125    5012203.34671429] <-> IFC [ 436044.16695688 5012201.69142313] (dist: 4.20m, conf: 0.720)\n", "Match 1352: <PERSON><PERSON> [ 436115.74125 5012106.29025] <-> IFC [ 436112.16695688 5012107.69142313] (dist: 3.84m, conf: 0.744)\n", "Match 1353: <PERSON><PERSON> [ 435336.85724138 5011753.06668965] <-> IFC [ 435334.16695688 5011749.69142313] (dist: 4.32m, conf: 0.712)\n", "Match 1354: <PERSON><PERSON> [ 436518.259      5012094.51082353] <-> IFC [ 436520.16695688 5012095.69142313] (dist: 2.24m, conf: 0.850)\n", "Match 1355: <PERSON><PERSON> [ 436021.25307692 5012296.71826923] <-> IFC [ 436016.16695688 5012295.69142313] (dist: 5.19m, conf: 0.654)\n", "Match 1356: <PERSON><PERSON> [ 435797.0834     5011448.65053333] <-> IFC [ 435799.16695688 5011449.69142314] (dist: 2.33m, conf: 0.845)\n", "Match 1357: <PERSON><PERSON> [ 435908.76028571 5012288.94495238] <-> IFC [ 435912.16695688 5012285.69142313] (dist: 4.71m, conf: 0.686)\n", "Match 1358: <PERSON><PERSON> [ 436391.18760976 5011192.071     ] <-> IFC [ 436388.16695688 5011195.69142313] (dist: 4.72m, conf: 0.686)\n", "Match 1359: <PERSON><PERSON> [ 436404.93977778 5012074.80944444] <-> IFC [ 436406.16695688 5012070.69142313] (dist: 4.30m, conf: 0.714)\n", "Match 1360: <PERSON><PERSON> [ 436151.4587619  5012320.93752381] <-> IFC [ 436148.16695688 5012319.69142313] (dist: 3.52m, conf: 0.765)\n", "Match 1361: <PERSON><PERSON> [ 435336.55333333 5011767.70066667] <-> IFC [ 435334.16695688 5011765.69142313] (dist: 3.12m, conf: 0.792)\n", "Match 1362: <PERSON><PERSON> [ 435808.59916667 5012033.906     ] <-> IFC [ 435808.16695688 5012031.69142313] (dist: 2.26m, conf: 0.850)\n", "Match 1363: <PERSON><PERSON> [ 435872.49053333 5012037.73186667] <-> IFC [ 435876.16695688 5012035.69142313] (dist: 4.20m, conf: 0.720)\n", "Match 1364: <PERSON><PERSON> [ 436048.88025 5012212.44845] <-> IFC [ 436044.16695688 5012209.69142313] (dist: 5.46m, conf: 0.636)\n", "Match 1365: <PERSON><PERSON> [ 435666.399      5011466.26995652] <-> IFC [ 435666.16695688 5011469.69142313] (dist: 3.43m, conf: 0.771)\n", "Match 1366: <PERSON><PERSON> [ 436302.42561538 5012359.21280769] <-> IFC [ 436300.16695688 5012361.69142313] (dist: 3.35m, conf: 0.776)\n", "Match 1367: <PERSON><PERSON> [ 436577.7753125 5011859.157875 ] <-> IFC [ 436578.16695688 5011859.69142313] (dist: 0.66m, conf: 0.956)\n", "Match 1368: <PERSON><PERSON> [ 435846.53592857 5011551.97978571] <-> IFC [ 435846.16695688 5011557.69142313] (dist: 5.72m, conf: 0.618)\n", "Match 1369: <PERSON><PERSON> [ 436424.93047059 5012091.25717647] <-> IFC [ 436424.16695688 5012095.69142313] (dist: 4.50m, conf: 0.700)\n", "Match 1370: <PERSON><PERSON> [ 436662.36135294 5011811.84852941] <-> IFC [ 436662.16695688 5011811.69142313] (dist: 0.25m, conf: 0.983)\n", "Match 1371: <PERSON><PERSON> [ 435835.28745455 5012203.80309091] <-> IFC [ 435837.16695688 5012201.69142313] (dist: 2.83m, conf: 0.812)\n", "Match 1372: <PERSON><PERSON> [ 436040.00694737 5012277.53447368] <-> IFC [ 436044.16695688 5012277.69142313] (dist: 4.16m, conf: 0.722)\n", "Match 1373: <PERSON><PERSON> [ 435702.91478947 5011993.6728421 ] <-> IFC [ 435704.16695688 5011995.69142313] (dist: 2.38m, conf: 0.842)\n", "Match 1374: <PERSON><PERSON> [ 436330.75018182 5012306.37827273] <-> IFC [ 436330.16695688 5012309.69142313] (dist: 3.36m, conf: 0.776)\n", "Match 1375: <PERSON><PERSON> [ 436359.54705263 5012212.87626316] <-> IFC [ 436358.16695688 5012213.69142313] (dist: 1.60m, conf: 0.893)\n", "Match 1376: <PERSON><PERSON> [ 435817.657   5011454.29275] <-> IFC [ 435818.16695688 5011455.69142313] (dist: 1.49m, conf: 0.901)\n", "Match 1377: <PERSON><PERSON> [ 436331.70722222 5012161.04461111] <-> IFC [ 436330.16695688 5012157.69142313] (dist: 3.69m, conf: 0.754)\n", "Match 1378: <PERSON><PERSON> [ 436264.6764     5012289.31473333] <-> IFC [ 436262.16695688 5012291.69142313] (dist: 3.46m, conf: 0.770)\n", "Match 1379: <PERSON><PERSON> [ 435468.14353846 5011669.85930769] <-> IFC [ 435466.16695688 5011667.69142313] (dist: 2.93m, conf: 0.804)\n", "Match 1380: <PERSON><PERSON> [ 435337.5253 5011787.4117] <-> IFC [ 435334.16695688 5011789.69142313] (dist: 4.06m, conf: 0.729)\n", "Match 1381: <PERSON><PERSON> [ 435964.132875 5012226.324625] <-> IFC [ 435968.16695688 5012225.69142313] (dist: 4.08m, conf: 0.728)\n", "Match 1382: <PERSON><PERSON> [ 435901.06011111 5012170.672     ] <-> IFC [ 435904.16695688 5012169.69142313] (dist: 3.26m, conf: 0.783)\n", "Match 1383: <PERSON><PERSON> [ 436325.3798 5011091.8142] <-> IFC [ 436322.16695688 5011091.69142313] (dist: 3.22m, conf: 0.786)\n", "Match 1384: <PERSON><PERSON> [ 436058.212     5012205.3186875] <-> IFC [ 436054.16695688 5012206.69142313] (dist: 4.27m, conf: 0.715)\n", "Match 1385: <PERSON><PERSON> [ 436456.1520303  5011199.69636364] <-> IFC [ 436454.16695688 5011195.69142313] (dist: 4.47m, conf: 0.702)\n", "Match 1386: <PERSON><PERSON> [ 435779.9809 5011312.9124] <-> IFC [ 435780.16695688 5011317.69142313] (dist: 4.78m, conf: 0.681)\n", "Match 1387: <PERSON><PERSON> [ 436127.5864 5011052.876 ] <-> IFC [ 436122.16695688 5011045.69142313] (dist: 9.00m, conf: 0.400)\n", "Match 1388: <PERSON><PERSON> [ 435609.72988235 5012113.297     ] <-> IFC [ 435610.16695688 5012115.69142313] (dist: 2.43m, conf: 0.838)\n", "Match 1389: <PERSON><PERSON> [ 435861.9232 5012137.4856] <-> IFC [ 435866.16695688 5012141.69142313] (dist: 5.97m, conf: 0.602)\n", "Match 1390: <PERSON><PERSON> [ 436349.95787879 5012272.0959394 ] <-> IFC [ 436348.16695688 5012269.69142313] (dist: 3.00m, conf: 0.800)\n", "Match 1391: <PERSON><PERSON> [ 435675.41116667 5012151.58825   ] <-> IFC [ 435676.16695688 5012143.69142313] (dist: 7.93m, conf: 0.471)\n", "Match 1392: <PERSON><PERSON> [ 435543.6952 5011529.2917] <-> IFC [ 435542.16695688 5011525.69142313] (dist: 3.91m, conf: 0.739)\n", "Match 1393: <PERSON><PERSON> [ 435290.47414286 5011789.50821429] <-> IFC [ 435286.16695688 5011787.69142313] (dist: 4.67m, conf: 0.688)\n", "Match 1394: <PERSON><PERSON> [ 436228.09792308 5012191.90315385] <-> IFC [ 436224.16695688 5012195.69142313] (dist: 5.46m, conf: 0.636)\n", "Match 1395: <PERSON><PERSON> [ 435797.9084 5011431.9236] <-> IFC [ 435800.16695688 5011429.69142313] (dist: 3.18m, conf: 0.788)\n", "Match 1396: <PERSON><PERSON> [ 436234.7576 5012412.3552] <-> IFC [ 436234.16695688 5012411.69142313] (dist: 0.89m, conf: 0.941)\n", "Match 1397: <PERSON><PERSON> [ 435759.90663158 5011421.6911579 ] <-> IFC [ 435761.16695688 5011417.69142313] (dist: 4.19m, conf: 0.720)\n", "Match 1398: <PERSON><PERSON> [ 435346.28169565 5011740.731     ] <-> IFC [ 435344.16695688 5011737.69142313] (dist: 3.70m, conf: 0.753)\n", "Match 1399: <PERSON><PERSON> [ 436032.07486667 5012045.85186667] <-> IFC [ 436036.16695688 5012047.69142313] (dist: 4.49m, conf: 0.701)\n", "Match 1400: <PERSON><PERSON> [ 436292.6335     5012287.61992308] <-> IFC [ 436292.16695688 5012289.69142313] (dist: 2.12m, conf: 0.858)\n", "Match 1401: <PERSON><PERSON> [ 435944.50490909 5012287.27054545] <-> IFC [ 435950.16695688 5012291.69142313] (dist: 7.18m, conf: 0.521)\n", "Match 1402: <PERSON><PERSON> [ 436521.01955556 5012056.80122222] <-> IFC [ 436520.16695688 5012053.69142313] (dist: 3.22m, conf: 0.785)\n", "Match 1403: <PERSON><PERSON> [ 436411.4665 5011160.5055] <-> IFC [ 436416.16695688 5011157.69142313] (dist: 5.48m, conf: 0.635)\n", "Match 1404: <PERSON><PERSON> [ 436020.04009091 5012348.923     ] <-> IFC [ 436016.16695688 5012345.69142313] (dist: 5.04m, conf: 0.664)\n", "Match 1405: <PERSON><PERSON> [ 435777.6862 5011986.4148] <-> IFC [ 435780.16695688 5011987.69142313] (dist: 2.79m, conf: 0.814)\n", "Match 1406: <PERSON><PERSON> [ 436089.51352381 5011735.81357143] <-> IFC [ 436094.16695688 5011737.69142313] (dist: 5.02m, conf: 0.665)\n", "Match 1407: <PERSON><PERSON> [ 436024.85414286 5011643.74942857] <-> IFC [ 436027.16695688 5011649.69142313] (dist: 6.38m, conf: 0.575)\n", "Match 1408: <PERSON><PERSON> [ 436654.62266667 5011863.35833333] <-> IFC [ 436654.16695688 5011855.69142313] (dist: 7.68m, conf: 0.488)\n", "Match 1409: <PERSON><PERSON> [ 436199.8935 5012229.0815] <-> IFC [ 436196.16695688 5012231.69142313] (dist: 4.55m, conf: 0.697)\n", "Match 1410: <PERSON><PERSON> [ 435963.77416667 5012231.52191667] <-> IFC [ 435958.16695688 5012229.69142313] (dist: 5.90m, conf: 0.607)\n", "Match 1411: <PERSON><PERSON> [ 436227.404      5012233.08365217] <-> IFC [ 436224.16695688 5012229.69142313] (dist: 4.69m, conf: 0.687)\n", "Match 1412: <PERSON><PERSON> [ 436229.1195  5011985.24725] <-> IFC [ 436226.16695688 5011981.69142313] (dist: 4.62m, conf: 0.692)\n", "Match 1413: <PERSON><PERSON> [ 436284.94418182 5012167.502     ] <-> IFC [ 436282.16695688 5012167.69142313] (dist: 2.78m, conf: 0.814)\n", "Match 1414: <PERSON><PERSON> [ 435742.22880952 5011398.87004762] <-> IFC [ 435742.16695688 5011397.69142313] (dist: 1.18m, conf: 0.921)\n", "Match 1415: <PERSON><PERSON> [ 435948.01328571 5011525.92823809] <-> IFC [ 435951.16695688 5011523.69142313] (dist: 3.87m, conf: 0.742)\n", "Match 1416: <PERSON><PERSON> [ 436539.43858333 5011884.9825    ] <-> IFC [ 436540.16695688 5011881.69142313] (dist: 3.37m, conf: 0.775)\n", "Match 1417: <PERSON><PERSON> [ 436266.59925 5012174.72375] <-> IFC [ 436262.16695688 5012173.69142313] (dist: 4.55m, conf: 0.697)\n", "Match 1418: <PERSON><PERSON> [ 436341.2091  5012121.33215] <-> IFC [ 436338.16695688 5012120.69142313] (dist: 3.11m, conf: 0.793)\n", "Match 1419: <PERSON><PERSON> [ 436254.75415 5012318.4977 ] <-> IFC [ 436254.16695688 5012321.69142313] (dist: 3.25m, conf: 0.784)\n", "Match 1420: <PERSON><PERSON> [ 435966.03428571 5012150.693     ] <-> IFC [ 435970.16695688 5012151.69142313] (dist: 4.25m, conf: 0.717)\n", "Match 1421: <PERSON><PERSON> [ 436327.2666     5011637.10546667] <-> IFC [ 436326.16695688 5011635.69142313] (dist: 1.79m, conf: 0.881)\n", "Match 1422: <PERSON><PERSON> [ 436068.6625  5012303.60925] <-> IFC [ 436072.16695688 5012301.69142313] (dist: 3.99m, conf: 0.734)\n", "Match 1423: <PERSON><PERSON> [ 436012.37557143 5012223.19385714] <-> IFC [ 436016.16695688 5012227.69142313] (dist: 5.88m, conf: 0.608)\n", "Match 1424: <PERSON><PERSON> [ 436171.81233333 5012378.32673333] <-> IFC [ 436168.16695688 5012381.69142313] (dist: 4.96m, conf: 0.669)\n", "Match 1425: <PERSON><PERSON> [ 436061.47271429 5011059.70157143] <-> IFC [ 436056.16695688 5011059.69142313] (dist: 5.31m, conf: 0.646)\n", "Match 1426: <PERSON><PERSON> [ 436175.8989 5010979.3226] <-> IFC [ 436180.16695688 5010977.69142313] (dist: 4.57m, conf: 0.695)\n", "Match 1427: <PERSON><PERSON> [ 436368.79785714 5012131.47157143] <-> IFC [ 436368.16695688 5012127.69142313] (dist: 3.83m, conf: 0.745)\n", "Match 1428: <PERSON><PERSON> [ 435458.1064 5011802.4162] <-> IFC [ 435458.16695688 5011801.69142313] (dist: 0.73m, conf: 0.952)\n", "Match 1429: <PERSON><PERSON> [ 436473.07471429 5012137.93571428] <-> IFC [ 436472.16695688 5012137.69142313] (dist: 0.94m, conf: 0.937)\n", "Match 1430: <PERSON><PERSON> [ 436576.0938 5012010.6076] <-> IFC [ 436576.16695688 5012005.69142313] (dist: 4.92m, conf: 0.672)\n", "Match 1431: <PERSON><PERSON> [ 436293.22934783 5012326.11752174] <-> IFC [ 436292.16695688 5012323.69142313] (dist: 2.65m, conf: 0.823)\n", "Match 1432: <PERSON><PERSON> [ 436511.320125  5011949.4619375] <-> IFC [ 436510.16695688 5011951.69142313] (dist: 2.51m, conf: 0.833)\n", "Match 1433: <PERSON><PERSON> [ 435981.34841176 5011240.37305882] <-> IFC [ 435980.16695688 5011245.69142313] (dist: 5.45m, conf: 0.637)\n", "Match 1434: Dr<PERSON> [ 435721.872      5012013.01033333] <-> IFC [ 435724.16695688 5012009.69142313] (dist: 4.04m, conf: 0.731)\n", "Match 1435: <PERSON><PERSON> [ 436198.93052941 5012440.50223529] <-> IFC [ 436196.16695688 5012439.69142313] (dist: 2.88m, conf: 0.808)\n", "Match 1436: <PERSON><PERSON> [ 436634.89790909 5011907.68063636] <-> IFC [ 436634.16695688 5011905.69142313] (dist: 2.12m, conf: 0.859)\n", "Match 1437: <PERSON><PERSON> [ 436162.78927273 5012343.29927273] <-> IFC [ 436158.16695688 5012341.69142313] (dist: 4.89m, conf: 0.674)\n", "Match 1438: <PERSON><PERSON> [ 436227.991      5012143.35933333] <-> IFC [ 436224.16695688 5012145.69142313] (dist: 4.48m, conf: 0.701)\n", "Match 1439: <PERSON><PERSON> [ 436040.12307143 5012212.67814286] <-> IFC [ 436044.16695688 5012209.69142313] (dist: 5.03m, conf: 0.665)\n", "Match 1440: <PERSON><PERSON> [ 436164.17609091 5010972.47518182] <-> IFC [ 436160.16695688 5010969.69142313] (dist: 4.88m, conf: 0.675)\n", "Match 1441: <PERSON><PERSON> [ 436614.83166667 5011861.28425   ] <-> IFC [ 436616.16695688 5011861.69142313] (dist: 1.40m, conf: 0.907)\n", "Match 1442: <PERSON><PERSON> [ 435806.97807692 5011387.28807692] <-> IFC [ 435808.16695688 5011383.69142313] (dist: 3.79m, conf: 0.747)\n", "Match 1443: <PERSON><PERSON> [ 435648.03529412 5012152.74447059] <-> IFC [ 435656.16695688 5012149.69142313] (dist: 8.69m, conf: 0.421)\n", "Match 1444: <PERSON><PERSON> [ 436566.5774 5011800.8924] <-> IFC [ 436568.16695688 5011803.69142313] (dist: 3.22m, conf: 0.785)\n", "Match 1445: <PERSON><PERSON> [ 436040.19072727 5012269.04713636] <-> IFC [ 436044.16695688 5012268.69142313] (dist: 3.99m, conf: 0.734)\n", "Match 1446: <PERSON><PERSON> [ 436001.8431  5012280.48685] <-> IFC [ 436006.16695688 5012280.69142313] (dist: 4.33m, conf: 0.711)\n", "Match 1447: <PERSON><PERSON> [ 436188.15685714 5012331.23      ] <-> IFC [ 436186.16695688 5012331.69142313] (dist: 2.04m, conf: 0.864)\n", "Match 1448: <PERSON><PERSON> [ 435442.6665     5011639.24733333] <-> IFC [ 435438.16695688 5011635.69142313] (dist: 5.74m, conf: 0.618)\n", "Match 1449: <PERSON><PERSON> [ 435694.762   5011411.21035] <-> IFC [ 435694.16695688 5011407.69142313] (dist: 3.57m, conf: 0.762)\n", "Match 1450: <PERSON><PERSON> [ 435826.0559 5012186.9736] <-> IFC [ 435828.16695688 5012189.69142313] (dist: 3.44m, conf: 0.771)\n", "Match 1451: <PERSON><PERSON> [ 435778.66290909 5011405.37945454] <-> IFC [ 435780.16695688 5011403.69142313] (dist: 2.26m, conf: 0.849)\n", "Match 1452: <PERSON><PERSON> [ 436453.50175  5012107.277125] <-> IFC [ 436452.16695688 5012103.69142313] (dist: 3.83m, conf: 0.745)\n", "Match 1453: <PERSON><PERSON> [ 436082.01183333 5011773.69216667] <-> IFC [ 436084.16695688 5011777.69142313] (dist: 4.54m, conf: 0.697)\n", "Match 1454: <PERSON><PERSON> [ 435900.64525    5011520.26566667] <-> IFC [ 435904.16695688 5011521.69142313] (dist: 3.80m, conf: 0.747)\n", "Match 1455: <PERSON><PERSON> [ 436056.96442857 5012328.02328571] <-> IFC [ 436054.16695688 5012324.69142313] (dist: 4.35m, conf: 0.710)\n", "Match 1456: <PERSON><PERSON> [ 436423.58842857 5012072.47371429] <-> IFC [ 436424.16695688 5012069.69142313] (dist: 2.84m, conf: 0.811)\n", "Match 1457: <PERSON><PERSON> [ 435601.71888889 5011734.50522222] <-> IFC [ 435600.16695688 5011735.69142313] (dist: 1.95m, conf: 0.870)\n", "Match 1458: <PERSON><PERSON> [ 436116.1992 5012301.1552] <-> IFC [ 436120.16695688 5012303.69142313] (dist: 4.71m, conf: 0.686)\n", "Match 1459: <PERSON><PERSON> [ 435790.264      5011294.60914286] <-> IFC [ 435790.16695688 5011289.69142313] (dist: 4.92m, conf: 0.672)\n", "Match 1460: <PERSON><PERSON> [ 435805.476  5012112.1835] <-> IFC [ 435808.16695688 5012111.69142313] (dist: 2.74m, conf: 0.818)\n", "Match 1461: <PERSON><PERSON> [ 435846.34425 5011546.45125] <-> IFC [ 435838.16695688 5011549.69142313] (dist: 8.80m, conf: 0.414)\n", "Match 1462: <PERSON><PERSON> [ 436347.09718182 5011569.79409091] <-> IFC [ 436344.16695688 5011570.69142313] (dist: 3.06m, conf: 0.796)\n", "Match 1463: <PERSON><PERSON> [ 435345.5056 5011763.4442] <-> IFC [ 435344.16695688 5011763.69142313] (dist: 1.36m, conf: 0.909)\n", "Match 1464: <PERSON><PERSON> [ 436051.8725 5011055.1606] <-> IFC [ 436046.16695688 5011053.69142313] (dist: 5.89m, conf: 0.607)\n", "Match 1465: <PERSON><PERSON> [ 436633.75955556 5011894.83294444] <-> IFC [ 436634.16695688 5011897.69142313] (dist: 2.89m, conf: 0.808)\n", "Match 1466: <PERSON><PERSON> [ 436437.08972727 5011971.18445454] <-> IFC [ 436436.16695688 5011971.69142313] (dist: 1.05m, conf: 0.930)\n", "Match 1467: <PERSON><PERSON> [ 435909.306625 5011968.98725 ] <-> IFC [ 435904.16695688 5011971.69142313] (dist: 5.81m, conf: 0.613)\n", "Match 1468: <PERSON><PERSON> [ 436444.7553 5012095.4919] <-> IFC [ 436444.16695688 5012097.69142313] (dist: 2.28m, conf: 0.848)\n", "Match 1469: <PERSON><PERSON> [ 435954.1866 5012284.3594] <-> IFC [ 435950.16695688 5012282.69142313] (dist: 4.35m, conf: 0.710)\n", "Match 1470: <PERSON><PERSON> [ 435931.1052 5012145.8928] <-> IFC [ 435932.16695688 5012149.69142313] (dist: 3.94m, conf: 0.737)\n", "Match 1471: <PERSON><PERSON> [ 435374.90847619 5011793.41447619] <-> IFC [ 435372.16695688 5011787.69142313] (dist: 6.35m, conf: 0.577)\n", "Match 1472: <PERSON><PERSON> [ 435769.4185     5011461.27966667] <-> IFC [ 435770.16695688 5011463.69142313] (dist: 2.53m, conf: 0.832)\n", "Match 1473: <PERSON><PERSON> [ 435945.76855556 5012297.29344444] <-> IFC [ 435950.16695688 5012299.69142313] (dist: 5.01m, conf: 0.666)\n", "Match 1474: <PERSON><PERSON> [ 436132.5855 5012319.94  ] <-> IFC [ 436130.16695688 5012315.69142313] (dist: 4.89m, conf: 0.674)\n", "Match 1475: <PERSON><PERSON> [ 436707.98276471 5011839.75835294] <-> IFC [ 436710.16695688 5011837.69142313] (dist: 3.01m, conf: 0.800)\n", "Match 1476: <PERSON><PERSON> [ 436396.97964286 5012186.62428571] <-> IFC [ 436396.16695688 5012183.69142313] (dist: 3.04m, conf: 0.797)\n", "Match 1477: <PERSON><PERSON> [ 435911.287375 5012125.754625] <-> IFC [ 435904.16695688 5012127.69142313] (dist: 7.38m, conf: 0.508)\n", "Match 1478: <PERSON><PERSON> [ 435901.01486667 5011995.8778    ] <-> IFC [ 435904.16695688 5011997.69142313] (dist: 3.64m, conf: 0.758)\n", "Match 1479: <PERSON><PERSON> [ 436247.52733333 5012171.98591667] <-> IFC [ 436244.16695688 5012169.69142313] (dist: 4.07m, conf: 0.729)\n", "Match 1480: <PERSON><PERSON> [ 435740.32075 5011467.5685 ] <-> IFC [ 435742.16695688 5011463.69142313] (dist: 4.29m, conf: 0.714)\n", "Match 1481: <PERSON><PERSON> [ 436330.04504 5012329.52076] <-> IFC [ 436330.16695688 5012335.69142313] (dist: 6.17m, conf: 0.589)\n", "Match 1482: <PERSON><PERSON> [ 436293.46061538 5012103.51961538] <-> IFC [ 436292.16695688 5012103.69142313] (dist: 1.31m, conf: 0.913)\n", "Match 1483: <PERSON><PERSON> [ 436556.41372727 5012046.31809091] <-> IFC [ 436558.16695688 5012047.69142313] (dist: 2.23m, conf: 0.852)\n", "Match 1484: <PERSON><PERSON> [ 436156.3205     5011232.56416667] <-> IFC [ 436150.16695688 5011229.69142313] (dist: 6.79m, conf: 0.547)\n", "Match 1485: <PERSON><PERSON> [ 435553.32957143 5011494.90171429] <-> IFC [ 435552.16695688 5011495.69142313] (dist: 1.41m, conf: 0.906)\n", "Match 1486: <PERSON><PERSON> [ 436425.74030769 5012077.37507692] <-> IFC [ 436424.16695688 5012069.69142313] (dist: 7.84m, conf: 0.477)\n", "Match 1487: <PERSON><PERSON> [ 435778.87990476 5011700.54590476] <-> IFC [ 435780.16695688 5011697.69142313] (dist: 3.13m, conf: 0.791)\n", "Match 1488: <PERSON><PERSON> [ 436067.58016667 5012233.74786667] <-> IFC [ 436072.16695688 5012233.69142313] (dist: 4.59m, conf: 0.694)\n", "Match 1489: <PERSON><PERSON> [ 436114.0137 5012226.7774] <-> IFC [ 436110.16695688 5012229.69142313] (dist: 4.83m, conf: 0.678)\n", "Match 1490: <PERSON><PERSON> [ 436116.70547059 5012079.40317647] <-> IFC [ 436112.16695688 5012081.69142313] (dist: 5.08m, conf: 0.661)\n", "Match 1491: <PERSON><PERSON> [ 436048.47727778 5012348.08816667] <-> IFC [ 436044.16695688 5012345.69142313] (dist: 4.93m, conf: 0.671)\n", "Match 1492: <PERSON><PERSON> [ 436137.66972222 5010996.00488889] <-> IFC [ 436140.16695688 5010991.69142313] (dist: 4.98m, conf: 0.668)\n", "Match 1493: <PERSON><PERSON> [ 436086.76257143 5012278.322     ] <-> IFC [ 436092.16695688 5012277.69142313] (dist: 5.44m, conf: 0.637)\n", "Match 1494: <PERSON><PERSON> [ 436453.453      5012040.14871428] <-> IFC [ 436452.16695688 5012043.69142313] (dist: 3.77m, conf: 0.749)\n", "Match 1495: <PERSON><PERSON> [ 436652.82833333 5011901.40958333] <-> IFC [ 436654.16695688 5011897.69142313] (dist: 3.95m, conf: 0.737)\n", "Match 1496: <PERSON><PERSON> [ 436297.58014286 5011165.24028571] <-> IFC [ 436302.16695688 5011161.69142313] (dist: 5.80m, conf: 0.613)\n", "Match 1497: <PERSON><PERSON> [ 436052.4253125 5011853.3995   ] <-> IFC [ 436056.16695688 5011853.69142313] (dist: 3.75m, conf: 0.750)\n", "Match 1498: <PERSON><PERSON> [ 436265.20305714 5012223.43305714] <-> IFC [ 436262.16695688 5012225.69142313] (dist: 3.78m, conf: 0.748)\n", "Match 1499: <PERSON><PERSON> [ 436642.59258824 5011912.38088235] <-> IFC [ 436634.16695688 5011913.69142313] (dist: 8.53m, conf: 0.432)\n", "Match 1500: <PERSON><PERSON> [ 435967.544375 5011988.969   ] <-> IFC [ 435970.16695688 5011987.69142313] (dist: 2.92m, conf: 0.806)\n", "Match 1501: <PERSON><PERSON> [ 436137.33075 5011006.85425] <-> IFC [ 436141.16695688 5011009.69142313] (dist: 4.77m, conf: 0.682)\n", "Match 1502: <PERSON><PERSON> [ 436425.68561905 5012061.85752381] <-> IFC [ 436424.16695688 5012061.69142313] (dist: 1.53m, conf: 0.898)\n", "Match 1503: <PERSON><PERSON> [ 436293.41678571 5012246.04692857] <-> IFC [ 436292.16695688 5012247.69142313] (dist: 2.07m, conf: 0.862)\n", "Match 1504: <PERSON><PERSON> [ 436425.95045455 5012155.20436364] <-> IFC [ 436424.16695688 5012155.69142313] (dist: 1.85m, conf: 0.877)\n", "Match 1505: <PERSON><PERSON> [ 436124.90083333 5012231.316     ] <-> IFC [ 436130.16695688 5012233.69142313] (dist: 5.78m, conf: 0.615)\n", "Match 1506: <PERSON><PERSON> [ 435882.16605556 5012170.36383333] <-> IFC [ 435884.16695688 5012167.69142313] (dist: 3.34m, conf: 0.777)\n", "Match 1507: <PERSON><PERSON> [ 436152.69625 5012337.227  ] <-> IFC [ 436148.16695688 5012335.69142313] (dist: 4.78m, conf: 0.681)\n", "Match 1508: <PERSON><PERSON> [ 435769.83976 5011430.72268] <-> IFC [ 435770.16695688 5011433.69142313] (dist: 2.99m, conf: 0.801)\n", "Match 1509: <PERSON><PERSON> [ 436369.0305   5012143.342125] <-> IFC [ 436368.16695688 5012143.69142313] (dist: 0.93m, conf: 0.938)\n", "Match 1510: <PERSON><PERSON> [ 436500.091    5012024.185125] <-> IFC [ 436500.16695688 5012025.69142313] (dist: 1.51m, conf: 0.899)\n", "Match 1511: <PERSON><PERSON> [ 436151.0954 5012327.2788] <-> IFC [ 436148.16695688 5012327.69142313] (dist: 2.96m, conf: 0.803)\n", "Match 1512: <PERSON><PERSON> [ 436595.934875 5011899.02875 ] <-> IFC [ 436596.16695688 5011894.69142313] (dist: 4.34m, conf: 0.710)\n", "Match 1513: <PERSON><PERSON> [ 436471.96508333 5012075.47733333] <-> IFC [ 436472.16695688 5012071.69142313] (dist: 3.79m, conf: 0.747)\n", "Match 1514: <PERSON><PERSON> [ 435910.37410345 5012084.34975862] <-> IFC [ 435914.16695688 5012080.69142313] (dist: 5.27m, conf: 0.649)\n", "Match 1515: <PERSON><PERSON> [ 435846.11521429 5011421.282     ] <-> IFC [ 435846.16695688 5011419.69142313] (dist: 1.59m, conf: 0.894)\n", "Match 1516: <PERSON><PERSON> [ 436254.62778571 5012426.13164286] <-> IFC [ 436254.16695688 5012429.69142313] (dist: 3.59m, conf: 0.761)\n", "Match 1517: <PERSON><PERSON> [ 436568.685      5011884.95944444] <-> IFC [ 436568.16695688 5011887.69142313] (dist: 2.78m, conf: 0.815)\n", "Match 1518: <PERSON><PERSON> [ 435769.73131579 5011982.55905263] <-> IFC [ 435770.16695688 5011977.69142313] (dist: 4.89m, conf: 0.674)\n", "Match 1519: <PERSON><PERSON> [ 436229.50989286 5011955.03867857] <-> IFC [ 436226.16695688 5011955.69142313] (dist: 3.41m, conf: 0.773)\n", "Match 1520: <PERSON><PERSON> [ 435975.9237 5012022.0116] <-> IFC [ 435980.16695688 5012019.69142313] (dist: 4.84m, conf: 0.678)\n", "Match 1521: <PERSON><PERSON> [ 436049.22133333 5012206.5945    ] <-> IFC [ 436054.16695688 5012206.69142313] (dist: 4.95m, conf: 0.670)\n", "Match 1522: <PERSON><PERSON> [ 436415.3535  5012149.21475] <-> IFC [ 436414.16695688 5012151.69142313] (dist: 2.75m, conf: 0.817)\n", "Match 1523: <PERSON><PERSON> [ 436463.95945455 5012158.055     ] <-> IFC [ 436462.16695688 5012159.69142313] (dist: 2.43m, conf: 0.838)\n", "Match 1524: <PERSON><PERSON> [ 435714.5506 5011409.1648] <-> IFC [ 435714.16695688 5011405.69142313] (dist: 3.49m, conf: 0.767)\n", "Match 1525: <PERSON><PERSON> [ 436114.901      5012296.03728571] <-> IFC [ 436110.16695688 5012297.69142313] (dist: 5.01m, conf: 0.666)\n", "Match 1526: <PERSON><PERSON> [ 435345.79661538 5011797.00361538] <-> IFC [ 435344.16695688 5011795.69142313] (dist: 2.09m, conf: 0.861)\n", "Match 1527: <PERSON><PERSON> [ 436265.75533333 5012134.39233333] <-> IFC [ 436262.16695688 5012131.69142313] (dist: 4.49m, conf: 0.701)\n", "Match 1528: <PERSON><PERSON> [ 436319.4032 5012292.8884] <-> IFC [ 436320.16695688 5012295.69142313] (dist: 2.91m, conf: 0.806)\n", "Match 1529: <PERSON><PERSON> [ 436353.67484211 5011157.83042105] <-> IFC [ 436350.16695688 5011155.69142313] (dist: 4.11m, conf: 0.726)\n", "Match 1530: <PERSON><PERSON> [ 436671.225      5011818.13916667] <-> IFC [ 436672.16695688 5011815.69142313] (dist: 2.62m, conf: 0.825)\n", "Match 1531: <PERSON><PERSON> [ 435797.01366667 5011463.38516667] <-> IFC [ 435790.16695688 5011463.69142313] (dist: 6.85m, conf: 0.543)\n", "Match 1532: <PERSON><PERSON> [ 435936.7574 5011999.5088] <-> IFC [ 435942.16695688 5012001.69142313] (dist: 5.83m, conf: 0.611)\n", "Match 1533: <PERSON><PERSON> [ 436510.21964286 5012012.983     ] <-> IFC [ 436510.16695688 5012013.69142313] (dist: 0.71m, conf: 0.953)\n", "Match 1534: <PERSON><PERSON> [ 436484.37885714 5011888.409     ] <-> IFC [ 436482.16695688 5011891.69142313] (dist: 3.96m, conf: 0.736)\n", "Match 1535: <PERSON><PERSON> [ 435497.26414286 5011597.56042857] <-> IFC [ 435496.16695688 5011597.69142313] (dist: 1.10m, conf: 0.926)\n", "Match 1536: <PERSON><PERSON> [ 436058.10163636 5012368.14427273] <-> IFC [ 436054.16695688 5012367.69142313] (dist: 3.96m, conf: 0.736)\n", "Match 1537: <PERSON><PERSON> [ 436414.5697 5012217.876 ] <-> IFC [ 436414.16695688 5012217.69142313] (dist: 0.44m, conf: 0.970)\n", "Match 1538: <PERSON><PERSON> [ 436183.58175 5010961.9155 ] <-> IFC [ 436188.16695688 5010963.69142313] (dist: 4.92m, conf: 0.672)\n", "Match 1539: <PERSON><PERSON> [ 436453.5692  5012146.72055] <-> IFC [ 436452.16695688 5012145.69142313] (dist: 1.74m, conf: 0.884)\n", "Match 1540: <PERSON><PERSON> [ 436021.04027273 5012284.80654546] <-> IFC [ 436016.16695688 5012285.69142313] (dist: 4.95m, conf: 0.670)\n", "Match 1541: <PERSON><PERSON> [ 436316.3830625 5011112.2275   ] <-> IFC [ 436312.16695688 5011109.69142313] (dist: 4.92m, conf: 0.672)\n", "Match 1542: <PERSON><PERSON> [ 435336.7866  5011963.30515] <-> IFC [ 435334.16695688 5011966.69142313] (dist: 4.28m, conf: 0.715)\n", "Match 1543: <PERSON><PERSON> [ 436441.2618     5011674.32986667] <-> IFC [ 436440.16695688 5011673.69142313] (dist: 1.27m, conf: 0.916)\n", "Match 1544: <PERSON><PERSON> [ 435892.05145 5011609.9124 ] <-> IFC [ 435884.16695688 5011607.69142313] (dist: 8.19m, conf: 0.454)\n", "Match 1545: <PERSON><PERSON> [ 435946.968  5011448.0852] <-> IFC [ 435942.16695688 5011449.69142313] (dist: 5.06m, conf: 0.662)\n", "Match 1546: <PERSON><PERSON> [ 436315.32255556 5011239.044     ] <-> IFC [ 436312.16695688 5011236.69142313] (dist: 3.94m, conf: 0.738)\n", "Match 1547: <PERSON><PERSON> [ 435553.72006667 5011560.3272    ] <-> IFC [ 435552.16695688 5011563.69142313] (dist: 3.71m, conf: 0.753)\n", "Match 1548: <PERSON><PERSON> [ 435300.0082 5011793.7498] <-> IFC [ 435296.16695688 5011793.69142313] (dist: 3.84m, conf: 0.744)\n", "Match 1549: <PERSON><PERSON> [ 436293.81403704 5012253.28688889] <-> IFC [ 436292.16695688 5012255.69142313] (dist: 2.91m, conf: 0.806)\n", "Match 1550: <PERSON><PERSON> [ 436010.653  5012234.8598] <-> IFC [ 436006.16695688 5012237.69142313] (dist: 5.30m, conf: 0.646)\n", "Match 1551: <PERSON><PERSON> [ 436171.05268182 5012391.53140909] <-> IFC [ 436168.16695688 5012389.69142313] (dist: 3.42m, conf: 0.772)\n", "Match 1552: <PERSON><PERSON> [ 436013.32566667 5012046.92966667] <-> IFC [ 436018.16695688 5012049.69142313] (dist: 5.57m, conf: 0.628)\n", "Match 1553: <PERSON><PERSON> [ 436230.789      5011965.37311765] <-> IFC [ 436226.16695688 5011965.69142313] (dist: 4.63m, conf: 0.691)\n", "Match 1554: <PERSON><PERSON> [ 435383.87688889 5011687.978     ] <-> IFC [ 435382.16695688 5011691.69142313] (dist: 4.09m, conf: 0.727)\n", "Match 1555: <PERSON><PERSON> [ 436325.43075  5011074.442375] <-> IFC [ 436322.16695688 5011075.69142313] (dist: 3.49m, conf: 0.767)\n", "Match 1556: <PERSON><PERSON> [ 435901.77071429 5011602.605     ] <-> IFC [ 435904.16695688 5011599.69142313] (dist: 3.77m, conf: 0.749)\n", "Match 1557: <PERSON><PERSON> [ 436340.27192 5012273.31068] <-> IFC [ 436338.16695688 5012271.69142313] (dist: 2.66m, conf: 0.823)\n", "Match 1558: <PERSON><PERSON> [ 435517.74785714 5011556.64714286] <-> IFC [ 435514.16695688 5011556.69142313] (dist: 3.58m, conf: 0.761)\n", "Match 1559: <PERSON><PERSON> [ 436605.883      5011763.23114286] <-> IFC [ 436606.16695688 5011763.69142313] (dist: 0.54m, conf: 0.964)\n", "Match 1560: <PERSON><PERSON> [ 435599.9884     5011660.40693333] <-> IFC [ 435600.16695688 5011657.69142313] (dist: 2.72m, conf: 0.819)\n", "Match 1561: <PERSON><PERSON> [ 435796.524  5011520.6236] <-> IFC [ 435800.16695688 5011515.69142313] (dist: 6.13m, conf: 0.591)\n", "Match 1562: <PERSON><PERSON> [ 435937.74222222 5012021.66388889] <-> IFC [ 435932.16695688 5012019.69142313] (dist: 5.91m, conf: 0.606)\n", "Match 1563: <PERSON><PERSON> [ 436189.982625 5012448.804875] <-> IFC [ 436186.16695688 5012449.69142313] (dist: 3.92m, conf: 0.739)\n", "Match 1564: <PERSON><PERSON> [ 436208.78244444 5012247.30122222] <-> IFC [ 436206.16695688 5012247.69142313] (dist: 2.64m, conf: 0.824)\n", "Match 1565: <PERSON><PERSON> [ 436254.45678947 5012358.94057895] <-> IFC [ 436254.16695688 5012355.69142313] (dist: 3.26m, conf: 0.783)\n", "Match 1566: <PERSON><PERSON> [ 436546.2094 5012035.0685] <-> IFC [ 436548.16695688 5012033.69142313] (dist: 2.39m, conf: 0.840)\n", "Match 1567: <PERSON><PERSON> [ 435523.62416667 5011576.78416667] <-> IFC [ 435524.16695688 5011577.69142313] (dist: 1.06m, conf: 0.930)\n", "Match 1568: <PERSON><PERSON> [ 436063.04657143 5011055.29642857] <-> IFC [ 436065.16695688 5011047.69142313] (dist: 7.90m, conf: 0.474)\n", "Match 1569: <PERSON><PERSON> [ 436227.25642593 5012395.25851852] <-> IFC [ 436224.23282408 5012392.67444576] (dist: 3.98m, conf: 0.735)\n", "Match 1570: <PERSON><PERSON> [ 436344.845375 5011192.766875] <-> IFC [ 436340.16695688 5011195.69142313] (dist: 5.52m, conf: 0.632)\n", "Match 1571: <PERSON><PERSON> [ 436378.42066667 5012177.31433333] <-> IFC [ 436376.16695688 5012173.69142313] (dist: 4.27m, conf: 0.716)\n", "Match 1572: <PERSON><PERSON> [ 436143.408625 5012149.889625] <-> IFC [ 436140.16695688 5012153.69142313] (dist: 5.00m, conf: 0.667)\n", "Match 1573: <PERSON><PERSON> [ 436061.52361538 5011043.62723077] <-> IFC [ 436065.16695688 5011047.69142313] (dist: 5.46m, conf: 0.636)\n", "Match 1574: <PERSON><PERSON> [ 436105.28386667 5012271.12706667] <-> IFC [ 436102.16695688 5012267.69142313] (dist: 4.64m, conf: 0.691)\n", "Match 1575: <PERSON><PERSON> [ 435356.85452381 5011768.43823809] <-> IFC [ 435352.16695688 5011769.69142313] (dist: 4.85m, conf: 0.677)\n", "Match 1576: <PERSON><PERSON> [ 435563.16228571 5011482.57828571] <-> IFC [ 435562.16695688 5011482.69142313] (dist: 1.00m, conf: 0.933)\n", "Match 1577: <PERSON><PERSON> [ 436501.30285714 5011958.67778571] <-> IFC [ 436502.16695688 5011955.69142313] (dist: 3.11m, conf: 0.793)\n", "Match 1578: <PERSON><PERSON> [ 436577.87666667 5011884.8045    ] <-> IFC [ 436578.16695688 5011885.69142313] (dist: 0.93m, conf: 0.938)\n", "Match 1579: <PERSON><PERSON> [ 436095.94577778 5012215.94711111] <-> IFC [ 436092.16695688 5012219.69142313] (dist: 5.32m, conf: 0.645)\n", "Match 1580: <PERSON><PERSON> [ 435346.72091176 5011755.51217647] <-> IFC [ 435344.16695688 5011755.69142313] (dist: 2.56m, conf: 0.829)\n", "Match 1581: <PERSON><PERSON> [ 436359.03853571 5012224.61410714] <-> IFC [ 436358.16695688 5012223.69142313] (dist: 1.27m, conf: 0.915)\n", "Match 1582: <PERSON><PERSON> [ 436185.374 5011174.788] <-> IFC [ 436180.16695688 5011172.69142313] (dist: 5.61m, conf: 0.626)\n", "Match 1583: <PERSON><PERSON> [ 435771.3825     5011988.27433333] <-> IFC [ 435770.16695688 5011987.69142313] (dist: 1.35m, conf: 0.910)\n", "Match 1584: <PERSON><PERSON> [ 436462.73588235 5012104.55176471] <-> IFC [ 436462.16695688 5012107.69142313] (dist: 3.19m, conf: 0.787)\n", "Match 1585: <PERSON><PERSON> [ 435826.79073333 5012104.589     ] <-> IFC [ 435828.16695688 5012103.69142313] (dist: 1.64m, conf: 0.890)\n", "Match 1586: <PERSON><PERSON> [ 436264.68308333 5012311.06191667] <-> IFC [ 436262.16695688 5012309.69142313] (dist: 2.87m, conf: 0.809)\n", "Match 1587: <PERSON><PERSON> [ 436415.76033333 5012180.80204167] <-> IFC [ 436414.16695688 5012185.69142313] (dist: 5.14m, conf: 0.657)\n", "Match 1588: <PERSON><PERSON> [ 436567.34628571 5012003.57585714] <-> IFC [ 436566.16695688 5012001.69142313] (dist: 2.22m, conf: 0.852)\n", "Match 1589: <PERSON><PERSON> [ 435919.03675    5011447.43466667] <-> IFC [ 435922.16695688 5011443.69142313] (dist: 4.88m, conf: 0.675)\n", "Match 1590: <PERSON><PERSON> [ 436672.8303 5011811.3561] <-> IFC [ 436672.16695688 5011807.69142313] (dist: 3.72m, conf: 0.752)\n", "Match 1591: <PERSON><PERSON> [ 436179.43627273 5012359.54718182] <-> IFC [ 436178.16695688 5012361.69142313] (dist: 2.49m, conf: 0.834)\n", "Match 1592: <PERSON><PERSON> [ 436152.49154545 5012285.10490909] <-> IFC [ 436148.16695688 5012285.69142313] (dist: 4.36m, conf: 0.709)\n", "Match 1593: <PERSON><PERSON> [ 436012.2076 5012265.3454] <-> IFC [ 436016.16695688 5012269.69142313] (dist: 5.88m, conf: 0.608)\n", "Match 1594: <PERSON><PERSON> [ 436436.73985714 5011963.60314286] <-> IFC [ 436436.16695688 5011963.69142313] (dist: 0.58m, conf: 0.961)\n", "Match 1595: <PERSON><PERSON> [ 436369.24085714 5012137.05371428] <-> IFC [ 436368.16695688 5012135.69142313] (dist: 1.73m, conf: 0.884)\n", "Match 1596: <PERSON><PERSON> [ 436268.32016667 5011417.712     ] <-> IFC [ 436264.16695688 5011417.69142313] (dist: 4.15m, conf: 0.723)\n", "Match 1597: <PERSON><PERSON> [ 435928.14944444 5012277.91677778] <-> IFC [ 435930.16695688 5012279.69142313] (dist: 2.69m, conf: 0.821)\n", "Match 1598: <PERSON><PERSON> [ 435846.4676 5011540.8968] <-> IFC [ 435856.16695688 5011539.69142313] (dist: 9.77m, conf: 0.348)\n", "Match 1599: <PERSON><PERSON> [ 436199.27463636 5012330.02390909] <-> IFC [ 436196.16695688 5012329.69142313] (dist: 3.13m, conf: 0.792)\n", "Match 1600: <PERSON><PERSON> [ 435926.71285714 5012300.00214286] <-> IFC [ 435930.16695688 5012297.69142313] (dist: 4.16m, conf: 0.723)\n", "Match 1601: <PERSON><PERSON> [ 436170.47716667 5012300.02333333] <-> IFC [ 436168.16695688 5012295.69142313] (dist: 4.91m, conf: 0.673)\n", "Match 1602: <PERSON><PERSON> [ 435478.45133333 5011816.0448    ] <-> IFC [ 435476.16695688 5011816.69142313] (dist: 2.37m, conf: 0.842)\n", "Match 1603: <PERSON><PERSON> [ 435422.676 5012007.944] <-> IFC [ 435420.16695688 5012007.69142313] (dist: 2.52m, conf: 0.832)\n", "Match 1604: <PERSON><PERSON> [ 436143.901125 5012214.173125] <-> IFC [ 436140.16695688 5012213.69142313] (dist: 3.77m, conf: 0.749)\n", "Match 1605: <PERSON><PERSON> [ 436022.03066667 5012119.463     ] <-> IFC [ 436018.16695688 5012117.69142313] (dist: 4.25m, conf: 0.717)\n", "Match 1606: <PERSON><PERSON> [ 435779.9349 5011318.4241] <-> IFC [ 435780.16695688 5011317.69142313] (dist: 0.77m, conf: 0.949)\n", "Match 1607: <PERSON><PERSON> [ 436669.85933333 5011835.39966667] <-> IFC [ 436672.16695688 5011832.69142313] (dist: 3.56m, conf: 0.763)\n", "Match 1608: <PERSON><PERSON> [ 435440.22704 5011660.88192] <-> IFC [ 435438.16695688 5011661.69142313] (dist: 2.21m, conf: 0.852)\n", "Match 1609: <PERSON><PERSON> [ 436180.69513333 5012207.65373333] <-> IFC [ 436178.16695688 5012208.69142313] (dist: 2.73m, conf: 0.818)\n", "Match 1610: <PERSON><PERSON> [ 435554.59595 5011486.90105] <-> IFC [ 435552.16695688 5011487.69142313] (dist: 2.55m, conf: 0.830)\n", "Match 1611: <PERSON><PERSON> [ 436315.439      5011162.42266667] <-> IFC [ 436312.16695688 5011161.69142313] (dist: 3.35m, conf: 0.776)\n", "Match 1612: <PERSON><PERSON> [ 435583.16771429 5011535.34742857] <-> IFC [ 435580.16695688 5011535.69142313] (dist: 3.02m, conf: 0.799)\n", "Match 1613: <PERSON><PERSON> [ 435636.62233333 5011987.2405    ] <-> IFC [ 435638.16695688 5011987.69142313] (dist: 1.61m, conf: 0.893)\n", "Match 1614: <PERSON><PERSON> [ 436071.4512 5010938.0664] <-> IFC [ 436066.16695688 5010941.69142313] (dist: 6.41m, conf: 0.573)\n", "Match 1615: <PERSON><PERSON> [ 436158.932      5011640.92471428] <-> IFC [ 436154.16695688 5011639.69142313] (dist: 4.92m, conf: 0.672)\n", "Match 1616: <PERSON><PERSON> [ 435909.36633333 5012276.72588889] <-> IFC [ 435912.16695688 5012277.69142313] (dist: 2.96m, conf: 0.803)\n", "Match 1617: <PERSON><PERSON> [ 436292.88572727 5012336.39563636] <-> IFC [ 436292.16695688 5012331.69142313] (dist: 4.76m, conf: 0.683)\n", "Match 1618: <PERSON><PERSON> [ 436031.502      5012059.85614286] <-> IFC [ 436027.16695688 5012057.69142313] (dist: 4.85m, conf: 0.677)\n", "Match 1619: <PERSON><PERSON> [ 435479.05855556 5011576.35188889] <-> IFC [ 435476.16695688 5011579.69142313] (dist: 4.42m, conf: 0.706)\n", "Match 1620: <PERSON><PERSON> [ 435995.517625 5011851.94875 ] <-> IFC [ 435989.16695688 5011849.69142313] (dist: 6.74m, conf: 0.551)\n", "Match 1621: <PERSON><PERSON> [ 436180.62541176 5012281.40735294] <-> IFC [ 436178.16695688 5012285.69142313] (dist: 4.94m, conf: 0.671)\n", "Match 1622: <PERSON><PERSON> [ 435563.34146667 5011569.34806667] <-> IFC [ 435562.16695688 5011567.69142313] (dist: 2.03m, conf: 0.865)\n", "Match 1623: <PERSON><PERSON> [ 436390.8303125 5011124.170125 ] <-> IFC [ 436388.16695688 5011127.69142313] (dist: 4.42m, conf: 0.706)\n", "Match 1624: <PERSON><PERSON> [ 436547.93391667 5011903.06933333] <-> IFC [ 436548.16695688 5011903.69142313] (dist: 0.66m, conf: 0.956)\n", "Match 1625: <PERSON><PERSON> [ 436052.2623 5011070.0493] <-> IFC [ 436056.16695688 5011067.69142313] (dist: 4.56m, conf: 0.696)\n", "Match 1626: <PERSON><PERSON> [ 436143.997      5012289.51354545] <-> IFC [ 436140.16695688 5012288.69142313] (dist: 3.92m, conf: 0.739)\n", "Match 1627: <PERSON><PERSON> [ 435845.0259375 5011396.6948125] <-> IFC [ 435846.16695688 5011393.69142313] (dist: 3.21m, conf: 0.786)\n", "Match 1628: <PERSON><PERSON> [ 436434.187875 5012185.6385  ] <-> IFC [ 436434.16695688 5012185.69142313] (dist: 0.06m, conf: 0.996)\n", "Match 1629: <PERSON><PERSON> [ 436144.38066667 5012202.3516    ] <-> IFC [ 436148.16695688 5012203.69142313] (dist: 4.02m, conf: 0.732)\n", "Match 1630: <PERSON><PERSON> [ 436265.46125    5012330.01928571] <-> IFC [ 436262.16695688 5012333.69142313] (dist: 4.93m, conf: 0.671)\n", "Match 1631: <PERSON><PERSON> [ 436292.39438095 5012374.58814286] <-> IFC [ 436292.16695688 5012373.69142313] (dist: 0.93m, conf: 0.938)\n", "Match 1632: <PERSON><PERSON> [ 436483.6945 5012045.3125] <-> IFC [ 436482.16695688 5012041.69142313] (dist: 3.93m, conf: 0.738)\n", "Match 1633: <PERSON><PERSON> [ 435965.78357895 5012049.17563158] <-> IFC [ 435970.16695688 5012045.69142313] (dist: 5.60m, conf: 0.627)\n", "Match 1634: <PERSON><PERSON> [ 435685.22633333 5011399.90366667] <-> IFC [ 435685.39772611 5011408.4606539 ] (dist: 8.56m, conf: 0.429)\n", "Match 1635: <PERSON><PERSON> [ 436369.47447368 5012125.36615789] <-> IFC [ 436368.16695688 5012127.69142313] (dist: 2.67m, conf: 0.822)\n", "Match 1636: <PERSON><PERSON> [ 436425.4949 5012097.8376] <-> IFC [ 436424.16695688 5012095.69142313] (dist: 2.52m, conf: 0.832)\n", "Match 1637: <PERSON><PERSON> [ 435918.96233333 5012035.273     ] <-> IFC [ 435922.16695688 5012037.69142313] (dist: 4.01m, conf: 0.732)\n", "Match 1638: <PERSON><PERSON> [ 436105.53       5012400.68577778] <-> IFC [ 436102.16695688 5012401.69142313] (dist: 3.51m, conf: 0.766)\n", "Match 1639: <PERSON><PERSON> [ 435740.7138 5012010.8326] <-> IFC [ 435742.16695688 5012007.69142313] (dist: 3.46m, conf: 0.769)\n", "Match 1640: <PERSON><PERSON> [ 436198.95315385 5012435.57607692] <-> IFC [ 436196.16695688 5012431.69142313] (dist: 4.78m, conf: 0.681)\n", "Match 1641: <PERSON><PERSON> [ 436029.9035 5012245.4615] <-> IFC [ 436034.16695688 5012245.69142313] (dist: 4.27m, conf: 0.715)\n", "Match 1642: <PERSON><PERSON> [ 436245.59877778 5012176.77044444] <-> IFC [ 436244.16695688 5012178.69142313] (dist: 2.40m, conf: 0.840)\n", "Match 1643: <PERSON><PERSON> [ 436127.8939 5010995.8703] <-> IFC [ 436132.16695688 5010997.69142313] (dist: 4.64m, conf: 0.690)\n", "Match 1644: <PERSON><PERSON> [ 435928.367   5011632.16025] <-> IFC [ 435932.16695688 5011629.69142313] (dist: 4.53m, conf: 0.698)\n", "Match 1645: <PERSON><PERSON> [ 436264.9411 5012388.7937] <-> IFC [ 436262.16695688 5012385.69142313] (dist: 4.16m, conf: 0.723)\n", "Match 1646: <PERSON><PERSON> [ 436247.9342 5012186.6824] <-> IFC [ 436244.16695688 5012187.69142313] (dist: 3.90m, conf: 0.740)\n", "Match 1647: <PERSON><PERSON> [ 435938.45183333 5012096.5515    ] <-> IFC [ 435932.16695688 5012099.69142313] (dist: 7.03m, conf: 0.532)\n", "Match 1648: <PERSON><PERSON> [ 435843.10714286 5011681.25157143] <-> IFC [ 435846.16695688 5011683.69142313] (dist: 3.91m, conf: 0.739)\n", "Match 1649: <PERSON><PERSON> [ 436058.37341667 5012312.76466667] <-> IFC [ 436054.16695688 5012315.69142313] (dist: 5.12m, conf: 0.658)\n", "Match 1650: <PERSON><PERSON> [ 435741.2795 5011289.8744] <-> IFC [ 435742.16695688 5011291.69142313] (dist: 2.02m, conf: 0.865)\n", "Match 1651: <PERSON><PERSON> [ 436672.766 5011849.329] <-> IFC [ 436672.16695688 5011849.69142313] (dist: 0.70m, conf: 0.953)\n", "Match 1652: <PERSON><PERSON> [ 435657.5393871  5011425.03696774] <-> IFC [ 435656.16695688 5011417.69142313] (dist: 7.47m, conf: 0.502)\n", "Match 1653: <PERSON><PERSON> [ 436292.9323 5012179.9669] <-> IFC [ 436292.16695688 5012179.69142313] (dist: 0.81m, conf: 0.946)\n", "Match 1654: <PERSON><PERSON> [ 436321.67142857 5012138.68542857] <-> IFC [ 436320.16695688 5012135.69142313] (dist: 3.35m, conf: 0.777)\n", "Match 1655: <PERSON><PERSON> [ 436493.37766667 5011881.56716667] <-> IFC [ 436492.16695688 5011883.69142313] (dist: 2.45m, conf: 0.837)\n", "Match 1656: <PERSON><PERSON> [ 435824.6193 5011642.142 ] <-> IFC [ 435828.16695688 5011639.69142313] (dist: 4.31m, conf: 0.713)\n", "Match 1657: <PERSON><PERSON> [ 436348.91714286 5012190.299     ] <-> IFC [ 436348.16695688 5012193.69142313] (dist: 3.47m, conf: 0.768)\n", "Match 1658: <PERSON><PERSON> [ 436179.6505     5012404.31716667] <-> IFC [ 436178.16695688 5012403.69142313] (dist: 1.61m, conf: 0.893)\n", "Match 1659: <PERSON><PERSON> [ 436217.54783333 5012344.96472222] <-> IFC [ 436216.16695688 5012343.69142313] (dist: 1.88m, conf: 0.875)\n", "Match 1660: <PERSON><PERSON> [ 436615.76309091 5011909.75445454] <-> IFC [ 436616.16695688 5011913.69142313] (dist: 3.96m, conf: 0.736)\n", "Match 1661: <PERSON><PERSON> [ 436052.222    5011075.435125] <-> IFC [ 436056.16695688 5011075.69142313] (dist: 3.95m, conf: 0.736)\n", "Match 1662: <PERSON><PERSON> [ 436217.83173684 5012387.25168421] <-> IFC [ 436216.16695688 5012385.69142313] (dist: 2.28m, conf: 0.848)\n", "Match 1663: <PERSON><PERSON> [ 435844.429      5012050.16008696] <-> IFC [ 435846.16695688 5012045.69142313] (dist: 4.79m, conf: 0.680)\n", "Match 1664: <PERSON><PERSON> [ 436144.24607143 5012362.355     ] <-> IFC [ 436148.16695688 5012361.69142313] (dist: 3.98m, conf: 0.735)\n", "Match 1665: <PERSON><PERSON> [ 435918.46122222 5012248.878     ] <-> IFC [ 435920.16695688 5012249.69142313] (dist: 1.89m, conf: 0.874)\n", "Match 1666: <PERSON><PERSON> [ 436341.19036364 5012220.71254545] <-> IFC [ 436338.16695688 5012221.69142313] (dist: 3.18m, conf: 0.788)\n", "Match 1667: <PERSON><PERSON> [ 436184.43091304 5010967.05069565] <-> IFC [ 436188.16695688 5010963.69142313] (dist: 5.02m, conf: 0.665)\n", "Match 1668: <PERSON><PERSON> [ 436353.32172727 5011228.23645454] <-> IFC [ 436350.16695688 5011231.69142313] (dist: 4.68m, conf: 0.688)\n", "Match 1669: <PERSON><PERSON> [ 435768.49406667 5011972.75713333] <-> IFC [ 435770.16695688 5011969.69142313] (dist: 3.49m, conf: 0.767)\n", "Match 1670: <PERSON><PERSON> [ 436002.92642857 5012005.84      ] <-> IFC [ 436008.16695688 5012006.69142313] (dist: 5.31m, conf: 0.646)\n", "Match 1671: <PERSON><PERSON> [ 436296.68463636 5011170.55854545] <-> IFC [ 436294.16695688 5011170.69142313] (dist: 2.52m, conf: 0.832)\n", "Match 1672: <PERSON><PERSON> [ 436256.2552 5012334.5858] <-> IFC [ 436254.16695688 5012337.69142313] (dist: 3.74m, conf: 0.751)\n", "Match 1673: <PERSON><PERSON> [ 436312.33396296 5012249.61837037] <-> IFC [ 436310.16695688 5012249.69142313] (dist: 2.17m, conf: 0.855)\n", "Match 1674: <PERSON><PERSON> [ 436350.25577778 5012128.89444444] <-> IFC [ 436348.16695688 5012125.69142313] (dist: 3.82m, conf: 0.745)\n", "Match 1675: <PERSON><PERSON> [ 436295.4432 5011101.1432] <-> IFC [ 436293.50029021 5011098.3580898 ] (dist: 3.40m, conf: 0.774)\n", "Match 1676: <PERSON><PERSON> [ 435947.19383333 5012291.22633333] <-> IFC [ 435950.16695688 5012291.69142313] (dist: 3.01m, conf: 0.799)\n", "Match 1677: <PERSON><PERSON> [ 436088.755      5012074.82983333] <-> IFC [ 436084.16695688 5012075.69142313] (dist: 4.67m, conf: 0.689)\n", "Match 1678: <PERSON><PERSON> [ 436049.34341379 5012230.63196552] <-> IFC [ 436054.16695688 5012231.69142313] (dist: 4.94m, conf: 0.671)\n", "Match 1679: <PERSON><PERSON> [ 436188.62266667 5012380.67033333] <-> IFC [ 436186.16695688 5012383.69142313] (dist: 3.89m, conf: 0.740)\n", "Match 1680: <PERSON><PERSON> [ 436292.05717647 5011589.16408823] <-> IFC [ 436288.16695688 5011591.69142313] (dist: 4.64m, conf: 0.691)\n", "Match 1681: <PERSON><PERSON> [ 436594.562      5011811.79283333] <-> IFC [ 436596.16695688 5011809.69142313] (dist: 2.64m, conf: 0.824)\n", "Match 1682: <PERSON><PERSON> [ 435779.29011765 5011707.20917647] <-> IFC [ 435770.16695688 5011709.69142313] (dist: 9.45m, conf: 0.370)\n", "Match 1683: <PERSON><PERSON> [ 436434.56385714 5012178.78061905] <-> IFC [ 436434.16695688 5012177.69142313] (dist: 1.16m, conf: 0.923)\n", "Match 1684: <PERSON><PERSON> [ 436128.3598     5010936.50637143] <-> IFC [ 436132.16695688 5010939.69142313] (dist: 4.96m, conf: 0.669)\n", "Match 1685: <PERSON><PERSON> [ 436586.17533333 5011912.16155555] <-> IFC [ 436586.16695688 5011915.69142313] (dist: 3.53m, conf: 0.765)\n", "Match 1686: <PERSON><PERSON> [ 436577.47158333 5011866.02916667] <-> IFC [ 436578.16695688 5011867.69142313] (dist: 1.80m, conf: 0.880)\n", "Match 1687: <PERSON><PERSON> [ 435807.72417647 5011395.13435294] <-> IFC [ 435808.16695688 5011391.69142313] (dist: 3.47m, conf: 0.769)\n", "Match 1688: <PERSON><PERSON> [ 435919.459      5012028.23642105] <-> IFC [ 435922.16695688 5012029.69142313] (dist: 3.07m, conf: 0.795)\n", "Match 1689: <PERSON><PERSON> [ 436417.2195  5012222.04675] <-> IFC [ 436414.16695688 5012217.69142313] (dist: 5.32m, conf: 0.645)\n", "Match 1690: <PERSON><PERSON> [ 435638.82211111 5011415.85111111] <-> IFC [ 435638.16695688 5011415.69142313] (dist: 0.67m, conf: 0.955)\n", "Match 1691: <PERSON><PERSON> [ 435835.7885     5011453.89407143] <-> IFC [ 435838.16695688 5011453.69142313] (dist: 2.39m, conf: 0.841)\n", "Match 1692: <PERSON><PERSON> [ 436547.75316667 5011915.01316667] <-> IFC [ 436548.16695688 5011911.69142313] (dist: 3.35m, conf: 0.777)\n", "Match 1693: <PERSON><PERSON> [ 436322.3516875 5012115.75875  ] <-> IFC [ 436320.16695688 5012119.69142313] (dist: 4.50m, conf: 0.700)\n", "Match 1694: <PERSON><PERSON> [ 436386.28616667 5012160.88033333] <-> IFC [ 436386.16695688 5012161.69142313] (dist: 0.82m, conf: 0.945)\n", "Match 1695: <PERSON><PERSON> [ 435807.13341176 5011444.41652941] <-> IFC [ 435808.16695688 5011441.69142313] (dist: 2.91m, conf: 0.806)\n", "Match 1696: <PERSON><PERSON> [ 435676.1464 5011387.1284] <-> IFC [ 435676.16695688 5011383.69142313] (dist: 3.44m, conf: 0.771)\n", "Match 1697: <PERSON><PERSON> [ 435542.6508 5011576.5928] <-> IFC [ 435542.16695688 5011577.69142313] (dist: 1.20m, conf: 0.920)\n", "Match 1698: <PERSON><PERSON> [ 436213.641      5011168.41466667] <-> IFC [ 436218.16695688 5011169.69142313] (dist: 4.70m, conf: 0.686)\n", "Match 1699: <PERSON><PERSON> [ 436454.8805  5012085.04625] <-> IFC [ 436452.16695688 5012085.69142313] (dist: 2.79m, conf: 0.814)\n", "Match 1700: <PERSON><PERSON> [ 436312.29951724 5012227.41955172] <-> IFC [ 436310.16695688 5012223.69142313] (dist: 4.29m, conf: 0.714)\n", "Match 1701: <PERSON><PERSON> [ 435928.1469 5012270.7271] <-> IFC [ 435930.16695688 5012271.69142313] (dist: 2.24m, conf: 0.851)\n", "Match 1702: <PERSON><PERSON> [ 435816.36271429 5011392.222     ] <-> IFC [ 435818.16695688 5011389.69142313] (dist: 3.11m, conf: 0.793)\n", "Match 1703: <PERSON><PERSON> [ 436171.6976 5012305.6698] <-> IFC [ 436168.16695688 5012304.69142313] (dist: 3.66m, conf: 0.756)\n", "Match 1704: <PERSON><PERSON> [ 436635.20275 5011899.3645 ] <-> IFC [ 436634.16695688 5011897.69142313] (dist: 1.97m, conf: 0.869)\n", "Match 1705: <PERSON><PERSON> [ 436028.562  5012221.6565] <-> IFC [ 436026.16695688 5012223.69142313] (dist: 3.14m, conf: 0.790)\n", "Match 1706: <PERSON><PERSON> [ 436382.36227273 5011135.98290909] <-> IFC [ 436378.16695688 5011133.69142313] (dist: 4.78m, conf: 0.681)\n", "Match 1707: <PERSON><PERSON> [ 436575.68883333 5012006.90566667] <-> IFC [ 436576.16695688 5012005.69142313] (dist: 1.30m, conf: 0.913)\n", "Match 1708: <PERSON><PERSON> [ 435817.719375 5011371.34    ] <-> IFC [ 435818.16695688 5011371.69142313] (dist: 0.57m, conf: 0.962)\n", "Match 1709: <PERSON><PERSON> [ 436020.25164706 5012222.56911765] <-> IFC [ 436016.16695688 5012218.69142313] (dist: 5.63m, conf: 0.625)\n", "Match 1710: <PERSON><PERSON> [ 435423.4178 5011667.3358] <-> IFC [ 435420.16695688 5011667.69142313] (dist: 3.27m, conf: 0.782)\n", "Match 1711: <PERSON><PERSON> [ 435909.34388889 5012003.99172222] <-> IFC [ 435904.16695688 5012005.69142313] (dist: 5.45m, conf: 0.637)\n", "Match 1712: <PERSON><PERSON> [ 436265.39666667 5012263.64466667] <-> IFC [ 436262.16695688 5012267.69142313] (dist: 5.18m, conf: 0.655)\n", "Match 1713: <PERSON><PERSON> [ 436227.36925    5012225.38083333] <-> IFC [ 436224.16695688 5012221.69142313] (dist: 4.89m, conf: 0.674)\n", "Match 1714: <PERSON><PERSON> [ 436701.121      5011831.71033333] <-> IFC [ 436700.16695688 5011831.69142313] (dist: 0.95m, conf: 0.936)\n", "Match 1715: <PERSON><PERSON> [ 436284.70309091 5012185.43718182] <-> IFC [ 436282.16695688 5012183.69142313] (dist: 3.08m, conf: 0.795)\n", "Match 1716: <PERSON><PERSON> [ 436293.701625 5012183.794125] <-> IFC [ 436292.16695688 5012179.69142313] (dist: 4.38m, conf: 0.708)\n", "Match 1717: <PERSON><PERSON> [ 436447.42023077 5011197.60661539] <-> IFC [ 436446.16695688 5011197.69142313] (dist: 1.26m, conf: 0.916)\n", "Match 1718: <PERSON><PERSON> [ 435991.9365 5012209.427 ] <-> IFC [ 435988.16695688 5012211.69142313] (dist: 4.40m, conf: 0.707)\n", "Match 1719: <PERSON><PERSON> [ 436633.421125 5011804.84625 ] <-> IFC [ 436634.16695688 5011803.69142313] (dist: 1.37m, conf: 0.908)\n", "Match 1720: <PERSON><PERSON> [ 435768.57728571 5011646.44271429] <-> IFC [ 435770.16695688 5011649.69142313] (dist: 3.62m, conf: 0.759)\n", "Match 1721: <PERSON><PERSON> [ 436436.01585714 5012141.24385714] <-> IFC [ 436434.16695688 5012143.69142313] (dist: 3.07m, conf: 0.796)\n", "Match 1722: <PERSON><PERSON> [ 436052.3634 5011953.1768] <-> IFC [ 436056.16695688 5011953.69142313] (dist: 3.84m, conf: 0.744)\n", "Match 1723: <PERSON><PERSON> [ 436218.26425 5012363.8695 ] <-> IFC [ 436216.16695688 5012361.69142313] (dist: 3.02m, conf: 0.798)\n", "Match 1724: <PERSON><PERSON> [ 436349.5519 5012124.6308] <-> IFC [ 436348.16695688 5012125.69142313] (dist: 1.74m, conf: 0.884)\n", "Match 1725: <PERSON><PERSON> [ 436368.72693333 5012154.2548    ] <-> IFC [ 436368.16695688 5012151.69142313] (dist: 2.62m, conf: 0.825)\n", "Match 1726: <PERSON><PERSON> [ 436349.75366667 5012115.6398    ] <-> IFC [ 436348.16695688 5012117.69142313] (dist: 2.59m, conf: 0.827)\n", "Match 1727: <PERSON><PERSON> [ 436099.14544444 5011576.60522222] <-> IFC [ 436094.16695688 5011575.69142313] (dist: 5.06m, conf: 0.663)\n", "Match 1728: <PERSON><PERSON> [ 436208.34557143 5012167.77714286] <-> IFC [ 436206.16695688 5012171.69142313] (dist: 4.48m, conf: 0.701)\n", "Match 1729: <PERSON><PERSON> [ 436203.72446667 5010987.19153333] <-> IFC [ 436198.16695688 5010989.69142313] (dist: 6.09m, conf: 0.594)\n", "Match 1730: <PERSON><PERSON> [ 436174.61640541 5010961.22997297] <-> IFC [ 436170.16695688 5010965.69142313] (dist: 6.30m, conf: 0.580)\n", "Match 1731: <PERSON><PERSON> [ 436321.88333333 5012231.66955556] <-> IFC [ 436320.16695688 5012229.69142313] (dist: 2.62m, conf: 0.825)\n", "Match 1732: <PERSON><PERSON> [ 436566.027875 5011993.251625] <-> IFC [ 436566.16695688 5011993.69142313] (dist: 0.46m, conf: 0.969)\n", "Match 1733: <PERSON><PERSON> [ 435972.46971429 5012291.25064286] <-> IFC [ 435968.16695688 5012293.69142313] (dist: 4.95m, conf: 0.670)\n", "Match 1734: <PERSON><PERSON> [ 436223.06242857 5011247.51557143] <-> IFC [ 436226.16695688 5011245.69142313] (dist: 3.60m, conf: 0.760)\n", "Match 1735: <PERSON><PERSON> [ 436136.92066667 5010989.89533333] <-> IFC [ 436140.16695688 5010991.69142313] (dist: 3.71m, conf: 0.753)\n", "Match 1736: <PERSON><PERSON> [ 436330.25776923 5012187.912     ] <-> IFC [ 436330.16695688 5012191.69142313] (dist: 3.78m, conf: 0.748)\n", "Match 1737: <PERSON><PERSON> [ 435825.93542857 5011462.245     ] <-> IFC [ 435828.16695688 5011463.69142313] (dist: 2.66m, conf: 0.823)\n", "Match 1738: <PERSON><PERSON> [ 436577.93415152 5011850.08048485] <-> IFC [ 436578.16695688 5011851.69142313] (dist: 1.63m, conf: 0.891)\n", "Match 1739: <PERSON><PERSON> [ 435609.5697 5011834.9462] <-> IFC [ 435610.16695688 5011831.69142313] (dist: 3.31m, conf: 0.779)\n", "Match 1740: <PERSON><PERSON> [ 435393.64514286 5011760.64371428] <-> IFC [ 435390.16695688 5011757.69142313] (dist: 4.56m, conf: 0.696)\n", "Match 1741: <PERSON><PERSON> [ 436190.35791667 5012399.7405    ] <-> IFC [ 436186.16695688 5012399.69142313] (dist: 4.19m, conf: 0.721)\n", "Match 1742: <PERSON><PERSON> [ 436004.98966667 5012042.26655556] <-> IFC [ 436008.16695688 5012042.69142313] (dist: 3.21m, conf: 0.786)\n", "Match 1743: <PERSON><PERSON> [ 435958.19225  5012061.166875] <-> IFC [ 435960.16695688 5012057.69142313] (dist: 4.00m, conf: 0.734)\n", "Match 1744: <PERSON><PERSON> [ 436256.83218182 5012206.913     ] <-> IFC [ 436254.16695688 5012203.69142313] (dist: 4.18m, conf: 0.721)\n", "Match 1745: <PERSON><PERSON> [ 436129.4486 5011049.3148] <-> IFC [ 436122.16695688 5011045.69142313] (dist: 8.13m, conf: 0.458)\n", "Match 1746: <PERSON><PERSON> [ 436547.58913333 5012004.33726667] <-> IFC [ 436548.16695688 5012007.69142313] (dist: 3.40m, conf: 0.773)\n", "Match 1747: <PERSON><PERSON> [ 435721.97566667 5011434.19316667] <-> IFC [ 435724.16695688 5011433.69142313] (dist: 2.25m, conf: 0.850)\n", "Match 1748: <PERSON><PERSON> [ 436185.53457143 5011002.81957143] <-> IFC [ 436188.16695688 5010997.69142313] (dist: 5.76m, conf: 0.616)\n", "Match 1749: <PERSON><PERSON> [ 435975.4895     5012029.41977778] <-> IFC [ 435980.16695688 5012027.69142313] (dist: 4.99m, conf: 0.668)\n", "Match 1750: <PERSON><PERSON> [ 435760.05780952 5011403.94238095] <-> IFC [ 435760.16695688 5011403.69142313] (dist: 0.27m, conf: 0.982)\n", "Match 1751: <PERSON><PERSON> [ 435957.35357143 5011595.85192857] <-> IFC [ 435960.16695688 5011597.69142313] (dist: 3.36m, conf: 0.776)\n", "Match 1752: <PERSON><PERSON> [ 436268.75844444 5011242.51811111] <-> IFC [ 436264.16695688 5011243.69142313] (dist: 4.74m, conf: 0.684)\n", "Match 1753: <PERSON><PERSON> [ 436651.4592 5011865.1892] <-> IFC [ 436644.16695688 5011867.69142313] (dist: 7.71m, conf: 0.486)\n", "Match 1754: <PERSON><PERSON> [ 436197.829      5011669.30685714] <-> IFC [ 436202.16695688 5011667.69142313] (dist: 4.63m, conf: 0.691)\n", "Match 1755: <PERSON><PERSON> [ 436319.4161875 5011640.0658125] <-> IFC [ 436316.16695688 5011643.69142313] (dist: 4.87m, conf: 0.675)\n", "Match 1756: <PERSON><PERSON> [ 436340.278      5012146.38244445] <-> IFC [ 436338.16695688 5012145.69142313] (dist: 2.22m, conf: 0.852)\n", "Match 1757: <PERSON><PERSON> [ 435477.09283333 5011659.01333333] <-> IFC [ 435476.16695688 5011655.69142313] (dist: 3.45m, conf: 0.770)\n", "Match 1758: <PERSON><PERSON> [ 436162.87188889 5012197.24888889] <-> IFC [ 436158.16695688 5012193.69142313] (dist: 5.90m, conf: 0.607)\n", "Match 1759: <PERSON><PERSON> [ 435410.6564 5011718.1604] <-> IFC [ 435410.16695688 5011717.69142313] (dist: 0.68m, conf: 0.955)\n", "Match 1760: <PERSON><PERSON> [ 435958.258625 5012139.176875] <-> IFC [ 435960.16695688 5012139.69142313] (dist: 1.98m, conf: 0.868)\n", "Match 1761: <PERSON><PERSON> [ 435846.21242857 5011534.01757143] <-> IFC [ 435837.16695688 5011533.69142313] (dist: 9.05m, conf: 0.397)\n", "Match 1762: <PERSON><PERSON> [ 436115.354625 5012165.565125] <-> IFC [ 436120.16695688 5012163.69142313] (dist: 5.16m, conf: 0.656)\n", "Match 1763: <PERSON><PERSON> [ 436255.65125   5012225.1211875] <-> IFC [ 436254.16695688 5012220.69142313] (dist: 4.67m, conf: 0.689)\n", "Match 1764: <PERSON><PERSON> [ 436226.64186667 5012415.15      ] <-> IFC [ 436224.16695688 5012417.69142313] (dist: 3.55m, conf: 0.764)\n", "Match 1765: <PERSON><PERSON> [ 436364.316625 5011799.755625] <-> IFC [ 436364.16695688 5011799.69142313] (dist: 0.16m, conf: 0.989)\n", "Match 1766: <PERSON><PERSON> [ 436680.02163636 5011818.73027273] <-> IFC [ 436682.16695688 5011822.69142313] (dist: 4.50m, conf: 0.700)\n", "Match 1767: <PERSON><PERSON> [ 436218.66172727 5012167.74331818] <-> IFC [ 436216.16695688 5012171.69142313] (dist: 4.67m, conf: 0.689)\n", "Match 1768: <PERSON><PERSON> [ 436311.65825  5012115.826875] <-> IFC [ 436310.16695688 5012113.69142313] (dist: 2.60m, conf: 0.826)\n", "Match 1769: <PERSON><PERSON> [ 435394.551    5011768.560875] <-> IFC [ 435400.16695688 5011771.69142313] (dist: 6.43m, conf: 0.571)\n", "Match 1770: <PERSON><PERSON> [ 436197.52757143 5012359.88985714] <-> IFC [ 436196.16695688 5012363.69142313] (dist: 4.04m, conf: 0.731)\n", "Match 1771: <PERSON><PERSON> [ 436548.504      5011866.08313333] <-> IFC [ 436548.16695688 5011869.69142313] (dist: 3.62m, conf: 0.758)\n", "Match 1772: <PERSON><PERSON> [ 435619.33555556 5011754.90488889] <-> IFC [ 435618.16695688 5011751.69142313] (dist: 3.42m, conf: 0.772)\n", "Match 1773: <PERSON><PERSON> [ 436284.08847368 5012261.01278947] <-> IFC [ 436282.16695688 5012259.69142313] (dist: 2.33m, conf: 0.845)\n", "Match 1774: <PERSON><PERSON> [ 436509.108125 5011784.760375] <-> IFC [ 436506.16695688 5011783.69142313] (dist: 3.13m, conf: 0.791)\n", "Match 1775: <PERSON><PERSON> [ 436336.89207692 5011657.339     ] <-> IFC [ 436334.16695688 5011653.69142313] (dist: 4.55m, conf: 0.696)\n", "Match 1776: <PERSON><PERSON> [ 436651.4504 5011846.7888] <-> IFC [ 436654.16695688 5011847.69142313] (dist: 2.86m, conf: 0.809)\n", "Match 1777: <PERSON><PERSON> [ 436034.1822 5012039.8318] <-> IFC [ 436036.16695688 5012039.69142313] (dist: 1.99m, conf: 0.867)\n", "Match 1778: <PERSON><PERSON> [ 436184.93012 5010987.04908] <-> IFC [ 436188.16695688 5010989.69142313] (dist: 4.18m, conf: 0.721)\n", "Match 1779: <PERSON><PERSON> [ 435326.84035 5011824.75135] <-> IFC [ 435324.16695688 5011826.69142313] (dist: 3.30m, conf: 0.780)\n", "Match 1780: <PERSON><PERSON> [ 435601.74854545 5011457.06072727] <-> IFC [ 435600.16695688 5011455.69142313] (dist: 2.09m, conf: 0.861)\n", "Match 1781: <PERSON><PERSON> [ 436188.92685714 5012394.50042857] <-> IFC [ 436186.16695688 5012391.69142313] (dist: 3.94m, conf: 0.737)\n", "Match 1782: <PERSON><PERSON> [ 436104.616125 5012184.124625] <-> IFC [ 436102.16695688 5012183.69142313] (dist: 2.49m, conf: 0.834)\n", "Match 1783: <PERSON><PERSON> [ 435460.56766667 5011680.4682    ] <-> IFC [ 435458.16695688 5011679.69142313] (dist: 2.52m, conf: 0.832)\n", "Match 1784: <PERSON><PERSON> [ 436125.28113333 5012193.11433333] <-> IFC [ 436120.16695688 5012189.69142313] (dist: 6.15m, conf: 0.590)\n", "Match 1785: <PERSON><PERSON> [ 435966.86825 5011685.11275] <-> IFC [ 435970.16695688 5011685.69142313] (dist: 3.35m, conf: 0.777)\n", "Match 1786: <PERSON><PERSON> [ 435572.17976923 5011651.76053846] <-> IFC [ 435572.16695688 5011651.69142313] (dist: 0.07m, conf: 0.995)\n", "Match 1787: <PERSON><PERSON> [ 435816.87658824 5011381.45488235] <-> IFC [ 435818.16695688 5011379.69142313] (dist: 2.19m, conf: 0.854)\n", "Match 1788: <PERSON><PERSON> [ 436340.332      5012322.63384615] <-> IFC [ 436338.16695688 5012323.69142313] (dist: 2.41m, conf: 0.839)\n", "Match 1789: <PERSON><PERSON> [ 436293.76455882 5012230.83808823] <-> IFC [ 436292.16695688 5012231.69142313] (dist: 1.81m, conf: 0.879)\n", "Match 1790: <PERSON><PERSON> [ 436237.94516667 5012155.70508333] <-> IFC [ 436234.16695688 5012153.69142313] (dist: 4.28m, conf: 0.715)\n", "Match 1791: <PERSON><PERSON> [ 436266.18883333 5012111.01788889] <-> IFC [ 436262.16695688 5012114.69142313] (dist: 5.45m, conf: 0.637)\n", "Match 1792: <PERSON><PERSON> [ 436133.00594444 5012251.78033333] <-> IFC [ 436130.16695688 5012249.69142313] (dist: 3.52m, conf: 0.765)\n", "Match 1793: <PERSON><PERSON> [ 436030.16971429 5012343.16428571] <-> IFC [ 436026.16695688 5012341.69142313] (dist: 4.27m, conf: 0.716)\n", "Match 1794: <PERSON><PERSON> [ 435900.71845455 5011989.17581818] <-> IFC [ 435904.16695688 5011987.69142313] (dist: 3.75m, conf: 0.750)\n", "Match 1795: <PERSON><PERSON> [ 435957.51992857 5012069.38507143] <-> IFC [ 435960.16695688 5012065.69142313] (dist: 4.54m, conf: 0.697)\n", "Match 1796: <PERSON><PERSON> [ 436391.129      5011145.95730769] <-> IFC [ 436388.16695688 5011145.69142313] (dist: 2.97m, conf: 0.802)\n", "Match 1797: <PERSON><PERSON> [ 435478.79011111 5011633.714     ] <-> IFC [ 435476.16695688 5011629.69142313] (dist: 4.80m, conf: 0.680)\n", "Match 1798: <PERSON><PERSON> [ 435835.51615 5012053.0208 ] <-> IFC [ 435838.16695688 5012057.69142313] (dist: 5.37m, conf: 0.642)\n", "Match 1799: <PERSON><PERSON> [ 435806.98863636 5012115.98881818] <-> IFC [ 435808.16695688 5012119.69142313] (dist: 3.89m, conf: 0.741)\n", "Match 1800: <PERSON><PERSON> [ 436539.7116 5011810.1204] <-> IFC [ 436540.16695688 5011805.69142313] (dist: 4.45m, conf: 0.703)\n", "Match 1801: <PERSON><PERSON> [ 436152.19084615 5012189.65715385] <-> IFC [ 436148.16695688 5012186.69142313] (dist: 5.00m, conf: 0.667)\n", "Match 1802: <PERSON><PERSON> [ 435740.56928571 5011980.33657143] <-> IFC [ 435742.16695688 5011981.69142313] (dist: 2.09m, conf: 0.860)\n", "Match 1803: <PERSON><PERSON> [ 435790.779      5011221.66583333] <-> IFC [ 435790.16695688 5011223.69142313] (dist: 2.12m, conf: 0.859)\n", "Match 1804: <PERSON><PERSON> [ 435947.14588889 5011588.08933333] <-> IFC [ 435942.16695688 5011589.69142313] (dist: 5.23m, conf: 0.651)\n", "Match 1805: <PERSON><PERSON> [ 436595.742625 5011856.530625] <-> IFC [ 436596.16695688 5011860.69142313] (dist: 4.18m, conf: 0.721)\n", "Match 1806: <PERSON><PERSON> [ 436311.79557895 5012238.24489474] <-> IFC [ 436310.16695688 5012241.69142313] (dist: 3.81m, conf: 0.746)\n", "Match 1807: <PERSON><PERSON> [ 436509.25863636 5012108.91181818] <-> IFC [ 436510.16695688 5012107.69142313] (dist: 1.52m, conf: 0.899)\n", "Match 1808: <PERSON><PERSON> [ 435797.82644444 5011469.21422222] <-> IFC [ 435799.16695688 5011471.69142313] (dist: 2.82m, conf: 0.812)\n", "Match 1809: <PERSON><PERSON> [ 435620.4085 5011473.6415] <-> IFC [ 435618.16695688 5011465.69142313] (dist: 8.26m, conf: 0.449)\n", "Match 1810: <PERSON><PERSON> [ 436605.53866667 5011909.7705    ] <-> IFC [ 436606.16695688 5011907.69142313] (dist: 2.17m, conf: 0.855)\n", "Match 1811: <PERSON><PERSON> [ 436567.39982759 5011912.80193103] <-> IFC [ 436568.16695688 5011913.69142313] (dist: 1.17m, conf: 0.922)\n", "Match 1812: <PERSON><PERSON> [ 435420.2234 5011783.7464] <-> IFC [ 435420.16695688 5011787.69142313] (dist: 3.95m, conf: 0.737)\n", "Match 1813: <PERSON><PERSON> [ 436256.77428571 5012203.05742857] <-> IFC [ 436254.16695688 5012203.69142313] (dist: 2.68m, conf: 0.821)\n", "Match 1814: <PERSON><PERSON> [ 436268.04511111 5011163.95566667] <-> IFC [ 436264.16695688 5011165.69142313] (dist: 4.25m, conf: 0.717)\n", "Match 1815: <PERSON><PERSON> [ 435525.86044444 5011545.81255556] <-> IFC [ 435524.16695688 5011543.69142313] (dist: 2.71m, conf: 0.819)\n", "Match 1816: <PERSON><PERSON> [ 436614.55575    5011833.98841667] <-> IFC [ 436616.16695688 5011837.69142313] (dist: 4.04m, conf: 0.731)\n", "Match 1817: <PERSON><PERSON> [ 436161.0245     5012379.82088889] <-> IFC [ 436158.16695688 5012383.69142313] (dist: 4.81m, conf: 0.679)\n", "Match 1818: <PERSON><PERSON> [ 436284.87971429 5012152.95685714] <-> IFC [ 436282.16695688 5012149.69142313] (dist: 4.25m, conf: 0.717)\n", "Match 1819: <PERSON><PERSON> [ 436029.1022 5012328.9274] <-> IFC [ 436026.16695688 5012325.69142313] (dist: 4.37m, conf: 0.709)\n", "Match 1820: <PERSON><PERSON> [ 435655.91857143 5011498.27928571] <-> IFC [ 435656.16695688 5011496.69142313] (dist: 1.61m, conf: 0.893)\n", "Match 1821: <PERSON><PERSON> [ 436231.1024 5011234.7506] <-> IFC [ 436226.16695688 5011237.69142313] (dist: 5.75m, conf: 0.617)\n", "Match 1822: <PERSON><PERSON> [ 435777.86046154 5011635.954     ] <-> IFC [ 435780.16695688 5011639.69142313] (dist: 4.39m, conf: 0.707)\n", "Match 1823: <PERSON><PERSON> [ 435338.01983333 5011772.94183333] <-> IFC [ 435344.16695688 5011771.69142313] (dist: 6.27m, conf: 0.582)\n", "Match 1824: <PERSON><PERSON> [ 436124.068  5012394.4556] <-> IFC [ 436120.16695688 5012395.69142313] (dist: 4.09m, conf: 0.727)\n", "Match 1825: <PERSON><PERSON> [ 436269.85425 5011158.623  ] <-> IFC [ 436274.16695688 5011155.69142313] (dist: 5.21m, conf: 0.652)\n", "Match 1826: <PERSON><PERSON> [ 436201.175 5010991.09 ] <-> IFC [ 436198.16695688 5010989.69142313] (dist: 3.32m, conf: 0.779)\n", "Match 1827: <PERSON><PERSON> [ 436615.9544 5011839.7355] <-> IFC [ 436616.16695688 5011837.69142313] (dist: 2.06m, conf: 0.863)\n", "Match 1828: <PERSON><PERSON> [ 435844.77507143 5012076.39485714] <-> IFC [ 435846.16695688 5012079.69142313] (dist: 3.58m, conf: 0.761)\n", "Match 1829: <PERSON><PERSON> [ 435966.27436364 5012155.38881818] <-> IFC [ 435970.16695688 5012151.69142313] (dist: 5.37m, conf: 0.642)\n", "Match 1830: <PERSON><PERSON> [ 436116.95975 5012226.56175] <-> IFC [ 436120.16695688 5012227.69142313] (dist: 3.40m, conf: 0.773)\n", "Match 1831: <PERSON><PERSON> [ 436292.4322 5012175.1016] <-> IFC [ 436292.16695688 5012171.69142313] (dist: 3.42m, conf: 0.772)\n", "Match 1832: <PERSON><PERSON> [ 436061.301875 5011006.74425 ] <-> IFC [ 436065.30981402 5011006.548566  ] (dist: 4.01m, conf: 0.732)\n", "Match 1833: <PERSON><PERSON> [ 436143.04228571 5012421.02714286] <-> IFC [ 436140.16695688 5012423.69142313] (dist: 3.92m, conf: 0.739)\n", "Match 1834: <PERSON><PERSON> [ 436359.801      5012121.55607143] <-> IFC [ 436358.16695688 5012121.69142313] (dist: 1.64m, conf: 0.891)\n", "Match 1835: <PERSON><PERSON> [ 435863.562      5011553.16345454] <-> IFC [ 435866.16695688 5011551.69142313] (dist: 2.99m, conf: 0.801)\n", "Match 1836: <PERSON><PERSON> [ 436114.1065625 5012355.486375 ] <-> IFC [ 436110.16695688 5012357.69142313] (dist: 4.51m, conf: 0.699)\n", "Match 1837: <PERSON><PERSON> [ 436349.32016667 5012135.75666667] <-> IFC [ 436348.16695688 5012133.69142313] (dist: 2.37m, conf: 0.842)\n", "Match 1838: <PERSON><PERSON> [ 436208.107 5012233.691] <-> IFC [ 436206.16695688 5012231.69142313] (dist: 2.79m, conf: 0.814)\n", "Match 1839: <PERSON><PERSON> [ 436003.94881818 5011635.86054545] <-> IFC [ 436008.16695688 5011639.69142313] (dist: 5.70m, conf: 0.620)\n", "Match 1840: <PERSON><PERSON> [ 436032.660375 5011765.32375 ] <-> IFC [ 436028.16695688 5011763.69142313] (dist: 4.78m, conf: 0.681)\n", "Match 1841: <PERSON><PERSON> [ 436575.52383333 5012026.45866667] <-> IFC [ 436566.16695688 5012027.69142313] (dist: 9.44m, conf: 0.371)\n", "Match 1842: <PERSON><PERSON> [ 435964.91871429 5012320.70857143] <-> IFC [ 435968.16695688 5012317.69142313] (dist: 4.43m, conf: 0.704)\n", "Match 1843: <PERSON><PERSON> [ 435909.513125 5012282.294625] <-> IFC [ 435912.16695688 5012285.69142313] (dist: 4.31m, conf: 0.713)\n", "Match 1844: <PERSON><PERSON> [ 435967.51189474 5011568.47326316] <-> IFC [ 435970.16695688 5011565.69142313] (dist: 3.85m, conf: 0.744)\n", "Match 1845: <PERSON><PERSON> [ 436330.9435   5012146.820375] <-> IFC [ 436330.16695688 5012149.69142313] (dist: 2.97m, conf: 0.802)\n", "Match 1846: <PERSON><PERSON> [ 436268.767      5011175.99088889] <-> IFC [ 436264.16695688 5011173.69142313] (dist: 5.14m, conf: 0.657)\n", "Match 1847: <PERSON><PERSON> [ 435966.83042857 5012041.89142857] <-> IFC [ 435970.16695688 5012045.69142313] (dist: 5.06m, conf: 0.663)\n", "Match 1848: <PERSON><PERSON> [ 435571.984      5011656.31533333] <-> IFC [ 435572.16695688 5011651.69142313] (dist: 4.63m, conf: 0.691)\n", "Match 1849: <PERSON><PERSON> [ 435515.08135 5012063.0451 ] <-> IFC [ 435514.16695688 5012061.69142313] (dist: 1.63m, conf: 0.891)\n", "Match 1850: <PERSON><PERSON> [ 435317.85171429 5011949.36964286] <-> IFC [ 435314.16695688 5011947.69142313] (dist: 4.05m, conf: 0.730)\n", "Match 1851: <PERSON><PERSON> [ 436141.655      5012197.55966667] <-> IFC [ 436140.16695688 5012195.69142313] (dist: 2.39m, conf: 0.841)\n", "Match 1852: <PERSON><PERSON> [ 435469.68333333 5011636.83411111] <-> IFC [ 435466.16695688 5011633.69142313] (dist: 4.72m, conf: 0.686)\n", "Match 1853: <PERSON><PERSON> [ 435439.94145455 5011680.62390909] <-> IFC [ 435438.16695688 5011677.69142313] (dist: 3.43m, conf: 0.771)\n", "Match 1854: <PERSON><PERSON> [ 436161.95244444 5012202.53372222] <-> IFC [ 436158.16695688 5012203.69142313] (dist: 3.96m, conf: 0.736)\n", "Match 1855: <PERSON><PERSON> [ 435741.54608 5011427.70132] <-> IFC [ 435742.16695688 5011429.69142313] (dist: 2.08m, conf: 0.861)\n", "Match 1856: <PERSON><PERSON> [ 436013.07375 5012348.87775] <-> IFC [ 436016.16695688 5012345.69142313] (dist: 4.44m, conf: 0.704)\n", "Match 1857: <PERSON><PERSON> [ 435723.20933333 5011450.72826667] <-> IFC [ 435723.16695688 5011451.69142313] (dist: 0.96m, conf: 0.936)\n", "Match 1858: <PERSON><PERSON> [ 436160.6925 5012229.708 ] <-> IFC [ 436158.16695688 5012227.69142313] (dist: 3.23m, conf: 0.785)\n", "Match 1859: <PERSON><PERSON> [ 436090.031      5012030.07283333] <-> IFC [ 436084.16695688 5012025.69142313] (dist: 7.32m, conf: 0.512)\n", "Match 1860: <PERSON><PERSON> [ 435713.75141176 5011415.0267647 ] <-> IFC [ 435714.16695688 5011413.69142313] (dist: 1.40m, conf: 0.907)\n", "Match 1861: <PERSON><PERSON> [ 436607.2322 5011870.163 ] <-> IFC [ 436606.16695688 5011873.69142313] (dist: 3.69m, conf: 0.754)\n", "Match 1862: <PERSON><PERSON> [ 435581.53925 5011545.158  ] <-> IFC [ 435580.16695688 5011543.69142313] (dist: 2.01m, conf: 0.866)\n", "Match 1863: <PERSON><PERSON> [ 435375.95033333 5011682.29922222] <-> IFC [ 435372.16695688 5011679.69142313] (dist: 4.60m, conf: 0.694)\n", "Match 1864: <PERSON><PERSON> [ 436256.71       5012156.25466667] <-> IFC [ 436254.16695688 5012152.69142313] (dist: 4.38m, conf: 0.708)\n", "Match 1865: <PERSON><PERSON> [ 436069.57885714 5012218.54014286] <-> IFC [ 436072.16695688 5012217.69142313] (dist: 2.72m, conf: 0.818)\n", "Match 1866: <PERSON><PERSON> [ 435806.85130769 5012148.72923077] <-> IFC [ 435808.16695688 5012145.69142313] (dist: 3.31m, conf: 0.779)\n", "Match 1867: <PERSON><PERSON> [ 436444.43294444 5012086.71994444] <-> IFC [ 436444.16695688 5012089.69142313] (dist: 2.98m, conf: 0.801)\n", "Match 1868: <PERSON><PERSON> [ 436473.79676923 5011961.89223077] <-> IFC [ 436472.16695688 5011959.69142313] (dist: 2.74m, conf: 0.817)\n", "Match 1869: <PERSON><PERSON> [ 436633.093      5011810.04885714] <-> IFC [ 436634.16695688 5011812.69142313] (dist: 2.85m, conf: 0.810)\n", "Match 1870: <PERSON><PERSON> [ 436519.4692 5012034.6156] <-> IFC [ 436520.16695688 5012035.69142313] (dist: 1.28m, conf: 0.915)\n", "Match 1871: <PERSON><PERSON> [ 436548.06484615 5012028.86461538] <-> IFC [ 436548.16695688 5012025.69142313] (dist: 3.17m, conf: 0.788)\n", "Match 1872: <PERSON><PERSON> [ 436095.38116667 5012392.97008333] <-> IFC [ 436092.16695688 5012395.69142313] (dist: 4.21m, conf: 0.719)\n", "Match 1873: <PERSON><PERSON> [ 436061.90686667 5011049.52953333] <-> IFC [ 436065.16695688 5011047.69142313] (dist: 3.74m, conf: 0.750)\n", "Match 1874: <PERSON><PERSON> [ 435855.058      5012165.46561538] <-> IFC [ 435856.16695688 5012161.69142313] (dist: 3.93m, conf: 0.738)\n", "Match 1875: <PERSON><PERSON> [ 436386.44436364 5012262.76318182] <-> IFC [ 436386.16695688 5012263.69142313] (dist: 0.97m, conf: 0.935)\n", "Match 1876: <PERSON><PERSON> [ 435864.049 5012086.991] <-> IFC [ 435866.16695688 5012089.69142313] (dist: 3.43m, conf: 0.771)\n", "Match 1877: <PERSON><PERSON> [ 436315.026875 5011230.397875] <-> IFC [ 436312.16695688 5011227.69142313] (dist: 3.94m, conf: 0.737)\n", "Match 1878: <PERSON><PERSON> [ 436188.9618125 5012337.526125 ] <-> IFC [ 436186.16695688 5012339.69142313] (dist: 3.54m, conf: 0.764)\n", "Match 1879: <PERSON><PERSON> [ 435750.02932 5012027.5748 ] <-> IFC [ 435752.16695688 5012023.69142313] (dist: 4.43m, conf: 0.704)\n", "Match 1880: <PERSON><PERSON> [ 436539.566      5011894.63441667] <-> IFC [ 436540.16695688 5011889.69142313] (dist: 4.98m, conf: 0.668)\n", "Match 1881: <PERSON><PERSON> [ 436236.42215385 5012338.81246154] <-> IFC [ 436234.16695688 5012343.69142313] (dist: 5.37m, conf: 0.642)\n", "Match 1882: <PERSON><PERSON> [ 436246.391375 5012376.608375] <-> IFC [ 436244.16695688 5012375.69142313] (dist: 2.41m, conf: 0.840)\n", "Match 1883: <PERSON><PERSON> [ 436566.9475 5011922.9401] <-> IFC [ 436568.16695688 5011921.69142313] (dist: 1.75m, conf: 0.884)\n", "Match 1884: <PERSON><PERSON> [ 435798.369    5011416.605125] <-> IFC [ 435798.16695688 5011419.69142313] (dist: 3.09m, conf: 0.794)\n", "Match 1885: <PERSON><PERSON> [ 436499.12285714 5012069.312     ] <-> IFC [ 436500.16695688 5012067.69142313] (dist: 1.93m, conf: 0.871)\n", "Match 1886: <PERSON><PERSON> [ 435780.83018182 5011279.41972727] <-> IFC [ 435780.16695688 5011285.69142313] (dist: 6.31m, conf: 0.580)\n", "Match 1887: <PERSON><PERSON> [ 436023.7646 5012132.5826] <-> IFC [ 436018.16695688 5012135.69142313] (dist: 6.40m, conf: 0.573)\n", "Match 1888: <PERSON><PERSON> [ 435834.2425     5012184.34383333] <-> IFC [ 435838.16695688 5012185.69142313] (dist: 4.15m, conf: 0.723)\n", "Match 1889: <PERSON><PERSON> [ 435449.28706667 5012023.98153333] <-> IFC [ 435448.16695688 5012023.69142313] (dist: 1.16m, conf: 0.923)\n", "Match 1890: <PERSON><PERSON> [ 436294.0144 5012332.2286] <-> IFC [ 436292.16695688 5012331.69142313] (dist: 1.92m, conf: 0.872)\n", "Match 1891: Drone [ 436188.6206 5012372.1584] <-> IFC [ 436186.16695688 5012373.69142313] (dist: 2.89m, conf: 0.807)\n", "Match 1892: <PERSON><PERSON> [ 436605.10866667 5011898.10333333] <-> IFC [ 436606.16695688 5011899.69142313] (dist: 1.91m, conf: 0.873)\n", "Match 1893: <PERSON><PERSON> [ 436615.9331 5011922.0509] <-> IFC [ 436616.16695688 5011921.69142313] (dist: 0.43m, conf: 0.971)\n", "Match 1894: <PERSON><PERSON> [ 436672.5986 5011815.7058] <-> IFC [ 436672.16695688 5011815.69142313] (dist: 0.43m, conf: 0.971)\n", "Match 1895: Dr<PERSON> [ 435420.0706 5011660.47  ] <-> IFC [ 435420.16695688 5011659.69142313] (dist: 0.78m, conf: 0.948)\n", "Match 1896: <PERSON><PERSON> [ 435825.0094 5012098.2622] <-> IFC [ 435828.16695688 5012095.69142313] (dist: 4.07m, conf: 0.729)\n", "Match 1897: <PERSON><PERSON> [ 436272.81871429 5012390.97014286] <-> IFC [ 436272.16695688 5012389.69142313] (dist: 1.44m, conf: 0.904)\n", "Match 1898: <PERSON><PERSON> [ 436320.403  5012148.9884] <-> IFC [ 436320.16695688 5012153.69142313] (dist: 4.71m, conf: 0.686)\n", "Match 1899: <PERSON><PERSON> [ 436153.455      5012412.11116667] <-> IFC [ 436158.16695688 5012409.69142313] (dist: 5.30m, conf: 0.647)\n", "Match 1900: <PERSON><PERSON> [ 435874.11675 5011479.295  ] <-> IFC [ 435876.16695688 5011483.69142313] (dist: 4.85m, conf: 0.677)\n", "Match 1901: Drone [ 435260.3132 5011822.5   ] <-> IFC [ 435268.16695688 5011821.69142313] (dist: 7.90m, conf: 0.474)\n", "Match 1902: <PERSON><PERSON> [ 435544.437      5012080.62716667] <-> IFC [ 435542.16695688 5012077.69142313] (dist: 3.71m, conf: 0.753)\n", "Match 1903: <PERSON><PERSON> [ 436434.3055  5012163.49375] <-> IFC [ 436434.16695688 5012161.69142313] (dist: 1.81m, conf: 0.879)\n", "Match 1904: <PERSON><PERSON> [ 436237.378375  5012164.9918125] <-> IFC [ 436234.16695688 5012170.69142313] (dist: 6.54m, conf: 0.564)\n", "Match 1905: <PERSON><PERSON> [ 435394.35216667 5011750.04841667] <-> IFC [ 435390.16695688 5011749.69142313] (dist: 4.20m, conf: 0.720)\n", "Match 1906: <PERSON><PERSON> [ 436680.9373 5011877.7194] <-> IFC [ 436682.16695688 5011881.69142313] (dist: 4.16m, conf: 0.723)\n", "Match 1907: <PERSON><PERSON> [ 436021.11365 5012304.6582 ] <-> IFC [ 436016.16695688 5012303.69142313] (dist: 5.04m, conf: 0.664)\n", "Match 1908: <PERSON><PERSON> [ 436519.23366667 5012077.7085    ] <-> IFC [ 436520.16695688 5012077.69142313] (dist: 0.93m, conf: 0.938)\n", "Match 1909: <PERSON><PERSON> [ 436507.4681 5011792.2957] <-> IFC [ 436506.16695688 5011791.69142313] (dist: 1.43m, conf: 0.904)\n", "Match 1910: <PERSON><PERSON> [ 436124.71933333 5012372.56633333] <-> IFC [ 436120.16695688 5012369.69142313] (dist: 5.38m, conf: 0.641)\n", "Match 1911: <PERSON><PERSON> [ 436671.4395   5011822.197875] <-> IFC [ 436672.16695688 5011823.69142313] (dist: 1.66m, conf: 0.889)\n", "Match 1912: <PERSON><PERSON> [ 436498.718  5011809.9916] <-> IFC [ 436496.16695688 5011811.69142313] (dist: 3.07m, conf: 0.796)\n", "Match 1913: <PERSON><PERSON> [ 435534.821 5011542.804] <-> IFC [ 435534.16695688 5011539.69142313] (dist: 3.18m, conf: 0.788)\n", "Match 1914: <PERSON><PERSON> [ 436418.8835 5011144.3315] <-> IFC [ 436416.16695688 5011147.69142313] (dist: 4.32m, conf: 0.712)\n", "Match 1915: <PERSON><PERSON> [ 436077.555  5012349.7566] <-> IFC [ 436082.16695688 5012349.69142313] (dist: 4.61m, conf: 0.693)\n", "Match 1916: <PERSON><PERSON> [ 436217.26342857 5012461.54785714] <-> IFC [ 436216.16695688 5012460.69142313] (dist: 1.39m, conf: 0.907)\n", "Match 1917: <PERSON><PERSON> [ 435937.9419 5012003.0187] <-> IFC [ 435942.16695688 5012001.69142313] (dist: 4.43m, conf: 0.705)\n", "Match 1918: <PERSON><PERSON> [ 435646.88616667 5011473.13666667] <-> IFC [ 435648.16695688 5011471.69142313] (dist: 1.93m, conf: 0.871)\n", "Match 1919: <PERSON><PERSON> [ 436661.33381818 5011831.37736364] <-> IFC [ 436662.16695688 5011827.69142313] (dist: 3.78m, conf: 0.748)\n", "Match 1920: <PERSON><PERSON> [ 436546.811125 5012046.8365  ] <-> IFC [ 436548.16695688 5012051.69142313] (dist: 5.04m, conf: 0.664)\n", "Match 1921: Dr<PERSON> [ 436253.88088889 5012372.794     ] <-> IFC [ 436254.16695688 5012371.69142313] (dist: 1.14m, conf: 0.924)\n", "Match 1922: <PERSON><PERSON> [ 436671.4896 5011839.2564] <-> IFC [ 436672.16695688 5011841.69142313] (dist: 2.53m, conf: 0.832)\n", "Match 1923: <PERSON><PERSON> [ 435686.08584615 5011445.23653846] <-> IFC [ 435684.16695688 5011441.69142313] (dist: 4.03m, conf: 0.731)\n", "Match 1924: <PERSON><PERSON> [ 436151.43863636 5012388.16036364] <-> IFC [ 436148.16695688 5012387.69142313] (dist: 3.31m, conf: 0.780)\n", "Match 1925: <PERSON><PERSON> [ 436123.9918 5012233.7878] <-> IFC [ 436120.16695688 5012235.69142313] (dist: 4.27m, conf: 0.715)\n", "Match 1926: <PERSON><PERSON> [ 435759.95866667 5011271.96377778] <-> IFC [ 435760.16695688 5011267.69142313] (dist: 4.28m, conf: 0.715)\n", "Match 1927: <PERSON><PERSON> [ 435554.48528571 5011564.36214286] <-> IFC [ 435552.16695688 5011563.69142313] (dist: 2.41m, conf: 0.839)\n", "Match 1928: <PERSON><PERSON> [ 436052.1438 5011007.0736] <-> IFC [ 436056.16695688 5011007.69142313] (dist: 4.07m, conf: 0.729)\n", "Match 1929: <PERSON><PERSON> [ 436057.170125 5012305.778875] <-> IFC [ 436054.16695688 5012307.69142313] (dist: 3.56m, conf: 0.763)\n", "Match 1930: <PERSON><PERSON> [ 436193.888  5011003.4516] <-> IFC [ 436188.16695688 5010997.69142313] (dist: 8.12m, conf: 0.459)\n", "Match 1931: <PERSON><PERSON> [ 436105.08273333 5012316.31753333] <-> IFC [ 436102.16695688 5012317.69142313] (dist: 3.22m, conf: 0.785)\n", "Match 1932: <PERSON><PERSON> [ 436153.10014286 5012219.88571429] <-> IFC [ 436148.16695688 5012219.69142313] (dist: 4.94m, conf: 0.671)\n", "Match 1933: <PERSON><PERSON> [ 435496.8490625 5011677.336625 ] <-> IFC [ 435496.16695688 5011673.69142313] (dist: 3.71m, conf: 0.753)\n", "Match 1934: <PERSON><PERSON> [ 435919.50057143 5012016.28664286] <-> IFC [ 435922.16695688 5012011.69142313] (dist: 5.31m, conf: 0.646)\n", "Match 1935: <PERSON><PERSON> [ 436208.636375 5012155.218125] <-> IFC [ 436206.16695688 5012153.69142313] (dist: 2.90m, conf: 0.806)\n", "Match 1936: <PERSON><PERSON> [ 436052.03775 5011846.998  ] <-> IFC [ 436056.16695688 5011845.69142313] (dist: 4.33m, conf: 0.711)\n", "Match 1937: <PERSON><PERSON> [ 435486.334      5011676.56233333] <-> IFC [ 435486.16695688 5011677.69142313] (dist: 1.14m, conf: 0.924)\n", "Match 1938: <PERSON><PERSON> [ 436330.74647059 5012227.45611765] <-> IFC [ 436330.16695688 5012225.69142313] (dist: 1.86m, conf: 0.876)\n", "Match 1939: <PERSON><PERSON> [ 436604.07357143 5011978.09142857] <-> IFC [ 436604.16695688 5011979.69142313] (dist: 1.60m, conf: 0.893)\n", "Match 1940: <PERSON><PERSON> [ 436218.32742857 5012337.83071429] <-> IFC [ 436216.16695688 5012335.69142313] (dist: 3.04m, conf: 0.797)\n", "Match 1941: <PERSON><PERSON> [ 436338.89633333 5011620.33222222] <-> IFC [ 436334.16695688 5011621.69142313] (dist: 4.92m, conf: 0.672)\n", "Match 1942: <PERSON><PERSON> [ 435929.221625 5012085.373125] <-> IFC [ 435932.16695688 5012091.69142313] (dist: 6.97m, conf: 0.535)\n", "Match 1943: <PERSON><PERSON> [ 436245.2534 5012251.0193] <-> IFC [ 436244.16695688 5012255.69142313] (dist: 4.80m, conf: 0.680)\n", "Match 1944: <PERSON><PERSON> [ 435498.13125 5011636.154  ] <-> IFC [ 435496.16695688 5011639.69142313] (dist: 4.05m, conf: 0.730)\n", "Match 1945: <PERSON><PERSON> [ 436264.5935   5012393.230125] <-> IFC [ 436262.16695688 5012393.69142313] (dist: 2.47m, conf: 0.835)\n", "Match 1946: <PERSON><PERSON> [ 435364.68333333 5011726.0045    ] <-> IFC [ 435362.16695688 5011723.69142313] (dist: 3.42m, conf: 0.772)\n", "Match 1947: <PERSON><PERSON> [ 435458.67954545 5011646.82572727] <-> IFC [ 435458.16695688 5011645.69142313] (dist: 1.24m, conf: 0.917)\n", "Match 1948: <PERSON><PERSON> [ 436566.047      5011942.73816667] <-> IFC [ 436568.16695688 5011939.69142313] (dist: 3.71m, conf: 0.753)\n", "Match 1949: <PERSON><PERSON> [ 435817.82244444 5011326.50766667] <-> IFC [ 435818.16695688 5011329.69142313] (dist: 3.20m, conf: 0.787)\n", "Match 1950: <PERSON><PERSON> [ 436717.84688889 5011835.50655555] <-> IFC [ 436719.16695688 5011833.69142313] (dist: 2.24m, conf: 0.850)\n", "Match 1951: <PERSON><PERSON> [ 435955.67733333 5012066.64533333] <-> IFC [ 435960.16695688 5012065.69142313] (dist: 4.59m, conf: 0.694)\n", "Match 1952: <PERSON><PERSON> [ 435667.47425  5012156.409125] <-> IFC [ 435666.16695688 5012155.69142313] (dist: 1.49m, conf: 0.901)\n", "Match 1953: <PERSON><PERSON> [ 436323.1357 5012255.4231] <-> IFC [ 436320.16695688 5012253.69142313] (dist: 3.44m, conf: 0.771)\n", "Match 1954: <PERSON><PERSON> [ 436076.571875 5012253.2175  ] <-> IFC [ 436072.16695688 5012250.69142313] (dist: 5.08m, conf: 0.661)\n", "Match 1955: <PERSON><PERSON> [ 436049.8465  5012222.45575] <-> IFC [ 436054.16695688 5012223.69142313] (dist: 4.49m, conf: 0.700)\n", "Match 1956: <PERSON><PERSON> [ 435281.682      5011924.20571429] <-> IFC [ 435286.16695688 5011923.69142313] (dist: 4.51m, conf: 0.699)\n", "Match 1957: <PERSON><PERSON> [ 436129.1441 5010979.6257] <-> IFC [ 436132.16695688 5010981.69142313] (dist: 3.66m, conf: 0.756)\n", "Match 1958: <PERSON><PERSON> [ 435375.43685714 5011730.00385714] <-> IFC [ 435372.16695688 5011729.69142313] (dist: 3.28m, conf: 0.781)\n", "Match 1959: <PERSON><PERSON> [ 435947.79428571 5011692.29071429] <-> IFC [ 435942.16695688 5011691.69142313] (dist: 5.66m, conf: 0.623)\n", "Match 1960: <PERSON><PERSON> [ 436415.9735 5012059.887 ] <-> IFC [ 436414.16695688 5012061.69142313] (dist: 2.55m, conf: 0.830)\n", "Match 1961: <PERSON><PERSON> [ 436718.09175 5011844.5675 ] <-> IFC [ 436719.16695688 5011841.69142313] (dist: 3.07m, conf: 0.795)\n", "Match 1962: <PERSON><PERSON> [ 435318.1175  5011953.32425] <-> IFC [ 435314.16695688 5011955.69142313] (dist: 4.61m, conf: 0.693)\n", "Match 1963: <PERSON><PERSON> [ 436295.1852 5011219.3552] <-> IFC [ 436293.16695688 5011217.69142313] (dist: 2.62m, conf: 0.826)\n", "Match 1964: <PERSON><PERSON> [ 436250.92222222 5011264.61566667] <-> IFC [ 436256.16695688 5011265.69142313] (dist: 5.35m, conf: 0.643)\n", "Match 1965: <PERSON><PERSON> [ 436335.1952 5011155.54  ] <-> IFC [ 436331.16695688 5011157.69142313] (dist: 4.57m, conf: 0.696)\n", "Match 1966: <PERSON><PERSON> [ 436634.65514286 5011825.03685714] <-> IFC [ 436634.16695688 5011821.69142313] (dist: 3.38m, conf: 0.775)\n", "Match 1967: <PERSON><PERSON> [ 436623.888  5011882.8105] <-> IFC [ 436624.16695688 5011883.69142313] (dist: 0.92m, conf: 0.938)\n", "Match 1968: <PERSON><PERSON> [ 436165.091  5011016.7484] <-> IFC [ 436160.16695688 5011019.69142313] (dist: 5.74m, conf: 0.618)\n", "Match 1969: <PERSON><PERSON> [ 435854.11044444 5012145.17933333] <-> IFC [ 435856.16695688 5012143.69142313] (dist: 2.54m, conf: 0.831)\n", "Match 1970: <PERSON><PERSON> [ 436508.94075 5012056.14525] <-> IFC [ 436510.16695688 5012055.69142313] (dist: 1.31m, conf: 0.913)\n", "Match 1971: <PERSON><PERSON> [ 435499.06628571 5011621.47985714] <-> IFC [ 435496.16695688 5011623.69142313] (dist: 3.65m, conf: 0.757)\n", "Match 1972: <PERSON><PERSON> [ 436322.5485     5012299.18483333] <-> IFC [ 436320.16695688 5012295.69142313] (dist: 4.23m, conf: 0.718)\n", "Match 1973: <PERSON><PERSON> [ 436033.85833333 5011037.92066667] <-> IFC [ 436036.16695688 5011039.69142313] (dist: 2.91m, conf: 0.806)\n", "Match 1974: <PERSON><PERSON> [ 436212.99842857 5011994.17414286] <-> IFC [ 436217.16695688 5011995.69142314] (dist: 4.44m, conf: 0.704)\n", "Match 1975: <PERSON><PERSON> [ 436273.235  5012209.8506] <-> IFC [ 436272.16695688 5012213.69142313] (dist: 3.99m, conf: 0.734)\n", "Match 1976: <PERSON><PERSON> [ 436292.851125 5012168.945875] <-> IFC [ 436292.16695688 5012171.69142313] (dist: 2.83m, conf: 0.811)\n", "Match 1977: Dr<PERSON> [ 435873.8082 5012090.9858] <-> IFC [ 435874.56695688 5012094.09142313] (dist: 3.20m, conf: 0.787)\n", "Match 1978: <PERSON><PERSON> [ 435467.888      5011650.34016667] <-> IFC [ 435466.16695688 5011650.69142313] (dist: 1.76m, conf: 0.883)\n", "Match 1979: <PERSON><PERSON> [ 436497.083  5011800.1898] <-> IFC [ 436496.16695688 5011803.69142313] (dist: 3.62m, conf: 0.759)\n", "Match 1980: <PERSON><PERSON> [ 436063.20942857 5011106.44385714] <-> IFC [ 436066.16695688 5011107.69142313] (dist: 3.21m, conf: 0.786)\n", "Match 1981: <PERSON><PERSON> [ 435993.5772 5012329.1372] <-> IFC [ 435996.16695688 5012325.69142313] (dist: 4.31m, conf: 0.713)\n", "Match 1982: Drone [ 435768.27136364 5011988.96027273] <-> IFC [ 435770.16695688 5011987.69142313] (dist: 2.28m, conf: 0.848)\n", "Match 1983: <PERSON><PERSON> [ 436199.36339286 5012222.43971428] <-> IFC [ 436196.16695688 5012223.69142313] (dist: 3.43m, conf: 0.771)\n", "Match 1984: Dr<PERSON> [ 436031.7012 5012200.393 ] <-> IFC [ 436034.16695688 5012203.69142313] (dist: 4.12m, conf: 0.725)\n", "Match 1985: Drone [ 436568.9506 5011876.7378] <-> IFC [ 436568.16695688 5011879.69142313] (dist: 3.06m, conf: 0.796)\n", "Match 1986: Dr<PERSON> [ 436228.985  5012185.7145] <-> IFC [ 436224.16695688 5012187.69142313] (dist: 5.21m, conf: 0.653)\n", "Match 1987: <PERSON><PERSON> [ 436401.21583333 5011981.58166667] <-> IFC [ 436398.16695688 5011977.69142313] (dist: 4.94m, conf: 0.670)\n", "Match 1988: <PERSON><PERSON> [ 435996.180875 5012020.63625 ] <-> IFC [ 435998.16695688 5012017.69142313] (dist: 3.55m, conf: 0.763)\n", "Match 1989: Drone [ 436160.311 5012334.654] <-> IFC [ 436158.16695688 5012333.69142313] (dist: 2.35m, conf: 0.843)\n", "Match 1990: Drone [ 435788.9353 5011347.4589] <-> IFC [ 435790.16695688 5011345.69142313] (dist: 2.15m, conf: 0.856)\n", "Match 1991: Dr<PERSON> [ 435966.314    5012111.516375] <-> IFC [ 435970.16695688 5012109.69142313] (dist: 4.26m, conf: 0.716)\n", "Match 1992: Dr<PERSON> [ 436623.9933 5011817.6716] <-> IFC [ 436624.16695688 5011816.69142313] (dist: 1.00m, conf: 0.934)\n", "Match 1993: Drone [ 436180.79407143 5012397.59542857] <-> IFC [ 436178.16695688 5012393.69142313] (dist: 4.71m, conf: 0.686)\n", "Match 1994: Dr<PERSON> [ 436166.426  5010995.7479] <-> IFC [ 436160.16695688 5010995.69142313] (dist: 6.26m, conf: 0.583)\n", "Match 1995: Drone [ 436320.69785714 5012242.79542857] <-> IFC [ 436320.16695688 5012245.69142313] (dist: 2.94m, conf: 0.804)\n", "Match 1996: <PERSON><PERSON> [ 436276.88866667 5011141.66233333] <-> IFC [ 436274.16695688 5011137.69142313] (dist: 4.81m, conf: 0.679)\n", "Match 1997: Drone [ 435928.78216667 5012236.945     ] <-> IFC [ 435930.16695688 5012237.69142313] (dist: 1.57m, conf: 0.895)\n", "Match 1998: Drone [ 436227.26475  5012325.440375] <-> IFC [ 436224.16695688 5012325.69142313] (dist: 3.11m, conf: 0.793)\n", "Match 1999: Drone [ 436228.45477778 5012409.64777778] <-> IFC [ 436224.16695688 5012409.69142313] (dist: 4.29m, conf: 0.714)\n", "Match 2000: Dr<PERSON> [ 436171.41133333 5012350.43083333] <-> IFC [ 436168.16695688 5012347.69142313] (dist: 4.25m, conf: 0.717)\n", "Match 2001: Dr<PERSON> [ 436010.69047059 5012228.58652941] <-> IFC [ 436006.16695688 5012229.69142313] (dist: 4.66m, conf: 0.690)\n", "Match 2002: Drone [ 435911.08716667 5012151.9275    ] <-> IFC [ 435914.16695688 5012157.69142313] (dist: 6.54m, conf: 0.564)\n", "Match 2003: Drone [ 435983.8475  5011991.28475] <-> IFC [ 435980.16695688 5011994.69142313] (dist: 5.02m, conf: 0.666)\n", "Match 2004: Drone [ 435657.3785     5011904.28383333] <-> IFC [ 435656.16695688 5011901.69142313] (dist: 2.86m, conf: 0.809)\n", "Match 2005: Drone [ 436339.566  5012199.3835] <-> IFC [ 436338.16695688 5012195.69142313] (dist: 3.95m, conf: 0.737)\n", "Match 2006: Drone [ 435543.123      5011564.56528571] <-> IFC [ 435542.16695688 5011567.69142313] (dist: 3.27m, conf: 0.782)\n", "Match 2007: Drone [ 436616.36411765 5011847.28994118] <-> IFC [ 436616.16695688 5011845.69142313] (dist: 1.61m, conf: 0.893)\n", "Match 2008: Drone [ 436169.93125 5011656.69875] <-> IFC [ 436174.16695688 5011653.69142313] (dist: 5.19m, conf: 0.654)\n", "Match 2009: Drone [ 436048.30925 5012330.30775] <-> IFC [ 436044.16695688 5012327.69142313] (dist: 4.90m, conf: 0.673)\n", "Match 2010: Dr<PERSON> [ 436442.9805  5012167.07975] <-> IFC [ 436444.16695688 5012165.69142313] (dist: 1.83m, conf: 0.878)\n", "Match 2011: <PERSON><PERSON> [ 436032.49475 5011769.18425] <-> IFC [ 436028.16695688 5011763.69142313] (dist: 6.99m, conf: 0.534)\n", "Match 2012: Drone [ 435610.99288889 5011443.16722222] <-> IFC [ 435609.16695688 5011443.69142313] (dist: 1.90m, conf: 0.873)\n", "Match 2013: Drone [ 436171.249  5012331.0135] <-> IFC [ 436168.16695688 5012329.69142313] (dist: 3.35m, conf: 0.776)\n", "Match 2014: Drone [ 436117.33907692 5012097.67276923] <-> IFC [ 436112.16695688 5012099.69142313] (dist: 5.55m, conf: 0.630)\n", "Match 2015: Drone [ 435345.59016667 5011767.14433333] <-> IFC [ 435344.16695688 5011763.69142313] (dist: 3.73m, conf: 0.751)\n", "Match 2016: Dr<PERSON> [ 436284.73533333 5012145.08977778] <-> IFC [ 436282.16695688 5012141.69142313] (dist: 4.26m, conf: 0.716)\n", "Match 2017: Drone [ 436342.5698 5011978.0728] <-> IFC [ 436340.16695688 5011973.69142313] (dist: 5.00m, conf: 0.667)\n", "Match 2018: <PERSON><PERSON> [ 436623.5794 5011799.5444] <-> IFC [ 436624.16695688 5011799.69142313] (dist: 0.61m, conf: 0.960)\n", "Match 2019: Drone [ 435809.65975 5011385.28275] <-> IFC [ 435808.16695688 5011383.69142313] (dist: 2.18m, conf: 0.855)\n", "Match 2020: Dr<PERSON> [ 436360.85271429 5011210.06871429] <-> IFC [ 436360.16695688 5011210.69142313] (dist: 0.93m, conf: 0.938)\n", "Match 2021: <PERSON><PERSON> [ 435338.2678 5011739.5982] <-> IFC [ 435334.16695688 5011741.69142313] (dist: 4.60m, conf: 0.693)\n", "Match 2022: <PERSON><PERSON> [ 436438.7454 5011178.2592] <-> IFC [ 436436.16695688 5011179.69142313] (dist: 2.95m, conf: 0.803)\n", "Match 2023: <PERSON><PERSON> [ 436226.0812 5012205.206 ] <-> IFC [ 436224.16695688 5012205.69142313] (dist: 1.97m, conf: 0.868)\n", "Match 2024: <PERSON><PERSON> [ 435628.7772 5011469.8223] <-> IFC [ 435638.16695688 5011473.69142313] (dist: 10.16m, conf: 0.323)\n", "Match 2025: <PERSON><PERSON> [ 436510.8634 5012098.2108] <-> IFC [ 436510.16695688 5012099.69142313] (dist: 1.64m, conf: 0.891)\n", "Match 2026: <PERSON><PERSON> [ 436472.62       5012130.44285714] <-> IFC [ 436472.16695688 5012129.69142313] (dist: 0.88m, conf: 0.942)\n", "Match 2027: <PERSON><PERSON> [ 435366.2584 5011730.8292] <-> IFC [ 435362.16695688 5011731.69142313] (dist: 4.18m, conf: 0.721)\n", "Match 2028: <PERSON><PERSON> [ 436019.582  5012206.7394] <-> IFC [ 436016.16695688 5012209.69142313] (dist: 4.51m, conf: 0.699)\n", "Match 2029: Dr<PERSON> [ 435770.0944 5012048.4684] <-> IFC [ 435770.16695688 5012045.69142313] (dist: 2.78m, conf: 0.815)\n", "Match 2030: <PERSON><PERSON> [ 436352.56475 5011219.5365 ] <-> IFC [ 436350.16695688 5011215.69142313] (dist: 4.53m, conf: 0.698)\n", "Match 2031: <PERSON><PERSON> [ 436304.29622222 5012206.21622222] <-> IFC [ 436310.16695688 5012207.69142313] (dist: 6.05m, conf: 0.596)\n", "Match 2032: <PERSON><PERSON> [ 436066.547 5012314.193] <-> IFC [ 436064.16695688 5012313.69142313] (dist: 2.43m, conf: 0.838)\n", "Match 2033: <PERSON><PERSON> [ 436170.0488 5012229.7092] <-> IFC [ 436168.16695688 5012227.69142313] (dist: 2.76m, conf: 0.816)\n", "Match 2034: Dr<PERSON> [ 436218.54616667 5012158.89791667] <-> IFC [ 436216.16695688 5012154.69142313] (dist: 4.83m, conf: 0.678)\n", "Match 2035: <PERSON><PERSON> [ 435752.37283333 5011469.7895    ] <-> IFC [ 435752.16695688 5011469.69142313] (dist: 0.23m, conf: 0.985)\n", "Match 2036: <PERSON><PERSON> [ 436182.3574     5012154.88946667] <-> IFC [ 436178.16695688 5012157.69142313] (dist: 5.04m, conf: 0.664)\n", "Match 2037: <PERSON><PERSON> [ 436500.35681818 5012098.72745454] <-> IFC [ 436500.16695688 5012101.69142313] (dist: 2.97m, conf: 0.802)\n", "Match 2038: <PERSON><PERSON> [ 435852.8582 5011454.8124] <-> IFC [ 435856.16695688 5011451.69142313] (dist: 4.55m, conf: 0.697)\n", "Match 2039: <PERSON><PERSON> [ 436470.61742857 5012144.98457143] <-> IFC [ 436472.16695688 5012147.69142313] (dist: 3.12m, conf: 0.792)\n", "Match 2040: <PERSON><PERSON> [ 436085.67825 5012243.54775] <-> IFC [ 436082.16695688 5012239.69142313] (dist: 5.22m, conf: 0.652)\n", "Match 2041: <PERSON><PERSON> [ 435610.5475 5011831.953 ] <-> IFC [ 435610.16695688 5011831.69142313] (dist: 0.46m, conf: 0.969)\n", "Match 2042: Dr<PERSON> [ 436288.69366667 5011097.62688889] <-> IFC [ 436293.50029021 5011098.3580898 ] (dist: 4.86m, conf: 0.676)\n", "Match 2043: <PERSON><PERSON> [ 435910.1502 5012171.4342] <-> IFC [ 435914.16695688 5012173.69142313] (dist: 4.61m, conf: 0.693)\n", "Match 2044: <PERSON><PERSON> [ 436671.70416667 5011870.56316667] <-> IFC [ 436672.16695688 5011866.69142313] (dist: 3.90m, conf: 0.740)\n", "Match 2045: <PERSON><PERSON> [ 436029.63033333 5012325.3765    ] <-> IFC [ 436026.16695688 5012325.69142313] (dist: 3.48m, conf: 0.768)\n", "Match 2046: <PERSON><PERSON> [ 436175.28069231 5010972.19053846] <-> IFC [ 436179.16695688 5010969.69142313] (dist: 4.62m, conf: 0.692)\n", "Match 2047: <PERSON><PERSON> [ 436241.0476 5012015.1186] <-> IFC [ 436236.16695688 5012013.69142313] (dist: 5.09m, conf: 0.661)\n", "Match 2048: <PERSON><PERSON> [ 436490.92  5012114.536] <-> IFC [ 436490.16695688 5012113.69142313] (dist: 1.13m, conf: 0.925)\n", "Match 2049: <PERSON><PERSON> [ 436471.41283333 5012156.563     ] <-> IFC [ 436472.16695688 5012155.69142313] (dist: 1.15m, conf: 0.923)\n", "Match 2050: <PERSON><PERSON> [ 435919.30833333 5011527.70858333] <-> IFC [ 435922.16695688 5011527.69142313] (dist: 2.86m, conf: 0.809)\n", "Match 2051: <PERSON><PERSON> [ 436453.5292 5012070.5959] <-> IFC [ 436452.16695688 5012069.69142313] (dist: 1.64m, conf: 0.891)\n", "Match 2052: <PERSON><PERSON> [ 436173.4692 5010974.697 ] <-> IFC [ 436170.16695688 5010973.69142313] (dist: 3.45m, conf: 0.770)\n", "Match 2053: <PERSON><PERSON> [ 435921.395      5012022.35533333] <-> IFC [ 435922.16695688 5012021.69142313] (dist: 1.02m, conf: 0.932)\n", "Match 2054: <PERSON><PERSON> [ 436624.6124 5011871.6242] <-> IFC [ 436624.16695688 5011867.69142313] (dist: 3.96m, conf: 0.736)\n", "Match 2055: <PERSON><PERSON> [ 436128.77566667 5011044.10533333] <-> IFC [ 436132.16695688 5011039.69142313] (dist: 5.57m, conf: 0.629)\n", "Match 2056: <PERSON><PERSON> [ 436519.568 5012030.731] <-> IFC [ 436520.16695688 5012027.69142313] (dist: 3.10m, conf: 0.793)\n", "Match 2057: <PERSON><PERSON> [ 435789.9125  5011351.13675] <-> IFC [ 435790.16695688 5011353.69142313] (dist: 2.57m, conf: 0.829)\n", "Match 2058: <PERSON><PERSON> [ 435844.98133333 5012147.166     ] <-> IFC [ 435846.16695688 5012147.69142313] (dist: 1.30m, conf: 0.914)\n", "Match 2059: <PERSON><PERSON> [ 436029.809      5012347.86916667] <-> IFC [ 436026.16695688 5012349.69142313] (dist: 4.07m, conf: 0.729)\n", "Match 2060: <PERSON><PERSON> [ 436129.0338 5010987.673 ] <-> IFC [ 436132.16695688 5010989.69142313] (dist: 3.73m, conf: 0.752)\n", "Match 2061: <PERSON><PERSON> [ 435721.55157143 5012003.00985714] <-> IFC [ 435724.16695688 5012001.69142313] (dist: 2.93m, conf: 0.805)\n", "Match 2062: <PERSON><PERSON> [ 436235.1254 5012370.782 ] <-> IFC [ 436234.16695688 5012369.69142313] (dist: 1.45m, conf: 0.903)\n", "Match 2063: <PERSON><PERSON> [ 435703.87214286 5011456.24642857] <-> IFC [ 435704.16695688 5011457.69142313] (dist: 1.47m, conf: 0.902)\n", "Match 2064: <PERSON><PERSON> [ 436033.362625 5012103.064   ] <-> IFC [ 436036.16695688 5012099.69142313] (dist: 4.39m, conf: 0.708)\n", "Match 2065: <PERSON><PERSON> [ 436578.42727273 5011932.92272727] <-> IFC [ 436578.16695688 5011935.69142313] (dist: 2.78m, conf: 0.815)\n", "Match 2066: <PERSON><PERSON> [ 435659.2152 5011392.196 ] <-> IFC [ 435656.16695688 5011391.69142313] (dist: 3.09m, conf: 0.794)\n", "Match 2067: <PERSON><PERSON> [ 435984.75433333 5012218.5885    ] <-> IFC [ 435988.16695688 5012219.69142313] (dist: 3.59m, conf: 0.761)\n", "Match 2068: <PERSON><PERSON> [ 436011.523625 5012240.594375] <-> IFC [ 436016.16695688 5012243.69142313] (dist: 5.58m, conf: 0.628)\n", "Match 2069: <PERSON><PERSON> [ 436235.6634 5012259.5568] <-> IFC [ 436234.16695688 5012255.69142313] (dist: 4.14m, conf: 0.724)\n", "Match 2070: <PERSON><PERSON> [ 436160.749    5012177.099625] <-> IFC [ 436158.16695688 5012177.69142313] (dist: 2.65m, conf: 0.823)\n", "Match 2071: <PERSON><PERSON> [ 436595.9775 5011889.8235] <-> IFC [ 436596.16695688 5011885.69142313] (dist: 4.14m, conf: 0.724)\n", "Match 2072: <PERSON><PERSON> [ 435899.35183333 5012284.8905    ] <-> IFC [ 435902.16695688 5012281.69142313] (dist: 4.26m, conf: 0.716)\n", "Match 2073: <PERSON><PERSON> [ 436453.26238462 5012046.96138462] <-> IFC [ 436452.16695688 5012043.69142313] (dist: 3.45m, conf: 0.770)\n", "Match 2074: <PERSON><PERSON> [ 436576.94       5011890.33316667] <-> IFC [ 436578.16695688 5011893.69142313] (dist: 3.58m, conf: 0.762)\n", "Match 2075: <PERSON><PERSON> [ 435392.8305  5011754.39075] <-> IFC [ 435390.16695688 5011757.69142313] (dist: 4.24m, conf: 0.717)\n", "Match 2076: <PERSON><PERSON> [ 435896.9808 5012277.1492] <-> IFC [ 435892.16695688 5012275.69142313] (dist: 5.03m, conf: 0.665)\n", "Match 2077: <PERSON><PERSON> [ 436245.44583333 5011642.16766667] <-> IFC [ 436240.16695688 5011645.69142313] (dist: 6.35m, conf: 0.577)\n", "Match 2078: <PERSON><PERSON> [ 436339.669 5012150.34 ] <-> IFC [ 436338.16695688 5012153.69142313] (dist: 3.67m, conf: 0.755)\n", "Match 2079: <PERSON><PERSON> [ 436689.1225 5011845.4995] <-> IFC [ 436692.16695688 5011843.69142313] (dist: 3.54m, conf: 0.764)\n", "Match 2080: Dr<PERSON> [ 436141.9424 5012396.1992] <-> IFC [ 436140.16695688 5012397.69142313] (dist: 2.32m, conf: 0.845)\n", "Match 2081: <PERSON><PERSON> [ 435468.8655     5011658.74616667] <-> IFC [ 435466.16695688 5011659.69142313] (dist: 2.86m, conf: 0.809)\n", "Match 2082: <PERSON><PERSON> [ 435542.377 5011701.017] <-> IFC [ 435542.16695688 5011700.69142313] (dist: 0.39m, conf: 0.974)\n", "Match 2083: <PERSON><PERSON> [ 435667.23733333 5011969.394     ] <-> IFC [ 435666.16695688 5011967.69142313] (dist: 2.01m, conf: 0.866)\n", "Match 2084: <PERSON><PERSON> [ 435752.11814286 5011463.14142857] <-> IFC [ 435752.16695688 5011459.69142313] (dist: 3.45m, conf: 0.770)\n", "Match 2085: <PERSON><PERSON> [ 436500.15015385 5012120.01407692] <-> IFC [ 436500.16695688 5012119.69142313] (dist: 0.32m, conf: 0.978)\n", "Match 2086: <PERSON><PERSON> [ 435732.7298 5011459.2046] <-> IFC [ 435732.16695688 5011457.69142313] (dist: 1.61m, conf: 0.892)\n", "Match 2087: <PERSON><PERSON> [ 435366.628      5011728.14466667] <-> IFC [ 435362.16695688 5011731.69142313] (dist: 5.70m, conf: 0.620)\n", "Match 2088: <PERSON><PERSON> [ 436277.1214 5011280.3652] <-> IFC [ 436274.16695688 5011273.69142313] (dist: 7.30m, conf: 0.513)\n", "Match 2089: <PERSON><PERSON> [ 435338.0755     5011901.54766667] <-> IFC [ 435334.16695688 5011899.69142313] (dist: 4.33m, conf: 0.712)\n", "Match 2090: <PERSON><PERSON> [ 436287.6186 5011104.9987] <-> IFC [ 436284.16695688 5011103.69142313] (dist: 3.69m, conf: 0.754)\n", "Match 2091: <PERSON><PERSON> [ 435956.67644444 5011673.19544444] <-> IFC [ 435960.16695688 5011673.69142313] (dist: 3.53m, conf: 0.765)\n", "Match 2092: <PERSON><PERSON> [ 436031.3236 5011838.965 ] <-> IFC [ 436028.16695688 5011839.69142313] (dist: 3.24m, conf: 0.784)\n", "Match 2093: <PERSON><PERSON> [ 435440.4186 5011668.8708] <-> IFC [ 435438.16695688 5011669.69142313] (dist: 2.40m, conf: 0.840)\n", "Match 2094: <PERSON><PERSON> [ 435893.285 5011514.045] <-> IFC [ 435894.16695688 5011513.69142313] (dist: 0.95m, conf: 0.937)\n", "Match 2095: <PERSON><PERSON> [ 436029.03390909 5012318.59245455] <-> IFC [ 436026.16695688 5012317.69142313] (dist: 3.01m, conf: 0.800)\n", "Match 2096: <PERSON><PERSON> [ 435328.56016667 5011777.76616667] <-> IFC [ 435334.16695688 5011778.69142313] (dist: 5.68m, conf: 0.621)\n", "Match 2097: <PERSON><PERSON> [ 435298.33663636 5011935.42109091] <-> IFC [ 435296.16695688 5011935.69142313] (dist: 2.19m, conf: 0.854)\n", "Match 2098: <PERSON><PERSON> [ 436508.78563636 5012040.53654545] <-> IFC [ 436510.16695688 5012039.69142313] (dist: 1.62m, conf: 0.892)\n", "Match 2099: <PERSON><PERSON> [ 435572.33871429 5011630.85057143] <-> IFC [ 435572.16695688 5011627.69142313] (dist: 3.16m, conf: 0.789)\n", "Match 2100: <PERSON><PERSON> [ 436425.109875 5012182.070375] <-> IFC [ 436424.16695688 5012181.69142313] (dist: 1.02m, conf: 0.932)\n", "Match 2101: <PERSON><PERSON> [ 436378.78725 5012151.3945 ] <-> IFC [ 436376.16695688 5012147.69142313] (dist: 4.54m, conf: 0.698)\n", "Match 2102: <PERSON><PERSON> [ 436437.869125 5011171.293375] <-> IFC [ 436436.16695688 5011171.69142313] (dist: 1.75m, conf: 0.883)\n", "Match 2103: <PERSON><PERSON> [ 435618.693875 5011509.185375] <-> IFC [ 435618.16695688 5011507.69142313] (dist: 1.58m, conf: 0.894)\n", "Match 2104: <PERSON><PERSON> [ 435458.21475  5011688.030125] <-> IFC [ 435458.16695688 5011687.69142313] (dist: 0.34m, conf: 0.977)\n", "Match 2105: <PERSON><PERSON> [ 435909.30172727 5011964.33118182] <-> IFC [ 435904.16695688 5011963.69142313] (dist: 5.17m, conf: 0.655)\n", "Match 2106: <PERSON><PERSON> [ 436594.95933333 5011911.00766667] <-> IFC [ 436596.16695688 5011911.69142313] (dist: 1.39m, conf: 0.907)\n", "Match 2107: <PERSON><PERSON> [ 435564.646      5011598.39071429] <-> IFC [ 435562.16695688 5011601.69142313] (dist: 4.13m, conf: 0.725)\n", "Match 2108: <PERSON><PERSON> [ 436275.74   5012333.5794] <-> IFC [ 436272.16695688 5012331.69142313] (dist: 4.04m, conf: 0.731)\n", "Match 2109: <PERSON><PERSON> [ 436149.688625 5011648.797625] <-> IFC [ 436154.16695688 5011647.69142313] (dist: 4.61m, conf: 0.692)\n", "Match 2110: <PERSON><PERSON> [ 435514.09144444 5011676.39122222] <-> IFC [ 435514.16695688 5011675.69142313] (dist: 0.70m, conf: 0.953)\n", "Match 2111: <PERSON><PERSON> [ 436367.698      5012134.08133333] <-> IFC [ 436368.16695688 5012135.69142313] (dist: 1.68m, conf: 0.888)\n", "Match 2112: <PERSON><PERSON> [ 436644.2078 5011789.0356] <-> IFC [ 436644.16695688 5011791.69142313] (dist: 2.66m, conf: 0.823)\n", "Match 2113: <PERSON><PERSON> [ 436209.33355556 5012178.00533333] <-> IFC [ 436206.16695688 5012179.69142313] (dist: 3.59m, conf: 0.761)\n", "Match 2114: <PERSON><PERSON> [ 436549.197 5011919.095] <-> IFC [ 436548.16695688 5011921.69142313] (dist: 2.79m, conf: 0.814)\n", "Match 2115: <PERSON><PERSON> [ 435919.92975    5011624.60633333] <-> IFC [ 435922.16695688 5011625.69142313] (dist: 2.49m, conf: 0.834)\n", "Match 2116: <PERSON><PERSON> [ 436498.33542857 5011793.17585714] <-> IFC [ 436496.16695688 5011793.69142313] (dist: 2.23m, conf: 0.851)\n", "Match 2117: <PERSON><PERSON> [ 436189.1734 5012327.3344] <-> IFC [ 436186.16695688 5012323.69142313] (dist: 4.72m, conf: 0.685)\n", "Match 2118: <PERSON><PERSON> [ 435953.6776 5012316.9474] <-> IFC [ 435950.16695688 5012315.69142313] (dist: 3.73m, conf: 0.751)\n", "Match 2119: <PERSON><PERSON> [ 436115.60554545 5012394.94172727] <-> IFC [ 436120.16695688 5012395.69142313] (dist: 4.62m, conf: 0.692)\n", "Match 2120: <PERSON><PERSON> [ 436068.24916667 5012297.192     ] <-> IFC [ 436064.16695688 5012295.69142313] (dist: 4.35m, conf: 0.710)\n", "Match 2121: <PERSON><PERSON> [ 436163.4952 5012187.6265] <-> IFC [ 436168.16695688 5012185.69142313] (dist: 5.06m, conf: 0.663)\n", "Match 2122: <PERSON><PERSON> [ 436284.2066 5012161.7082] <-> IFC [ 436282.16695688 5012158.69142313] (dist: 3.64m, conf: 0.757)\n", "Match 2123: <PERSON><PERSON> [ 436043.06818182 5011993.50836364] <-> IFC [ 436046.16695688 5011995.69142313] (dist: 3.79m, conf: 0.747)\n", "Match 2124: <PERSON><PERSON> [ 436228.7986 5012298.1646] <-> IFC [ 436224.16695688 5012297.69142313] (dist: 4.66m, conf: 0.690)\n", "Match 2125: <PERSON><PERSON> [ 436314.6075     5012031.80083333] <-> IFC [ 436312.16695688 5012033.69142313] (dist: 3.09m, conf: 0.794)\n", "Match 2126: <PERSON><PERSON> [ 436059.6618 5012087.3632] <-> IFC [ 436056.16695688 5012086.69142313] (dist: 3.56m, conf: 0.763)\n", "Match 2127: <PERSON><PERSON> [ 436216.754      5012373.04583333] <-> IFC [ 436216.16695688 5012369.69142313] (dist: 3.41m, conf: 0.773)\n", "Match 2128: <PERSON><PERSON> [ 436132.8626 5012402.5934] <-> IFC [ 436130.16695688 5012401.69142313] (dist: 2.84m, conf: 0.810)\n", "Match 2129: <PERSON><PERSON> [ 435769.2264 5011245.2324] <-> IFC [ 435770.16695688 5011239.69142313] (dist: 5.62m, conf: 0.625)\n", "Match 2130: <PERSON><PERSON> [ 435771.0116 5012003.3544] <-> IFC [ 435770.16695688 5012003.69142313] (dist: 0.91m, conf: 0.939)\n", "Match 2131: <PERSON><PERSON> [ 436303.88566667 5012241.82666667] <-> IFC [ 436300.16695688 5012243.69142313] (dist: 4.16m, conf: 0.723)\n", "Match 2132: <PERSON><PERSON> [ 436302.489  5012346.2522] <-> IFC [ 436300.16695688 5012345.69142313] (dist: 2.39m, conf: 0.841)\n", "Match 2133: <PERSON><PERSON> [ 436363.254 5011114.92 ] <-> IFC [ 436360.16695688 5011117.69142313] (dist: 4.15m, conf: 0.723)\n", "Match 2134: <PERSON><PERSON> [ 436528.96866667 5011950.15633333] <-> IFC [ 436530.16695688 5011953.69142313] (dist: 3.73m, conf: 0.751)\n", "Match 2135: <PERSON><PERSON> [ 435561.8678 5011561.5004] <-> IFC [ 435562.16695688 5011559.69142313] (dist: 1.83m, conf: 0.878)\n", "Match 2136: <PERSON><PERSON> [ 435900.39785714 5012150.51085714] <-> IFC [ 435904.16695688 5012151.69142313] (dist: 3.95m, conf: 0.737)\n", "Match 2137: <PERSON><PERSON> [ 436292.67428571 5012303.62642857] <-> IFC [ 436292.16695688 5012297.69142313] (dist: 5.96m, conf: 0.603)\n", "Match 2138: <PERSON><PERSON> [ 436218.4494 5012151.3075] <-> IFC [ 436216.16695688 5012154.69142313] (dist: 4.08m, conf: 0.728)\n", "Match 2139: <PERSON><PERSON> [ 436245.63825 5012242.6385 ] <-> IFC [ 436244.16695688 5012245.69142313] (dist: 3.39m, conf: 0.774)\n", "Match 2140: <PERSON><PERSON> [ 436322.9538 5012260.8566] <-> IFC [ 436320.16695688 5012262.69142313] (dist: 3.34m, conf: 0.778)\n", "Match 2141: <PERSON><PERSON> [ 436222.8326 5011309.174 ] <-> IFC [ 436226.16695688 5011305.69142313] (dist: 4.82m, conf: 0.679)\n", "Match 2142: <PERSON><PERSON> [ 436273.7834 5012109.89  ] <-> IFC [ 436272.16695688 5012111.69142313] (dist: 2.42m, conf: 0.839)\n", "Match 2143: <PERSON><PERSON> [ 435450.18885714 5012028.98157143] <-> IFC [ 435448.16695688 5012031.69142313] (dist: 3.38m, conf: 0.775)\n", "Match 2144: <PERSON><PERSON> [ 436148.2154 5011035.9382] <-> IFC [ 436150.16695688 5011027.69142313] (dist: 8.47m, conf: 0.435)\n", "Match 2145: <PERSON><PERSON> [ 436339.2285 5012195.155 ] <-> IFC [ 436338.16695688 5012195.69142313] (dist: 1.19m, conf: 0.921)\n", "Match 2146: <PERSON><PERSON> [ 435337.00938462 5011905.97276923] <-> IFC [ 435334.16695688 5011907.69142313] (dist: 3.32m, conf: 0.779)\n", "Match 2147: <PERSON><PERSON> [ 436031.073  5012251.0432] <-> IFC [ 436026.16695688 5012249.69142313] (dist: 5.09m, conf: 0.661)\n", "Match 2148: <PERSON><PERSON> [ 435573.0746 5011583.895 ] <-> IFC [ 435571.16695688 5011577.51900934] (dist: 6.66m, conf: 0.556)\n", "Match 2149: <PERSON><PERSON> [ 436200.25557143 5011978.45128571] <-> IFC [ 436198.16695688 5011975.69142313] (dist: 3.46m, conf: 0.769)\n", "Match 2150: <PERSON><PERSON> [ 436291.27766667 5012239.16166667] <-> IFC [ 436292.16695688 5012239.69142313] (dist: 1.04m, conf: 0.931)\n", "Match 2151: <PERSON><PERSON> [ 436134.2868 5012159.7882] <-> IFC [ 436130.16695688 5012163.69142313] (dist: 5.68m, conf: 0.622)\n", "Match 2152: <PERSON><PERSON> [ 436200.9914 5012383.8238] <-> IFC [ 436206.16695688 5012385.69142313] (dist: 5.50m, conf: 0.633)\n", "Match 2153: <PERSON><PERSON> [ 436056.898  5012337.8262] <-> IFC [ 436054.16695688 5012341.69142313] (dist: 4.73m, conf: 0.684)\n", "Match 2154: <PERSON><PERSON> [ 436399.754    5011182.681875] <-> IFC [ 436398.16695688 5011181.69142313] (dist: 1.87m, conf: 0.875)\n", "Match 2155: <PERSON><PERSON> [ 435787.372 5011338.029] <-> IFC [ 435790.16695688 5011337.69142313] (dist: 2.82m, conf: 0.812)\n", "Match 2156: <PERSON><PERSON> [ 436095.906      5012259.78157143] <-> IFC [ 436092.16695688 5012261.69142313] (dist: 4.20m, conf: 0.720)\n", "Match 2157: <PERSON><PERSON> [ 435824.788  5011368.7442] <-> IFC [ 435828.16695688 5011371.69142313] (dist: 4.48m, conf: 0.701)\n", "Match 2158: <PERSON><PERSON> [ 436388.5644 5012260.6366] <-> IFC [ 436386.16695688 5012263.69142313] (dist: 3.88m, conf: 0.741)\n", "Match 2159: <PERSON><PERSON> [ 436351.56116667 5012207.24416667] <-> IFC [ 436348.16695688 5012209.69142313] (dist: 4.18m, conf: 0.721)\n", "Match 2160: <PERSON><PERSON> [ 435899.6124 5012063.5484] <-> IFC [ 435894.16695688 5012063.69142313] (dist: 5.45m, conf: 0.637)\n", "Match 2161: <PERSON><PERSON> [ 436030.18191667 5012256.71325   ] <-> IFC [ 436026.16695688 5012257.69142313] (dist: 4.13m, conf: 0.725)\n", "Match 2162: <PERSON><PERSON> [ 436170.574125 5012360.930375] <-> IFC [ 436168.16695688 5012363.69142313] (dist: 3.66m, conf: 0.756)\n", "Match 2163: <PERSON><PERSON> [ 436115.575  5012171.0682] <-> IFC [ 436120.16695688 5012173.69142313] (dist: 5.29m, conf: 0.647)\n", "Match 2164: <PERSON><PERSON> [ 435365.213375 5011979.96225 ] <-> IFC [ 435362.16695688 5011983.69142313] (dist: 4.82m, conf: 0.679)\n", "Match 2165: <PERSON><PERSON> [ 435423.281      5011662.51828571] <-> IFC [ 435420.16695688 5011659.69142313] (dist: 4.21m, conf: 0.720)\n", "Match 2166: <PERSON><PERSON> [ 435611.031      5011438.05266667] <-> IFC [ 435610.16695688 5011435.69142313] (dist: 2.51m, conf: 0.832)\n", "Match 2167: <PERSON><PERSON> [ 436404.96375 5012069.129  ] <-> IFC [ 436406.16695688 5012070.69142313] (dist: 1.97m, conf: 0.869)\n", "Match 2168: <PERSON><PERSON> [ 436269.195  5011168.3546] <-> IFC [ 436264.16695688 5011165.69142313] (dist: 5.69m, conf: 0.621)\n", "Match 2169: <PERSON><PERSON> [ 435889.01071429 5012255.57814286] <-> IFC [ 435892.16695688 5012259.69142313] (dist: 5.18m, conf: 0.654)\n", "Match 2170: <PERSON><PERSON> [ 436353.029      5011138.82522222] <-> IFC [ 436350.16695688 5011139.69142313] (dist: 2.99m, conf: 0.801)\n", "Match 2171: <PERSON><PERSON> [ 435780.171  5012140.1932] <-> IFC [ 435780.16695688 5012143.69142313] (dist: 3.50m, conf: 0.767)\n", "Match 2172: <PERSON><PERSON> [ 436098.3684 5012064.7122] <-> IFC [ 436094.16695688 5012067.69142313] (dist: 5.15m, conf: 0.657)\n", "Match 2173: <PERSON><PERSON> [ 436068.2636 5012266.5997] <-> IFC [ 436072.16695688 5012267.69142313] (dist: 4.05m, conf: 0.730)\n", "Match 2174: <PERSON><PERSON> [ 436004.6486 5012121.7772] <-> IFC [ 435998.16695688 5012125.69142313] (dist: 7.57m, conf: 0.495)\n", "Match 2175: <PERSON><PERSON> [ 435844.1456 5012205.6184] <-> IFC [ 435846.16695688 5012205.69142313] (dist: 2.02m, conf: 0.865)\n", "Match 2176: <PERSON><PERSON> [ 436327.3338 5011627.4582] <-> IFC [ 436334.16695688 5011629.69142313] (dist: 7.19m, conf: 0.521)\n", "Match 2177: <PERSON><PERSON> [ 435770.8637 5011562.4935] <-> IFC [ 435770.16695688 5011565.69142313] (dist: 3.27m, conf: 0.782)\n", "Match 2178: <PERSON><PERSON> [ 436320.0358 5012119.7936] <-> IFC [ 436320.16695688 5012119.69142313] (dist: 0.17m, conf: 0.989)\n", "Match 2179: <PERSON><PERSON> [ 436003.4628 5011733.6952] <-> IFC [ 436008.16695688 5011731.69142313] (dist: 5.11m, conf: 0.659)\n", "Match 2180: <PERSON><PERSON> [ 436409.43844444 5011993.01644444] <-> IFC [ 436407.16695688 5011989.69142313] (dist: 4.03m, conf: 0.732)\n", "Match 2181: <PERSON><PERSON> [ 435637.4386 5011422.4448] <-> IFC [ 435638.16695688 5011423.69142313] (dist: 1.44m, conf: 0.904)\n", "Match 2182: <PERSON><PERSON> [ 436301.8465  5012245.15275] <-> IFC [ 436300.16695688 5012243.69142313] (dist: 2.23m, conf: 0.852)\n", "Match 2183: <PERSON><PERSON> [ 436423.97816667 5012195.7895    ] <-> IFC [ 436424.16695688 5012197.69142313] (dist: 1.91m, conf: 0.873)\n", "Match 2184: <PERSON><PERSON> [ 435770.8638 5011998.414 ] <-> IFC [ 435770.16695688 5011995.69142313] (dist: 2.81m, conf: 0.813)\n", "Match 2185: <PERSON><PERSON> [ 435580.2368 5012071.2174] <-> IFC [ 435580.16695688 5012073.69142313] (dist: 2.48m, conf: 0.835)\n", "Match 2186: <PERSON><PERSON> [ 435639.771 5011448.842] <-> IFC [ 435638.16695688 5011441.69142313] (dist: 7.33m, conf: 0.511)\n", "Match 2187: <PERSON><PERSON> [ 435554.944      5011531.03477778] <-> IFC [ 435552.16695688 5011529.69142313] (dist: 3.08m, conf: 0.794)\n", "Match 2188: <PERSON><PERSON> [ 436128.5676 5011029.9025] <-> IFC [ 436132.16695688 5011031.69142313] (dist: 4.02m, conf: 0.732)\n", "Match 2189: <PERSON><PERSON> [ 436114.2952 5012271.4726] <-> IFC [ 436110.16695688 5012272.69142313] (dist: 4.30m, conf: 0.713)\n", "Match 2190: <PERSON><PERSON> [ 436279.22385714 5011105.18685714] <-> IFC [ 436284.16695688 5011103.69142313] (dist: 5.16m, conf: 0.656)\n", "Match 2191: <PERSON><PERSON> [ 436029.2195     5012302.52316667] <-> IFC [ 436026.16695688 5012299.69142313] (dist: 4.16m, conf: 0.722)\n", "Match 2192: <PERSON><PERSON> [ 436518.2975 5012047.158 ] <-> IFC [ 436520.16695688 5012043.69142313] (dist: 3.94m, conf: 0.737)\n", "Match 2193: <PERSON><PERSON> [ 435946.32366667 5012029.93016667] <-> IFC [ 435942.16695688 5012027.69142313] (dist: 4.72m, conf: 0.685)\n", "Match 2194: <PERSON><PERSON> [ 436115.31377778 5012337.03788889] <-> IFC [ 436110.16695688 5012339.69142313] (dist: 5.79m, conf: 0.614)\n", "Match 2195: <PERSON><PERSON> [ 435862.6552 5012205.1516] <-> IFC [ 435866.16695688 5012199.69142313] (dist: 6.49m, conf: 0.567)\n", "Match 2196: <PERSON><PERSON> [ 435328.9452 5011859.486 ] <-> IFC [ 435324.16695688 5011859.69142313] (dist: 4.78m, conf: 0.681)\n", "Match 2197: <PERSON><PERSON> [ 436166.8984 5011200.5402] <-> IFC [ 436170.16695688 5011197.69142313] (dist: 4.34m, conf: 0.711)\n", "Match 2198: <PERSON><PERSON> [ 436408.35225  5012061.673875] <-> IFC [ 436406.16695688 5012061.69142313] (dist: 2.19m, conf: 0.854)\n", "Match 2199: <PERSON><PERSON> [ 436348.9998 5012186.4636] <-> IFC [ 436348.16695688 5012183.69142313] (dist: 2.89m, conf: 0.807)\n", "Match 2200: <PERSON><PERSON> [ 436489.90133333 5012110.3815    ] <-> IFC [ 436490.16695688 5012113.69142313] (dist: 3.32m, conf: 0.779)\n", "Match 2201: <PERSON><PERSON> [ 436203.07711111 5011971.31033333] <-> IFC [ 436198.16695688 5011967.69142313] (dist: 6.10m, conf: 0.593)\n", "Match 2202: <PERSON><PERSON> [ 436162.20181818 5012373.56554545] <-> IFC [ 436158.16695688 5012375.69142313] (dist: 4.56m, conf: 0.696)\n", "Match 2203: <PERSON><PERSON> [ 436042.3902 5011077.6762] <-> IFC [ 436046.16695688 5011079.69142313] (dist: 4.28m, conf: 0.715)\n", "Match 2204: <PERSON><PERSON> [ 435844.60825 5011426.23425] <-> IFC [ 435846.16695688 5011427.69142313] (dist: 2.13m, conf: 0.858)\n", "Match 2205: <PERSON><PERSON> [ 436217.3772 5012174.7798] <-> IFC [ 436216.16695688 5012171.69142313] (dist: 3.32m, conf: 0.779)\n", "Match 2206: <PERSON><PERSON> [ 436320.18375 5011606.2545 ] <-> IFC [ 436316.16695688 5011609.69142313] (dist: 5.29m, conf: 0.648)\n", "Match 2207: <PERSON><PERSON> [ 436162.85983333 5012193.54416667] <-> IFC [ 436158.16695688 5012193.69142313] (dist: 4.70m, conf: 0.687)\n", "Match 2208: <PERSON><PERSON> [ 435534.60755556 5011577.44411111] <-> IFC [ 435534.16695688 5011573.69142313] (dist: 3.78m, conf: 0.748)\n", "Match 2209: <PERSON><PERSON> [ 435940.1418 5012101.3666] <-> IFC [ 435942.16695688 5012103.69142313] (dist: 3.08m, conf: 0.794)\n", "Match 2210: <PERSON><PERSON> [ 435911.6258 5011141.4064] <-> IFC [ 435914.16695688 5011133.69142313] (dist: 8.12m, conf: 0.458)\n", "Match 2211: <PERSON><PERSON> [ 436615.16   5011768.9132] <-> IFC [ 436616.16695688 5011769.69142313] (dist: 1.27m, conf: 0.915)\n", "Match 2212: <PERSON><PERSON> [ 435637.483  5011457.7656] <-> IFC [ 435638.16695688 5011465.69142313] (dist: 7.96m, conf: 0.470)\n", "Match 2213: <PERSON><PERSON> [ 435806.61166667 5011311.23283333] <-> IFC [ 435808.16695688 5011315.69142313] (dist: 4.72m, conf: 0.685)\n", "Match 2214: <PERSON><PERSON> [ 435899.9292 5012022.0308] <-> IFC [ 435904.16695688 5012021.69142313] (dist: 4.25m, conf: 0.717)\n", "Match 2215: <PERSON><PERSON> [ 436183.651 5010975.131] <-> IFC [ 436180.16695688 5010977.69142313] (dist: 4.32m, conf: 0.712)\n", "Match 2216: <PERSON><PERSON> [ 435347.5754 5011804.1686] <-> IFC [ 435344.16695688 5011803.69142313] (dist: 3.44m, conf: 0.771)\n", "Match 2217: <PERSON><PERSON> [ 436575.667  5012004.4385] <-> IFC [ 436576.16695688 5012005.69142313] (dist: 1.35m, conf: 0.910)\n", "Match 2218: <PERSON><PERSON> [ 436460.15516667 5011812.92266667] <-> IFC [ 436458.16695688 5011813.69142313] (dist: 2.13m, conf: 0.858)\n", "Match 2219: <PERSON><PERSON> [ 435402.5025  5012000.82675] <-> IFC [ 435400.16695688 5012003.69142313] (dist: 3.70m, conf: 0.754)\n", "Match 2220: <PERSON><PERSON> [ 435489.01225 5011563.2615 ] <-> IFC [ 435486.16695688 5011559.69142313] (dist: 4.57m, conf: 0.696)\n", "Match 2221: <PERSON><PERSON> [ 435467.2014 5011665.6508] <-> IFC [ 435466.16695688 5011667.69142313] (dist: 2.29m, conf: 0.847)\n", "Match 2222: <PERSON><PERSON> [ 436302.2464 5012255.9126] <-> IFC [ 436300.16695688 5012253.69142313] (dist: 3.04m, conf: 0.797)\n", "Match 2223: <PERSON><PERSON> [ 436189.8338 5012323.808 ] <-> IFC [ 436186.16695688 5012323.69142313] (dist: 3.67m, conf: 0.755)\n", "Match 2224: <PERSON><PERSON> [ 436226.5924 5012332.3844] <-> IFC [ 436224.16695688 5012333.69142313] (dist: 2.76m, conf: 0.816)\n", "Match 2225: <PERSON><PERSON> [ 435911.428   5012131.66675] <-> IFC [ 435904.16695688 5012127.69142313] (dist: 8.28m, conf: 0.448)\n", "Match 2226: <PERSON><PERSON> [ 436584.03575 5011993.76   ] <-> IFC [ 436586.16695688 5011993.69142313] (dist: 2.13m, conf: 0.858)\n", "Match 2227: <PERSON><PERSON> [ 436360.585      5012140.48766667] <-> IFC [ 436358.16695688 5012139.69142313] (dist: 2.55m, conf: 0.830)\n", "Match 2228: <PERSON><PERSON> [ 435976.9387 5012044.4278] <-> IFC [ 435980.16695688 5012045.69142313] (dist: 3.47m, conf: 0.769)\n", "Match 2229: <PERSON><PERSON> [ 436124.61355556 5012177.198     ] <-> IFC [ 436120.16695688 5012173.69142313] (dist: 5.66m, conf: 0.622)\n", "Match 2230: <PERSON><PERSON> [ 436316.42983333 5011136.95133333] <-> IFC [ 436312.16695688 5011135.69142313] (dist: 4.45m, conf: 0.704)\n", "Match 2231: <PERSON><PERSON> [ 436490.1352 5012041.9832] <-> IFC [ 436490.16695688 5012037.69142313] (dist: 4.29m, conf: 0.714)\n", "Match 2232: <PERSON><PERSON> [ 435873.10966667 5011477.34366667] <-> IFC [ 435876.16695688 5011483.69142313] (dist: 7.05m, conf: 0.530)\n", "Match 2233: <PERSON><PERSON> [ 436097.03525 5012362.10475] <-> IFC [ 436092.16695688 5012363.69142313] (dist: 5.12m, conf: 0.659)\n", "Match 2234: <PERSON><PERSON> [ 435451.00833333 5011653.98966667] <-> IFC [ 435448.16695688 5011657.69142313] (dist: 4.67m, conf: 0.689)\n", "Match 2235: <PERSON><PERSON> [ 435796.86675 5011344.63975] <-> IFC [ 435790.16695688 5011345.69142313] (dist: 6.78m, conf: 0.548)\n", "Match 2236: <PERSON><PERSON> [ 435478.0944 5011782.0228] <-> IFC [ 435476.16695688 5011783.69142313] (dist: 2.55m, conf: 0.830)\n", "Match 2237: <PERSON><PERSON> [ 436539.0285     5012010.82133333] <-> IFC [ 436538.16695688 5012011.69142313] (dist: 1.22m, conf: 0.918)\n", "Match 2238: <PERSON><PERSON> [ 436257.1388 5012347.786 ] <-> IFC [ 436254.16695688 5012345.69142313] (dist: 3.64m, conf: 0.758)\n", "Match 2239: <PERSON><PERSON> [ 436494.67533333 5011927.66      ] <-> IFC [ 436492.16695688 5011925.69142313] (dist: 3.19m, conf: 0.787)\n", "Match 2240: <PERSON><PERSON> [ 436245.7905  5012290.51425] <-> IFC [ 436244.16695688 5012288.69142313] (dist: 2.44m, conf: 0.837)\n", "Match 2241: <PERSON><PERSON> [ 436522.042875 5011871.7935  ] <-> IFC [ 436520.16695688 5011871.69142313] (dist: 1.88m, conf: 0.875)\n", "Match 2242: <PERSON><PERSON> [ 435808.853 5011449.762] <-> IFC [ 435808.16695688 5011449.69142313] (dist: 0.69m, conf: 0.954)\n", "Match 2243: <PERSON><PERSON> [ 435814.507  5012208.4692] <-> IFC [ 435818.16695688 5012209.69142313] (dist: 3.86m, conf: 0.743)\n", "Match 2244: <PERSON><PERSON> [ 436688.8858 5011841.8066] <-> IFC [ 436692.16695688 5011843.69142313] (dist: 3.78m, conf: 0.748)\n", "Match 2245: <PERSON><PERSON> [ 436282.8175 5012265.2035] <-> IFC [ 436282.16695688 5012267.69142313] (dist: 2.57m, conf: 0.829)\n", "Match 2246: <PERSON><PERSON> [ 436379.90833333 5011171.73516667] <-> IFC [ 436378.16695688 5011175.69142313] (dist: 4.32m, conf: 0.712)\n", "Match 2247: <PERSON><PERSON> [ 436631.6866 5011914.9508] <-> IFC [ 436634.16695688 5011913.69142313] (dist: 2.78m, conf: 0.815)\n", "Match 2248: <PERSON><PERSON> [ 436287.5742 5012047.193 ] <-> IFC [ 436284.16695688 5012043.69142313] (dist: 4.89m, conf: 0.674)\n", "Match 2249: <PERSON><PERSON> [ 435871.3924 5012167.2122] <-> IFC [ 435866.16695688 5012165.69142313] (dist: 5.44m, conf: 0.637)\n", "Match 2250: <PERSON><PERSON> [ 436218.02242857 5012381.14042857] <-> IFC [ 436216.16695688 5012377.69142313] (dist: 3.92m, conf: 0.739)\n", "Match 2251: <PERSON><PERSON> [ 435414.244 5011668.449] <-> IFC [ 435410.16695688 5011667.69142313] (dist: 4.15m, conf: 0.724)\n", "Match 2252: <PERSON><PERSON> [ 435401.3582 5011772.0132] <-> IFC [ 435400.16695688 5011771.69142313] (dist: 1.23m, conf: 0.918)\n", "Match 2253: <PERSON><PERSON> [ 436319.7196 5011630.1074] <-> IFC [ 436316.16695688 5011627.69142313] (dist: 4.30m, conf: 0.714)\n", "Match 2254: <PERSON><PERSON> [ 435740.9054 5011461.5534] <-> IFC [ 435742.16695688 5011463.69142313] (dist: 2.48m, conf: 0.835)\n", "Match 2255: <PERSON><PERSON> [ 436492.475      5011938.75933333] <-> IFC [ 436492.16695688 5011941.69142313] (dist: 2.95m, conf: 0.803)\n", "Match 2256: <PERSON><PERSON> [ 435991.8186 5012332.2906] <-> IFC [ 435988.16695688 5012329.69142313] (dist: 4.48m, conf: 0.701)\n", "Match 2257: <PERSON><PERSON> [ 436209.85157143 5012253.17885714] <-> IFC [ 436206.16695688 5012255.69142313] (dist: 4.46m, conf: 0.703)\n", "Match 2258: <PERSON><PERSON> [ 436423.8788 5012082.3694] <-> IFC [ 436424.16695688 5012086.69142313] (dist: 4.33m, conf: 0.711)\n", "Match 2259: <PERSON><PERSON> [ 435663.76571429 5012160.59714286] <-> IFC [ 435666.16695688 5012155.69142313] (dist: 5.46m, conf: 0.636)\n", "Match 2260: <PERSON><PERSON> [ 435571.48433333 5011633.95733333] <-> IFC [ 435572.16695688 5011635.69142313] (dist: 1.86m, conf: 0.876)\n", "Match 2261: <PERSON><PERSON> [ 436022.4732 5012332.3912] <-> IFC [ 436026.16695688 5012333.69142313] (dist: 3.92m, conf: 0.739)\n", "Match 2262: <PERSON><PERSON> [ 435902.48875 5012166.4355 ] <-> IFC [ 435904.16695688 5012169.69142313] (dist: 3.66m, conf: 0.756)\n", "Match 2263: <PERSON><PERSON> [ 436540.911   5011829.01025] <-> IFC [ 436540.16695688 5011831.69142313] (dist: 2.78m, conf: 0.815)\n", "Match 2264: <PERSON><PERSON> [ 435780.8314 5011570.921 ] <-> IFC [ 435780.16695688 5011571.69142313] (dist: 1.02m, conf: 0.932)\n", "Match 2265: <PERSON><PERSON> [ 436679.9812 5011868.775 ] <-> IFC [ 436682.16695688 5011865.69142313] (dist: 3.78m, conf: 0.748)\n", "Match 2266: <PERSON><PERSON> [ 436597.29683333 5011903.2365    ] <-> IFC [ 436596.16695688 5011903.69142313] (dist: 1.22m, conf: 0.919)\n", "Match 2267: <PERSON><PERSON> [ 435946.6544 5012277.8246] <-> IFC [ 435950.16695688 5012273.69142313] (dist: 5.42m, conf: 0.638)\n", "Match 2268: <PERSON><PERSON> [ 435666.7886 5012162.6008] <-> IFC [ 435666.16695688 5012155.69142313] (dist: 6.94m, conf: 0.538)\n", "Match 2269: <PERSON><PERSON> [ 436435.5766 5012199.4464] <-> IFC [ 436434.16695688 5012195.69142313] (dist: 4.01m, conf: 0.733)\n", "Match 2270: <PERSON><PERSON> [ 436057.6228 5012211.0708] <-> IFC [ 436054.16695688 5012206.69142313] (dist: 5.58m, conf: 0.628)\n", "Match 2271: <PERSON><PERSON> [ 436633.5205  5011800.42725] <-> IFC [ 436634.16695688 5011803.69142313] (dist: 3.33m, conf: 0.778)\n", "Match 2272: <PERSON><PERSON> [ 435609.048   5011447.82025] <-> IFC [ 435610.16695688 5011451.69142313] (dist: 4.03m, conf: 0.731)\n", "Match 2273: <PERSON><PERSON> [ 435842.726  5012048.5365] <-> IFC [ 435846.16695688 5012045.69142313] (dist: 4.46m, conf: 0.702)\n", "Match 2274: <PERSON><PERSON> [ 436189.2956 5012285.2882] <-> IFC [ 436186.16695688 5012281.69142313] (dist: 4.77m, conf: 0.682)\n", "Match 2275: <PERSON><PERSON> [ 435326.616  5011855.2124] <-> IFC [ 435324.16695688 5011851.69142313] (dist: 4.29m, conf: 0.714)\n", "Match 2276: <PERSON><PERSON> [ 435442.4272 5011654.0924] <-> IFC [ 435438.16695688 5011653.69142313] (dist: 4.28m, conf: 0.715)\n", "Match 2277: <PERSON><PERSON> [ 436050.0885  5012055.22275] <-> IFC [ 436046.16695688 5012055.69142313] (dist: 3.95m, conf: 0.737)\n", "Match 2278: <PERSON><PERSON> [ 435987.27733333 5011693.05483333] <-> IFC [ 435989.16695688 5011687.69142313] (dist: 5.69m, conf: 0.621)\n", "Match 2279: <PERSON><PERSON> [ 435984.5926 5012316.514 ] <-> IFC [ 435988.16695688 5012311.69142313] (dist: 6.00m, conf: 0.600)\n", "Match 2280: <PERSON><PERSON> [ 435514.4924 5011661.1396] <-> IFC [ 435514.16695688 5011658.69142313] (dist: 2.47m, conf: 0.835)\n", "Match 2281: <PERSON><PERSON> [ 436075.53833333 5012216.833     ] <-> IFC [ 436072.16695688 5012217.69142313] (dist: 3.48m, conf: 0.768)\n", "Match 2282: <PERSON><PERSON> [ 435732.6104 5011978.9948] <-> IFC [ 435732.16695688 5011983.69142313] (dist: 4.72m, conf: 0.685)\n", "Match 2283: <PERSON><PERSON> [ 435909.24316667 5012012.2215    ] <-> IFC [ 435904.16695688 5012013.69142313] (dist: 5.28m, conf: 0.648)\n", "Match 2284: <PERSON><PERSON> [ 436219.76166667 5012368.19633333] <-> IFC [ 436216.16695688 5012369.69142313] (dist: 3.89m, conf: 0.740)\n", "Match 2285: <PERSON><PERSON> [ 435457.7374 5012011.3994] <-> IFC [ 435458.16695688 5012011.69142313] (dist: 0.52m, conf: 0.965)\n", "Match 2286: <PERSON><PERSON> [ 435789.037  5011263.8266] <-> IFC [ 435780.16695688 5011261.69142313] (dist: 9.12m, conf: 0.392)\n", "Match 2287: <PERSON><PERSON> [ 435974.5388 5012035.7832] <-> IFC [ 435970.16695688 5012037.69142313] (dist: 4.77m, conf: 0.682)\n", "Match 2288: <PERSON><PERSON> [ 435739.8476 5011401.431 ] <-> IFC [ 435742.16695688 5011397.69142313] (dist: 4.40m, conf: 0.707)\n", "Match 2289: <PERSON><PERSON> [ 436330.26233333 5011662.05316667] <-> IFC [ 436326.16695688 5011661.69142313] (dist: 4.11m, conf: 0.726)\n", "Match 2290: <PERSON><PERSON> [ 435984.45  5012060.842] <-> IFC [ 435980.16695688 5012061.69142313] (dist: 4.37m, conf: 0.709)\n", "Match 2291: <PERSON><PERSON> [ 435574.38866667 5011537.9485    ] <-> IFC [ 435572.16695688 5011541.69142313] (dist: 4.35m, conf: 0.710)\n", "Match 2292: <PERSON><PERSON> [ 436010.87   5012269.0755] <-> IFC [ 436016.16695688 5012269.69142313] (dist: 5.33m, conf: 0.644)\n", "Match 2293: <PERSON><PERSON> [ 435844.184  5011435.5116] <-> IFC [ 435846.16695688 5011435.69142313] (dist: 1.99m, conf: 0.867)\n", "Match 2294: <PERSON><PERSON> [ 435826.152  5011967.4512] <-> IFC [ 435828.16695688 5011965.69142313] (dist: 2.68m, conf: 0.822)\n", "Match 2295: <PERSON><PERSON> [ 436465.23025 5012070.859  ] <-> IFC [ 436462.16695688 5012073.69142313] (dist: 4.17m, conf: 0.722)\n", "Match 2296: <PERSON><PERSON> [ 435986.7305  5011240.77175] <-> IFC [ 435980.16695688 5011245.69142313] (dist: 8.20m, conf: 0.453)\n", "Match 2297: <PERSON><PERSON> [ 436259.3175 5012025.696 ] <-> IFC [ 436255.16695688 5012029.69142313] (dist: 5.76m, conf: 0.616)\n", "Match 2298: <PERSON><PERSON> [ 436172.491  5012276.6315] <-> IFC [ 436168.16695688 5012279.69142313] (dist: 5.30m, conf: 0.647)\n", "Match 2299: <PERSON><PERSON> [ 435993.9524 5011720.8648] <-> IFC [ 435998.16695688 5011719.69142313] (dist: 4.37m, conf: 0.708)\n", "Match 2300: <PERSON><PERSON> [ 435769.2655 5011402.874 ] <-> IFC [ 435770.16695688 5011399.69142313] (dist: 3.31m, conf: 0.779)\n", "Match 2301: <PERSON><PERSON> [ 436119.5384 5010966.0876] <-> IFC [ 436122.16695688 5010969.69142313] (dist: 4.46m, conf: 0.703)\n", "Match 2302: <PERSON><PERSON> [ 436068.7624 5012082.6868] <-> IFC [ 436065.16695688 5012087.69142314] (dist: 6.16m, conf: 0.589)\n", "Match 2303: <PERSON><PERSON> [ 436577.0678 5011833.0384] <-> IFC [ 436578.16695688 5011833.69142313] (dist: 1.28m, conf: 0.915)\n", "Match 2304: <PERSON><PERSON> [ 435533.5675  5011672.49075] <-> IFC [ 435534.16695688 5011667.69142313] (dist: 4.84m, conf: 0.678)\n", "Match 2305: <PERSON><PERSON> [ 435977.792      5011574.34966667] <-> IFC [ 435980.16695688 5011571.69142313] (dist: 3.56m, conf: 0.762)\n", "Match 2306: <PERSON><PERSON> [ 436263.537   5012294.92075] <-> IFC [ 436262.16695688 5012291.69142313] (dist: 3.51m, conf: 0.766)\n", "Match 2307: <PERSON><PERSON> [ 436221.012 5011941.284] <-> IFC [ 436226.16695688 5011939.69142313] (dist: 5.40m, conf: 0.640)\n", "Match 2308: <PERSON><PERSON> [ 436316.777 5011629.669] <-> IFC [ 436316.16695688 5011627.69142313] (dist: 2.07m, conf: 0.862)\n", "Match 2309: Drone [ 435801.078 5011863.384] <-> IFC [ 435798.16695688 5011861.69142313] (dist: 3.37m, conf: 0.776)\n", "Match 2310: <PERSON><PERSON> [ 435978.114 5011428.237] <-> IFC [ 435980.16695688 5011425.69142313] (dist: 3.27m, conf: 0.782)\n", "Match 2311: <PERSON><PERSON> [ 436470.256 5011680.666] <-> IFC [ 436468.16695688 5011681.69142313] (dist: 2.33m, conf: 0.845)\n", "Match 2312: <PERSON><PERSON> [ 436191.138 5011593.247] <-> IFC [ 436192.16695688 5011593.69142313] (dist: 1.12m, conf: 0.925)\n", "Match 2313: <PERSON><PERSON> [ 435552.246 5011560.102] <-> IFC [ 435552.16695688 5011563.69142313] (dist: 3.59m, conf: 0.761)\n", "Match 2314: <PERSON><PERSON> [ 436451.789 5011665.025] <-> IFC [ 436450.16695688 5011665.69142313] (dist: 1.75m, conf: 0.883)\n", "Match 2315: <PERSON><PERSON> [ 436010.907 5011620.053] <-> IFC [ 436008.16695688 5011623.69142313] (dist: 4.55m, conf: 0.696)\n", "Match 2316: <PERSON><PERSON> [ 435969.132 5011979.947] <-> IFC [ 435970.16695688 5011978.69142313] (dist: 1.63m, conf: 0.892)\n", "Match 2317: <PERSON><PERSON> [ 435990.479 5011055.96 ] <-> IFC [ 435990.16695688 5011063.69142313] (dist: 7.74m, conf: 0.484)\n", "Match 2318: <PERSON><PERSON> [ 436035.2   5011955.929] <-> IFC [ 436036.16695688 5011954.69142313] (dist: 1.57m, conf: 0.895)\n", "Match 2319: <PERSON><PERSON> [ 436706.728 5011839.849] <-> IFC [ 436710.16695688 5011837.69142313] (dist: 4.06m, conf: 0.729)\n", "Match 2320: <PERSON><PERSON> [ 435873.303 5011809.734] <-> IFC [ 435875.16695688 5011813.69142313] (dist: 4.37m, conf: 0.708)\n", "Match 2321: <PERSON><PERSON> [ 435338.722 5011698.509] <-> IFC [ 435344.16695688 5011695.69142313] (dist: 6.13m, conf: 0.591)\n", "Match 2322: <PERSON><PERSON> [ 436034.901 5011989.32 ] <-> IFC [ 436036.16695688 5011988.69142313] (dist: 1.41m, conf: 0.906)\n", "Match 2323: <PERSON><PERSON> [ 435504.573 5011956.389] <-> IFC [ 435504.16695688 5011953.69142313] (dist: 2.73m, conf: 0.818)\n", "Match 2324: <PERSON><PERSON> [ 435570.645 5011476.143] <-> IFC [ 435572.16695688 5011473.69142313] (dist: 2.89m, conf: 0.808)\n", "Match 2325: <PERSON><PERSON> [ 435924.427 5011383.481] <-> IFC [ 435922.16695688 5011381.69142313] (dist: 2.88m, conf: 0.808)\n", "Match 2326: <PERSON><PERSON> [ 435992.91  5011716.193] <-> IFC [ 435990.16695688 5011717.69142313] (dist: 3.13m, conf: 0.792)\n", "Match 2327: <PERSON><PERSON> [ 436232.959 5011149.572] <-> IFC [ 436236.16695688 5011149.69142313] (dist: 3.21m, conf: 0.786)\n", "Match 2328: <PERSON><PERSON> [ 436365.019 5011230.494] <-> IFC [ 436369.16695688 5011231.69142313] (dist: 4.32m, conf: 0.712)\n", "Match 2329: <PERSON><PERSON> [ 436262.86  5011515.688] <-> IFC [ 436268.16695688 5011513.69142313] (dist: 5.67m, conf: 0.622)\n", "Match 2330: <PERSON><PERSON> [ 435588.486 5011782.696] <-> IFC [ 435590.16695688 5011783.69142313] (dist: 1.95m, conf: 0.870)\n", "Match 2331: <PERSON><PERSON> [ 435960.16 5011134.4 ] <-> IFC [ 435960.16695688 5011135.69142313] (dist: 1.29m, conf: 0.914)\n", "Match 2332: <PERSON><PERSON> [ 435471.467 5011620.479] <-> IFC [ 435476.16695688 5011621.69142313] (dist: 4.85m, conf: 0.676)\n", "Match 2333: <PERSON><PERSON> [ 435929.799 5011488.078] <-> IFC [ 435932.16695688 5011485.69142313] (dist: 3.36m, conf: 0.776)\n", "Match 2334: <PERSON><PERSON> [ 436001.743 5011446.028] <-> IFC [ 435998.16695688 5011441.69142313] (dist: 5.62m, conf: 0.625)\n", "Match 2335: <PERSON><PERSON> [ 435960.095 5011647.053] <-> IFC [ 435960.16695688 5011647.69142313] (dist: 0.64m, conf: 0.957)\n", "Match 2336: <PERSON><PERSON> [ 436152.479 5012391.801] <-> IFC [ 436158.16695688 5012391.69142313] (dist: 5.69m, conf: 0.621)\n", "Match 2337: Drone [ 435978.501 5012130.604] <-> IFC [ 435980.16695688 5012131.69142313] (dist: 1.99m, conf: 0.867)\n", "Match 2338: <PERSON><PERSON> [ 435581.761 5011812.271] <-> IFC [ 435580.16695688 5011815.69142313] (dist: 3.77m, conf: 0.748)\n", "Match 2339: <PERSON><PERSON> [ 436122.637 5011134.118] <-> IFC [ 436122.16695688 5011137.69142313] (dist: 3.60m, conf: 0.760)\n", "Match 2340: <PERSON><PERSON> [ 435459.556 5011566.597] <-> IFC [ 435466.16695688 5011567.69142313] (dist: 6.70m, conf: 0.553)\n", "Match 2341: <PERSON><PERSON> [ 435695.907 5011965.106] <-> IFC [ 435694.16695688 5011963.69142313] (dist: 2.24m, conf: 0.851)\n", "Match 2342: <PERSON><PERSON> [ 436314.461 5012286.004] <-> IFC [ 436310.16695688 5012283.69142313] (dist: 4.88m, conf: 0.675)\n", "Match 2343: <PERSON><PERSON> [ 435798.028 5011425.764] <-> IFC [ 435800.16695688 5011429.69142313] (dist: 4.47m, conf: 0.702)\n", "Match 2344: <PERSON><PERSON> [ 435843.042 5011473.148] <-> IFC [ 435846.16695688 5011469.69142313] (dist: 4.66m, conf: 0.689)\n", "Match 2345: <PERSON><PERSON> [ 436338.088 5011532.971] <-> IFC [ 436334.16695688 5011535.69142313] (dist: 4.77m, conf: 0.682)\n", "Match 2346: <PERSON><PERSON> [ 435855.092 5011854.381] <-> IFC [ 435856.16695688 5011855.69142313] (dist: 1.69m, conf: 0.887)\n", "Match 2347: <PERSON><PERSON> [ 436128.218 5012376.825] <-> IFC [ 436130.16695688 5012375.69142313] (dist: 2.25m, conf: 0.850)\n", "Match 2348: <PERSON><PERSON> [ 436140.364 5011449.385] <-> IFC [ 436141.16695688 5011452.69142313] (dist: 3.40m, conf: 0.773)\n", "Match 2349: <PERSON><PERSON> [ 435687.421 5011404.114] <-> IFC [ 435685.39772611 5011408.4606539 ] (dist: 4.79m, conf: 0.680)\n", "Match 2350: <PERSON><PERSON> [ 435789.419 5011185.58 ] <-> IFC [ 435799.76695688 5011186.09142313] (dist: 10.36m, conf: 0.309)\n", "Match 2351: <PERSON><PERSON> [ 436199.842 5012154.447] <-> IFC [ 436206.16695688 5012153.69142313] (dist: 6.37m, conf: 0.575)\n", "Match 2352: <PERSON><PERSON> [ 436068.657 5011545.368] <-> IFC [ 436074.16695688 5011543.69142313] (dist: 5.76m, conf: 0.616)\n", "Match 2353: <PERSON><PERSON> [ 435825.091 5011701.798] <-> IFC [ 435828.16695688 5011699.69142313] (dist: 3.73m, conf: 0.751)\n", "Match 2354: <PERSON><PERSON> [ 435372.264 5011761.099] <-> IFC [ 435372.16695688 5011763.69142313] (dist: 2.59m, conf: 0.827)\n", "Match 2355: <PERSON><PERSON> [ 435552.438 5011680.535] <-> IFC [ 435552.16695688 5011683.69142313] (dist: 3.17m, conf: 0.789)\n", "Match 2356: <PERSON><PERSON> [ 435873.323 5011197.709] <-> IFC [ 435876.16695688 5011205.69142313] (dist: 8.47m, conf: 0.435)\n", "Match 2357: <PERSON><PERSON> [ 435393.572 5011872.125] <-> IFC [ 435390.16695688 5011873.69142313] (dist: 3.75m, conf: 0.750)\n", "Match 2358: <PERSON><PERSON> [ 436260.078 5011116.457] <-> IFC [ 436256.16695688 5011115.69142313] (dist: 3.99m, conf: 0.734)\n", "Match 2359: <PERSON><PERSON> [ 436233.309 5012358.499] <-> IFC [ 436234.16695688 5012361.69142313] (dist: 3.31m, conf: 0.780)\n", "Match 2360: <PERSON><PERSON> [ 435947.869 5011995.171] <-> IFC [ 435942.16695688 5011993.69142313] (dist: 5.89m, conf: 0.607)\n", "Match 2361: <PERSON><PERSON> [ 436074.412 5012073.099] <-> IFC [ 436074.16695688 5012071.69142313] (dist: 1.43m, conf: 0.905)\n", "Match 2362: <PERSON><PERSON> [ 435429.175 5011812.205] <-> IFC [ 435420.16695688 5011811.69142313] (dist: 9.02m, conf: 0.398)\n", "Match 2363: <PERSON><PERSON> [ 435785.8  5011473.25] <-> IFC [ 435780.16695688 5011479.69142313] (dist: 8.56m, conf: 0.430)\n", "Match 2364: <PERSON><PERSON> [ 436302.172 5011800.574] <-> IFC [ 436306.16695688 5011799.69142313] (dist: 4.09m, conf: 0.727)\n", "Match 2365: <PERSON><PERSON> [ 435861.066 5011275.828] <-> IFC [ 435856.16695688 5011277.69142313] (dist: 5.24m, conf: 0.651)\n", "Match 2366: <PERSON><PERSON> [ 436329.151 5011665.235] <-> IFC [ 436326.16695688 5011661.69142313] (dist: 4.63m, conf: 0.691)\n", "Match 2367: <PERSON><PERSON> [ 436257.538 5012205.82 ] <-> IFC [ 436254.16695688 5012203.69142313] (dist: 3.99m, conf: 0.734)\n", "Match 2368: <PERSON><PERSON> [ 435845.995 5011362.494] <-> IFC [ 435846.16695688 5011359.69142313] (dist: 2.81m, conf: 0.813)\n", "Match 2369: <PERSON><PERSON> [ 435612.343 5011539.561] <-> IFC [ 435618.16695688 5011541.69142313] (dist: 6.20m, conf: 0.587)\n", "Match 2370: <PERSON><PERSON> [ 435693.376 5011458.608] <-> IFC [ 435694.16695688 5011457.69142313] (dist: 1.21m, conf: 0.919)\n", "Match 2371: <PERSON><PERSON> [ 435705.189 5011410.601] <-> IFC [ 435704.16695688 5011405.69142313] (dist: 5.01m, conf: 0.666)\n", "Match 2372: <PERSON><PERSON> [ 436164.309 5012430.142] <-> IFC [ 436168.16695688 5012431.69142313] (dist: 4.16m, conf: 0.723)\n", "Match 2373: <PERSON><PERSON> [ 436014.159 5011488.189] <-> IFC [ 436018.16695688 5011491.69142313] (dist: 5.32m, conf: 0.645)\n", "Match 2374: <PERSON><PERSON> [ 436706.691 5011836.692] <-> IFC [ 436710.16695688 5011837.69142313] (dist: 3.62m, conf: 0.759)\n", "Match 2375: <PERSON><PERSON> [ 435861.028 5011188.457] <-> IFC [ 435856.16695688 5011187.69142313] (dist: 4.92m, conf: 0.672)\n", "Match 2376: <PERSON><PERSON> [ 436121.947 5010980.908] <-> IFC [ 436122.16695688 5010978.69142313] (dist: 2.23m, conf: 0.852)\n", "Match 2377: <PERSON><PERSON> [ 435930.158 5011623.048] <-> IFC [ 435932.16695688 5011619.69142313] (dist: 3.91m, conf: 0.739)\n", "Match 2378: <PERSON><PERSON> [ 436104.045 5010966.831] <-> IFC [ 436094.16695688 5010967.69142313] (dist: 9.92m, conf: 0.339)\n", "Match 2379: <PERSON><PERSON> [ 436440.517 5011659.572] <-> IFC [ 436440.16695688 5011657.69142313] (dist: 1.91m, conf: 0.872)\n", "Match 2380: <PERSON><PERSON> [ 436049.827 5012229.325] <-> IFC [ 436054.16695688 5012231.69142313] (dist: 4.94m, conf: 0.670)\n", "Match 2381: <PERSON><PERSON> [ 436244.934 5012231.955] <-> IFC [ 436244.16695688 5012229.69142313] (dist: 2.39m, conf: 0.841)\n", "Match 2382: <PERSON><PERSON> [ 435789.189 5011191.068] <-> IFC [ 435790.16695688 5011199.69142313] (dist: 8.68m, conf: 0.421)\n", "Match 2383: <PERSON><PERSON> [ 436206.012 5011701.852] <-> IFC [ 436202.16695688 5011701.69142313] (dist: 3.85m, conf: 0.743)\n", "Match 2384: <PERSON><PERSON> [ 436083.593 5010897.845] <-> IFC [ 436084.16695688 5010901.69142313] (dist: 3.89m, conf: 0.741)\n", "Match 2385: <PERSON><PERSON> [ 436014.267 5012298.134] <-> IFC [ 436016.16695688 5012295.69142313] (dist: 3.09m, conf: 0.794)\n", "Match 2386: <PERSON><PERSON> [ 436071.406 5010921.33 ] <-> IFC [ 436074.16695688 5010921.69142313] (dist: 2.78m, conf: 0.814)\n", "Match 2387: <PERSON><PERSON> [ 436125.102 5012352.049] <-> IFC [ 436120.16695688 5012353.69142313] (dist: 5.20m, conf: 0.653)\n", "Match 2388: <PERSON><PERSON> [ 436472.813 5012031.11 ] <-> IFC [ 436472.16695688 5012029.69142313] (dist: 1.56m, conf: 0.896)\n", "Match 2389: <PERSON><PERSON> [ 436388.89  5012160.205] <-> IFC [ 436386.16695688 5012161.69142313] (dist: 3.10m, conf: 0.793)\n", "Match 2390: <PERSON><PERSON> [ 436380.276 5012196.077] <-> IFC [ 436376.16695688 5012199.69142313] (dist: 5.47m, conf: 0.635)\n", "Match 2391: <PERSON><PERSON> [ 435965.813 5011938.263] <-> IFC [ 435970.16695688 5011935.69142313] (dist: 5.06m, conf: 0.663)\n", "Match 2392: <PERSON><PERSON> [ 436179.019 5012160.566] <-> IFC [ 436178.16695688 5012157.69142313] (dist: 3.00m, conf: 0.800)\n", "Match 2393: <PERSON><PERSON> [ 436086.324 5012370.83 ] <-> IFC [ 436082.16695688 5012373.69142313] (dist: 5.05m, conf: 0.664)\n", "Match 2394: <PERSON><PERSON> [ 436098.405 5012253.727] <-> IFC [ 436102.16695688 5012257.69142313] (dist: 5.47m, conf: 0.636)\n", "Match 2395: <PERSON><PERSON> [ 435471.02 5011635.86] <-> IFC [ 435466.16695688 5011633.69142313] (dist: 5.32m, conf: 0.646)\n", "Match 2396: <PERSON><PERSON> [ 436020.632 5011544.879] <-> IFC [ 436018.16695688 5011541.69142313] (dist: 4.03m, conf: 0.731)\n", "Match 2397: <PERSON><PERSON> [ 436203.468 5011656.227] <-> IFC [ 436202.16695688 5011659.69142313] (dist: 3.70m, conf: 0.753)\n", "Match 2398: <PERSON><PERSON> [ 435761.947 5011275.574] <-> IFC [ 435761.16695688 5011281.69142313] (dist: 6.17m, conf: 0.589)\n", "Match 2399: <PERSON><PERSON> [ 436176.231 5011464.313] <-> IFC [ 436180.16695688 5011463.69142313] (dist: 3.98m, conf: 0.734)\n", "Match 2400: <PERSON><PERSON> [ 436079.754 5012250.481] <-> IFC [ 436082.16695688 5012247.69142313] (dist: 3.69m, conf: 0.754)\n", "Match 2401: <PERSON><PERSON> [ 436013.918 5012145.114] <-> IFC [ 436018.16695688 5012143.69142313] (dist: 4.48m, conf: 0.701)\n", "Match 2402: <PERSON><PERSON> [ 436146.334 5011863.192] <-> IFC [ 436150.16695688 5011863.69142313] (dist: 3.87m, conf: 0.742)\n", "Match 2403: <PERSON><PERSON> [ 435899.881 5012070.354] <-> IFC [ 435904.16695688 5012067.69142313] (dist: 5.05m, conf: 0.664)\n", "Match 2404: <PERSON><PERSON> [ 435975.347 5011089.713] <-> IFC [ 435970.16695688 5011089.69142313] (dist: 5.18m, conf: 0.655)\n", "Match 2405: <PERSON><PERSON> [ 435675.532 5011950.619] <-> IFC [ 435676.16695688 5011949.69142313] (dist: 1.12m, conf: 0.925)\n", "Match 2406: <PERSON><PERSON> [ 435794.933 5011182.755] <-> IFC [ 435799.76695688 5011186.09142313] (dist: 5.87m, conf: 0.608)\n", "Match 2407: <PERSON><PERSON> [ 436326.589 5011569.209] <-> IFC [ 436326.16695688 5011569.69142313] (dist: 0.64m, conf: 0.957)\n", "Match 2408: Dr<PERSON> [ 436062.078 5011536.592] <-> IFC [ 436065.50029021 5011540.3580898 ] (dist: 5.09m, conf: 0.661)\n", "Match 2409: <PERSON><PERSON> [ 435579.186 5011872.288] <-> IFC [ 435580.16695688 5011873.69142313] (dist: 1.71m, conf: 0.886)\n", "Match 2410: <PERSON><PERSON> [ 436256.995 5011176.792] <-> IFC [ 436256.16695688 5011173.69142313] (dist: 3.21m, conf: 0.786)\n", "Match 2411: <PERSON><PERSON> [ 435957.372 5011938.084] <-> IFC [ 435960.16695688 5011938.69142313] (dist: 2.86m, conf: 0.809)\n", "Match 2412: <PERSON><PERSON> [ 435560.9   5012070.572] <-> IFC [ 435562.16695688 5012071.69142313] (dist: 1.69m, conf: 0.887)\n", "Match 2413: <PERSON><PERSON> [ 436356.5   5011728.089] <-> IFC [ 436354.16695688 5011729.69142313] (dist: 2.83m, conf: 0.811)\n", "Match 2414: <PERSON><PERSON> [ 435833.967 5011269.239] <-> IFC [ 435838.16695688 5011269.69142313] (dist: 4.22m, conf: 0.718)\n", "Match 2415: <PERSON><PERSON> [ 435993.409 5011059.209] <-> IFC [ 435990.16695688 5011063.69142313] (dist: 5.53m, conf: 0.631)\n", "Match 2416: <PERSON><PERSON> [ 435935.849 5011107.575] <-> IFC [ 435932.16695688 5011109.69142313] (dist: 4.25m, conf: 0.717)\n", "Match 2417: <PERSON><PERSON> [ 436208.861 5012292.343] <-> IFC [ 436206.16695688 5012291.69142313] (dist: 2.77m, conf: 0.815)\n", "Match 2418: <PERSON><PERSON> [ 436134.191 5012190.078] <-> IFC [ 436130.16695688 5012187.69142313] (dist: 4.68m, conf: 0.688)\n", "Match 2419: <PERSON><PERSON> [ 435779.811 5011557.399] <-> IFC [ 435780.16695688 5011553.69142313] (dist: 3.72m, conf: 0.752)\n", "Match 2420: <PERSON><PERSON> [ 435846.645 5011239.357] <-> IFC [ 435846.16695688 5011235.69142313] (dist: 3.70m, conf: 0.754)\n", "Match 2421: <PERSON><PERSON> [ 436100.759 5012004.795] <-> IFC [ 436094.16695688 5012001.69142313] (dist: 7.29m, conf: 0.514)\n", "Match 2422: <PERSON><PERSON> [ 436098.261 5012118.49 ] <-> IFC [ 436094.16695688 5012113.69142313] (dist: 6.31m, conf: 0.579)\n", "Match 2423: <PERSON><PERSON> [ 435911.795 5011098.299] <-> IFC [ 435904.16695688 5011099.69142313] (dist: 7.75m, conf: 0.483)\n", "Match 2424: <PERSON><PERSON> [ 435810.511 5011416.458] <-> IFC [ 435808.16695688 5011416.69142313] (dist: 2.36m, conf: 0.843)\n", "Match 2425: <PERSON><PERSON> [ 435837.048 5012148.737] <-> IFC [ 435837.16695688 5012151.69142313] (dist: 2.96m, conf: 0.803)\n", "Match 2426: <PERSON><PERSON> [ 436200.444 5011800.409] <-> IFC [ 436198.16695688 5011801.69142313] (dist: 2.61m, conf: 0.826)\n", "Match 2427: <PERSON><PERSON> [ 435435.086 5011902.239] <-> IFC [ 435438.16695688 5011899.69142313] (dist: 4.00m, conf: 0.733)\n", "Match 2428: <PERSON><PERSON> [ 436164.456 5011725.699] <-> IFC [ 436164.16695688 5011721.69142313] (dist: 4.02m, conf: 0.732)\n", "Match 2429: <PERSON><PERSON> [ 435968.832 5011725.002] <-> IFC [ 435970.16695688 5011721.69142313] (dist: 3.57m, conf: 0.762)\n", "Match 2430: <PERSON><PERSON> [ 436359.648 5012184.353] <-> IFC [ 436358.16695688 5012181.69142313] (dist: 3.05m, conf: 0.797)\n", "Match 2431: <PERSON><PERSON> [ 435762.327 5011272.663] <-> IFC [ 435760.16695688 5011267.69142313] (dist: 5.42m, conf: 0.639)\n", "Match 2432: <PERSON><PERSON> [ 435656.88 5011617.32] <-> IFC [ 435656.16695688 5011613.69142313] (dist: 3.70m, conf: 0.753)\n", "Match 2433: <PERSON><PERSON> [ 435459.561 5011809.659] <-> IFC [ 435458.16695688 5011809.69142313] (dist: 1.39m, conf: 0.907)\n", "Match 2434: <PERSON><PERSON> [ 436071.209 5011698.311] <-> IFC [ 436074.16695688 5011697.69142313] (dist: 3.02m, conf: 0.799)\n", "Match 2435: <PERSON><PERSON> [ 435759.168 5011422.522] <-> IFC [ 435761.16695688 5011417.69142313] (dist: 5.23m, conf: 0.651)\n", "Match 2436: <PERSON><PERSON> [ 435666.615 5011421.862] <-> IFC [ 435676.16695688 5011425.69142313] (dist: 10.29m, conf: 0.314)\n", "Match 2437: <PERSON><PERSON> [ 436551.314 5011875.122] <-> IFC [ 436548.16695688 5011878.69142313] (dist: 4.76m, conf: 0.683)\n", "Match 2438: <PERSON><PERSON> [ 435569.817 5011689.663] <-> IFC [ 435572.16695688 5011685.69142313] (dist: 4.61m, conf: 0.692)\n", "Match 2439: <PERSON><PERSON> [ 435888.309 5011605.809] <-> IFC [ 435884.16695688 5011607.69142313] (dist: 4.55m, conf: 0.697)\n", "Match 2440: <PERSON><PERSON> [ 435959.956 5011725.661] <-> IFC [ 435960.16695688 5011723.69142313] (dist: 1.98m, conf: 0.868)\n", "Match 2441: <PERSON><PERSON> [ 435357.397 5011890.08 ] <-> IFC [ 435362.16695688 5011889.69142313] (dist: 4.79m, conf: 0.681)\n", "Match 2442: <PERSON><PERSON> [ 436446.282 5012151.158] <-> IFC [ 436444.16695688 5012149.69142313] (dist: 2.57m, conf: 0.828)\n", "Match 2443: <PERSON><PERSON> [ 435410.908 5011950.382] <-> IFC [ 435410.16695688 5011951.69142313] (dist: 1.50m, conf: 0.900)\n", "Match 2444: <PERSON><PERSON> [ 436155.039 5011689.125] <-> IFC [ 436154.16695688 5011689.69142313] (dist: 1.04m, conf: 0.931)\n", "Match 2445: <PERSON><PERSON> [ 436550.992 5011863.06 ] <-> IFC [ 436548.16695688 5011861.69142313] (dist: 3.14m, conf: 0.791)\n", "Match 2446: <PERSON><PERSON> [ 436323.23  5012264.936] <-> IFC [ 436320.16695688 5012262.69142313] (dist: 3.80m, conf: 0.747)\n", "Match 2447: <PERSON><PERSON> [ 435432.048 5011842.542] <-> IFC [ 435428.16695688 5011851.69142313] (dist: 9.94m, conf: 0.337)\n", "Match 2448: <PERSON><PERSON> [ 435927.595 5011386.83 ] <-> IFC [ 435932.16695688 5011387.69142313] (dist: 4.65m, conf: 0.690)\n", "Match 2449: <PERSON><PERSON> [ 436134.041 5011020.833] <-> IFC [ 436132.16695688 5011023.69142313] (dist: 3.42m, conf: 0.772)\n", "Match 2450: <PERSON><PERSON> [ 436386.208 5012220.172] <-> IFC [ 436386.16695688 5012221.69142313] (dist: 1.52m, conf: 0.899)\n", "Match 2451: <PERSON><PERSON> [ 435843.146 5011428.794] <-> IFC [ 435846.16695688 5011427.69142313] (dist: 3.22m, conf: 0.786)\n", "Match 2452: <PERSON><PERSON> [ 435501.304 5011614.59 ] <-> IFC [ 435504.16695688 5011611.69142313] (dist: 4.07m, conf: 0.728)\n", "Match 2453: <PERSON><PERSON> [ 435612.358 5011472.893] <-> IFC [ 435610.16695688 5011467.69142313] (dist: 5.64m, conf: 0.624)\n", "Match 2454: <PERSON><PERSON> [ 436308.525 5011151.994] <-> IFC [ 436312.16695688 5011151.69142313] (dist: 3.65m, conf: 0.756)\n", "Match 2455: <PERSON><PERSON> [ 435980.7   5011895.969] <-> IFC [ 435980.16695688 5011893.69142313] (dist: 2.34m, conf: 0.844)\n", "Match 2456: <PERSON><PERSON> [ 435411.659 5011938.707] <-> IFC [ 435410.16695688 5011942.69142313] (dist: 4.25m, conf: 0.716)\n", "Match 2457: <PERSON><PERSON> [ 435986.85  5012136.776] <-> IFC [ 435989.16695688 5012141.69142313] (dist: 5.43m, conf: 0.638)\n", "Match 2458: <PERSON><PERSON> [ 436452.547 5011743.473] <-> IFC [ 436448.16695688 5011741.69142313] (dist: 4.73m, conf: 0.685)\n", "Match 2459: <PERSON><PERSON> [ 436502.863 5011944.762] <-> IFC [ 436502.16695688 5011947.69142313] (dist: 3.01m, conf: 0.799)\n", "Match 2460: <PERSON><PERSON> [ 436407.089 5012187.174] <-> IFC [ 436406.16695688 5012187.69142313] (dist: 1.06m, conf: 0.930)\n", "Match 2461: <PERSON><PERSON> [ 436229.893 5011227.491] <-> IFC [ 436226.16695688 5011229.69142313] (dist: 4.33m, conf: 0.712)\n", "Match 2462: <PERSON><PERSON> [ 436283.944 5012256.735] <-> IFC [ 436282.16695688 5012259.69142313] (dist: 3.45m, conf: 0.770)\n", "Match 2463: <PERSON><PERSON> [ 435573.638 5011884.561] <-> IFC [ 435572.16695688 5011883.69142313] (dist: 1.71m, conf: 0.886)\n", "Match 2464: <PERSON><PERSON> [ 435456.104 5011665.222] <-> IFC [ 435458.16695688 5011662.69142313] (dist: 3.26m, conf: 0.782)\n", "Match 2465: <PERSON><PERSON> [ 435996.006 5011467.372] <-> IFC [ 435990.16695688 5011469.69142313] (dist: 6.28m, conf: 0.581)\n", "Match 2466: <PERSON><PERSON> [ 436217.849 5011629.633] <-> IFC [ 436220.16695688 5011631.69142313] (dist: 3.10m, conf: 0.793)\n", "Match 2467: <PERSON><PERSON> [ 436320.35  5011596.553] <-> IFC [ 436316.16695688 5011593.69142313] (dist: 5.07m, conf: 0.662)\n", "Match 2468: <PERSON><PERSON> [ 436100.929 5012181.841] <-> IFC [ 436102.16695688 5012183.69142313] (dist: 2.23m, conf: 0.852)\n", "Match 2469: <PERSON><PERSON> [ 435735.358 5011254.276] <-> IFC [ 435742.16695688 5011259.69142313] (dist: 8.70m, conf: 0.420)\n", "Match 2470: <PERSON><PERSON> [ 435884.917 5011848.703] <-> IFC [ 435884.16695688 5011851.69142313] (dist: 3.08m, conf: 0.795)\n", "Match 2471: <PERSON><PERSON> [ 435672.49  5011746.599] <-> IFC [ 435676.16695688 5011745.69142313] (dist: 3.79m, conf: 0.748)\n", "Match 2472: <PERSON><PERSON> [ 436596.088 5011919.93 ] <-> IFC [ 436596.16695688 5011919.69142313] (dist: 0.25m, conf: 0.983)\n", "Match 2473: <PERSON><PERSON> [ 435816.627 5011335.718] <-> IFC [ 435818.16695688 5011337.69142313] (dist: 2.50m, conf: 0.833)\n", "Match 2474: <PERSON><PERSON> [ 436005.31  5010963.521] <-> IFC [ 436008.16695688 5010969.69142313] (dist: 6.80m, conf: 0.547)\n", "Match 2475: <PERSON><PERSON> [ 435336.035 5011809.437] <-> IFC [ 435334.16695688 5011805.69142313] (dist: 4.19m, conf: 0.721)\n", "Match 2476: <PERSON><PERSON> [ 435773.95 5011274.96] <-> IFC [ 435770.16695688 5011273.69142313] (dist: 3.99m, conf: 0.734)\n", "Match 2477: <PERSON><PERSON> [ 435611.77  5012058.033] <-> IFC [ 435610.16695688 5012056.69142313] (dist: 2.09m, conf: 0.861)\n", "Match 2478: <PERSON><PERSON> [ 435878.829 5011824.282] <-> IFC [ 435884.16695688 5011827.69142313] (dist: 6.33m, conf: 0.578)\n", "Match 2479: <PERSON><PERSON> [ 436245.19  5011479.655] <-> IFC [ 436246.16695688 5011481.69142313] (dist: 2.26m, conf: 0.849)\n", "Match 2480: <PERSON><PERSON> [ 435509.884 5011509.086] <-> IFC [ 435514.16695688 5011505.69142313] (dist: 5.47m, conf: 0.636)\n", "Match 2481: <PERSON><PERSON> [ 435783.612 5011473.078] <-> IFC [ 435780.16695688 5011479.69142313] (dist: 7.46m, conf: 0.503)\n", "Match 2482: <PERSON><PERSON> [ 436509.117 5011947.637] <-> IFC [ 436510.16695688 5011943.69142313] (dist: 4.08m, conf: 0.728)\n", "Match 2483: <PERSON><PERSON> [ 436188.359 5012435.955] <-> IFC [ 436186.16695688 5012433.69142313] (dist: 3.15m, conf: 0.790)\n", "Match 2484: <PERSON><PERSON> [ 436155.146 5011332.771] <-> IFC [ 436150.16695688 5011335.69142313] (dist: 5.77m, conf: 0.615)\n", "Match 2485: <PERSON><PERSON> [ 436170.342 5012378.958] <-> IFC [ 436168.16695688 5012381.69142313] (dist: 3.49m, conf: 0.767)\n", "Match 2486: <PERSON><PERSON> [ 436340.958 5012238.445] <-> IFC [ 436338.16695688 5012238.69142313] (dist: 2.80m, conf: 0.813)\n", "Match 2487: <PERSON><PERSON> [ 436167.39  5012442.543] <-> IFC [ 436168.16695688 5012439.69142313] (dist: 2.96m, conf: 0.803)\n", "Match 2488: <PERSON><PERSON> [ 436259.942 5011953.785] <-> IFC [ 436264.16695688 5011953.69142313] (dist: 4.23m, conf: 0.718)\n", "Match 2489: <PERSON><PERSON> [ 435848.987 5011452.458] <-> IFC [ 435846.16695688 5011453.69142313] (dist: 3.08m, conf: 0.795)\n", "Match 2490: <PERSON><PERSON> [ 435888.625 5012090.878] <-> IFC [ 435884.16695688 5012091.69142313] (dist: 4.53m, conf: 0.698)\n", "Match 2491: <PERSON><PERSON> [ 435711.62  5011833.229] <-> IFC [ 435714.16695688 5011833.69142313] (dist: 2.59m, conf: 0.827)\n", "Match 2492: <PERSON><PERSON> [ 436464.226 5011734.799] <-> IFC [ 436458.16695688 5011737.69142313] (dist: 6.71m, conf: 0.552)\n", "Match 2493: <PERSON><PERSON> [ 435933.25  5011395.004] <-> IFC [ 435932.16695688 5011387.69142313] (dist: 7.39m, conf: 0.507)\n", "Match 2494: <PERSON><PERSON> [ 436490.685 5012118.196] <-> IFC [ 436490.16695688 5012113.69142313] (dist: 4.53m, conf: 0.698)\n", "Match 2495: <PERSON><PERSON> [ 436161.641 5011646.883] <-> IFC [ 436164.16695688 5011645.69142313] (dist: 2.79m, conf: 0.814)\n", "Match 2496: <PERSON><PERSON> [ 435620.904 5012094.138] <-> IFC [ 435618.16695688 5012095.69142313] (dist: 3.15m, conf: 0.790)\n", "Match 2497: <PERSON><PERSON> [ 436089.072 5012340.108] <-> IFC [ 436092.16695688 5012337.69142313] (dist: 3.93m, conf: 0.738)\n", "Match 2498: <PERSON><PERSON> [ 436353.32  5011251.306] <-> IFC [ 436350.16695688 5011249.69142313] (dist: 3.54m, conf: 0.764)\n", "Match 2499: <PERSON><PERSON> [ 436008.584 5011104.254] <-> IFC [ 436008.16695688 5011105.69142313] (dist: 1.50m, conf: 0.900)\n", "Match 2500: Dr<PERSON> [ 435894.056 5011206.453] <-> IFC [ 435894.16695688 5011205.69142313] (dist: 0.77m, conf: 0.949)\n", "Match 2501: <PERSON><PERSON> [ 435551.734 5012003.993] <-> IFC [ 435552.16695688 5012007.69142313] (dist: 3.72m, conf: 0.752)\n", "Match 2502: <PERSON><PERSON> [ 435899.835 5011641.131] <-> IFC [ 435904.16695688 5011639.69142313] (dist: 4.56m, conf: 0.696)\n", "Match 2503: <PERSON><PERSON> [ 436173.562 5011806.255] <-> IFC [ 436170.16695688 5011805.69142313] (dist: 3.44m, conf: 0.771)\n", "Match 2504: <PERSON><PERSON> [ 435705.375 5011775.989] <-> IFC [ 435704.16695688 5011769.69142313] (dist: 6.41m, conf: 0.573)\n", "Match 2505: <PERSON><PERSON> [ 436304.684 5012301.071] <-> IFC [ 436300.16695688 5012303.69142313] (dist: 5.22m, conf: 0.652)\n", "Match 2506: <PERSON><PERSON> [ 436217.933 5011908.788] <-> IFC [ 436217.16695688 5011903.01574746] (dist: 5.82m, conf: 0.612)\n", "Match 2507: <PERSON><PERSON> [ 435894.016 5011389.741] <-> IFC [ 435894.16695688 5011387.69142313] (dist: 2.06m, conf: 0.863)\n", "Match 2508: <PERSON><PERSON> [ 435582.588 5011785.081] <-> IFC [ 435580.16695688 5011781.69142313] (dist: 4.17m, conf: 0.722)\n", "Match 2509: <PERSON><PERSON> [ 436455.02  5012097.859] <-> IFC [ 436452.16695688 5012095.69142313] (dist: 3.58m, conf: 0.761)\n", "Match 2510: <PERSON><PERSON> [ 435957.127 5011995.435] <-> IFC [ 435960.16695688 5011997.69142313] (dist: 3.79m, conf: 0.748)\n", "Match 2511: <PERSON><PERSON> [ 435939.064 5011668.233] <-> IFC [ 435942.16695688 5011673.69142313] (dist: 6.28m, conf: 0.581)\n", "Match 2512: <PERSON><PERSON> [ 435828.441 5011443.028] <-> IFC [ 435828.16695688 5011439.69142313] (dist: 3.35m, conf: 0.777)\n", "Match 2513: <PERSON><PERSON> [ 436269.26  5012181.693] <-> IFC [ 436272.16695688 5012179.69142313] (dist: 3.53m, conf: 0.765)\n", "Match 2514: <PERSON><PERSON> [ 435909.34  5011374.791] <-> IFC [ 435913.16695688 5011373.69142313] (dist: 3.98m, conf: 0.735)\n", "Match 2515: <PERSON><PERSON> [ 435870.494 5012106.775] <-> IFC [ 435866.16695688 5012107.69142313] (dist: 4.42m, conf: 0.705)\n", "Match 2516: <PERSON><PERSON> [ 435702.46  5011959.138] <-> IFC [ 435704.16695688 5011961.69142313] (dist: 3.07m, conf: 0.795)\n", "Match 2517: <PERSON><PERSON> [ 436299.33  5012346.042] <-> IFC [ 436300.16695688 5012345.69142313] (dist: 0.91m, conf: 0.940)\n", "Match 2518: <PERSON><PERSON> [ 436182.252 5011383.345] <-> IFC [ 436179.16695688 5011391.69142313] (dist: 8.90m, conf: 0.407)\n", "Match 2519: <PERSON><PERSON> [ 435330.154 5011707.235] <-> IFC [ 435334.16695688 5011715.69142313] (dist: 9.36m, conf: 0.376)\n", "Match 2520: <PERSON><PERSON> [ 436304.744 5012151.377] <-> IFC [ 436300.16695688 5012151.69142313] (dist: 4.59m, conf: 0.694)\n", "Match 2521: <PERSON><PERSON> [ 435591.287 5011590.53 ] <-> IFC [ 435590.16695688 5011587.69142313] (dist: 3.05m, conf: 0.797)\n", "Match 2522: <PERSON><PERSON> [ 435779.793 5011677.846] <-> IFC [ 435780.16695688 5011681.69142313] (dist: 3.86m, conf: 0.742)\n", "Match 2523: <PERSON><PERSON> [ 436455.176 5011926.386] <-> IFC [ 436454.16695688 5011919.69142313] (dist: 6.77m, conf: 0.549)\n", "Match 2524: <PERSON><PERSON> [ 436086.398 5011737.67 ] <-> IFC [ 436084.16695688 5011735.69142313] (dist: 2.98m, conf: 0.801)\n", "Match 2525: <PERSON><PERSON> [ 435906.359 5011656.301] <-> IFC [ 435904.16695688 5011655.69142313] (dist: 2.28m, conf: 0.848)\n", "Match 2526: <PERSON><PERSON> [ 436182.41  5011290.481] <-> IFC [ 436188.16695688 5011291.69142313] (dist: 5.88m, conf: 0.608)\n", "Match 2527: <PERSON><PERSON> [ 436547.701 5011649.908] <-> IFC [ 436544.16695688 5011649.69142313] (dist: 3.54m, conf: 0.764)\n", "Match 2528: <PERSON><PERSON> [ 435636.276 5011473.575] <-> IFC [ 435638.16695688 5011473.69142313] (dist: 1.89m, conf: 0.874)\n", "Match 2529: <PERSON><PERSON> [ 435638.92  5011505.955] <-> IFC [ 435638.16695688 5011507.69142313] (dist: 1.89m, conf: 0.874)\n", "Match 2530: <PERSON><PERSON> [ 435663.57  5011605.473] <-> IFC [ 435666.16695688 5011613.69142313] (dist: 8.62m, conf: 0.425)\n", "Match 2531: <PERSON><PERSON> [ 435945.266 5011902.256] <-> IFC [ 435942.16695688 5011909.69142313] (dist: 8.06m, conf: 0.463)\n", "Match 2532: <PERSON><PERSON> [ 435801.144 5011248.596] <-> IFC [ 435808.16695688 5011247.69142313] (dist: 7.08m, conf: 0.528)\n", "Match 2533: <PERSON><PERSON> [ 435765.502 5011227.214] <-> IFC [ 435770.16695688 5011223.69142313] (dist: 5.85m, conf: 0.610)\n", "Match 2534: <PERSON><PERSON> [ 435330.588 5011884.626] <-> IFC [ 435334.16695688 5011881.69142313] (dist: 4.63m, conf: 0.691)\n", "Match 2535: <PERSON><PERSON> [ 435957.537 5012286.42 ] <-> IFC [ 435958.16695688 5012287.69142313] (dist: 1.42m, conf: 0.905)\n", "Match 2536: <PERSON><PERSON> [ 436106.941 5011731.015] <-> IFC [ 436112.16695688 5011727.69142313] (dist: 6.19m, conf: 0.587)\n", "Match 2537: <PERSON><PERSON> [ 435777.447 5011266.568] <-> IFC [ 435780.16695688 5011261.69142313] (dist: 5.58m, conf: 0.628)\n", "Match 2538: <PERSON><PERSON> [ 436328.783 5011494.275] <-> IFC [ 436326.16695688 5011493.69142313] (dist: 2.68m, conf: 0.821)\n", "Match 2539: <PERSON><PERSON> [ 436191.199 5012313.403] <-> IFC [ 436196.16695688 5012311.69142313] (dist: 5.25m, conf: 0.650)\n", "Match 2540: <PERSON><PERSON> [ 436277.696 5011077.255] <-> IFC [ 436284.16695688 5011077.69142313] (dist: 6.49m, conf: 0.568)\n", "Match 2541: <PERSON><PERSON> [ 436202.965 5012145.569] <-> IFC [ 436206.16695688 5012145.69142313] (dist: 3.20m, conf: 0.786)\n", "Match 2542: <PERSON><PERSON> [ 435855.102 5011185.382] <-> IFC [ 435856.16695688 5011187.69142313] (dist: 2.54m, conf: 0.830)\n", "Match 2543: <PERSON><PERSON> [ 436134.252 5011029.209] <-> IFC [ 436132.16695688 5011031.69142313] (dist: 3.24m, conf: 0.784)\n", "Match 2544: <PERSON><PERSON> [ 436116.632 5012337.25 ] <-> IFC [ 436110.16695688 5012339.69142313] (dist: 6.91m, conf: 0.539)\n", "Match 2545: <PERSON><PERSON> [ 435959.806 5011644.572] <-> IFC [ 435960.16695688 5011647.69142313] (dist: 3.14m, conf: 0.791)\n", "Match 2546: <PERSON><PERSON> [ 435932.767 5011572.428] <-> IFC [ 435932.16695688 5011569.69142313] (dist: 2.80m, conf: 0.813)\n", "Match 2547: <PERSON><PERSON> [ 436328.745 5012138.975] <-> IFC [ 436330.16695688 5012141.69142313] (dist: 3.07m, conf: 0.796)\n", "Match 2548: <PERSON><PERSON> [ 436122.275 5011599.347] <-> IFC [ 436122.16695688 5011595.69142313] (dist: 3.66m, conf: 0.756)\n", "Match 2549: <PERSON><PERSON> [ 436062.311 5011380.206] <-> IFC [ 436065.16695688 5011379.69142313] (dist: 2.90m, conf: 0.807)\n", "Match 2550: <PERSON><PERSON> [ 436385.677 5012136.156] <-> IFC [ 436386.16695688 5012135.69142313] (dist: 0.68m, conf: 0.955)\n", "Match 2551: <PERSON><PERSON> [ 436527.117 5012058.109] <-> IFC [ 436528.16695688 5012057.69142313] (dist: 1.13m, conf: 0.925)\n", "Match 2552: <PERSON><PERSON> [ 435984.588 5011653.653] <-> IFC [ 435980.16695688 5011653.69142313] (dist: 4.42m, conf: 0.705)\n", "Match 2553: <PERSON><PERSON> [ 436014.476 5011878.62 ] <-> IFC [ 436018.16695688 5011881.69142313] (dist: 4.80m, conf: 0.680)\n", "Match 2554: <PERSON><PERSON> [ 436028.899 5011944.498] <-> IFC [ 436027.16695688 5011943.69142313] (dist: 1.91m, conf: 0.873)\n", "Match 2555: <PERSON><PERSON> [ 435468.226 5011932.385] <-> IFC [ 435466.16695688 5011933.69142313] (dist: 2.44m, conf: 0.837)\n", "Match 2556: <PERSON><PERSON> [ 436592.692 5011998.591] <-> IFC [ 436596.16695688 5011999.69142313] (dist: 3.65m, conf: 0.757)\n", "Match 2557: <PERSON><PERSON> [ 435816.459 5012085.046] <-> IFC [ 435818.16695688 5012081.69142313] (dist: 3.76m, conf: 0.749)\n", "Match 2558: <PERSON><PERSON> [ 435975.054 5011026.232] <-> IFC [ 435980.16695688 5011031.69142313] (dist: 7.48m, conf: 0.501)\n", "Match 2559: <PERSON><PERSON> [ 436190.737 5012406.348] <-> IFC [ 436196.16695688 5012405.69142313] (dist: 5.47m, conf: 0.635)\n", "Match 2560: <PERSON><PERSON> [ 435282.611 5011872.279] <-> IFC [ 435286.16695688 5011871.69142313] (dist: 3.60m, conf: 0.760)\n", "Match 2561: <PERSON><PERSON> [ 436293.432 5011586.997] <-> IFC [ 436296.16695688 5011583.69142313] (dist: 4.29m, conf: 0.714)\n", "Match 2562: <PERSON><PERSON> [ 436091.736 5011779.307] <-> IFC [ 436094.16695688 5011779.69142313] (dist: 2.46m, conf: 0.836)\n", "Match 2563: <PERSON><PERSON> [ 436151.755 5010960.779] <-> IFC [ 436150.16695688 5010961.69142313] (dist: 1.83m, conf: 0.878)\n", "Match 2564: <PERSON><PERSON> [ 436224.412 5012118.6  ] <-> IFC [ 436224.16695688 5012119.69142313] (dist: 1.12m, conf: 0.925)\n", "Match 2565: <PERSON><PERSON> [ 435788.932 5011250.903] <-> IFC [ 435790.16695688 5011249.69142313] (dist: 1.73m, conf: 0.885)\n", "Match 2566: <PERSON><PERSON> [ 436377.21  5011749.103] <-> IFC [ 436382.16695688 5011747.69142313] (dist: 5.15m, conf: 0.656)\n", "Match 2567: <PERSON><PERSON> [ 435342.039 5011812.633] <-> IFC [ 435344.16695688 5011811.69142313] (dist: 2.33m, conf: 0.845)\n", "Match 2568: <PERSON><PERSON> [ 435411.614 5011620.162] <-> IFC [ 435420.16695688 5011617.69142313] (dist: 8.90m, conf: 0.406)\n", "Match 2569: <PERSON><PERSON> [ 435999.125 5011026.423] <-> IFC [ 435998.16695688 5011023.69142313] (dist: 2.89m, conf: 0.807)\n", "Match 2570: <PERSON><PERSON> [ 436314.628 5011212.318] <-> IFC [ 436312.16695688 5011211.69142313] (dist: 2.54m, conf: 0.831)\n", "Match 2571: <PERSON><PERSON> [ 435933.078 5012313.821] <-> IFC [ 435940.16695688 5012309.69142313] (dist: 8.20m, conf: 0.453)\n", "Match 2572: <PERSON><PERSON> [ 436310.807 5011659.26 ] <-> IFC [ 436306.16695688 5011659.69142313] (dist: 4.66m, conf: 0.689)\n", "Match 2573: <PERSON><PERSON> [ 436128.619 5012415.463] <-> IFC [ 436130.16695688 5012417.69142313] (dist: 2.71m, conf: 0.819)\n", "Match 2574: <PERSON><PERSON> [ 435393.582 5011779.435] <-> IFC [ 435390.16695688 5011783.69142313] (dist: 5.46m, conf: 0.636)\n", "Match 2575: <PERSON><PERSON> [ 436059.077 5012016.066] <-> IFC [ 436056.16695688 5012019.69142313] (dist: 4.65m, conf: 0.690)\n", "Match 2576: <PERSON><PERSON> [ 436230.045 5011242.074] <-> IFC [ 436226.16695688 5011245.69142313] (dist: 5.30m, conf: 0.646)\n", "Match 2577: <PERSON><PERSON> [ 436272.21  5012358.186] <-> IFC [ 436272.16695688 5012355.69142313] (dist: 2.49m, conf: 0.834)\n", "Match 2578: <PERSON><PERSON> [ 435819.478 5011254.633] <-> IFC [ 435818.16695688 5011251.69142313] (dist: 3.22m, conf: 0.785)\n", "Match 2579: <PERSON><PERSON> [ 435657.276 5012160.421] <-> IFC [ 435666.16695688 5012155.69142313] (dist: 10.07m, conf: 0.329)\n", "Match 2580: <PERSON><PERSON> [ 436212.564 5011554.54 ] <-> IFC [ 436212.16695688 5011556.69142313] (dist: 2.19m, conf: 0.854)\n", "Match 2581: <PERSON><PERSON> [ 435774.274 5012112.409] <-> IFC [ 435770.16695688 5012113.69142313] (dist: 4.30m, conf: 0.713)\n", "Match 2582: <PERSON><PERSON> [ 435969.369 5011698.423] <-> IFC [ 435970.16695688 5011693.69142313] (dist: 4.80m, conf: 0.680)\n", "Match 2583: <PERSON><PERSON> [ 436530.    5012073.551] <-> IFC [ 436528.16695688 5012075.69142313] (dist: 2.82m, conf: 0.812)\n", "Match 2584: <PERSON><PERSON> [ 436518.58  5012025.648] <-> IFC [ 436520.16695688 5012027.69142313] (dist: 2.59m, conf: 0.828)\n", "Match 2585: <PERSON><PERSON> [ 436326.18  5011818.214] <-> IFC [ 436326.16695688 5011817.69142313] (dist: 0.52m, conf: 0.965)\n", "Match 2586: <PERSON><PERSON> [ 436460.758 5011860.45 ] <-> IFC [ 436458.16695688 5011855.69142313] (dist: 5.42m, conf: 0.639)\n", "Match 2587: <PERSON><PERSON> [ 435809.696 5011413.355] <-> IFC [ 435808.16695688 5011416.69142313] (dist: 3.67m, conf: 0.755)\n", "Match 2588: <PERSON><PERSON> [ 436235.803 5012226.66 ] <-> IFC [ 436234.16695688 5012229.69142313] (dist: 3.44m, conf: 0.770)\n", "Match 2589: <PERSON><PERSON> [ 436059.474 5011788.015] <-> IFC [ 436056.16695688 5011793.69142313] (dist: 6.57m, conf: 0.562)\n", "Match 2590: <PERSON><PERSON> [ 435852.611 5011551.197] <-> IFC [ 435856.16695688 5011547.69142313] (dist: 4.99m, conf: 0.667)\n", "Match 2591: <PERSON><PERSON> [ 435933.202 5011689.539] <-> IFC [ 435932.16695688 5011685.69142313] (dist: 3.98m, conf: 0.734)\n", "Match 2592: <PERSON><PERSON> [ 435438.497 5011932.392] <-> IFC [ 435438.16695688 5011933.69142313] (dist: 1.34m, conf: 0.911)\n", "Match 2593: <PERSON><PERSON> [ 436490.992 5011922.946] <-> IFC [ 436492.16695688 5011925.69142313] (dist: 2.99m, conf: 0.801)\n", "Match 2594: <PERSON><PERSON> [ 436302.399 5011509.551] <-> IFC [ 436306.16695688 5011507.69142313] (dist: 4.20m, conf: 0.720)\n", "Match 2595: <PERSON><PERSON> [ 435921.524 5012103.639] <-> IFC [ 435922.16695688 5012101.69142313] (dist: 2.05m, conf: 0.863)\n", "Match 2596: <PERSON><PERSON> [ 436461.083 5011845.148] <-> IFC [ 436458.16695688 5011847.69142313] (dist: 3.87m, conf: 0.742)\n", "Match 2597: <PERSON><PERSON> [ 436266.654 5012285.921] <-> IFC [ 436262.16695688 5012283.69142313] (dist: 5.01m, conf: 0.666)\n", "Match 2598: <PERSON><PERSON> [ 435360.36  5011677.408] <-> IFC [ 435362.16695688 5011673.69142313] (dist: 4.13m, conf: 0.724)\n", "Match 2599: <PERSON><PERSON> [ 435912.238 5012280.433] <-> IFC [ 435912.16695688 5012277.69142313] (dist: 2.74m, conf: 0.817)\n", "Match 2600: <PERSON><PERSON> [ 435905.865 5011613.952] <-> IFC [ 435914.16695688 5011613.69142313] (dist: 8.31m, conf: 0.446)\n", "Match 2601: <PERSON><PERSON> [ 436125.072 5011623.314] <-> IFC [ 436122.16695688 5011619.69142313] (dist: 4.64m, conf: 0.690)\n", "Match 2602: <PERSON><PERSON> [ 436173.318 5012222.881] <-> IFC [ 436178.16695688 5012225.69142313] (dist: 5.60m, conf: 0.626)\n", "Match 2603: <PERSON><PERSON> [ 436179.352 5010951.567] <-> IFC [ 436179.16695688 5010955.69142313] (dist: 4.13m, conf: 0.725)\n", "Match 2604: <PERSON><PERSON> [ 436407.238 5011982.912] <-> IFC [ 436407.16695688 5011989.69142313] (dist: 6.78m, conf: 0.548)\n", "Match 2605: <PERSON><PERSON> [ 436319.778 5011524.308] <-> IFC [ 436316.16695688 5011525.69142313] (dist: 3.87m, conf: 0.742)\n", "Match 2606: <PERSON><PERSON> [ 435360.061 5011665.591] <-> IFC [ 435362.16695688 5011673.69142313] (dist: 8.37m, conf: 0.442)\n", "Match 2607: <PERSON><PERSON> [ 436257.245 5012232.425] <-> IFC [ 436254.16695688 5012229.69142313] (dist: 4.12m, conf: 0.726)\n", "Match 2608: <PERSON><PERSON> [ 435909.445 5011521.436] <-> IFC [ 435914.16695688 5011521.69142313] (dist: 4.73m, conf: 0.685)\n", "Match 2609: <PERSON><PERSON> [ 436196.788 5012175.537] <-> IFC [ 436196.16695688 5012171.69142313] (dist: 3.90m, conf: 0.740)\n", "Match 2610: <PERSON><PERSON> [ 435375.224 5011971.77 ] <-> IFC [ 435372.16695688 5011971.69142313] (dist: 3.06m, conf: 0.796)\n", "Match 2611: <PERSON><PERSON> [ 435713.822 5011400.978] <-> IFC [ 435714.16695688 5011397.69142313] (dist: 3.30m, conf: 0.780)\n", "Match 2612: <PERSON><PERSON> [ 435470.857 5011884.639] <-> IFC [ 435466.16695688 5011881.69142313] (dist: 5.54m, conf: 0.631)\n", "Match 2613: <PERSON><PERSON> [ 435731.771 5011430.969] <-> IFC [ 435732.16695688 5011431.69142313] (dist: 0.82m, conf: 0.945)\n", "Match 2614: <PERSON><PERSON> [ 435458.875 5011653.578] <-> IFC [ 435458.16695688 5011653.69142313] (dist: 0.72m, conf: 0.952)\n", "Match 2615: <PERSON><PERSON> [ 436344.504 5011224.008] <-> IFC [ 436340.16695688 5011220.69142313] (dist: 5.46m, conf: 0.636)\n", "Match 2616: <PERSON><PERSON> [ 436041.618 5010975.451] <-> IFC [ 436036.16695688 5010977.69142313] (dist: 5.89m, conf: 0.607)\n", "Match 2617: <PERSON><PERSON> [ 436671.405 5011869.319] <-> IFC [ 436672.16695688 5011866.69142313] (dist: 2.74m, conf: 0.818)\n", "Match 2618: <PERSON><PERSON> [ 436163.765 5011836.375] <-> IFC [ 436160.16695688 5011834.69142313] (dist: 3.97m, conf: 0.735)\n", "Match 2619: <PERSON><PERSON> [ 436349.792 5011722.167] <-> IFC [ 436354.16695688 5011721.69142313] (dist: 4.40m, conf: 0.707)\n", "Match 2620: <PERSON><PERSON> [ 436107.014 5011980.495] <-> IFC [ 436103.16695688 5011975.69142313] (dist: 6.15m, conf: 0.590)\n", "Match 2621: <PERSON><PERSON> [ 435630.172 5011656.456] <-> IFC [ 435628.16695688 5011657.69142313] (dist: 2.36m, conf: 0.843)\n", "Match 2622: <PERSON><PERSON> [ 435903.116 5012039.937] <-> IFC [ 435904.16695688 5012031.69142313] (dist: 8.31m, conf: 0.446)\n", "Match 2623: <PERSON><PERSON> [ 436248.156 5012166.736] <-> IFC [ 436244.16695688 5012169.69142313] (dist: 4.96m, conf: 0.669)\n", "Match 2624: <PERSON><PERSON> [ 436023.621 5011548.486] <-> IFC [ 436027.16695688 5011553.69142314] (dist: 6.30m, conf: 0.580)\n", "Match 2625: <PERSON><PERSON> [ 436245.261 5012304.688] <-> IFC [ 436244.16695688 5012305.69142313] (dist: 1.48m, conf: 0.901)\n", "Match 2626: <PERSON><PERSON> [ 435573.351 5012100.487] <-> IFC [ 435580.16695688 5012099.69142313] (dist: 6.86m, conf: 0.543)\n", "Match 2627: <PERSON><PERSON> [ 435300.259 5011839.255] <-> IFC [ 435296.16695688 5011835.69142313] (dist: 5.43m, conf: 0.638)\n", "Match 2628: <PERSON><PERSON> [ 436320.135 5011563.587] <-> IFC [ 436316.16695688 5011559.69142313] (dist: 5.56m, conf: 0.629)\n", "Match 2629: <PERSON><PERSON> [ 435894.124 5011830.854] <-> IFC [ 435894.16695688 5011831.69142313] (dist: 0.84m, conf: 0.944)\n", "Match 2630: <PERSON><PERSON> [ 435570.184 5011446.371] <-> IFC [ 435571.16695688 5011449.69142313] (dist: 3.46m, conf: 0.769)\n", "Match 2631: <PERSON><PERSON> [ 435543.21  5011944.041] <-> IFC [ 435542.16695688 5011941.69142313] (dist: 2.57m, conf: 0.829)\n", "Match 2632: <PERSON><PERSON> [ 435999.514 5011458.459] <-> IFC [ 435998.16695688 5011459.69142313] (dist: 1.83m, conf: 0.878)\n", "Match 2633: <PERSON><PERSON> [ 435797.767 5012151.848] <-> IFC [ 435798.16695688 5012147.69142313] (dist: 4.18m, conf: 0.722)\n", "Match 2634: <PERSON><PERSON> [ 435983.923 5011977.736] <-> IFC [ 435980.16695688 5011977.69142313] (dist: 3.76m, conf: 0.750)\n", "Match 2635: <PERSON><PERSON> [ 435467.823 5011730.933] <-> IFC [ 435466.16695688 5011727.69142313] (dist: 3.64m, conf: 0.757)\n", "Match 2636: <PERSON><PERSON> [ 435993.325 5011725.445] <-> IFC [ 435998.16695688 5011727.69142313] (dist: 5.34m, conf: 0.644)\n", "Match 2637: <PERSON><PERSON> [ 435849.57  5011539.427] <-> IFC [ 435856.16695688 5011539.69142313] (dist: 6.60m, conf: 0.560)\n", "Match 2638: <PERSON><PERSON> [ 435876.119 5011776.266] <-> IFC [ 435875.16695688 5011777.69142313] (dist: 1.71m, conf: 0.886)\n", "Match 2639: <PERSON><PERSON> [ 435771.597 5011737.344] <-> IFC [ 435762.16695688 5011735.69142313] (dist: 9.57m, conf: 0.362)\n", "Match 2640: <PERSON><PERSON> [ 435768.185 5011452.554] <-> IFC [ 435770.16695688 5011455.69142313] (dist: 3.71m, conf: 0.753)\n", "Match 2641: <PERSON><PERSON> [ 435986.951 5011929.192] <-> IFC [ 435980.16695688 5011927.69142313] (dist: 6.95m, conf: 0.537)\n", "Match 2642: <PERSON><PERSON> [ 435989.718 5011824.533] <-> IFC [ 435980.16695688 5011825.69142313] (dist: 9.62m, conf: 0.359)\n", "Match 2643: <PERSON><PERSON> [ 435561.198 5011982.871] <-> IFC [ 435562.16695688 5011979.69142313] (dist: 3.32m, conf: 0.778)\n", "Match 2644: <PERSON><PERSON> [ 436017.539 5011458.16 ] <-> IFC [ 436018.16695688 5011457.69142313] (dist: 0.78m, conf: 0.948)\n", "Match 2645: <PERSON><PERSON> [ 435731.974 5011434.148] <-> IFC [ 435732.16695688 5011431.69142313] (dist: 2.46m, conf: 0.836)\n", "Match 2646: <PERSON><PERSON> [ 435264.267 5011830.454] <-> IFC [ 435268.16695688 5011829.69142313] (dist: 3.97m, conf: 0.735)\n", "Match 2647: <PERSON><PERSON> [ 436593.255 5011994.995] <-> IFC [ 436596.16695688 5011991.69142313] (dist: 4.40m, conf: 0.706)\n", "Match 2648: <PERSON><PERSON> [ 435912.451 5012001.657] <-> IFC [ 435904.16695688 5011997.69142313] (dist: 9.18m, conf: 0.388)\n", "Match 2649: <PERSON><PERSON> [ 435777.518 5011530.251] <-> IFC [ 435780.16695688 5011529.69142313] (dist: 2.71m, conf: 0.820)\n", "Match 2650: <PERSON><PERSON> [ 436062.172 5011691.898] <-> IFC [ 436065.16695688 5011691.69142313] (dist: 3.00m, conf: 0.800)\n", "Match 2651: <PERSON><PERSON> [ 435600.2  5012055.43] <-> IFC [ 435600.16695688 5012051.69142313] (dist: 3.74m, conf: 0.751)\n", "Match 2652: <PERSON><PERSON> [ 435771.163 5011317.057] <-> IFC [ 435770.16695688 5011313.69142313] (dist: 3.51m, conf: 0.766)\n", "Match 2653: <PERSON><PERSON> [ 436134.498 5011545.457] <-> IFC [ 436132.16695688 5011545.69142313] (dist: 2.34m, conf: 0.844)\n", "Match 2654: <PERSON><PERSON> [ 436397.908 5011944.059] <-> IFC [ 436398.16695688 5011943.69142313] (dist: 0.45m, conf: 0.970)\n", "Match 2655: <PERSON><PERSON> [ 436196.908 5011560.796] <-> IFC [ 436202.16695688 5011565.69142313] (dist: 7.18m, conf: 0.521)\n", "Match 2656: <PERSON><PERSON> [ 436254.391 5011533.724] <-> IFC [ 436250.16695688 5011535.69142313] (dist: 4.66m, conf: 0.689)\n", "Match 2657: <PERSON><PERSON> [ 435278.968 5011890.059] <-> IFC [ 435286.16695688 5011889.69142313] (dist: 7.21m, conf: 0.519)\n", "Match 2658: <PERSON><PERSON> [ 435876.626 5011827.41 ] <-> IFC [ 435875.16695688 5011833.69142313] (dist: 6.45m, conf: 0.570)\n", "Match 2659: <PERSON><PERSON> [ 436272.272 5012379.57 ] <-> IFC [ 436272.16695688 5012381.69142313] (dist: 2.12m, conf: 0.858)\n", "Match 2660: <PERSON><PERSON> [ 435558.391 5011659.826] <-> IFC [ 435562.16695688 5011665.69142313] (dist: 6.98m, conf: 0.535)\n", "Match 2661: <PERSON><PERSON> [ 435770.909 5011782.464] <-> IFC [ 435770.16695688 5011785.69142313] (dist: 3.31m, conf: 0.779)\n", "Match 2662: <PERSON><PERSON> [ 436373.7   5011124.987] <-> IFC [ 436378.16695688 5011123.69142313] (dist: 4.65m, conf: 0.690)\n", "Match 2663: <PERSON><PERSON> [ 435912.577 5011401.527] <-> IFC [ 435914.16695688 5011403.69142313] (dist: 2.69m, conf: 0.821)\n", "Match 2664: <PERSON><PERSON> [ 435542.928 5011961.94 ] <-> IFC [ 435542.16695688 5011959.69142313] (dist: 2.37m, conf: 0.842)\n", "Match 2665: <PERSON><PERSON> [ 436266.387 5012115.437] <-> IFC [ 436262.16695688 5012114.69142313] (dist: 4.29m, conf: 0.714)\n", "Match 2666: <PERSON><PERSON> [ 436131.594 5012169.172] <-> IFC [ 436130.16695688 5012171.69142313] (dist: 2.90m, conf: 0.807)\n", "Match 2667: <PERSON><PERSON> [ 435891.327 5011103.874] <-> IFC [ 435894.16695688 5011103.69142313] (dist: 2.85m, conf: 0.810)\n", "Match 2668: <PERSON><PERSON> [ 436232.819 5012451.331] <-> IFC [ 436234.16695688 5012453.69142313] (dist: 2.72m, conf: 0.819)\n", "Match 2669: <PERSON><PERSON> [ 436526.916 5011707.597] <-> IFC [ 436526.16695688 5011703.69142313] (dist: 3.98m, conf: 0.735)\n", "Match 2670: <PERSON><PERSON> [ 435336.585 5011938.538] <-> IFC [ 435334.16695688 5011941.69142313] (dist: 3.97m, conf: 0.735)\n", "Match 2671: <PERSON><PERSON> [ 435264.329 5011860.578] <-> IFC [ 435268.16695688 5011863.69142313] (dist: 4.94m, conf: 0.671)\n", "Match 2672: <PERSON><PERSON> [ 435459.367 5011560.823] <-> IFC [ 435466.16695688 5011567.69142313] (dist: 9.67m, conf: 0.356)\n", "Match 2673: <PERSON><PERSON> [ 436070.737 5011620.285] <-> IFC [ 436074.16695688 5011619.69142313] (dist: 3.48m, conf: 0.768)\n", "Match 2674: <PERSON><PERSON> [ 435423.406 5011835.929] <-> IFC [ 435420.16695688 5011837.69142313] (dist: 3.69m, conf: 0.754)\n", "Match 2675: <PERSON><PERSON> [ 436392.315 5011269.197] <-> IFC [ 436388.16695688 5011267.69142313] (dist: 4.41m, conf: 0.706)\n", "Match 2676: <PERSON><PERSON> [ 436161.042 5011692.775] <-> IFC [ 436164.16695688 5011688.69142313] (dist: 5.14m, conf: 0.657)\n", "Match 2677: <PERSON><PERSON> [ 435639.071 5011785.465] <-> IFC [ 435638.16695688 5011783.69142313] (dist: 1.99m, conf: 0.867)\n", "Match 2678: <PERSON><PERSON> [ 435789.658 5011517.926] <-> IFC [ 435790.16695688 5011515.69142313] (dist: 2.29m, conf: 0.847)\n", "Match 2679: <PERSON><PERSON> [ 435588.144 5012022.523] <-> IFC [ 435590.16695688 5012019.69142313] (dist: 3.48m, conf: 0.768)\n", "Match 2680: Drone [ 435974.75  5012010.801] <-> IFC [ 435970.16695688 5012012.69142313] (dist: 4.96m, conf: 0.669)\n", "Match 2681: <PERSON><PERSON> [ 435951.206 5011191.6  ] <-> IFC [ 435952.16695688 5011193.69142313] (dist: 2.30m, conf: 0.847)\n", "Match 2682: <PERSON><PERSON> [ 436335.407 5011263.547] <-> IFC [ 436331.16695688 5011259.69142313] (dist: 5.73m, conf: 0.618)\n", "Match 2683: <PERSON><PERSON> [ 435552.221 5011974.832] <-> IFC [ 435552.16695688 5011973.69142313] (dist: 1.14m, conf: 0.924)\n", "Match 2684: <PERSON><PERSON> [ 435672.338 5012004.429] <-> IFC [ 435666.16695688 5012003.69142313] (dist: 6.21m, conf: 0.586)\n", "Match 2685: <PERSON><PERSON> [ 435810.22  5011791.639] <-> IFC [ 435808.16695688 5011793.69142313] (dist: 2.90m, conf: 0.806)\n", "Match 2686: <PERSON><PERSON> [ 435564.412 5011830.116] <-> IFC [ 435562.16695688 5011833.69142313] (dist: 4.22m, conf: 0.719)\n", "Match 2687: <PERSON><PERSON> [ 435873.036 5012268.302] <-> IFC [ 435874.16695688 5012265.69142313] (dist: 2.85m, conf: 0.810)\n", "Match 2688: <PERSON><PERSON> [ 435846.279 5011218.146] <-> IFC [ 435838.16695688 5011219.69142313] (dist: 8.26m, conf: 0.449)\n", "Match 2689: <PERSON><PERSON> [ 435834.339 5011913.905] <-> IFC [ 435838.16695688 5011909.69142313] (dist: 5.69m, conf: 0.620)\n", "Match 2690: <PERSON><PERSON> [ 436061.748 5011698.589] <-> IFC [ 436066.16695688 5011699.69142313] (dist: 4.55m, conf: 0.696)\n", "Match 2691: <PERSON><PERSON> [ 435921.512 5011374.16 ] <-> IFC [ 435922.16695688 5011372.69142313] (dist: 1.61m, conf: 0.893)\n", "Match 2692: <PERSON><PERSON> [ 435798.069 5011641.257] <-> IFC [ 435800.16695688 5011641.69142313] (dist: 2.14m, conf: 0.857)\n", "Match 2693: <PERSON><PERSON> [ 435651.354 5011629.255] <-> IFC [ 435648.16695688 5011631.69142313] (dist: 4.01m, conf: 0.733)\n", "Match 2694: <PERSON><PERSON> [ 436290.112 5011406.865] <-> IFC [ 436294.16695688 5011405.69142313] (dist: 4.22m, conf: 0.719)\n", "Match 2695: <PERSON><PERSON> [ 436233.302 5011269.165] <-> IFC [ 436236.16695688 5011265.69142313] (dist: 4.50m, conf: 0.700)\n", "Match 2696: <PERSON><PERSON> [ 436163.801 5011236.159] <-> IFC [ 436170.16695688 5011239.69142313] (dist: 7.28m, conf: 0.515)\n", "Match 2697: <PERSON><PERSON> [ 436070.705 5011038.188] <-> IFC [ 436074.16695688 5011035.69142313] (dist: 4.27m, conf: 0.715)\n", "Match 2698: <PERSON><PERSON> [ 436130.997 5011476.257] <-> IFC [ 436132.16695688 5011478.69142313] (dist: 2.70m, conf: 0.820)\n", "Match 2699: <PERSON><PERSON> [ 436136.926 5011865.913] <-> IFC [ 436132.16695688 5011869.69142313] (dist: 6.08m, conf: 0.595)\n", "Match 2700: <PERSON><PERSON> [ 435300.118 5011926.108] <-> IFC [ 435296.16695688 5011927.69142313] (dist: 4.26m, conf: 0.716)\n", "Match 2701: <PERSON><PERSON> [ 435668.86  5011977.173] <-> IFC [ 435666.16695688 5011975.69142313] (dist: 3.07m, conf: 0.795)\n", "Match 2702: <PERSON><PERSON> [ 435977.859 5011449.502] <-> IFC [ 435980.16695688 5011445.69142313] (dist: 4.46m, conf: 0.703)\n", "Match 2703: <PERSON><PERSON> [ 436245.649 5012202.64 ] <-> IFC [ 436244.16695688 5012203.69142313] (dist: 1.82m, conf: 0.879)\n", "Match 2704: <PERSON><PERSON> [ 435542.994 5011748.88 ] <-> IFC [ 435542.16695688 5011751.69142313] (dist: 2.93m, conf: 0.805)\n", "Match 2705: <PERSON><PERSON> [ 435776.92  5011767.069] <-> IFC [ 435780.16695688 5011769.69142313] (dist: 4.17m, conf: 0.722)\n", "Match 2706: <PERSON><PERSON> [ 436148.75  5011353.754] <-> IFC [ 436150.16695688 5011345.69142313] (dist: 8.19m, conf: 0.454)\n", "Match 2707: <PERSON><PERSON> [ 435494.764 5011698.052] <-> IFC [ 435496.16695688 5011699.69142313] (dist: 2.16m, conf: 0.856)\n", "Match 2708: <PERSON><PERSON> [ 435563.764 5011539.308] <-> IFC [ 435562.16695688 5011541.69142313] (dist: 2.87m, conf: 0.809)\n", "Match 2709: <PERSON><PERSON> [ 436449.408 5011977.833] <-> IFC [ 436446.16695688 5011975.69142313] (dist: 3.88m, conf: 0.741)\n", "Match 2710: <PERSON><PERSON> [ 436116.466 5011796.916] <-> IFC [ 436112.16695688 5011795.69142313] (dist: 4.47m, conf: 0.702)\n", "Match 2711: <PERSON><PERSON> [ 435441.415 5011737.299] <-> IFC [ 435438.16695688 5011737.69142313] (dist: 3.27m, conf: 0.782)\n", "Match 2712: <PERSON><PERSON> [ 435822.307 5011167.107] <-> IFC [ 435828.16695688 5011167.69142313] (dist: 5.89m, conf: 0.607)\n", "Match 2713: <PERSON><PERSON> [ 435944.695 5011413.66 ] <-> IFC [ 435942.16695688 5011415.69142313] (dist: 3.24m, conf: 0.784)\n", "Match 2714: <PERSON><PERSON> [ 435699.487 5011773.673] <-> IFC [ 435704.16695688 5011769.69142313] (dist: 6.14m, conf: 0.590)\n", "Match 2715: <PERSON><PERSON> [ 436293.09  5012199.484] <-> IFC [ 436292.16695688 5012197.69142313] (dist: 2.02m, conf: 0.866)\n", "Match 2716: <PERSON><PERSON> [ 436263.121 5012310.426] <-> IFC [ 436262.16695688 5012309.69142313] (dist: 1.20m, conf: 0.920)\n", "Match 2717: <PERSON><PERSON> [ 436089.563 5011953.095] <-> IFC [ 436094.16695688 5011951.69142313] (dist: 4.81m, conf: 0.679)\n", "Match 2718: <PERSON><PERSON> [ 436046.858 5012334.511] <-> IFC [ 436044.16695688 5012335.69142313] (dist: 2.94m, conf: 0.804)\n", "Match 2719: <PERSON><PERSON> [ 435993.21 5011992.17] <-> IFC [ 435998.16695688 5011991.69142313] (dist: 4.98m, conf: 0.668)\n", "Match 2720: <PERSON><PERSON> [ 436008.018 5012331.729] <-> IFC [ 436006.16695688 5012331.69142313] (dist: 1.85m, conf: 0.877)\n", "Match 2721: <PERSON><PERSON> [ 436313.708 5011224.207] <-> IFC [ 436312.16695688 5011227.69142313] (dist: 3.81m, conf: 0.746)\n", "Match 2722: <PERSON><PERSON> [ 436044.375 5010948.381] <-> IFC [ 436046.16695688 5010947.69142313] (dist: 1.92m, conf: 0.872)\n", "Match 2723: <PERSON><PERSON> [ 435807.11  5011260.391] <-> IFC [ 435808.16695688 5011263.69142313] (dist: 3.47m, conf: 0.769)\n", "Match 2724: <PERSON><PERSON> [ 435467.828 5011587.699] <-> IFC [ 435466.16695688 5011591.69142313] (dist: 4.32m, conf: 0.712)\n", "Match 2725: <PERSON><PERSON> [ 435525.098 5011593.837] <-> IFC [ 435524.16695688 5011593.69142313] (dist: 0.94m, conf: 0.937)\n", "Match 2726: <PERSON><PERSON> [ 435534.228 5011779.518] <-> IFC [ 435534.16695688 5011777.69142313] (dist: 1.83m, conf: 0.878)\n", "Match 2727: <PERSON><PERSON> [ 435884.683 5011620.061] <-> IFC [ 435884.16695688 5011616.69142313] (dist: 3.41m, conf: 0.773)\n", "Match 2728: <PERSON><PERSON> [ 436091.81  5011563.761] <-> IFC [ 436094.16695688 5011567.69142313] (dist: 4.58m, conf: 0.694)\n", "Match 2729: <PERSON><PERSON> [ 435740.964 5011797.643] <-> IFC [ 435742.16695688 5011800.69142313] (dist: 3.28m, conf: 0.782)\n", "Match 2730: <PERSON><PERSON> [ 436403.802 5012214.236] <-> IFC [ 436406.16695688 5012213.69142313] (dist: 2.43m, conf: 0.838)\n", "Match 2731: <PERSON><PERSON> [ 436550.878 5011821.349] <-> IFC [ 436548.16695688 5011819.69142313] (dist: 3.18m, conf: 0.788)\n", "Match 2732: <PERSON><PERSON> [ 436380.248 5011626.093] <-> IFC [ 436382.16695688 5011629.69142313] (dist: 4.08m, conf: 0.728)\n", "Match 2733: <PERSON><PERSON> [ 435750.415 5011245.283] <-> IFC [ 435752.16695688 5011247.69142313] (dist: 2.98m, conf: 0.801)\n", "Match 2734: Dr<PERSON> [ 436188.135 5011563.642] <-> IFC [ 436188.16695688 5011561.69142313] (dist: 1.95m, conf: 0.870)\n", "Match 2735: <PERSON><PERSON> [ 435639.494 5011581.68 ] <-> IFC [ 435638.16695688 5011591.69142313] (dist: 10.10m, conf: 0.327)\n", "Match 2736: <PERSON><PERSON> [ 436452.156 5011739.88 ] <-> IFC [ 436448.16695688 5011741.69142313] (dist: 4.38m, conf: 0.708)\n", "Match 2737: <PERSON><PERSON> [ 436094.883 5012406.46 ] <-> IFC [ 436102.16695688 5012401.69142313] (dist: 8.71m, conf: 0.420)\n", "Match 2738: <PERSON><PERSON> [ 436608.003 5011833.139] <-> IFC [ 436606.16695688 5011831.69142313] (dist: 2.34m, conf: 0.844)\n", "Match 2739: <PERSON><PERSON> [ 435975.22  5011140.389] <-> IFC [ 435970.16695688 5011139.69142313] (dist: 5.10m, conf: 0.660)\n", "Match 2740: <PERSON><PERSON> [ 436023.647 5011671.559] <-> IFC [ 436027.39772611 5011670.92219236] (dist: 3.80m, conf: 0.746)\n", "Match 2741: <PERSON><PERSON> [ 436119.64 5011575.19] <-> IFC [ 436122.16695688 5011577.69142313] (dist: 3.56m, conf: 0.763)\n", "Match 2742: <PERSON><PERSON> [ 435663.539 5011509.814] <-> IFC [ 435666.16695688 5011511.69142313] (dist: 3.23m, conf: 0.785)\n", "Match 2743: <PERSON><PERSON> [ 436116.004 5011836.296] <-> IFC [ 436112.16695688 5011837.69142313] (dist: 4.08m, conf: 0.728)\n", "Match 2744: <PERSON><PERSON> [ 436062.08  5011497.739] <-> IFC [ 436056.16695688 5011503.69142313] (dist: 8.39m, conf: 0.441)\n", "Match 2745: <PERSON><PERSON> [ 435441.156 5011869.148] <-> IFC [ 435438.16695688 5011865.69142313] (dist: 4.57m, conf: 0.695)\n", "Match 2746: <PERSON><PERSON> [ 436331.786 5012190.149] <-> IFC [ 436330.16695688 5012191.69142313] (dist: 2.24m, conf: 0.851)\n", "Match 2747: <PERSON><PERSON> [ 436068.194 5012013.666] <-> IFC [ 436074.16695688 5012009.69142313] (dist: 7.17m, conf: 0.522)\n", "Match 2748: <PERSON><PERSON> [ 435822.633 5011731.221] <-> IFC [ 435828.16695688 5011733.69142313] (dist: 6.06m, conf: 0.596)\n", "Match 2749: <PERSON><PERSON> [ 436203.184 5011857.777] <-> IFC [ 436198.16695688 5011861.69142313] (dist: 6.36m, conf: 0.576)\n", "Match 2750: <PERSON><PERSON> [ 435819.525 5011674.34 ] <-> IFC [ 435818.16695688 5011675.69142313] (dist: 1.92m, conf: 0.872)\n", "Match 2751: <PERSON><PERSON> [ 435875.806 5011395.77 ] <-> IFC [ 435876.16695688 5011398.69142313] (dist: 2.94m, conf: 0.804)\n", "Match 2752: <PERSON><PERSON> [ 436266.647 5011221.346] <-> IFC [ 436264.16695688 5011215.69142313] (dist: 6.17m, conf: 0.588)\n", "Match 2753: <PERSON><PERSON> [ 436224.488 5011619.885] <-> IFC [ 436230.16695688 5011619.69142313] (dist: 5.68m, conf: 0.621)\n", "Match 2754: <PERSON><PERSON> [ 436166.892 5011863.087] <-> IFC [ 436160.16695688 5011859.69142313] (dist: 7.53m, conf: 0.498)\n", "Match 2755: <PERSON><PERSON> [ 435552.285 5012088.53 ] <-> IFC [ 435552.16695688 5012091.69142313] (dist: 3.16m, conf: 0.789)\n", "Match 2756: <PERSON><PERSON> [ 436641.675 5011782.253] <-> IFC [ 436644.16695688 5011783.69142313] (dist: 2.88m, conf: 0.808)\n", "Match 2757: <PERSON><PERSON> [ 436008.633 5011461.102] <-> IFC [ 436008.16695688 5011467.69142313] (dist: 6.61m, conf: 0.560)\n", "Match 2758: <PERSON><PERSON> [ 436341.662 5011197.03 ] <-> IFC [ 436340.16695688 5011195.69142313] (dist: 2.01m, conf: 0.866)\n", "Match 2759: <PERSON><PERSON> [ 436176.283 5011791.376] <-> IFC [ 436179.16695688 5011791.71845016] (dist: 2.90m, conf: 0.806)\n", "Match 2760: <PERSON><PERSON> [ 435962.812 5011688.882] <-> IFC [ 435960.16695688 5011681.69142313] (dist: 7.66m, conf: 0.489)\n", "Match 2761: <PERSON><PERSON> [ 435588.279 5011890.775] <-> IFC [ 435590.16695688 5011893.69142313] (dist: 3.47m, conf: 0.768)\n", "Match 2762: <PERSON><PERSON> [ 436299.278 5011575.172] <-> IFC [ 436296.16695688 5011574.69142313] (dist: 3.15m, conf: 0.790)\n", "Match 2763: <PERSON><PERSON> [ 436050.335 5011527.521] <-> IFC [ 436046.16695688 5011529.69142313] (dist: 4.70m, conf: 0.687)\n", "Match 2764: <PERSON><PERSON> [ 435468.336 5011986.131] <-> IFC [ 435466.16695688 5011983.69142313] (dist: 3.26m, conf: 0.782)\n", "Match 2765: <PERSON><PERSON> [ 435636.489 5011503.744] <-> IFC [ 435638.16695688 5011507.69142313] (dist: 4.29m, conf: 0.714)\n", "Match 2766: <PERSON><PERSON> [ 436200.635 5012373.661] <-> IFC [ 436196.16695688 5012371.69142313] (dist: 4.88m, conf: 0.674)\n", "Match 2767: <PERSON><PERSON> [ 435554.72  5011827.615] <-> IFC [ 435552.16695688 5011825.69142313] (dist: 3.20m, conf: 0.787)\n", "Match 2768: <PERSON><PERSON> [ 435891.233 5011136.974] <-> IFC [ 435894.16695688 5011139.69142313] (dist: 4.00m, conf: 0.733)\n", "Match 2769: <PERSON><PERSON> [ 436487.961 5011776.241] <-> IFC [ 436486.16695688 5011775.69142313] (dist: 1.88m, conf: 0.875)\n", "Match 2770: <PERSON><PERSON> [ 436014.084 5012214.158] <-> IFC [ 436016.16695688 5012209.69142313] (dist: 4.93m, conf: 0.671)\n", "Match 2771: <PERSON><PERSON> [ 436020.351 5011770.266] <-> IFC [ 436028.16695688 5011763.69142313] (dist: 10.21m, conf: 0.319)\n", "Match 2772: <PERSON><PERSON> [ 436373.81  5011878.289] <-> IFC [ 436372.16695688 5011874.69142313] (dist: 3.96m, conf: 0.736)\n", "Match 2773: <PERSON><PERSON> [ 436155.055 5012277.122] <-> IFC [ 436158.16695688 5012273.69142313] (dist: 4.63m, conf: 0.691)\n", "Match 2774: <PERSON><PERSON> [ 435657.211 5011968.786] <-> IFC [ 435656.16695688 5011967.69142313] (dist: 1.51m, conf: 0.899)\n", "Match 2775: <PERSON><PERSON> [ 435825.441 5011236.858] <-> IFC [ 435828.16695688 5011239.69142313] (dist: 3.93m, conf: 0.738)\n", "Match 2776: <PERSON><PERSON> [ 436137.354 5011785.354] <-> IFC [ 436132.16695688 5011785.69142313] (dist: 5.20m, conf: 0.653)\n", "Match 2777: <PERSON><PERSON> [ 436344.18  5011164.831] <-> IFC [ 436340.16695688 5011161.69142313] (dist: 5.10m, conf: 0.660)\n", "Match 2778: <PERSON><PERSON> [ 436212.554 5011896.742] <-> IFC [ 436208.16695688 5011899.69142313] (dist: 5.29m, conf: 0.648)\n", "Match 2779: <PERSON><PERSON> [ 435948.335 5011586.975] <-> IFC [ 435952.16695688 5011585.69142313] (dist: 4.04m, conf: 0.731)\n", "Match 2780: <PERSON><PERSON> [ 436295.729 5012121.675] <-> IFC [ 436292.16695688 5012121.69142313] (dist: 3.56m, conf: 0.763)\n", "Match 2781: <PERSON><PERSON> [ 435629.989 5011941.176] <-> IFC [ 435628.16695688 5011940.69142313] (dist: 1.89m, conf: 0.874)\n", "Match 2782: <PERSON><PERSON> [ 436203.345 5011989.313] <-> IFC [ 436208.16695688 5011991.69142313] (dist: 5.38m, conf: 0.642)\n", "Match 2783: <PERSON><PERSON> [ 436080.398 5011182.469] <-> IFC [ 436084.16695688 5011187.69142313] (dist: 6.44m, conf: 0.571)\n", "Match 2784: <PERSON><PERSON> [ 435953.781 5011406.953] <-> IFC [ 435960.16695688 5011410.69142313] (dist: 7.40m, conf: 0.507)\n", "Match 2785: <PERSON><PERSON> [ 436035.636 5011797.173] <-> IFC [ 436036.16695688 5011797.69142313] (dist: 0.74m, conf: 0.951)\n", "Match 2786: <PERSON><PERSON> [ 435983.966 5011908.793] <-> IFC [ 435980.16695688 5011909.69142313] (dist: 3.90m, conf: 0.740)\n", "Match 2787: <PERSON><PERSON> [ 436404.665 5012166.184] <-> IFC [ 436406.16695688 5012163.69142313] (dist: 2.91m, conf: 0.806)\n", "Match 2788: <PERSON><PERSON> [ 435972.572 5012235.154] <-> IFC [ 435978.16695688 5012230.69142313] (dist: 7.16m, conf: 0.523)\n", "Match 2789: <PERSON><PERSON> [ 436545.264 5011727.962] <-> IFC [ 436544.16695688 5011726.69142313] (dist: 1.68m, conf: 0.888)\n", "Match 2790: <PERSON><PERSON> [ 436313.99  5011116.677] <-> IFC [ 436312.16695688 5011118.69142313] (dist: 2.72m, conf: 0.819)\n", "Match 2791: <PERSON><PERSON> [ 435684.543 5011902.329] <-> IFC [ 435684.16695688 5011905.69142313] (dist: 3.38m, conf: 0.774)\n", "Match 2792: <PERSON><PERSON> [ 435701.979 5011455.228] <-> IFC [ 435704.16695688 5011457.69142313] (dist: 3.29m, conf: 0.780)\n", "Match 2793: <PERSON><PERSON> [ 436305.001 5012373.761] <-> IFC [ 436300.16695688 5012371.69142313] (dist: 5.26m, conf: 0.649)\n", "Match 2794: <PERSON><PERSON> [ 435414.122 5011692.757] <-> IFC [ 435410.16695688 5011693.69142313] (dist: 4.06m, conf: 0.729)\n", "Match 2795: <PERSON><PERSON> [ 436005.386 5011910.913] <-> IFC [ 436008.16695688 5011909.69142313] (dist: 3.04m, conf: 0.798)\n", "Match 2796: <PERSON><PERSON> [ 436452.56 5012118.48] <-> IFC [ 436452.16695688 5012119.69142313] (dist: 1.27m, conf: 0.915)\n", "Match 2797: <PERSON><PERSON> [ 436065.427 5012376.17 ] <-> IFC [ 436064.16695688 5012379.69142313] (dist: 3.74m, conf: 0.751)\n", "Match 2798: <PERSON><PERSON> [ 435351.608 5011686.803] <-> IFC [ 435352.16695688 5011685.69142313] (dist: 1.24m, conf: 0.917)\n", "Match 2799: <PERSON><PERSON> [ 435908.764 5011614.791] <-> IFC [ 435914.16695688 5011613.69142313] (dist: 5.51m, conf: 0.632)\n", "Match 2800: <PERSON><PERSON> [ 436331.93 5012334.7 ] <-> IFC [ 436330.16695688 5012335.69142313] (dist: 2.02m, conf: 0.865)\n", "Match 2801: <PERSON><PERSON> [ 436473.359 5011188.36 ] <-> IFC [ 436474.16695688 5011185.69142313] (dist: 2.79m, conf: 0.814)\n", "Match 2802: <PERSON><PERSON> [ 435857.706 5011857.859] <-> IFC [ 435856.16695688 5011855.69142313] (dist: 2.66m, conf: 0.823)\n", "Match 2803: <PERSON><PERSON> [ 436359.629 5012256.601] <-> IFC [ 436358.16695688 5012256.69142313] (dist: 1.46m, conf: 0.902)\n", "Match 2804: <PERSON><PERSON> [ 435741.529 5011452.516] <-> IFC [ 435742.16695688 5011454.69142313] (dist: 2.27m, conf: 0.849)\n", "Match 2805: <PERSON><PERSON> [ 435705.502 5011455.59 ] <-> IFC [ 435704.16695688 5011457.69142313] (dist: 2.49m, conf: 0.834)\n", "Match 2806: <PERSON><PERSON> [ 436625.743 5011895.977] <-> IFC [ 436624.16695688 5011892.69142313] (dist: 3.64m, conf: 0.757)\n", "Match 2807: <PERSON><PERSON> [ 435968.848 5011533.85 ] <-> IFC [ 435970.16695688 5011531.69142313] (dist: 2.53m, conf: 0.831)\n", "Match 2808: <PERSON><PERSON> [ 436359.517 5011959.225] <-> IFC [ 436360.16695688 5011957.69142313] (dist: 1.67m, conf: 0.889)\n", "Match 2809: <PERSON><PERSON> [ 435702.253 5011929.396] <-> IFC [ 435704.16695688 5011927.69142313] (dist: 2.56m, conf: 0.829)\n", "Match 2810: <PERSON><PERSON> [ 435702.437 5011872.296] <-> IFC [ 435704.16695688 5011868.69142313] (dist: 4.00m, conf: 0.733)\n", "Match 2811: <PERSON><PERSON> [ 435617.698 5011431.369] <-> IFC [ 435618.16695688 5011431.69142313] (dist: 0.57m, conf: 0.962)\n", "Match 2812: <PERSON><PERSON> [ 435608.75 5011848.81] <-> IFC [ 435610.16695688 5011849.69142313] (dist: 1.67m, conf: 0.889)\n", "Match 2813: <PERSON><PERSON> [ 435345.415 5011836.654] <-> IFC [ 435344.16695688 5011837.69142313] (dist: 1.62m, conf: 0.892)\n", "Match 2814: <PERSON><PERSON> [ 436005.542 5011020.161] <-> IFC [ 435998.16695688 5011023.69142313] (dist: 8.18m, conf: 0.455)\n", "Match 2815: <PERSON><PERSON> [ 436176.632 5011251.113] <-> IFC [ 436179.76695688 5011252.09142313] (dist: 3.28m, conf: 0.781)\n", "Match 2816: <PERSON><PERSON> [ 436182.58  5012429.873] <-> IFC [ 436178.16695688 5012427.69142313] (dist: 4.92m, conf: 0.672)\n", "Match 2817: <PERSON><PERSON> [ 436257.553 5011925.883] <-> IFC [ 436256.16695688 5011927.69142313] (dist: 2.28m, conf: 0.848)\n", "Match 2818: <PERSON><PERSON> [ 436008.285 5011773.011] <-> IFC [ 436008.16695688 5011771.69142313] (dist: 1.32m, conf: 0.912)\n", "Match 2819: <PERSON><PERSON> [ 436145.83  5011028.986] <-> IFC [ 436150.16695688 5011027.69142313] (dist: 4.53m, conf: 0.698)\n", "Match 2820: <PERSON><PERSON> [ 435929.886 5011137.351] <-> IFC [ 435932.16695688 5011139.69142313] (dist: 3.27m, conf: 0.782)\n", "Match 2821: <PERSON><PERSON> [ 436002.587 5011572.771] <-> IFC [ 435998.16695688 5011569.69142313] (dist: 5.39m, conf: 0.641)\n", "Match 2822: <PERSON><PERSON> [ 435495.413 5011650.174] <-> IFC [ 435496.16695688 5011648.69142313] (dist: 1.66m, conf: 0.889)\n", "Match 2823: <PERSON><PERSON> [ 435771.075 5011907.884] <-> IFC [ 435770.16695688 5011911.69142313] (dist: 3.91m, conf: 0.739)\n", "Match 2824: <PERSON><PERSON> [ 436109.832 5011659.678] <-> IFC [ 436112.16695688 5011657.69142313] (dist: 3.07m, conf: 0.796)\n", "Match 2825: <PERSON><PERSON> [ 435968.816 5011806.399] <-> IFC [ 435970.16695688 5011803.69142313] (dist: 3.03m, conf: 0.798)\n", "Match 2826: <PERSON><PERSON> [ 436416.509 5012193.472] <-> IFC [ 436414.16695688 5012193.69142313] (dist: 2.35m, conf: 0.843)\n", "Match 2827: <PERSON><PERSON> [ 436454.895 5012133.744] <-> IFC [ 436452.16695688 5012137.69142313] (dist: 4.80m, conf: 0.680)\n", "Match 2828: <PERSON><PERSON> [ 435842.714 5012133.562] <-> IFC [ 435846.16695688 5012130.69142313] (dist: 4.49m, conf: 0.701)\n", "Match 2829: <PERSON><PERSON> [ 436245.392 5012301.518] <-> IFC [ 436244.16695688 5012297.69142313] (dist: 4.02m, conf: 0.732)\n", "Match 2830: <PERSON><PERSON> [ 435795.421 5011344.459] <-> IFC [ 435790.16695688 5011345.69142313] (dist: 5.40m, conf: 0.640)\n", "Match 2831: <PERSON><PERSON> [ 436475.928 5012145.251] <-> IFC [ 436472.16695688 5012147.69142313] (dist: 4.48m, conf: 0.701)\n", "Match 2832: <PERSON><PERSON> [ 435591.616 5011497.285] <-> IFC [ 435590.16695688 5011501.69142313] (dist: 4.64m, conf: 0.691)\n", "Match 2833: <PERSON><PERSON> [ 435957.431 5011580.935] <-> IFC [ 435952.16695688 5011585.69142313] (dist: 7.09m, conf: 0.527)\n", "Match 2834: <PERSON><PERSON> [ 436500.19  5011779.585] <-> IFC [ 436496.16695688 5011777.69142313] (dist: 4.45m, conf: 0.704)\n", "Match 2835: <PERSON><PERSON> [ 436065.103 5010945.082] <-> IFC [ 436066.16695688 5010941.69142313] (dist: 3.55m, conf: 0.763)\n", "Match 2836: <PERSON><PERSON> [ 435639.372 5011542.162] <-> IFC [ 435638.16695688 5011541.69142313] (dist: 1.29m, conf: 0.914)\n", "Match 2837: <PERSON><PERSON> [ 436586.816 5011899.16 ] <-> IFC [ 436586.16695688 5011897.69142313] (dist: 1.61m, conf: 0.893)\n", "Match 2838: <PERSON><PERSON> [ 435284.911 5011947.81 ] <-> IFC [ 435286.16695688 5011939.69142313] (dist: 8.22m, conf: 0.452)\n", "Match 2839: <PERSON><PERSON> [ 435423.588 5011868.869] <-> IFC [ 435420.16695688 5011871.69142313] (dist: 4.44m, conf: 0.704)\n", "Match 2840: Dr<PERSON> [ 436179.606 5011220.944] <-> IFC [ 436188.16695688 5011223.69142313] (dist: 8.99m, conf: 0.401)\n", "Match 2841: <PERSON><PERSON> [ 435645.284 5011902.689] <-> IFC [ 435648.16695688 5011905.69142313] (dist: 4.16m, conf: 0.723)\n", "Match 2842: <PERSON><PERSON> [ 436062.325 5011788.316] <-> IFC [ 436056.16695688 5011793.69142313] (dist: 8.17m, conf: 0.455)\n", "Match 2843: <PERSON><PERSON> [ 436112.787 5012286.05 ] <-> IFC [ 436110.16695688 5012289.69142313] (dist: 4.49m, conf: 0.701)\n", "Match 2844: <PERSON><PERSON> [ 435411.037 5011709.927] <-> IFC [ 435410.16695688 5011717.69142313] (dist: 7.81m, conf: 0.479)\n", "Match 2845: <PERSON><PERSON> [ 436229.922 5012154.316] <-> IFC [ 436234.16695688 5012153.69142313] (dist: 4.29m, conf: 0.714)\n", "Match 2846: <PERSON><PERSON> [ 436079.918 5011512.589] <-> IFC [ 436084.16695688 5011507.69142313] (dist: 6.48m, conf: 0.568)\n", "Match 2847: <PERSON><PERSON> [ 436289.876 5011527.743] <-> IFC [ 436288.16695688 5011531.69142313] (dist: 4.30m, conf: 0.713)\n", "Match 2848: <PERSON><PERSON> [ 436349.789 5012139.149] <-> IFC [ 436348.16695688 5012141.69142313] (dist: 3.02m, conf: 0.799)\n", "Match 2849: <PERSON><PERSON> [ 435929.949 5011685.898] <-> IFC [ 435932.16695688 5011685.69142313] (dist: 2.23m, conf: 0.851)\n", "Match 2850: <PERSON><PERSON> [ 436080.389 5011748.948] <-> IFC [ 436084.16695688 5011751.69142313] (dist: 4.67m, conf: 0.689)\n", "Match 2851: <PERSON><PERSON> [ 435909.587 5011575.054] <-> IFC [ 435913.16695688 5011571.69142313] (dist: 4.91m, conf: 0.673)\n", "Match 2852: <PERSON><PERSON> [ 435824.792 5011272.021] <-> IFC [ 435828.16695688 5011271.69142313] (dist: 3.39m, conf: 0.774)\n", "Match 2853: <PERSON><PERSON> [ 435687.382 5011812.733] <-> IFC [ 435686.16695688 5011811.69142313] (dist: 1.60m, conf: 0.893)\n", "Match 2854: <PERSON><PERSON> [ 436335.046 5012274.834] <-> IFC [ 436338.16695688 5012271.69142313] (dist: 4.43m, conf: 0.705)\n", "Match 2855: <PERSON><PERSON> [ 436239.187 5011199.91 ] <-> IFC [ 436246.16695688 5011199.69142313] (dist: 6.98m, conf: 0.534)\n", "Match 2856: <PERSON><PERSON> [ 436286.791 5011875.601] <-> IFC [ 436284.16695688 5011877.69142313] (dist: 3.35m, conf: 0.776)\n", "Match 2857: <PERSON><PERSON> [ 436199.896 5012289.67 ] <-> IFC [ 436196.16695688 5012287.69142313] (dist: 4.22m, conf: 0.719)\n", "Match 2858: <PERSON><PERSON> [ 436349.917 5012273.93 ] <-> IFC [ 436348.16695688 5012277.69142313] (dist: 4.15m, conf: 0.723)\n", "Match 2859: <PERSON><PERSON> [ 435686.874 5012073.539] <-> IFC [ 435686.16695688 5012073.69142313] (dist: 0.72m, conf: 0.952)\n", "Match 2860: <PERSON><PERSON> [ 436070.86  5011985.898] <-> IFC [ 436074.16695688 5011984.69142313] (dist: 3.52m, conf: 0.765)\n", "Match 2861: <PERSON><PERSON> [ 436281.666 5011551.238] <-> IFC [ 436278.16695688 5011547.69142313] (dist: 4.98m, conf: 0.668)\n", "Match 2862: <PERSON><PERSON> [ 436431.54 5011836.31] <-> IFC [ 436430.16695688 5011833.69142313] (dist: 2.96m, conf: 0.803)\n", "Match 2863: <PERSON><PERSON> [ 436047.65  5012238.858] <-> IFC [ 436044.16695688 5012234.69142313] (dist: 5.43m, conf: 0.638)\n", "Match 2864: <PERSON><PERSON> [ 436095.143 5011005.557] <-> IFC [ 436094.16695688 5011001.69142313] (dist: 3.99m, conf: 0.734)\n", "Match 2865: <PERSON><PERSON> [ 435582.03  5011782.377] <-> IFC [ 435580.16695688 5011781.69142313] (dist: 1.99m, conf: 0.868)\n", "Match 2866: <PERSON><PERSON> [ 435789.629 5011254.594] <-> IFC [ 435790.16695688 5011249.69142313] (dist: 4.93m, conf: 0.671)\n", "Match 2867: <PERSON><PERSON> [ 435996.228 5011506.725] <-> IFC [ 435998.16695688 5011511.69142313] (dist: 5.33m, conf: 0.645)\n", "Match 2868: <PERSON><PERSON> [ 436097.956 5011881.783] <-> IFC [ 436094.16695688 5011883.69142313] (dist: 4.24m, conf: 0.717)\n", "Match 2869: <PERSON><PERSON> [ 436026.469 5010924.011] <-> IFC [ 436028.16695688 5010929.69142313] (dist: 5.93m, conf: 0.605)\n", "Match 2870: <PERSON><PERSON> [ 436047.284 5011788.208] <-> IFC [ 436046.16695688 5011783.69142313] (dist: 4.65m, conf: 0.690)\n", "Match 2871: <PERSON><PERSON> [ 435837.407 5011215.776] <-> IFC [ 435838.16695688 5011219.69142313] (dist: 3.99m, conf: 0.734)\n", "Match 2872: <PERSON><PERSON> [ 435734.902 5012049.501] <-> IFC [ 435732.16695688 5012051.69142313] (dist: 3.50m, conf: 0.766)\n", "Match 2873: <PERSON><PERSON> [ 435990.178 5011068.072] <-> IFC [ 435990.16695688 5011071.69142313] (dist: 3.62m, conf: 0.759)\n", "Match 2874: <PERSON><PERSON> [ 435525.413 5011677.313] <-> IFC [ 435524.16695688 5011679.69142313] (dist: 2.69m, conf: 0.821)\n", "Match 2875: <PERSON><PERSON> [ 436208.733 5011395.077] <-> IFC [ 436208.16695688 5011389.69142313] (dist: 5.42m, conf: 0.639)\n", "Match 2876: <PERSON><PERSON> [ 436118.768 5011581.462] <-> IFC [ 436122.16695688 5011577.69142313] (dist: 5.08m, conf: 0.662)\n", "Match 2877: <PERSON><PERSON> [ 436209.005 5012421.272] <-> IFC [ 436206.16695688 5012417.69142313] (dist: 4.57m, conf: 0.695)\n", "Match 2878: <PERSON><PERSON> [ 435786.282 5011242.024] <-> IFC [ 435790.16695688 5011239.69142313] (dist: 4.53m, conf: 0.698)\n", "Match 2879: <PERSON><PERSON> [ 436220.716 5012262.597] <-> IFC [ 436224.16695688 5012263.69142313] (dist: 3.62m, conf: 0.759)\n", "Match 2880: <PERSON><PERSON> [ 436026.577 5011871.907] <-> IFC [ 436018.16695688 5011871.69142313] (dist: 8.41m, conf: 0.439)\n", "Match 2881: <PERSON><PERSON> [ 436038.529 5012108.97 ] <-> IFC [ 436036.16695688 5012107.69142313] (dist: 2.69m, conf: 0.821)\n", "Match 2882: <PERSON><PERSON> [ 436280.733 5011542.404] <-> IFC [ 436278.16695688 5011539.69142313] (dist: 3.73m, conf: 0.751)\n", "Match 2883: <PERSON><PERSON> [ 436098.009 5012316.286] <-> IFC [ 436102.16695688 5012317.69142313] (dist: 4.39m, conf: 0.707)\n", "Match 2884: <PERSON><PERSON> [ 435927.166 5012091.076] <-> IFC [ 435932.16695688 5012091.69142313] (dist: 5.04m, conf: 0.664)\n", "Match 2885: <PERSON><PERSON> [ 435771.472 5011575.298] <-> IFC [ 435770.16695688 5011573.69142313] (dist: 2.07m, conf: 0.862)\n", "Match 2886: <PERSON><PERSON> [ 435591.097 5012085.686] <-> IFC [ 435590.16695688 5012087.69142313] (dist: 2.21m, conf: 0.853)\n", "Match 2887: <PERSON><PERSON> [ 435834.492 5011701.148] <-> IFC [ 435828.16695688 5011699.69142313] (dist: 6.49m, conf: 0.567)\n", "Match 2888: <PERSON><PERSON> [ 436181.713 5011281.263] <-> IFC [ 436179.16695688 5011281.69142313] (dist: 2.58m, conf: 0.828)\n", "Match 2889: <PERSON><PERSON> [ 436434.46  5012103.469] <-> IFC [ 436434.16695688 5012101.69142313] (dist: 1.80m, conf: 0.880)\n", "Match 2890: Dr<PERSON> [ 435848.776 5012024.867] <-> IFC [ 435846.16695688 5012021.69142313] (dist: 4.11m, conf: 0.726)\n", "Match 2891: <PERSON><PERSON> [ 436022.818 5011653.467] <-> IFC [ 436018.16695688 5011651.69142313] (dist: 4.98m, conf: 0.668)\n", "Match 2892: <PERSON><PERSON> [ 435750.068 5011971.112] <-> IFC [ 435752.16695688 5011971.69142313] (dist: 2.18m, conf: 0.855)\n", "Match 2893: <PERSON><PERSON> [ 436013.855 5011752.42 ] <-> IFC [ 436018.16695688 5011753.69142313] (dist: 4.50m, conf: 0.700)\n", "Match 2894: <PERSON><PERSON> [ 436005.609 5010992.956] <-> IFC [ 436008.16695688 5010995.69142313] (dist: 3.75m, conf: 0.750)\n", "Match 2895: <PERSON><PERSON> [ 436176.575 5010966.355] <-> IFC [ 436179.16695688 5010969.69142313] (dist: 4.22m, conf: 0.718)\n", "Match 2896: <PERSON><PERSON> [ 436080.509 5010981.809] <-> IFC [ 436084.16695688 5010981.69142313] (dist: 3.66m, conf: 0.756)\n", "Match 2897: <PERSON><PERSON> [ 436683.492 5011824.136] <-> IFC [ 436682.16695688 5011822.69142313] (dist: 1.96m, conf: 0.869)\n", "Match 2898: <PERSON><PERSON> [ 436238.817 5011854.614] <-> IFC [ 436236.16695688 5011857.69142313] (dist: 4.06m, conf: 0.729)\n", "Match 2899: <PERSON><PERSON> [ 435723.664 5011716.549] <-> IFC [ 435724.16695688 5011713.69142313] (dist: 2.90m, conf: 0.807)\n", "Match 2900: <PERSON><PERSON> [ 435876.419 5011787.955] <-> IFC [ 435874.16695688 5011785.69142313] (dist: 3.19m, conf: 0.787)\n", "Match 2901: <PERSON><PERSON> [ 435536.917 5011734.184] <-> IFC [ 435534.16695688 5011735.69142313] (dist: 3.14m, conf: 0.791)\n", "Match 2902: <PERSON><PERSON> [ 436215.633 5011635.766] <-> IFC [ 436212.16695688 5011632.69142313] (dist: 4.63m, conf: 0.691)\n", "Match 2903: <PERSON><PERSON> [ 435960.065 5011692.571] <-> IFC [ 435970.16695688 5011693.69142313] (dist: 10.16m, conf: 0.322)\n", "Match 2904: <PERSON><PERSON> [ 436020.618 5011071.84 ] <-> IFC [ 436027.16695688 5011067.69142313] (dist: 7.75m, conf: 0.483)\n", "Match 2905: <PERSON><PERSON> [ 436197.296 5012430.381] <-> IFC [ 436196.16695688 5012431.69142313] (dist: 1.73m, conf: 0.885)\n", "Match 2906: <PERSON><PERSON> [ 435474.132 5011770.256] <-> IFC [ 435476.16695688 5011773.69142313] (dist: 3.99m, conf: 0.734)\n", "Match 2907: <PERSON><PERSON> [ 435873.228 5012043.065] <-> IFC [ 435876.16695688 5012043.69142313] (dist: 3.00m, conf: 0.800)\n", "Match 2908: <PERSON><PERSON> [ 435806.69  5011392.497] <-> IFC [ 435808.16695688 5011391.69142313] (dist: 1.68m, conf: 0.888)\n", "Match 2909: <PERSON><PERSON> [ 435906.288 5012243.866] <-> IFC [ 435902.16695688 5012239.69142313] (dist: 5.87m, conf: 0.609)\n", "Match 2910: <PERSON><PERSON> [ 435639.478 5011550.938] <-> IFC [ 435638.16695688 5011549.69142313] (dist: 1.81m, conf: 0.879)\n", "Match 2911: <PERSON><PERSON> [ 436653.228 5011884.305] <-> IFC [ 436654.16695688 5011881.69142313] (dist: 2.78m, conf: 0.815)\n", "Match 2912: <PERSON><PERSON> [ 435438.363 5011794.658] <-> IFC [ 435438.16695688 5011787.69142313] (dist: 6.97m, conf: 0.535)\n", "Match 2913: <PERSON><PERSON> [ 436212.45  5011986.309] <-> IFC [ 436208.16695688 5011983.69142313] (dist: 5.02m, conf: 0.665)\n", "Match 2914: <PERSON><PERSON> [ 435747.5   5011692.119] <-> IFC [ 435752.16695688 5011689.69142313] (dist: 5.26m, conf: 0.649)\n", "Match 2915: <PERSON><PERSON> [ 436550.848 5011679.989] <-> IFC [ 436544.16695688 5011683.69142313] (dist: 7.64m, conf: 0.491)\n", "Match 2916: Dr<PERSON> [ 435788.966 5012037.652] <-> IFC [ 435790.16695688 5012039.69142313] (dist: 2.37m, conf: 0.842)\n", "Match 2917: <PERSON><PERSON> [ 436376.713 5011701.594] <-> IFC [ 436372.16695688 5011697.69142313] (dist: 5.99m, conf: 0.601)\n", "Match 2918: Dr<PERSON> [ 435846.114 5011623.509] <-> IFC [ 435846.16695688 5011625.69142313] (dist: 2.18m, conf: 0.854)\n", "Match 2919: <PERSON><PERSON> [ 435813.318 5011275.692] <-> IFC [ 435818.16695688 5011281.69142313] (dist: 7.71m, conf: 0.486)\n", "Match 2920: <PERSON><PERSON> [ 435623.999 5011383.818] <-> IFC [ 435628.16695688 5011385.69142313] (dist: 4.57m, conf: 0.695)\n", "Match 2921: <PERSON><PERSON> [ 436190.751 5012433.377] <-> IFC [ 436186.16695688 5012433.69142313] (dist: 4.59m, conf: 0.694)\n", "Match 2922: <PERSON><PERSON> [ 435564.298 5011875.731] <-> IFC [ 435562.16695688 5011875.69142313] (dist: 2.13m, conf: 0.858)\n", "Match 2923: <PERSON><PERSON> [ 435414.115 5011974.143] <-> IFC [ 435410.16695688 5011976.69142313] (dist: 4.70m, conf: 0.687)\n", "Match 2924: <PERSON><PERSON> [ 436338.147 5012214.015] <-> IFC [ 436338.16695688 5012213.69142313] (dist: 0.32m, conf: 0.978)\n", "Match 2925: <PERSON><PERSON> [ 436016.866 5011794.129] <-> IFC [ 436018.16695688 5011798.69142313] (dist: 4.74m, conf: 0.684)\n", "Match 2926: <PERSON><PERSON> [ 435657.083 5011563.066] <-> IFC [ 435656.16695688 5011563.69142313] (dist: 1.11m, conf: 0.926)\n", "Match 2927: <PERSON><PERSON> [ 436056.107 5011796.979] <-> IFC [ 436056.16695688 5011793.69142313] (dist: 3.29m, conf: 0.781)\n", "Match 2928: <PERSON><PERSON> [ 436586.954 5011869.007] <-> IFC [ 436586.16695688 5011864.69142313] (dist: 4.39m, conf: 0.708)\n", "Match 2929: <PERSON><PERSON> [ 435306.123 5011724.888] <-> IFC [ 435314.16695688 5011729.69142313] (dist: 9.37m, conf: 0.375)\n", "Match 2930: Dr<PERSON> [ 436142.927 5011779.25 ] <-> IFC [ 436141.16695688 5011781.69142313] (dist: 3.01m, conf: 0.799)\n", "Match 2931: <PERSON><PERSON> [ 436035.004 5011068.168] <-> IFC [ 436036.16695688 5011065.69142313] (dist: 2.74m, conf: 0.818)\n", "Match 2932: <PERSON><PERSON> [ 436032.281 5011896.459] <-> IFC [ 436036.16695688 5011895.69142313] (dist: 3.96m, conf: 0.736)\n", "Match 2933: <PERSON><PERSON> [ 436107.383 5011872.767] <-> IFC [ 436104.16695688 5011875.69142313] (dist: 4.35m, conf: 0.710)\n", "Match 2934: Dr<PERSON> [ 435666.289 5011716.78 ] <-> IFC [ 435666.16695688 5011721.69142313] (dist: 4.91m, conf: 0.672)\n", "Match 2935: <PERSON><PERSON> [ 436050.337 5011863.399] <-> IFC [ 436046.16695688 5011863.69142313] (dist: 4.18m, conf: 0.721)\n", "Match 2936: <PERSON><PERSON> [ 435906.372 5011088.899] <-> IFC [ 435904.16695688 5011083.69142313] (dist: 5.66m, conf: 0.623)\n", "Match 2937: <PERSON><PERSON> [ 435918.62  5011638.341] <-> IFC [ 435914.16695688 5011637.69142313] (dist: 4.50m, conf: 0.700)\n", "Match 2938: <PERSON><PERSON> [ 436050.628 5012109.262] <-> IFC [ 436046.16695688 5012107.69142313] (dist: 4.73m, conf: 0.685)\n", "Match 2939: <PERSON><PERSON> [ 435947.917 5011983.521] <-> IFC [ 435951.30981402 5011984.548566  ] (dist: 3.55m, conf: 0.764)\n", "Match 2940: <PERSON><PERSON> [ 436215.139 5011656.47 ] <-> IFC [ 436212.16695688 5011657.69142313] (dist: 3.21m, conf: 0.786)\n", "Match 2941: <PERSON><PERSON> [ 436257.572 5011863.528] <-> IFC [ 436255.16695688 5011863.69142313] (dist: 2.41m, conf: 0.839)\n", "Match 2942: <PERSON><PERSON> [ 436259.931 5011194.188] <-> IFC [ 436256.16695688 5011191.69142313] (dist: 4.52m, conf: 0.699)\n", "Match 2943: <PERSON><PERSON> [ 435779.753 5011299.441] <-> IFC [ 435780.16695688 5011301.69142313] (dist: 2.29m, conf: 0.847)\n", "Match 2944: <PERSON><PERSON> [ 436071.236 5011785.508] <-> IFC [ 436074.16695688 5011782.69142313] (dist: 4.06m, conf: 0.729)\n", "Match 2945: <PERSON><PERSON> [ 435621.593 5011557.145] <-> IFC [ 435618.16695688 5011565.69142313] (dist: 9.21m, conf: 0.386)\n", "Match 2946: <PERSON><PERSON> [ 435998.905 5011803.43 ] <-> IFC [ 435998.16695688 5011801.69142313] (dist: 1.89m, conf: 0.874)\n", "Match 2947: <PERSON><PERSON> [ 436074.47  5011356.283] <-> IFC [ 436074.16695688 5011365.69142313] (dist: 9.41m, conf: 0.372)\n", "Match 2948: <PERSON><PERSON> [ 435348.258 5011812.196] <-> IFC [ 435344.16695688 5011811.69142313] (dist: 4.12m, conf: 0.725)\n", "Match 2949: <PERSON><PERSON> [ 435983.975 5011794.287] <-> IFC [ 435980.16695688 5011791.69142313] (dist: 4.61m, conf: 0.693)\n", "Match 2950: <PERSON><PERSON> [ 436385.997 5011842.27 ] <-> IFC [ 436382.16695688 5011839.69142313] (dist: 4.62m, conf: 0.692)\n", "Match 2951: <PERSON><PERSON> [ 436106.951 5012255.89 ] <-> IFC [ 436110.16695688 5012255.69142313] (dist: 3.22m, conf: 0.785)\n", "Match 2952: <PERSON><PERSON> [ 436515.071 5011689.768] <-> IFC [ 436516.16695688 5011687.69142313] (dist: 2.35m, conf: 0.843)\n", "Match 2953: <PERSON><PERSON> [ 436292.908 5011767.33 ] <-> IFC [ 436288.16695688 5011765.69142313] (dist: 5.02m, conf: 0.666)\n", "Match 2954: <PERSON><PERSON> [ 436302.094 5012133.313] <-> IFC [ 436300.16695688 5012134.69142313] (dist: 2.37m, conf: 0.842)\n", "Match 2955: <PERSON><PERSON> [ 436614.583 5011902.166] <-> IFC [ 436616.16695688 5011903.69142313] (dist: 2.20m, conf: 0.853)\n", "Match 2956: <PERSON><PERSON> [ 435740.895 5012046.729] <-> IFC [ 435732.16695688 5012051.69142313] (dist: 10.04m, conf: 0.331)\n", "Match 2957: <PERSON><PERSON> [ 436263.184 5012361.297] <-> IFC [ 436262.16695688 5012359.69142313] (dist: 1.90m, conf: 0.873)\n", "Match 2958: <PERSON><PERSON> [ 436293.375 5011793.988] <-> IFC [ 436296.16695688 5011795.69142313] (dist: 3.27m, conf: 0.782)\n", "Match 2959: <PERSON><PERSON> [ 435917.832 5012094.337] <-> IFC [ 435922.16695688 5012093.69142313] (dist: 4.38m, conf: 0.708)\n", "Match 2960: <PERSON><PERSON> [ 436463.944 5012144.916] <-> IFC [ 436462.16695688 5012141.69142313] (dist: 3.68m, conf: 0.755)\n", "Match 2961: <PERSON><PERSON> [ 436349.957 5011965.136] <-> IFC [ 436350.16695688 5011965.69142313] (dist: 0.59m, conf: 0.960)\n", "Match 2962: <PERSON><PERSON> [ 436485.296 5011898.896] <-> IFC [ 436482.16695688 5011899.69142313] (dist: 3.23m, conf: 0.785)\n", "Match 2963: <PERSON><PERSON> [ 436142.851 5012337.68 ] <-> IFC [ 436140.16695688 5012339.69142313] (dist: 3.35m, conf: 0.776)\n", "Match 2964: <PERSON><PERSON> [ 435752.903 5011506.246] <-> IFC [ 435752.16695688 5011501.69142313] (dist: 4.61m, conf: 0.692)\n", "Match 2965: <PERSON><PERSON> [ 436328.796 5012229.812] <-> IFC [ 436330.16695688 5012233.69142313] (dist: 4.11m, conf: 0.726)\n", "Match 2966: <PERSON><PERSON> [ 435986.912 5012148.665] <-> IFC [ 435990.16695688 5012153.69142313] (dist: 5.99m, conf: 0.601)\n", "Match 2967: <PERSON><PERSON> [ 435618.316 5011473.738] <-> IFC [ 435618.16695688 5011465.69142313] (dist: 8.05m, conf: 0.463)\n", "Match 2968: <PERSON><PERSON> [ 435438.482 5011860.858] <-> IFC [ 435438.16695688 5011857.69142313] (dist: 3.18m, conf: 0.788)\n", "Match 2969: <PERSON><PERSON> [ 436151.835 5012160.603] <-> IFC [ 436148.16695688 5012161.69142313] (dist: 3.83m, conf: 0.745)\n", "Match 2970: <PERSON><PERSON> [ 436521.111 5011788.306] <-> IFC [ 436516.16695688 5011787.69142313] (dist: 4.98m, conf: 0.668)\n", "Match 2971: <PERSON><PERSON> [ 435675.334 5011944.391] <-> IFC [ 435676.16695688 5011939.69142313] (dist: 4.77m, conf: 0.682)\n", "Match 2972: <PERSON><PERSON> [ 435516.033 5011773.073] <-> IFC [ 435514.16695688 5011771.69142313] (dist: 2.32m, conf: 0.845)\n", "Match 2973: <PERSON><PERSON> [ 436005.538 5011737.453] <-> IFC [ 436008.16695688 5011739.69142313] (dist: 3.45m, conf: 0.770)\n", "Match 2974: <PERSON><PERSON> [ 436140.218 5012187.859] <-> IFC [ 436140.16695688 5012187.69142313] (dist: 0.18m, conf: 0.988)\n", "Match 2975: <PERSON><PERSON> [ 436023.615 5011512.229] <-> IFC [ 436027.16695688 5011515.69142313] (dist: 4.96m, conf: 0.669)\n", "Match 2976: <PERSON><PERSON> [ 436137.553 5011764.503] <-> IFC [ 436142.16695688 5011765.69142313] (dist: 4.76m, conf: 0.682)\n", "Match 2977: <PERSON><PERSON> [ 436203.346 5011394.878] <-> IFC [ 436198.16695688 5011397.69142313] (dist: 5.89m, conf: 0.607)\n", "Match 2978: <PERSON><PERSON> [ 436371.098 5012172.062] <-> IFC [ 436368.16695688 5012169.69142313] (dist: 3.77m, conf: 0.749)\n", "Match 2979: <PERSON><PERSON> [ 435828.242 5011535.931] <-> IFC [ 435828.16695688 5011539.69142313] (dist: 3.76m, conf: 0.749)\n", "Match 2980: Drone [ 435831.202 5012043.434] <-> IFC [ 435828.16695688 5012045.69142313] (dist: 3.78m, conf: 0.748)\n", "Match 2981: <PERSON><PERSON> [ 435965.723 5011041.688] <-> IFC [ 435970.16695688 5011043.69142313] (dist: 4.87m, conf: 0.675)\n", "Match 2982: <PERSON><PERSON> [ 435882.509 5011467.673] <-> IFC [ 435884.16695688 5011465.69142313] (dist: 2.58m, conf: 0.828)\n", "Match 2983: <PERSON><PERSON> [ 436536.604 5012031.18 ] <-> IFC [ 436538.16695688 5012029.69142313] (dist: 2.16m, conf: 0.856)\n", "Match 2984: <PERSON><PERSON> [ 435686.85  5011419.155] <-> IFC [ 435686.16695688 5011425.69142313] (dist: 6.57m, conf: 0.562)\n", "Match 2985: <PERSON><PERSON> [ 436127.736 5011920.293] <-> IFC [ 436132.16695688 5011923.69142313] (dist: 5.58m, conf: 0.628)\n", "Match 2986: Dr<PERSON> [ 435401.732 5011770.835] <-> IFC [ 435400.16695688 5011771.69142313] (dist: 1.78m, conf: 0.881)\n", "Match 2987: <PERSON><PERSON> [ 436116.473 5010912.859] <-> IFC [ 436112.16695688 5010913.69142313] (dist: 4.39m, conf: 0.708)\n", "Match 2988: <PERSON><PERSON> [ 436152.393 5011464.457] <-> IFC [ 436150.16695688 5011459.69142313] (dist: 5.26m, conf: 0.649)\n", "Match 2989: Dr<PERSON> [ 435675.204 5011434.559] <-> IFC [ 435676.16695688 5011433.69142313] (dist: 1.30m, conf: 0.914)\n", "Match 2990: <PERSON><PERSON> [ 435447.379 5012025.428] <-> IFC [ 435448.16695688 5012023.69142313] (dist: 1.91m, conf: 0.873)\n", "Match 2991: <PERSON><PERSON> [ 436062.125 5011503.212] <-> IFC [ 436056.16695688 5011503.69142313] (dist: 5.98m, conf: 0.602)\n", "Match 2992: <PERSON><PERSON> [ 436323.102 5011923.615] <-> IFC [ 436322.16695688 5011925.69142313] (dist: 2.28m, conf: 0.848)\n", "Match 2993: <PERSON><PERSON> [ 435477.299 5012016.232] <-> IFC [ 435476.16695688 5012014.69142313] (dist: 1.91m, conf: 0.873)\n", "Match 2994: <PERSON><PERSON> [ 436005.47  5011968.754] <-> IFC [ 436008.16695688 5011969.69142313] (dist: 2.86m, conf: 0.810)\n", "Match 2995: <PERSON><PERSON> [ 436595.899 5011803.521] <-> IFC [ 436596.16695688 5011801.69142313] (dist: 1.85m, conf: 0.877)\n", "Match 2996: <PERSON><PERSON> [ 436529.722 5012067.816] <-> IFC [ 436528.16695688 5012065.69142313] (dist: 2.63m, conf: 0.824)\n", "Match 2997: <PERSON><PERSON> [ 436038.39  5012352.037] <-> IFC [ 436034.16695688 5012355.69142313] (dist: 5.58m, conf: 0.628)\n", "Match 2998: <PERSON><PERSON> [ 436566.321 5011914.408] <-> IFC [ 436568.16695688 5011913.69142313] (dist: 1.98m, conf: 0.868)\n", "Match 2999: <PERSON><PERSON> [ 436328.92 5011563.45] <-> IFC [ 436326.16695688 5011559.69142313] (dist: 4.66m, conf: 0.689)\n", "Match 3000: <PERSON><PERSON> [ 436010.953 5011058.874] <-> IFC [ 436018.16695688 5011055.69142313] (dist: 7.88m, conf: 0.474)\n", "Match 3001: <PERSON><PERSON> [ 435918.255 5011884.325] <-> IFC [ 435913.16695688 5011883.69142313] (dist: 5.13m, conf: 0.658)\n", "Match 3002: <PERSON><PERSON> [ 436482.156 5011932.254] <-> IFC [ 436482.16695688 5011933.69142313] (dist: 1.44m, conf: 0.904)\n", "Match 3003: <PERSON><PERSON> [ 435834.26  5011988.955] <-> IFC [ 435837.16695688 5011991.69142314] (dist: 3.99m, conf: 0.734)\n", "Match 3004: <PERSON><PERSON> [ 435947.744 5011602.687] <-> IFC [ 435952.16695688 5011601.69142313] (dist: 4.53m, conf: 0.698)\n", "\n", "Matching complete: 3004 valid GCP pairs found\n", "Mean matching distance: 59.39m\n", "Max matching distance: 123.61m\n"]}], "source": ["def match_gcp_candidates(drone_candidates, ifc_candidates, max_distance=10.0, min_confidence=0.5):\n", "    \"\"\"Match drone and IFC GCP candidates using spatial proximity\"\"\"\n", "    print(f\"\\nMatching {len(drone_candidates)} drone candidates with {len(ifc_candidates)} IFC candidates...\")\n", "    print(f\"Maximum matching distance: {max_distance}m\")\n", "    \n", "    if len(drone_candidates) == 0 or len(ifc_candidates) == 0:\n", "        print(\"No candidates to match\")\n", "        return np.array([]), np.array([]), np.array([])\n", "    \n", "    # Build KDTree for IFC candidates (XY only for initial matching)\n", "    ifc_tree = cKDTree(ifc_candidates[:, :2])\n", "    \n", "    matched_drone = []\n", "    matched_ifc = []\n", "    match_distances = []\n", "    \n", "    for i, drone_pt in enumerate(drone_candidates):\n", "        # Find nearest IFC candidate\n", "        distance, ifc_idx = ifc_tree.query(drone_pt[:2])\n", "        \n", "        if distance <= max_distance:\n", "            # Calculate 3D distance for quality assessment\n", "            ifc_pt = ifc_candidates[ifc_idx]\n", "            distance_3d = np.linalg.norm(drone_pt - ifc_pt)\n", "            \n", "            # Compute confidence score (inverse of normalized distance)\n", "            confidence = 1.0 - (distance / max_distance)\n", "            \n", "            if confidence >= min_confidence:\n", "                matched_drone.append(drone_pt)\n", "                matched_ifc.append(ifc_pt)\n", "                match_distances.append(distance_3d)\n", "                \n", "                print(f\"Match {len(matched_drone)}: Drone {drone_pt[:2]} <-> IFC {ifc_pt[:2]} (dist: {distance:.2f}m, conf: {confidence:.3f})\")\n", "    \n", "    matched_drone = np.array(matched_drone)\n", "    matched_ifc = np.array(matched_ifc)\n", "    match_distances = np.array(match_distances)\n", "    \n", "    print(f\"\\nMatching complete: {len(matched_drone)} valid GCP pairs found\")\n", "    if len(match_distances) > 0:\n", "        print(f\"Mean matching distance: {np.mean(match_distances):.2f}m\")\n", "        print(f\"Max matching distance: {np.max(match_distances):.2f}m\")\n", "    \n", "    return matched_drone, matched_ifc, match_distances\n", "\n", "# Match candidates\n", "matched_drone_gcp, matched_ifc_gcp, gcp_distances = match_gcp_candidates(\n", "    drone_gcp_candidates, ifc_gcp_candidates, max_distance=15.0, min_confidence=0.3\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Quality Assessment and Filtering"]}, {"cell_type": "code", "execution_count": 25, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "Filtering 3004 GCP pairs...\n", "After distance filtering (≤12.0m): 10 pairs\n", "\n", "Quality Metrics:\n", "  Mean distance: 8.60m\n", "  Std distance: 2.32m\n", "  Max distance: 11.49m\n", "  Min distance: 3.18m\n"]}], "source": ["def filter_gcp_pairs_by_quality(drone_gcp, ifc_gcp, distances, max_distance=10.0, min_pairs=4):\n", "    \"\"\"Filter GCP pairs based on quality criteria\"\"\"\n", "    print(f\"\\nFiltering {len(drone_gcp)} GCP pairs...\")\n", "    \n", "    if len(drone_gcp) == 0:\n", "        print(\"No GCP pairs to filter\")\n", "        return np.array([]), np.array([]), np.array([])\n", "    \n", "    # Filter by distance threshold\n", "    valid_mask = distances <= max_distance\n", "    \n", "    filtered_drone = drone_gcp[valid_mask]\n", "    filtered_ifc = ifc_gcp[valid_mask]\n", "    filtered_distances = distances[valid_mask]\n", "    \n", "    print(f\"After distance filtering (≤{max_distance}m): {len(filtered_drone)} pairs\")\n", "    \n", "    # Check minimum pairs requirement\n", "    if len(filtered_drone) < min_pairs:\n", "        print(f\"WARNING: Only {len(filtered_drone)} pairs found, need at least {min_pairs} for reliable alignment\")\n", "        print(\"Consider relaxing distance threshold or improving detection parameters\")\n", "    \n", "    # Additional quality metrics\n", "    if len(filtered_distances) > 0:\n", "        print(f\"\\nQuality Metrics:\")\n", "        print(f\"  Mean distance: {np.mean(filtered_distances):.2f}m\")\n", "        print(f\"  Std distance: {np.std(filtered_distances):.2f}m\")\n", "        print(f\"  Max distance: {np.max(filtered_distances):.2f}m\")\n", "        print(f\"  Min distance: {np.min(filtered_distances):.2f}m\")\n", "    \n", "    return filtered_drone, filtered_ifc, filtered_distances\n", "\n", "# Filter GCP pairs\n", "final_drone_gcp, final_ifc_gcp, final_distances = filter_gcp_pairs_by_quality(\n", "    matched_drone_gcp, matched_ifc_gcp, gcp_distances, max_distance=12.0, min_pairs=4\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Save Detected GCP Coordinates"]}, {"cell_type": "code", "execution_count": 26, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "=== SAVING DETECTED GCP COORDINATES ===\n", "Saved GCP coordinates: ../../../data/processed/automated_gcp/ransac_pmf/trino_enel_detected_gcp_coordinates.json\n", "Saved GCP coordinates CSV: ../../../data/processed/automated_gcp/ransac_pmf/trino_enel_detected_gcp_coordinates.csv\n", "\n", "Files saved for alignment notebook:\n", "  JSON: ../../../data/processed/automated_gcp/ransac_pmf/trino_enel_detected_gcp_coordinates.json\n", "  CSV: ../../../data/processed/automated_gcp/ransac_pmf/trino_enel_detected_gcp_coordinates.csv\n", "  GCP pairs: 10\n"]}], "source": ["def save_detected_gcp_coordinates(drone_gcp, ifc_gcp, distances, output_dir, site_name, ground_method):\n", "    \"\"\"Save detected GCP coordinates for use in alignment notebook\"\"\"\n", "    from pathlib import Path\n", "    import json\n", "    \n", "    # Create output directory\n", "    output_path = Path(output_dir) / ground_method\n", "    output_path.mkdir(parents=True, exist_ok=True)\n", "    \n", "    if len(drone_gcp) == 0:\n", "        print(\"No GCP coordinates to save\")\n", "        return None\n", "    \n", "    # Create GCP data structure\n", "    gcp_data = {\n", "        'metadata': {\n", "            'site_name': site_name,\n", "            'ground_method': ground_method,\n", "            'detection_method': 'automated',\n", "            'num_pairs': len(drone_gcp),\n", "            'mean_distance': float(np.mean(distances)) if len(distances) > 0 else 0.0,\n", "            'max_distance': float(np.max(distances)) if len(distances) > 0 else 0.0\n", "        },\n", "        'gcp_pairs': []\n", "    }\n", "    \n", "    # Add each GCP pair\n", "    for i, (drone_pt, ifc_pt, dist) in enumerate(zip(drone_gcp, ifc_gcp, distances)):\n", "        gcp_pair = {\n", "            'id': f'auto_gcp_{i+1}',\n", "            'drone_coordinates': {\n", "                'x': float(drone_pt[0]),\n", "                'y': float(drone_pt[1]),\n", "                'z': float(drone_pt[2])\n", "            },\n", "            'ifc_coordinates': {\n", "                'x': float(ifc_pt[0]),\n", "                'y': float(ifc_pt[1]),\n", "                'z': float(ifc_pt[2])\n", "            },\n", "            'matching_distance': float(dist)\n", "        }\n", "        gcp_data['gcp_pairs'].append(gcp_pair)\n", "    \n", "    # Save as JSO<PERSON>\n", "    json_file = output_path / f\"{site_name}_detected_gcp_coordinates.json\"\n", "    with open(json_file, 'w') as f:\n", "        json.dump(gcp_data, f, indent=2)\n", "    \n", "    print(f\"Saved GCP coordinates: {json_file}\")\n", "    \n", "    # Save as CSV for easy viewing\n", "    csv_data = []\n", "    for i, (drone_pt, ifc_pt, dist) in enumerate(zip(drone_gcp, ifc_gcp, distances)):\n", "        csv_data.append({\n", "            'gcp_id': f'auto_gcp_{i+1}',\n", "            'drone_x': drone_pt[0],\n", "            'drone_y': drone_pt[1],\n", "            'drone_z': drone_pt[2],\n", "            'ifc_x': ifc_pt[0],\n", "            'ifc_y': ifc_pt[1],\n", "            'ifc_z': ifc_pt[2],\n", "            'distance': dist\n", "        })\n", "    \n", "    df = pd.DataFrame(csv_data)\n", "    csv_file = output_path / f\"{site_name}_detected_gcp_coordinates.csv\"\n", "    df.to_csv(csv_file, index=False)\n", "    \n", "    print(f\"Saved GCP coordinates CSV: {csv_file}\")\n", "    \n", "    return {\n", "        'json_file': str(json_file),\n", "        'csv_file': str(csv_file),\n", "        'num_pairs': len(drone_gcp)\n", "    }\n", "\n", "# Save detected coordinates\n", "if len(final_drone_gcp) > 0 and save_results:\n", "    print(\"\\n=== SAVING DETECTED GCP COORDINATES ===\")\n", "    saved_files = save_detected_gcp_coordinates(\n", "        final_drone_gcp, final_ifc_gcp, final_distances, \n", "        output_dir, site_name, ground_method\n", "    )\n", "    \n", "    if saved_files:\n", "        print(f\"\\nFiles saved for alignment notebook:\")\n", "        print(f\"  JSON: {saved_files['json_file']}\")\n", "        print(f\"  CSV: {saved_files['csv_file']}\")\n", "        print(f\"  GCP pairs: {saved_files['num_pairs']}\")\n", "else:\n", "    print(\"\\nNo valid GCP pairs found or save_results=False\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Summary and Usage Instructions"]}, {"cell_type": "code", "execution_count": 27, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["=== AUTOMATED GCP DETECTION COMPLETE ===\n", "\n", "Results Summary:\n", "  Drone candidates detected: 4552\n", "  IFC candidates detected: 11513\n", "  Matched pairs: 3004\n", "  Final filtered pairs: 10\n", "\n", "SUCCESS: 10 GCP pairs detected (≥4 required for alignment)\n", "\n", "To use in alignment notebook:\n", "1. Load coordinates from: ../../../data/processed/automated_gcp/ransac_pmf/trino_enel_detected_gcp_coordinates.json\n", "2. Use the 'drone_coordinates' and 'ifc_coordinates' arrays\n", "3. Apply similarity transformation in 03_gcp_based_alignment.ipynb\n"]}], "source": ["print(\"=== AUTOMATED GCP DETECTION COMPLETE ===\")\n", "print(f\"\\nResults Summary:\")\n", "print(f\"  Drone candidates detected: {len(drone_gcp_candidates)}\")\n", "print(f\"  IFC candidates detected: {len(ifc_gcp_candidates)}\")\n", "print(f\"  Matched pairs: {len(matched_drone_gcp)}\")\n", "print(f\"  Final filtered pairs: {len(final_drone_gcp)}\")\n", "\n", "if len(final_drone_gcp) >= 4:\n", "    print(f\"\\nSUCCESS: {len(final_drone_gcp)} GCP pairs detected (≥4 required for alignment)\")\n", "    print(f\"\\nTo use in alignment notebook:\")\n", "    print(f\"1. Load coordinates from: {output_dir}/{ground_method}/{site_name}_detected_gcp_coordinates.json\")\n", "    print(f\"2. Use the 'drone_coordinates' and 'ifc_coordinates' arrays\")\n", "    print(f\"3. Apply similarity transformation in 03_gcp_based_alignment.ipynb\")\n", "elif len(final_drone_gcp) > 0:\n", "    print(f\"\\nWARNING: Only {len(final_drone_gcp)} GCP pairs detected (<4 required)\")\n", "    print(f\"Consider adjusting detection parameters or using manual GCP selection\")\n", "else:\n", "    print(f\"\\nFAILED: No valid GCP pairs detected\")\n", "    print(f\"Try adjusting detection parameters or using manual GCP identification\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["**To use in alignment notebook:**\n", "1. Load coordinates from: ../../../data/processed/automated_gcp/ransac_pmf/trino_enel_detected_gcp_coordinates.json\n", "2. Use the 'drone_coordinates' and 'ifc_coordinates' arrays\n", "3. Apply similarity transformation in 03_gcp_based_alignment.ipynb\n", "\n", "## Next steps:\n", "1. Review detected coordinates in CSV file\n", "2. Optionally visualize candidates for quality assessment\n", "3. Use coordinates in 03_gcp_based_alignment.ipynb for transformation\n", "4. Compare alignment quality with manual GCP methods"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 4}