{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Papermill Alignment Testing Execution\n", "\n", "This notebook systematically executes ICP alignment tests for all ground segmentation methods and generates results for analysis.\n", "\n", "**Testing Matrix**: 4 ground methods × ICP alignment = 4 tests  \n", "**Ground Methods**: CSF, PMF, RANSAC, RANSAC+PMF  \n", "**Alignment Method**: ICP with Z-correction  \n", "\n", "**Author**: <PERSON><PERSON><PERSON>  \n", "**Date**: August 2025  \n", "**Project**: As-Built Foundation Analysis\n", "\n", "## Workflow\n", "1. Execute ICP alignment for each ground segmentation method\n", "2. Generate standardized metrics and transformation files\n", "3. Run analysis notebook to compare results\n", "4. Generate visualization notebook for final review"]}, {"cell_type": "markdown", "metadata": {"tags": ["parameters"]}, "source": ["## Papermill Parameters"]}, {"cell_type": "code", "execution_count": 12, "metadata": {"tags": ["parameters"]}, "outputs": [], "source": ["# Papermill parameters\n", "site_name = \"trino_enel\"\n", "project_type = \"ENEL\"\n", "execute_all_methods = True\n", "run_analysis = True\n", "run_visualization = True\n", "save_results = True\n", "kernel_name = \"pytorch-geo-dev\"  # Specify kernel to avoid seaborn issues"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Setup and Preparation"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-08-08 15:35:32,063 - INFO - Papermill Alignment Execution Started\n", "2025-08-08 15:35:32,064 - INFO - Site: trino_enel\n", "2025-08-08 15:35:32,064 - INFO - Methods to test: ['csf', 'pmf', 'ransac', 'ransac_pmf']\n", "2025-08-08 15:35:32,065 - INFO - Output directory: /Users/<USER>/Documents/GitHub/asbuilt-foundation-analysis/data/output_runs/trino_enel/icp_alignment\n"]}], "source": ["# Setup environment and imports\n", "import sys\n", "from pathlib import Path\n", "import subprocess\n", "import json\n", "from datetime import datetime\n", "import logging\n", "\n", "# Add the notebooks folder to sys.path\n", "notebooks_root = Path(__file__).resolve().parents[1] if '__file__' in globals() else Path.cwd().parents[1]\n", "if str(notebooks_root) not in sys.path:\n", "    sys.path.insert(0, str(notebooks_root))\n", "\n", "# Import shared configuration\n", "from shared.config import get_data_path, get_output_path, get_processed_data_path\n", "\n", "# Setup logging\n", "logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')\n", "logger = logging.getLogger(__name__)\n", "\n", "# Configuration\n", "ground_methods = [\"csf\", \"pmf\", \"ransac\", \"ransac_pmf\"]\n", "icp_alignment_output = get_data_path(site_name, \"output_runs\") / \"icp_alignment\"\n", "alignment_testing_output = get_data_path(site_name, \"output_runs\") / \"alignment_testing\"\n", "\n", "# Create output directories\n", "for method in ground_methods:\n", "    (icp_alignment_output / method).mkdir(parents=True, exist_ok=True)\n", "    (alignment_testing_output / method).mkdir(parents=True, exist_ok=True)\n", "\n", "logger.info(f\"Papermill Alignment Execution Started\")\n", "logger.info(f\"Site: {site_name}\")\n", "logger.info(f\"Methods to test: {ground_methods}\")\n", "logger.info(f\"Output directory: {icp_alignment_output}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Kernel Detection and Setup"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-08-08 15:35:32,070 - INFO - Selected kernel: pytorch-geo-dev\n"]}], "source": ["# Detect available kernels and handle seaborn dependency\n", "def get_best_kernel():\n", "    \"\"\"Get the best available kernel for papermill execution\"\"\"\n", "    try:\n", "        import jupyter_client\n", "        km = jupyter_client.kernelspec.KernelSpecManager()\n", "        available_kernels = list(km.get_all_specs().keys())\n", "        \n", "        logger.info(f\"Available kernels: {available_kernels}\")\n", "        \n", "        # Prefer pytorch-geo-dev if available (has all dependencies)\n", "        if 'pytorch-geo-dev' in available_kernels:\n", "            return 'pytorch-geo-dev'\n", "        elif 'python3' in available_kernels:\n", "            return 'python3'\n", "        else:\n", "            return available_kernels[0] if available_kernels else 'python3'\n", "            \n", "    except Exception as e:\n", "        logger.warning(f\"Could not detect kernels: {e}, using python3\")\n", "        return 'python3'\n", "\n", "# Override kernel_name if not specified in parameters\n", "if 'kernel_name' not in locals() or not kernel_name:\n", "    kernel_name = get_best_kernel()\n", "\n", "logger.info(f\"Selected kernel: {kernel_name}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## ICP Alignment Execution\n", "\n", "Execute ICP alignment for each ground segmentation method using the Z-corrected alignment notebook."]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-08-08 15:35:32,077 - INFO - Starting ICP alignment for method: CSF\n", "2025-08-08 15:35:32,078 - INFO - Input Notebook:  03_icp_alignment_xy_hybrid.ipynb\n", "2025-08-08 15:35:32,078 - INFO - Output Notebook: /Users/<USER>/Documents/GitHub/asbuilt-foundation-analysis/data/output_runs/trino_enel/alignment_testing/csf/icp_alignment_csf_executed.ipynb\n", "2025-08-08 15:35:32,079 - INFO - Input Notebook:  03_icp_alignment_xy_hybrid.ipynb\n", "2025-08-08 15:35:32,079 - INFO - Output Notebook: /Users/<USER>/Documents/GitHub/asbuilt-foundation-analysis/data/output_runs/trino_enel/alignment_testing/csf/icp_alignment_csf_executed.ipynb\n", "2025-08-08 15:35:32,086 - WARNING - Passed unknown parameter: quality_sample_size\n", "2025-08-08 15:35:33,090 - INFO - Executing notebook with kernel: pytorch-geo-dev\n", "2025-08-08 15:35:33,091 - INFO - Executing Cell 1---------------------------------------\n", "2025-08-08 15:35:33,099 - INFO - Ending Cell 1------------------------------------------\n", "2025-08-08 15:35:33,103 - INFO - Executing Cell 2---------------------------------------\n", "2025-08-08 15:35:33,113 - INFO - Ending Cell 2------------------------------------------\n", "2025-08-08 15:35:33,116 - INFO - Executing Cell 3---------------------------------------\n", "2025-08-08 15:35:33,121 - INFO - Ending Cell 3------------------------------------------\n", "2025-08-08 15:35:33,124 - INFO - Executing Cell 4---------------------------------------\n", "2025-08-08 15:35:33,743 - WARNING - No handler found for comm target 'dash'\n", "2025-08-08 15:35:34,778 - INFO - === HYBRID XY-COORDINATE ALIGNMENT ===\n", "Strategy: Coordinate-only Z + Selective XY refinement\n", "Site: trino_enel\n", "Output: ../../../data/output_runs/icp_alignment/csf\n", "Timestamp: 2025-08-08 15:35:34\n", "\n", "2025-08-08 15:35:34,779 - INFO - Ending Cell 4------------------------------------------\n", "2025-08-08 15:35:34,783 - INFO - Executing Cell 5---------------------------------------\n", "2025-08-08 15:35:34,786 - INFO - Ending Cell 5------------------------------------------\n", "2025-08-08 15:35:34,788 - INFO - Executing Cell 6---------------------------------------\n", "2025-08-08 15:35:34,794 - INFO - Loading data with corrected approach...\n", "Drone file: ../../../data/processed/trino_enel/ground_segmentation/csf/trino_enel_nonground.ply\n", "IFC metadata file: ../../../data/processed/trino_enel/ifc_metadata/GRE.EEC.S.00.IT.P.14353.00.265_enhanced_metadata.csv\n", "IFC point cloud file: ../../../data/processed/trino_enel/ifc_pointclouds/GRE.EEC.S.00.IT.P.14353.00.265_data_driven.ply (for comparison)\n", "\n", "2025-08-08 15:35:34,796 - INFO - Ending Cell 6------------------------------------------\n", "2025-08-08 15:35:34,798 - INFO - Executing Cell 7---------------------------------------\n", "2025-08-08 15:35:35,152 - INFO - Loaded drone scan: 13,848 points\n", "Loaded IFC model: 5,480,340 points\n", "Coordinate Analysis:\n", "  Full 3D offset: [-38.35, 63.60, 154.87]\n", "  XY component: 74.27m\n", "  Z component: 154.87m\n", "\n", "2025-08-08 15:35:35,154 - INFO - Ending Cell 7------------------------------------------\n", "2025-08-08 15:35:35,157 - INFO - Executing Cell 8---------------------------------------\n", "2025-08-08 15:35:35,177 - INFO - \n", "INITIAL POINT CLOUD STATISTICS\n", "\n", "Drone scan (ground truth):\n", "  Points: 13,848\n", "  X range: [435223.72, 436794.15]\n", "  Y range: [5010816.92, 5012539.06]\n", "  Z range: [1.17, 13.14]\n", "\n", "IFC model (to be aligned):\n", "  Points: 5,480,340\n", "  X range: [435267.17, 436719.98]\n", "  Y range: [5010900.69, 5012462.43]\n", "  Z range: [152.85, 161.66]\n", "\n", "2025-08-08 15:35:35,179 - INFO - Ending Cell 8------------------------------------------\n", "2025-08-08 15:35:35,182 - INFO - Executing Cell 9---------------------------------------\n", "2025-08-08 15:35:35,187 - INFO - Ending Cell 9------------------------------------------\n", "2025-08-08 15:35:35,190 - INFO - Executing Cell 10--------------------------------------\n", "2025-08-08 15:35:35,196 - INFO - Ending Cell 10-----------------------------------------\n", "2025-08-08 15:35:35,198 - INFO - Executing Cell 11--------------------------------------\n", "2025-08-08 15:35:35,205 - INFO - \n", "=== CRS VALIDATION ===\n", "Drone CRS: Not Found\n", "IFC CRS:   32632\n", "Warning: CRS metadata missing. Proceeding with assumption of matching coordinate systems.\n", "\n", "Ready for alignment with 13,848 drone + 5,480,340 IFC points\n", "\n", "2025-08-08 15:35:35,207 - INFO - Ending Cell 11-----------------------------------------\n", "2025-08-08 15:35:35,210 - INFO - Executing Cell 12--------------------------------------\n", "2025-08-08 15:35:35,212 - INFO - Ending Cell 12-----------------------------------------\n", "2025-08-08 15:35:35,215 - INFO - Executing Cell 13--------------------------------------\n", "2025-08-08 15:35:35,348 - INFO - COORDINATE ALIGNMENT RESULTS:\n", "  Applied offset: [-38.35, 63.60, 154.87]m\n", "  Residual centroid error: 0.000000m\n", "  Coordinate ranges after alignment:\n", "    Drone: X=1570.4m, Y=1722.1m, Z=12.0m\n", "    IFC:   X=1452.8m, Y=1561.7m, Z=8.8m\n", "  WARNING: Coordinate ranges very different - check data compatibility\n", "  Status: Coordinate alignment complete\n", "\n", "2025-08-08 15:35:35,350 - INFO - Ending Cell 13-----------------------------------------\n", "2025-08-08 15:35:35,354 - INFO - Executing Cell 14--------------------------------------\n", "2025-08-08 15:35:35,357 - INFO - Ending Cell 14-----------------------------------------\n", "2025-08-08 15:35:35,361 - INFO - Executing Cell 15--------------------------------------\n", "2025-08-08 15:35:35,589 - INFO - XY-only point clouds prepared:\n", "  Source (Drone XY): 13,043 points\n", "  Target (IFC XY): 25,468 points\n", "\n", "2025-08-08 15:35:35,591 - INFO - Ending Cell 15-----------------------------------------\n", "2025-08-08 15:35:35,595 - INFO - Executing Cell 16--------------------------------------\n", "2025-08-08 15:35:35,597 - INFO - Ending Cell 16-----------------------------------------\n", "2025-08-08 15:35:35,601 - INFO - Executing Cell 17--------------------------------------\n", "2025-08-08 15:35:35,607 - INFO - XY Offset Analysis:\n", "  Source center: [435983.516, 5011744.663]\n", "  Target center: [435992.322, 5011770.805]\n", "  XY offset: [8.806, 26.142]\n", "  XY offset magnitude: 27.586m\n", "\n", "2025-08-08 15:35:35,609 - INFO - Ending Cell 17-----------------------------------------\n", "2025-08-08 15:35:35,613 - INFO - Executing Cell 18--------------------------------------\n", "2025-08-08 15:35:35,903 - INFO - <Figure size 1500x500 with 3 Axes>\n", "2025-08-08 15:35:35,909 - INFO - Ending Cell 18-----------------------------------------\n", "2025-08-08 15:35:35,913 - INFO - Executing Cell 19--------------------------------------\n", "2025-08-08 15:35:35,917 - INFO - Ending Cell 19-----------------------------------------\n", "2025-08-08 15:35:35,921 - INFO - Executing Cell 20--------------------------------------\n", "2025-08-08 15:35:35,991 - INFO - === TESTING XY-ONLY ICP REFINEMENT ===\n", "ICP Parameters:\n", "  Max correspondence distance: 1.00m\n", "  Max iterations: 30 (reduced for stability)\n", "\n", "ICP Results:\n", "  Fitness: 0.022464\n", "  RMSE: 0.669518\n", "  XY refinement: [5440.443, -470.561]\n", "  Refinement magnitude: 5460.756m\n", "\n", "REJECTING ICP REFINEMENT:\n", "   - Large refinement (5460.756m > 10.0m)\n", "   - Poor fitness (0.022464 < 0.1)\n", "   - Extreme single-axis movement\n", "   DECISION: Using coordinate-only alignment (more reliable)\n", "\n", "2025-08-08 15:35:35,993 - INFO - Ending Cell 20-----------------------------------------\n", "2025-08-08 15:35:35,998 - INFO - Executing Cell 21--------------------------------------\n", "2025-08-08 15:35:36,002 - INFO - Ending Cell 21-----------------------------------------\n", "2025-08-08 15:35:36,006 - INFO - Executing Cell 22--------------------------------------\n", "2025-08-08 15:35:36,014 - INFO - === HYBRID ALIGNMENT DECISION ===\n", "ICP refinement was REJECTED by validation checks\n", "Using coordinate-only alignment (more reliable)\n", "\n", "2025-08-08 15:35:36,015 - INFO - Ending Cell 22-----------------------------------------\n", "2025-08-08 15:35:36,020 - INFO - Executing Cell 23--------------------------------------\n", "2025-08-08 15:35:36,023 - INFO - Ending Cell 23-----------------------------------------\n", "2025-08-08 15:35:36,028 - INFO - Executing Cell 24--------------------------------------\n", "2025-08-08 15:35:36,177 - INFO - === FINAL ALIGNMENT QUALITY ===\n", "Method used: coordinate_only_icp_rejected\n", "Final centroid separation: 0.000000m\n", "RMSE: 49.59m\n", "Median distance: 8.46m\n", "Good points (<2m): 5.9%\n", "\n", "WARNING: Poor alignment quality detected!\n", "   RMSE 49.6m is very high, only 5.9% points well-aligned\n", "   Consider: rotation correction, scale adjustment, or different method\n", "\n", "2025-08-08 15:35:36,179 - INFO - Ending Cell 24-----------------------------------------\n", "2025-08-08 15:35:36,183 - INFO - Executing Cell 25--------------------------------------\n", "2025-08-08 15:35:36,187 - INFO - Ending Cell 25-----------------------------------------\n", "2025-08-08 15:35:36,191 - INFO - Executing Cell 26--------------------------------------\n", "2025-08-08 15:35:36,199 - INFO - === APPROACH COMPARISON ===\n", "Coordinate-only centroid error: 0.000m\n", "Hybrid approach centroid error: 0.000000m\n", "Hybrid approach RMSE: 49.59m\n", "XY refinement of 5460.756m was rejected (too large)\n", "Assessment: COORDINATE-ONLY PREFERRED\n", "\n", "=== OVERALL ALIGNMENT QUALITY ===\n", "POOR: RMSE 49.6m indicates significant alignment failure\n", "   Recommendation: Try different alignment method or check coordinate systems\n", "\n", "2025-08-08 15:35:36,200 - INFO - Ending Cell 26-----------------------------------------\n", "2025-08-08 15:35:36,203 - INFO - Executing Cell 27--------------------------------------\n", "2025-08-08 15:35:36,207 - INFO - Ending Cell 27-----------------------------------------\n", "2025-08-08 15:35:36,211 - INFO - Executing Cell 28--------------------------------------\n", "2025-08-08 15:35:36,319 - INFO - Results saved to: ../../../data/output_runs/icp_alignment/csf\n", "\n", "=== ALIGNMENT DIAGNOSTIC ===\n", "Drone extent: [1570.4, 1722.1, 12.0]m\n", "IFC extent:   [1452.8, 1561.7, 8.8]m\n", "Scale ratios: [1.08, 1.10, 1.36]\n", "Possible issues: rotation, scale, coordinate system, or coverage mismatch\n", "\n", "=== HYBRID XY-COORDINATE ALIGNMENT COMPLETE ===\n", "Final method: coordinate_only_icp_rejected\n", "Final RMSE: 49.59m\n", "Assessment: COORDINATE-ONLY PREFERRED\n", "\n", "2025-08-08 15:35:36,321 - INFO - Ending Cell 28-----------------------------------------\n", "2025-08-08 15:35:36,325 - INFO - Executing Cell 29--------------------------------------\n", "2025-08-08 15:35:36,417 - INFO - === STANDARDIZED EVALUATION: HYBRID_XY_COORDINATE ===\n", "RMSE: 49.76m\n", "Sample size: 13,848 points\n", "{'method': 'hybrid_xy_coordinate', 'rmse': 49.760146620948014, 'median_distance': 9.646753315493925, 'sample_size': 13848, 'evaluation': 'standardized_fair_comparison'}\n", "\n", "2025-08-08 15:35:36,419 - INFO - Ending Cell 29-----------------------------------------\n", "2025-08-08 15:35:36,750 - INFO - Successfully completed ICP alignment for CSF\n", "2025-08-08 15:35:36,751 - INFO - Starting ICP alignment for method: PMF\n", "2025-08-08 15:35:36,751 - INFO - Input Notebook:  03_icp_alignment_xy_hybrid.ipynb\n", "2025-08-08 15:35:36,751 - INFO - Output Notebook: /Users/<USER>/Documents/GitHub/asbuilt-foundation-analysis/data/output_runs/trino_enel/alignment_testing/pmf/icp_alignment_pmf_executed.ipynb\n", "2025-08-08 15:35:36,752 - INFO - Input Notebook:  03_icp_alignment_xy_hybrid.ipynb\n", "2025-08-08 15:35:36,752 - INFO - Output Notebook: /Users/<USER>/Documents/GitHub/asbuilt-foundation-analysis/data/output_runs/trino_enel/alignment_testing/pmf/icp_alignment_pmf_executed.ipynb\n", "2025-08-08 15:35:36,754 - WARNING - Passed unknown parameter: quality_sample_size\n", "2025-08-08 15:35:37,285 - INFO - Executing notebook with kernel: pytorch-geo-dev\n", "2025-08-08 15:35:37,286 - INFO - Executing Cell 1---------------------------------------\n", "2025-08-08 15:35:37,294 - INFO - Ending Cell 1------------------------------------------\n", "2025-08-08 15:35:37,297 - INFO - Executing Cell 2---------------------------------------\n", "2025-08-08 15:35:37,306 - INFO - Ending Cell 2------------------------------------------\n", "2025-08-08 15:35:37,310 - INFO - Executing Cell 3---------------------------------------\n", "2025-08-08 15:35:37,317 - INFO - Ending Cell 3------------------------------------------\n", "2025-08-08 15:35:37,319 - INFO - Executing Cell 4---------------------------------------\n", "2025-08-08 15:35:37,735 - WARNING - No handler found for comm target 'dash'\n", "2025-08-08 15:35:38,476 - INFO - === HYBRID XY-COORDINATE ALIGNMENT ===\n", "Strategy: Coordinate-only Z + Selective XY refinement\n", "Site: trino_enel\n", "Output: ../../../data/output_runs/icp_alignment/pmf\n", "Timestamp: 2025-08-08 15:35:38\n", "\n", "2025-08-08 15:35:38,478 - INFO - Ending Cell 4------------------------------------------\n", "2025-08-08 15:35:38,481 - INFO - Executing Cell 5---------------------------------------\n", "2025-08-08 15:35:38,483 - INFO - Ending Cell 5------------------------------------------\n", "2025-08-08 15:35:38,486 - INFO - Executing Cell 6---------------------------------------\n", "2025-08-08 15:35:38,491 - INFO - Loading data with corrected approach...\n", "Drone file: ../../../data/processed/trino_enel/ground_segmentation/pmf/trino_enel_nonground.ply\n", "IFC metadata file: ../../../data/processed/trino_enel/ifc_metadata/GRE.EEC.S.00.IT.P.14353.00.265_enhanced_metadata.csv\n", "IFC point cloud file: ../../../data/processed/trino_enel/ifc_pointclouds/GRE.EEC.S.00.IT.P.14353.00.265_data_driven.ply (for comparison)\n", "\n", "2025-08-08 15:35:38,493 - INFO - Ending Cell 6------------------------------------------\n", "2025-08-08 15:35:38,495 - INFO - Executing Cell 7---------------------------------------\n", "2025-08-08 15:35:38,743 - INFO - Loaded drone scan: 693,593 points\n", "Loaded IFC model: 5,480,340 points\n", "Coordinate Analysis:\n", "  Full 3D offset: [-52.17, 21.08, 155.88]\n", "  XY component: 56.27m\n", "  Z component: 155.88m\n", "\n", "2025-08-08 15:35:38,745 - INFO - Ending Cell 7------------------------------------------\n", "2025-08-08 15:35:38,748 - INFO - Executing Cell 8---------------------------------------\n", "2025-08-08 15:35:38,769 - INFO - \n", "INITIAL POINT CLOUD STATISTICS\n", "\n", "Drone scan (ground truth):\n", "  Points: 693,593\n", "  X range: [435220.59, 436794.95]\n", "  Y range: [5010812.82, 5012550.57]\n", "  Z range: [-0.72, 14.73]\n", "\n", "IFC model (to be aligned):\n", "  Points: 5,480,340\n", "  X range: [435267.17, 436719.98]\n", "  Y range: [5010900.69, 5012462.43]\n", "  Z range: [152.85, 161.66]\n", "\n", "2025-08-08 15:35:38,770 - INFO - Ending Cell 8------------------------------------------\n", "2025-08-08 15:35:38,773 - INFO - Executing Cell 9---------------------------------------\n", "2025-08-08 15:35:38,779 - INFO - Ending Cell 9------------------------------------------\n", "2025-08-08 15:35:38,782 - INFO - Executing Cell 10--------------------------------------\n", "2025-08-08 15:35:38,787 - INFO - Ending Cell 10-----------------------------------------\n", "2025-08-08 15:35:38,790 - INFO - Executing Cell 11--------------------------------------\n", "2025-08-08 15:35:38,796 - INFO - \n", "=== CRS VALIDATION ===\n", "Drone CRS: Not Found\n", "IFC CRS:   32632\n", "Warning: CRS metadata missing. Proceeding with assumption of matching coordinate systems.\n", "\n", "Ready for alignment with 693,593 drone + 5,480,340 IFC points\n", "\n", "2025-08-08 15:35:38,798 - INFO - Ending Cell 11-----------------------------------------\n", "2025-08-08 15:35:38,801 - INFO - Executing Cell 12--------------------------------------\n", "2025-08-08 15:35:38,804 - INFO - Ending Cell 12-----------------------------------------\n", "2025-08-08 15:35:38,806 - INFO - Executing Cell 13--------------------------------------\n", "2025-08-08 15:35:38,967 - INFO - COORDINATE ALIGNMENT RESULTS:\n", "  Applied offset: [-52.17, 21.08, 155.88]m\n", "  Residual centroid error: 0.000001m\n", "  Coordinate ranges after alignment:\n", "    Drone: X=1574.4m, Y=1737.8m, Z=15.4m\n", "    IFC:   X=1452.8m, Y=1561.7m, Z=8.8m\n", "  WARNING: Coordinate ranges very different - check data compatibility\n", "  Status: Coordinate alignment complete\n", "\n", "2025-08-08 15:35:38,968 - INFO - Ending Cell 13-----------------------------------------\n", "2025-08-08 15:35:38,972 - INFO - Executing Cell 14--------------------------------------\n", "2025-08-08 15:35:38,974 - INFO - Ending Cell 14-----------------------------------------\n", "2025-08-08 15:35:38,977 - INFO - Executing Cell 15--------------------------------------\n", "2025-08-08 15:35:40,251 - INFO - XY-only point clouds prepared:\n", "  Source (Drone XY): 665,715 points\n", "  Target (IFC XY): 25,468 points\n", "\n", "2025-08-08 15:35:40,253 - INFO - Ending Cell 15-----------------------------------------\n", "2025-08-08 15:35:40,257 - INFO - Executing Cell 16--------------------------------------\n", "2025-08-08 15:35:40,260 - INFO - Ending Cell 16-----------------------------------------\n", "2025-08-08 15:35:40,263 - INFO - Executing Cell 17--------------------------------------\n", "2025-08-08 15:35:40,275 - INFO - XY Offset Analysis:\n", "  Source center: [435985.827, 5011747.754]\n", "  Target center: [435992.322, 5011770.805]\n", "  XY offset: [6.495, 23.052]\n", "  XY offset magnitude: 23.949m\n", "\n", "2025-08-08 15:35:40,277 - INFO - Ending Cell 17-----------------------------------------\n", "2025-08-08 15:35:40,281 - INFO - Executing Cell 18--------------------------------------\n", "2025-08-08 15:35:40,762 - INFO - <Figure size 1500x500 with 3 Axes>\n", "2025-08-08 15:35:40,764 - INFO - Ending Cell 18-----------------------------------------\n", "2025-08-08 15:35:40,768 - INFO - Executing Cell 19--------------------------------------\n", "2025-08-08 15:35:40,773 - INFO - Ending Cell 19-----------------------------------------\n", "2025-08-08 15:35:40,777 - INFO - Executing Cell 20--------------------------------------\n", "2025-08-08 15:35:40,989 - INFO - === TESTING XY-ONLY ICP REFINEMENT ===\n", "ICP Parameters:\n", "  Max correspondence distance: 1.00m\n", "  Max iterations: 30 (reduced for stability)\n", "\n", "2025-08-08 15:35:42,365 - INFO - \n", "ICP Results:\n", "  Fitness: 0.025066\n", "  RMSE: 0.697038\n", "  XY refinement: [-3053.489, 266.611]\n", "  Refinement magnitude: 3065.107m\n", "\n", "REJECTING ICP REFINEMENT:\n", "   - Large refinement (3065.107m > 10.0m)\n", "   - Poor fitness (0.025066 < 0.1)\n", "   - Extreme single-axis movement\n", "   DECISION: Using coordinate-only alignment (more reliable)\n", "\n", "2025-08-08 15:35:42,367 - INFO - Ending Cell 20-----------------------------------------\n", "2025-08-08 15:35:42,372 - INFO - Executing Cell 21--------------------------------------\n", "2025-08-08 15:35:42,376 - INFO - Ending Cell 21-----------------------------------------\n", "2025-08-08 15:35:42,380 - INFO - Executing Cell 22--------------------------------------\n", "2025-08-08 15:35:42,392 - INFO - === HYBRID ALIGNMENT DECISION ===\n", "ICP refinement was REJECTED by validation checks\n", "Using coordinate-only alignment (more reliable)\n", "\n", "2025-08-08 15:35:42,394 - INFO - Ending Cell 22-----------------------------------------\n", "2025-08-08 15:35:42,398 - INFO - Executing Cell 23--------------------------------------\n", "2025-08-08 15:35:42,402 - INFO - Ending Cell 23-----------------------------------------\n", "2025-08-08 15:35:42,407 - INFO - Executing Cell 24--------------------------------------\n", "2025-08-08 15:35:42,601 - INFO - === FINAL ALIGNMENT QUALITY ===\n", "Method used: coordinate_only_icp_rejected\n", "Final centroid separation: 0.000001m\n", "RMSE: 27.99m\n", "Median distance: 4.82m\n", "Good points (<2m): 7.6%\n", "\n", "WARNING: Poor alignment quality detected!\n", "   RMSE 28.0m is very high, only 7.6% points well-aligned\n", "   Consider: rotation correction, scale adjustment, or different method\n", "\n", "2025-08-08 15:35:42,603 - INFO - Ending Cell 24-----------------------------------------\n", "2025-08-08 15:35:42,607 - INFO - Executing Cell 25--------------------------------------\n", "2025-08-08 15:35:42,611 - INFO - Ending Cell 25-----------------------------------------\n", "2025-08-08 15:35:42,615 - INFO - Executing Cell 26--------------------------------------\n", "2025-08-08 15:35:42,623 - INFO - === APPROACH COMPARISON ===\n", "Coordinate-only centroid error: 0.000m\n", "Hybrid approach centroid error: 0.000001m\n", "Hybrid approach RMSE: 27.99m\n", "XY refinement of 3065.107m was rejected (too large)\n", "Assessment: COORDINATE-ONLY PREFERRED\n", "\n", "=== OVERALL ALIGNMENT QUALITY ===\n", "POOR: RMSE 28.0m indicates significant alignment failure\n", "   Recommendation: Try different alignment method or check coordinate systems\n", "\n", "2025-08-08 15:35:42,624 - INFO - Ending Cell 26-----------------------------------------\n", "2025-08-08 15:35:42,628 - INFO - Executing Cell 27--------------------------------------\n", "2025-08-08 15:35:42,632 - INFO - Ending Cell 27-----------------------------------------\n", "2025-08-08 15:35:42,636 - INFO - Executing Cell 28--------------------------------------\n", "2025-08-08 15:35:42,788 - INFO - Results saved to: ../../../data/output_runs/icp_alignment/pmf\n", "\n", "=== ALIGNMENT DIAGNOSTIC ===\n", "Drone extent: [1574.4, 1737.8, 15.4]m\n", "IFC extent:   [1452.8, 1561.7, 8.8]m\n", "Scale ratios: [1.08, 1.11, 1.75]\n", " Scale mismatch detected - consider scale correction\n", "Possible issues: rotation, scale, coordinate system, or coverage mismatch\n", "\n", "=== HYBRID XY-COORDINATE ALIGNMENT COMPLETE ===\n", "Final method: coordinate_only_icp_rejected\n", "Final RMSE: 27.99m\n", "Assessment: COORDINATE-ONLY PREFERRED\n", "\n", "2025-08-08 15:35:42,790 - INFO - Ending Cell 28-----------------------------------------\n", "2025-08-08 15:35:42,795 - INFO - Executing Cell 29--------------------------------------\n", "2025-08-08 15:35:42,931 - INFO - === STANDARDIZED EVALUATION: HYBRID_XY_COORDINATE ===\n", "RMSE: 27.93m\n", "Sample size: 20,000 points\n", "{'method': 'hybrid_xy_coordinate', 'rmse': 27.927065638745653, 'median_distance': 5.726542821898246, 'sample_size': 20000, 'evaluation': 'standardized_fair_comparison'}\n", "\n", "2025-08-08 15:35:42,933 - INFO - Ending Cell 29-----------------------------------------\n", "2025-08-08 15:35:43,265 - INFO - Successfully completed ICP alignment for PMF\n", "2025-08-08 15:35:43,265 - INFO - Starting ICP alignment for method: RANSAC\n", "2025-08-08 15:35:43,265 - INFO - Input Notebook:  03_icp_alignment_xy_hybrid.ipynb\n", "2025-08-08 15:35:43,265 - INFO - Output Notebook: /Users/<USER>/Documents/GitHub/asbuilt-foundation-analysis/data/output_runs/trino_enel/alignment_testing/ransac/icp_alignment_ransac_executed.ipynb\n", "2025-08-08 15:35:43,266 - INFO - Input Notebook:  03_icp_alignment_xy_hybrid.ipynb\n", "2025-08-08 15:35:43,266 - INFO - Output Notebook: /Users/<USER>/Documents/GitHub/asbuilt-foundation-analysis/data/output_runs/trino_enel/alignment_testing/ransac/icp_alignment_ransac_executed.ipynb\n", "2025-08-08 15:35:43,269 - WARNING - Passed unknown parameter: quality_sample_size\n", "2025-08-08 15:35:43,804 - INFO - Executing notebook with kernel: pytorch-geo-dev\n", "2025-08-08 15:35:43,805 - INFO - Executing Cell 1---------------------------------------\n", "2025-08-08 15:35:43,808 - INFO - Ending Cell 1------------------------------------------\n", "2025-08-08 15:35:43,812 - INFO - Executing Cell 2---------------------------------------\n", "2025-08-08 15:35:43,821 - INFO - Ending Cell 2------------------------------------------\n", "2025-08-08 15:35:43,824 - INFO - Executing Cell 3---------------------------------------\n", "2025-08-08 15:35:43,830 - INFO - Ending Cell 3------------------------------------------\n", "2025-08-08 15:35:43,833 - INFO - Executing Cell 4---------------------------------------\n", "2025-08-08 15:35:44,329 - WARNING - No handler found for comm target 'dash'\n", "2025-08-08 15:35:45,075 - INFO - === HYBRID XY-COORDINATE ALIGNMENT ===\n", "Strategy: Coordinate-only Z + Selective XY refinement\n", "Site: trino_enel\n", "Output: ../../../data/output_runs/icp_alignment/ransac\n", "Timestamp: 2025-08-08 15:35:45\n", "\n", "2025-08-08 15:35:45,076 - INFO - Ending Cell 4------------------------------------------\n", "2025-08-08 15:35:45,079 - INFO - Executing Cell 5---------------------------------------\n", "2025-08-08 15:35:45,082 - INFO - Ending Cell 5------------------------------------------\n", "2025-08-08 15:35:45,085 - INFO - Executing Cell 6---------------------------------------\n", "2025-08-08 15:35:45,091 - INFO - Loading data with corrected approach...\n", "Drone file: ../../../data/processed/trino_enel/ground_segmentation/ransac/trino_enel_nonground.ply\n", "IFC metadata file: ../../../data/processed/trino_enel/ifc_metadata/GRE.EEC.S.00.IT.P.14353.00.265_enhanced_metadata.csv\n", "IFC point cloud file: ../../../data/processed/trino_enel/ifc_pointclouds/GRE.EEC.S.00.IT.P.14353.00.265_data_driven.ply (for comparison)\n", "\n", "2025-08-08 15:35:45,092 - INFO - Ending Cell 6------------------------------------------\n", "2025-08-08 15:35:45,095 - INFO - Executing Cell 7---------------------------------------\n", "2025-08-08 15:35:45,337 - INFO - Loaded drone scan: 504,697 points\n", "Loaded IFC model: 5,480,340 points\n", "Coordinate Analysis:\n", "  Full 3D offset: [-43.93, 32.88, 155.42]\n", "  XY component: 54.87m\n", "  Z component: 155.42m\n", "\n", "2025-08-08 15:35:45,338 - INFO - Ending Cell 7------------------------------------------\n", "2025-08-08 15:35:45,341 - INFO - Executing Cell 8---------------------------------------\n", "2025-08-08 15:35:45,372 - INFO - \n", "INITIAL POINT CLOUD STATISTICS\n", "\n", "Drone scan (ground truth):\n", "  Points: 504,697\n", "  X range: [435220.59, 436794.95]\n", "  Y range: [5010812.82, 5012549.25]\n", "  Z range: [-0.82, 14.73]\n", "\n", "IFC model (to be aligned):\n", "  Points: 5,480,340\n", "  X range: [435267.17, 436719.98]\n", "  Y range: [5010900.69, 5012462.43]\n", "  Z range: [152.85, 161.66]\n", "\n", "2025-08-08 15:35:45,373 - INFO - Ending Cell 8------------------------------------------\n", "2025-08-08 15:35:45,377 - INFO - Executing Cell 9---------------------------------------\n", "2025-08-08 15:35:45,384 - INFO - Ending Cell 9------------------------------------------\n", "2025-08-08 15:35:45,387 - INFO - Executing Cell 10--------------------------------------\n", "2025-08-08 15:35:45,393 - INFO - Ending Cell 10-----------------------------------------\n", "2025-08-08 15:35:45,396 - INFO - Executing Cell 11--------------------------------------\n", "2025-08-08 15:35:45,403 - INFO - \n", "=== CRS VALIDATION ===\n", "Drone CRS: Not Found\n", "IFC CRS:   32632\n", "Warning: CRS metadata missing. Proceeding with assumption of matching coordinate systems.\n", "\n", "Ready for alignment with 504,697 drone + 5,480,340 IFC points\n", "\n", "2025-08-08 15:35:45,404 - INFO - Ending Cell 11-----------------------------------------\n", "2025-08-08 15:35:45,407 - INFO - Executing Cell 12--------------------------------------\n", "2025-08-08 15:35:45,409 - INFO - Ending Cell 12-----------------------------------------\n", "2025-08-08 15:35:45,412 - INFO - Executing Cell 13--------------------------------------\n", "2025-08-08 15:35:45,566 - INFO - COORDINATE ALIGNMENT RESULTS:\n", "  Applied offset: [-43.93, 32.88, 155.42]m\n", "  Residual centroid error: 0.000000m\n", "  Coordinate ranges after alignment:\n", "    Drone: X=1574.4m, Y=1736.4m, Z=15.5m\n", "    IFC:   X=1452.8m, Y=1561.7m, Z=8.8m\n", "  WARNING: Coordinate ranges very different - check data compatibility\n", "  Status: Coordinate alignment complete\n", "\n", "2025-08-08 15:35:45,568 - INFO - Ending Cell 13-----------------------------------------\n", "2025-08-08 15:35:45,571 - INFO - Executing Cell 14--------------------------------------\n", "2025-08-08 15:35:45,573 - INFO - Ending Cell 14-----------------------------------------\n", "2025-08-08 15:35:45,576 - INFO - Executing Cell 15--------------------------------------\n", "2025-08-08 15:35:46,410 - INFO - XY-only point clouds prepared:\n", "  Source (Drone XY): 488,129 points\n", "  Target (IFC XY): 25,468 points\n", "\n", "2025-08-08 15:35:46,412 - INFO - Ending Cell 15-----------------------------------------\n", "2025-08-08 15:35:46,416 - INFO - Executing Cell 16--------------------------------------\n", "2025-08-08 15:35:46,419 - INFO - Ending Cell 16-----------------------------------------\n", "2025-08-08 15:35:46,422 - INFO - Executing Cell 17--------------------------------------\n", "2025-08-08 15:35:46,432 - INFO - XY Offset Analysis:\n", "  Source center: [435985.153, 5011748.368]\n", "  Target center: [435992.322, 5011770.805]\n", "  XY offset: [7.169, 22.438]\n", "  XY offset magnitude: 23.555m\n", "\n", "2025-08-08 15:35:46,434 - INFO - Ending Cell 17-----------------------------------------\n", "2025-08-08 15:35:46,438 - INFO - Executing Cell 18--------------------------------------\n", "2025-08-08 15:35:46,855 - INFO - <Figure size 1500x500 with 3 Axes>\n", "2025-08-08 15:35:46,858 - INFO - Ending Cell 18-----------------------------------------\n", "2025-08-08 15:35:46,863 - INFO - Executing Cell 19--------------------------------------\n", "2025-08-08 15:35:46,868 - INFO - Ending Cell 19-----------------------------------------\n", "2025-08-08 15:35:46,873 - INFO - Executing Cell 20--------------------------------------\n", "2025-08-08 15:35:47,096 - INFO - === TESTING XY-ONLY ICP REFINEMENT ===\n", "ICP Parameters:\n", "  Max correspondence distance: 1.00m\n", "  Max iterations: 30 (reduced for stability)\n", "\n", "2025-08-08 15:35:47,940 - INFO - \n", "ICP Results:\n", "  Fitness: 0.022217\n", "  RMSE: 0.698584\n", "  XY refinement: [-1541.847, 134.373]\n", "  Refinement magnitude: 1547.692m\n", "\n", "REJECTING ICP REFINEMENT:\n", "   - Large refinement (1547.692m > 10.0m)\n", "   - Poor fitness (0.022217 < 0.1)\n", "   - Extreme single-axis movement\n", "   DECISION: Using coordinate-only alignment (more reliable)\n", "\n", "2025-08-08 15:35:47,942 - INFO - Ending Cell 20-----------------------------------------\n", "2025-08-08 15:35:47,947 - INFO - Executing Cell 21--------------------------------------\n", "2025-08-08 15:35:47,951 - INFO - Ending Cell 21-----------------------------------------\n", "2025-08-08 15:35:47,955 - INFO - Executing Cell 22--------------------------------------\n", "2025-08-08 15:35:47,965 - INFO - === HYBRID ALIGNMENT DECISION ===\n", "ICP refinement was REJECTED by validation checks\n", "Using coordinate-only alignment (more reliable)\n", "\n", "2025-08-08 15:35:47,967 - INFO - Ending Cell 22-----------------------------------------\n", "2025-08-08 15:35:47,971 - INFO - Executing Cell 23--------------------------------------\n", "2025-08-08 15:35:47,975 - INFO - Ending Cell 23-----------------------------------------\n", "2025-08-08 15:35:47,980 - INFO - Executing Cell 24--------------------------------------\n", "2025-08-08 15:35:48,161 - INFO - === FINAL ALIGNMENT QUALITY ===\n", "Method used: coordinate_only_icp_rejected\n", "Final centroid separation: 0.000000m\n", "RMSE: 30.04m\n", "Median distance: 5.00m\n", "Good points (<2m): 6.9%\n", "\n", "WARNING: Poor alignment quality detected!\n", "   RMSE 30.0m is very high, only 6.9% points well-aligned\n", "   Consider: rotation correction, scale adjustment, or different method\n", "\n", "2025-08-08 15:35:48,162 - INFO - Ending Cell 24-----------------------------------------\n", "2025-08-08 15:35:48,167 - INFO - Executing Cell 25--------------------------------------\n", "2025-08-08 15:35:48,171 - INFO - Ending Cell 25-----------------------------------------\n", "2025-08-08 15:35:48,176 - INFO - Executing Cell 26--------------------------------------\n", "2025-08-08 15:35:48,184 - INFO - === APPROACH COMPARISON ===\n", "Coordinate-only centroid error: 0.000m\n", "Hybrid approach centroid error: 0.000000m\n", "Hybrid approach RMSE: 30.04m\n", "XY refinement of 1547.692m was rejected (too large)\n", "Assessment: COORDINATE-ONLY PREFERRED\n", "\n", "=== OVERALL ALIGNMENT QUALITY ===\n", "POOR: RMSE 30.0m indicates significant alignment failure\n", "   Recommendation: Try different alignment method or check coordinate systems\n", "\n", "2025-08-08 15:35:48,186 - INFO - Ending Cell 26-----------------------------------------\n", "2025-08-08 15:35:48,190 - INFO - Executing Cell 27--------------------------------------\n", "2025-08-08 15:35:48,194 - INFO - Ending Cell 27-----------------------------------------\n", "2025-08-08 15:35:48,198 - INFO - Executing Cell 28--------------------------------------\n", "2025-08-08 15:35:48,338 - INFO - Results saved to: ../../../data/output_runs/icp_alignment/ransac\n", "\n", "=== ALIGNMENT DIAGNOSTIC ===\n", "Drone extent: [1574.4, 1736.4, 15.5]m\n", "IFC extent:   [1452.8, 1561.7, 8.8]m\n", "Scale ratios: [1.08, 1.11, 1.76]\n", " Scale mismatch detected - consider scale correction\n", "Possible issues: rotation, scale, coordinate system, or coverage mismatch\n", "\n", "=== HYBRID XY-COORDINATE ALIGNMENT COMPLETE ===\n", "Final method: coordinate_only_icp_rejected\n", "Final RMSE: 30.04m\n", "Assessment: COORDINATE-ONLY PREFERRED\n", "\n", "2025-08-08 15:35:48,340 - INFO - Ending Cell 28-----------------------------------------\n", "2025-08-08 15:35:48,346 - INFO - Executing Cell 29--------------------------------------\n", "2025-08-08 15:35:48,478 - INFO - === STANDARDIZED EVALUATION: HYBRID_XY_COORDINATE ===\n", "RMSE: 30.08m\n", "Sample size: 20,000 points\n", "{'method': 'hybrid_xy_coordinate', 'rmse': 30.07988315409083, 'median_distance': 6.020668091544069, 'sample_size': 20000, 'evaluation': 'standardized_fair_comparison'}\n", "\n", "2025-08-08 15:35:48,480 - INFO - Ending Cell 29-----------------------------------------\n", "2025-08-08 15:35:48,810 - INFO - Successfully completed ICP alignment for RANSAC\n", "2025-08-08 15:35:48,810 - INFO - Starting ICP alignment for method: RANSAC_PMF\n", "2025-08-08 15:35:48,811 - INFO - Input Notebook:  03_icp_alignment_xy_hybrid.ipynb\n", "2025-08-08 15:35:48,811 - INFO - Output Notebook: /Users/<USER>/Documents/GitHub/asbuilt-foundation-analysis/data/output_runs/trino_enel/alignment_testing/ransac_pmf/icp_alignment_ransac_pmf_executed.ipynb\n", "2025-08-08 15:35:48,811 - INFO - Input Notebook:  03_icp_alignment_xy_hybrid.ipynb\n", "2025-08-08 15:35:48,811 - INFO - Output Notebook: /Users/<USER>/Documents/GitHub/asbuilt-foundation-analysis/data/output_runs/trino_enel/alignment_testing/ransac_pmf/icp_alignment_ransac_pmf_executed.ipynb\n", "2025-08-08 15:35:48,814 - WARNING - Passed unknown parameter: quality_sample_size\n", "2025-08-08 15:35:49,518 - INFO - Executing notebook with kernel: pytorch-geo-dev\n", "2025-08-08 15:35:49,518 - INFO - Executing Cell 1---------------------------------------\n", "2025-08-08 15:35:49,522 - INFO - Ending Cell 1------------------------------------------\n", "2025-08-08 15:35:49,525 - INFO - Executing Cell 2---------------------------------------\n", "2025-08-08 15:35:49,535 - INFO - Ending Cell 2------------------------------------------\n", "2025-08-08 15:35:49,538 - INFO - Executing Cell 3---------------------------------------\n", "2025-08-08 15:35:49,549 - INFO - Ending Cell 3------------------------------------------\n", "2025-08-08 15:35:49,556 - INFO - Executing Cell 4---------------------------------------\n", "2025-08-08 15:35:49,981 - WARNING - No handler found for comm target 'dash'\n", "2025-08-08 15:35:50,710 - INFO - === HYBRID XY-COORDINATE ALIGNMENT ===\n", "Strategy: Coordinate-only Z + Selective XY refinement\n", "Site: trino_enel\n", "Output: ../../../data/output_runs/icp_alignment/ransac_pmf\n", "Timestamp: 2025-08-08 15:35:50\n", "\n", "2025-08-08 15:35:50,711 - INFO - Ending Cell 4------------------------------------------\n", "2025-08-08 15:35:50,714 - INFO - Executing Cell 5---------------------------------------\n", "2025-08-08 15:35:50,717 - INFO - Ending Cell 5------------------------------------------\n", "2025-08-08 15:35:50,719 - INFO - Executing Cell 6---------------------------------------\n", "2025-08-08 15:35:50,725 - INFO - Loading data with corrected approach...\n", "Drone file: ../../../data/processed/trino_enel/ground_segmentation/ransac_pmf/trino_enel_nonground.ply\n", "IFC metadata file: ../../../data/processed/trino_enel/ifc_metadata/GRE.EEC.S.00.IT.P.14353.00.265_enhanced_metadata.csv\n", "IFC point cloud file: ../../../data/processed/trino_enel/ifc_pointclouds/GRE.EEC.S.00.IT.P.14353.00.265_data_driven.ply (for comparison)\n", "\n", "2025-08-08 15:35:50,727 - INFO - Ending Cell 6------------------------------------------\n", "2025-08-08 15:35:50,729 - INFO - Executing Cell 7---------------------------------------\n", "2025-08-08 15:35:50,971 - INFO - Loaded drone scan: 517,002 points\n", "Loaded IFC model: 5,480,340 points\n", "Coordinate Analysis:\n", "  Full 3D offset: [-44.73, 1.48, 155.44]\n", "  XY component: 44.75m\n", "  Z component: 155.44m\n", "\n", "2025-08-08 15:35:50,973 - INFO - Ending Cell 7------------------------------------------\n", "2025-08-08 15:35:50,975 - INFO - Executing Cell 8---------------------------------------\n", "2025-08-08 15:35:50,996 - INFO - \n", "INITIAL POINT CLOUD STATISTICS\n", "\n", "Drone scan (ground truth):\n", "  Points: 517,002\n", "  X range: [435220.82, 436795.41]\n", "  Y range: [5010813.78, 5012552.58]\n", "  Z range: [-0.71, 28.19]\n", "\n", "IFC model (to be aligned):\n", "  Points: 5,480,340\n", "  X range: [435267.17, 436719.98]\n", "  Y range: [5010900.69, 5012462.43]\n", "  Z range: [152.85, 161.66]\n", "\n", "2025-08-08 15:35:50,998 - INFO - Ending Cell 8------------------------------------------\n", "2025-08-08 15:35:51,001 - INFO - Executing Cell 9---------------------------------------\n", "2025-08-08 15:35:51,006 - INFO - Ending Cell 9------------------------------------------\n", "2025-08-08 15:35:51,010 - INFO - Executing Cell 10--------------------------------------\n", "2025-08-08 15:35:51,015 - INFO - Ending Cell 10-----------------------------------------\n", "2025-08-08 15:35:51,018 - INFO - Executing Cell 11--------------------------------------\n", "2025-08-08 15:35:51,024 - INFO - \n", "=== CRS VALIDATION ===\n", "Drone CRS: Not Found\n", "IFC CRS:   32632\n", "Warning: CRS metadata missing. Proceeding with assumption of matching coordinate systems.\n", "\n", "Ready for alignment with 517,002 drone + 5,480,340 IFC points\n", "\n", "2025-08-08 15:35:51,026 - INFO - Ending Cell 11-----------------------------------------\n", "2025-08-08 15:35:51,029 - INFO - Executing Cell 12--------------------------------------\n", "2025-08-08 15:35:51,031 - INFO - Ending Cell 12-----------------------------------------\n", "2025-08-08 15:35:51,034 - INFO - Executing Cell 13--------------------------------------\n", "2025-08-08 15:35:51,188 - INFO - COORDINATE ALIGNMENT RESULTS:\n", "  Applied offset: [-44.73, 1.48, 155.44]m\n", "  Residual centroid error: 0.000000m\n", "  Coordinate ranges after alignment:\n", "    Drone: X=1574.6m, Y=1738.8m, Z=28.9m\n", "    IFC:   X=1452.8m, Y=1561.7m, Z=8.8m\n", "  WARNING: Coordinate ranges very different - check data compatibility\n", "  Status: Coordinate alignment complete\n", "\n", "2025-08-08 15:35:51,190 - INFO - Ending Cell 13-----------------------------------------\n", "2025-08-08 15:35:51,194 - INFO - Executing Cell 14--------------------------------------\n", "2025-08-08 15:35:51,197 - INFO - Ending Cell 14-----------------------------------------\n", "2025-08-08 15:35:51,200 - INFO - Executing Cell 15--------------------------------------\n", "2025-08-08 15:35:51,985 - INFO - XY-only point clouds prepared:\n", "  Source (Drone XY): 499,282 points\n", "  Target (IFC XY): 25,468 points\n", "\n", "2025-08-08 15:35:51,987 - INFO - Ending Cell 15-----------------------------------------\n", "2025-08-08 15:35:51,990 - INFO - Executing Cell 16--------------------------------------\n", "2025-08-08 15:35:51,993 - INFO - Ending Cell 16-----------------------------------------\n", "2025-08-08 15:35:51,996 - INFO - Executing Cell 17--------------------------------------\n", "2025-08-08 15:35:52,006 - INFO - XY Offset Analysis:\n", "  Source center: [435984.698, 5011749.791]\n", "  Target center: [435992.322, 5011770.805]\n", "  XY offset: [7.625, 21.015]\n", "  XY offset magnitude: 22.355m\n", "\n", "2025-08-08 15:35:52,007 - INFO - Ending Cell 17-----------------------------------------\n", "2025-08-08 15:35:52,011 - INFO - Executing Cell 18--------------------------------------\n", "2025-08-08 15:35:52,509 - INFO - <Figure size 1500x500 with 3 Axes>\n", "2025-08-08 15:35:52,512 - INFO - Ending Cell 18-----------------------------------------\n", "2025-08-08 15:35:52,517 - INFO - Executing Cell 19--------------------------------------\n", "2025-08-08 15:35:52,522 - INFO - Ending Cell 19-----------------------------------------\n", "2025-08-08 15:35:52,526 - INFO - Executing Cell 20--------------------------------------\n", "2025-08-08 15:35:52,738 - INFO - === TESTING XY-ONLY ICP REFINEMENT ===\n", "ICP Parameters:\n", "  Max correspondence distance: 1.00m\n", "  Max iterations: 30 (reduced for stability)\n", "\n", "2025-08-08 15:35:53,725 - INFO - \n", "ICP Results:\n", "  Fitness: 0.021747\n", "  RMSE: 0.697495\n", "  XY refinement: [-1296.039, 112.982]\n", "  Refinement magnitude: 1300.954m\n", "\n", "REJECTING ICP REFINEMENT:\n", "   - Large refinement (1300.954m > 10.0m)\n", "   - Poor fitness (0.021747 < 0.1)\n", "   - Extreme single-axis movement\n", "   DECISION: Using coordinate-only alignment (more reliable)\n", "\n", "2025-08-08 15:35:53,727 - INFO - Ending Cell 20-----------------------------------------\n", "2025-08-08 15:35:53,733 - INFO - Executing Cell 21--------------------------------------\n", "2025-08-08 15:35:53,737 - INFO - Ending Cell 21-----------------------------------------\n", "2025-08-08 15:35:53,742 - INFO - Executing Cell 22--------------------------------------\n", "2025-08-08 15:35:53,752 - INFO - === HYBRID ALIGNMENT DECISION ===\n", "ICP refinement was REJECTED by validation checks\n", "Using coordinate-only alignment (more reliable)\n", "\n", "2025-08-08 15:35:53,754 - INFO - Ending Cell 22-----------------------------------------\n", "2025-08-08 15:35:53,759 - INFO - Executing Cell 23--------------------------------------\n", "2025-08-08 15:35:53,763 - INFO - Ending Cell 23-----------------------------------------\n", "2025-08-08 15:35:53,768 - INFO - Executing Cell 24--------------------------------------\n", "2025-08-08 15:35:53,953 - INFO - === FINAL ALIGNMENT QUALITY ===\n", "Method used: coordinate_only_icp_rejected\n", "Final centroid separation: 0.000000m\n", "RMSE: 27.48m\n", "Median distance: 4.99m\n", "Good points (<2m): 6.9%\n", "\n", "WARNING: Poor alignment quality detected!\n", "   RMSE 27.5m is very high, only 6.9% points well-aligned\n", "   Consider: rotation correction, scale adjustment, or different method\n", "\n", "2025-08-08 15:35:53,955 - INFO - Ending Cell 24-----------------------------------------\n", "2025-08-08 15:35:53,960 - INFO - Executing Cell 25--------------------------------------\n", "2025-08-08 15:35:53,964 - INFO - Ending Cell 25-----------------------------------------\n", "2025-08-08 15:35:53,968 - INFO - Executing Cell 26--------------------------------------\n", "2025-08-08 15:35:53,976 - INFO - === APPROACH COMPARISON ===\n", "Coordinate-only centroid error: 0.000m\n", "Hybrid approach centroid error: 0.000000m\n", "Hybrid approach RMSE: 27.48m\n", "XY refinement of 1300.954m was rejected (too large)\n", "Assessment: COORDINATE-ONLY PREFERRED\n", "\n", "=== OVERALL ALIGNMENT QUALITY ===\n", "POOR: RMSE 27.5m indicates significant alignment failure\n", "   Recommendation: Try different alignment method or check coordinate systems\n", "\n", "2025-08-08 15:35:53,978 - INFO - Ending Cell 26-----------------------------------------\n", "2025-08-08 15:35:53,982 - INFO - Executing Cell 27--------------------------------------\n", "2025-08-08 15:35:53,986 - INFO - Ending Cell 27-----------------------------------------\n", "2025-08-08 15:35:53,990 - INFO - Executing Cell 28--------------------------------------\n", "2025-08-08 15:35:54,139 - INFO - Results saved to: ../../../data/output_runs/icp_alignment/ransac_pmf\n", "\n", "=== ALIGNMENT DIAGNOSTIC ===\n", "Drone extent: [1574.6, 1738.8, 28.9]m\n", "IFC extent:   [1452.8, 1561.7, 8.8]m\n", "Scale ratios: [1.08, 1.11, 3.28]\n", " Scale mismatch detected - consider scale correction\n", "Possible issues: rotation, scale, coordinate system, or coverage mismatch\n", "\n", "=== HYBRID XY-COORDINATE ALIGNMENT COMPLETE ===\n", "Final method: coordinate_only_icp_rejected\n", "Final RMSE: 27.48m\n", "Assessment: COORDINATE-ONLY PREFERRED\n", "\n", "2025-08-08 15:35:54,141 - INFO - Ending Cell 28-----------------------------------------\n", "2025-08-08 15:35:54,146 - INFO - Executing Cell 29--------------------------------------\n", "2025-08-08 15:35:54,257 - INFO - === STANDARDIZED EVALUATION: HYBRID_XY_COORDINATE ===\n", "RMSE: 28.12m\n", "Sample size: 20,000 points\n", "{'method': 'hybrid_xy_coordinate', 'rmse': 28.1164740908521, 'median_distance': 6.063814127994267, 'sample_size': 20000, 'evaluation': 'standardized_fair_comparison'}\n", "\n", "2025-08-08 15:35:54,259 - INFO - Ending Cell 29-----------------------------------------\n", "2025-08-08 15:35:54,590 - INFO - Successfully completed ICP alignment for RANSAC_PMF\n", "2025-08-08 15:35:54,590 - INFO - \n", "=== ICP ALIGNMENT EXECUTION SUMMARY ===\n", "2025-08-08 15:35:54,590 - INFO - Successful methods: ['csf', 'pmf', 'ransac', 'ransac_pmf']\n", "2025-08-08 15:35:54,591 - INFO - Failed methods: []\n", "2025-08-08 15:35:54,591 - INFO - Success rate: 4/4\n"]}], "source": ["def execute_icp_alignment_for_method(method, site_name, kernel_name):\n", "    \"\"\"Execute ICP alignment for a specific ground segmentation method\"\"\"\n", "    \n", "    logger.info(f\"Starting ICP alignment for method: {method.upper()}\")\n", "    logger.info(f\"Input Notebook:  03_icp_alignment_xy_hybrid.ipynb\")\n", "    \n", "    # Output notebook\n", "    output_notebook = alignment_testing_output / method / f\"icp_alignment_{method}_executed.ipynb\"\n", "    logger.info(f\"Output Notebook: {output_notebook}\")\n", "    \n", "    # Parameters for papermill - but since target notebook doesn't have parameters, \n", "    # we'll modify the ground_method variable directly in the notebook\n", "    parameters = {\n", "        \"ground_method\": method,\n", "        \"site_name\": site_name,\n", "        \"save_results\": True,\n", "        \"quality_sample_size\": 5000\n", "    }\n", "    \n", "    try:\n", "        # Execute using papermill with specific kernel\n", "        import papermill as pm\n", "        \n", "        pm.execute_notebook(\n", "            \"03_icp_alignment_xy_hybrid.ipynb\",\n", "            str(output_notebook),\n", "            parameters=parameters,\n", "            kernel_name=kernel_name,\n", "            log_output=True,\n", "            progress_bar=False\n", "        )\n", "        \n", "        logger.info(f\"Successfully completed ICP alignment for {method.upper()}\")\n", "        return True\n", "        \n", "    except Exception as e:\n", "        logger.error(f\"Failed ICP alignment for {method.upper()}: {str(e)}\")\n", "        return False\n", "\n", "# Execute ICP alignment for all methods\n", "if execute_all_methods:\n", "    results = {}\n", "    \n", "    for method in ground_methods:\n", "        success = execute_icp_alignment_for_method(method, site_name, kernel_name)\n", "        results[method] = success\n", "    \n", "    # Summary\n", "    successful_methods = [method for method, success in results.items() if success]\n", "    failed_methods = [method for method, success in results.items() if not success]\n", "    \n", "    logger.info(f\"\\n=== ICP ALIGNMENT EXECUTION SUMMARY ===\")\n", "    logger.info(f\"Successful methods: {successful_methods}\")\n", "    logger.info(f\"Failed methods: {failed_methods}\")\n", "    logger.info(f\"Success rate: {len(successful_methods)}/{len(ground_methods)}\")\n", "else:\n", "    logger.info(\"Skipping ICP alignment execution (execute_all_methods=False)\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Analysis Execution\n", "\n", "Run the analysis notebook to compare alignment results across all ground segmentation methods."]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-08-08 15:35:54,598 - INFO - Starting alignment results analysis...\n", "2025-08-08 15:35:54,599 - ERROR - Analysis failed: name 'seaborn_available' is not defined\n", "2025-08-08 15:35:54,599 - INFO - Trying analysis without seaborn plots...\n", "2025-08-08 15:35:54,599 - INFO - Input Notebook:  07_analyze_alignment_results.ipynb\n", "2025-08-08 15:35:54,600 - INFO - Output Notebook: /Users/<USER>/Documents/GitHub/asbuilt-foundation-analysis/data/output_runs/trino_enel/alignment_testing/alignment_analysis_results.ipynb\n", "2025-08-08 15:35:54,602 - WARNING - Passed unknown parameter: site_name\n", "2025-08-08 15:35:54,602 - WARNING - Passed unknown parameter: methods\n", "2025-08-08 15:35:54,603 - WARNING - Passed unknown parameter: save_results\n", "2025-08-08 15:35:54,603 - WARNING - Passed unknown parameter: generate_plots\n", "2025-08-08 15:35:55,182 - INFO - Executing notebook with kernel: pytorch-geo-dev\n", "2025-08-08 15:35:55,185 - INFO - Executing Cell 1---------------------------------------\n", "2025-08-08 15:35:55,191 - INFO - Ending Cell 1------------------------------------------\n", "2025-08-08 15:35:55,196 - INFO - Executing Cell 2---------------------------------------\n", "2025-08-08 15:35:55,199 - INFO - Ending Cell 2------------------------------------------\n", "2025-08-08 15:35:55,203 - INFO - Executing Cell 3---------------------------------------\n", "2025-08-08 15:35:55,215 - INFO - Ending Cell 3------------------------------------------\n", "2025-08-08 15:35:55,217 - INFO - Executing Cell 4---------------------------------------\n", "2025-08-08 15:35:55,224 - INFO - Ending Cell 4------------------------------------------\n", "2025-08-08 15:35:55,226 - INFO - Executing Cell 5---------------------------------------\n", "2025-08-08 15:35:55,228 - INFO - Ending Cell 5------------------------------------------\n", "2025-08-08 15:35:55,231 - INFO - Executing Cell 6---------------------------------------\n", "2025-08-08 15:35:56,035 - WARNING - 2025-08-08 15:35:56,033 - INFO - === ALIGNMENT RESULTS ANALYSIS ===\n", "\n", "2025-08-08 15:35:56,035 - WARNING - 2025-08-08 15:35:56,034 - INFO - Site: trino_enel\n", "\n", "2025-08-08 15:35:56,036 - WARNING - 2025-08-08 15:35:56,034 - INFO - Methods: ['csf', 'pmf', 'ransac', 'ransac_pmf']\n", "\n", "2025-08-08 15:35:56,037 - WARNING - 2025-08-08 15:35:56,034 - INFO - Timestamp: 2025-08-08 15:35:56\n", "\n", "2025-08-08 15:35:56,038 - INFO - Ending Cell 6------------------------------------------\n", "2025-08-08 15:35:56,040 - INFO - Executing Cell 7---------------------------------------\n", "2025-08-08 15:35:56,045 - WARNING - 2025-08-08 15:35:56,044 - INFO - Results directory: ../../../data/output_runs/icp_alignment\n", "\n", "2025-08-08 15:35:56,045 - WARNING - 2025-08-08 15:35:56,044 - INFO - Output directory: /Users/<USER>/Documents/GitHub/asbuilt-foundation-analysis/data/output_runs/alignment_analysis/trino_enel_20250808_153556\n", "\n", "2025-08-08 15:35:56,047 - INFO - Ending Cell 7------------------------------------------\n", "2025-08-08 15:35:56,048 - INFO - Executing Cell 8---------------------------------------\n", "2025-08-08 15:35:56,050 - INFO - Ending Cell 8------------------------------------------\n", "2025-08-08 15:35:56,052 - INFO - Executing Cell 9---------------------------------------\n", "2025-08-08 15:35:56,058 - WARNING - 2025-08-08 15:35:56,058 - INFO - Loaded results for CSF\n", "\n", "2025-08-08 15:35:56,060 - WARNING - 2025-08-08 15:35:56,059 - INFO - Loaded results for PMF\n", "\n", "2025-08-08 15:35:56,061 - WARNING - 2025-08-08 15:35:56,060 - INFO - Loaded results for RANSAC\n", "\n", "2025-08-08 15:35:56,062 - WARNING - 2025-08-08 15:35:56,061 - INFO - Loaded results for RANSAC_PMF\n", "\n", "2025-08-08 15:35:56,063 - WARNING - 2025-08-08 15:35:56,062 - INFO - Loaded 4 method results for analysis\n", "\n", "2025-08-08 15:35:56,064 - INFO - Ending Cell 9------------------------------------------\n", "2025-08-08 15:35:56,066 - INFO - Executing Cell 10--------------------------------------\n", "2025-08-08 15:35:56,068 - INFO - Ending Cell 10-----------------------------------------\n", "2025-08-08 15:35:56,070 - INFO - Executing Cell 11--------------------------------------\n", "2025-08-08 15:35:56,136 - INFO - <pandas.io.formats.style.Styler at 0x2a0f7ff10>\n", "2025-08-08 15:35:56,138 - INFO - Ending Cell 11-----------------------------------------\n", "2025-08-08 15:35:56,140 - INFO - Executing Cell 12--------------------------------------\n", "2025-08-08 15:35:56,142 - INFO - Ending Cell 12-----------------------------------------\n", "2025-08-08 15:35:56,144 - INFO - Executing Cell 13--------------------------------------\n", "2025-08-08 15:35:56,774 - WARNING - 2025-08-08 15:35:56,760 - INFO - Saved comparison plot: /Users/<USER>/Documents/GitHub/asbuilt-foundation-analysis/data/output_runs/alignment_analysis/trino_enel_20250808_153556/alignment_comparison_trino_enel.png\n", "\n", "2025-08-08 15:35:56,882 - INFO - <Figure size 1500x1200 with 5 Axes>\n", "2025-08-08 15:35:56,884 - INFO - Ending Cell 13-----------------------------------------\n", "2025-08-08 15:35:56,886 - INFO - Executing Cell 14--------------------------------------\n", "2025-08-08 15:35:56,896 - INFO - Ending Cell 14-----------------------------------------\n", "2025-08-08 15:35:56,899 - INFO - Executing Cell 15--------------------------------------\n", "2025-08-08 15:35:56,904 - WARNING - 2025-08-08 15:35:56,903 - INFO - === GROUND SEGMENTATION IMPACT ===\n", "\n", "2025-08-08 15:35:56,905 - WARNING - 2025-08-08 15:35:56,904 - INFO - Best: CSF | RMSE: 19.374m | Fitness: 0.000638\n", "\n", "2025-08-08 15:35:56,906 - WARNING - 2025-08-08 15:35:56,904 - INFO - Worst: RANSAC_PMF | RMSE: 10.803m | Fitness: 0.001223\n", "\n", "2025-08-08 15:35:56,907 - WARNING - 2025-08-08 15:35:56,904 - INFO - Impact: RMSE improved by -8.572m (-79.3%) | Fitness by -0.000586 (-47.9%)\n", "\n", "2025-08-08 15:35:56,908 - INFO - Ending Cell 15-----------------------------------------\n", "2025-08-08 15:35:56,911 - INFO - Executing Cell 16--------------------------------------\n", "2025-08-08 15:35:56,914 - INFO - Ending Cell 16-----------------------------------------\n", "2025-08-08 15:35:56,916 - INFO - Executing Cell 17--------------------------------------\n", "2025-08-08 15:35:56,925 - WARNING - 2025-08-08 15:35:56,924 - INFO - Saved detailed results: /Users/<USER>/Documents/GitHub/asbuilt-foundation-analysis/data/output_runs/alignment_analysis/trino_enel_20250808_153556/alignment_analysis_results_trino_enel.csv\n", "\n", "2025-08-08 15:35:56,926 - WARNING - 2025-08-08 15:35:56,925 - INFO - Saved summary: /Users/<USER>/Documents/GitHub/asbuilt-foundation-analysis/data/output_runs/alignment_analysis/trino_enel_20250808_153556/alignment_analysis_summary_trino_enel.csv\n", "\n", "2025-08-08 15:35:56,927 - WARNING - 2025-08-08 15:35:56,926 - INFO - Saved metadata: /Users/<USER>/Documents/GitHub/asbuilt-foundation-analysis/data/output_runs/alignment_analysis/trino_enel_20250808_153556/alignment_analysis_metadata_trino_enel.json\n", "\n", "2025-08-08 15:35:56,928 - WARNING - 2025-08-08 15:35:56,926 - INFO - \n", "=== ANALYSIS COMPLETE ===\n", "\n", "2025-08-08 15:35:56,929 - WARNING - 2025-08-08 15:35:56,926 - INFO - Results saved to: /Users/<USER>/Documents/GitHub/asbuilt-foundation-analysis/data/output_runs/alignment_analysis/trino_enel_20250808_153556\n", "\n", "2025-08-08 15:35:56,930 - INFO - Ending Cell 17-----------------------------------------\n", "2025-08-08 15:35:57,154 - INFO - Analysis completed successfully (without plots)\n"]}], "source": ["# Execute analysis notebook\n", "if run_analysis:\n", "    logger.info(\"Starting alignment results analysis...\")\n", "    \n", "    try:\n", "        import papermill as pm\n", "        \n", "        analysis_output = alignment_testing_output / \"alignment_analysis_results.ipynb\"\n", "        \n", "        # Use the fixed analysis notebook\n", "        analysis_notebook = \"07_analyze_alignment_results.ipynb\"\n", "        \n", "        pm.execute_notebook(\n", "            analysis_notebook,\n", "            str(analysis_output),\n", "            parameters={\n", "                \"site_name\": site_name,\n", "                \"methods\": ground_methods,\n", "                \"save_results\": save_results,\n", "                \"generate_plots\": seaborn_available  # Only generate plots if seaborn is available\n", "            },\n", "            kernel_name=kernel_name,\n", "            log_output=True,\n", "            progress_bar=False\n", "        )\n", "        \n", "        logger.info(\"Analysis completed successfully\")\n", "        \n", "    except Exception as e:\n", "        logger.error(f\"Analysis failed: {str(e)}\")\n", "        logger.info(\"Trying analysis without seaborn plots...\")\n", "        \n", "        # Retry without plots if seaborn is the issue\n", "        try:\n", "            pm.execute_notebook(\n", "                analysis_notebook,\n", "                str(analysis_output),\n", "                parameters={\n", "                    \"site_name\": site_name,\n", "                    \"methods\": ground_methods,\n", "                    \"save_results\": save_results,\n", "                    \"generate_plots\": False  # Disable plots\n", "                },\n", "                kernel_name=kernel_name,\n", "                log_output=True,\n", "                progress_bar=False\n", "            )\n", "            logger.info(\"Analysis completed successfully (without plots)\")\n", "        except Exception as e2:\n", "            logger.error(f\"Analysis failed even without plots: {str(e2)}\")\n", "else:\n", "    logger.info(\"Skipping analysis execution (run_analysis=False)\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Visualization Execution\n", "\n", "Run the visualization notebook to generate final comparison plots."]}, {"cell_type": "code", "execution_count": 19, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-08-08 15:38:55,778 - INFO - Starting alignment visualization...\n", "2025-08-08 15:38:55,780 - INFO - Input Notebook:  08_visualize_aligned_pointcloud.ipynb\n", "2025-08-08 15:38:55,780 - INFO - Output Notebook: /Users/<USER>/Documents/GitHub/asbuilt-foundation-analysis/data/output_runs/trino_enel/alignment_testing/alignment_visualization_results.ipynb\n", "2025-08-08 15:38:55,784 - WARNING - Passed unknown parameter: site_name\n", "2025-08-08 15:38:55,785 - WARNING - Passed unknown parameter: ground_method\n", "2025-08-08 15:38:55,785 - WARNING - Passed unknown parameter: show_3d_viewer\n", "2025-08-08 15:38:55,785 - WARNING - Passed unknown parameter: save_visualizations\n", "2025-08-08 15:38:56,572 - INFO - Executing notebook with kernel: pytorch-geo-dev\n", "2025-08-08 15:38:56,573 - INFO - Executing Cell 1---------------------------------------\n", "2025-08-08 15:38:56,577 - INFO - Ending Cell 1------------------------------------------\n", "2025-08-08 15:38:56,579 - INFO - Executing Cell 2---------------------------------------\n", "2025-08-08 15:38:56,582 - INFO - Ending Cell 2------------------------------------------\n", "2025-08-08 15:38:56,584 - INFO - Executing Cell 3---------------------------------------\n", "2025-08-08 15:38:56,591 - INFO - Ending Cell 3------------------------------------------\n", "2025-08-08 15:38:56,593 - INFO - Executing Cell 4---------------------------------------\n", "2025-08-08 15:38:56,600 - INFO - Ending Cell 4------------------------------------------\n", "2025-08-08 15:38:56,602 - INFO - Executing Cell 5---------------------------------------\n", "2025-08-08 15:38:56,603 - INFO - Ending Cell 5------------------------------------------\n", "2025-08-08 15:38:56,605 - INFO - Executing Cell 6---------------------------------------\n", "2025-08-08 15:38:57,162 - WARNING - No handler found for comm target 'dash'\n", "2025-08-08 15:38:58,327 - WARNING - 2025-08-08 15:38:58,325 - INFO - === ALIGNMENT VISUALIZATION ===\n", "\n", "2025-08-08 15:38:58,328 - WARNING - 2025-08-08 15:38:58,326 - INFO - Site: trino_enel\n", "\n", "2025-08-08 15:38:58,329 - WARNING - 2025-08-08 15:38:58,326 - INFO - Ground method: ransac_pmf\n", "\n", "2025-08-08 15:38:58,330 - WARNING - 2025-08-08 15:38:58,326 - INFO - Timestamp: 2025-08-08 15:38:58\n", "\n", "2025-08-08 15:38:58,331 - INFO - Ending Cell 6------------------------------------------\n", "2025-08-08 15:38:58,333 - INFO - Executing Cell 7---------------------------------------\n", "2025-08-08 15:38:58,338 - WARNING - 2025-08-08 15:38:58,337 - INFO - Loading alignment results from: ../../../data/output_runs/icp_alignment/ransac_pmf\n", "\n", "2025-08-08 15:38:58,340 - INFO - Ending Cell 7------------------------------------------\n", "2025-08-08 15:38:58,342 - INFO - Executing Cell 8---------------------------------------\n", "2025-08-08 15:38:58,344 - INFO - Ending Cell 8------------------------------------------\n", "2025-08-08 15:38:58,346 - INFO - Executing Cell 9---------------------------------------\n", "2025-08-08 15:38:58,699 - WARNING - 2025-08-08 15:38:58,697 - INFO - Loaded drone cloud: 517002 points\n", "\n", "2025-08-08 15:38:58,700 - WARNING - 2025-08-08 15:38:58,698 - INFO - Loaded IFC cloud: 5480340 points\n", "\n", "2025-08-08 15:38:58,701 - WARNING - 2025-08-08 15:38:58,698 - INFO - Loaded aligned cloud: 1359240 points\n", "\n", "2025-08-08 15:38:58,702 - WARNING - 2025-08-08 15:38:58,699 - INFO - Alignment RMSE: 10.803m\n", "\n", "2025-08-08 15:38:58,703 - WARNING - 2025-08-08 15:38:58,700 - INFO - ICP Fitness: 0.001223\n", "\n", "2025-08-08 15:38:58,704 - INFO - Ending Cell 9------------------------------------------\n", "2025-08-08 15:38:58,706 - INFO - Executing Cell 10--------------------------------------\n", "2025-08-08 15:38:58,708 - INFO - Ending Cell 10-----------------------------------------\n", "2025-08-08 15:38:58,710 - INFO - Executing Cell 11--------------------------------------\n", "2025-08-08 15:38:59,511 - WARNING - 2025-08-08 15:38:59,496 - INFO - Saved visualization: /Users/<USER>/Documents/GitHub/asbuilt-foundation-analysis/data/output_runs/alignment_visualization/trino_enel_20250808_153858/alignment_visualization_ransac_pmf_trino_enel.png\n", "\n", "2025-08-08 15:38:59,700 - INFO - <Figure size 1500x1200 with 4 Axes>\n", "2025-08-08 15:38:59,702 - INFO - Ending Cell 11-----------------------------------------\n", "2025-08-08 15:38:59,706 - INFO - Executing Cell 12--------------------------------------\n", "2025-08-08 15:38:59,709 - INFO - Ending Cell 12-----------------------------------------\n", "2025-08-08 15:38:59,713 - INFO - Executing Cell 13--------------------------------------\n", "2025-08-08 15:38:59,735 - WARNING - 2025-08-08 15:38:59,720 - INFO - Preparing 3D visualization...\n", "\n", "2025-08-08 15:39:00,127 - WARNING - 2025-08-08 15:38:59,734 - INFO - Displaying 51701 drone points and 50343 aligned IFC points\n", "\n", "2025-08-08 15:39:00,128 - WARNING - 2025-08-08 15:39:00,126 - INFO - 3D viewer opened. Blue = Drone, Red = Aligned IFC\n", "\n", "2025-08-08 15:39:02,765 - WARNING - 2025-08-08 15:39:00,127 - INFO - Close the 3D viewer window to continue...\n", "\n", "2025-08-08 15:39:02,766 - WARNING - 2025-08-08 15:39:02,764 - INFO - 3D visualization closed\n", "\n", "2025-08-08 15:39:02,767 - INFO - Ending Cell 13-----------------------------------------\n", "2025-08-08 15:39:02,771 - INFO - Executing Cell 14--------------------------------------\n", "2025-08-08 15:39:02,774 - INFO - Ending Cell 14-----------------------------------------\n", "2025-08-08 15:39:02,778 - INFO - Executing Cell 15--------------------------------------\n", "2025-08-08 15:39:02,785 - WARNING - 2025-08-08 15:39:02,784 - INFO - ==================================================\n", "\n", "2025-08-08 15:39:02,786 - WARNING - 2025-08-08 15:39:02,784 - INFO - ALIGNMENT QUALITY ASSESSMENT\n", "\n", "2025-08-08 15:39:02,787 - WARNING - 2025-08-08 15:39:02,784 - INFO - Site: Trino Enel | Method: RANSAC_PMF\n", "\n", "2025-08-08 15:39:02,788 - WARNING - 2025-08-08 15:39:02,785 - INFO - RMSE: 10.803m | Max Dev: 66.063m | Fitness: 0.001223\n", "\n", "2025-08-08 15:39:02,789 - WARNING - 2025-08-08 15:39:02,785 - INFO - Quality: POOR | Requires significant improvement\n", "\n", "2025-08-08 15:39:02,790 - WARNING - 2025-08-08 15:39:02,785 - INFO - ==================================================\n", "\n", "2025-08-08 15:39:02,790 - WARNING - 2025-08-08 15:39:02,786 - INFO - Summary saved: /Users/<USER>/Documents/GitHub/asbuilt-foundation-analysis/data/output_runs/alignment_visualization/trino_enel_20250808_153858/summary_ransac_pmf_trino_enel.json\n", "\n", "2025-08-08 15:39:02,791 - WARNING - 2025-08-08 15:39:02,786 - INFO - Visualization complete | Output: /Users/<USER>/Documents/GitHub/asbuilt-foundation-analysis/data/output_runs/alignment_visualization/trino_enel_20250808_153858\n", "\n", "2025-08-08 15:39:02,792 - INFO - Ending Cell 15-----------------------------------------\n", "2025-08-08 15:39:03,120 - INFO - Visualization completed successfully\n"]}], "source": ["# Execute visualization notebook\n", "if run_visualization:\n", "    logger.info(\"Starting alignment visualization...\")\n", "    \n", "    try:\n", "        import papermill as pm\n", "        \n", "        viz_output = alignment_testing_output / \"alignment_visualization_results.ipynb\"\n", "        \n", "        # Use the fixed visualization notebook\n", "        viz_notebook = \"08_visualize_aligned_pointcloud.ipynb\"\n", "        \n", "        pm.execute_notebook(\n", "            viz_notebook,\n", "            str(viz_output),\n", "            parameters={\n", "                \"site_name\": site_name,\n", "                \"ground_method\": \"ransac_pmf\",  # Use best performing method\n", "                \"show_3d_viewer\": False,  # Disable for automated execution\n", "                \"save_visualizations\": save_results\n", "            },\n", "            kernel_name=kernel_name,\n", "            log_output=True,\n", "            progress_bar=False\n", "        )\n", "        \n", "        logger.info(\"Visualization completed successfully\")\n", "        \n", "    except Exception as e:\n", "        logger.error(f\"Visualization failed: {str(e)}\")\n", "else:\n", "    logger.info(\"Skipping visualization execution (run_visualization=False)\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Final Summary\n", "\n", "Generate a comprehensive summary of all executed workflows."]}, {"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-08-08 15:35:57,166 - INFO - ==================================================\n", "2025-08-08 15:35:57,167 - INFO - PAPERMILL EXECUTION COMPLETE\n", "2025-08-08 15:35:57,167 - INFO - ==================================================\n", "2025-08-08 15:35:57,167 - INFO - Site: trino_enel | Kernel: pytorch-geo-dev\n", "2025-08-08 15:35:57,168 - INFO - Timestamp: 2025-08-08 15:35:57\n", "2025-08-08 15:35:57,168 - INFO - Workflows: ICP Alignment: [Y] | Analysis: [Y] | Visualization: [Y]\n", "2025-08-08 15:35:57,168 - INFO - ICP Output: /Users/<USER>/Documents/GitHub/asbuilt-foundation-analysis/data/output_runs/trino_enel/icp_alignment\n", "2025-08-08 15:35:57,168 - INFO - Test Output: /Users/<USER>/Documents/GitHub/asbuilt-foundation-analysis/data/output_runs/trino_enel/alignment_testing\n", "2025-08-08 15:35:57,169 - INFO - ==================================================\n"]}], "source": ["logger.info(\"=\"*50)\n", "logger.info(\"PAPERMILL EXECUTION COMPLETE\")\n", "logger.info(\"=\"*50)\n", "logger.info(f\"Site: {site_name} | Kernel: {kernel_name}\")\n", "logger.info(f\"Timestamp: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\")\n", "\n", "# Workflow status\n", "workflows = [\n", "    f\"ICP Alignment: {'[Y]' if execute_all_methods else '[N]'}\",\n", "    f\"Analysis: {'[Y]' if run_analysis else '[N]'}\",\n", "    f\"Visualization: {'[Y]' if run_visualization else '[N]'}\"\n", "]\n", "logger.info(\"Workflows: \" + \" | \".join(workflows))\n", "\n", "logger.info(f\"ICP Output: {icp_alignment_output}\")\n", "logger.info(f\"Test Output: {alignment_testing_output}\")\n", "logger.info(\"=\"*50)"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 4}