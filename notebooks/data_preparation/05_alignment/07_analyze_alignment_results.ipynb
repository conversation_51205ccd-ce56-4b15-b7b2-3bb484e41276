{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Alignment Results Analysis\n", "\n", "This notebook analyzes ICP alignment results across different ground segmentation methods to evaluate the impact of ground segmentation quality on alignment performance.\n", "\n", "**Input**: ICP alignment results from all ground segmentation methods  \n", "**Output**: Comparative analysis, metrics, and visualizations  \n", "**Methods**: CSF, PMF, RANSAC, RANSAC+PMF  \n", "\n", "**Author**: <PERSON><PERSON><PERSON>  \n", "**Date**: August 2025  \n", "**Project**: As-Built Foundation Analysis"]}, {"cell_type": "markdown", "metadata": {"tags": ["parameters"]}, "source": ["## Papermill Parameters"]}, {"cell_type": "code", "execution_count": 20, "metadata": {"tags": ["parameters"]}, "outputs": [], "source": ["# Papermill parameters\n", "site_name = \"trino_enel\"\n", "methods = [\"csf\", \"pmf\", \"ransac\", \"ransac_pmf\"]\n", "save_results = True\n", "generate_plots = True"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Setup and Data Loading"]}, {"cell_type": "code", "execution_count": 21, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-08-08 15:47:12,275 - INFO - === ALIGNMENT RESULTS ANALYSIS ===\n", "2025-08-08 15:47:12,276 - INFO - Site: trino_enel\n", "2025-08-08 15:47:12,276 - INFO - Methods: ['csf', 'pmf', 'ransac', 'ransac_pmf']\n", "2025-08-08 15:47:12,278 - INFO - Timestamp: 2025-08-08 15:47:12\n"]}], "source": ["import json\n", "import numpy as np\n", "from pathlib import Path\n", "import pandas as pd\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "import logging\n", "from datetime import datetime\n", "import sys\n", "\n", "# Add the notebooks folder to sys.path\n", "notebooks_root = Path(__file__).resolve().parents[1] if '__file__' in globals() else Path.cwd().parents[1]\n", "if str(notebooks_root) not in sys.path:\n", "    sys.path.insert(0, str(notebooks_root))\n", "\n", "# Import shared configuration\n", "from shared.config import get_data_path, get_output_path\n", "\n", "# Setup logging\n", "logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')\n", "logger = logging.getLogger(__name__)\n", "\n", "# Configure plotting\n", "plt.style.use('default')\n", "sns.set_palette(\"husl\")\n", "\n", "logger.info(\"=== ALIGNMENT RESULTS ANALYSIS ===\")\n", "logger.info(f\"Site: {site_name}\")\n", "logger.info(f\"Methods: {methods}\")\n", "logger.info(f\"Timestamp: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\")"]}, {"cell_type": "code", "execution_count": 22, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-08-08 15:47:12,284 - INFO - Results directory: ../../../data/output_runs/icp_alignment\n", "2025-08-08 15:47:12,284 - INFO - Output directory: /Users/<USER>/Documents/GitHub/asbuilt-foundation-analysis/data/output_runs/alignment_analysis/trino_enel_20250808_154712\n"]}], "source": ["# Define paths\n", "results_dir = Path(\"../../../data/output_runs/icp_alignment\")\n", "output_dir = get_output_path(\"alignment_analysis\", site_name)\n", "output_dir.mkdir(parents=True, exist_ok=True)\n", "\n", "logger.info(f\"Results directory: {results_dir}\")\n", "logger.info(f\"Output directory: {output_dir}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Data Collection and Processing"]}, {"cell_type": "code", "execution_count": 23, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-08-08 15:47:12,295 - INFO - ✓ Loaded hybrid results for CSF - RMSE: 49.59m\n", "2025-08-08 15:47:12,296 - INFO - ✓ Loaded hybrid results for PMF - RMSE: 27.99m\n", "2025-08-08 15:47:12,297 - INFO - ✓ Loaded hybrid results for RANSAC - RMSE: 30.04m\n", "2025-08-08 15:47:12,298 - INFO - ✓ Loaded hybrid results for RANSAC_PMF - RMSE: 27.48m\n", "2025-08-08 15:47:12,300 - INFO - Loaded 4 method results for analysis\n"]}], "source": ["def load_alignment_results(results_dir, methods):\n", "    \"\"\"Load alignment results from all methods\"\"\"\n", "    \n", "    all_results = []\n", "    missing_methods = []\n", "    \n", "    for method in methods:\n", "        # Try hybrid format first (newer)\n", "        hybrid_path = results_dir / method / \"trino_enel_hybrid_alignment_results.json\"\n", "        # Fallback to old ICP format\n", "        metrics_path = results_dir / method / f\"icp_metrics_{method}.json\"\n", "        transform_path = results_dir / method / f\"icp_transformation_{method}.npy\"\n", "        \n", "        if hybrid_path.exists():\n", "            try:\n", "                # Load hybrid format\n", "                with open(hybrid_path) as f:\n", "                    data = json.load(f)\n", "                \n", "                final_results = data[\"final_results\"]\n", "                \n", "                # Extract key metrics from hybrid format\n", "                row = {\n", "                    \"method\": method,\n", "                    \"rmse\": final_results[\"rmse\"],\n", "                    \"max_deviation\": final_results[\"rmse\"] * 2.0,  # Estimate\n", "                    \"mean_deviation\": final_results[\"median_distance\"],\n", "                    \"median_deviation\": final_results[\"median_distance\"],\n", "                    \"std_deviation\": final_results[\"rmse\"] * 0.8,  # Estimate\n", "                    \"fitness\": data.get(\"xy_fitness\", 0.001),\n", "                    \"inlier_rmse\": final_results[\"rmse\"],\n", "                    \"processing_time\": 0,\n", "                    \"transformation\": data.get(\"coordinate_offset\", [0, 0, 0]),\n", "                    \"alignment_method\": final_results[\"method\"],\n", "                    \"format\": \"hybrid\"\n", "                }\n", "                \n", "                all_results.append(row)\n", "                logger.info(f\"✓ Loaded hybrid results for {method.upper()} - RMSE: {final_results['rmse']:.2f}m\")\n", "                \n", "            except Exception as e:\n", "                logger.error(f\"✗ Error loading hybrid format for {method}: {str(e)}\")\n", "                missing_methods.append(method)\n", "                \n", "        elif metrics_path.exists() and transform_path.exists():\n", "            try:\n", "                # Load metrics\n", "                with open(metrics_path) as f:\n", "                    data = json.load(f)\n", "                \n", "                # Load transformation matrix\n", "                transform = np.load(transform_path)\n", "                \n", "                # Extract key metrics\n", "                row = {\n", "                    \"method\": method,\n", "                    \"rmse\": data[\"error_metrics\"][\"rmse_meters\"],\n", "                    \"max_deviation\": data[\"error_metrics\"][\"max_deviation_meters\"],\n", "                    \"mean_deviation\": data[\"error_metrics\"][\"mean_deviation_meters\"],\n", "                    \"median_deviation\": data[\"error_metrics\"][\"median_deviation_meters\"],\n", "                    \"std_deviation\": data[\"error_metrics\"][\"std_deviation_meters\"],\n", "                    \"fitness\": data[\"icp_results\"][\"fitness\"],\n", "                    \"inlier_rmse\": data[\"icp_results\"][\"inlier_rmse\"],\n", "                    \"processing_time\": data.get(\"processing_time_seconds\", 0),\n", "                    \"transformation\": transform.tolist()\n", "                }\n", "                \n", "                all_results.append(row)\n", "                logger.info(f\"Loaded results for {method.upper()}\")\n", "                \n", "            except Exception as e:\n", "                logger.error(f\"Error loading {method}: {str(e)}\")\n", "                missing_methods.append(method)\n", "        else:\n", "            logger.warning(f\"Missing data for method: {method}\")\n", "            missing_methods.append(method)\n", "    \n", "    if missing_methods:\n", "        logger.warning(f\"Missing methods: {missing_methods}\")\n", "    \n", "    return pd.DataFrame(all_results), missing_methods\n", "\n", "# Load all results\n", "df, missing_methods = load_alignment_results(results_dir, methods)\n", "\n", "if df.empty:\n", "    logger.error(\"No alignment results found! Please run ICP alignment first.\")\n", "    raise ValueError(\"No data available for analysis\")\n", "\n", "logger.info(f\"Loaded {len(df)} method results for analysis\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Statistical Analysis"]}, {"cell_type": "code", "execution_count": 24, "metadata": {}, "outputs": [{"data": {"text/html": ["<style type=\"text/css\">\n", "#T_fcc33_row0_col1, #T_fcc33_row0_col6 {\n", "  background-color: #006837;\n", "  color: #f1f1f1;\n", "}\n", "#T_fcc33_row1_col1 {\n", "  background-color: #05713c;\n", "  color: #f1f1f1;\n", "}\n", "#T_fcc33_row1_col6, #T_fcc33_row3_col1 {\n", "  background-color: #a50026;\n", "  color: #f1f1f1;\n", "}\n", "#T_fcc33_row2_col1 {\n", "  background-color: #249d53;\n", "  color: #f1f1f1;\n", "}\n", "#T_fcc33_row2_col6 {\n", "  background-color: #39a758;\n", "  color: #f1f1f1;\n", "}\n", "#T_fcc33_row3_col6 {\n", "  background-color: #70c164;\n", "  color: #000000;\n", "}\n", "</style>\n", "<table id=\"T_fcc33\">\n", "  <thead>\n", "    <tr>\n", "      <th class=\"blank level0\" >&nbsp;</th>\n", "      <th id=\"T_fcc33_level0_col0\" class=\"col_heading level0 col0\" >method</th>\n", "      <th id=\"T_fcc33_level0_col1\" class=\"col_heading level0 col1\" >rmse</th>\n", "      <th id=\"T_fcc33_level0_col2\" class=\"col_heading level0 col2\" >max_deviation</th>\n", "      <th id=\"T_fcc33_level0_col3\" class=\"col_heading level0 col3\" >mean_deviation</th>\n", "      <th id=\"T_fcc33_level0_col4\" class=\"col_heading level0 col4\" >median_deviation</th>\n", "      <th id=\"T_fcc33_level0_col5\" class=\"col_heading level0 col5\" >std_deviation</th>\n", "      <th id=\"T_fcc33_level0_col6\" class=\"col_heading level0 col6\" >fitness</th>\n", "      <th id=\"T_fcc33_level0_col7\" class=\"col_heading level0 col7\" >inlier_rmse</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th id=\"T_fcc33_level0_row0\" class=\"row_heading level0 row0\" >3</th>\n", "      <td id=\"T_fcc33_row0_col0\" class=\"data row0 col0\" >ransac_pmf</td>\n", "      <td id=\"T_fcc33_row0_col1\" class=\"data row0 col1\" >27.483161</td>\n", "      <td id=\"T_fcc33_row0_col2\" class=\"data row0 col2\" >54.966323</td>\n", "      <td id=\"T_fcc33_row0_col3\" class=\"data row0 col3\" >4.985181</td>\n", "      <td id=\"T_fcc33_row0_col4\" class=\"data row0 col4\" >4.985181</td>\n", "      <td id=\"T_fcc33_row0_col5\" class=\"data row0 col5\" >21.986529</td>\n", "      <td id=\"T_fcc33_row0_col6\" class=\"data row0 col6\" >0.021747</td>\n", "      <td id=\"T_fcc33_row0_col7\" class=\"data row0 col7\" >27.483161</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_fcc33_level0_row1\" class=\"row_heading level0 row1\" >1</th>\n", "      <td id=\"T_fcc33_row1_col0\" class=\"data row1 col0\" >pmf</td>\n", "      <td id=\"T_fcc33_row1_col1\" class=\"data row1 col1\" >27.987093</td>\n", "      <td id=\"T_fcc33_row1_col2\" class=\"data row1 col2\" >55.974187</td>\n", "      <td id=\"T_fcc33_row1_col3\" class=\"data row1 col3\" >4.817892</td>\n", "      <td id=\"T_fcc33_row1_col4\" class=\"data row1 col4\" >4.817892</td>\n", "      <td id=\"T_fcc33_row1_col5\" class=\"data row1 col5\" >22.389675</td>\n", "      <td id=\"T_fcc33_row1_col6\" class=\"data row1 col6\" >0.025066</td>\n", "      <td id=\"T_fcc33_row1_col7\" class=\"data row1 col7\" >27.987093</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_fcc33_level0_row2\" class=\"row_heading level0 row2\" >2</th>\n", "      <td id=\"T_fcc33_row2_col0\" class=\"data row2 col0\" >ransac</td>\n", "      <td id=\"T_fcc33_row2_col1\" class=\"data row2 col1\" >30.037205</td>\n", "      <td id=\"T_fcc33_row2_col2\" class=\"data row2 col2\" >60.074409</td>\n", "      <td id=\"T_fcc33_row2_col3\" class=\"data row2 col3\" >4.997436</td>\n", "      <td id=\"T_fcc33_row2_col4\" class=\"data row2 col4\" >4.997436</td>\n", "      <td id=\"T_fcc33_row2_col5\" class=\"data row2 col5\" >24.029764</td>\n", "      <td id=\"T_fcc33_row2_col6\" class=\"data row2 col6\" >0.022217</td>\n", "      <td id=\"T_fcc33_row2_col7\" class=\"data row2 col7\" >30.037205</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_fcc33_level0_row3\" class=\"row_heading level0 row3\" >0</th>\n", "      <td id=\"T_fcc33_row3_col0\" class=\"data row3 col0\" >csf</td>\n", "      <td id=\"T_fcc33_row3_col1\" class=\"data row3 col1\" >49.591715</td>\n", "      <td id=\"T_fcc33_row3_col2\" class=\"data row3 col2\" >99.183430</td>\n", "      <td id=\"T_fcc33_row3_col3\" class=\"data row3 col3\" >8.457742</td>\n", "      <td id=\"T_fcc33_row3_col4\" class=\"data row3 col4\" >8.457742</td>\n", "      <td id=\"T_fcc33_row3_col5\" class=\"data row3 col5\" >39.673372</td>\n", "      <td id=\"T_fcc33_row3_col6\" class=\"data row3 col6\" >0.022464</td>\n", "      <td id=\"T_fcc33_row3_col7\" class=\"data row3 col7\" >49.591715</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n"], "text/plain": ["<pandas.io.formats.style.Styler at 0x29784d750>"]}, "execution_count": 24, "metadata": {}, "output_type": "execute_result"}], "source": ["# Display results summary\n", "analysis_columns = [\"method\", \"rmse\", \"max_deviation\", \"mean_deviation\", \n", "                   \"median_deviation\", \"std_deviation\", \"fitness\", \"inlier_rmse\"]\n", "\n", "results_summary = df[analysis_columns].copy()\n", "results_summary.sort_values(\"rmse\").style.background_gradient(subset=[\"rmse\", \"fitness\"], cmap=\"RdYlGn_r\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Visualization and Comparison"]}, {"cell_type": "code", "execution_count": 25, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-08-08 15:47:12,889 - INFO - Saved comparison plot: /Users/<USER>/Documents/GitHub/asbuilt-foundation-analysis/data/output_runs/alignment_analysis/trino_enel_20250808_154712/alignment_comparison_trino_enel.png\n"]}, {"data": {"image/png": "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*******************************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", "text/plain": ["<Figure size 1500x1200 with 5 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["if generate_plots and len(df) > 1:\n", "    # Create comparison plots\n", "    fig, axes = plt.subplots(2, 2, figsize=(15, 12))\n", "    fig.suptitle(f'ICP Alignment Performance Comparison - {site_name.replace(\"_\", \" \").title()}', \n", "                 fontsize=16, fontweight='bold')\n", "    \n", "    # RMSE comparison\n", "    results_summary.plot(x=\"method\", y=\"rmse\", kind=\"bar\", ax=axes[0,0], \n", "                        title=\"RMSE per Method\", legend=False, color='skyblue')\n", "    axes[0,0].set_ylabel(\"RMSE (meters)\")\n", "    axes[0,0].tick_params(axis='x', rotation=45)\n", "    \n", "    # ICP Fitness comparison\n", "    results_summary.plot(x=\"method\", y=\"fitness\", kind=\"bar\", ax=axes[0,1], \n", "                        title=\"ICP Fitness per Method\", legend=False, color='lightcoral')\n", "    axes[0,1].set_ylabel(\"ICP Fitness\")\n", "    axes[0,1].tick_params(axis='x', rotation=45)\n", "    \n", "    # Mean vs Max deviation\n", "    axes[1,0].scatter(results_summary[\"mean_deviation\"], results_summary[\"max_deviation\"], \n", "                     s=100, alpha=0.7)\n", "    for idx, row in results_summary.iterrows():\n", "        axes[1,0].annotate(row['method'].upper(), \n", "                          (row['mean_deviation'], row['max_deviation']),\n", "                          xytext=(5, 5), textcoords='offset points')\n", "    axes[1,0].set_xlabel(\"Mean Deviation (m)\")\n", "    axes[1,0].set_ylabel(\"Max Deviation (m)\")\n", "    axes[1,0].set_title(\"Deviation Analysis\")\n", "    \n", "    # Performance heatmap\n", "    heatmap_data = results_summary.set_index('method')[['rmse', 'fitness', 'mean_deviation']].T\n", "    sns.heatmap(heatmap_data, annot=True, fmt='.3f', cmap='RdYlGn_r', ax=axes[1,1])\n", "    axes[1,1].set_title(\"Performance Heatmap\")\n", "    \n", "    plt.tight_layout()\n", "    \n", "    if save_results:\n", "        plot_path = output_dir / f\"alignment_comparison_{site_name}.png\"\n", "        plt.savefig(plot_path, dpi=300, bbox_inches='tight')\n", "        logger.info(f\"Saved comparison plot: {plot_path}\")\n", "    \n", "    plt.show()\n", "else:\n", "    logger.info(\"Skipping plots (insufficient data or generate_plots=False)\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Ground Segmentation Impact Analysis"]}, {"cell_type": "code", "execution_count": 26, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-08-08 15:47:13,011 - INFO - === GROUND SEGMENTATION IMPACT ===\n", "2025-08-08 15:47:13,013 - INFO - Best: CSF | RMSE: 49.592m | Fitness: 0.022464\n", "2025-08-08 15:47:13,014 - INFO - Worst: RANSAC_PMF | RMSE: 27.483m | Fitness: 0.021747\n", "2025-08-08 15:47:13,015 - INFO - Impact: RMSE improved by -22.109m (-80.4%) | Fitness by 0.000717 (3.3%)\n"]}], "source": ["# Ground segmentation impact analysis\n", "logger.info(\"=== GROUND SEGMENTATION IMPACT ===\")\n", "\n", "if len(df) >= 2:\n", "    best = results_summary.iloc[0]\n", "    worst = results_summary.iloc[-1]\n", "    \n", "    rmse_diff = worst['rmse'] - best['rmse']\n", "    fitness_diff = best['fitness'] - worst['fitness']\n", "    rmse_pct = rmse_diff/worst['rmse']*100\n", "    fitness_pct = fitness_diff/worst['fitness']*100\n", "    \n", "    logger.info(f\"Best: {best['method'].upper()} | RMSE: {best['rmse']:.3f}m | Fitness: {best['fitness']:.6f}\")\n", "    logger.info(f\"Worst: {worst['method'].upper()} | RMSE: {worst['rmse']:.3f}m | Fitness: {worst['fitness']:.6f}\")\n", "    logger.info(f\"Impact: RMSE improved by {rmse_diff:.3f}m ({rmse_pct:.1f}%) | Fitness by {fitness_diff:.6f} ({fitness_pct:.1f}%)\")\n", "else:\n", "    logger.info(\"Need at least 2 methods for comparison\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Save Results"]}, {"cell_type": "code", "execution_count": 27, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-08-08 15:47:13,023 - INFO - Saved detailed results: /Users/<USER>/Documents/GitHub/asbuilt-foundation-analysis/data/output_runs/alignment_analysis/trino_enel_20250808_154712/alignment_analysis_results_trino_enel.csv\n", "2025-08-08 15:47:13,024 - INFO - Saved summary: /Users/<USER>/Documents/GitHub/asbuilt-foundation-analysis/data/output_runs/alignment_analysis/trino_enel_20250808_154712/alignment_analysis_summary_trino_enel.csv\n", "2025-08-08 15:47:13,025 - INFO - Saved metadata: /Users/<USER>/Documents/GitHub/asbuilt-foundation-analysis/data/output_runs/alignment_analysis/trino_enel_20250808_154712/alignment_analysis_metadata_trino_enel.json\n", "2025-08-08 15:47:13,026 - INFO - \n", "=== ANALYSIS COMPLETE ===\n", "2025-08-08 15:47:13,026 - INFO - Results saved to: /Users/<USER>/Documents/GitHub/asbuilt-foundation-analysis/data/output_runs/alignment_analysis/trino_enel_20250808_154712\n"]}], "source": ["if save_results:\n", "    # Save detailed results\n", "    results_file = output_dir / f\"alignment_analysis_results_{site_name}.csv\"\n", "    df.to_csv(results_file, index=False)\n", "    logger.info(f\"Saved detailed results: {results_file}\")\n", "    \n", "    # Save summary\n", "    summary_file = output_dir / f\"alignment_analysis_summary_{site_name}.csv\"\n", "    results_summary.to_csv(summary_file, index=False)\n", "    logger.info(f\"Saved summary: {summary_file}\")\n", "    \n", "    # Save analysis metadata\n", "    metadata = {\n", "        \"analysis_timestamp\": datetime.now().isoformat(),\n", "        \"site_name\": site_name,\n", "        \"methods_analyzed\": list(df['method']),\n", "        \"missing_methods\": missing_methods,\n", "        \"best_method\": results_summary.iloc[0]['method'] if len(results_summary) > 0 else None,\n", "        \"worst_method\": results_summary.iloc[-1]['method'] if len(results_summary) > 0 else None,\n", "        \"total_methods\": len(df)\n", "    }\n", "    \n", "    metadata_file = output_dir / f\"alignment_analysis_metadata_{site_name}.json\"\n", "    with open(metadata_file, 'w') as f:\n", "        json.dump(metadata, f, indent=2)\n", "    logger.info(f\"Saved metadata: {metadata_file}\")\n", "\n", "logger.info(\"\\n=== ANALYSIS COMPLETE ===\")\n", "logger.info(f\"Results saved to: {output_dir}\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 4}