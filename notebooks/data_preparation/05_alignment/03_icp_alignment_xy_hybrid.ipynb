{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# ICP Point Cloud Alignment\n", "\n", "Streamlined ICP alignment between drone scan point clouds and IFC point clouds.\n", "\n", "**Steps:**\n", "1. Load Data (Drone scan & IFC point cloud)\n", "2. Preprocess (measure and do Z-shift correction)\n", "3. <PERSON> (Coarse-to-fine alignment)\n", "4. Visualize Results (Before/after alignment)\n", "5. <PERSON><PERSON><PERSON> (RMSE, max deviation)\n", "\n", "**Key Principle:** Drone point cloud represents ground truth elevation (Z), IFC model is aligned to it.\n", "\n", "**Author**: <PERSON><PERSON><PERSON>  \n", "**Date**: July 2025"]}, {"cell_type": "code", "execution_count": 2, "metadata": {"execution": {"iopub.execute_input": "2025-07-20T13:25:22.316392Z", "iopub.status.busy": "2025-07-20T13:25:22.316068Z", "iopub.status.idle": "2025-07-20T13:25:22.325018Z", "shell.execute_reply": "2025-07-20T13:25:22.324540Z"}, "tags": ["parameters"]}, "outputs": [], "source": ["# Parameters (Papermill)\n", "ground_method = \"ransac_pmf\"  # Ground segmentation method: csf, pmf, ransac\n", "site_name = \"trino_enel\"\n", "\n", "icp_max_iterations = 50\n", "icp_tolerance = 1e-6\n", "voxel_size = 0.02  # For downsampling if needed\n", "\n", "# Hybrid parameters\n", "xy_refinement_threshold = 10.0  # Only accept small XY refinements\n", "max_correspondence_distance = 2.0  # Conservative for 2D ICP\n", "\n", "STANDARD_SAMPLE_SIZE = 50000\n", "RANDOM_SEED = 42              \n", "\n", "output_dir = \"../../../data/output_runs/icp_alignment\"\n", "enable_visualization = True\n", "save_results = True"]}, {"cell_type": "code", "execution_count": 3, "metadata": {"execution": {"iopub.execute_input": "2025-07-20T13:25:22.327952Z", "iopub.status.busy": "2025-07-20T13:25:22.327597Z", "iopub.status.idle": "2025-07-20T13:25:23.796539Z", "shell.execute_reply": "2025-07-20T13:25:23.796259Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["=== HYBRID XY-COORDINATE ALIGNMENT ===\n", "Strategy: Coordinate-only Z + Selective XY refinement\n", "Site: trino_enel\n", "Output: ../../../data/output_runs/icp_alignment/ransac_pmf\n", "Timestamp: 2025-07-20 18:58:53\n"]}], "source": ["# Imports\n", "import numpy as np\n", "import open3d as o3d\n", "import matplotlib.pyplot as plt\n", "import pandas as pd\n", "import time\n", "import json\n", "from pathlib import Path\n", "from datetime import datetime\n", "from scipy.spatial import cKDTree\n", "\n", "# Setup\n", "np.random.seed(42)\n", "output_path = Path(output_dir) / ground_method\n", "output_path.mkdir(parents=True, exist_ok=True)\n", "\n", "print(\"=== HYBRID XY-COORDINATE ALIGNMENT ===\")\n", "print(f\"Strategy: Coordinate-only Z + Selective XY refinement\")\n", "print(f\"Site: {site_name}\")\n", "print(f\"Output: {output_path}\")\n", "print(f\"Timestamp: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1. Load Data (Drone scan & IFC point cloud)"]}, {"cell_type": "code", "execution_count": 4, "metadata": {"execution": {"iopub.execute_input": "2025-07-20T13:25:23.814847Z", "iopub.status.busy": "2025-07-20T13:25:23.814593Z", "iopub.status.idle": "2025-07-20T13:25:23.816726Z", "shell.execute_reply": "2025-07-20T13:25:23.816478Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Loading data with corrected approach...\n", "Drone file: ../../../data/processed/trino_enel/ground_segmentation/ransac_pmf/trino_enel_nonground.ply\n", "IFC metadata file: ../../../data/processed/trino_enel/ifc_metadata/GRE.EEC.S.00.IT.P.14353.00.265_enhanced_metadata.csv\n", "IFC point cloud file: ../../../data/processed/trino_enel/ifc_pointclouds/GRE.EEC.S.00.IT.P.14353.00.265_data_driven.ply (for comparison)\n"]}], "source": ["# Define file paths\n", "drone_file = f\"../../../data/processed/{site_name}/ground_segmentation/{ground_method}/{site_name}_nonground.ply\"\n", "ifc_metadata_file = f\"../../../data/processed/{site_name}/ifc_metadata/GRE.EEC.S.00.IT.P.14353.00.265_enhanced_metadata.csv\"\n", "ifc_pointcloud_file = f\"../../../data/processed/{site_name}/ifc_pointclouds/GRE.EEC.S.00.IT.P.14353.00.265_data_driven.ply\"\n", "\n", "print(\"Loading data with corrected approach...\")\n", "print(f\"Drone file: {drone_file}\")\n", "print(f\"IFC metadata file: {ifc_metadata_file}\")\n", "print(f\"IFC point cloud file: {ifc_pointcloud_file} (for comparison)\")\n", "\n"]}, {"cell_type": "code", "execution_count": 5, "metadata": {"execution": {"iopub.execute_input": "2025-07-20T13:25:23.817904Z", "iopub.status.busy": "2025-07-20T13:25:23.817818Z", "iopub.status.idle": "2025-07-20T13:25:24.186158Z", "shell.execute_reply": "2025-07-20T13:25:24.185858Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Loaded drone scan: 517,002 points\n", "Loaded IFC model: 5,480,340 points\n", "Coordinate Analysis:\n", "  Full 3D offset: [-44.73, 1.48, 155.44]\n", "  XY component: 44.75m\n", "  Z component: 155.44m\n"]}], "source": ["def load_and_analyze_data():\n", "    # Load point clouds\n", "    drone_points = np.asarray(o3d.io.read_point_cloud(drone_file).points)\n", "    ifc_points = np.asarray(o3d.io.read_point_cloud(ifc_pointcloud_file).points)\n", "    \n", "    print(f\"Loaded drone scan: {len(drone_points):,} points\")\n", "    print(f\"Loaded IFC model: {len(ifc_points):,} points\")\n", "    \n", "    # Compute coordinate-only offset\n", "    drone_center = np.mean(drone_points, axis=0)\n", "    ifc_center = np.mean(ifc_points, axis=0)\n", "    full_offset = ifc_center - drone_center\n", "    \n", "    print(f\"Coordinate Analysis:\")\n", "    print(f\"  Full 3D offset: [{full_offset[0]:.2f}, {full_offset[1]:.2f}, {full_offset[2]:.2f}]\")\n", "    print(f\"  XY component: {np.linalg.norm(full_offset[:2]):.2f}m\")\n", "    print(f\"  Z component: {full_offset[2]:.2f}m\")\n", "    \n", "    return drone_points, ifc_points, full_offset\n", "\n", "drone_points, ifc_points, coordinate_offset = load_and_analyze_data()\n"]}, {"cell_type": "code", "execution_count": 6, "metadata": {"execution": {"iopub.execute_input": "2025-07-20T13:25:24.187785Z", "iopub.status.busy": "2025-07-20T13:25:24.187702Z", "iopub.status.idle": "2025-07-20T13:25:24.206611Z", "shell.execute_reply": "2025-07-20T13:25:24.206340Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "INITIAL POINT CLOUD STATISTICS\n", "\n", "Drone scan (ground truth):\n", "  Points: 517,002\n", "  X range: [435220.82, 436795.41]\n", "  Y range: [5010813.78, 5012552.58]\n", "  Z range: [-0.71, 28.19]\n", "\n", "IFC model (to be aligned):\n", "  Points: 5,480,340\n", "  X range: [435267.17, 436719.98]\n", "  Y range: [5010900.69, 5012462.43]\n", "  Z range: [152.85, 161.66]\n"]}], "source": ["# Display initial statistics\n", "print(\"\\nINITIAL POINT CLOUD STATISTICS\")\n", "print(\"\\nDrone scan (ground truth):\")\n", "print(f\"  Points: {drone_points.shape[0]:,}\")\n", "print(f\"  X range: [{drone_points[:, 0].min():.2f}, {drone_points[:, 0].max():.2f}]\")\n", "print(f\"  Y range: [{drone_points[:, 1].min():.2f}, {drone_points[:, 1].max():.2f}]\")\n", "print(f\"  Z range: [{drone_points[:, 2].min():.2f}, {drone_points[:, 2].max():.2f}]\")\n", "\n", "print(\"\\nIFC model (to be aligned):\")\n", "print(f\"  Points: {ifc_points.shape[0]:,}\")\n", "print(f\"  X range: [{ifc_points[:, 0].min():.2f}, {ifc_points[:, 0].max():.2f}]\")\n", "print(f\"  Y range: [{ifc_points[:, 1].min():.2f}, {ifc_points[:, 1].max():.2f}]\")\n", "print(f\"  Z range: [{ifc_points[:, 2].min():.2f}, {ifc_points[:, 2].max():.2f}]\")"]}, {"cell_type": "code", "execution_count": 7, "metadata": {"execution": {"iopub.execute_input": "2025-07-20T13:25:24.208078Z", "iopub.status.busy": "2025-07-20T13:25:24.207987Z", "iopub.status.idle": "2025-07-20T13:25:24.209846Z", "shell.execute_reply": "2025-07-20T13:25:24.209601Z"}}, "outputs": [], "source": ["# def offset_based_normalization(points):\n", "#     \"\"\"\n", "#     Subtracts the minimum Z-value from a point cloud to bring it to a Z=0 base frame.\n", "#     Returns the normalized points and the offset applied.\n", "#     \"\"\"\n", "#     if points.shape[0] == 0:\n", "#         print(\"Warning: Empty point cloud provided for offset-based normalization\")\n", "#         return points, 0.0\n", "    \n", "#     # Find minimum Z-value\n", "#     min_z = np.min(points[:, 2])\n", "    \n", "#     # Subtract min <PERSON> from all points\n", "#     normalized_points = points.copy()\n", "#     normalized_points[:, 2] -= min_z\n", "    \n", "#     return normalized_points, min_z\n", "\n", "# drone_points, drone_z_offset = offset_based_normalization(drone_points)\n", "# ifc_points, ifc_z_offset = offset_based_normalization(ifc_points)\n", "\n", "# print(f\"Drone Z-offset applied: {drone_z_offset:.4f}m\")\n", "# print(f\"IFC Z-offset applied: {ifc_z_offset:.4f}m\")"]}, {"cell_type": "code", "execution_count": 8, "metadata": {"execution": {"iopub.execute_input": "2025-07-20T13:25:24.210956Z", "iopub.status.busy": "2025-07-20T13:25:24.210891Z", "iopub.status.idle": "2025-07-20T13:25:24.212612Z", "shell.execute_reply": "2025-07-20T13:25:24.212396Z"}}, "outputs": [], "source": ["# # Display initial statistics\n", "# print(\"\\nDrone scan (ground truth):\")\n", "# print(f\"  Points: {drone_points.shape[0]:,}\")\n", "# print(f\"  X range: [{drone_points[:, 0].min():.2f}, {drone_points[:, 0].max():.2f}]\")\n", "# print(f\"  Y range: [{drone_points[:, 1].min():.2f}, {drone_points[:, 1].max():.2f}]\")\n", "# print(f\"  Z range: [{drone_points[:, 2].min():.2f}, {drone_points[:, 2].max():.2f}]\")\n", "\n", "# print(\"\\nIFC model (to be aligned):\")\n", "# print(f\"  Points: {ifc_points.shape[0]:,}\")\n", "# print(f\"  X range: [{ifc_points[:, 0].min():.2f}, {ifc_points[:, 0].max():.2f}]\")\n", "# print(f\"  Y range: [{ifc_points[:, 1].min():.2f}, {ifc_points[:, 1].max():.2f}]\")\n", "# print(f\"  Z range: [{ifc_points[:, 2].min():.2f}, {ifc_points[:, 2].max():.2f}]\")"]}, {"cell_type": "code", "execution_count": 9, "metadata": {"execution": {"iopub.execute_input": "2025-07-20T13:25:24.213809Z", "iopub.status.busy": "2025-07-20T13:25:24.213730Z", "iopub.status.idle": "2025-07-20T13:25:24.218182Z", "shell.execute_reply": "2025-07-20T13:25:24.217952Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "=== CRS VALIDATION ===\n", "Drone CRS: Not Found\n", "IFC CRS:   32632\n", "Warning: CRS metadata missing. Proceeding with assumption of matching coordinate systems.\n", "\n", "Ready for alignment with 517,002 drone + 5,480,340 IFC points\n"]}], "source": ["# Validate CRS compatibility\n", "def load_crs_metadata(pcd_file):\n", "    \"\"\"Load CRS metadata with multiple fallback strategies\"\"\"\n", "    stem = pcd_file.stem\n", "    base_stem = stem.split(\"_data_driven\")[0] if \"_data_driven\" in stem else stem\n", "    \n", "    candidates = [\n", "        pcd_file.with_name(f\"{stem}_crs.json\"),\n", "        pcd_file.with_name(f\"{base_stem}_crs.json\"),\n", "        pcd_file.parent / \"crs_metadata.json\",\n", "        pcd_file.parent / f\"{base_stem}_crs_metadata.json\"\n", "    ]\n", "    \n", "    for crs_file in candidates:\n", "        if crs_file.exists():\n", "            try:\n", "                with open(crs_file) as f:\n", "                    return json.load(f)\n", "            except json.JSONDecodeError:\n", "                print(f\"Warning: Failed to parse CRS metadata in {crs_file}\")\n", "    return None\n", "print(\"\\n=== CRS VALIDATION ===\")\n", "drone_crs = load_crs_metadata(Path(drone_file))\n", "ifc_crs = load_crs_metadata(Path(ifc_pointcloud_file))\n", "\n", "drone_epsg = drone_crs.get('epsg') if drone_crs else None\n", "ifc_epsg = ifc_crs.get('epsg') if ifc_crs else None\n", "\n", "print(f\"Drone CRS: {drone_epsg or 'Not Found'}\")\n", "print(f\"IFC CRS:   {ifc_epsg or 'Not Found'}\")\n", "\n", "if drone_epsg and ifc_epsg:\n", "    if drone_epsg != ifc_epsg:\n", "        raise ValueError(f\"CRS Mismatch: Drone EPSG {drone_epsg} ≠ IFC EPSG {ifc_epsg}\")\n", "    print(\"CRS match confirmed\")\n", "else:\n", "    print(\"Warning: CRS metadata missing. Proceeding with assumption of matching coordinate systems.\")\n", "\n", "print(f\"\\nReady for alignment with {len(drone_points):,} drone + {len(ifc_points):,} IFC points\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2. Apply Coordinate-Only Alignment"]}, {"cell_type": "code", "execution_count": 10, "metadata": {"execution": {"iopub.execute_input": "2025-07-20T13:25:24.219398Z", "iopub.status.busy": "2025-07-20T13:25:24.219310Z", "iopub.status.idle": "2025-07-20T13:25:24.375260Z", "shell.execute_reply": "2025-07-20T13:25:24.374994Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["COORDINATE ALIGNMENT RESULTS:\n", "  Applied offset: [-44.73, 1.48, 155.44]m\n", "  Residual centroid error: 0.000000m\n", "  Coordinate ranges after alignment:\n", "    Drone: X=1574.6m, Y=1738.8m, Z=28.9m\n", "    IFC:   X=1452.8m, Y=1561.7m, Z=8.8m\n", "  WARNING: Coordinate ranges very different - check data compatibility\n", "  Status: Coordinate alignment complete\n"]}], "source": ["def apply_coordinate_alignment(drone_pts, ifc_pts):\n", "    \"\"\"\n", "    IMPROVED: Apply coordinate alignment with validation and diagnostics\n", "    \"\"\"\n", "    # Calculate centroids\n", "    drone_center = np.mean(drone_pts, axis=0)\n", "    ifc_center = np.mean(ifc_pts, axis=0)\n", "    offset = ifc_center - drone_center\n", "    \n", "    # Apply offset\n", "    drone_aligned = drone_pts + offset\n", "    \n", "    # VALIDATION: Verify alignment worked\n", "    aligned_center = np.mean(drone_aligned, axis=0)\n", "    residual_error = np.linalg.norm(aligned_center - ifc_center)\n", "    \n", "    # DIAGNOSTICS: Check coordinate ranges for sanity\n", "    drone_range = np.ptp(drone_aligned, axis=0)  # range per axis\n", "    ifc_range = np.ptp(ifc_pts, axis=0)\n", "    \n", "    print(f\"COORDINATE ALIGNMENT RESULTS:\")\n", "    print(f\"  Applied offset: [{offset[0]:.2f}, {offset[1]:.2f}, {offset[2]:.2f}]m\")\n", "    print(f\"  Residual centroid error: {residual_error:.6f}m\")\n", "    print(f\"  Coordinate ranges after alignment:\")\n", "    print(f\"    Drone: X={drone_range[0]:.1f}m, Y={drone_range[1]:.1f}m, Z={drone_range[2]:.1f}m\")\n", "    print(f\"    IFC:   X={ifc_range[0]:.1f}m, Y={ifc_range[1]:.1f}m, Z={ifc_range[2]:.1f}m\")\n", "    \n", "    # VALIDATION: Check if alignment is reasonable\n", "    if residual_error > 0.001:  # Should be near-zero for centroid alignment\n", "        print(f\"  WARNING: Residual error unexpectedly high!\")\n", "    \n", "    if np.any(np.abs(drone_range - ifc_range) > 100):  # Ranges shouldn't be wildly different\n", "        print(f\"  WARNING: Coordinate ranges very different - check data compatibility\")\n", "    \n", "    print(f\"  Status: Coordinate alignment complete\")\n", "    return drone_aligned, offset\n", "\n", "# def apply_coordinate_alignment(drone_pts, ifc_pts):\n", "#     \"\"\"\n", "#     Aligns drone and IFC point clouds by matching XY centroids, preserving Z=0 from normalization.\n", "#     Returns the aligned drone points and the XY offset applied.\n", "#     \"\"\"\n", "#     # Calculate XY centroids\n", "#     drone_center = np.mean(drone_pts[:, :2], axis=0)\n", "#     ifc_center = np.mean(ifc_pts[:, :2], axis=0)\n", "    \n", "#     # Compute XY offset\n", "#     xy_offset = ifc_center - drone_center\n", "    \n", "#     # Apply XY offset, preserve Z\n", "#     drone_aligned = drone_pts.copy()\n", "#     drone_aligned[:, :2] += xy_offset\n", "    \n", "#     # Verify alignment\n", "#     aligned_center = np.mean(drone_aligned[:, :2], axis=0)\n", "#     residual_error = np.linalg.norm(aligned_center - ifc_center)\n", "    \n", "#     print(f\"Coordinate Alignment Results:\")\n", "#     print(f\"  XY offset applied: [{xy_offset[0]:.3f}, {xy_offset[1]:.3f}]m\")\n", "#     print(f\"  Residual XY centroid error: {residual_error:.6f}m\")\n", "#     print(f\"  Status: XY centroid alignment achieved, Z preserved at 0\")\n", "    \n", "#     return drone_aligned, xy_offset\n", "\n", "drone_coordinate_aligned, coordinate_offset = apply_coordinate_alignment(drone_points, ifc_points)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 3. Prepare for XY-Only ICP Test"]}, {"cell_type": "code", "execution_count": 11, "metadata": {"execution": {"iopub.execute_input": "2025-07-20T13:25:24.376640Z", "iopub.status.busy": "2025-07-20T13:25:24.376552Z", "iopub.status.idle": "2025-07-20T13:25:25.197957Z", "shell.execute_reply": "2025-07-20T13:25:25.197529Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["XY-only point clouds prepared:\n", "  Source (Drone XY): 499,282 points\n", "  Target (IFC XY): 25,468 points\n"]}], "source": ["def prepare_xy_icp_test(drone_aligned, ifc_pts):\n", "    # Extract XY coordinates only (set Z=0 for 2D ICP)\n", "    drone_xy = drone_aligned[:, :2]\n", "    ifc_xy = ifc_pts[:, :2]\n", "    \n", "    # Create 2D point clouds (Z=0)\n", "    drone_xy_3d = np.column_stack([drone_xy, np.zeros(len(drone_xy))])\n", "    ifc_xy_3d = np.column_stack([ifc_xy, np.zeros(len(ifc_xy))])\n", "    \n", "    # Create Open3D point clouds\n", "    source_pcd = o3d.geometry.PointCloud()\n", "    source_pcd.points = o3d.utility.Vector3dVector(drone_xy_3d)\n", "    \n", "    target_pcd = o3d.geometry.PointCloud()\n", "    target_pcd.points = o3d.utility.Vector3dVector(ifc_xy_3d)\n", "    \n", "    # Downsample for performance\n", "    source_pcd = source_pcd.voxel_down_sample(0.15)\n", "    target_pcd = target_pcd.voxel_down_sample(0.15)\n", "    \n", "    # Remove outliers\n", "    source_pcd, _ = source_pcd.remove_statistical_outlier(nb_neighbors=20, std_ratio=2.0)\n", "    target_pcd, _ = target_pcd.remove_statistical_outlier(nb_neighbors=20, std_ratio=2.0)\n", "    \n", "    print(f\"XY-only point clouds prepared:\")\n", "    print(f\"  Source (Drone XY): {len(source_pcd.points):,} points\")\n", "    print(f\"  Target (IFC XY): {len(target_pcd.points):,} points\")\n", "    \n", "    return source_pcd, target_pcd\n", "\n", "# CORRECTED APPROACH: Use coordinate-aligned data for ICP local refinement\\n\n", "# Step 1: Coordinate alignment brings datasets to same coordinate system\\n\n", "# Step 2: ICP refines local misalignments within that coordinate system\\n\n", "source_xy_pcd, target_xy_pcd = prepare_xy_icp_test(drone_coordinate_aligned, ifc_points)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 4. Calculate XY Offset After Downsampling\n"]}, {"cell_type": "code", "execution_count": 12, "metadata": {"execution": {"iopub.execute_input": "2025-07-20T13:25:25.199998Z", "iopub.status.busy": "2025-07-20T13:25:25.199894Z", "iopub.status.idle": "2025-07-20T13:25:25.206454Z", "shell.execute_reply": "2025-07-20T13:25:25.206138Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["XY Offset Analysis:\n", "  Source center: [435984.698, 5011749.791]\n", "  Target center: [435992.322, 5011770.805]\n", "  XY offset: [7.625, 21.015]\n", "  XY offset magnitude: 22.355m\n"]}], "source": ["def calculate_xy_offset(source_pcd, target_pcd):\n", "    source_center = np.mean(np.asarray(source_pcd.points), axis=0)\n", "    target_center = np.mean(np.asarray(target_pcd.points), axis=0)\n", "    \n", "    xy_offset = target_center[:2] - source_center[:2]  # Only XY components\n", "    xy_offset_magnitude = np.linalg.norm(xy_offset)\n", "    \n", "    print(f\"XY Offset Analysis:\")\n", "    print(f\"  Source center: [{source_center[0]:.3f}, {source_center[1]:.3f}]\")\n", "    print(f\"  Target center: [{target_center[0]:.3f}, {target_center[1]:.3f}]\")\n", "    print(f\"  XY offset: [{xy_offset[0]:.3f}, {xy_offset[1]:.3f}]\")\n", "    print(f\"  XY offset magnitude: {xy_offset_magnitude:.3f}m\")\n", "    \n", "    return xy_offset, xy_offset_magnitude\n", "\n", "xy_offset, xy_offset_magnitude = calculate_xy_offset(source_xy_pcd, target_xy_pcd)\n"]}, {"cell_type": "code", "execution_count": 13, "metadata": {"execution": {"iopub.execute_input": "2025-07-20T13:25:25.208204Z", "iopub.status.busy": "2025-07-20T13:25:25.208049Z", "iopub.status.idle": "2025-07-20T13:25:25.624017Z", "shell.execute_reply": "2025-07-20T13:25:25.623743Z"}}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 1500x500 with 3 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# See what you're trying to align:\n", "import matplotlib.pyplot as plt\n", "from mpl_toolkits.mplot3d import Axes3D\n", "import numpy as np\n", "\n", "# Sample points for visualization\n", "drone_sample = np.asarray(source_xy_pcd.points)[::100]  # Every 100th point\n", "ifc_sample = np.asarray(target_xy_pcd.points)[::50]     # Every 50th point\n", "\n", "fig = plt.figure(figsize=(15, 5))\n", "\n", "# Side by side view\n", "ax1 = fig.add_subplot(131, projection='3d')\n", "ax1.scatter(drone_sample[:, 0], drone_sample[:, 1], drone_sample[:, 2], \n", "           c='red', s=1, alpha=0.6, label='Drone')\n", "ax1.set_title('Drone Points')\n", "\n", "ax2 = fig.add_subplot(132, projection='3d')\n", "ax2.scatter(ifc_sample[:, 0], ifc_sample[:, 1], ifc_sample[:, 2], \n", "           c='blue', s=1, alpha=0.6, label='IFC')\n", "ax2.set_title('IFC Points')\n", "\n", "# Overlay view\n", "ax3 = fig.add_subplot(133, projection='3d')\n", "ax3.scatter(drone_sample[:, 0], drone_sample[:, 1], drone_sample[:, 2], \n", "           c='red', s=1, alpha=0.4, label='Drone')\n", "ax3.scatter(ifc_sample[:, 0], ifc_sample[:, 1], ifc_sample[:, 2], \n", "           c='blue', s=1, alpha=0.4, label='IFC')\n", "ax3.set_title('Overlay')\n", "ax3.legend()\n", "\n", "plt.tight_layout()\n", "plt.show()\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 5. Test XY-Only ICP Refinement\n"]}, {"cell_type": "code", "execution_count": 14, "metadata": {"execution": {"iopub.execute_input": "2025-07-20T13:25:25.626272Z", "iopub.status.busy": "2025-07-20T13:25:25.626161Z", "iopub.status.idle": "2025-07-20T13:25:26.730115Z", "shell.execute_reply": "2025-07-20T13:25:26.729690Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["=== TESTING XY-ONLY ICP REFINEMENT ===\n", "ICP Parameters:\n", "  Max correspondence distance: 1.00m\n", "  Max iterations: 30 (reduced for stability)\n", "\n", "ICP Results:\n", "  Fitness: 0.021747\n", "  RMSE: 0.697495\n", "  XY refinement: [-1296.039, 112.982]\n", "  Refinement magnitude: 1300.954m\n", "\n", "REJECTING ICP REFINEMENT:\n", "   - Large refinement (1300.954m > 10.0m)\n", "   - Poor fitness (0.021747 < 0.1)\n", "   - Extreme single-axis movement\n", "   DECISION: Using coordinate-only alignment (more reliable)\n"]}], "source": ["def test_xy_icp_refinement(source_pcd, target_pcd):\n", "    print(\"=== TESTING XY-ONLY ICP REFINEMENT ===\")\n", "    \n", "    # IMPROVED ICP PARAMETERS for better stability\n", "    # Use smaller correspondence distance for more conservative matching\n", "    conservative_distance = min(max_correspondence_distance, 1.0)  # Cap at 1m\n", "    \n", "    print(f\"ICP Parameters:\")\n", "    print(f\"  Max correspondence distance: {conservative_distance:.2f}m\")\n", "    print(f\"  Max iterations: 30 (reduced for stability)\")\n", "    \n", "    # Run ICP with conservative parameters\n", "    icp_result = o3d.pipelines.registration.registration_icp(\n", "        source_pcd, target_pcd,\n", "        max_correspondence_distance=conservative_distance,\n", "        init=np.eye(4),  # Start from coordinate-aligned position\n", "        estimation_method=o3d.pipelines.registration.TransformationEstimationPointToPoint(),\n", "        criteria=o3d.pipelines.registration.ICPConvergenceCriteria(\n", "            max_iteration=30,  # Reduced iterations\n", "            relative_fitness=1e-6,\n", "            relative_rmse=1e-6\n", "        )\n", "    )\n", "    \n", "    # Extract XY refinement (ignore any Z movement)\n", "    xy_refinement = icp_result.transformation[:2, 3]\n", "    xy_refinement_magnitude = np.linalg.norm(xy_refinement)\n", "    \n", "    print(f\"\\nICP Results:\")\n", "    print(f\"  Fitness: {icp_result.fitness:.6f}\")\n", "    print(f\"  RMSE: {icp_result.inlier_rmse:.6f}\")\n", "    print(f\"  XY refinement: [{xy_refinement[0]:.3f}, {xy_refinement[1]:.3f}]\")\n", "    print(f\"  Refinement magnitude: {xy_refinement_magnitude:.3f}m\")\n", "    \n", "    # DRIFT REJECTION: Multiple validation checks\n", "    rejection_reasons = []\n", "    \n", "    # Check 1: Refinement magnitude\n", "    if xy_refinement_magnitude > xy_refinement_threshold:\n", "        rejection_reasons.append(f\"Large refinement ({xy_refinement_magnitude:.3f}m > {xy_refinement_threshold}m)\")\n", "    \n", "    # Check 2: Poor fitness (suggests bad alignment)\n", "    if icp_result.fitness < 0.1:\n", "        rejection_reasons.append(f\"Poor fitness ({icp_result.fitness:.6f} < 0.1)\")\n", "    \n", "    # Check 3: High RMSE (suggests noisy alignment)\n", "    if icp_result.inlier_rmse > 5.0:\n", "        rejection_reasons.append(f\"High RMSE ({icp_result.inlier_rmse:.3f}m > 5.0m)\")\n", "    \n", "    # Check 4: Extreme individual axis movements\n", "    if np.any(np.abs(xy_refinement) > xy_refinement_threshold * 0.8):\n", "        rejection_reasons.append(f\"Extreme single-axis movement\")\n", "    \n", "    # DECISION LOGIC\n", "    if rejection_reasons:\n", "        print(f\"\\nREJECTING ICP REFINEMENT:\")\n", "        for reason in rejection_reasons:\n", "            print(f\"   - {reason}\")\n", "        print(f\"   DECISION: Using coordinate-only alignment (more reliable)\")\n", "        return None, xy_refinement_magnitude, icp_result.fitness  # Return None to signal rejection\n", "    else:\n", "        print(f\"\\nACCEPTING ICP REFINEMENT:\")\n", "        print(f\"   - Small, reasonable adjustment\")\n", "        print(f\"   - Good fitness and RMSE\")\n", "        return xy_refinement, xy_refinement_magnitude, icp_result.fitness\n", "\n", "xy_refinement, xy_refinement_mag, xy_fitness = test_xy_icp_refinement(source_xy_pcd, target_xy_pcd)\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 6. Hybrid Decision Logic\n"]}, {"cell_type": "code", "execution_count": 15, "metadata": {"execution": {"iopub.execute_input": "2025-07-20T13:25:26.732135Z", "iopub.status.busy": "2025-07-20T13:25:26.732026Z", "iopub.status.idle": "2025-07-20T13:25:26.736037Z", "shell.execute_reply": "2025-07-20T13:25:26.735615Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["=== HYBRID ALIGNMENT DECISION ===\n", "ICP refinement was REJECTED by validation checks\n", "Using coordinate-only alignment (more reliable)\n"]}], "source": ["def apply_hybrid_alignment(drone_aligned, xy_refinement, xy_magnitude):\n", "    print(\"=== HYBRID ALIGNMENT DECISION ===\")\n", "    \n", "    # Handle case where ICP was rejected (xy_refinement is None)\n", "    if xy_refinement is None:\n", "        print(f\"ICP refinement was REJECTED by validation checks\")\n", "        print(f\"Using coordinate-only alignment (more reliable)\")\n", "        \n", "        drone_hybrid = drone_aligned.copy()\n", "        method_used = \"coordinate_only_icp_rejected\"\n", "        \n", "    elif xy_magnitude < xy_refinement_threshold:\n", "        print(f\"ACCEPTING XY refinement: {xy_magnitude:.3f}m < {xy_refinement_threshold}m threshold\")\n", "        \n", "        # Apply XY refinement while preserving Z from coordinate alignment\n", "        drone_hybrid = drone_aligned.copy()\n", "        drone_hybrid[:, 0] += xy_refinement[0]  # Refine X\n", "        drone_hybrid[:, 1] += xy_refinement[1]  # Refine Y\n", "        # Z stays from coordinate-only alignment\n", "        \n", "        method_used = \"hybrid_coordinate_xy\"\n", "        print(f\"Applied hybrid transformation: XY refinement + coordinate Z\")\n", "        \n", "    else:\n", "        print(f\"REJECTING XY refinement: {xy_magnitude:.3f}m > {xy_refinement_threshold}m threshold\")\n", "        print(f\"Using coordinate-only alignment (more reliable)\")\n", "        \n", "        drone_hybrid = drone_aligned.copy()\n", "        method_used = \"coordinate_only_threshold_exceeded\"\n", "    \n", "    return drone_hybrid, method_used\n", "\n", "drone_final, alignment_method = apply_hybrid_alignment(drone_coordinate_aligned, xy_refinement, xy_refinement_mag)\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 7. Evaluate Final Alignment Quality\n"]}, {"cell_type": "code", "execution_count": 16, "metadata": {"execution": {"iopub.execute_input": "2025-07-20T13:25:26.738030Z", "iopub.status.busy": "2025-07-20T13:25:26.737950Z", "iopub.status.idle": "2025-07-20T13:25:26.920682Z", "shell.execute_reply": "2025-07-20T13:25:26.920339Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["=== FINAL ALIGNMENT QUALITY ===\n", "Method used: coordinate_only_icp_rejected\n", "Final centroid separation: 0.000000m\n", "RMSE: 27.48m\n", "Median distance: 4.99m\n", "Good points (<2m): 6.9%\n", "\n", "WARNING: Poor alignment quality detected!\n", "   RMSE 27.5m is very high, only 6.9% points well-aligned\n", "   Consider: rotation correction, scale adjustment, or different method\n"]}], "source": ["def evaluate_final_alignment(drone_final, ifc_pts, method):\n", "    # Calculate final centroid separation\n", "    final_drone_center = np.mean(drone_final, axis=0)\n", "    final_ifc_center = np.mean(ifc_pts, axis=0)\n", "    final_separation = np.linalg.norm(final_drone_center - final_ifc_center)\n", "    \n", "    # Quality assessment with sampling\n", "    # sample_size = 5000\n", "    # drone_sample = drone_final[np.random.choice(len(drone_final), min(sample_size, len(drone_final)), replace=False)]\n", "    # ifc_sample = ifc_pts[np.random.choice(len(ifc_pts), min(sample_size, len(ifc_pts)), replace=False)]\n", "    \n", "    np.random.seed(RANDOM_SEED)  # Ensure reproducible sampling\n", "    \n", "    # Use same sample size across all methods\n", "    n_drone = min(STANDARD_SAMPLE_SIZE, len(drone_final))\n", "    n_ifc = min(STANDARD_SAMPLE_SIZE, len(ifc_pts))\n", "    \n", "    # Fixed sampling indices for reproducibility\n", "    drone_indices = np.random.choice(len(drone_final), n_drone, replace=False)\n", "    ifc_indices = np.random.choice(len(ifc_pts), n_ifc, replace=False)\n", "\n", "    drone_sample = drone_final[drone_indices]\n", "    ifc_sample = ifc_pts[ifc_indices]\n", "\n", "    # Nearest neighbor distances\n", "    tree = cKDTree(ifc_sample)\n", "    distances, _ = tree.query(drone_sample)\n", "    \n", "    # Calculate metrics\n", "    rmse = np.sqrt(np.mean(distances**2))\n", "    median_dist = np.median(distances)\n", "    good_pct = np.sum(distances < 2.0) / len(distances) * 100\n", "    \n", "    print(f\"=== FINAL ALIGNMENT QUALITY ===\")\n", "    print(f\"Method used: {method}\")\n", "    print(f\"Final centroid separation: {final_separation:.6f}m\")\n", "    print(f\"RMSE: {rmse:.2f}m\")\n", "    print(f\"Median distance: {median_dist:.2f}m\")\n", "    print(f\"Good points (<2m): {good_pct:.1f}%\")\n", "    \n", "    # Add quality assessment warnings\n", "    if rmse > 10.0 and good_pct < 20.0:\n", "        print(f\"\\nWARNING: Poor alignment quality detected!\")\n", "        print(f\"   RMSE {rmse:.1f}m is very high, only {good_pct:.1f}% points well-aligned\")\n", "        print(f\"   Consider: rotation correction, scale adjustment, or different method\")\n", "    elif rmse > 5.0:\n", "        print(f\"\\nCAUTION: Moderate alignment issues detected\")\n", "        print(f\"   RMSE {rmse:.1f}m suggests structural misalignment\")\n", "    else:\n", "        print(f\"\\nGood alignment quality achieved\")\n", "    \n", "    return {\n", "        'method': method,\n", "        'final_separation': final_separation,\n", "        'rmse': rmse,\n", "        'median_distance': median_dist,\n", "        'good_pct': good_pct,\n", "        'xy_refinement_applied': method == \"hybrid_coordinate_xy\",\n", "        'xy_refinement_magnitude': xy_refinement_mag if method == \"hybrid_coordinate_xy\" else 0.0\n", "    }\n", "\n", "final_results = evaluate_final_alignment(drone_final, ifc_points, alignment_method)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# 8. Compare Approaches"]}, {"cell_type": "code", "execution_count": 17, "metadata": {"execution": {"iopub.execute_input": "2025-07-20T13:25:26.922267Z", "iopub.status.busy": "2025-07-20T13:25:26.922173Z", "iopub.status.idle": "2025-07-20T13:25:26.925135Z", "shell.execute_reply": "2025-07-20T13:25:26.924884Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["=== APPROACH COMPARISON ===\n", "Coordinate-only centroid error: 0.000m\n", "Hybrid approach centroid error: 0.000000m\n", "Hybrid approach RMSE: 27.48m\n", "XY refinement of 1300.954m was rejected (too large)\n", "Assessment: COORDINATE-ONLY PREFERRED\n", "\n", "=== OVERALL ALIGNMENT QUALITY ===\n", "POOR: RMSE 27.5m indicates significant alignment failure\n", "   Recommendation: Try different alignment method or check coordinate systems\n"]}], "source": ["def compare_alignment_approaches():\n", "    print(\"=== APPROACH COMPARISON ===\")\n", "    \n", "    # Coordinate-only (perfect centroid alignment by definition)\n", "    coord_only_error = 0.000\n", "    \n", "    # Current results\n", "    current_rmse = final_results['rmse']\n", "    current_separation = final_results['final_separation']\n", "    \n", "    print(f\"Coordinate-only centroid error: {coord_only_error:.3f}m\")\n", "    print(f\"Hybrid approach centroid error: {current_separation:.6f}m\")\n", "    print(f\"Hybrid approach RMSE: {current_rmse:.2f}m\")\n", "    \n", "    if final_results['xy_refinement_applied']:\n", "        print(f\"XY refinement of {xy_refinement_mag:.3f}m was applied\")\n", "        if current_separation < 0.01:\n", "            assessment = \"SUCCESSFUL REFINEMENT\"\n", "        else:\n", "            assessment = \"REFINEMENT WITH TRADE-OFFS\"\n", "    else:\n", "        print(f\"XY refinement of {xy_refinement_mag:.3f}m was rejected (too large)\")\n", "        assessment = \"COORDINATE-ONLY PREFERRED\"\n", "    \n", "    print(f\"Assessment: {assessment}\")\n", "    \n", "    # Add overall quality assessment based on RMSE\n", "    print(f\"\\n=== OVERALL ALIGNMENT QUALITY ===\")\n", "    if current_rmse > 20.0:\n", "        print(f\"POOR: RMSE {current_rmse:.1f}m indicates significant alignment failure\")\n", "        print(f\"   Recommendation: Try different alignment method or check coordinate systems\")\n", "    elif current_rmse > 10.0:\n", "        print(f\"MODERATE: RMSE {current_rmse:.1f}m shows alignment issues\")\n", "        print(f\"   Recommendation: Consider rotation/scale correction\")\n", "    elif current_rmse > 5.0:\n", "        print(f\"ACCEPTABLE: RMSE {current_rmse:.1f}m is usable but could be improved\")\n", "    else:\n", "        print(f\"GOOD: RMSE {current_rmse:.1f}m shows successful alignment\")\n", "    \n", "    return assessment\n", "\n", "comparison_result = compare_alignment_approaches()\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 9. Save Results"]}, {"cell_type": "code", "execution_count": 18, "metadata": {"execution": {"iopub.execute_input": "2025-07-20T13:25:26.926478Z", "iopub.status.busy": "2025-07-20T13:25:26.926393Z", "iopub.status.idle": "2025-07-20T13:25:27.062513Z", "shell.execute_reply": "2025-07-20T13:25:27.062235Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Results saved to: ../../../data/output_runs/icp_alignment/ransac_pmf\n", "\n", "=== ALIGNMENT DIAGNOSTIC ===\n", "Drone extent: [1574.6, 1738.8, 28.9]m\n", "IFC extent:   [1452.8, 1561.7, 8.8]m\n", "Scale ratios: [1.08, 1.11, 3.28]\n", " Scale mismatch detected - consider scale correction\n", "Possible issues: rotation, scale, coordinate system, or coverage mismatch\n", "\n", "=== HYBRID XY-COORDINATE ALIGNMENT COMPLETE ===\n", "Final method: coordinate_only_icp_rejected\n", "Final RMSE: 27.48m\n", "Assessment: COORDINATE-ONLY PREFERRED\n"]}], "source": ["if save_results:\n", "    output_path = Path(output_dir) / ground_method\n", "    output_path.mkdir(parents=True, exist_ok=True)\n", "    \n", "    # Save final aligned point cloud\n", "    final_pcd = o3d.geometry.PointCloud()\n", "    final_pcd.points = o3d.utility.Vector3dVector(drone_final)\n", "    aligned_file = output_path / f\"{site_name}_hybrid_aligned.ply\"\n", "    o3d.io.write_point_cloud(str(aligned_file), final_pcd)\n", "    \n", "    # Save comprehensive results\n", "    results_data = {\n", "        'coordinate_offset': coordinate_offset.tolist(),\n", "        'xy_offset_after_downsampling': xy_offset.tolist(),\n", "        'xy_refinement_proposed': xy_refinement.tolist() if xy_refinement is not None else None,\n", "        'xy_refinement_magnitude': xy_refinement_mag,\n", "        'xy_fitness': xy_fitness,\n", "        'refinement_threshold': xy_refinement_threshold,\n", "        'final_results': final_results,\n", "        'comparison_assessment': comparison_result,\n", "        'decision_logic': {\n", "            'xy_magnitude_vs_threshold': f\"{xy_refinement_mag:.3f}m vs {xy_refinement_threshold}m\",\n", "            'decision': \"ACCEPT\" if final_results['xy_refinement_applied'] else \"REJECT\",\n", "            'reason': \"ICP rejected by validation\" if xy_refinement is None else (\n", "                     \"Small refinement accepted\" if final_results['xy_refinement_applied'] \n", "                     else \"Large refinement rejected - coordinate-only more reliable\")\n", "        }\n", "    }\n", "    \n", "    with open(output_path / f\"{site_name}_hybrid_alignment_results.json\", 'w') as f:\n", "        json.dump(results_data, f, indent=2)\n", "    \n", "    print(f\"Results saved to: {output_path}\")\n", "\n", "# Quick diagnostic for poor alignment\n", "if final_results['rmse'] > 10.0:\n", "    print(\"\\n=== ALIGNMENT DIAGNOSTIC ===\")\n", "    drone_bounds = np.ptp(drone_final, axis=0)\n", "    ifc_bounds = np.ptp(ifc_points, axis=0)\n", "    scale_ratios = drone_bounds / ifc_bounds\n", "    print(f\"Drone extent: [{drone_bounds[0]:.1f}, {drone_bounds[1]:.1f}, {drone_bounds[2]:.1f}]m\")\n", "    print(f\"IFC extent:   [{ifc_bounds[0]:.1f}, {ifc_bounds[1]:.1f}, {ifc_bounds[2]:.1f}]m\")\n", "    print(f\"Scale ratios: [{scale_ratios[0]:.2f}, {scale_ratios[1]:.2f}, {scale_ratios[2]:.2f}]\")\n", "    if np.any(scale_ratios > 1.5) or np.any(scale_ratios < 0.67):\n", "        print(\" Scale mismatch detected - consider scale correction\")\n", "    print(f\"Possible issues: rotation, scale, coordinate system, or coverage mismatch\")\n", "\n", "print(\"\\n=== HYBRID XY-COORDINATE ALIGNMENT COMPLETE ===\")\n", "print(f\"Final method: {alignment_method}\")\n", "print(f\"Final RMSE: {final_results['rmse']:.2f}m\")\n", "print(f\"Assessment: {comparison_result}\")\n"]}, {"cell_type": "code", "execution_count": 19, "metadata": {"execution": {"iopub.execute_input": "2025-07-20T13:25:27.063817Z", "iopub.status.busy": "2025-07-20T13:25:27.063740Z", "iopub.status.idle": "2025-07-20T13:25:27.171924Z", "shell.execute_reply": "2025-07-20T13:25:27.171653Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["=== STANDARDIZED EVALUATION: HYBRID_XY_COORDINATE ===\n", "RMSE: 28.12m\n", "Sample size: 20,000 points\n", "{'method': 'hybrid_xy_coordinate', 'rmse': 28.1164740908521, 'median_distance': 6.063814127994267, 'sample_size': 20000, 'evaluation': 'standardized_fair_comparison'}\n"]}], "source": ["def fair_comparison_evaluation(drone_aligned, ifc_pts, method_name):\n", "    \"\"\"Fair comparison with identical evaluation methodology\"\"\"\n", "    \n", "    # Use large consistent sample\n", "    SAMPLE_SIZE = 20000\n", "    np.random.seed(42)  # Fixed seed\n", "    \n", "    # Consistent sampling\n", "    drone_sample = drone_aligned[np.random.choice(len(drone_aligned), \n", "                                                 min(SAMPLE_SIZE, len(drone_aligned)), \n", "                                                 replace=False)]\n", "    ifc_sample = ifc_pts[np.random.choice(len(ifc_pts), \n", "                                         min(SAMPLE_SIZE, len(ifc_pts)), \n", "                                         replace=False)]\n", "    \n", "    # Standard evaluation\n", "    tree = cKDTree(ifc_sample)\n", "    distances, _ = tree.query(drone_sample)\n", "    \n", "    results = {\n", "        'method': method_name,\n", "        'rmse': np.sqrt(np.mean(distances**2)),\n", "        'median_distance': np.median(distances),\n", "        'sample_size': len(drone_sample),\n", "        'evaluation': 'standardized_fair_comparison'\n", "    }\n", "    \n", "    print(f\"=== STANDARDIZED EVALUATION: {method_name.upper()} ===\")\n", "    print(f\"RMSE: {results['rmse']:.2f}m\")\n", "    print(f\"Sample size: {results['sample_size']:,} points\")\n", "    \n", "    return results\n", "\n", "fair_comparison_results = fair_comparison_evaluation(drone_final, ifc_points, \"hybrid_xy_coordinate\")\n", "print(fair_comparison_results)"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 4}