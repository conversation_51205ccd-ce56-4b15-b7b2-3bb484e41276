{"cells": [{"cell_type": "markdown", "metadata": {"tags": ["parameters"]}, "source": ["# Solar Equipment CAD Extraction\n", "\n", "Extract solar trackers and module boundaries from solar equipment CAD files.\n", "\n", "**Purpose**: \n", "- Extract solar equipment data for point cloud alignment workflows \n", "- Extract tracker and module center points and dimensions\n", "- Avoid hardcoded layer names\n", "\n", "**Input**: Solar equipment CAD files (DXF format) \n", "**Output**: Tracker coordinates and module boundaries CSV files  \n", "- Save outputs as clean CSVs for downstream analysis (e.g., point cloud alignment)\n", "\n", "**Note**: This notebook handles solar equipment only.  \n", "Structural elements extraction is handled in `04_structural_site_elements_extraction.ipynb`.\n", "\n", "**Author**: <PERSON><PERSON><PERSON>  \n", "**Project**: As-Built Foundation Analysis"]}, {"cell_type": "code", "execution_count": 71, "metadata": {"tags": ["parameters"]}, "outputs": [], "source": ["# Papermill parameters\n", "site_name = \"nortan_res\"\n", "project_type = \"ENEL\"\n", "target_files = [\"2024.08.26 - Norton_PILE-SP_All Piles_Preliminary.dxf\"]\n", "search_path = \"../../../data/raw\"\n", "output_dir = \"../../../preprocessed/nortan_res/cad_extraction\"\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Setup and Configuration"]}, {"cell_type": "code", "execution_count": 72, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["INFO: Solar CAD extraction started\n", "INFO: Site: nortan_res, Project: ENEL\n", "INFO: Target files: ['2024.08.26 - Norton_PILE-SP_All Piles_Preliminary.dxf']\n", "INFO: Search path: ../../../data/raw\n", "INFO: Output directory: ../../../preprocessed/nortan_res/cad_extraction\n"]}], "source": ["import pandas as pd\n", "import numpy as np\n", "from pathlib import Path\n", "import ezdxf\n", "import json\n", "import logging\n", "from datetime import datetime\n", "from collections import defaultdict\n", "\n", "# Setup logging\n", "logging.basicConfig(level=logging.INFO, format='%(levelname)s: %(message)s')\n", "logger = logging.getLogger(__name__)\n", "\n", "logger.info(\"Solar CAD extraction started\")\n", "logger.info(f\"Site: {site_name}, Project: {project_type}\")\n", "logger.info(f\"Target files: {target_files}\")\n", "logger.info(f\"Search path: {search_path}\")\n", "logger.info(f\"Output directory: {output_dir}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Load Coordinate System Configuration"]}, {"cell_type": "code", "execution_count": 73, "metadata": {}, "outputs": [], "source": ["# Coordinate system range config (already saved from coordinate system detection)\n", "def load_coordinate_config(config_dir=\"../../../output_runs/coordinate_analysis\"):\n", "    config_path = Path(config_dir)\n", "    config_files = list(config_path.glob(\"*/site_coordinate_config_*.json\"))\n", "    if not config_files:\n", "        logger.warning(\"No config found\")\n", "        return None\n", "    latest = max(config_files, key=lambda f: f.stat().st_mtime)\n", "    return json.load(open(latest))\n", "\n", "coord_config = load_coordinate_config()\n", "valid_ranges = coord_config.get(\"valid_ranges\") if coord_config else None"]}, {"cell_type": "code", "execution_count": 74, "metadata": {}, "outputs": [], "source": ["def create_coordinate_validator(valid_ranges):\n", "    if not valid_ranges:\n", "        # Use more permissive validation for large coordinate systems\n", "        return lambda x, y, z=None: abs(x) > 1000 and abs(y) > 1000\n", "    def validator(x, y, z=None):\n", "        return (valid_ranges['x_min'] <= x <= valid_ranges['x_max'] and\n", "                valid_ranges['y_min'] <= y <= valid_ranges['y_max'])\n", "    return validator\n", "\n", "is_valid_coordinate = create_coordinate_validator(valid_ranges)\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## CAD File Discovery"]}, {"cell_type": "code", "execution_count": 75, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[INFO] Found 1 CAD file(s)\n"]}], "source": ["import subprocess\n", "\n", "def find_cad_files(search_path, target_files):\n", "    found = []\n", "    for name in target_files:\n", "        result = subprocess.getoutput(f\"find {search_path} -name '{name}'\").strip()\n", "        if result:\n", "            found.append(Path(result))\n", "    return found\n", "\n", "cad_files = find_cad_files(search_path, target_files)\n", "assert cad_files, \"No CAD files found.\"\n", "print(f\"[INFO] Found {len(cad_files)} CAD file(s)\")"]}, {"cell_type": "code", "execution_count": 76, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "Top layers in modelspace:\n", "S-PILE_A_INT_BEAR: 17340\n", "S-PILE_B_INT_BEAR: 12642\n", "S-PILE_A_INT_SEISMIC: 2890\n", "S-PILE_B_INT_SEISMIC: 2122\n", "S-TRACKER_A: 1505\n", "S-PILE_A_INT_GEAR: 1445\n", "S-TRACKER_B: 1107\n", "S-PILE_CAB-NS-INTER: 1092\n", "S-PILE_B_INT_GEAR: 1061\n", "S-PILE_A_EXT_BEAR: 840\n", "S-PILE_B_EXT_BEAR: 626\n", "S-PILE_LBD: 464\n", "S-PILE_INVERTER: 380\n", "E-ARRY-STR-24: 319\n", "S-PILE_CAB-EW: 220\n", "S-PILE_CAB-NS: 158\n", "S-PILE_CAB_NS_TRANSITION: 156\n", "S-PILE_A_EXT_SEISMIC: 120\n", "E-ARRY-STR-20: 104\n", "S-PILE_B_EXT_SEISMIC: 92\n", "\n", "Entity types found:\n", "CIRCLE: 41829\n", "LWPOLYLINE: 3252\n", "MTEXT: 38\n", "HATCH: 6\n", "\n", "Tracker/Module layer details:\n", "Layer: S-TRACKER_A, Type: LWPOLYLINE, Block: N/A\n", "Layer: S-TRACKER_A, Type: LWPOLYLINE, Block: N/A\n", "Layer: S-TRACKER_A, Type: LWPOLYLINE, Block: N/A\n", "Layer: S-TRACKER_A, Type: LWPOLYLINE, Block: N/A\n", "Layer: S-TRACKER_A, Type: LWPOLYLINE, Block: N/A\n", "Layer: S-TRACKER_A, Type: LWPOLYLINE, Block: N/A\n", "Layer: S-TRACKER_A, Type: LWPOLYLINE, Block: N/A\n", "Layer: S-TRACKER_A, Type: LWPOLYLINE, Block: N/A\n", "Layer: S-TRACKER_A, Type: LWPOLYLINE, Block: N/A\n", "Layer: S-TRACKER_A, Type: LWPOLYLINE, Block: N/A\n"]}], "source": ["from collections import Counter\n", "\n", "layer_counts = Counter()\n", "entity_types = Counter()\n", "tracker_module_details = []\n", "\n", "for f in cad_files:\n", "    doc = ezdxf.readfile(f)\n", "    msp = doc.modelspace()\n", "    for e in msp:\n", "        layer = getattr(e.dxf, 'layer', 'UNKNOWN')\n", "        entity_type = e.dxftype()\n", "        layer_counts[layer] += 1\n", "        entity_types[entity_type] += 1\n", "        \n", "        # Check for tracker/module entities\n", "        if 'TRACKER' in layer or 'ARRY' in layer:\n", "            tracker_module_details.append({\n", "                'layer': layer,\n", "                'entity_type': entity_type,\n", "                'block_name': getattr(e.dxf, 'name', '') if entity_type == 'INSERT' else 'N/A'\n", "            })\n", "\n", "print(\"\\nTop layers in modelspace:\")\n", "for layer, count in layer_counts.most_common(20):\n", "    print(f\"{layer}: {count}\")\n", "\n", "print(\"\\nEntity types found:\")\n", "for entity_type, count in entity_types.most_common(10):\n", "    print(f\"{entity_type}: {count}\")\n", "\n", "print(\"\\nTracker/Module layer details:\")\n", "for detail in tracker_module_details[:10]:  # Show first 10\n", "    print(f\"Layer: {detail['layer']}, Type: {detail['entity_type']}, Block: {detail['block_name']}\")\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Layer Analysis Results\n", "\n", "Based on the layer analysis above, we can see that:\n", "- Most entities are pile-related (S-PILE_* layers)\n", "- Tracker layers: S-TRACKER_A, S-TRACKER_B\n", "- Module/Array layers: E-ARRY-STR-24, E-ARRY-STR-20\n", "\n", "The extraction functions will target these specific layer patterns."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Solar Equipment Extraction Functions"]}, {"cell_type": "code", "execution_count": 77, "metadata": {}, "outputs": [], "source": ["# <PERSON><PERSON><PERSON> TRACKER FOUNDATION PILES ONLY - Exclude infrastructure piles\n", "# Only include piles that support solar trackers (bearing, seismic, gear)\n", "SOLAR_TRACKER_PILE_LAYERS = {\n", "    \"S-PILE_A_INT_BEAR\", \"S-PILE_B_INT_BEAR\",      # Interior bearing piles\n", "    \"S-PILE_A_INT_SEISMIC\", \"S-PILE_B_INT_SEISMIC\", # Interior seismic piles\n", "    \"S-PILE_A_INT_GEAR\", \"S-PILE_B_INT_GEAR\",       # Interior gear piles\n", "    \"S-PILE_A_EXT_BEAR\", \"S-PILE_B_EXT_BEAR\",       # Exterior bearing piles\n", "    \"S-PILE_A_EXT_SEISMIC\", \"S-PILE_B_EXT_SEISMIC\"  # Exterior seismic piles\n", "}\n", "\n", "# EXCLUDED PILE TYPES (infrastructure, not solar tracker foundations):\n", "EXCLUDED_PILE_LAYERS = {\n", "    \"S-PILE_INVERTER\",           # Inverter equipment piles\n", "    \"S-PILE_CAB-NS-INTER\",      # Cable route piles (North-South Interconnect)\n", "    \"S-PILE_CAB_NS_TRANSITION\", # Cable route transition piles\n", "    \"S-PILE_CAB-NS\",            # Cable route piles (North-South)\n", "    \"S-PILE_CAB-EW\",            # Cable route piles (East-West)\n", "    \"S-PILE_LBD\",               # Load break disconnect piles\n", "    \"S-PILE_MET\",               # Meteorological station piles\n", "    \"S-PILE_SCADA\"              # SCADA equipment piles\n", "}\n", "\n", "# Updated keywords for solar tracker foundations only\n", "tracker_keywords = ['TRACKER']\n", "tracker_layers = {'S-TRACKER_A', 'S-TRACKER_B'}\n", "module_keywords = ['ARRY', 'STR']\n", "module_layers = {'E-ARRY-STR-24', 'E-ARRY-STR-20'}\n", "\n", "# Only include solar tracker foundation pile keywords\n", "solar_pile_keywords = ['BEAR', 'SEISMIC', 'GEAR']  # Removed 'CAB', 'INVERTER', 'LBD'\n"]}, {"cell_type": "code", "execution_count": 78, "metadata": {}, "outputs": [], "source": ["def extract_solar_trackers(entity, layer):\n", "    if layer in tracker_layers:\n", "        try:\n", "            if entity.dxftype() == 'INSERT':\n", "                p = entity.dxf.insert\n", "                cx, cy, cz = p.x, p.y, p.z\n", "            elif entity.dxftype() in ['LWPOLYLINE', 'POLYLINE']:\n", "                # Calculate centroid from vertices\n", "                vertices = list(entity.vertices())\n", "                if vertices:\n", "                    x_coords = [v[0] for v in vertices]\n", "                    y_coords = [v[1] for v in vertices]\n", "                    z_coords = [v[2] if len(v) > 2 else 0.0 for v in vertices]\n", "                    cx = sum(x_coords) / len(x_coords)\n", "                    cy = sum(y_coords) / len(y_coords)\n", "                    cz = sum(z_coords) / len(z_coords)\n", "                else:\n", "                    return []\n", "            <PERSON><PERSON>(entity.dxf, 'center'):\n", "                p = entity.dxf.center\n", "                cx, cy, cz = p.x, p.y, p.z\n", "            else:\n", "                return []\n", "            \n", "            if is_valid_coordinate(cx, cy, cz):\n", "                return [{\n", "                    'x': cx, 'y': cy, 'z': cz,\n", "                    'layer': layer,\n", "                    'block_name': getattr(entity.dxf, 'name', '') if entity.dxftype() == 'INSERT' else '',\n", "                    'rotation': getattr(entity.dxf, 'rotation', 0.0),\n", "                    'type': 'tracker'\n", "                }]\n", "        except:\n", "            pass\n", "    return []\n"]}, {"cell_type": "code", "execution_count": 79, "metadata": {}, "outputs": [], "source": ["def extract_solar_modules(entity, layer):\n", "    if layer in module_layers:\n", "        try:\n", "            if entity.dxftype() in ['LWPOLYLINE', 'POLYLINE']:\n", "                # Calculate centroid and dimensions from vertices\n", "                vertices = list(entity.vertices())\n", "                if vertices:\n", "                    x_coords = [v[0] for v in vertices]\n", "                    y_coords = [v[1] for v in vertices]\n", "                    z_coords = [v[2] if len(v) > 2 else 0.0 for v in vertices]\n", "                    cx = sum(x_coords) / len(x_coords)\n", "                    cy = sum(y_coords) / len(y_coords)\n", "                    cz = sum(z_coords) / len(z_coords)\n", "                    width = max(x_coords) - min(x_coords)\n", "                    height = max(y_coords) - min(y_coords)\n", "                else:\n", "                    return []\n", "            elif entity.dxftype() == 'INSERT':\n", "                p = entity.dxf.insert\n", "                cx, cy, cz = p.x, p.y, p.z\n", "                width = height = 1.0\n", "            elif entity.dxftype() == 'CIRCLE':\n", "                p = entity.dxf.center\n", "                cx, cy, cz = p.x, p.y, p.z\n", "                radius = entity.dxf.radius\n", "                width = height = radius * 2\n", "            else:\n", "                return []\n", "            \n", "            if is_valid_coordinate(cx, cy, cz):\n", "                return [{\n", "                    'x': cx, 'y': cy, 'z': cz,\n", "                    'layer': layer,\n", "                    'width': width,\n", "                    'height': height,\n", "                    'block_name': getattr(entity.dxf, 'name', '') if entity.dxftype() == 'INSERT' else '',\n", "                    'type': 'module'\n", "                }]\n", "        except:\n", "            pass\n", "    return []"]}, {"cell_type": "code", "execution_count": 80, "metadata": {}, "outputs": [], "source": ["def extract_solar_tracker_piles(entity, layer):\n", "    \"\"\"\n", "    Extract ONLY solar tracker foundation piles, excluding infrastructure piles\n", "    \"\"\"\n", "    # Only extract from explicitly allowed solar tracker pile layers\n", "    if layer in SOLAR_TRACKER_PILE_LAYERS and entity.dxftype() == 'CIRCLE':\n", "        center = entity.dxf.center\n", "        radius = entity.dxf.radius\n", "        if is_valid_coordinate(center.x, center.y, center.z):\n", "            # Try to find parent block reference (INSERT) if any\n", "            parent_name = ''\n", "            if hasattr(entity, 'block') and hasattr(entity.block, 'name'):\n", "                parent_name = entity.block.name\n", "            \n", "            # Determine pile function from layer name\n", "            pile_function = 'unknown'\n", "            if 'BEAR' in layer:\n", "                pile_function = 'bearing'\n", "            elif 'SEISMIC' in layer:\n", "                pile_function = 'seismic'\n", "            elif 'GEAR' in layer:\n", "                pile_function = 'gear'\n", "            \n", "            # Determine pile location (interior vs exterior)\n", "            pile_location = 'interior' if 'INT' in layer else 'exterior'\n", "            \n", "            # Determine tracker type (A or B)\n", "            tracker_type = 'A' if '_A_' in layer else 'B' if '_B_' in layer else 'unknown'\n", "            \n", "            return [{\n", "                'x': center.x, 'y': center.y, 'z': center.z,\n", "                'radius': radius,\n", "                'layer': layer,\n", "                'block_name': parent_name,\n", "                'type': 'solar_tracker_pile',\n", "                'pile_function': pile_function,\n", "                'pile_location': pile_location,\n", "                'tracker_type': tracker_type\n", "            }]\n", "    \n", "    # Log excluded pile types for verification\n", "    elif layer in EXCLUDED_PILE_LAYERS and entity.dxftype() == 'CIRCLE':\n", "        # Don't extract, but could log for debugging\n", "        pass\n", "    \n", "    return []"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Main CAD Processing"]}, {"cell_type": "code", "execution_count": 81, "metadata": {}, "outputs": [], "source": ["from collections import Counter\n", "\n", "entity_counter = Counter()\n", "\n", "def process_cad_file(file_path):\n", "    doc = ezdxf.readfile(file_path)\n", "    msp = doc.modelspace()\n", "    trackers = []\n", "    modules = []\n", "    solar_tracker_piles = []\n", "    \n", "    # Track excluded pile counts for reporting\n", "    excluded_pile_counts = {layer: 0 for layer in EXCLUDED_PILE_LAYERS}\n", "    \n", "    for e in msp:\n", "        layer = getattr(e.dxf, 'layer', 'UNKNOWN')\n", "        trackers.extend(extract_solar_trackers(e, layer))\n", "        modules.extend(extract_solar_modules(e, layer))\n", "        solar_tracker_piles.extend(extract_solar_tracker_piles(e, layer))\n", "        \n", "        # Count excluded piles for reporting\n", "        if layer in EXCLUDED_PILE_LAYERS and e.dxftype() == 'CIRCLE':\n", "            excluded_pile_counts[layer] += 1\n", "    \n", "    # Report excluded pile counts\n", "    total_excluded = sum(excluded_pile_counts.values())\n", "    if total_excluded > 0:\n", "        print(f\"\\nExcluded {total_excluded} infrastructure piles:\")\n", "        for layer, count in excluded_pile_counts.items():\n", "            if count > 0:\n", "                print(f\"  {layer}: {count}\")\n", "    \n", "    return trackers, modules, solar_tracker_piles"]}, {"cell_type": "code", "execution_count": 82, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "Excluded 2545 infrastructure piles:\n", "  S-PILE_CAB_NS_TRANSITION: 156\n", "  S-PILE_CAB-EW: 220\n", "  S-PILE_INVERTER: 380\n", "  S-PILE_LBD: 464\n", "  S-PILE_CAB-NS: 158\n", "  S-PILE_MET: 4\n", "  S-PILE_CAB-NS-INTER: 1087\n", "  S-PILE_SCADA: 76\n"]}], "source": ["all_trackers = []\n", "all_modules = []\n", "all_solar_tracker_piles = []\n", "\n", "for f in cad_files:\n", "    t, m, p = process_cad_file(f)\n", "    for item in t + m + p:\n", "        item['source_file'] = f.name\n", "    all_trackers.extend(t)\n", "    all_modules.extend(m)\n", "    all_solar_tracker_piles.extend(p)\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Output Generation"]}, {"cell_type": "code", "execution_count": 83, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Extracted 2612 trackers, 423 modules, 39178 solar tracker piles.\n", "\n", "Note: Excluded infrastructure piles (inverter, cable route, LBD, MET, SCADA) - see processing output above.\n"]}], "source": ["timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')\n", "out_path = Path(output_dir)\n", "out_path.mkdir(exist_ok=True, parents=True)\n", "\n", "if all_trackers:\n", "    pd.DataFrame(all_trackers).to_csv(out_path / f\"{site_name.lower()}_trackers_{timestamp}.csv\", index=False)\n", "if all_modules:\n", "    pd.DataFrame(all_modules).to_csv(out_path / f\"{site_name.lower()}_modules_{timestamp}.csv\", index=False)\n", "if all_solar_tracker_piles:\n", "    pd.DataFrame(all_solar_tracker_piles).to_csv(out_path / f\"{site_name.lower()}_solar_tracker_piles_{timestamp}.csv\", index=False)\n", "\n", "print(f\"Extracted {len(all_trackers)} trackers, {len(all_modules)} modules, {len(all_solar_tracker_piles)} solar tracker piles.\")\n", "print(f\"\\nNote: Excluded infrastructure piles (inverter, cable route, LBD, MET, SCADA) - see processing output above.\")\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Pile Filtering Summary\n", "\n", "Show breakdown of extracted vs excluded pile types."]}, {"cell_type": "code", "execution_count": 88, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["=== SOLAR TRACKER PILE EXTRACTION SUMMARY ===\n", "\n", "Extracted Solar Tracker Piles by Layer:\n", "  S-PILE_A_EXT_BEAR: 840\n", "  S-PILE_A_EXT_SEISMIC: 120\n", "  S-PILE_A_INT_BEAR: 17340\n", "  S-PILE_A_INT_GEAR: 1445\n", "  S-PILE_A_INT_SEISMIC: 2890\n", "  S-PILE_B_EXT_BEAR: 626\n", "  S-PILE_B_EXT_SEISMIC: 92\n", "  S-PILE_B_INT_BEAR: 12642\n", "  S-PILE_B_INT_GEAR: 1061\n", "  S-PILE_B_INT_SEISMIC: 2122\n", "\n", "Extracted Solar Tracker Piles by Function:\n", "  bearing: 31448\n", "  seismic: 5224\n", "  gear: 2506\n", "\n", "Extracted Solar Tracker Piles by Location:\n", "  interior: 37500\n", "  exterior: 1678\n", "\n", "Extracted Solar Tracker Piles by Tracker Type:\n", "  Tracker A: 22635\n", "  Tracker B: 16543\n", "\n", "=== EXCLUDED PILE TYPES ===\n", "The following pile types were EXCLUDED from extraction:\n", "  S-PILE_CAB_NS_TRANSITION\n", "  S-PILE_CAB-EW\n", "  S-PILE_INVERTER\n", "  S-PILE_LBD\n", "  S-PILE_CAB-NS\n", "  S-PILE_MET\n", "  S-PILE_CAB-NS-INTER\n", "  S-PILE_SCADA\n", "\n", " Total Solar Tracker Foundation Piles: 39178\n", " These are the piles that should be compared with PointNet++ detections.\n"]}], "source": ["if all_solar_tracker_piles:\n", "    piles_df = pd.DataFrame(all_solar_tracker_piles)\n", "    \n", "    print(\"=== <PERSON><PERSON>AR TRACKER PILE EXTRACTION SUMMARY ===\")\n", "    print(f\"\\nExtracted Solar Tracker Piles by Layer:\")\n", "    layer_counts = piles_df['layer'].value_counts().sort_index()\n", "    for layer, count in layer_counts.items():\n", "        print(f\"  {layer}: {count}\")\n", "    \n", "    print(f\"\\nExtracted Solar Tracker Piles by Function:\")\n", "    function_counts = piles_df['pile_function'].value_counts()\n", "    for function, count in function_counts.items():\n", "        print(f\"  {function}: {count}\")\n", "    \n", "    print(f\"\\nExtracted Solar Tracker Piles by Location:\")\n", "    location_counts = piles_df['pile_location'].value_counts()\n", "    for location, count in location_counts.items():\n", "        print(f\"  {location}: {count}\")\n", "    \n", "    print(f\"\\nExtracted Solar Tracker Piles by Tracker Type:\")\n", "    tracker_counts = piles_df['tracker_type'].value_counts()\n", "    for tracker_type, count in tracker_counts.items():\n", "        print(f\"  Tracker {tracker_type}: {count}\")\n", "    \n", "    print(f\"\\n=== EXCLUDED PILE TYPES ===\")\n", "    print(\"The following pile types were EXCLUDED from extraction:\")\n", "    for layer in EXCLUDED_PILE_LAYERS:\n", "        print(f\"  {layer}\")\n", "    \n", "    print(f\"\\n Total Solar Tracker Foundation Piles: {len(all_solar_tracker_piles)}\")\n", "    print(f\" These are the piles that should be compared with PointNet++ detections.\")\n", "else:\n", "    print(\"No solar tracker piles extracted.\")\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Survey Boundary Analysis\n", "\n", "- Filter piles within the surveyed area boundary for RES Renewables project.\n", "- Generate KML files for visualization and compare spatial extents with PointNet++ results."]}, {"cell_type": "code", "execution_count": 85, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Pile distribution within survey boundary:\n", "within_survey         False  True \n", "layer                             \n", "S-PILE_A_EXT_BEAR       840      0\n", "S-PILE_A_EXT_SEISMIC    120      0\n", "S-PILE_A_INT_BEAR     17340      0\n", "S-PILE_A_INT_GEAR      1445      0\n", "S-PILE_A_INT_SEISMIC   2890      0\n", "S-PILE_B_EXT_BEAR       589     37\n", "S-PILE_B_EXT_SEISMIC     86      6\n", "S-PILE_B_INT_BEAR     12269    373\n", "S-PILE_B_INT_GEAR      1025     36\n", "S-PILE_B_INT_SEISMIC   2050     72\n", "\n", "Summary:\n", "Total piles extracted: 39178\n", "Piles within survey boundary: 524\n", "Piles outside survey boundary: 38654\n", "Survey coverage: 1.3%\n", "\n", "Saved 524 surveyed piles to CSV\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO: Created 39,178 records\n", "INFO: Created 0 records\n"]}, {"name": "stdout", "output_type": "stream", "text": ["KML saved: ../../../preprocessed/nortan_res/cad_extraction/nortan_res_cad_piles_20250729_175836.kml\n", "Boundary KML saved: ../../../preprocessed/nortan_res/cad_extraction/nortan_res_survey_boundary_20250729_175836.kml\n"]}], "source": ["import geopandas as gpd\n", "import pandas as pd\n", "import numpy as np\n", "from shapely.geometry import Point, Polygon\n", "from pyproj import Transformer\n", "import warnings\n", "\n", "warnings.filterwarnings(\"ignore\")  # Suppress geospatial and KML export warnings\n", "\n", "# Transformer from CAD CRS (EPSG:3857) to WGS84 (EPSG:4326)\n", "transformer = Transformer.from_crs(\"EPSG:3857\", \"EPSG:4326\", always_xy=True)\n", "\n", "# Define survey boundary in CAD projection (EPSG:3857)\n", "survey_boundary_coords = [\n", "    (2335367.6595, 10652597.6363),\n", "    (2335624.5755, 10652597.5533),\n", "    (2335624.5755, 10651698.6673),\n", "    (2334666.9795, 10652112.1673),\n", "    (2334666.9795, 10651698.5013)\n", "]\n", "survey_polygon = Polygon(survey_boundary_coords)\n", "\n", "# Main data processing\n", "if all_solar_tracker_piles:\n", "    # Load and clean\n", "    piles_df = pd.DataFrame(all_solar_tracker_piles)\n", "    piles_df['z_numeric'] = pd.to_numeric(piles_df.get('z', 0.0), errors='coerce').fillna(0.0)\n", "\n", "    # Survey inclusion check (still in CAD coords)\n", "    piles_df['within_survey'] = piles_df.apply(\n", "        lambda row: survey_polygon.contains(Point(row['x'], row['y'])), axis=1\n", "    )\n", "\n", "    # Summary stats\n", "    total_piles = len(piles_df)\n", "    piles_in_boundary = piles_df['within_survey'].sum()\n", "    piles_outside_boundary = total_piles - piles_in_boundary\n", "\n", "    print(\"Pile distribution within survey boundary:\")\n", "    print(piles_df.groupby(['layer', 'within_survey']).size().unstack(fill_value=0))\n", "    print(f\"\\nSummary:\")\n", "    print(f\"Total piles extracted: {total_piles}\")\n", "    print(f\"Piles within survey boundary: {piles_in_boundary}\")\n", "    print(f\"Piles outside survey boundary: {piles_outside_boundary}\")\n", "    print(f\"Survey coverage: {piles_in_boundary / total_piles * 100:.1f}%\")\n", "\n", "    # Save filtered CSV\n", "    surveyed_piles = piles_df[piles_df['within_survey']]\n", "    if not surveyed_piles.empty:\n", "        surveyed_piles.to_csv(out_path / f\"{site_name.lower()}_piles_surveyed_{timestamp}.csv\", index=False)\n", "        print(f\"\\nSaved {len(surveyed_piles)} surveyed piles to CSV\")\n", "\n", "    # Coordinate transformation to WGS84\n", "    transformed_coords = [transformer.transform(x, y) for x, y in zip(piles_df['x'], piles_df['y'])]\n", "    piles_df['lon'], piles_df['lat'] = zip(*transformed_coords)\n", "    piles_df['geometry'] = [Point(lon, lat) for lon, lat in transformed_coords]\n", "\n", "    # Build GeoDataFrame with valid Points only\n", "    gdf = gpd.GeoDataFrame(piles_df, geometry='geometry', crs=\"EPSG:4326\")\n", "    gdf = gdf[gdf.geometry.type == \"Point\"]\n", "    gdf = gdf[gdf.is_valid]\n", "    gdf['Name'] = gdf['layer'].astype(str)\n", "    gdf['surveyed'] = piles_df['within_survey']\n", "\n", "    # Export to KML\n", "    out_file = out_path / f\"{site_name.lower()}_cad_piles_{timestamp}.kml\"\n", "    gdf[['Name', 'geometry']].to_file(out_file, driver=\"KML\")\n", "    print(f\"KML saved: {out_file}\")\n", "\n", "    # Export boundary polygon\n", "    boundary_wgs = [transformer.transform(x, y) for x, y in survey_boundary_coords]\n", "    boundary_wgs.append(boundary_wgs[0])  # Close the polygon\n", "    boundary_polygon = Polygon(boundary_wgs)\n", "\n", "    boundary_gdf = gpd.GeoDataFrame(\n", "        [{\"Name\": \"Surveyed Area\", \"geometry\": boundary_polygon}],\n", "        crs=\"EPSG:4326\"\n", "    )\n", "    boundary_gdf = boundary_gdf[boundary_gdf.geometry.type == \"Polygon\"]\n", "    boundary_gdf = boundary_gdf[boundary_gdf.is_valid]\n", "\n", "    boundary_kml = out_path / f\"{site_name.lower()}_survey_boundary_{timestamp}.kml\"\n", "    boundary_gdf.to_file(boundary_kml, driver=\"KML\")\n", "    print(f\"Boundary KML saved: {boundary_kml}\")\n", "\n", "else:\n", "    print(\"No pile data available for processing or KML export.\")\n"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 4}