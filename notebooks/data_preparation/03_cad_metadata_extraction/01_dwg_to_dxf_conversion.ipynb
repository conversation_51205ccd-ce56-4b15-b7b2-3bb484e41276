{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["## Objective\n", "\n", "Discover DWG files and facilitate their conversion to DXF format:\n", "- Locate all DWG files in the project data structure\n", "- Validate file accessibility and basic properties\n", "- Provide conversion guidance and automation where possible\n", "- Generate file inventory for tracking conversion progress"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Approach\n", "\n", "1. Recursively discover DWG files using cross-platform file operations\n", "2. Validate file accessibility and basic properties\n", "3. Check for existing DXF conversions\n", "4. Provide conversion instructions and automation scripts\n", "5. Generate progress tracking and file management tools"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Code"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["DWG File Discovery and DXF Conversion - Starting...\n", "Timestamp: 2025-07-29 13:18:02\n"]}], "source": ["import logging\n", "import os\n", "import subprocess\n", "from pathlib import Path\n", "from collections import defaultdict\n", "from datetime import datetime\n", "from typing import List, Dict, Optional, Tuple\n", "\n", "# Configure logging\n", "logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')\n", "logger = logging.getLogger(__name__)\n", "\n", "print(\"DWG File Discovery and DXF Conversion - Starting...\")\n", "print(f\"Timestamp: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\")"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-07-29 13:18:41,664 - INFO - Data directory verified: ../../../data/raw/nortan_res/cad\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Project root: /Users/<USER>/Documents/GitHub/asbuilt-foundation-analysis/data/raw/nortan_res\n", "Data path: /Users/<USER>/Documents/GitHub/asbuilt-foundation-analysis/data/raw/nortan_res/cad\n"]}], "source": ["# Define paths for DWG file discovery\n", "project_root = Path('../../../data/raw/nortan_res/')  # Adjust to project root from notebooks/preprocessing/dwg_to_dxf/\n", "data_path = project_root / 'cad'\n", "\n", "print(f\"Project root: {project_root.resolve()}\")\n", "print(f\"Data path: {data_path.resolve()}\")\n", "\n", "# Verify the data directory exists\n", "if not data_path.exists():\n", "    logger.error(f\"Data directory does not exist: {data_path}\")\n", "    raise FileNotFoundError(f\"Directory not found: {data_path}\")\n", "elif not data_path.is_dir():\n", "    logger.error(f\"Data path is not a directory: {data_path}\")\n", "    raise NotADirectoryError(f\"Path is not a directory: {data_path}\")\n", "else:\n", "    logger.info(f\"Data directory verified: {data_path}\")"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "Discovered 1 DWG files\n", "Conversion status per file:\n", "- ../../../data/raw/nortan_res/cad/2024.08.26 - Norton_PILE-SP_All Piles_Preliminary.dwg | DXF: No | Size: 2.05MB → 0MB\n"]}], "source": ["# Discover DWG files\n", "dwg_files = list(data_path.rglob(\"*.dwg\"))\n", "conversion_status = defaultdict(dict)\n", "subdir_count = defaultdict(int)\n", "\n", "for dwg in dwg_files:\n", "    if not os.access(dwg, os.R_OK):\n", "        continue  # Skip unreadable files\n", "    dxf = dwg.with_suffix(\".dxf\")\n", "    rel_path = dwg.relative_to(data_path)\n", "    subdir = rel_path.parts[0] if len(rel_path.parts) > 1 else \"root\"\n", "    subdir_count[subdir] += 1\n", "    conversion_status[str(dwg)] = {\n", "        \"dxf_exists\": dxf.exists(),\n", "        \"needs_conversion\": not dxf.exists(),\n", "        \"dwg_size_mb\": round(dwg.stat().st_size / (1024 * 1024), 2),\n", "        \"dxf_size_mb\": round(dxf.stat().st_size / (1024 * 1024), 2) if dxf.exists() else 0,\n", "    }\n", "\n", "print(f\"\\nDiscovered {len(dwg_files)} DWG files\")\n", "print(\"Conversion status per file:\")\n", "for path, status in conversion_status.items():\n", "    print(f\"- {path} | DXF: {'Yes' if status['dxf_exists'] else 'No'} | Size: {status['dwg_size_mb']}MB → {status['dxf_size_mb']}MB\")"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "Available conversion tools:\n", "  ODAFileConverter: Available\n", "  dwg2dxf: Not Found\n", "  librecad: Not Found\n", "  qcad: Not Found\n", "\n", "At least one DWG to DXF tool is available.\n", "\n", "DWG Check Completed.\n", "============================================================\n"]}], "source": ["from pathlib import Path\n", "import subprocess\n", "\n", "# --- Check available conversion tools ---\n", "def tool_available(cmd_or_path, test_arg=\"--version\", allow_fail=False):\n", "    try:\n", "        result = subprocess.run([cmd_or_path, test_arg], capture_output=True, timeout=5)\n", "        return result.returncode == 0 or allow_fail\n", "    except Exception:\n", "        return allow_fail  # allow_fail=True means we trust presence even if not executable\n", "\n", "# ODA macOS app path\n", "ODA_PATH = \"/Applications/ODAFileConverter.app/Contents/MacOS/ODAFileConverter\"\n", "oda_installed = Path(ODA_PATH).exists()\n", "\n", "# Trust ODA if present, even if CLI call hangs or fails\n", "if oda_installed:\n", "    try:\n", "        subprocess.run([ODA_PATH], capture_output=True, timeout=5)\n", "        oda_available = True\n", "    except subprocess.TimeoutExpired:\n", "        print(f\"ODAFileConverter found at {ODA_PATH}, but execution timed out.\")\n", "        oda_available = True  # Still trust it\n", "    except Exception as e:\n", "        print(f\"ODAFileConverter found at {ODA_PATH}, but could not run: {e}\")\n", "        oda_available = True  # Still trust it\n", "else:\n", "    oda_available = False\n", "\n", "# Check other tools in PATH\n", "tools = {\n", "    \"ODAFileConverter\": oda_available,\n", "    \"dwg2dxf\": tool_available(\"dwg2dxf\"),\n", "    \"librecad\": tool_available(\"librecad\"),\n", "    \"qcad\": tool_available(\"qcad\"),\n", "}\n", "\n", "print(\"\\nAvailable conversion tools:\")\n", "for name, available in tools.items():\n", "    print(f\"  {name}: {'Available' if available else 'Not Found'}\")\n", "\n", "if not any(tools.values()):\n", "    print(\"\\nNo conversion tools available. Please install ODAFileConverter or another DWG to DXF utility.\")\n", "else:\n", "    print(\"\\nAt least one DWG to DXF tool is available.\")\n", "\n", "print(\"\\nDWG Check Completed.\")\n", "print(\"=\" * 60)\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Input folder: ../../../data/raw/nortan_res/cad\n", "Output folder: ../../../data/raw/nortan_res/converted_dxf\n", "Conversion output:\n", "\n", "\n", "Converted 1 file(s):\n", "- 2024.08.26 - Norton_PILE-SP_All Piles_Preliminary.dxf\n"]}], "source": ["import subprocess\n", "import os\n", "\n", "# ----- Configuration -----\n", "TEIGHA_PATH = \"/Applications/ODAFileConverter.app/Contents/MacOS/ODAFileConverter\"  # For macOS GUI (launches UI)\n", "# TEIGHA_PATH = \"ODAFileConverter.exe\"  # If using Wine or on Windows\n", "\n", "INPUT_FOLDER = data_path\n", "OUTPUT_FOLDER = f\"{data_path.parent}/converted_dxf\"\n", "OUTVER = \"ACAD2013\"\n", "OUTFORMAT = \"DXF\"\n", "RECURSIVE = \"0\"\n", "AUDIT = \"1\"\n", "INPUTFILTER = \"*.DWG\"\n", "\n", "print(f\"Input folder: {INPUT_FOLDER}\")\n", "print(f\"Output folder: {OUTPUT_FOLDER}\")\n", "\n", "# ----- Check if executable exists -----\n", "if not os.path.exists(TEIGHA_PATH):\n", "    raise FileNotFoundError(f\"Converter not found at: {TEIGHA_PATH}\")\n", "\n", "# ----- Build command -----\n", "cmd = [\n", "    TEIGHA_PATH,\n", "    INPUT_FOLDER,\n", "    OUTPUT_FOLDER,\n", "    OUTVER,\n", "    OUTFORMAT,\n", "    RECURSIVE,\n", "    AUDIT,\n", "    INPUTFILTER\n", "]\n", "\n", "# ----- Execute -----\n", "try:\n", "    result = subprocess.run(cmd, capture_output=True, text=True ,check=True)\n", "    print(\"Conversion output:\")\n", "    print(result.stdout)\n", "    # List converted files\n", "    converted_files = list(Path(OUTPUT_FOLDER).rglob(\"*.dxf\"))\n", "    print(f\"\\nConverted {len(converted_files)} file(s):\")\n", "    for file in converted_files:\n", "        print(f\"- {file.name}\")\n", "\n", "except Exception as e:\n", "    print(f\"Error during conversion: {e}\")\n"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 4}