{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# CAD Coordinate System Analysis\n", "\n", "This notebook analyzes CAD files to automatically detect coordinate systems and valid coordinate ranges.\n", "\n", "## Purpose:\n", "- **Detect coordinate system type** (UTM, Geographic, Local)\n", "- **Find valid coordinate ranges** using statistical analysis\n", "- **Generate site configuration** for other notebooks to use\n", "\n", "## Outputs:\n", "- **site_coordinate_config.json**: Coordinate system info and valid ranges\n", "- **coordinate_analysis_report.txt**: Human-readable analysis report\n", "\n", "**Author**: <PERSON><PERSON><PERSON>  \n", "**Project**: As-Built Foundation Analysis"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 1: Setup and Configuration"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[INFO] Site: nortan_res\n", "[INFO] Source DXF dir: ../../../data/raw/nortan_res/converted_dxf\n", "[INFO] Output config: ../../../data/raw/nortan_res/site_coordinate_config.json\n"]}], "source": ["from pathlib import Path\n", "import pandas as pd\n", "import numpy as np\n", "import ezdxf\n", "import json\n", "\n", "SITE = \"nortan_res\"\n", "ROOT = Path(\"../../../data/raw\") / SITE\n", "DXF_DIR = ROOT / \"converted_dxf\"\n", "CONFIG_OUT = ROOT / \"site_coordinate_config.json\"\n", "\n", "print(f\"[INFO] Site: {SITE}\")\n", "print(f\"[INFO] Source DXF dir: {DXF_DIR}\")\n", "print(f\"[INFO] Output config: {CONFIG_OUT}\")\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 2: Load and extract coordinates from DXF files"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["def extract_all_coords_from_dxf(file_path):\n", "    doc = ezdxf.readfile(file_path)\n", "    msp = doc.modelspace()\n", "    coords = []\n", "\n", "    for e in msp:\n", "        if hasattr(e.dxf, \"insert\"):\n", "            pt = e.dxf.insert\n", "            coords.append({'x': pt.x, 'y': pt.y})\n", "        elif e.dxftype() in [\"LINE\"]:\n", "            coords.append({'x': e.dxf.start.x, 'y': e.dxf.start.y})\n", "            coords.append({'x': e.dxf.end.x, 'y': e.dxf.end.y})\n", "        elif e.dx<PERSON>pe() in [\"LWPOLYLINE\", \"POLYLINE\"]:\n", "            for pt in e.get_points():\n", "                coords.append({'x': pt[0], 'y': pt[1]})\n", "\n", "    return pd.<PERSON><PERSON><PERSON><PERSON>(coords)"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[INFO] Found 1 DXF file(s)\n", "[INFO] Extracted 13297 coordinates\n"]}], "source": ["dxf_files = list(DXF_DIR.glob(\"*.dxf\"))\n", "assert dxf_files, \"No DXF files found.\"\n", "print(f\"[INFO] Found {len(dxf_files)} DXF file(s)\")\n", "\n", "coords_df = extract_all_coords_from_dxf(dxf_files[0])\n", "print(f\"[INFO] Extracted {len(coords_df)} coordinates\")\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 3: Coordinate System Detection and Validation"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[CRS] Detected: {'type': 'Local', 'confidence': 'medium'}\n"]}], "source": ["def detect_crs(xmin, ymin, xmax, ymax):\n", "    x_range, y_range = xmax - xmin, ymax - ymin\n", "    if 100_000 <= xmin <= 900_000 and 1_000_000 <= ymin <= 10_000_000:\n", "        return {'type': 'UTM', 'confidence': 'high'}\n", "    elif -180 <= xmin <= 180 and -90 <= ymin <= 90:\n", "        return {'type': 'Geographic', 'confidence': 'high'}\n", "    elif x_range < 100_000 and y_range < 100_000:\n", "        return {'type': 'Local', 'confidence': 'medium'}\n", "    return {'type': 'Unknown', 'confidence': 'low'}\n", "\n", "xmin, xmax = coords_df['x'].min(), coords_df['x'].max()\n", "ymin, ymax = coords_df['y'].min(), coords_df['y'].max()\n", "\n", "crs_info = detect_crs(xmin, ymin, xmax, ymax)\n", "print(f\"[CRS] Detected: {crs_info}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 4: IQR-Based Valid Range Estimation"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[X] Valid range: 2330779.48 – 2339123.57\n", "[Y] Valid range: 10643475.99 – 10658099.00\n"]}], "source": ["def iqr_bounds(arr):\n", "    q1, q3 = np.percentile(arr, [25, 75])\n", "    iqr = q3 - q1\n", "    return q1 - 1.5 * iqr, q3 + 1.5 * iqr\n", "\n", "x_lo, x_hi = iqr_bounds(coords_df['x'])\n", "y_lo, y_hi = iqr_bounds(coords_df['y'])\n", "\n", "print(f\"[X] Valid range: {x_lo:.2f} – {x_hi:.2f}\")\n", "print(f\"[Y] Valid range: {y_lo:.2f} – {y_hi:.2f}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 6: Create Site Configuration"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[INFO] Config written to ../../../data/raw/nortan_res/site_coordinate_config.json\n"]}], "source": ["config = {\n", "    'site': SITE,\n", "    'crs': crs_info,\n", "    'x_range': [x_lo, x_hi],\n", "    'y_range': [y_lo, y_hi],\n", "    'xmin': xmin,\n", "    'ymin': ymin,\n", "    'features': int(len(coords_df)),\n", "    'type': crs_info['type'],\n", "    'confidence': crs_info['confidence']\n", "}\n", "\n", "with open(CONFIG_OUT, \"w\") as f:\n", "    json.dump(config, f, indent=2)\n", "\n", "print(f\"[INFO] Config written to {CONFIG_OUT}\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 4}