{"cells": [{"cell_type": "markdown", "id": "41c717df", "metadata": {}, "source": ["## Pile Detection – Single Site Style Data Preparation\n", "\n", "This notebook adapts the proven single-site validation workflow (RES → RCPS) for multi-source integration, combining IFC data, Buffer KML, and point cloud inputs.  \n", "The Buffer KML serves as the primary ground truth, with polygons converted to centroids for patch extraction. Patch parameters match the successful RES configuration (3 m radius, 64 points, 22 features).\n", "\n", "We train a classical ML model (Gradient Boosting) using these patches, validating performance against known pile locations to measure detection rate. IFC data is used for confidence boosting rather than as a primary training source.\n", "\n", "**Output:** Preprocessed training dataset for pile detection, aligned to point cloud CRS.\n", "\n", "**Author:** Adapted from successful single-site approach  \n", "**Date:** August 2025  \n", "**Project:** As-Built Foundation Analysis\n"]}, {"cell_type": "code", "execution_count": 152, "id": "7ce3b466", "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "import pandas as pd\n", "import open3d as o3d\n", "import geopandas as gpd\n", "from scipy.spatial import cKDTree\n", "from sklearn.ensemble import GradientBoostingClassifier\n", "from sklearn.model_selection import train_test_split\n", "from sklearn.metrics import accuracy_score, f1_score\n", "from pathlib import Path\n", "import pickle\n", "import json\n", "from datetime import datetime\n", "import matplotlib.pyplot as plt\n", "import warnings\n", "warnings.filterwarnings('ignore')\n", "\n", "# Set global random seed for reproducibility\n", "np.random.seed(42)\n"]}, {"cell_type": "code", "execution_count": 153, "id": "bce04cde", "metadata": {}, "outputs": [], "source": ["# Configuration\n", "PATCH_RADIUS = 3.0          # From RES success (was 8.0 in failed approaches)\n", "TARGET_PATCH_SIZE = 64      # From RES success (was 1024 in failed approaches)\n", "MIN_POINTS = 20\n", "SITE_NAME = \"trino_enel\"\n", "TARGET_CRS = \"EPSG:32632\"   # UTM Zone 32N (Europe)\n", "\n", "POINT_CLOUD_PATH = \"../../../../data/processed/trino_enel/denoising/trino_enel_denoised_for_registration.ply\"\n", "BUFFER_KML_PATH = \"../../../../data/raw/trino_enel/kml/pile.kml\"\n", "IFC_CSV_PATH = \"../../../../data/processed/trino_enel/ifc_metadata//GRE.EEC.S.00.IT.P.14353.00.265_coordinates.csv\"\n", "OUTPUT_DIR = f\"output_runs/trino_single_site_{datetime.now().strftime('%Y%m%d_%H%M%S')}\""]}, {"cell_type": "code", "execution_count": 154, "id": "8e8ab28b", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Configuration:\n", "  Site: trino_enel\n", "  Target CRS: EPSG:32632\n", "  Patch radius: 3.0m\n", "  Points per patch: 64\n", "  Output: output_runs/trino_single_site_20250812_211901\n"]}], "source": ["# Create output directory\n", "output_dir = Path(OUTPUT_DIR)\n", "output_dir.mkdir(parents=True, exist_ok=True)\n", "\n", "print(f\"Configuration:\")\n", "print(f\"  Site: {SITE_NAME}\")\n", "print(f\"  Target CRS: {TARGET_CRS}\")\n", "print(f\"  Patch radius: {PATCH_RADIUS}m\")\n", "print(f\"  Points per patch: {TARGET_PATCH_SIZE}\")\n", "print(f\"  Output: {OUTPUT_DIR}\")"]}, {"cell_type": "code", "execution_count": 155, "id": "b23ae9ac", "metadata": {}, "outputs": [], "source": ["## Load Data"]}, {"cell_type": "code", "execution_count": 156, "id": "1880fb95", "metadata": {}, "outputs": [], "source": ["def load_point_cloud(path):\n", "    \"\"\"Load point cloud as numpy array\"\"\"\n", "    pc = o3d.io.read_point_cloud(path)\n", "    points = np.asarray(pc.points)\n", "    print(f\"Loaded {len(points):,} points\")\n", "    return points\n", "\n", "def load_buffer_kml(path, target_crs=\"EPSG:32632\"):\n", "    \"\"\"Load Buffer KML and return pile locations in target CRS\"\"\"\n", "    gdf = gpd.read_file(path, driver=\"KML\")\n", "    if gdf.crs is None:\n", "        gdf.set_crs(\"EPSG:4326\", inplace=True)\n", "    \n", "    gdf_utm = gdf.to_crs(target_crs)\n", "    pile_locations = [\n", "        [geom.x, geom.y] if geom.geom_type == \"Point\" else [geom.centroid.x, geom.centroid.y]\n", "        for geom in gdf_utm.geometry\n", "    ]\n", "    \n", "    print(f\"Loaded {len(pile_locations)} pile locations\")\n", "    return np.array(pile_locations)\n", "\n", "def load_ifc_csv(path):\n", "    \"\"\"Load IFC pile coordinates from CSV\"\"\"\n", "    try:\n", "        df = pd.read_csv(path)\n", "        x_col = next((c for c in df.columns if \"x\" in c.lower() or \"east\" in c.lower()), None)\n", "        y_col = next((c for c in df.columns if \"y\" in c.lower() or \"north\" in c.lower()), None)\n", "\n", "        if x_col and y_col:\n", "            coord_df = df[[x_col, y_col]].copy()\n", "            coord_df[x_col] = pd.to_numeric(coord_df[x_col], errors='coerce')\n", "            coord_df[y_col] = pd.to_numeric(coord_df[y_col], errors='coerce')\n", "            coord_df = coord_df.dropna()\n", "            \n", "            if len(coord_df) > 0:\n", "                print(f\"Loaded {len(coord_df):,} IFC coordinates\")\n", "                return coord_df.values\n", "        \n", "        print(\"No IFC coordinates loaded\")\n", "        return None\n", "    except:\n", "        print(\"No IFC data available\")\n", "        return None\n", "\n", "def load_data():\n", "    \"\"\"Load multi-source data\"\"\"\n", "    points = load_point_cloud(POINT_CLOUD_PATH)\n", "    buffer_piles = load_buffer_kml(BUFFER_KML_PATH)\n", "    ifc_piles = load_ifc_csv(IFC_CSV_PATH)\n", "    return points, buffer_piles, ifc_piles"]}, {"cell_type": "markdown", "id": "26d6ba5f", "metadata": {}, "source": ["## Filter point cloud"]}, {"cell_type": "code", "execution_count": 157, "id": "aaf18de8", "metadata": {}, "outputs": [], "source": ["def filter_point_cloud_around_piles(points, pile_locations, buffer_radius=10.0):\n", "    \"\"\"Return points within buffer_radius of any pile location.\"\"\"\n", "    pile_tree = cKDTree(pile_locations)\n", "    distances, _ = pile_tree.query(points[:, :2], k=1)\n", "\n", "    mask = distances <= buffer_radius\n", "    filtered_points = points[mask]\n", "\n", "    print(f\"Filtered to {len(filtered_points):,} points around pile zones\")\n", "    return filtered_points"]}, {"cell_type": "markdown", "id": "b51c75da", "metadata": {}, "source": ["## IFC enhancement"]}, {"cell_type": "code", "execution_count": 158, "id": "ed0fe57f", "metadata": {}, "outputs": [], "source": ["def enhance_with_ifc_confidence(buffer_piles, ifc_piles):\n", "    \"\"\"Use IFC to add confidence scores to buffer pile locations\"\"\"\n", "    \n", "    if ifc_piles is None:\n", "        print(\"No IFC data available. Using buffer piles with medium confidence.\")\n", "        return [{'pile_id': f'trino_pile_{i}', \n", "                'x': pile[0], 'y': pile[1], \n", "                'confidence': 'medium', \n", "                'source': 'buffer_only',\n", "                'ifc_distance': -1,\n", "                'site': SITE_NAME} \n", "                for i, pile in enumerate(buffer_piles)]\n", "    \n", "    print(f\"Enhancing {len(buffer_piles)} buffer piles with IFC confidence...\")\n", "    \n", "    ifc_tree = cKDTree(ifc_piles)\n", "    enhanced_piles = []\n", "    \n", "    confidence_counts = {'high': 0, 'medium': 0, 'low': 0}\n", "    \n", "    for i, buffer_pile in enumerate(buffer_piles):\n", "        # Find nearest IFC pile\n", "        distance, nearest_idx = ifc_tree.query(buffer_pile)\n", "        \n", "        # Assign confidence based on distance to IFC design\n", "        if distance < 2.0:\n", "            confidence = 'high'\n", "            source = 'buffer_ifc_matched'\n", "        elif distance < 5.0:\n", "            confidence = 'medium'\n", "            source = 'buffer_ifc_close'\n", "        else:\n", "            confidence = 'low'\n", "            source = 'buffer_only'\n", "        \n", "        confidence_counts[confidence] += 1\n", "        \n", "        enhanced_piles.append({\n", "            'pile_id': f'trino_pile_{i}',\n", "            'x': buffer_pile[0],\n", "            'y': buffer_pile[1],\n", "            'confidence': confidence,\n", "            'source': source,\n", "            'ifc_distance': distance,\n", "            'site': SITE_NAME\n", "        })\n", "    \n", "    print(f\"Confidence distribution:\")\n", "    for conf, count in confidence_counts.items():\n", "        print(f\"   {conf.capitalize()}: {count} piles\")\n", "    \n", "    return enhanced_piles\n"]}, {"cell_type": "markdown", "id": "b91e1d6f", "metadata": {}, "source": ["## Feature extraction"]}, {"cell_type": "code", "execution_count": 159, "id": "fe5bbd40", "metadata": {}, "outputs": [], "source": ["def extract_22_engineering_features(patch_points, center_on_pile=True):\n", "    \"\"\"Extract 22 engineering features from a point cloud patch.\"\"\"\n", "    if patch_points is None or len(patch_points) == 0:\n", "        return np.zeros(22, dtype=np.float32)\n", "\n", "    x, y, z = patch_points[:, 0], patch_points[:, 1], patch_points[:, 2]\n", "\n", "    if center_on_pile:\n", "        ref_x, ref_y = 0.0, 0.0\n", "    else:\n", "        ref_x, ref_y = np.mean(x), np.mean(y)\n", "\n", "    radial = np.sqrt((x - ref_x)**2 + (y - ref_y)**2)\n", "    height_above_min = z - np.min(z)\n", "\n", "    try:\n", "        feats = [\n", "            x.mean(), x.std(), x.max() - x.min(),\n", "            y.mean(), y.std(), y.max() - y.min(),\n", "            z.mean(), z.std(), z.max() - z.min(),\n", "\n", "            height_above_min.mean(), height_above_min.std(),\n", "            np.percentile(height_above_min, 75), np.percentile(height_above_min, 25),\n", "\n", "            radial.mean(), radial.std(), radial.min(), radial.max(),\n", "\n", "            len(patch_points),\n", "            x.std() / (y.std() + 1e-6),\n", "            z.std() / (x.std() + y.std() + 1e-6),\n", "            np.percentile(radial, 90),\n", "            np.mean(height_above_min > height_above_min.mean())\n", "        ]\n", "        return np.array(feats, dtype=np.float32)\n", "    except Exception:\n", "        return np.zeros(22, dtype=np.float32)"]}, {"cell_type": "markdown", "id": "f9f8a788", "metadata": {}, "source": ["## Subsampling & Patch extraction"]}, {"cell_type": "code", "execution_count": 160, "id": "ec6edcd8", "metadata": {}, "outputs": [], "source": ["def subsample_patch(patch_points, target_size=TARGET_PATCH_SIZE):\n", "    \"\"\"Subsample patch to target size\"\"\"\n", "    if len(patch_points) <= target_size:\n", "        return patch_points\n", "    \n", "    np.random.seed(42)\n", "    indices = np.random.choice(len(patch_points), target_size, replace=False)\n", "    return patch_points[indices]"]}, {"cell_type": "code", "execution_count": 161, "id": "7bf5eeb2", "metadata": {}, "outputs": [], "source": ["def extract_patch(points, location, point_tree, radius=PATCH_RADIUS, center_on_pile=False):\n", "    \"\"\"Extract patch around a location. `location` can be dict {'x','y'} or [x,y].\"\"\"\n", "    if isinstance(location, dict):\n", "        coord = [location['x'], location['y']]\n", "    else:\n", "        coord = list(location)\n", "\n", "    indices = point_tree.query_ball_point(coord, radius)\n", "    if not indices or len(indices) < MIN_POINTS:\n", "        return None\n", "\n", "    patch_points = points[indices]\n", "    patch_points = subsample_patch(patch_points, TARGET_PATCH_SIZE)\n", "\n", "    if center_on_pile:\n", "        center = np.array([coord[0], coord[1], np.mean(patch_points[:, 2])])\n", "        processed = patch_points - center\n", "        features = extract_22_engineering_features(processed, center_on_pile=True)\n", "        return {\n", "            'features': features,\n", "            'centered_patch': processed,\n", "            'original_points': len(indices),\n", "            'patch_center': center\n", "        }\n", "    else:\n", "        features = extract_22_engineering_features(patch_points, center_on_pile=False)\n", "        return {\n", "            'features': features,\n", "            'raw_patch': patch_points,\n", "            'original_points': len(indices),\n", "            'patch_location': coord\n", "        }"]}, {"cell_type": "markdown", "id": "12264718", "metadata": {}, "source": ["## Negative sampling"]}, {"cell_type": "code", "execution_count": 162, "id": "a91d0d79", "metadata": {}, "outputs": [], "source": ["def create_negative_samples(points, pile_data, point_tree, n_negatives, center_on_pile=False):\n", "    \"\"\"Create negative samples by randomly sampling far-from-pile locations.\"\"\"\n", "    pile_coords = np.array([[p['x'], p['y']] for p in pile_data])\n", "    pile_tree = cKDTree(pile_coords)\n", "\n", "    buffer = 50.0\n", "    x_min, x_max = points[:, 0].min() + buffer, points[:, 0].max() - buffer\n", "    y_min, y_max = points[:, 1].min() + buffer, points[:, 1].max() - buffer\n", "\n", "    negatives_f, negatives_meta = [], []\n", "    attempts = 0\n", "    max_attempts = n_negatives * 6\n", "    min_distance = PATCH_RADIUS * 2\n", "\n", "    while len(negatives_f) < n_negatives and attempts < max_attempts:\n", "        # Random location (same as original)\n", "        x = np.random.uniform(x_min, x_max)\n", "        y = np.random.uniform(y_min, y_max)\n", "\n", "        dist, _ = pile_tree.query([x, y])\n", "        if dist > min_distance:\n", "            loc = {'x': x, 'y': y} if center_on_pile else [x, y]\n", "            patch = extract_patch(points, loc, point_tree, center_on_pile=center_on_pile)\n", "            if patch is not None:\n", "                negatives_f.append(patch['features'])\n", "                negatives_meta.append({\n", "                    'pile_id': f'negative_{len(negatives_f)}',\n", "                    'x': x, 'y': y,\n", "                    'confidence': 'synthetic',\n", "                    'source': 'negative_sampling',\n", "                    'label': 0,\n", "                    'patch_type': 'negative',\n", "                    'original_points': patch['original_points'],\n", "                    'site': SITE_NAME\n", "                })\n", "        attempts += 1\n", "\n", "    return negatives_f, negatives_meta"]}, {"cell_type": "markdown", "id": "1fe578eb", "metadata": {}, "source": ["## Dataset creation"]}, {"cell_type": "code", "execution_count": 163, "id": "b92e4e5e", "metadata": {}, "outputs": [], "source": ["def create_dataset(points, enhanced_pile_data, mode='biased'):\n", "    \"\"\"Create dataset for pile patch extraction. mode in {'biased','unbiased'}.\"\"\"\n", "    point_tree = cKDTree(points[:, :2])\n", "    pos_f, pos_meta = [], []\n", "\n", "    center_flag = True if mode == 'biased' else False\n", "\n", "    for pile in enhanced_pile_data:\n", "        loc = pile if center_flag else [pile['x'], pile['y']]\n", "        patch = extract_patch(points, loc, point_tree, center_on_pile=center_flag)\n", "        if patch is not None:\n", "            pos_f.append(patch['features'])\n", "            pos_meta.append({**pile, 'label': 1, 'patch_type': 'positive', 'original_points': patch['original_points']})\n", "\n", "    print(f\"Extracted {len(pos_f)} positive patches ({mode.upper()})\")\n", "\n", "    neg_f, neg_meta = create_negative_samples(points, enhanced_pile_data, point_tree, len(pos_f), center_on_pile=center_flag)\n", "    print(f\"Created {len(neg_f)} negative patches ({mode.upper()})\")\n", "\n", "    all_features = pos_f + neg_f\n", "    all_meta = pos_meta + neg_meta\n", "    all_labels = [1] * len(pos_f) + [0] * len(neg_f)\n", "\n", "    return np.array(all_features), np.array(all_labels), all_meta"]}, {"cell_type": "markdown", "id": "defb6ddb", "metadata": {}, "source": ["## Validation"]}, {"cell_type": "code", "execution_count": 164, "id": "5e01b20a", "metadata": {}, "outputs": [], "source": ["def validate_on_known_piles(model, pile_locations, points, center_on_pile=True):\n", "    \"\"\"Validate on known pile locations\"\"\"\n", "    point_tree = cKDTree(points[:, :2])\n", "    detected, confidences = [], []\n", "    failed = 0\n", "\n", "    for pile in pile_locations:\n", "        loc = pile if center_on_pile else [pile['x'], pile['y']]\n", "        patch = extract_patch(points, loc, point_tree, center_on_pile=center_on_pile)\n", "        if patch is not None:\n", "            feats = patch['features'].reshape(1, -1)\n", "            pred = model.predict(feats)[0]\n", "            conf = model.predict_proba(feats)[0][1]\n", "            detected.append(pred)\n", "            confidences.append(conf)\n", "        else:\n", "            failed += 1\n", "\n", "    detection_rate = float(np.mean(detected)) if detected else 0.0\n", "    avg_conf = float(np.mean(confidences)) if confidences else 0.0\n", "\n", "    if detection_rate >= 0.9:\n", "        status = 'EXCELLENT'\n", "    elif detection_rate >= 0.8:\n", "        status = 'GOOD'\n", "    elif detection_rate >= 0.6:\n", "        status = 'MODERATE'\n", "    else:\n", "        status = 'POOR'\n", "\n", "    return {\n", "        'detection_rate': detection_rate,\n", "        'avg_confidence': avg_conf,\n", "        'status': status,\n", "        'detected_piles': detected,\n", "        'detection_confidences': confidences,\n", "        'failed_extractions': failed,\n", "        'total_tested': len(pile_locations)\n", "    }"]}, {"cell_type": "markdown", "id": "dc2bdc66", "metadata": {}, "source": ["## Model Training "]}, {"cell_type": "code", "execution_count": 165, "id": "57bf5d4f", "metadata": {}, "outputs": [], "source": ["def train_model(X_train, y_train):\n", "    model = GradientBoostingClassifier(n_estimators=100, max_depth=6, learning_rate=0.1, random_state=42)\n", "    model.fit(X_train, y_train)\n", "    train_acc = accuracy_score(y_train, model.predict(X_train))\n", "    print(f\"Training accuracy: {train_acc:.3f}\")\n", "    return model"]}, {"cell_type": "markdown", "id": "15247857", "metadata": {}, "source": ["## Comparison procedure"]}, {"cell_type": "code", "execution_count": 166, "id": "0b47d08d", "metadata": {}, "outputs": [], "source": ["def compare_biased_vs_unbiased_approaches(points, enhanced_pile_data, filtered_points):\n", "    print(\"COMPARING BIASED vs UNBIASED APPROACHES\")\n", "    \n", "    # Create datasets\n", "    X_biased, y_biased, _ = create_dataset(filtered_points, enhanced_pile_data, mode='biased')\n", "    X_unbiased, y_unbiased, _ = create_dataset(filtered_points, enhanced_pile_data, mode='unbiased')\n", "    \n", "    # Train models\n", "    X_train_b, _, y_train_b, _ = train_test_split(X_biased, y_biased, test_size=0.2, random_state=42, stratify=y_biased)\n", "    X_train_u, _, y_train_u, _ = train_test_split(X_unbiased, y_unbiased, test_size=0.2, random_state=42, stratify=y_unbiased)\n", "    \n", "    model_biased = train_model(X_train_b, y_train_b)\n", "    model_unbiased = train_model(X_train_u, y_train_u)\n", "    \n", "    # Validate on ground truth\n", "    biased_results = validate_on_known_piles(model_biased, enhanced_pile_data, filtered_points, center_on_pile=True)\n", "    unbiased_results = validate_on_known_piles(model_unbiased, enhanced_pile_data, filtered_points, center_on_pile=False)\n", "    \n", "    # Results\n", "    biased_rate = biased_results['detection_rate']\n", "    unbiased_rate = unbiased_results['detection_rate']\n", "    \n", "    print(f\"Biased:   {biased_rate*100:.1f}% detection ({biased_results['status']})\")\n", "    print(f\"Unbiased: {unbiased_rate*100:.1f}% detection ({unbiased_results['status']})\")\n", "    \n", "    # Recommendation\n", "    if abs(biased_rate - unbiased_rate) < 0.05:\n", "        recommendation = \"Both approaches valid - choose based on use case\"\n", "    elif unbiased_rate > biased_rate + 0.05:\n", "        recommendation = \"Unbiased approach recommended\"\n", "    else:\n", "        recommendation = \"Biased approach recommended for verification tasks\"\n", "    \n", "    print(f\"Recommendation: {recommendation}\")\n", "    \n", "    comparison_results = {\n", "        'biased_results': biased_results,\n", "        'unbiased_results': unbiased_results,\n", "        'recommendation': recommendation,\n", "        'better_approach': 'biased' if biased_rate > unbiased_rate else 'unbiased'\n", "    }\n", "    \n", "    return comparison_results, model_biased, model_unbiased"]}, {"cell_type": "markdown", "id": "c0766c9d", "metadata": {}, "source": ["## Save Results"]}, {"cell_type": "code", "execution_count": 167, "id": "b6fcc2ea", "metadata": {}, "outputs": [], "source": ["def save_results(results_dict, model, output_dir):\n", "    timestamp = datetime.now().strftime(\"%Y%m%d_%H%M%S\")\n", "    results_file = output_dir / f\"trino_results_{timestamp}.json\"\n", "    with open(results_file, 'w') as f:\n", "        json.dump(results_dict, f, indent=2, default=str)\n", "    model_file = output_dir / f\"trino_model_{timestamp}.pkl\"\n", "    with open(model_file, 'wb') as f:\n", "        pickle.dump(model, f)\n", "    print(f\"Results saved to {output_dir}\")\n", "    return results_file\n"]}, {"cell_type": "code", "execution_count": 168, "id": "30667c17", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Loading data...\n", "Loaded 983,884 points\n", "Loaded 1288 pile locations\n", "No IFC coordinates loaded\n"]}], "source": ["print(\"Loading data...\")\n", "points, buffer_piles, ifc_piles = load_data()\n", "\n", "if points is None or buffer_piles is None:\n", "    print(\"Failed to load required data. Please check file paths.\")\n", "    exit()"]}, {"cell_type": "code", "execution_count": 169, "id": "dd04632f", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Processing data...\n", "Filtered to 63,211 points around pile zones\n", "No IFC data available. Using buffer piles with medium confidence.\n"]}], "source": ["print(\"Processing data...\")\n", "filtered_points = filter_point_cloud_around_piles(points, buffer_piles)\n", "enhanced_pile_data = enhance_with_ifc_confidence(buffer_piles, ifc_piles)"]}, {"cell_type": "code", "execution_count": 170, "id": "bc3dbf04", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Creating dataset (BIASED)...\n", "Extracted 203 positive patches (BIASED)\n", "Created 7 negative patches (BIASED)\n"]}], "source": ["print(\"Creating dataset (BIASED)...\")\n", "X, y, metadata = create_dataset(filtered_points, enhanced_pile_data, mode='biased')"]}, {"cell_type": "code", "execution_count": 171, "id": "b91b2456", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Training model...\n", "Training accuracy: 1.000\n"]}], "source": ["print(\"Training model...\")\n", "X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42, stratify=y)\n", "model = train_model(X_train, y_train)"]}, {"cell_type": "code", "execution_count": 172, "id": "2aea4f8e", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Validating on known pile locations...\n"]}], "source": ["print(\"Validating on known pile locations...\")\n", "validation_results = validate_on_known_piles(model, enhanced_pile_data, filtered_points)"]}, {"cell_type": "code", "execution_count": 173, "id": "31b71be1", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Test accuracy: 0.929\n", "Test F1-score: 0.963\n"]}], "source": ["# Test set evaluation\n", "if len(X_test) > 0:\n", "    y_pred = model.predict(X_test)\n", "    test_accuracy = accuracy_score(y_test, y_pred)\n", "    test_f1 = f1_score(y_test, y_pred)\n", "    print(f\"Test accuracy: {test_accuracy:.3f}\")\n", "    print(f\"Test F1-score: {test_f1:.3f}\")\n", "else:\n", "    print(\"No test data available for evaluation.\")"]}, {"cell_type": "code", "execution_count": 174, "id": "9ee31a8f", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "============================================================\n", "CONDUCTING BIASED vs UNBIASED COMPARISON\n", "============================================================\n", "COMPARING BIASED vs UNBIASED APPROACHES\n", "Extracted 203 positive patches (BIASED)\n", "Created 11 negative patches (BIASED)\n", "Extracted 203 positive patches (UNBIASED)\n", "Created 9 negative patches (UNBIASED)\n", "Training accuracy: 1.000\n", "Training accuracy: 1.000\n", "Biased:   99.0% detection (EXCELLENT)\n", "Unbiased: 100.0% detection (EXCELLENT)\n", "Recommendation: Both approaches valid - choose based on use case\n"]}], "source": ["# BIASED vs UNBIASED COMPARISON\n", "print(\"\\n\" + \"=\"*60)\n", "print(\"CONDUCTING BIASED vs UNBIASED COMPARISON\")\n", "print(\"=\"*60)\n", "\n", "# Run the comparison\n", "comparison_results, model_biased, model_unbiased = compare_biased_vs_unbiased_approaches(\n", "    points, enhanced_pile_data, filtered_points\n", ")"]}, {"cell_type": "code", "execution_count": 175, "id": "0009cfd8", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "Feature importance analysis...\n"]}], "source": ["# Feature importance analysis for BIASED approach\n", "print(\"\\nFeature importance analysis...\")\n", "\n", "feature_names = [\n", "    'x_mean', 'x_std', 'x_range',\n", "    'y_mean', 'y_std', 'y_range', \n", "    'z_mean', 'z_std', 'z_range',\n", "    'height_mean', 'height_std', 'height_75p', 'height_25p',\n", "    'radial_mean', 'radial_std', 'radial_min', 'radial_max',\n", "    'point_count', 'aspect_ratio', 'height_width_ratio', 'radial_90p', 'elevated_ratio'\n", "]\n"]}, {"cell_type": "code", "execution_count": 176, "id": "bffd15e6", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "Top 10 Most Important Features (BIASED approach):\n", "  z_mean: 0.294\n", "  radial_min: 0.140\n", "  radial_std: 0.123\n", "  y_range: 0.113\n", "  y_std: 0.078\n", "  x_std: 0.039\n", "  radial_mean: 0.029\n", "  height_width_ratio: 0.029\n", "  radial_90p: 0.027\n", "  y_mean: 0.027\n"]}], "source": ["importances_biased = model_biased.feature_importances_\n", "feature_importance_biased = pd.DataFrame({\n", "    'feature': feature_names,\n", "    'importance': importances_biased\n", "}).sort_values('importance', ascending=False)\n", "\n", "print(\"\\nTop 10 Most Important Features (BIASED approach):\")\n", "for i, row in feature_importance_biased.head(10).iterrows():\n", "    print(f\"  {row['feature']}: {row['importance']:.3f}\")\n"]}, {"cell_type": "code", "execution_count": 177, "id": "43225dcd", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "Top 10 Most Important Features (UNBIASED approach):\n", "  radial_90p: 0.213\n", "  y_range: 0.140\n", "  x_range: 0.126\n", "  radial_mean: 0.094\n", "  radial_std: 0.088\n", "  z_std: 0.082\n", "  y_std: 0.057\n", "  z_range: 0.048\n", "  height_75p: 0.034\n", "  height_mean: 0.027\n"]}], "source": ["# Feature importance analysis for UNBIASED approach\n", "feature_names_unbiased = [\n", "    'x_std', 'y_std', 'z_std',  # Spread instead of mean position\n", "    'x_range', 'y_range', 'z_range',\n", "    'x_iqr', 'y_iqr', 'z_iqr',\n", "    'height_mean', 'height_std', 'height_75p', 'height_25p',\n", "    'radial_mean', 'radial_std', 'radial_min', 'radial_max',\n", "    'point_count', 'aspect_ratio', 'height_width_ratio', 'radial_90p', 'elevated_ratio'\n", "]\n", "\n", "importances_unbiased = model_unbiased.feature_importances_\n", "feature_importance_unbiased = pd.DataFrame({\n", "    'feature': feature_names_unbiased,\n", "    'importance': importances_unbiased\n", "}).sort_values('importance', ascending=False)\n", "\n", "print(\"\\nTop 10 Most Important Features (UNBIASED approach):\")\n", "for i, row in feature_importance_unbiased.head(10).iterrows():\n", "    print(f\"  {row['feature']}: {row['importance']:.3f}\")\n"]}, {"cell_type": "code", "execution_count": 178, "id": "6d8c61df", "metadata": {}, "outputs": [], "source": ["# Save results\n", "results = {\n", "    'site': SITE_NAME,\n", "    'timestamp': datetime.now().isoformat(),\n", "    'biased_detection_rate': comparison_results['biased_results']['detection_rate'],\n", "    'unbiased_detection_rate': comparison_results['unbiased_results']['detection_rate'],\n", "    'recommendation': comparison_results['recommendation'],\n", "    'better_approach': comparison_results['better_approach']\n", "}"]}, {"cell_type": "code", "execution_count": 179, "id": "53e1e068", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Results saved to output_runs/trino_single_site_20250812_211901\n", "COMPARISON COMPLETE!\n", "Biased: 99.0% detection\n", "Unbiased: 100.0% detection\n", "Recommendation: Both approaches valid - choose based on use case\n", "Results saved in: output_runs/trino_single_site_20250812_211901\n"]}], "source": ["# Save results\n", "results_file = save_results(results, model_biased, output_dir)\n", "\n", "print(f\"COMPARISON COMPLETE!\")\n", "print(f\"Biased: {results['biased_detection_rate']*100:.1f}% detection\")\n", "print(f\"Unbiased: {results['unbiased_detection_rate']*100:.1f}% detection\")\n", "print(f\"Recommendation: {results['recommendation']}\")\n", "print(f\"Results saved in: {output_dir}\")"]}, {"cell_type": "code", "execution_count": 180, "id": "e81a42d9", "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 800x600 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["Biased: 99.0%, Unbiased: 100.0%\n"]}], "source": ["def create_simple_comparison():\n", "    \"\"\"Simple comparison plot\"\"\"\n", "    \n", "    approaches = ['Biased\\n(Pile-Centered)', 'Unbiased\\n(Patch-Centered)']\n", "    detection_rates = [\n", "        comparison_results['biased_results']['detection_rate'],\n", "        comparison_results['unbiased_results']['detection_rate']\n", "    ]\n", "    \n", "    plt.figure(figsize=(8, 6))\n", "    bars = plt.bar(approaches, detection_rates, color=['orange', 'blue'], alpha=0.7)\n", "    plt.title('Detection Rate Comparison')\n", "    plt.ylabel('Detection Rate')\n", "    plt.ylim(0, 1.1)\n", "    \n", "    # Add values on bars\n", "    for bar, rate in zip(bars, detection_rates):\n", "        plt.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.01,\n", "                f'{rate:.1%}', ha='center', fontweight='bold')\n", "    \n", "    plt.tight_layout()\n", "    plt.savefig(f\"{OUTPUT_DIR}/comparison.png\", dpi=300, bbox_inches='tight')\n", "    plt.show()\n", "    \n", "    print(f\"Biased: {detection_rates[0]:.1%}, Unbiased: {detection_rates[1]:.1%}\")\n", "\n", "create_simple_comparison()"]}, {"cell_type": "markdown", "id": "deep_learning_section", "metadata": {}, "source": ["## Deep Learning Architecture Comparison\n", "\n", "This section implements PointNet++ and DGCNN architectures for comparison with the classical ML approach."]}, {"cell_type": "code", "execution_count": 181, "id": "dl_imports", "metadata": {}, "outputs": [], "source": ["# Deep Learning imports\n", "import torch\n", "import torch.nn as nn\n", "import torch.nn.functional as F\n", "from torch.utils.data import DataLoader, TensorDataset\n", "import time"]}, {"cell_type": "code", "execution_count": 182, "id": "dl_data_prep", "metadata": {}, "outputs": [], "source": ["def extract_coordinates_for_deep_learning(points, enhanced_pile_data, point_tree):\n", "    \"\"\"Extract 3D coordinates for deep learning (same patches as classical ML)\"\"\"\n", "    \n", "    print(f\"Extracting 3D coordinates for deep learning from {len(enhanced_pile_data)} locations...\")\n", "    \n", "    positive_patches = []\n", "    positive_labels = []\n", "    positive_metadata = []\n", "    \n", "    # Extract positive patches - SAME locations as classical ML\n", "    for pile_data in enhanced_pile_data:\n", "        pile_coord = [pile_data['x'], pile_data['y']]\n", "        \n", "        # Find points within radius (SAME as classical ML)\n", "        indices = point_tree.query_ball_point(pile_coord, PATCH_RADIUS)\n", "        \n", "        if len(indices) < MIN_POINTS:\n", "            continue\n", "            \n", "        patch_points = points[indices]\n", "        \n", "        # Subsample to target size (SAME as classical ML)\n", "        if len(patch_points) > TARGET_PATCH_SIZE:\n", "            sampled_indices = np.random.choice(len(patch_points), TARGET_PATCH_SIZE, replace=False)\n", "            patch_points = patch_points[sampled_indices]\n", "        elif len(patch_points) < TARGET_PATCH_SIZE:\n", "            # Pad with duplicated points + small noise\n", "            needed = TARGET_PATCH_SIZE - len(patch_points)\n", "            duplicates = []\n", "            for _ in range(needed):\n", "                idx = np.random.randint(0, len(patch_points))\n", "                duplicate = patch_points[idx].copy()\n", "                # Add small noise to avoid identical points\n", "                duplicate += np.random.normal(0, 0.01, 3)\n", "                duplicates.append(duplicate)\n", "            patch_points = np.vstack([patch_points, duplicates])\n", "        \n", "        positive_patches.append(patch_points)\n", "        positive_labels.append(1)\n", "        positive_metadata.append(pile_data)\n", "    \n", "    print(f\"Extracted {len(positive_patches)} positive coordinate patches\")\n", "    \n", "    # Create negative samples - SAME strategy as classical ML\n", "    negative_patches, negative_labels, negative_metadata = create_negative_coordinate_patches(\n", "        points, enhanced_pile_data, point_tree, len(positive_patches)\n", "    )\n", "    \n", "    print(f\"Created {len(negative_patches)} negative coordinate patches\")\n", "    \n", "    # Combine all patches\n", "    all_patches = positive_patches + negative_patches\n", "    all_labels = positive_labels + negative_labels\n", "    all_metadata = positive_metadata + negative_metadata\n", "    \n", "    print(f\"Total coordinate dataset: {len(all_patches)} patches\")\n", "    print(f\"   Shape per patch: {all_patches[0].shape} (points, XYZ)\")\n", "    \n", "    return np.array(all_patches), np.array(all_labels), all_metadata\n", "\n", "def create_negative_coordinate_patches(points, pile_data, point_tree, n_negatives):\n", "    \"\"\"Create negative coordinate patches (same strategy as classical ML)\"\"\"\n", "    \n", "    pile_coords = np.array([[p['x'], p['y']] for p in pile_data])\n", "    pile_tree = cKDTree(pile_coords)\n", "    \n", "    buffer = 50.0\n", "    x_min, x_max = points[:, 0].min() + buffer, points[:, 0].max() - buffer\n", "    y_min, y_max = points[:, 1].min() + buffer, points[:, 1].max() - buffer\n", "    \n", "    negative_patches = []\n", "    negative_labels = []\n", "    negative_metadata = []\n", "    \n", "    attempts = 0\n", "    max_attempts = n_negatives * 10\n", "    min_distance = PATCH_RADIUS * 2\n", "    \n", "    np.random.seed(42)\n", "    \n", "    while len(negative_patches) < n_negatives and attempts < max_attempts:\n", "        attempts += 1\n", "        \n", "        x = np.random.uniform(x_min, x_max)\n", "        y = np.random.uniform(y_min, y_max)\n", "        \n", "        dist, _ = pile_tree.query([x, y])\n", "        if dist > min_distance:\n", "            indices = point_tree.query_ball_point([x, y], PATCH_RADIUS)\n", "            \n", "            if len(indices) >= MIN_POINTS:\n", "                patch_points = points[indices]\n", "                \n", "                # Same subsampling as positive patches\n", "                if len(patch_points) > TARGET_PATCH_SIZE:\n", "                    sampled_indices = np.random.choice(len(patch_points), TARGET_PATCH_SIZE, replace=False)\n", "                    patch_points = patch_points[sampled_indices]\n", "                elif len(patch_points) < TARGET_PATCH_SIZE:\n", "                    needed = TARGET_PATCH_SIZE - len(patch_points)\n", "                    duplicates = []\n", "                    for _ in range(needed):\n", "                        idx = np.random.randint(0, len(patch_points))\n", "                        duplicate = patch_points[idx].copy()\n", "                        duplicate += np.random.normal(0, 0.01, 3)\n", "                        duplicates.append(duplicate)\n", "                    patch_points = np.vstack([patch_points, duplicates])\n", "                \n", "                negative_patches.append(patch_points)\n", "                negative_labels.append(0)\n", "                negative_metadata.append({\n", "                    'pile_id': f'negative_{len(negative_patches)}',\n", "                    'x': x, 'y': y,\n", "                    'confidence': 'synthetic'\n", "                })\n", "    \n", "    return negative_patches, negative_labels, negative_metadata\n", "\n", "def prepare_data_for_pytorch(coordinate_patches, coordinate_labels):\n", "    \"\"\"Prepare coordinate data for PyTorch training\"\"\"\n", "    \n", "    # Split data\n", "    X_train, X_test, y_train, y_test = train_test_split(\n", "        coordinate_patches, coordinate_labels, \n", "        test_size=0.2, random_state=42, stratify=coordinate_labels\n", "    )\n", "    \n", "    # Convert to tensors\n", "    X_train_tensor = torch.FloatTensor(X_train)\n", "    X_test_tensor = torch.FloatTensor(X_test)\n", "    y_train_tensor = torch.LongTensor(y_train)\n", "    y_test_tensor = torch.LongTensor(y_test)\n", "    \n", "    print(f\"PyTorch data prepared:\")\n", "    print(f\"  Train: {X_train_tensor.shape} patches, {y_train_tensor.shape} labels\")\n", "    print(f\"  Test: {X_test_tensor.shape} patches, {y_test_tensor.shape} labels\")\n", "    \n", "    return X_train_tensor, X_test_tensor, y_train_tensor, y_test_tensor"]}, {"cell_type": "code", "execution_count": 183, "id": "pointnet_model", "metadata": {}, "outputs": [], "source": ["class SimplePointNet(nn.Module):\n", "    \"\"\"Simplified PointNet++ for pile detection comparison\"\"\"\n", "    \n", "    def __init__(self, num_classes=2, num_points=64):\n", "        super(SimplePointNet, self).__init__()\n", "        \n", "        # Feature extraction layers\n", "        self.conv1 = nn.Conv1d(3, 64, 1)\n", "        self.conv2 = nn.Conv1d(64, 128, 1)\n", "        self.conv3 = nn.Conv1d(128, 256, 1)\n", "        \n", "        # Batch normalization\n", "        self.bn1 = nn.BatchNorm1d(64)\n", "        self.bn2 = nn.BatchNorm1d(128)\n", "        self.bn3 = nn.BatchNorm1d(256)\n", "        \n", "        # Classification layers\n", "        self.fc1 = nn.Linear(256, 128)\n", "        self.fc2 = nn.<PERSON>ar(128, 64)\n", "        self.fc3 = nn.Linear(64, num_classes)\n", "        \n", "        self.dropout = nn.Dropout(0.3)\n", "        \n", "    def forward(self, x):\n", "        # x shape: (batch_size, num_points, 3)\n", "        x = x.transpose(2, 1)  # (batch_size, 3, num_points)\n", "        \n", "        # Feature extraction\n", "        x = F.relu(self.bn1(self.conv1(x)))\n", "        x = F.relu(self.bn2(self.conv2(x)))\n", "        x = <PERSON>.relu(self.bn3(self.conv3(x)))\n", "        \n", "        # Global max pooling\n", "        x = torch.max(x, 2)[0]\n", "        \n", "        # Classification\n", "        x = F.relu(self.fc1(x))\n", "        x = self.dropout(x)\n", "        x = <PERSON>.relu(self.fc2(x))\n", "        x = self.dropout(x)\n", "        x = self.fc3(x)\n", "        \n", "        return x"]}, {"cell_type": "code", "execution_count": 184, "id": "dgcnn_helpers", "metadata": {}, "outputs": [], "source": ["def get_knn_graph(x, k=20):\n", "    \"\"\"Get k-nearest neighbor graph for DGCNN\"\"\"\n", "    batch_size, num_dims, num_points = x.size()\n", "    \n", "    # Compute pairwise distances\n", "    inner = -2 * torch.matmul(x.transpose(2, 1), x)\n", "    xx = torch.sum(x**2, dim=1, keepdim=True)\n", "    pairwise_distance = -xx - inner - xx.transpose(2, 1)\n", "    \n", "    idx = pairwise_distance.topk(k=k, dim=-1)[1]  # (batch_size, num_points, k)\n", "    \n", "    return idx\n", "\n", "def get_edge_features(x, idx):\n", "    \"\"\"Get edge features for DGCNN\"\"\"\n", "    batch_size, num_dims, num_points = x.size()\n", "    k = idx.size(-1)\n", "    \n", "    # Reshape x for gathering: (batch_size, num_dims, num_points) -> (batch_size * num_points, num_dims)\n", "    x_reshaped = x.transpose(2, 1).contiguous().view(batch_size * num_points, num_dims)\n", "    \n", "    # Adjust indices for batch dimension\n", "    batch_idx = torch.arange(batch_size, device=x.device).view(-1, 1, 1) * num_points\n", "    idx_adjusted = idx + batch_idx\n", "    \n", "    # Gather neighbor features\n", "    neighbors = x_reshaped[idx_adjusted.view(-1)].view(batch_size, num_points, k, num_dims)\n", "    neighbors = neighbors.permute(0, 3, 1, 2)  # (batch_size, num_dims, num_points, k)\n", "    \n", "    # Central point features\n", "    central = x.unsqueeze(-1).expand(-1, -1, -1, k)\n", "    \n", "    # Edge features: [central, neighbor - central]\n", "    edge_features = torch.cat([central, neighbors - central], dim=1)\n", "    \n", "    return edge_features"]}, {"cell_type": "code", "execution_count": 185, "id": "dgcnn_model", "metadata": {}, "outputs": [], "source": ["class SimpleDGCNN(nn.Module):\n", "    \"\"\"Simplified DGCNN for pile detection comparison\"\"\"\n", "    \n", "    def __init__(self, num_classes=2, k=20):\n", "        super(SimpleDGCN<PERSON>, self).__init__()\n", "        self.k = k\n", "        \n", "        # Edge convolution layers\n", "        self.conv1 = nn.Conv2d(6, 64, 1)    # 3*2 = 6 (central + edge)\n", "        self.conv2 = nn.Conv2d(128, 128, 1)  # 64*2 = 128\n", "        self.conv3 = nn.Conv2d(256, 256, 1)  # 128*2 = 256\n", "        \n", "        # Batch normalization\n", "        self.bn1 = nn.BatchNorm2d(64)\n", "        self.bn2 = nn.BatchNorm2d(128)\n", "        self.bn3 = nn.BatchNorm2d(256)\n", "        \n", "        # Classification head - Conv1d layers (same as original)\n", "        self.conv4 = nn.Conv1d(448, 256, 1)  # 64+128+256 = 448\n", "        self.conv5 = nn.Conv1d(256, 128, 1)\n", "        self.conv6 = nn.Conv1d(128, num_classes, 1)\n", "        \n", "        self.bn4 = nn.<PERSON>chNorm1d(256)\n", "        self.bn5 = nn.<PERSON>ch<PERSON>orm1d(128)\n", "        \n", "        self.dropout = nn.Dropout(0.3)\n", "        \n", "    def forward(self, x):\n", "        batch_size = x.size(0)\n", "        x = x.transpose(2, 1)  # (batch_size, 3, num_points)\n", "        \n", "        # First edge convolution\n", "        idx = get_knn_graph(x, k=self.k)\n", "        x1 = get_edge_features(x, idx)\n", "        x1 = <PERSON>.relu(self.bn1(self.conv1(x1)))\n", "        x1 = x1.max(dim=-1, keepdim=False)[0]\n", "        \n", "        # Second edge convolution\n", "        idx = get_knn_graph(x1, k=self.k)\n", "        x2 = get_edge_features(x1, idx)\n", "        x2 = <PERSON>.relu(self.bn2(self.conv2(x2)))\n", "        x2 = x2.max(dim=-1, keepdim=False)[0]\n", "        \n", "        # Third edge convolution\n", "        idx = get_knn_graph(x2, k=self.k)\n", "        x3 = get_edge_features(x2, idx)\n", "        x3 = <PERSON><PERSON>relu(self.bn3(self.conv3(x3)))\n", "        x3 = x3.max(dim=-1, keepdim=False)[0]\n", "        \n", "        # Concatenate features\n", "        x = torch.cat((x1, x2, x3), dim=1)\n", "        \n", "        # Classification head with Conv1d\n", "        x = <PERSON>.relu(self.bn4(self.conv4(x)))\n", "        x = self.dropout(x)\n", "        x = <PERSON>.relu(self.bn5(self.conv5(x)))\n", "        x = self.dropout(x)\n", "        x = self.conv6(x)\n", "        \n", "        # Global max pooling for final classification\n", "        x = torch.max(x, 2)[0]\n", "        \n", "        return x"]}, {"cell_type": "code", "execution_count": 186, "id": "dl_training", "metadata": {}, "outputs": [], "source": ["def train_deep_learning_model(model, X_train_tensor, y_train_tensor, X_test_tensor, y_test_tensor, model_name, epochs=50):\n", "    \"\"\"Train a deep learning model\"\"\"\n", "    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')\n", "    model = model.to(device)\n", "    \n", "    # Move tensors to device\n", "    X_train_tensor = X_train_tensor.to(device)\n", "    y_train_tensor = y_train_tensor.to(device)\n", "    X_test_tensor = X_test_tensor.to(device)\n", "    y_test_tensor = y_test_tensor.to(device)\n", "    \n", "    # Create data loaders\n", "    train_dataset = TensorDataset(X_train_tensor, y_train_tensor)\n", "    train_loader = DataLoader(train_dataset, batch_size=32, shuffle=True)\n", "    \n", "    # Optimizer and loss\n", "    optimizer = torch.optim.Adam(model.parameters(), lr=0.001)\n", "    criterion = nn.CrossEntropyLoss()\n", "    \n", "    start_time = time.time()\n", "    best_test_acc = 0.0\n", "    \n", "    print(f\"Training {model_name}...\")\n", "    \n", "    for epoch in range(epochs):\n", "        model.train()\n", "        train_loss = 0.0\n", "        train_correct = 0\n", "        \n", "        for batch_x, batch_y in train_loader:\n", "            optimizer.zero_grad()\n", "            outputs = model(batch_x)\n", "            loss = criterion(outputs, batch_y)\n", "            loss.backward()\n", "            optimizer.step()\n", "            \n", "            train_loss += loss.item()\n", "            _, predicted = torch.max(outputs.data, 1)\n", "            train_correct += (predicted == batch_y).sum().item()\n", "        \n", "        # Test evaluation\n", "        model.eval()\n", "        with torch.no_grad():\n", "            test_outputs = model(X_test_tensor)\n", "            _, test_predicted = torch.max(test_outputs.data, 1)\n", "            test_correct = (test_predicted == y_test_tensor).sum().item()\n", "            test_acc = test_correct / len(y_test_tensor)\n", "            \n", "            if test_acc > best_test_acc:\n", "                best_test_acc = test_acc\n", "        \n", "        train_acc = train_correct / len(y_train_tensor)\n", "        \n", "        if (epoch + 1) % 10 == 0:\n", "            print(f\"  Epoch {epoch+1}/{epochs}: Train Acc: {train_acc:.3f}, Test Acc: {test_acc:.3f}\")\n", "    \n", "    training_time = time.time() - start_time\n", "    \n", "    print(f\"{model_name} training complete!\")\n", "    print(f\"   Final test accuracy: {test_acc:.3f}\")\n", "    print(f\"   Best test accuracy: {best_test_acc:.3f}\")\n", "    print(f\"   Training time: {training_time:.1f}s\")\n", "    \n", "    return {\n", "        'model': model,\n", "        'final_accuracy': test_acc,\n", "        'best_accuracy': best_test_acc,\n", "        'training_time': training_time\n", "    }"]}, {"cell_type": "code", "execution_count": 187, "id": "dl_execution", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Preparing data for deep learning models...\n", "Extracting 3D coordinates for deep learning from 1288 locations...\n", "Extracted 203 positive coordinate patches\n", "Created 12 negative coordinate patches\n", "Total coordinate dataset: 215 patches\n", "   Shape per patch: (64, 3) (points, XYZ)\n", "PyTorch data prepared:\n", "  Train: torch.<PERSON><PERSON>([172, 64, 3]) patches, torch.<PERSON><PERSON>([172]) labels\n", "  Test: torch.<PERSON><PERSON>([43, 64, 3]) patches, torch.<PERSON><PERSON>([43]) labels\n", "Deep learning data ready!\n", "  Same 215 patches as classical ML\n", "  Same 203 positive samples\n", "  Ready for PointNet++ and DGCNN training\n"]}], "source": ["# Prepare data for deep learning\n", "print(\"Preparing data for deep learning models...\")\n", "point_tree = cKDTree(filtered_points[:, :2])\n", "coordinate_patches, coordinate_labels, coordinate_metadata = extract_coordinates_for_deep_learning(\n", "    filtered_points, enhanced_pile_data, point_tree\n", ")\n", "\n", "# Prepare for PyTorch\n", "X_train_dl, X_test_dl, y_train_dl, y_test_dl = prepare_data_for_pytorch(\n", "    coordinate_patches, coordinate_labels\n", ")\n", "\n", "print(f\"Deep learning data ready!\")\n", "print(f\"  Same {len(coordinate_patches)} patches as classical ML\")\n", "print(f\"  Same {np.sum(coordinate_labels)} positive samples\")\n", "print(f\"  Ready for PointNet++ and DGCNN training\")"]}, {"cell_type": "code", "execution_count": 188, "id": "dl_training_execution", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "Training Deep Learning Models\n", "==================================================\n", "Training PointNet++...\n", "  Epoch 10/50: Train Acc: 0.942, Test Acc: 0.953\n", "  Epoch 20/50: Train Acc: 0.942, Test Acc: 0.953\n", "  Epoch 30/50: Train Acc: 0.942, Test Acc: 0.953\n", "  Epoch 40/50: Train Acc: 0.942, Test Acc: 0.953\n", "  Epoch 50/50: Train Acc: 0.942, Test Acc: 0.953\n", "PointNet++ training complete!\n", "   Final test accuracy: 0.953\n", "   Best test accuracy: 0.953\n", "   Training time: 8.7s\n", "\n", "Training DGCNN...\n", "  Epoch 10/50: Train Acc: 0.942, Test Acc: 0.953\n", "  Epoch 20/50: Train Acc: 0.942, Test Acc: 0.953\n", "  Epoch 30/50: Train Acc: 0.930, Test Acc: 0.953\n", "  Epoch 40/50: Train Acc: 0.948, Test Acc: 0.953\n", "  Epoch 50/50: Train Acc: 0.942, Test Acc: 0.953\n", "DGCNN training complete!\n", "   Final test accuracy: 0.953\n", "   Best test accuracy: 0.953\n", "   Training time: 58.9s\n"]}], "source": ["print(\"\\nTraining Deep Learning Models\")\n", "print(\"=\" * 50)\n", "\n", "# Train PointNet++\n", "pointnet_model = SimplePointNet(num_classes=2, num_points=TARGET_PATCH_SIZE)\n", "pointnet_results = train_deep_learning_model(\n", "    pointnet_model, X_train_dl, y_train_dl, X_test_dl, y_test_dl, \n", "    \"PointNet++\", epochs=50\n", ")\n", "\n", "print()\n", "\n", "# Train DGCNN  \n", "dgcnn_model = SimpleDGCNN(num_classes=2, k=10)  # Smaller k for 64 points\n", "dgcnn_results = train_deep_learning_model(\n", "    dgcnn_model, X_train_dl, y_train_dl, X_test_dl, y_test_dl,\n", "    \"DGCNN\", epochs=50\n", ")"]}, {"cell_type": "code", "execution_count": 189, "id": "dl_comparison", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "Architecture Comparison Results\n", "==================================================\n", "Performance Summary:\n", "  Classical ML (Biased):   99.0% detection\n", "  Classical ML (Unbiased): 100.0% detection\n", "  PointNet++:              95.3% test accuracy\n", "  DGCNN:                   95.3% test accuracy\n", "\n", "Training Time Comparison:\n", "  Classical ML:  ~30 seconds\n", "  PointNet++:    8.7s\n", "  DGCNN:         58.9s\n", "\n", "Results saved:\n", "  Architecture comparison: architecture_comparison_20250812_212011.csv\n", "  Pile locations & results: pile_detection_results_20250812_212011.csv\n", "  Comprehensive JSON: architecture_comparison_20250812_212011.json\n"]}], "source": ["# Compare all approaches\n", "print(\"\\nArchitecture Comparison Results\")\n", "print(\"=\" * 50)\n", "\n", "print(f\"Performance Summary:\")\n", "print(f\"  Classical ML (Biased):   {results['biased_detection_rate']*100:.1f}% detection\")\n", "print(f\"  Classical ML (Unbiased): {results['unbiased_detection_rate']*100:.1f}% detection\")\n", "print(f\"  PointNet++:              {pointnet_results['final_accuracy']*100:.1f}% test accuracy\")\n", "print(f\"  DGCNN:                   {dgcnn_results['final_accuracy']*100:.1f}% test accuracy\")\n", "\n", "print(f\"\\nTraining Time Comparison:\")\n", "print(f\"  Classical ML:  ~30 seconds\")\n", "print(f\"  PointNet++:    {pointnet_results['training_time']:.1f}s\")\n", "print(f\"  DGCNN:         {dgcnn_results['training_time']:.1f}s\")\n", "\n", "# Save comprehensive results\n", "comprehensive_results = {\n", "    'timestamp': datetime.now().isoformat(),\n", "    'site': SITE_NAME,\n", "    'classical_ml': {\n", "        'biased_detection_rate': results['biased_detection_rate'],\n", "        'unbiased_detection_rate': results['unbiased_detection_rate'],\n", "        'recommendation': results['recommendation']\n", "    },\n", "    'deep_learning': {\n", "        'pointnet': {\n", "            'final_accuracy': pointnet_results['final_accuracy'],\n", "            'best_accuracy': pointnet_results['best_accuracy'],\n", "            'training_time': pointnet_results['training_time']\n", "        },\n", "        'dgcnn': {\n", "            'final_accuracy': dgcnn_results['final_accuracy'],\n", "            'best_accuracy': dgcnn_results['best_accuracy'],\n", "            'training_time': dgcnn_results['training_time']\n", "        }\n", "    }\n", "}\n", "\n", "# Save to JSON file\n", "comp_results_file = output_dir / f\"architecture_comparison_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json\"\n", "with open(comp_results_file, 'w') as f:\n", "    json.dump(comprehensive_results, f, indent=2, default=str)\n", "\n", "# Save to CSV file for easy analysis\n", "csv_results = []\n", "csv_results.append({\n", "    'Architecture': 'Classical ML (Biased)',\n", "    'Accuracy/Detection_Rate': results['biased_detection_rate'],\n", "    'Training_Time_Seconds': 30,\n", "    'Model_Type': 'Classical',\n", "    'Notes': 'Biased detection approach'\n", "})\n", "csv_results.append({\n", "    'Architecture': 'Classical ML (Unbiased)',\n", "    'Accuracy/Detection_Rate': results['unbiased_detection_rate'],\n", "    'Training_Time_Seconds': 30,\n", "    'Model_Type': 'Classical',\n", "    'Notes': 'Unbiased detection approach'\n", "})\n", "csv_results.append({\n", "    'Architecture': 'PointNet++',\n", "    'Accuracy/Detection_Rate': pointnet_results['final_accuracy'],\n", "    'Training_Time_Seconds': pointnet_results['training_time'],\n", "    'Model_Type': 'Deep Learning',\n", "    'Notes': f\"Best accuracy: {pointnet_results['best_accuracy']:.3f}\"\n", "})\n", "csv_results.append({\n", "    'Architecture': 'DGCNN',\n", "    'Accuracy/Detection_Rate': dgcnn_results['final_accuracy'],\n", "    'Training_Time_Seconds': dgcnn_results['training_time'],\n", "    'Model_Type': 'Deep Learning',\n", "    'Notes': f\"Best accuracy: {dgcnn_results['best_accuracy']:.3f}\"\n", "})\n", "\n", "csv_df = pd.DataFrame(csv_results)\n", "csv_file = output_dir / f\"architecture_comparison_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv\"\n", "csv_df.to_csv(csv_file, index=False)\n", "\n", "# Save pile locations with detection results (like original notebook)\n", "pile_locations_data = []\n", "for i, pile_data in enumerate(enhanced_pile_data):\n", "    if i < len(validation_results['detected_piles']):\n", "        detected = validation_results['detected_piles'][i]\n", "        confidence = validation_results['detection_confidences'][i]\n", "        detection_status = 'Detected' if detected == 1 else 'Missed'\n", "    else:\n", "        detected = 0\n", "        confidence = 0.0\n", "        detection_status = 'Not Tested'\n", "    \n", "    pile_locations_data.append({\n", "        'pile_id': pile_data['pile_id'],\n", "        'utm_x': pile_data['x'],\n", "        'utm_y': pile_data['y'],\n", "        'confidence_level': pile_data['confidence'],\n", "        'source': pile_data['source'],\n", "        'predicted_pile': detected,\n", "        'prediction_confidence': confidence,\n", "        'detection_status': detection_status,\n", "        'site_name': SITE_NAME,\n", "        'validation_type': 'architecture_comparison',\n", "        'ifc_distance': pile_data.get('ifc_distance', -1),\n", "        'crs': TARGET_CRS\n", "    })\n", "\n", "pile_locations_df = pd.DataFrame(pile_locations_data)\n", "pile_locations_file = output_dir / f\"pile_detection_results_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv\"\n", "pile_locations_df.to_csv(pile_locations_file, index=False)\n", "\n", "print(f\"\\nResults saved:\")\n", "print(f\"  Architecture comparison: {csv_file.name}\")\n", "print(f\"  Pile locations & results: {pile_locations_file.name}\")\n", "print(f\"  Comprehensive JSON: {comp_results_file.name}\")"]}, {"cell_type": "code", "execution_count": 190, "id": "deep_learning_qgis_export", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Creating deep learning QGIS export...\n", "Converted coordinates from EPSG:32632 to WGS84\n", "Deep learning QGIS export created: deep_learning_qgis_20250812_212027.csv\n", "   Total piles: 1288\n", "   PointNet++ detected: 203 (15.8%)\n", "   DGCNN detected: 203 (15.8%)\n", "\n", "Final results summary:\n", "  Architecture comparison: architecture_comparison_20250812_212011.csv\n", "  Classical ML pile results: pile_detection_results_20250812_212011.csv\n", "  Deep learning QGIS export: deep_learning_qgis_20250812_212027.csv\n", "  Comprehensive JSON: architecture_comparison_20250812_212011.json\n"]}], "source": ["# Create deep learning results CSV for QGIS visualization\n", "def create_deep_learning_qgis_export(enhanced_pile_data, pointnet_model, dgcnn_model, output_dir):\n", "    \"\"\"Create QGIS-ready CSV with deep learning predictions for each pile location\"\"\"\n", "    \n", "    print(\"Creating deep learning QGIS export...\")\n", "    \n", "    # Get predictions from both models\n", "    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')\n", "    pointnet_model.eval()\n", "    dgcnn_model.eval()\n", "    \n", "    qgis_data = []\n", "    \n", "    # For each pile location, get deep learning predictions\n", "    for i, pile_data in enumerate(enhanced_pile_data):\n", "        # Extract patch for this pile location\n", "        point_tree = cKDTree(filtered_points[:, :2])\n", "        pile_coord = [pile_data['x'], pile_data['y']]\n", "        indices = point_tree.query_ball_point(pile_coord, PATCH_RADIUS)\n", "        \n", "        if len(indices) >= MIN_POINTS:\n", "            patch_points = filtered_points[indices]\n", "            \n", "            # Standardize patch size\n", "            if len(patch_points) > TARGET_PATCH_SIZE:\n", "                sampled_indices = np.random.choice(len(patch_points), TARGET_PATCH_SIZE, replace=False)\n", "                patch_points = patch_points[sampled_indices]\n", "            elif len(patch_points) < TARGET_PATCH_SIZE:\n", "                needed = TARGET_PATCH_SIZE - len(patch_points)\n", "                duplicates = []\n", "                for _ in range(needed):\n", "                    idx = np.random.randint(0, len(patch_points))\n", "                    duplicate = patch_points[idx].copy()\n", "                    duplicate += np.random.normal(0, 0.01, 3)\n", "                    duplicates.append(duplicate)\n", "                patch_points = np.vstack([patch_points, duplicates])\n", "            \n", "            # Convert to tensor\n", "            patch_tensor = torch.FloatTensor(patch_points).unsqueeze(0).to(device)\n", "            \n", "            # Get predictions\n", "            with torch.no_grad():\n", "                pointnet_output = pointnet_model(patch_tensor)\n", "                dgcnn_output = dgcnn_model(patch_tensor)\n", "                \n", "                pointnet_prob = torch.softmax(pointnet_output, dim=1)[0, 1].cpu().item()\n", "                dgcnn_prob = torch.softmax(dgcnn_output, dim=1)[0, 1].cpu().item()\n", "                \n", "                pointnet_pred = 1 if pointnet_prob > 0.5 else 0\n", "                dgcnn_pred = 1 if dgcnn_prob > 0.5 else 0\n", "        else:\n", "            pointnet_pred = 0\n", "            dgcnn_pred = 0\n", "            pointnet_prob = 0.0\n", "            dgcnn_prob = 0.0\n", "        \n", "        qgis_data.append({\n", "            'pile_id': pile_data['pile_id'],\n", "            'utm_x': pile_data['x'],\n", "            'utm_y': pile_data['y'],\n", "            'confidence_level': pile_data['confidence'],\n", "            'source': pile_data['source'],\n", "            'pointnet_prediction': pointnet_pred,\n", "            'pointnet_confidence': pointnet_prob,\n", "            'dgcnn_prediction': dgcnn_pred,\n", "            'dgcnn_confidence': dgcnn_prob,\n", "            'site_name': SITE_NAME,\n", "            'model_type': 'deep_learning',\n", "            'crs': TARGET_CRS\n", "        })\n", "    \n", "    results_df = pd.DataFrame(qgis_data)\n", "    \n", "    # Convert to geographic coordinates for QGIS\n", "    try:\n", "        from shapely.geometry import Point\n", "        \n", "        # Create GeoDataFrame with UTM coordinates\n", "        geometry = [Point(row['utm_x'], row['utm_y']) for _, row in results_df.iterrows()]\n", "        gdf = gpd.GeoDataFrame(results_df, geometry=geometry, crs=TARGET_CRS)\n", "        \n", "        # Convert to WGS84 for QGIS compatibility\n", "        gdf_wgs84 = gdf.to_crs('EPSG:4326')\n", "        results_df['longitude'] = gdf_wgs84.geometry.x\n", "        results_df['latitude'] = gdf_wgs84.geometry.y\n", "        \n", "        print(f'Converted coordinates from {TARGET_CRS} to WGS84')\n", "        \n", "    except Exception as e:\n", "        print(f'Could not convert coordinates: {e}')\n", "        results_df['longitude'] = 0.0\n", "        results_df['latitude'] = 0.0\n", "    \n", "    # Save CSV for QGIS\n", "    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')\n", "    csv_file = output_dir / f'deep_learning_qgis_{timestamp}.csv'\n", "    results_df.to_csv(csv_file, index=False)\n", "    \n", "    # Summary statistics\n", "    pointnet_detected = results_df['pointnet_prediction'].sum()\n", "    dgcnn_detected = results_df['dgcnn_prediction'].sum()\n", "    total_piles = len(results_df)\n", "    \n", "    print(f'Deep learning QGIS export created: {csv_file.name}')\n", "    print(f'   Total piles: {total_piles}')\n", "    print(f'   PointNet++ detected: {pointnet_detected} ({pointnet_detected/total_piles*100:.1f}%)')\n", "    print(f'   DGCNN detected: {dgcnn_detected} ({dgcnn_detected/total_piles*100:.1f}%)')\n", "    \n", "    return csv_file, results_df\n", "\n", "# Create deep learning QGIS export\n", "dl_qgis_file, dl_qgis_df = create_deep_learning_qgis_export(\n", "    enhanced_pile_data, pointnet_results['model'], dgcnn_results['model'], output_dir\n", ")\n", "\n", "print(f'\\nFinal results summary:')\n", "print(f'  Architecture comparison: {csv_file.name}')\n", "print(f'  Classical ML pile results: {pile_locations_file.name}')\n", "print(f'  Deep learning QGIS export: {dl_qgis_file.name}')\n", "print(f'  Comprehensive JSON: {comp_results_file.name}')"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 5}