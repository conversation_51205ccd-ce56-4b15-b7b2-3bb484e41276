import numpy as np
import pandas as pd
import open3d as o3d
import geopandas as gpd
from scipy.spatial import cKDTree
from sklearn.ensemble import GradientBoostingClassifier
from sklearn.model_selection import train_test_split
from sklearn.metrics import accuracy_score, f1_score
from pathlib import Path
import pickle
import json
from datetime import datetime
import matplotlib.pyplot as plt
import warnings
warnings.filterwarnings('ignore')


# Configuration
PATCH_RADIUS = 3.0          # From RES success (was 8.0 in failed approaches)
TARGET_PATCH_SIZE = 64      # From RES success (was 1024 in failed approaches)
MIN_POINTS = 20
SITE_NAME = "trino_enel"
TARGET_CRS = "EPSG:32632"   # UTM Zone 32N (Europe)

POINT_CLOUD_PATH = "../../../../data/processed/trino_enel/denoising/trino_enel_denoised_for_registration.ply"
BUFFER_KML_PATH = "../../../../data/raw/trino_enel/kml/pile.kml"
IFC_CSV_PATH = "../../../../data/processed/trino_enel/ifc_metadata//GRE.EEC.S.00.IT.P.14353.00.265_coordinates.csv"
OUTPUT_DIR = f"output_runs/trino_single_site_{datetime.now().strftime('%Y%m%d_%H%M%S')}"

# Create output directory
output_dir = Path(OUTPUT_DIR)
output_dir.mkdir(parents=True, exist_ok=True)

print(f"Configuration:")
print(f"  Site: {SITE_NAME}")
print(f"  Target CRS: {TARGET_CRS}")
print(f"  Patch radius: {PATCH_RADIUS}m")
print(f"  Points per patch: {TARGET_PATCH_SIZE}")
print(f"  Output: {OUTPUT_DIR}")

## Load Data

def load_point_cloud(path):
    """Load point cloud as numpy array"""
    pc = o3d.io.read_point_cloud(path)
    points = np.asarray(pc.points)
    print(f"Loaded {len(points):,} points")
    return points

def load_buffer_kml(path, target_crs="EPSG:32632"):
    """Load Buffer KML and return pile locations in target CRS"""
    gdf = gpd.read_file(path, driver="KML")
    if gdf.crs is None:
        gdf.set_crs("EPSG:4326", inplace=True)
    
    gdf_utm = gdf.to_crs(target_crs)
    pile_locations = [
        [geom.x, geom.y] if geom.geom_type == "Point" else [geom.centroid.x, geom.centroid.y]
        for geom in gdf_utm.geometry
    ]
    
    print(f"Loaded {len(pile_locations)} pile locations")
    return np.array(pile_locations)

def load_ifc_csv(path):
    """Load IFC pile coordinates from CSV"""
    try:
        df = pd.read_csv(path)
        x_col = next((c for c in df.columns if "x" in c.lower() or "east" in c.lower()), None)
        y_col = next((c for c in df.columns if "y" in c.lower() or "north" in c.lower()), None)

        if x_col and y_col:
            coord_df = df[[x_col, y_col]].copy()
            coord_df[x_col] = pd.to_numeric(coord_df[x_col], errors='coerce')
            coord_df[y_col] = pd.to_numeric(coord_df[y_col], errors='coerce')
            coord_df = coord_df.dropna()
            
            if len(coord_df) > 0:
                print(f"Loaded {len(coord_df):,} IFC coordinates")
                return coord_df.values
        
        print("No IFC coordinates loaded")
        return None
    except:
        print("No IFC data available")
        return None

def load_data():
    """Load multi-source data"""
    points = load_point_cloud(POINT_CLOUD_PATH)
    buffer_piles = load_buffer_kml(BUFFER_KML_PATH)
    ifc_piles = load_ifc_csv(IFC_CSV_PATH)
    return points, buffer_piles, ifc_piles

def filter_point_cloud_around_piles(points, pile_locations, buffer_radius=10.0):
    """Return points within buffer_radius of any pile location."""
    pile_tree = cKDTree(pile_locations)
    distances, _ = pile_tree.query(points[:, :2], k=1)

    mask = distances <= buffer_radius
    filtered_points = points[mask]

    print(f"Filtered to {len(filtered_points):,} points around pile zones")
    return filtered_points

def enhance_with_ifc_confidence(buffer_piles, ifc_piles):
    """Use IFC to add confidence scores to buffer pile locations"""
    
    if ifc_piles is None:
        print("No IFC data available. Using buffer piles with medium confidence.")
        return [{'pile_id': f'trino_pile_{i}', 
                'x': pile[0], 'y': pile[1], 
                'confidence': 'medium', 
                'source': 'buffer_only',
                'ifc_distance': -1,
                'site': SITE_NAME} 
                for i, pile in enumerate(buffer_piles)]
    
    print(f"Enhancing {len(buffer_piles)} buffer piles with IFC confidence...")
    
    ifc_tree = cKDTree(ifc_piles)
    enhanced_piles = []
    
    confidence_counts = {'high': 0, 'medium': 0, 'low': 0}
    
    for i, buffer_pile in enumerate(buffer_piles):
        # Find nearest IFC pile
        distance, nearest_idx = ifc_tree.query(buffer_pile)
        
        # Assign confidence based on distance to IFC design
        if distance < 2.0:
            confidence = 'high'
            source = 'buffer_ifc_matched'
        elif distance < 5.0:
            confidence = 'medium'
            source = 'buffer_ifc_close'
        else:
            confidence = 'low'
            source = 'buffer_only'
        
        confidence_counts[confidence] += 1
        
        enhanced_piles.append({
            'pile_id': f'trino_pile_{i}',
            'x': buffer_pile[0],
            'y': buffer_pile[1],
            'confidence': confidence,
            'source': source,
            'ifc_distance': distance,
            'site': SITE_NAME
        })
    
    print(f"Confidence distribution:")
    for conf, count in confidence_counts.items():
        print(f"   {conf.capitalize()}: {count} piles")
    
    return enhanced_piles


def extract_22_engineering_features(patch_points, center_on_pile=True):
    """Extract 22 engineering features from a point cloud patch."""
    if patch_points is None or len(patch_points) == 0:
        return np.zeros(22, dtype=np.float32)

    x, y, z = patch_points[:, 0], patch_points[:, 1], patch_points[:, 2]

    if center_on_pile:
        ref_x, ref_y = 0.0, 0.0
    else:
        ref_x, ref_y = np.mean(x), np.mean(y)

    radial = np.sqrt((x - ref_x)**2 + (y - ref_y)**2)
    height_above_min = z - np.min(z)

    try:
        feats = [
            x.mean(), x.std(), x.max() - x.min(),
            y.mean(), y.std(), y.max() - y.min(),
            z.mean(), z.std(), z.max() - z.min(),

            height_above_min.mean(), height_above_min.std(),
            np.percentile(height_above_min, 75), np.percentile(height_above_min, 25),

            radial.mean(), radial.std(), radial.min(), radial.max(),

            len(patch_points),
            x.std() / (y.std() + 1e-6),
            z.std() / (x.std() + y.std() + 1e-6),
            np.percentile(radial, 90),
            np.mean(height_above_min > height_above_min.mean())
        ]
        return np.array(feats, dtype=np.float32)
    except Exception:
        return np.zeros(22, dtype=np.float32)

def subsample_patch(patch_points, target_size=TARGET_PATCH_SIZE):
    """Subsample patch to target size"""
    if len(patch_points) <= target_size:
        return patch_points
    
    np.random.seed(42)
    indices = np.random.choice(len(patch_points), target_size, replace=False)
    return patch_points[indices]

def extract_patch(points, location, point_tree, radius=PATCH_RADIUS, center_on_pile=False):
    """Extract patch around a location. `location` can be dict {'x','y'} or [x,y]."""
    if isinstance(location, dict):
        coord = [location['x'], location['y']]
    else:
        coord = list(location)

    indices = point_tree.query_ball_point(coord, radius)
    if not indices or len(indices) < MIN_POINTS:
        return None

    patch_points = points[indices]
    patch_points = subsample_patch(patch_points, TARGET_PATCH_SIZE)

    if center_on_pile:
        center = np.array([coord[0], coord[1], np.mean(patch_points[:, 2])])
        processed = patch_points - center
        features = extract_22_engineering_features(processed, center_on_pile=True)
        return {
            'features': features,
            'centered_patch': processed,
            'original_points': len(indices),
            'patch_center': center
        }
    else:
        features = extract_22_engineering_features(patch_points, center_on_pile=False)
        return {
            'features': features,
            'raw_patch': patch_points,
            'original_points': len(indices),
            'patch_location': coord
        }

def create_negative_samples(points, pile_data, point_tree, n_negatives, center_on_pile=False):
    """Create negative samples by randomly sampling far-from-pile locations."""
    pile_coords = np.array([[p['x'], p['y']] for p in pile_data])
    pile_tree = cKDTree(pile_coords)

    buffer = 50.0
    x_min, x_max = points[:, 0].min() + buffer, points[:, 0].max() - buffer
    y_min, y_max = points[:, 1].min() + buffer, points[:, 1].max() - buffer

    negatives_f, negatives_meta = [], []
    attempts = 0
    max_attempts = n_negatives * 6
    min_distance = PATCH_RADIUS * 2

    rng = np.random.default_rng(0)
    while len(negatives_f) < n_negatives and attempts < max_attempts:
        x = float(rng.uniform(x_min, x_max))
        y = float(rng.uniform(y_min, y_max))

        dist, _ = pile_tree.query([x, y])
        if dist > min_distance:
            loc = {'x': x, 'y': y} if center_on_pile else [x, y]
            patch = extract_patch(points, loc, point_tree, center_on_pile=center_on_pile)
            if patch is not None:
                negatives_f.append(patch['features'])
                negatives_meta.append({
                    'pile_id': f'negative_{len(negatives_f)}',
                    'x': x, 'y': y,
                    'confidence': 'synthetic',
                    'source': 'negative_sampling',
                    'label': 0,
                    'patch_type': 'negative',
                    'original_points': patch['original_points'],
                    'site': SITE_NAME
                })
        attempts += 1

    return negatives_f, negatives_meta

def create_dataset(points, enhanced_pile_data, mode='biased'):
    """Create dataset for pile patch extraction. mode in {'biased','unbiased'}."""
    point_tree = cKDTree(points[:, :2])
    pos_f, pos_meta = [], []

    center_flag = True if mode == 'biased' else False

    for pile in enhanced_pile_data:
        loc = pile if center_flag else [pile['x'], pile['y']]
        patch = extract_patch(points, loc, point_tree, center_on_pile=center_flag)
        if patch is not None:
            pos_f.append(patch['features'])
            pos_meta.append({**pile, 'label': 1, 'patch_type': 'positive', 'original_points': patch['original_points']})

    print(f"Extracted {len(pos_f)} positive patches ({mode.upper()})")

    neg_f, neg_meta = create_negative_samples(points, enhanced_pile_data, point_tree, len(pos_f), center_on_pile=center_flag)
    print(f"Created {len(neg_f)} negative patches ({mode.upper()})")

    all_features = pos_f + neg_f
    all_meta = pos_meta + neg_meta
    all_labels = [1] * len(pos_f) + [0] * len(neg_f)

    return np.array(all_features), np.array(all_labels), all_meta

def validate_on_known_piles(model, pile_locations, points, center_on_pile=True):
    """Validate on known pile locations"""
    point_tree = cKDTree(points[:, :2])
    detected, confidences = [], []
    failed = 0

    for pile in pile_locations:
        loc = pile if center_on_pile else [pile['x'], pile['y']]
        patch = extract_patch(points, loc, point_tree, center_on_pile=center_on_pile)
        if patch is not None:
            feats = patch['features'].reshape(1, -1)
            pred = model.predict(feats)[0]
            conf = model.predict_proba(feats)[0][1]
            detected.append(pred)
            confidences.append(conf)
        else:
            failed += 1

    detection_rate = float(np.mean(detected)) if detected else 0.0
    avg_conf = float(np.mean(confidences)) if confidences else 0.0

    if detection_rate >= 0.9:
        status = 'EXCELLENT'
    elif detection_rate >= 0.8:
        status = 'GOOD'
    elif detection_rate >= 0.6:
        status = 'MODERATE'
    else:
        status = 'POOR'

    return {
        'detection_rate': detection_rate,
        'avg_confidence': avg_conf,
        'status': status,
        'detected_piles': detected,
        'detection_confidences': confidences,
        'failed_extractions': failed,
        'total_tested': len(pile_locations)
    }

def train_model(X_train, y_train):
    model = GradientBoostingClassifier(n_estimators=100, max_depth=6, learning_rate=0.1, random_state=42)
    model.fit(X_train, y_train)
    train_acc = accuracy_score(y_train, model.predict(X_train))
    print(f"Training accuracy: {train_acc:.3f}")
    return model

def compare_biased_vs_unbiased_approaches(points, enhanced_pile_data, filtered_points):
    print("COMPARING BIASED vs UNBIASED APPROACHES")
    
    # Create datasets
    X_biased, y_biased, _ = create_dataset(filtered_points, enhanced_pile_data, mode='biased')
    X_unbiased, y_unbiased, _ = create_dataset(filtered_points, enhanced_pile_data, mode='unbiased')
    
    # Train models
    X_train_b, _, y_train_b, _ = train_test_split(X_biased, y_biased, test_size=0.2, random_state=42, stratify=y_biased)
    X_train_u, _, y_train_u, _ = train_test_split(X_unbiased, y_unbiased, test_size=0.2, random_state=42, stratify=y_unbiased)
    
    model_biased = train_model(X_train_b, y_train_b)
    model_unbiased = train_model(X_train_u, y_train_u)
    
    # Validate on ground truth
    biased_results = validate_on_known_piles(model_biased, enhanced_pile_data, filtered_points, center_on_pile=True)
    unbiased_results = validate_on_known_piles(model_unbiased, enhanced_pile_data, filtered_points, center_on_pile=False)
    
    # Results
    biased_rate = biased_results['detection_rate']
    unbiased_rate = unbiased_results['detection_rate']
    
    print(f"Biased:   {biased_rate*100:.1f}% detection ({biased_results['status']})")
    print(f"Unbiased: {unbiased_rate*100:.1f}% detection ({unbiased_results['status']})")
    
    # Recommendation
    if abs(biased_rate - unbiased_rate) < 0.05:
        recommendation = "Both approaches valid - choose based on use case"
    elif unbiased_rate > biased_rate + 0.05:
        recommendation = "Unbiased approach recommended"
    else:
        recommendation = "Biased approach recommended for verification tasks"
    
    print(f"Recommendation: {recommendation}")
    
    comparison_results = {
        'biased_results': biased_results,
        'unbiased_results': unbiased_results,
        'recommendation': recommendation,
        'better_approach': 'biased' if biased_rate > unbiased_rate else 'unbiased'
    }
    
    return comparison_results, model_biased, model_unbiased

def save_results(results_dict, model, output_dir):
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    results_file = output_dir / f"trino_results_{timestamp}.json"
    with open(results_file, 'w') as f:
        json.dump(results_dict, f, indent=2, default=str)
    model_file = output_dir / f"trino_model_{timestamp}.pkl"
    with open(model_file, 'wb') as f:
        pickle.dump(model, f)
    print(f"Results saved to {output_dir}")
    return results_file


print("Loading data...")
points, buffer_piles, ifc_piles = load_data()

if points is None or buffer_piles is None:
    print("Failed to load required data. Please check file paths.")
    exit()

print("Processing data...")
filtered_points = filter_point_cloud_around_piles(points, buffer_piles)
enhanced_pile_data = enhance_with_ifc_confidence(buffer_piles, ifc_piles)

print("Creating dataset (BIASED)...")
X, y, metadata = create_dataset(filtered_points, enhanced_pile_data, mode='biased')

print("Training model...")
X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42, stratify=y)
model = train_model(X_train, y_train)

print("Validating on known pile locations...")
validation_results = validate_on_known_piles(model, enhanced_pile_data, filtered_points)

# Test set evaluation
if len(X_test) > 0:
    y_pred = model.predict(X_test)
    test_accuracy = accuracy_score(y_test, y_pred)
    test_f1 = f1_score(y_test, y_pred)
    print(f"Test accuracy: {test_accuracy:.3f}")
    print(f"Test F1-score: {test_f1:.3f}")
else:
    print("No test data available for evaluation.")

# BIASED vs UNBIASED COMPARISON
print("\n" + "="*60)
print("CONDUCTING BIASED vs UNBIASED COMPARISON")
print("="*60)

# Run the comparison
comparison_results, model_biased, model_unbiased = compare_biased_vs_unbiased_approaches(
    points, enhanced_pile_data, filtered_points
)

# Feature importance analysis for BIASED approach
print("\nFeature importance analysis...")

feature_names = [
    'x_mean', 'x_std', 'x_range',
    'y_mean', 'y_std', 'y_range', 
    'z_mean', 'z_std', 'z_range',
    'height_mean', 'height_std', 'height_75p', 'height_25p',
    'radial_mean', 'radial_std', 'radial_min', 'radial_max',
    'point_count', 'aspect_ratio', 'height_width_ratio', 'radial_90p', 'elevated_ratio'
]


importances_biased = model_biased.feature_importances_
feature_importance_biased = pd.DataFrame({
    'feature': feature_names,
    'importance': importances_biased
}).sort_values('importance', ascending=False)

print("\nTop 10 Most Important Features (BIASED approach):")
for i, row in feature_importance_biased.head(10).iterrows():
    print(f"  {row['feature']}: {row['importance']:.3f}")


# Feature importance analysis for UNBIASED approach
feature_names_unbiased = [
    'x_std', 'y_std', 'z_std',  # Spread instead of mean position
    'x_range', 'y_range', 'z_range',
    'x_iqr', 'y_iqr', 'z_iqr',
    'height_mean', 'height_std', 'height_75p', 'height_25p',
    'radial_mean', 'radial_std', 'radial_min', 'radial_max',
    'point_count', 'aspect_ratio', 'height_width_ratio', 'radial_90p', 'elevated_ratio'
]

importances_unbiased = model_unbiased.feature_importances_
feature_importance_unbiased = pd.DataFrame({
    'feature': feature_names_unbiased,
    'importance': importances_unbiased
}).sort_values('importance', ascending=False)

print("\nTop 10 Most Important Features (UNBIASED approach):")
for i, row in feature_importance_unbiased.head(10).iterrows():
    print(f"  {row['feature']}: {row['importance']:.3f}")


# Save results
results = {
    'site': SITE_NAME,
    'timestamp': datetime.now().isoformat(),
    'biased_detection_rate': comparison_results['biased_results']['detection_rate'],
    'unbiased_detection_rate': comparison_results['unbiased_results']['detection_rate'],
    'recommendation': comparison_results['recommendation'],
    'better_approach': comparison_results['better_approach']
}

# Save results
results_file = save_results(results, model_biased, output_dir)

print(f"COMPARISON COMPLETE!")
print(f"Biased: {results['biased_detection_rate']*100:.1f}% detection")
print(f"Unbiased: {results['unbiased_detection_rate']*100:.1f}% detection")
print(f"Recommendation: {results['recommendation']}")
print(f"Results saved in: {output_dir}")

def create_simple_comparison():
    """Simple comparison plot"""
    
    approaches = ['Biased\n(Pile-Centered)', 'Unbiased\n(Patch-Centered)']
    detection_rates = [
        comparison_results['biased_results']['detection_rate'],
        comparison_results['unbiased_results']['detection_rate']
    ]
    
    plt.figure(figsize=(8, 6))
    bars = plt.bar(approaches, detection_rates, color=['orange', 'blue'], alpha=0.7)
    plt.title('Detection Rate Comparison')
    plt.ylabel('Detection Rate')
    plt.ylim(0, 1.1)
    
    # Add values on bars
    for bar, rate in zip(bars, detection_rates):
        plt.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.01,
                f'{rate:.1%}', ha='center', fontweight='bold')
    
    plt.tight_layout()
    plt.savefig(f"{OUTPUT_DIR}/comparison.png", dpi=300, bbox_inches='tight')
    plt.show()
    
    print(f"Biased: {detection_rates[0]:.1%}, Unbiased: {detection_rates[1]:.1%}")

create_simple_comparison()

# Deep Learning imports
import torch
import torch.nn as nn
import torch.nn.functional as F
from torch.utils.data import DataLoader, TensorDataset
import time

def extract_coordinates_for_deep_learning(points, enhanced_pile_data, point_tree):
    """Extract 3D coordinates for deep learning (same patches as classical ML)"""
    
    print(f"Extracting 3D coordinates for deep learning from {len(enhanced_pile_data)} locations...")
    
    positive_patches = []
    positive_labels = []
    positive_metadata = []
    
    # Extract positive patches - SAME locations as classical ML
    for pile_data in enhanced_pile_data:
        pile_coord = [pile_data['x'], pile_data['y']]
        
        # Find points within radius (SAME as classical ML)
        indices = point_tree.query_ball_point(pile_coord, PATCH_RADIUS)
        
        if len(indices) < MIN_POINTS:
            continue
            
        patch_points = points[indices]
        
        # Subsample to target size (SAME as classical ML)
        if len(patch_points) > TARGET_PATCH_SIZE:
            sampled_indices = np.random.choice(len(patch_points), TARGET_PATCH_SIZE, replace=False)
            patch_points = patch_points[sampled_indices]
        elif len(patch_points) < TARGET_PATCH_SIZE:
            # Pad with duplicated points + small noise
            needed = TARGET_PATCH_SIZE - len(patch_points)
            duplicates = []
            for _ in range(needed):
                idx = np.random.randint(0, len(patch_points))
                duplicate = patch_points[idx].copy()
                # Add small noise to avoid identical points
                duplicate += np.random.normal(0, 0.01, 3)
                duplicates.append(duplicate)
            patch_points = np.vstack([patch_points, duplicates])
        
        positive_patches.append(patch_points)
        positive_labels.append(1)
        positive_metadata.append(pile_data)
    
    print(f"Extracted {len(positive_patches)} positive coordinate patches")
    
    # Create negative samples - SAME strategy as classical ML
    negative_patches, negative_labels, negative_metadata = create_negative_coordinate_patches(
        points, enhanced_pile_data, point_tree, len(positive_patches)
    )
    
    print(f"Created {len(negative_patches)} negative coordinate patches")
    
    # Combine all patches
    all_patches = positive_patches + negative_patches
    all_labels = positive_labels + negative_labels
    all_metadata = positive_metadata + negative_metadata
    
    print(f"Total coordinate dataset: {len(all_patches)} patches")
    print(f"   Shape per patch: {all_patches[0].shape} (points, XYZ)")
    
    return np.array(all_patches), np.array(all_labels), all_metadata

def create_negative_coordinate_patches(points, pile_data, point_tree, n_negatives):
    """Create negative coordinate patches (same strategy as classical ML)"""
    
    pile_coords = np.array([[p['x'], p['y']] for p in pile_data])
    pile_tree = cKDTree(pile_coords)
    
    buffer = 50.0
    x_min, x_max = points[:, 0].min() + buffer, points[:, 0].max() - buffer
    y_min, y_max = points[:, 1].min() + buffer, points[:, 1].max() - buffer
    
    negative_patches = []
    negative_labels = []
    negative_metadata = []
    
    attempts = 0
    max_attempts = n_negatives * 10
    min_distance = PATCH_RADIUS * 2
    
    np.random.seed(42)
    
    while len(negative_patches) < n_negatives and attempts < max_attempts:
        attempts += 1
        
        x = np.random.uniform(x_min, x_max)
        y = np.random.uniform(y_min, y_max)
        
        dist, _ = pile_tree.query([x, y])
        if dist > min_distance:
            indices = point_tree.query_ball_point([x, y], PATCH_RADIUS)
            
            if len(indices) >= MIN_POINTS:
                patch_points = points[indices]
                
                # Same subsampling as positive patches
                if len(patch_points) > TARGET_PATCH_SIZE:
                    sampled_indices = np.random.choice(len(patch_points), TARGET_PATCH_SIZE, replace=False)
                    patch_points = patch_points[sampled_indices]
                elif len(patch_points) < TARGET_PATCH_SIZE:
                    needed = TARGET_PATCH_SIZE - len(patch_points)
                    duplicates = []
                    for _ in range(needed):
                        idx = np.random.randint(0, len(patch_points))
                        duplicate = patch_points[idx].copy()
                        duplicate += np.random.normal(0, 0.01, 3)
                        duplicates.append(duplicate)
                    patch_points = np.vstack([patch_points, duplicates])
                
                negative_patches.append(patch_points)
                negative_labels.append(0)
                negative_metadata.append({
                    'pile_id': f'negative_{len(negative_patches)}',
                    'x': x, 'y': y,
                    'confidence': 'synthetic'
                })
    
    return negative_patches, negative_labels, negative_metadata

def prepare_data_for_pytorch(coordinate_patches, coordinate_labels):
    """Prepare coordinate data for PyTorch training"""
    
    # Split data
    X_train, X_test, y_train, y_test = train_test_split(
        coordinate_patches, coordinate_labels, 
        test_size=0.2, random_state=42, stratify=coordinate_labels
    )
    
    # Convert to tensors
    X_train_tensor = torch.FloatTensor(X_train)
    X_test_tensor = torch.FloatTensor(X_test)
    y_train_tensor = torch.LongTensor(y_train)
    y_test_tensor = torch.LongTensor(y_test)
    
    print(f"PyTorch data prepared:")
    print(f"  Train: {X_train_tensor.shape} patches, {y_train_tensor.shape} labels")
    print(f"  Test: {X_test_tensor.shape} patches, {y_test_tensor.shape} labels")
    
    return X_train_tensor, X_test_tensor, y_train_tensor, y_test_tensor

class SimplePointNet(nn.Module):
    """Simplified PointNet++ for pile detection comparison"""
    
    def __init__(self, num_classes=2, num_points=64):
        super(SimplePointNet, self).__init__()
        
        # Feature extraction layers
        self.conv1 = nn.Conv1d(3, 64, 1)
        self.conv2 = nn.Conv1d(64, 128, 1)
        self.conv3 = nn.Conv1d(128, 256, 1)
        
        # Batch normalization
        self.bn1 = nn.BatchNorm1d(64)
        self.bn2 = nn.BatchNorm1d(128)
        self.bn3 = nn.BatchNorm1d(256)
        
        # Classification layers
        self.fc1 = nn.Linear(256, 128)
        self.fc2 = nn.Linear(128, 64)
        self.fc3 = nn.Linear(64, num_classes)
        
        self.dropout = nn.Dropout(0.3)
        
    def forward(self, x):
        # x shape: (batch_size, num_points, 3)
        x = x.transpose(2, 1)  # (batch_size, 3, num_points)
        
        # Feature extraction
        x = F.relu(self.bn1(self.conv1(x)))
        x = F.relu(self.bn2(self.conv2(x)))
        x = F.relu(self.bn3(self.conv3(x)))
        
        # Global max pooling
        x = torch.max(x, 2)[0]
        
        # Classification
        x = F.relu(self.fc1(x))
        x = self.dropout(x)
        x = F.relu(self.fc2(x))
        x = self.dropout(x)
        x = self.fc3(x)
        
        return x

def get_knn_graph(x, k=20):
    """Get k-nearest neighbor graph for DGCNN"""
    batch_size, num_dims, num_points = x.size()
    
    # Compute pairwise distances
    inner = -2 * torch.matmul(x.transpose(2, 1), x)
    xx = torch.sum(x**2, dim=1, keepdim=True)
    pairwise_distance = -xx - inner - xx.transpose(2, 1)
    
    idx = pairwise_distance.topk(k=k, dim=-1)[1]  # (batch_size, num_points, k)
    
    return idx

def get_edge_features(x, idx):
    """Get edge features for DGCNN"""
    batch_size, num_dims, num_points = x.size()
    k = idx.size(-1)
    
    # Reshape x for gathering: (batch_size, num_dims, num_points) -> (batch_size * num_points, num_dims)
    x_reshaped = x.transpose(2, 1).contiguous().view(batch_size * num_points, num_dims)
    
    # Adjust indices for batch dimension
    batch_idx = torch.arange(batch_size, device=x.device).view(-1, 1, 1) * num_points
    idx_adjusted = idx + batch_idx
    
    # Gather neighbor features
    neighbors = x_reshaped[idx_adjusted.view(-1)].view(batch_size, num_points, k, num_dims)
    neighbors = neighbors.permute(0, 3, 1, 2)  # (batch_size, num_dims, num_points, k)
    
    # Central point features
    central = x.unsqueeze(-1).expand(-1, -1, -1, k)
    
    # Edge features: [central, neighbor - central]
    edge_features = torch.cat([central, neighbors - central], dim=1)
    
    return edge_features

class SimpleDGCNN(nn.Module):
    """Simplified DGCNN for pile detection comparison"""
    
    def __init__(self, num_classes=2, k=20):
        super(SimpleDGCNN, self).__init__()
        self.k = k
        
        # Edge convolution layers
        self.conv1 = nn.Conv2d(6, 64, 1)    # 3*2 = 6 (central + edge)
        self.conv2 = nn.Conv2d(128, 128, 1)  # 64*2 = 128
        self.conv3 = nn.Conv2d(256, 256, 1)  # 128*2 = 256
        
        # Batch normalization
        self.bn1 = nn.BatchNorm2d(64)
        self.bn2 = nn.BatchNorm2d(128)
        self.bn3 = nn.BatchNorm2d(256)
        
        # Classification head - Conv1d layers (same as original)
        self.conv4 = nn.Conv1d(448, 256, 1)  # 64+128+256 = 448
        self.conv5 = nn.Conv1d(256, 128, 1)
        self.conv6 = nn.Conv1d(128, num_classes, 1)
        
        self.bn4 = nn.BatchNorm1d(256)
        self.bn5 = nn.BatchNorm1d(128)
        
        self.dropout = nn.Dropout(0.3)
        
    def forward(self, x):
        batch_size = x.size(0)
        x = x.transpose(2, 1)  # (batch_size, 3, num_points)
        
        # First edge convolution
        idx = get_knn_graph(x, k=self.k)
        x1 = get_edge_features(x, idx)
        x1 = F.relu(self.bn1(self.conv1(x1)))
        x1 = x1.max(dim=-1, keepdim=False)[0]
        
        # Second edge convolution
        idx = get_knn_graph(x1, k=self.k)
        x2 = get_edge_features(x1, idx)
        x2 = F.relu(self.bn2(self.conv2(x2)))
        x2 = x2.max(dim=-1, keepdim=False)[0]
        
        # Third edge convolution
        idx = get_knn_graph(x2, k=self.k)
        x3 = get_edge_features(x2, idx)
        x3 = F.relu(self.bn3(self.conv3(x3)))
        x3 = x3.max(dim=-1, keepdim=False)[0]
        
        # Concatenate features
        x = torch.cat((x1, x2, x3), dim=1)
        
        # Classification head with Conv1d
        x = F.relu(self.bn4(self.conv4(x)))
        x = self.dropout(x)
        x = F.relu(self.bn5(self.conv5(x)))
        x = self.dropout(x)
        x = self.conv6(x)
        
        # Global max pooling for final classification
        x = torch.max(x, 2)[0]
        
        return x

def train_deep_learning_model(model, X_train_tensor, y_train_tensor, X_test_tensor, y_test_tensor, model_name, epochs=50):
    """Train a deep learning model"""
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    model = model.to(device)
    
    # Move tensors to device
    X_train_tensor = X_train_tensor.to(device)
    y_train_tensor = y_train_tensor.to(device)
    X_test_tensor = X_test_tensor.to(device)
    y_test_tensor = y_test_tensor.to(device)
    
    # Create data loaders
    train_dataset = TensorDataset(X_train_tensor, y_train_tensor)
    train_loader = DataLoader(train_dataset, batch_size=32, shuffle=True)
    
    # Optimizer and loss
    optimizer = torch.optim.Adam(model.parameters(), lr=0.001)
    criterion = nn.CrossEntropyLoss()
    
    start_time = time.time()
    best_test_acc = 0.0
    
    print(f"Training {model_name}...")
    
    for epoch in range(epochs):
        model.train()
        train_loss = 0.0
        train_correct = 0
        
        for batch_x, batch_y in train_loader:
            optimizer.zero_grad()
            outputs = model(batch_x)
            loss = criterion(outputs, batch_y)
            loss.backward()
            optimizer.step()
            
            train_loss += loss.item()
            _, predicted = torch.max(outputs.data, 1)
            train_correct += (predicted == batch_y).sum().item()
        
        # Test evaluation
        model.eval()
        with torch.no_grad():
            test_outputs = model(X_test_tensor)
            _, test_predicted = torch.max(test_outputs.data, 1)
            test_correct = (test_predicted == y_test_tensor).sum().item()
            test_acc = test_correct / len(y_test_tensor)
            
            if test_acc > best_test_acc:
                best_test_acc = test_acc
        
        train_acc = train_correct / len(y_train_tensor)
        
        if (epoch + 1) % 10 == 0:
            print(f"  Epoch {epoch+1}/{epochs}: Train Acc: {train_acc:.3f}, Test Acc: {test_acc:.3f}")
    
    training_time = time.time() - start_time
    
    print(f"{model_name} training complete!")
    print(f"   Final test accuracy: {test_acc:.3f}")
    print(f"   Best test accuracy: {best_test_acc:.3f}")
    print(f"   Training time: {training_time:.1f}s")
    
    return {
        'model': model,
        'final_accuracy': test_acc,
        'best_accuracy': best_test_acc,
        'training_time': training_time
    }

# Prepare data for deep learning
print("Preparing data for deep learning models...")
point_tree = cKDTree(filtered_points[:, :2])
coordinate_patches, coordinate_labels, coordinate_metadata = extract_coordinates_for_deep_learning(
    filtered_points, enhanced_pile_data, point_tree
)

# Prepare for PyTorch
X_train_dl, X_test_dl, y_train_dl, y_test_dl = prepare_data_for_pytorch(
    coordinate_patches, coordinate_labels
)

print(f"Deep learning data ready!")
print(f"  Same {len(coordinate_patches)} patches as classical ML")
print(f"  Same {np.sum(coordinate_labels)} positive samples")
print(f"  Ready for PointNet++ and DGCNN training")

print("\nTraining Deep Learning Models")
print("=" * 50)

# Train PointNet++
pointnet_model = SimplePointNet(num_classes=2, num_points=TARGET_PATCH_SIZE)
pointnet_results = train_deep_learning_model(
    pointnet_model, X_train_dl, y_train_dl, X_test_dl, y_test_dl, 
    "PointNet++", epochs=50
)

print()

# Train DGCNN  
dgcnn_model = SimpleDGCNN(num_classes=2, k=10)  # Smaller k for 64 points
dgcnn_results = train_deep_learning_model(
    dgcnn_model, X_train_dl, y_train_dl, X_test_dl, y_test_dl,
    "DGCNN", epochs=50
)

# Compare all approaches
print("\nArchitecture Comparison Results")
print("=" * 50)

print(f"Performance Summary:")
print(f"  Classical ML (Biased):   {results['biased_detection_rate']*100:.1f}% detection")
print(f"  Classical ML (Unbiased): {results['unbiased_detection_rate']*100:.1f}% detection")
print(f"  PointNet++:              {pointnet_results['final_accuracy']*100:.1f}% test accuracy")
print(f"  DGCNN:                   {dgcnn_results['final_accuracy']*100:.1f}% test accuracy")

print(f"\nTraining Time Comparison:")
print(f"  Classical ML:  ~30 seconds")
print(f"  PointNet++:    {pointnet_results['training_time']:.1f}s")
print(f"  DGCNN:         {dgcnn_results['training_time']:.1f}s")

# Save comprehensive results
comprehensive_results = {
    'timestamp': datetime.now().isoformat(),
    'site': SITE_NAME,
    'classical_ml': {
        'biased_detection_rate': results['biased_detection_rate'],
        'unbiased_detection_rate': results['unbiased_detection_rate'],
        'recommendation': results['recommendation']
    },
    'deep_learning': {
        'pointnet': {
            'final_accuracy': pointnet_results['final_accuracy'],
            'best_accuracy': pointnet_results['best_accuracy'],
            'training_time': pointnet_results['training_time']
        },
        'dgcnn': {
            'final_accuracy': dgcnn_results['final_accuracy'],
            'best_accuracy': dgcnn_results['best_accuracy'],
            'training_time': dgcnn_results['training_time']
        }
    }
}

# Save to JSON file
comp_results_file = output_dir / f"architecture_comparison_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
with open(comp_results_file, 'w') as f:
    json.dump(comprehensive_results, f, indent=2, default=str)

# Save to CSV file for easy analysis
csv_results = []
csv_results.append({
    'Architecture': 'Classical ML (Biased)',
    'Accuracy/Detection_Rate': results['biased_detection_rate'],
    'Training_Time_Seconds': 30,
    'Model_Type': 'Classical',
    'Notes': 'Biased detection approach'
})
csv_results.append({
    'Architecture': 'Classical ML (Unbiased)',
    'Accuracy/Detection_Rate': results['unbiased_detection_rate'],
    'Training_Time_Seconds': 30,
    'Model_Type': 'Classical',
    'Notes': 'Unbiased detection approach'
})
csv_results.append({
    'Architecture': 'PointNet++',
    'Accuracy/Detection_Rate': pointnet_results['final_accuracy'],
    'Training_Time_Seconds': pointnet_results['training_time'],
    'Model_Type': 'Deep Learning',
    'Notes': f"Best accuracy: {pointnet_results['best_accuracy']:.3f}"
})
csv_results.append({
    'Architecture': 'DGCNN',
    'Accuracy/Detection_Rate': dgcnn_results['final_accuracy'],
    'Training_Time_Seconds': dgcnn_results['training_time'],
    'Model_Type': 'Deep Learning',
    'Notes': f"Best accuracy: {dgcnn_results['best_accuracy']:.3f}"
})

csv_df = pd.DataFrame(csv_results)
csv_file = output_dir / f"architecture_comparison_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"
csv_df.to_csv(csv_file, index=False)

# Save pile locations with detection results (like original notebook)
pile_locations_data = []
for i, pile_data in enumerate(enhanced_pile_data):
    if i < len(pile_validation_results['detected_piles']):
        detected = pile_validation_results['detected_piles'][i]
        confidence = pile_validation_results['detection_confidences'][i]
        detection_status = 'Detected' if detected == 1 else 'Missed'
    else:
        detected = 0
        confidence = 0.0
        detection_status = 'Not Tested'
    
    pile_locations_data.append({
        'pile_id': pile_data['pile_id'],
        'utm_x': pile_data['x'],
        'utm_y': pile_data['y'],
        'confidence_level': pile_data['confidence'],
        'source': pile_data['source'],
        'predicted_pile': detected,
        'prediction_confidence': confidence,
        'detection_status': detection_status,
        'site_name': SITE_NAME,
        'validation_type': 'architecture_comparison',
        'ifc_distance': pile_data.get('ifc_distance', -1),
        'crs': TARGET_CRS
    })

pile_locations_df = pd.DataFrame(pile_locations_data)
pile_locations_file = output_dir / f"pile_detection_results_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"
pile_locations_df.to_csv(pile_locations_file, index=False)

print(f"\nResults saved:")
print(f"  Architecture comparison: {csv_file.name}")
print(f"  Pile locations & results: {pile_locations_file.name}")
print(f"  Comprehensive JSON: {comp_results_file.name}")