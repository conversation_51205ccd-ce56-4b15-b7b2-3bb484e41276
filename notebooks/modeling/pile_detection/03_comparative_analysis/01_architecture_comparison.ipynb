# %% [markdown]
# # Trino Pile Detection - Single Site Style Data Preparation
# 
# This notebook adapts the successful single-site validation approach (RES→RCPS) 
# to work with Trino's multi-source data (IFC + Buffer KML + Point Cloud).
# 
# **Key Strategy:**
# - Use Buffer KML as primary ground truth (like successful RES approach)
# - Apply same patch parameters (3m radius, 64 points, 22 features)
# - Use classical ML (Gradient Boosting) as primary algorithm
# - Validate using ground truth detection rate on known pile locations
# - Use IFC only for confidence enhancement, not primary training
# 
# **Author**: Adapted from successful single-site approach
# **Date**: August 2025

# %%
import numpy as np
import pandas as pd
import open3d as o3d
import geopandas as gpd
from scipy.spatial import cKDTree
from sklearn.ensemble import GradientBoostingClassifier
from sklearn.model_selection import train_test_split
from sklearn.metrics import accuracy_score, f1_score, precision_score, recall_score
from pathlib import Path
import pickle
import json
from datetime import datetime
import matplotlib.pyplot as plt
import warnings
warnings.filterwarnings('ignore')

print("Libraries imported successfully")

# %%
# Single-site style parameters (successful RES approach)
PATCH_RADIUS = 3.0          # From RES success (was 8.0 in failed approaches)
TARGET_PATCH_SIZE = 64      # From RES success (was 1024 in failed approaches)
MIN_POINTS = 20             # Keep same
SITE_NAME = "trino_enel"
TARGET_CRS = "EPSG:32632"   # UTM Zone 32N (Europe) - Trino's coordinate system


# File paths - UPDATE THESE TO YOUR TRINO DATA
POINT_CLOUD_PATH = "../../../../data/processed/trino_enel/denoising/trino_enel_denoised_for_registration.ply"
BUFFER_KML_PATH = "../../../../data/raw/trino_enel/kml/pile.kml"  # Your Buffer KML
IFC_CSV_PATH = "../../../../data/processed/trino_enel/ifc_metadata//GRE.EEC.S.00.IT.P.14353.00.265_coordinates.csv"  # Your 14k+ IFC piles
OUTPUT_DIR = f"output_runs/trino_single_site_{datetime.now().strftime('%Y%m%d_%H%M%S')}"

# Create output directory
output_dir = Path(OUTPUT_DIR)
output_dir.mkdir(parents=True, exist_ok=True)

print(f"Configuration:")
print(f"  Site: {SITE_NAME}")
print(f"  Target CRS: {TARGET_CRS}")
print(f"  Patch radius: {PATCH_RADIUS}m (like RES)")
print(f"  Points per patch: {TARGET_PATCH_SIZE} (like RES)")
print(f"  Output: {OUTPUT_DIR}")
print(f"  Point cloud: {POINT_CLOUD_PATH}")
print(f"  Buffer KML: {BUFFER_KML_PATH}")
print(f"  IFC CSV: {IFC_CSV_PATH}")

# %%
# Replace the load_trino_data() function with this fixed version

# Updated load_trino_data() function specifically for EPSG:32632

def load_trino_data():
    """Load Trino multi-source data - optimized for EPSG:32632 (UTM Zone 32N)"""
    
    print("Loading Trino point cloud...")
    try:
        point_cloud = o3d.io.read_point_cloud(POINT_CLOUD_PATH)
        points = np.asarray(point_cloud.points)
        print(f"✅ Loaded {len(points):,} points")
    except Exception as e:
        print(f"❌ Error loading point cloud: {e}")
        return None, None, None
    
    print("Loading Buffer KML (ground truth like RES)...")
    try:
        # Load KML and extract pile locations
        gdf_kml = gpd.read_file(BUFFER_KML_PATH, driver='KML')
        print(f"Loaded KML with {len(gdf_kml)} features")
        
        # Debug: Check what geometry types we have
        geom_types = gdf_kml.geometry.geom_type.value_counts()
        print(f"Geometry types found: {dict(geom_types)}")
        
        # Extract coordinates from different geometry types
        buffer_pile_locations = []
        for idx, geom in enumerate(gdf_kml.geometry):
            try:
                if geom.geom_type == 'Point':
                    buffer_pile_locations.append([geom.x, geom.y])
                elif geom.geom_type == 'Polygon':
                    # Use centroid of polygon
                    centroid = geom.centroid
                    buffer_pile_locations.append([centroid.x, centroid.y])
                elif geom.geom_type == 'LineString':
                    # Use centroid of line
                    centroid = geom.centroid
                    buffer_pile_locations.append([centroid.x, centroid.y])
                elif geom.geom_type == 'MultiPolygon':
                    # Use centroid of multipolygon
                    centroid = geom.centroid
                    buffer_pile_locations.append([centroid.x, centroid.y])
                else:
                    print(f"Warning: Unknown geometry type {geom.geom_type} at index {idx}")
            except Exception as e:
                print(f"Warning: Failed to process geometry at index {idx}: {e}")
                continue
        
        if len(buffer_pile_locations) == 0:
            print("❌ No valid pile locations extracted from KML")
            return None, None, None
        
        print(f"✅ Extracted {len(buffer_pile_locations)} pile locations from KML geometries")
        
        # Reproject from WGS84 (KML default) to UTM Zone 32N
        print("Reprojecting KML from WGS84 to EPSG:32632 (UTM Zone 32N)...")
        
        # Set CRS if not already set
        if gdf_kml.crs is None:
            gdf_kml = gdf_kml.set_crs('EPSG:4326')  # KML is typically WGS84
        
        # Reproject to UTM Zone 32N
        gdf_utm = gdf_kml.to_crs('EPSG:32632')
        
        # Extract UTM coordinates
        buffer_pile_locations_utm = []
        for geom in gdf_utm.geometry:
            try:
                if geom.geom_type == 'Point':
                    buffer_pile_locations_utm.append([geom.x, geom.y])
                else:
                    centroid = geom.centroid
                    buffer_pile_locations_utm.append([centroid.x, centroid.y])
            except Exception as e:
                print(f"Warning: Failed to extract UTM coordinates: {e}")
                continue
        
        buffer_piles = np.array(buffer_pile_locations_utm)
        print(f"✅ Reprojected to EPSG:32632 - {len(buffer_piles)} pile locations")
        
        # Coordinate range check
        coord_range_x = buffer_piles[:, 0].max() - buffer_piles[:, 0].min()
        coord_range_y = buffer_piles[:, 1].max() - buffer_piles[:, 1].min()
        
        print(f"UTM coordinates (EPSG:32632):")
        print(f"  X (Easting): {buffer_piles[:, 0].min():.1f} to {buffer_piles[:, 0].max():.1f} (range: {coord_range_x:.1f}m)")
        print(f"  Y (Northing): {buffer_piles[:, 1].min():.1f} to {buffer_piles[:, 1].max():.1f} (range: {coord_range_y:.1f}m)")
        
        # Sanity check for UTM Zone 32N coordinates
        if buffer_piles[:, 0].min() < 166000 or buffer_piles[:, 0].max() > 834000:
            print("⚠️  Warning: X coordinates outside typical UTM Zone 32N range")
        if buffer_piles[:, 1].min() < 0 or buffer_piles[:, 1].max() > 9400000:
            print("⚠️  Warning: Y coordinates outside typical UTM Zone 32N range")
        
    except Exception as e:
        print(f"❌ Error loading Buffer KML: {e}")
        print(f"File path: {BUFFER_KML_PATH}")
        return None, None, None
    
    print("Loading IFC data (for confidence enhancement)...")
    try:
        ifc_df = pd.read_csv(IFC_CSV_PATH)
        
        # Debug: Check IFC column names
        print(f"IFC columns available: {list(ifc_df.columns)}")
        
        # Common column name patterns for coordinates
        x_col = None
        y_col = None
        
        # Try exact matches first
        if 'X' in ifc_df.columns and 'Y' in ifc_df.columns:
            x_col, y_col = 'X', 'Y'
        elif 'x' in ifc_df.columns and 'y' in ifc_df.columns:
            x_col, y_col = 'x', 'y'
        elif 'Easting' in ifc_df.columns and 'Northing' in ifc_df.columns:
            x_col, y_col = 'Easting', 'Northing'
        elif 'easting' in ifc_df.columns and 'northing' in ifc_df.columns:
            x_col, y_col = 'easting', 'northing'
        else:
            # Try fuzzy matching
            for col in ifc_df.columns:
                col_lower = col.lower()
                if any(pattern in col_lower for pattern in ['x_coord', 'x_utm', 'utm_x', 'coord_x']):
                    x_col = col
                elif any(pattern in col_lower for pattern in ['y_coord', 'y_utm', 'utm_y', 'coord_y']):
                    y_col = col
                elif 'east' in col_lower and 'ing' in col_lower:
                    x_col = col
                elif 'north' in col_lower and 'ing' in col_lower:
                    y_col = col
        
        if x_col and y_col:
            ifc_piles = ifc_df[[x_col, y_col]].dropna().values
            print(f"✅ Loaded {len(ifc_piles):,} IFC pile locations using columns: '{x_col}', '{y_col}'")
            
            # Check IFC coordinate ranges
            ifc_range_x = ifc_piles[:, 0].max() - ifc_piles[:, 0].min()
            ifc_range_y = ifc_piles[:, 1].max() - ifc_piles[:, 1].min()
            print(f"IFC coordinates (assuming EPSG:32632):")
            print(f"  X (Easting): {ifc_piles[:, 0].min():.1f} to {ifc_piles[:, 0].max():.1f} (range: {ifc_range_x:.1f}m)")
            print(f"  Y (Northing): {ifc_piles[:, 1].min():.1f} to {ifc_piles[:, 1].max():.1f} (range: {ifc_range_y:.1f}m)")
            
            # Check if IFC and KML coordinates are in similar ranges
            x_overlap = (max(buffer_piles[:, 0].min(), ifc_piles[:, 0].min()), 
                        min(buffer_piles[:, 0].max(), ifc_piles[:, 0].max()))
            y_overlap = (max(buffer_piles[:, 1].min(), ifc_piles[:, 1].min()), 
                        min(buffer_piles[:, 1].max(), ifc_piles[:, 1].max()))
            
            if x_overlap[1] > x_overlap[0] and y_overlap[1] > y_overlap[0]:
                print(f"✅ KML and IFC coordinates have overlapping ranges - good!")
            else:
                print(f"⚠️  Warning: KML and IFC coordinates don't overlap - check coordinate systems")
            
        else:
            print(f"❌ Could not identify X,Y coordinate columns in IFC data")
            print(f"Available columns: {list(ifc_df.columns)}")
            print("Please update the column names or file path")
            ifc_piles = None
            
    except Exception as e:
        print(f"⚠️ Could not load IFC data: {e}")
        print("Continuing with Buffer KML only...")
        ifc_piles = None
    
    return points, buffer_piles, ifc_piles

# Load all data
points, buffer_piles, ifc_piles = load_trino_data()

if points is None or buffer_piles is None:
    print("❌ Failed to load required data. Please check file paths.")
    exit()

# %%
def filter_point_cloud_around_piles(points, pile_locations, buffer_radius=10.0):
    """Filter point cloud around confirmed piles (like RES filtering)"""
    
    print(f"Filtering point cloud around {len(pile_locations)} pile locations...")
    
    # Create spatial index for efficiency
    pile_tree = cKDTree(pile_locations)
    
    # Find all points within buffer_radius of any pile
    point_indices_near_piles = set()
    
    for i, point in enumerate(points[:, :2]):
        distances, _ = pile_tree.query(point, k=1)
        if distances <= buffer_radius:
            point_indices_near_piles.add(i)
    
    filtered_indices = list(point_indices_near_piles)
    filtered_points = points[filtered_indices]
    
    print(f"✅ Filtered to {len(filtered_points):,} points around pile zones")
    print(f"   Reduction: {len(points):,} → {len(filtered_points):,} points")
    
    return filtered_points

# Filter point cloud like successful RES approach
filtered_points = filter_point_cloud_around_piles(points, buffer_piles, buffer_radius=10.0)

# %%
def enhance_with_ifc_confidence(buffer_piles, ifc_piles):
    """Use IFC to add confidence scores to buffer pile locations"""
    
    if ifc_piles is None:
        print("No IFC data available. Using buffer piles with medium confidence.")
        return [{'x': pile[0], 'y': pile[1], 'confidence': 'medium', 'source': 'buffer_only'} 
                for pile in buffer_piles]
    
    print(f"Enhancing {len(buffer_piles)} buffer piles with IFC confidence...")
    
    ifc_tree = cKDTree(ifc_piles)
    enhanced_piles = []
    
    confidence_counts = {'high': 0, 'medium': 0, 'low': 0}
    
    for i, buffer_pile in enumerate(buffer_piles):
        # Find nearest IFC pile
        distance, nearest_idx = ifc_tree.query(buffer_pile)
        
        # Assign confidence based on distance to IFC design
        if distance < 2.0:
            confidence = 'high'
            source = 'buffer_ifc_matched'
        elif distance < 5.0:
            confidence = 'medium'
            source = 'buffer_ifc_close'
        else:
            confidence = 'low'
            source = 'buffer_only'
        
        confidence_counts[confidence] += 1
        
        enhanced_piles.append({
            'pile_id': f'trino_pile_{i}',
            'x': buffer_pile[0],
            'y': buffer_pile[1],
            'confidence': confidence,
            'source': source,
            'ifc_distance': distance,
            'site': SITE_NAME
        })
    
    print(f"✅ Confidence distribution:")
    for conf, count in confidence_counts.items():
        print(f"   {conf.capitalize()}: {count} piles")
    
    return enhanced_piles

# Enhance buffer piles with IFC confidence
enhanced_pile_data = enhance_with_ifc_confidence(buffer_piles, ifc_piles)

# %%
def extract_22_engineering_features(patch_points):
    """Extract same 22 engineering features as successful single-site approach"""
    
    if len(patch_points) == 0:
        return np.zeros(22)
    
    # Patch should already be centered
    x, y, z = patch_points[:, 0], patch_points[:, 1], patch_points[:, 2]
    radial_dist = np.sqrt(x**2 + y**2)
    height_above_min = z - np.min(z)
    
    try:
        feature_vector = [
            # Basic spatial statistics (9 features)
            np.mean(x), np.std(x), np.max(x) - np.min(x),
            np.mean(y), np.std(y), np.max(y) - np.min(y),
            np.mean(z), np.std(z), np.max(z) - np.min(z),
            
            # Height-based features (4 features)
            np.mean(height_above_min), np.std(height_above_min),
            np.percentile(height_above_min, 75), np.percentile(height_above_min, 25),
            
            # Radial distance features (4 features)
            np.mean(radial_dist), np.std(radial_dist),
            np.min(radial_dist), np.max(radial_dist),
            
            # Shape and density features (5 features)
            len(patch_points),
            np.std(x) / (np.std(y) + 1e-6),                    # Aspect ratio
            np.std(z) / (np.std(x) + np.std(y) + 1e-6),        # Height-to-width ratio
            np.percentile(radial_dist, 90),                     # 90th percentile radius
            np.sum(height_above_min > np.mean(height_above_min)) / len(patch_points),  # Elevated points ratio
        ]
        
        return np.array(feature_vector, dtype=np.float32)
    
    except Exception as e:
        print(f"Warning: Feature extraction failed: {e}")
        return np.zeros(22, dtype=np.float32)

def subsample_patch(patch_points, target_size=TARGET_PATCH_SIZE):
    """Subsample patch to target size (like single-site)"""
    if len(patch_points) <= target_size:
        return patch_points
    
    np.random.seed(42)  # For reproducibility
    indices = np.random.choice(len(patch_points), target_size, replace=False)
    return patch_points[indices]

def extract_patch_around_pile(points, pile_data, point_tree, radius=PATCH_RADIUS):
    """Extract and process patch around pile location (single-site style)"""
    
    pile_coord = [pile_data['x'], pile_data['y']]
    
    # Find points within radius
    indices = point_tree.query_ball_point(pile_coord, radius)
    
    if len(indices) < MIN_POINTS:
        return None
    
    patch_points = points[indices]
    
    # Subsample to target size
    patch_points = subsample_patch(patch_points, TARGET_PATCH_SIZE)
    
    # Center the patch (like single-site)
    center = np.array([pile_data['x'], pile_data['y'], np.mean(patch_points[:, 2])])
    centered_patch = patch_points - center
    
    # Extract 22 engineering features
    features = extract_22_engineering_features(centered_patch)
    
    return {
        'features': features,
        'centered_patch': centered_patch,
        'original_points': len(indices),
        'patch_center': center
    }

print("Feature extraction functions defined")

# %%
def create_trino_dataset_single_site_style(points, enhanced_pile_data):
    """Create dataset following single-site methodology"""
    
    print(f"Creating dataset from {len(enhanced_pile_data)} pile locations...")
    
    # Create spatial index
    point_tree = cKDTree(points[:, :2])
    
    positive_features = []
    positive_metadata = []
    
    # Extract positive patches (around buffer pile locations)
    for pile_data in enhanced_pile_data:
        patch_result = extract_patch_around_pile(points, pile_data, point_tree)
        
        if patch_result is not None:
            positive_features.append(patch_result['features'])
            positive_metadata.append({
                **pile_data,
                'label': 1,
                'patch_type': 'positive',
                'original_points': patch_result['original_points']
            })
    
    print(f"✅ Extracted {len(positive_features)} positive patches")
    
    # Create negative samples (targeted, not random)
    print("Creating negative samples...")
    negative_features, negative_metadata = create_negative_samples(
        points, enhanced_pile_data, point_tree, n_negatives=len(positive_features)
    )
    
    print(f"✅ Created {len(negative_features)} negative patches")
    
    # Combine positive and negative
    all_features = positive_features + negative_features
    all_metadata = positive_metadata + negative_metadata
    all_labels = [1] * len(positive_features) + [0] * len(negative_features)
    
    print(f"✅ Total dataset: {len(all_features)} samples")
    print(f"   Positive: {len(positive_features)} ({len(positive_features)/len(all_features)*100:.1f}%)")
    print(f"   Negative: {len(negative_features)} ({len(negative_features)/len(all_features)*100:.1f}%)")
    
    return np.array(all_features), np.array(all_labels), all_metadata

def create_negative_samples(points, pile_data, point_tree, n_negatives):
    """Create negative samples avoiding pile areas (like single-site)"""
    
    pile_coords = np.array([[p['x'], p['y']] for p in pile_data])
    pile_tree = cKDTree(pile_coords)
    
    # Define bounds
    x_min, x_max = points[:, 0].min(), points[:, 0].max()
    y_min, y_max = points[:, 1].min(), points[:, 1].max()
    
    # Add buffer to avoid edges
    buffer = 50.0
    x_min += buffer
    x_max -= buffer
    y_min += buffer
    y_max -= buffer
    
    negative_features = []
    negative_metadata = []
    
    attempts = 0
    max_attempts = n_negatives * 5
    min_distance = PATCH_RADIUS * 2  # Stay away from pile areas
    
    while len(negative_features) < n_negatives and attempts < max_attempts:
        # Random location
        x = np.random.uniform(x_min, x_max)
        y = np.random.uniform(y_min, y_max)
        
        # Check distance to nearest pile
        dist_to_nearest_pile, _ = pile_tree.query([x, y])
        
        if dist_to_nearest_pile > min_distance:
            # Create fake pile data for patch extraction
            fake_pile = {'x': x, 'y': y, 'confidence': 'synthetic', 'source': 'negative'}
            
            patch_result = extract_patch_around_pile(points, fake_pile, point_tree)
            
            if patch_result is not None:
                negative_features.append(patch_result['features'])
                negative_metadata.append({
                    'pile_id': f'negative_{len(negative_features)}',
                    'x': x,
                    'y': y,
                    'confidence': 'synthetic',
                    'source': 'negative_sampling',
                    'label': 0,
                    'patch_type': 'negative',
                    'original_points': patch_result['original_points'],
                    'site': SITE_NAME
                })
        
        attempts += 1
    
    return negative_features, negative_metadata

# Create dataset
X, y, metadata = create_trino_dataset_single_site_style(filtered_points, enhanced_pile_data)

# %%
def organize_by_confidence(X, y, metadata):
    """Organize training data by confidence levels (like single-site quality control)"""
    
    confidence_data = {'high': [], 'medium': [], 'low': [], 'synthetic': []}
    
    for i, meta in enumerate(metadata):
        conf = meta.get('confidence', 'unknown')
        if conf in confidence_data:
            confidence_data[conf].append(i)
    
    print("Dataset organization by confidence:")
    for conf, indices in confidence_data.items():
        positive_count = sum(1 for i in indices if y[i] == 1)
        negative_count = sum(1 for i in indices if y[i] == 0)
        print(f"  {conf.capitalize()}: {len(indices)} samples ({positive_count} positive, {negative_count} negative)")
    
    return confidence_data

def create_confidence_weighted_splits(X, y, metadata, confidence_data):
    """Create train/test splits prioritizing high-confidence data"""
    
    # Prioritize high and medium confidence for training
    high_conf_indices = confidence_data['high']
    medium_conf_indices = confidence_data['medium']
    low_conf_indices = confidence_data['low']
    synthetic_indices = confidence_data['synthetic']
    
    # Training set: All high + most medium + some synthetic
    train_indices = high_conf_indices.copy()
    
    if len(medium_conf_indices) > 0:
        # Use 80% of medium confidence for training
        n_medium_train = int(0.8 * len(medium_conf_indices))
        train_indices.extend(medium_conf_indices[:n_medium_train])
        
        # Use remaining medium confidence for validation
        val_indices = medium_conf_indices[n_medium_train:]
    else:
        val_indices = []
    
    # Add synthetic negatives to both sets
    n_synthetic_train = int(0.8 * len(synthetic_indices))
    train_indices.extend(synthetic_indices[:n_synthetic_train])
    val_indices.extend(synthetic_indices[n_synthetic_train:])
    
    # Test set: low confidence + some validation data
    test_indices = low_conf_indices.copy()
    if len(val_indices) > 20:  # Keep some validation data for testing
        n_val_for_test = min(20, len(val_indices) // 2)
        test_indices.extend(val_indices[-n_val_for_test:])
        val_indices = val_indices[:-n_val_for_test]
    
    return train_indices, val_indices, test_indices

# Organize data by confidence
confidence_data = organize_by_confidence(X, y, metadata)

# Create confidence-weighted splits
train_idx, val_idx, test_idx = create_confidence_weighted_splits(X, y, metadata, confidence_data)

print(f"\nDataset splits:")
print(f"  Training: {len(train_idx)} samples")
print(f"  Validation: {len(val_idx)} samples") 
print(f"  Test: {len(test_idx)} samples")

# %%
def train_single_site_style_model(X_train, y_train, X_val=None, y_val=None):
    """Train model using same approach as successful single-site"""
    
    print("Training Gradient Boosting model (like successful RES approach)...")
    
    # Same parameters as successful single-site
    model = GradientBoostingClassifier(
        n_estimators=100,
        max_depth=6,
        learning_rate=0.1,
        random_state=42
    )
    
    model.fit(X_train, y_train)
    
    # Training performance
    train_pred = model.predict(X_train)
    train_accuracy = accuracy_score(y_train, train_pred)
    print(f"✅ Training accuracy: {train_accuracy:.3f}")
    
    # Validation performance (if available)
    if X_val is not None and y_val is not None and len(X_val) > 0:
        val_pred = model.predict(X_val)
        val_accuracy = accuracy_score(y_val, val_pred)
        print(f"✅ Validation accuracy: {val_accuracy:.3f}")
    
    return model

# Prepare training data
X_train = X[train_idx]
y_train = y[train_idx]

X_val = X[val_idx] if len(val_idx) > 0 else None
y_val = y[val_idx] if len(val_idx) > 0 else None

X_test = X[test_idx] if len(test_idx) > 0 else None
y_test = y[test_idx] if len(test_idx) > 0 else None

# Train model
model = train_single_site_style_model(X_train, y_train, X_val, y_val)

# %%
def validate_on_known_pile_locations(model, pile_locations, points):
    """Apply single-site validation: test detection on known pile locations"""
    
    print(f"Validating on {len(pile_locations)} known pile locations...")
    
    point_tree = cKDTree(points[:, :2])
    detected_piles = []
    detection_confidences = []
    failed_extractions = 0
    
    for pile_data in pile_locations:
        patch_result = extract_patch_around_pile(points, pile_data, point_tree)
        
        if patch_result is not None:
            # Get prediction
            features = patch_result['features'].reshape(1, -1)
            prediction = model.predict(features)[0]
            confidence = model.predict_proba(features)[0][1]
            
            detected_piles.append(prediction)
            detection_confidences.append(confidence)
        else:
            failed_extractions += 1
    
    # Calculate metrics like single-site
    detection_rate = np.mean(detected_piles) if detected_piles else 0
    avg_confidence = np.mean(detection_confidences) if detection_confidences else 0
    
    # Single-site inspired assessment
    if detection_rate >= 0.9:
        status = "EXCELLENT"
        color = "🟢"
    elif detection_rate >= 0.8:
        status = "GOOD"
        color = "🟡"
    elif detection_rate >= 0.6:
        status = "MODERATE"
        color = "🟠"
    else:
        status = "POOR"
        color = "🔴"
    
    print(f"\n{color} GROUND TRUTH VALIDATION RESULTS:")
    print(f"  Detection rate: {detection_rate*100:.1f}% ({np.sum(detected_piles)}/{len(detected_piles)})")
    print(f"  Average confidence: {avg_confidence:.3f}")
    print(f"  Status: {status}")
    print(f"  Failed extractions: {failed_extractions}")
    
    return {
        'detection_rate': detection_rate,
        'avg_confidence': avg_confidence,
        'status': status,
        'detected_piles': detected_piles,
        'detection_confidences': detection_confidences,
        'failed_extractions': failed_extractions,
        'total_tested': len(pile_locations)
    }

# Validate on known pile locations (like successful RES approach)
pile_validation_results = validate_on_known_pile_locations(
    model, enhanced_pile_data, filtered_points
)

# %%
def evaluate_test_set_performance(model, X_test, y_test, test_metadata):
    """Evaluate performance on test set"""
    
    if X_test is None or len(X_test) == 0:
        print("No test set available")
        return None
    
    print(f"\nEvaluating on test set ({len(X_test)} samples)...")
    
    y_pred = model.predict(X_test)
    y_prob = model.predict_proba(X_test)[:, 1]
    
    # Calculate metrics
    accuracy = accuracy_score(y_test, y_pred)
    f1 = f1_score(y_test, y_pred)
    precision = precision_score(y_test, y_pred)
    recall = recall_score(y_test, y_pred)
    
    print(f"Test Set Performance:")
    print(f"  Accuracy: {accuracy:.3f}")
    print(f"  F1-Score: {f1:.3f}")
    print(f"  Precision: {precision:.3f}")
    print(f"  Recall: {recall:.3f}")
    
    # Break down by confidence level
    test_meta = [metadata[i] for i in test_idx]
    confidence_performance = {}
    
    for conf in ['high', 'medium', 'low', 'synthetic']:
        conf_indices = [i for i, meta in enumerate(test_meta) 
                       if meta.get('confidence') == conf]
        
        if conf_indices:
            conf_y_true = y_test[conf_indices]
            conf_y_pred = y_pred[conf_indices]
            conf_acc = accuracy_score(conf_y_true, conf_y_pred)
            confidence_performance[conf] = {
                'accuracy': conf_acc,
                'count': len(conf_indices)
            }
            print(f"  {conf.capitalize()} confidence: {conf_acc:.3f} ({len(conf_indices)} samples)")
    
    return {
        'accuracy': accuracy,
        'f1_score': f1,
        'precision': precision,
        'recall': recall,
        'confidence_breakdown': confidence_performance
    }

# Evaluate test set
if X_test is not None:
    test_results = evaluate_test_set_performance(model, X_test, y_test, [metadata[i] for i in test_idx])
else:
    print("No test set available for evaluation")
    test_results = None

# %%
def analyze_feature_importance(model, feature_names=None):
    """Analyze which features are most important for pile detection"""
    
    if feature_names is None:
        feature_names = [
            'x_mean', 'x_std', 'x_range',
            'y_mean', 'y_std', 'y_range', 
            'z_mean', 'z_std', 'z_range',
            'height_mean', 'height_std', 'height_75p', 'height_25p',
            'radial_mean', 'radial_std', 'radial_min', 'radial_max',
            'point_count', 'aspect_ratio', 'height_width_ratio', 'radial_90p', 'elevated_ratio'
        ]
    
    importances = model.feature_importances_
    feature_importance_df = pd.DataFrame({
        'feature': feature_names,
        'importance': importances
    }).sort_values('importance', ascending=False)
    
    print("\nTop 10 Most Important Features:")
    for i, row in feature_importance_df.head(10).iterrows():
        print(f"  {row['feature']}: {row['importance']:.3f}")
    
    return feature_importance_df

# Analyze feature importance
feature_importance = analyze_feature_importance(model)

# %%
def save_trino_results(results_dict, output_dir):
    """Save all results for future reference"""
    
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    
    # Save main results
    results_file = output_dir / f"trino_single_site_results_{timestamp}.json"
    with open(results_file, 'w') as f:
        json.dump(results_dict, f, indent=2, default=str)
    
    # Save model
    model_file = output_dir / f"trino_pile_detection_model_{timestamp}.pkl"
    with open(model_file, 'wb') as f:
        pickle.dump(model, f)
    
    # Save feature importance
    feature_importance.to_csv(output_dir / f"feature_importance_{timestamp}.csv", index=False)
    
    print(f"\n✅ Results saved to {output_dir}")
    print(f"  Main results: {results_file.name}")
    print(f"  Model: {model_file.name}")
    print(f"  Feature importance: feature_importance_{timestamp}.csv")
    
    return results_file

# Compile all results
all_results = {
    'site_name': SITE_NAME,
    'timestamp': datetime.now().isoformat(),
    'approach': 'single_site_style_adaptation',
    'configuration': {
        'patch_radius': PATCH_RADIUS,
        'target_patch_size': TARGET_PATCH_SIZE,
        'min_points': MIN_POINTS,
        'algorithm': 'GradientBoostingClassifier'
    },
    'data_summary': {
        'total_points': len(points),
        'filtered_points': len(filtered_points),
        'buffer_piles': len(buffer_piles),
        'ifc_piles': len(ifc_piles) if ifc_piles is not None else 0,
        'enhanced_piles': len(enhanced_pile_data),
        'total_samples': len(X),
        'positive_samples': int(np.sum(y)),
        'negative_samples': int(len(y) - np.sum(y))
    },
    'pile_validation': pile_validation_results,
    'test_performance': test_results,
    'feature_importance': feature_importance.to_dict('records'),
    'confidence_distribution': {conf: len(indices) for conf, indices in confidence_data.items()}
}

# Save results
results_file = save_trino_results(all_results, output_dir)

# %%
# Replace the create_qgis_export function with this version

def create_qgis_export(enhanced_pile_data, detection_results, output_dir):
    """Create QGIS-ready export for field validation (optimized for EPSG:32632)"""
    
    print("Creating QGIS export...")
    
    # Prepare results DataFrame
    qgis_data = []
    
    for i, pile_data in enumerate(enhanced_pile_data):
        if i < len(detection_results['detected_piles']):
            detected = detection_results['detected_piles'][i]
            confidence = detection_results['detection_confidences'][i]
            detection_status = 'Detected' if detected == 1 else 'Missed'
        else:
            detected = 0
            confidence = 0.0
            detection_status = 'Not Tested'
        
        qgis_data.append({
            'pile_id': pile_data['pile_id'],
            'utm_x': pile_data['x'],
            'utm_y': pile_data['y'],
            'confidence_level': pile_data['confidence'],
            'source': pile_data['source'],
            'predicted_pile': detected,
            'prediction_confidence': confidence,
            'detection_status': detection_status,
            'site_name': SITE_NAME,
            'validation_type': 'single_site_style',
            'ifc_distance': pile_data.get('ifc_distance', -1),
            'crs': TARGET_CRS
        })
    
    results_df = pd.DataFrame(qgis_data)
    
    # Convert to geographic coordinates for QGIS
    try:
        from shapely.geometry import Point
        
        # Create GeoDataFrame with UTM coordinates
        geometry = [Point(row['utm_x'], row['utm_y']) for _, row in results_df.iterrows()]
        gdf = gpd.GeoDataFrame(results_df, geometry=geometry, crs=TARGET_CRS)
        
        # Convert to WGS84 for QGIS compatibility
        gdf_wgs84 = gdf.to_crs('EPSG:4326')
        results_df['longitude'] = gdf_wgs84.geometry.x
        results_df['latitude'] = gdf_wgs84.geometry.y
        
        print(f"✅ Converted coordinates from {TARGET_CRS} to WGS84")
        
    except Exception as e:
        print(f"⚠️ Warning: Could not add geographic coordinates: {e}")
        results_df['longitude'] = 0.0
        results_df['latitude'] = 0.0
    
    # Save CSV for QGIS
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    csv_file = output_dir / f"trino_pile_detection_qgis_{timestamp}.csv"
    results_df.to_csv(csv_file, index=False)
    
    # Summary statistics
    total_tested = len([d for d in detection_results['detected_piles']])
    total_detected = sum(detection_results['detected_piles'])
    detection_rate = total_detected / total_tested if total_tested > 0 else 0
    
    print(f"✅ QGIS export created: {csv_file.name}")
    print(f"   Coordinate system: {TARGET_CRS} (UTM) + WGS84 (Geographic)")
    print(f"   Total piles tested: {total_tested}")
    print(f"   Piles detected: {total_detected}")
    print(f"   Detection rate: {detection_rate*100:.1f}%")
    
    return csv_file, results_df

# Create QGIS export
qgis_file, qgis_df = create_qgis_export(enhanced_pile_data, pile_validation_results, output_dir)

# %%
def visualize_results(qgis_df, pile_validation_results, output_dir):
    """Create visualization of results"""
    
    fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(15, 12))
    
    # Plot 1: Confidence distribution
    conf_counts = qgis_df['confidence_level'].value_counts()
    ax1.bar(conf_counts.index, conf_counts.values, alpha=0.7, color=['green', 'orange', 'red', 'gray'])
    ax1.set_title('Pile Confidence Distribution')
    ax1.set_xlabel('Confidence Level')
    ax1.set_ylabel('Number of Piles')
    ax1.grid(True, alpha=0.3)
    
    # Plot 2: Detection status
    detection_counts = qgis_df['detection_status'].value_counts()
    colors = ['green' if 'Detected' in status else 'red' for status in detection_counts.index]
    ax2.bar(detection_counts.index, detection_counts.values, alpha=0.7, color=colors)
    ax2.set_title('Detection Results')
    ax2.set_xlabel('Detection Status')
    ax2.set_ylabel('Number of Piles')
    ax2.grid(True, alpha=0.3)
    
    # Plot 3: Confidence vs Detection Rate
    if len(pile_validation_results['detection_confidences']) > 0:
        detected = np.array(pile_validation_results['detected_piles'])
        confidences = np.array(pile_validation_results['detection_confidences'])
        
        # Scatter plot
        colors = ['green' if d == 1 else 'red' for d in detected]
        ax3.scatter(confidences, detected + np.random.normal(0, 0.02, len(detected)), 
                   c=colors, alpha=0.6)
        ax3.set_title('Prediction Confidence vs Detection')
        ax3.set_xlabel('Prediction Confidence')
        ax3.set_ylabel('Detected (1) / Missed (0)')
        ax3.grid(True, alpha=0.3)
        ax3.set_ylim(-0.1, 1.1)
    
    # Plot 4: Feature importance (top 10)
    top_features = feature_importance.head(10)
    ax4.barh(range(len(top_features)), top_features['importance'])
    ax4.set_yticks(range(len(top_features)))
    ax4.set_yticklabels(top_features['feature'])
    ax4.set_title('Top 10 Feature Importances')
    ax4.set_xlabel('Importance')
    ax4.grid(True, alpha=0.3)
    
    plt.tight_layout()
    
    # Save plot
    plot_file = output_dir / f"trino_results_visualization_{datetime.now().strftime('%Y%m%d_%H%M%S')}.png"
    plt.savefig(plot_file, dpi=300, bbox_inches='tight')
    plt.show()
    
    print(f"✅ Visualization saved: {plot_file.name}")
    return plot_file

# Create visualization
plot_file = visualize_results(qgis_df, pile_validation_results, output_dir)

# %%
def print_final_summary():
    """Print final summary of results"""
    
    print("\n" + "="*60)
    print("🎯 TRINO SINGLE-SITE STYLE RESULTS SUMMARY")
    print("="*60)
    
    print(f"\n📊 Dataset Summary:")
    print(f"   Site: {SITE_NAME}")
    print(f"   Total point cloud: {len(points):,} points")
    print(f"   Filtered around piles: {len(filtered_points):,} points")
    print(f"   Buffer pile locations: {len(buffer_piles)}")
    print(f"   IFC pile locations: {len(ifc_piles) if ifc_piles is not None else 0:,}")
    print(f"   Final dataset: {len(X)} samples ({np.sum(y)} positive, {len(y) - np.sum(y)} negative)")
    
    print(f"\n🎯 Ground Truth Validation (KEY METRIC):")
    print(f"   Detection rate: {pile_validation_results['detection_rate']*100:.1f}%")
    print(f"   Average confidence: {pile_validation_results['avg_confidence']:.3f}")
    print(f"   Status: {pile_validation_results['status']}")
    print(f"   Piles tested: {pile_validation_results['total_tested']}")
    
    if test_results:
        print(f"\n📈 Test Set Performance:")
        print(f"   Accuracy: {test_results['accuracy']:.3f}")
        print(f"   F1-Score: {test_results['f1_score']:.3f}")
        print(f"   Precision: {test_results['precision']:.3f}")
        print(f"   Recall: {test_results['recall']:.3f}")
    
    print(f"\n🔧 Configuration Used (Single-Site Style):")
    print(f"   Patch radius: {PATCH_RADIUS}m (vs 8-20m in failed approaches)")
    print(f"   Points per patch: {TARGET_PATCH_SIZE} (vs 1024 in failed approaches)")
    print(f"   Features: 22 engineering features (vs 20 statistical in failed approaches)")
    print(f"   Algorithm: Gradient Boosting (vs PointNet++ in failed approaches)")
    print(f"   Validation: Ground truth detection rate (vs abstract accuracy in failed approaches)")
    
    print(f"\n📁 Output Files:")
    print(f"   Results: {results_file.name}")
    print(f"   QGIS export: {qgis_file.name}")
    print(f"   Visualization: {plot_file.name}")
    print(f"   All files in: {output_dir}")
    
    # Success assessment
    detection_rate = pile_validation_results['detection_rate']
    if detection_rate >= 0.9:
        status_msg = "🟢 EXCELLENT - Ready for production use!"
    elif detection_rate >= 0.8:
        status_msg = "🟡 GOOD - Suitable for field validation with human oversight"
    elif detection_rate >= 0.6:
        status_msg = "🟠 MODERATE - Needs improvement before field use"
    else:
        status_msg = "🔴 POOR - Requires significant refinement"
    
    print(f"\n🏆 FINAL ASSESSMENT:")
    print(f"   {status_msg}")
    
    if detection_rate >= 0.8:
        print(f"\n✅ SUCCESS: This approach shows significant improvement over previous failed attempts!")
        print(f"   • Used Buffer KML as ground truth (like successful RES)")
        print(f"   • Applied single-site parameters (3m patches, 64 points)")
        print(f"   • Used engineering features (22 interpretable features)")
        print(f"   • Applied classical ML (Gradient Boosting)")
        print(f"   • Validated on ground truth (detection rate on known piles)")
    else:
        print(f"\n⚠️  NEEDS IMPROVEMENT:")
        print(f"   • Consider refining patch parameters")
        print(f"   • Check data quality and preprocessing")
        print(f"   • Analyze failed detections for patterns")
        print(f"   • Ensure point cloud filtering is appropriate")
    
    print("\n" + "="*60)

# Print final summary
print_final_summary()

# %%
print("🎉 TRINO SINGLE-SITE STYLE DATA PREPARATION COMPLETE!")
print(f"📂 All results saved in: {output_dir}")
print(f"📊 Ground truth detection rate: {pile_validation_results['detection_rate']*100:.1f}%")
print(f"🎯 Status: {pile_validation_results['status']}")
print("\nNext steps:")
print("1. Review QGIS export for field validation")
print("2. Analyze feature importance for insights")
print("3. If successful, apply to other sites")
print("4. If needs improvement, refine based on failed detection analysis")

# Replace the extract_22_engineering_features() and extract_patch_around_pile() functions with these unbiased versions

def extract_22_engineering_features_unbiased(patch_points):
    """Extract 22 engineering features WITHOUT pile-centered bias"""
    
    if len(patch_points) == 0:
        return np.zeros(22)
    
    # DON'T assume patch is centered on pile - work with raw coordinates
    x, y, z = patch_points[:, 0], patch_points[:, 1], patch_points[:, 2]
    
    # Calculate patch characteristics from the data itself
    patch_center_x = np.mean(x)
    patch_center_y = np.mean(y)
    patch_center_z = np.mean(z)
    
    # Distances from DATA center, not pile center
    radial_dist = np.sqrt((x - patch_center_x)**2 + (y - patch_center_y)**2)
    height_above_min = z - np.min(z)
    
    try:
        feature_vector = [
            # Spatial spread (9 features) - NOT relative position
            np.std(x), np.std(y), np.std(z),                    # Spread, not mean position
            np.max(x) - np.min(x),                              # X range
            np.max(y) - np.min(y),                              # Y range  
            np.max(z) - np.min(z),                              # Z range
            np.percentile(x, 75) - np.percentile(x, 25),        # X IQR
            np.percentile(y, 75) - np.percentile(y, 25),        # Y IQR
            np.percentile(z, 75) - np.percentile(z, 25),        # Z IQR
            
            # Height-based features (4 features)
            np.mean(height_above_min), np.std(height_above_min),
            np.percentile(height_above_min, 75), np.percentile(height_above_min, 25),
            
            # Radial distribution from patch center (4 features)
            np.mean(radial_dist), np.std(radial_dist),
            np.min(radial_dist), np.max(radial_dist),
            
            # Shape and density features (5 features)
            len(patch_points),                                  # Point count
            np.std(x) / (np.std(y) + 1e-6),                    # Aspect ratio
            np.std(z) / (np.std(x) + np.std(y) + 1e-6),        # Height-to-width ratio
            np.percentile(radial_dist, 90),                     # 90th percentile radius
            np.sum(height_above_min > np.mean(height_above_min)) / len(patch_points),  # Elevated points ratio
        ]
        
        return np.array(feature_vector, dtype=np.float32)
    
    except Exception as e:
        print(f"Warning: Unbiased feature extraction failed: {e}")
        return np.zeros(22, dtype=np.float32)

def extract_patch_around_location_unbiased(points, location_coord, point_tree, radius=PATCH_RADIUS):
    """Extract patch around location WITHOUT centering (unbiased)"""
    
    # Find points within radius
    indices = point_tree.query_ball_point(location_coord, radius)
    
    if len(indices) < MIN_POINTS:
        return None
    
    patch_points = points[indices]
    
    # Subsample to target size
    patch_points = subsample_patch(patch_points, TARGET_PATCH_SIZE)
    
    # DON'T center the patch - use raw coordinates
    # This removes pile-location bias
    
    # Extract unbiased features
    features = extract_22_engineering_features_unbiased(patch_points)
    
    return {
        'features': features,
        'raw_patch': patch_points,
        'original_points': len(indices),
        'patch_location': location_coord
    }

def create_trino_dataset_unbiased(points, enhanced_pile_data):
    """Create dataset with unbiased feature extraction"""
    
    print(f"Creating UNBIASED dataset from {len(enhanced_pile_data)} pile locations...")
    
    # Create spatial index
    point_tree = cKDTree(points[:, :2])
    
    positive_features = []
    positive_metadata = []
    
    # Extract positive patches (around buffer pile locations) - UNBIASED
    for pile_data in enhanced_pile_data:
        location_coord = [pile_data['x'], pile_data['y']]
        patch_result = extract_patch_around_location_unbiased(points, location_coord, point_tree)
        
        if patch_result is not None:
            positive_features.append(patch_result['features'])
            positive_metadata.append({
                **pile_data,
                'label': 1,
                'patch_type': 'positive',
                'original_points': patch_result['original_points']
            })
    
    print(f"✅ Extracted {len(positive_features)} positive patches (UNBIASED)")
    
    # Create negative samples (targeted, not random) - UNBIASED
    print("Creating negative samples (UNBIASED)...")
    negative_features, negative_metadata = create_negative_samples_unbiased(
        points, enhanced_pile_data, point_tree, n_negatives=len(positive_features)
    )
    
    print(f"✅ Created {len(negative_features)} negative patches (UNBIASED)")
    
    # Combine positive and negative
    all_features = positive_features + negative_features
    all_metadata = positive_metadata + negative_metadata
    all_labels = [1] * len(positive_features) + [0] * len(negative_features)
    
    print(f"✅ Total UNBIASED dataset: {len(all_features)} samples")
    print(f"   Positive: {len(positive_features)} ({len(positive_features)/len(all_features)*100:.1f}%)")
    print(f"   Negative: {len(negative_features)} ({len(negative_features)/len(all_features)*100:.1f}%)")
    
    return np.array(all_features), np.array(all_labels), all_metadata

def create_negative_samples_unbiased(points, pile_data, point_tree, n_negatives):
    """Create negative samples avoiding pile areas (UNBIASED)"""
    
    pile_coords = np.array([[p['x'], p['y']] for p in pile_data])
    pile_tree = cKDTree(pile_coords)
    
    # Define bounds
    x_min, x_max = points[:, 0].min(), points[:, 0].max()
    y_min, y_max = points[:, 1].min(), points[:, 1].max()
    
    # Add buffer to avoid edges
    buffer = 50.0
    x_min += buffer
    x_max -= buffer
    y_min += buffer
    y_max -= buffer
    
    negative_features = []
    negative_metadata = []
    
    attempts = 0
    max_attempts = n_negatives * 5
    min_distance = PATCH_RADIUS * 2  # Stay away from pile areas
    
    while len(negative_features) < n_negatives and attempts < max_attempts:
        # Random location
        x = np.random.uniform(x_min, x_max)
        y = np.random.uniform(y_min, y_max)
        location_coord = [x, y]
        
        # Check distance to nearest pile
        dist_to_nearest_pile, _ = pile_tree.query(location_coord)
        
        if dist_to_nearest_pile > min_distance:
            patch_result = extract_patch_around_location_unbiased(points, location_coord, point_tree)
            
            if patch_result is not None:
                negative_features.append(patch_result['features'])
                negative_metadata.append({
                    'pile_id': f'negative_{len(negative_features)}',
                    'x': x,
                    'y': y,
                    'confidence': 'synthetic',
                    'source': 'negative_sampling',
                    'label': 0,
                    'patch_type': 'negative',
                    'original_points': patch_result['original_points'],
                    'site': SITE_NAME
                })
        
        attempts += 1
    
    return negative_features, negative_metadata

def validate_on_known_pile_locations_unbiased(model, pile_locations, points):
    """Apply unbiased validation: test detection on known pile locations"""
    
    print(f"Validating on {len(pile_locations)} known pile locations (UNBIASED)...")
    
    point_tree = cKDTree(points[:, :2])
    detected_piles = []
    detection_confidences = []
    failed_extractions = 0
    
    for pile_data in pile_locations:
        location_coord = [pile_data['x'], pile_data['y']]
        patch_result = extract_patch_around_location_unbiased(points, location_coord, point_tree)
        
        if patch_result is not None:
            # Get prediction
            features = patch_result['features'].reshape(1, -1)
            prediction = model.predict(features)[0]
            confidence = model.predict_proba(features)[0][1]
            
            detected_piles.append(prediction)
            detection_confidences.append(confidence)
        else:
            failed_extractions += 1
    
    # Calculate metrics
    detection_rate = np.mean(detected_piles) if detected_piles else 0
    avg_confidence = np.mean(detection_confidences) if detection_confidences else 0
    
    # Assessment
    if detection_rate >= 0.9:
        status = "EXCELLENT"
        color = "🟢"
    elif detection_rate >= 0.8:
        status = "GOOD"
        color = "🟡"
    elif detection_rate >= 0.6:
        status = "MODERATE"
        color = "🟠"
    else:
        status = "POOR"
        color = "🔴"
    
    print(f"\n{color} UNBIASED GROUND TRUTH VALIDATION RESULTS:")
    print(f"  Detection rate: {detection_rate*100:.1f}% ({np.sum(detected_piles)}/{len(detected_piles)})")
    print(f"  Average confidence: {avg_confidence:.3f}")
    print(f"  Status: {status}")
    print(f"  Failed extractions: {failed_extractions}")
    
    return {
        'detection_rate': detection_rate,
        'avg_confidence': avg_confidence,
        'status': status,
        'detected_piles': detected_piles,
        'detection_confidences': detection_confidences,
        'failed_extractions': failed_extractions,
        'total_tested': len(pile_locations)
    }

# Add this cell to compare both approaches

print("🔬 TESTING BIASED vs UNBIASED FEATURE EXTRACTION")
print("="*60)

# Test 1: Create unbiased dataset
print("\n1️⃣ Creating UNBIASED dataset...")
X_unbiased, y_unbiased, metadata_unbiased = create_trino_dataset_unbiased(
    filtered_points, enhanced_pile_data
)

# Test 2: Train unbiased model
print("\n2️⃣ Training UNBIASED model...")

# Use same train/test split approach for fair comparison
confidence_data_unbiased = organize_by_confidence(X_unbiased, y_unbiased, metadata_unbiased)
train_idx_unbiased, val_idx_unbiased, test_idx_unbiased = create_confidence_weighted_splits(
    X_unbiased, y_unbiased, metadata_unbiased, confidence_data_unbiased
)

X_train_unbiased = X_unbiased[train_idx_unbiased]
y_train_unbiased = y_unbiased[train_idx_unbiased]

X_val_unbiased = X_unbiased[val_idx_unbiased] if len(val_idx_unbiased) > 0 else None
y_val_unbiased = y_unbiased[val_idx_unbiased] if len(val_idx_unbiased) > 0 else None

model_unbiased = train_single_site_style_model(
    X_train_unbiased, y_train_unbiased, X_val_unbiased, y_val_unbiased
)

# Test 3: Validate unbiased model
print("\n3️⃣ Validating UNBIASED model...")
unbiased_validation_results = validate_on_known_pile_locations_unbiased(
    model_unbiased, enhanced_pile_data, filtered_points
)

# Test 4: Compare results
print("\n4️⃣ COMPARISON RESULTS:")
print("="*40)

print(f"\n📊 DATASET COMPARISON:")
print(f"   Biased dataset:   {len(X)} samples ({np.sum(y)} positive)")
print(f"   Unbiased dataset: {len(X_unbiased)} samples ({np.sum(y_unbiased)} positive)")

print(f"\n🎯 DETECTION RATE COMPARISON:")
print(f"   Biased approach:   {pile_validation_results['detection_rate']*100:.1f}% ({pile_validation_results['status']})")
print(f"   Unbiased approach: {unbiased_validation_results['detection_rate']*100:.1f}% ({unbiased_validation_results['status']})")

print(f"\n📈 CONFIDENCE COMPARISON:")
print(f"   Biased approach:   {pile_validation_results['avg_confidence']:.3f}")
print(f"   Unbiased approach: {unbiased_validation_results['avg_confidence']:.3f}")

print(f"\n⚠️ FAILED EXTRACTIONS:")
print(f"   Biased approach:   {pile_validation_results['failed_extractions']}")
print(f"   Unbiased approach: {unbiased_validation_results['failed_extractions']}")

# Test 5: Feature importance comparison
print(f"\n🔍 FEATURE IMPORTANCE COMPARISON:")
feature_importance_unbiased = analyze_feature_importance(model_unbiased)

print(f"\nTop 5 features - BIASED approach:")
for i, row in feature_importance.head(5).iterrows():
    print(f"   {row['feature']}: {row['importance']:.3f}")

print(f"\nTop 5 features - UNBIASED approach:")
for i, row in feature_importance_unbiased.head(5).iterrows():
    print(f"   {row['feature']}: {row['importance']:.3f}")

# Test 6: Which approach is better?
print(f"\n🏆 RECOMMENDATION:")
biased_rate = pile_validation_results['detection_rate']
unbiased_rate = unbiased_validation_results['detection_rate']

if abs(biased_rate - unbiased_rate) < 0.05:  # Within 5%
    print(f"   📊 SIMILAR PERFORMANCE: Both approaches achieve similar results")
    print(f"   ✅ This suggests the methodology is robust")
    if biased_rate > 0.9:
        print(f"   💡 CONCLUSION: Use BIASED approach for pile verification tasks")
        print(f"      (When you have suspected pile locations to verify)")
    else:
        print(f"   💡 CONCLUSION: Use UNBIASED approach for pile discovery tasks") 
        print(f"      (When searching for unknown piles)")
        
elif unbiased_rate > biased_rate + 0.05:  # Unbiased significantly better
    print(f"   🟢 UNBIASED APPROACH WINS!")
    print(f"   📈 Performance improvement: {(unbiased_rate - biased_rate)*100:.1f} percentage points")
    print(f"   💡 CONCLUSION: Use unbiased features for better generalization")
    
else:  # Biased significantly better
    print(f"   🟡 BIASED APPROACH WINS!")
    print(f"   📈 Performance advantage: {(biased_rate - unbiased_rate)*100:.1f} percentage points")
    print(f"   💡 CONCLUSION: Pile-centered features help for verification tasks")
    print(f"   ⚠️  NOTE: This approach assumes known pile locations")

print(f"\n📝 METHODOLOGY NOTE:")
print(f"   • Biased approach: Suitable for PILE VERIFICATION (known locations)")
print(f"   • Unbiased approach: Suitable for PILE DISCOVERY (unknown locations)")
print(f"   • Your use case determines which approach to use")

print("\n" + "="*60)

# Data Preparation for PointNet++ and DGCNN Comparison
# Add these functions to your notebook

import torch
import torch.nn as nn
import numpy as np
from sklearn.model_selection import train_test_split
from sklearn.metrics import accuracy_score, classification_report

def extract_coordinates_for_deep_learning(points, enhanced_pile_data, point_tree):
    """Extract 3D coordinates for deep learning (same patches as classical ML)"""
    
    print(f"Extracting 3D coordinates for deep learning from {len(enhanced_pile_data)} locations...")
    
    positive_patches = []
    positive_labels = []
    positive_metadata = []
    
    # Extract positive patches - SAME locations as classical ML
    for pile_data in enhanced_pile_data:
        pile_coord = [pile_data['x'], pile_data['y']]
        
        # Find points within radius (SAME as classical ML)
        indices = point_tree.query_ball_point(pile_coord, PATCH_RADIUS)
        
        if len(indices) < MIN_POINTS:
            continue
            
        patch_points = points[indices]
        
        # Subsample to target size (SAME as classical ML)
        if len(patch_points) > TARGET_PATCH_SIZE:
            sampled_indices = np.random.choice(len(patch_points), TARGET_PATCH_SIZE, replace=False)
            patch_points = patch_points[sampled_indices]
        elif len(patch_points) < TARGET_PATCH_SIZE:
            # Pad with duplicated points + small noise
            needed = TARGET_PATCH_SIZE - len(patch_points)
            duplicates = []
            for _ in range(needed):
                idx = np.random.randint(0, len(patch_points))
                duplicate = patch_points[idx].copy()
                # Add small noise to avoid identical points
                duplicate += np.random.normal(0, 0.01, 3)
                duplicates.append(duplicate)
            patch_points = np.vstack([patch_points, np.array(duplicates)])
        
        # Store raw coordinates (no centering for fair comparison with unbiased)
        positive_patches.append(patch_points.astype(np.float32))
        positive_labels.append(1)
        positive_metadata.append(pile_data)
    
    print(f"✅ Extracted {len(positive_patches)} positive coordinate patches")
    
    # Create negative samples - SAME strategy as classical ML
    negative_patches, negative_labels, negative_metadata = create_negative_coordinate_patches(
        points, enhanced_pile_data, point_tree, len(positive_patches)
    )
    
    print(f"✅ Created {len(negative_patches)} negative coordinate patches")
    
    # Combine positive and negative
    all_patches = positive_patches + negative_patches
    all_labels = positive_labels + negative_labels
    all_metadata = positive_metadata + negative_metadata
    
    print(f"✅ Total coordinate dataset: {len(all_patches)} patches")
    print(f"   Shape per patch: {all_patches[0].shape} (points, XYZ)")
    
    return np.array(all_patches), np.array(all_labels), all_metadata

def create_negative_coordinate_patches(points, pile_data, point_tree, n_negatives):
    """Create negative coordinate patches (same strategy as classical ML)"""
    
    pile_coords = np.array([[p['x'], p['y']] for p in pile_data])
    pile_tree = cKDTree(pile_coords)
    
    # Same bounds as classical ML
    x_min, x_max = points[:, 0].min(), points[:, 0].max()
    y_min, y_max = points[:, 1].min(), points[:, 1].max()
    
    buffer = 50.0
    x_min += buffer
    x_max -= buffer
    y_min += buffer
    y_max -= buffer
    
    negative_patches = []
    negative_labels = []
    negative_metadata = []
    
    attempts = 0
    max_attempts = n_negatives * 5
    min_distance = PATCH_RADIUS * 2
    
    while len(negative_patches) < n_negatives and attempts < max_attempts:
        # Random location
        x = np.random.uniform(x_min, x_max)
        y = np.random.uniform(y_min, y_max)
        
        # Check distance to nearest pile
        dist_to_nearest_pile, _ = pile_tree.query([x, y])
        
        if dist_to_nearest_pile > min_distance:
            # Extract patch
            indices = point_tree.query_ball_point([x, y], PATCH_RADIUS)
            
            if len(indices) >= MIN_POINTS:
                patch_points = points[indices]
                
                # Same subsampling as positive patches
                if len(patch_points) > TARGET_PATCH_SIZE:
                    sampled_indices = np.random.choice(len(patch_points), TARGET_PATCH_SIZE, replace=False)
                    patch_points = patch_points[sampled_indices]
                elif len(patch_points) < TARGET_PATCH_SIZE:
                    needed = TARGET_PATCH_SIZE - len(patch_points)
                    duplicates = []
                    for _ in range(needed):
                        idx = np.random.randint(0, len(patch_points))
                        duplicate = patch_points[idx].copy()
                        duplicate += np.random.normal(0, 0.01, 3)
                        duplicates.append(duplicate)
                    patch_points = np.vstack([patch_points, np.array(duplicates)])
                
                negative_patches.append(patch_points.astype(np.float32))
                negative_labels.append(0)
                negative_metadata.append({
                    'pile_id': f'negative_{len(negative_patches)}',
                    'x': x, 'y': y,
                    'confidence': 'synthetic',
                    'source': 'negative_sampling'
                })
        
        attempts += 1
    
    return negative_patches, negative_labels, negative_metadata

def normalize_patches_for_deep_learning(patches):
    """Normalize coordinate patches for deep learning"""
    
    normalized_patches = []
    
    for patch in patches:
        # Center each patch
        centroid = np.mean(patch, axis=0)
        centered = patch - centroid
        
        # Scale to unit sphere
        max_dist = np.max(np.linalg.norm(centered, axis=1))
        if max_dist > 1e-6:
            scaled = centered / max_dist
        else:
            scaled = centered
            
        normalized_patches.append(scaled)
    
    return np.array(normalized_patches)

def prepare_data_for_pytorch(patches, labels, test_size=0.2):
    """Prepare data for PyTorch training"""
    
    # Normalize patches
    normalized_patches = normalize_patches_for_deep_learning(patches)
    
    # Train/test split
    X_train, X_test, y_train, y_test = train_test_split(
        normalized_patches, labels, test_size=test_size, 
        random_state=42, stratify=labels
    )
    
    # Convert to PyTorch tensors
    X_train_tensor = torch.FloatTensor(X_train)
    X_test_tensor = torch.FloatTensor(X_test)
    y_train_tensor = torch.LongTensor(y_train)
    y_test_tensor = torch.LongTensor(y_test)
    
    print(f"PyTorch data prepared:")
    print(f"  Train: {X_train_tensor.shape} patches, {y_train_tensor.shape} labels")
    print(f"  Test: {X_test_tensor.shape} patches, {y_test_tensor.shape} labels")
    
    return X_train_tensor, X_test_tensor, y_train_tensor, y_test_tensor

# Create coordinate dataset from same locations as classical ML
print("\n🔄 PREPARING DATA FOR DEEP LEARNING COMPARISON")
print("="*50)

# Use same point tree and locations as classical ML
coordinate_patches, coordinate_labels, coordinate_metadata = extract_coordinates_for_deep_learning(
    filtered_points, enhanced_pile_data, cKDTree(filtered_points[:, :2])
)

# Prepare for PyTorch
X_train_dl, X_test_dl, y_train_dl, y_test_dl = prepare_data_for_pytorch(
    coordinate_patches, coordinate_labels
)

print(f"✅ Deep learning data ready!")
print(f"   Same {len(coordinate_patches)} patches as classical ML")
print(f"   Same {np.sum(coordinate_labels)} positive samples")
print(f"   Ready for PointNet++ and DGCNN training")

# Simplified PointNet++ and DGCNN Models for Fair Comparison

import torch
import torch.nn as nn
import torch.nn.functional as F
from torch.utils.data import DataLoader, TensorDataset
import time

class SimplePointNet(nn.Module):
    """Simplified PointNet++ for pile detection comparison"""
    
    def __init__(self, num_classes=2, num_points=64):
        super(SimplePointNet, self).__init__()
        
        # Feature extraction layers
        self.conv1 = nn.Conv1d(3, 64, 1)
        self.conv2 = nn.Conv1d(64, 128, 1)
        self.conv3 = nn.Conv1d(128, 256, 1)
        
        self.bn1 = nn.BatchNorm1d(64)
        self.bn2 = nn.BatchNorm1d(128)
        self.bn3 = nn.BatchNorm1d(256)
        
        # Classification head
        self.fc1 = nn.Linear(256, 128)
        self.fc2 = nn.Linear(128, 64)
        self.fc3 = nn.Linear(64, num_classes)
        
        self.dropout = nn.Dropout(0.3)
        
    def forward(self, x):
        # x shape: (batch_size, num_points, 3)
        x = x.transpose(2, 1)  # (batch_size, 3, num_points)
        
        # Feature extraction
        x = F.relu(self.bn1(self.conv1(x)))
        x = F.relu(self.bn2(self.conv2(x)))
        x = F.relu(self.bn3(self.conv3(x)))
        
        # Global max pooling
        x = torch.max(x, 2)[0]  # (batch_size, 256)
        
        # Classification
        x = F.relu(self.fc1(x))
        x = self.dropout(x)
        x = F.relu(self.fc2(x))
        x = self.dropout(x)
        x = self.fc3(x)
        
        return x

def get_knn_graph(x, k=20):
    """Get k-nearest neighbor graph for DGCNN"""
    batch_size, num_dims, num_points = x.size()
    
    # Compute pairwise distances
    inner = -2 * torch.matmul(x.transpose(2, 1), x)
    xx = torch.sum(x**2, dim=1, keepdim=True)
    pairwise_distance = -xx - inner - xx.transpose(2, 1)
    
    # Get k nearest neighbors
    idx = pairwise_distance.topk(k=k, dim=-1)[1]  # (batch_size, num_points, k)
    
    return idx

def get_edge_features(x, idx):
    """Get edge features for DGCNN"""
    batch_size, num_dims, num_points = x.size()
    k = idx.size(-1)
    
    # Get neighbor features
    idx_expanded = idx.unsqueeze(1).expand(batch_size, num_dims, num_points, k)
    neighbors = torch.gather(x.unsqueeze(-1).expand(-1, -1, -1, k), 2, idx_expanded)
    
    # Central features
    central = x.unsqueeze(-1).expand(-1, -1, -1, k)
    
    # Edge features: [central, neighbor - central]
    edge_features = torch.cat([central, neighbors - central], dim=1)
    
    return edge_features

class SimpleDGCNN(nn.Module):
    """Simplified DGCNN for pile detection comparison"""
    
    def __init__(self, num_classes=2, k=20):
        super(SimpleDGCNN, self).__init__()
        self.k = k
        
        # Edge convolution layers
        self.conv1 = nn.Conv2d(6, 64, 1)    # 3*2 = 6 (central + edge)
        self.conv2 = nn.Conv2d(128, 128, 1)  # 64*2 = 128
        self.conv3 = nn.Conv2d(256, 256, 1)  # 128*2 = 256
        
        self.bn1 = nn.BatchNorm2d(64)
        self.bn2 = nn.BatchNorm2d(128)
        self.bn3 = nn.BatchNorm2d(256)
        
        # Classification head
        self.conv4 = nn.Conv1d(448, 256, 1)  # 64+128+256 = 448
        self.conv5 = nn.Conv1d(256, 128, 1)
        self.conv6 = nn.Conv1d(128, num_classes, 1)
        
        self.bn4 = nn.BatchNorm1d(256)
        self.bn5 = nn.BatchNorm1d(128)
        
        self.dropout = nn.Dropout(0.3)
        
    def forward(self, x):
        # x shape: (batch_size, num_points, 3)
        x = x.transpose(2, 1)  # (batch_size, 3, num_points)
        batch_size, num_dims, num_points = x.size()
        
        # Edge convolution 1
        idx1 = get_knn_graph(x, self.k)
        edge_features1 = get_edge_features(x, idx1)
        x1 = F.relu(self.bn1(self.conv1(edge_features1)))
        x1 = x1.max(dim=-1)[0]  # (batch_size, 64, num_points)
        
        # Edge convolution 2
        idx2 = get_knn_graph(x1, self.k)
        edge_features2 = get_edge_features(x1, idx2)
        x2 = F.relu(self.bn2(self.conv2(edge_features2)))
        x2 = x2.max(dim=-1)[0]  # (batch_size, 128, num_points)
        
        # Edge convolution 3
        idx3 = get_knn_graph(x2, self.k)
        edge_features3 = get_edge_features(x2, idx3)
        x3 = F.relu(self.bn3(self.conv3(edge_features3)))
        x3 = x3.max(dim=-1)[0]  # (batch_size, 256, num_points)
        
        # Concatenate features
        x = torch.cat([x1, x2, x3], dim=1)  # (batch_size, 448, num_points)
        
        # Global features
        x = F.relu(self.bn4(self.conv4(x)))
        x = F.relu(self.bn5(self.conv5(x)))
        x = self.conv6(x)
        
        # Global max pooling
        x = torch.max(x, 2)[0]  # (batch_size, num_classes)
        
        return x

def train_deep_learning_model(model, X_train, y_train, X_test, y_test, model_name, epochs=50):
    """Train and evaluate deep learning model"""
    
    print(f"\n🚀 Training {model_name}...")
    
    # Setup
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    model = model.to(device)
    
    # Data loaders
    train_dataset = TensorDataset(X_train, y_train)
    test_dataset = TensorDataset(X_test, y_test)
    
    train_loader = DataLoader(train_dataset, batch_size=16, shuffle=True)
    test_loader = DataLoader(test_dataset, batch_size=16, shuffle=False)
    
    # Optimizer and loss
    optimizer = torch.optim.Adam(model.parameters(), lr=0.001, weight_decay=1e-4)
    criterion = nn.CrossEntropyLoss()
    
    # Training loop
    start_time = time.time()
    best_test_acc = 0
    
    for epoch in range(epochs):
        # Training
        model.train()
        train_loss = 0
        train_correct = 0
        train_total = 0
        
        for batch_x, batch_y in train_loader:
            batch_x, batch_y = batch_x.to(device), batch_y.to(device)
            
            optimizer.zero_grad()
            outputs = model(batch_x)
            loss = criterion(outputs, batch_y)
            loss.backward()
            optimizer.step()
            
            train_loss += loss.item()
            _, predicted = torch.max(outputs.data, 1)
            train_total += batch_y.size(0)
            train_correct += (predicted == batch_y).sum().item()
        
        # Testing
        model.eval()
        test_correct = 0
        test_total = 0
        
        with torch.no_grad():
            for batch_x, batch_y in test_loader:
                batch_x, batch_y = batch_x.to(device), batch_y.to(device)
                outputs = model(batch_x)
                _, predicted = torch.max(outputs.data, 1)
                test_total += batch_y.size(0)
                test_correct += (predicted == batch_y).sum().item()
        
        train_acc = train_correct / train_total
        test_acc = test_correct / test_total
        best_test_acc = max(best_test_acc, test_acc)
        
        if (epoch + 1) % 10 == 0:
            print(f"  Epoch {epoch+1}/{epochs}: Train Acc: {train_acc:.3f}, Test Acc: {test_acc:.3f}")
    
    training_time = time.time() - start_time
    
    # Final evaluation
    model.eval()
    all_predictions = []
    all_targets = []
    
    with torch.no_grad():
        for batch_x, batch_y in test_loader:
            batch_x, batch_y = batch_x.to(device), batch_y.to(device)
            outputs = model(batch_x)
            _, predicted = torch.max(outputs.data, 1)
            all_predictions.extend(predicted.cpu().numpy())
            all_targets.extend(batch_y.cpu().numpy())
    
    final_accuracy = accuracy_score(all_targets, all_predictions)
    
    print(f"✅ {model_name} training complete!")
    print(f"   Final test accuracy: {final_accuracy:.3f}")
    print(f"   Best test accuracy: {best_test_acc:.3f}")
    print(f"   Training time: {training_time:.1f}s")
    
    return {
        'model': model,
        'final_accuracy': final_accuracy,
        'best_accuracy': best_test_acc,
        'training_time': training_time,
        'predictions': all_predictions,
        'targets': all_targets
    }

# Train both models
print("\n🏁 TRAINING DEEP LEARNING MODELS FOR COMPARISON")
print("="*60)

# PointNet++
pointnet_model = SimplePointNet(num_classes=2, num_points=TARGET_PATCH_SIZE)
pointnet_results = train_deep_learning_model(
    pointnet_model, X_train_dl, y_train_dl, X_test_dl, y_test_dl, 
    "PointNet++", epochs=50
)

# DGCNN  
dgcnn_model = SimpleDGCNN(num_classes=2, k=10)  # Smaller k for 64 points
dgcnn_results = train_deep_learning_model(
    dgcnn_model, X_train_dl, y_train_dl, X_test_dl, y_test_dl,
    "DGCNN", epochs=50
)

# Comprehensive Algorithm Comparison Framework

def comprehensive_algorithm_comparison():
    """Compare all three approaches objectively across multiple criteria"""
    
    print("\n🏆 COMPREHENSIVE ALGORITHM COMPARISON")
    print("="*70)
    
    # Collect results from all approaches
    results = {
        'Classical ML (Unbiased)': {
            'detection_rate': unbiased_validation_results['detection_rate'],
            'test_accuracy': unbiased_validation_results['detection_rate'], 
            'training_time': '~30 seconds',
            'inference_speed': '~0.1ms per patch',
            'interpretability': 'High (22 engineering features)',
            'complexity': 'Low',
            'data_requirements': f"{len(X_unbiased)} samples",
            'failed_extractions': unbiased_validation_results['failed_extractions'],
            'confidence': unbiased_validation_results['avg_confidence']
        },
        'PointNet++': {
            'detection_rate': 'TBD - needs ground truth validation',
            'test_accuracy': pointnet_results['final_accuracy'],
            'training_time': f"{pointnet_results['training_time']:.1f}s",
            'inference_speed': '~1-5ms per patch',
            'interpretability': 'Low (learned 3D features)',
            'complexity': 'High (deep neural network)',
            'data_requirements': f"{len(X_train_dl)} samples",
            'failed_extractions': 'Same as classical ML',
            'confidence': 'TBD'
        },
        'DGCNN': {
            'detection_rate': 'TBD - needs ground truth validation',
            'test_accuracy': dgcnn_results['final_accuracy'],
            'training_time': f"{dgcnn_results['training_time']:.1f}s", 
            'inference_speed': '~5-10ms per patch',
            'interpretability': 'Low (learned graph features)',
            'complexity': 'Very High (graph neural network)',
            'data_requirements': f"{len(X_train_dl)} samples",
            'failed_extractions': 'Same as classical ML',
            'confidence': 'TBD'
        }
    }
    
    # Display comparison table
    print(f"\n📊 PERFORMANCE COMPARISON:")
    print(f"{'Metric':<20} {'Classical ML':<15} {'PointNet++':<15} {'DGCNN':<15}")
    print("-" * 70)
    
    metrics_to_compare = ['test_accuracy', 'training_time', 'interpretability', 'complexity']
    
    for metric in metrics_to_compare:
        classical = results['Classical ML (Unbiased)'][metric]
        pointnet = results['PointNet++'][metric]
        dgcnn = results['DGCNN'][metric]
        
        print(f"{metric:<20} {str(classical):<15} {str(pointnet):<15} {str(dgcnn):<15}")
    
    # Determine best approach for different scenarios
    print(f"\n🎯 SCENARIO-BASED RECOMMENDATIONS:")
    
    # Get numeric accuracies for comparison
    classical_acc = results['Classical ML (Unbiased)']['detection_rate']
    pointnet_acc = results['PointNet++']['test_accuracy']
    dgcnn_acc = results['DGCNN']['test_accuracy']
    
    accuracies = {
        'Classical ML': classical_acc,
        'PointNet++': pointnet_acc, 
        'DGCNN': dgcnn_acc
    }
    
    best_accuracy = max(accuracies.items(), key=lambda x: x[1])
    
    print(f"\n📈 ACCURACY RANKING:")
    sorted_acc = sorted(accuracies.items(), key=lambda x: x[1], reverse=True)
    for i, (method, acc) in enumerate(sorted_acc):
        print(f"   {i+1}. {method}: {acc:.3f}")
    
    # Scenario analysis
    scenarios = {
        'Maximum Accuracy': best_accuracy[0],
        'Fastest Training': 'Classical ML' if results['Classical ML (Unbiased)']['training_time'] == '~30 seconds' else 'Need to compare',
        'Most Interpretable': 'Classical ML',
        'Lowest Complexity': 'Classical ML',
        'Production Deployment': 'Classical ML' if classical_acc > 0.95 else best_accuracy[0],
        'Research Exploration': 'DGCNN' if dgcnn_acc > 0.90 else 'PointNet++'
    }
    
    print(f"\n🎯 SCENARIO RECOMMENDATIONS:")
    for scenario, recommendation in scenarios.items():
        print(f"   {scenario:<25}: {recommendation}")
    
    # Overall assessment
    print(f"\n🏆 OVERALL ASSESSMENT:")
    
    if classical_acc >= max(pointnet_acc, dgcnn_acc) - 0.05:  # Within 5%
        print(f"   🟢 CLASSICAL ML RECOMMENDED")
        print(f"   📊 Achieves comparable accuracy ({classical_acc:.3f}) with significant advantages:")
        print(f"      • Much faster training and inference")
        print(f"      • Highly interpretable features")
        print(f"      • Lower computational requirements")
        print(f"      • Easier deployment and maintenance")
        
    elif pointnet_acc > classical_acc + 0.05:
        print(f"   🟡 POINTNET++ SHOWS PROMISE")
        print(f"   📊 Higher accuracy ({pointnet_acc:.3f} vs {classical_acc:.3f})")
        print(f"   ⚠️  Trade-offs: Higher complexity, longer training, less interpretable")
        
    elif dgcnn_acc > classical_acc + 0.05:
        print(f"   🟠 DGCNN SHOWS POTENTIAL")
        print(f"   📊 Higher accuracy ({dgcnn_acc:.3f} vs {classical_acc:.3f})")
        print(f"   ⚠️  Trade-offs: Highest complexity, longest training, least interpretable")
    
    else:
        print(f"   🔄 MIXED RESULTS - CONTEXT DEPENDENT")
        print(f"   📊 Similar accuracies across approaches")
        print(f"   💡 Choice depends on specific deployment requirements")
    
    # Practical deployment guidance
    print(f"\n💡 PRACTICAL DEPLOYMENT GUIDANCE:")
    print(f"   🚀 Quick Deployment: Classical ML (30s training, immediate results)")
    print(f"   🎯 Maximum Accuracy: {best_accuracy[0]} ({best_accuracy[1]:.3f} accuracy)")
    print(f"   🔍 Interpretable Results: Classical ML (engineers can understand features)")
    print(f"   🏭 Large Scale Production: Classical ML (lower computational overhead)")
    print(f"   🔬 Research Applications: All approaches valid for different research questions")
    
    return results

def validate_deep_learning_on_ground_truth(model, model_name, coordinate_patches, coordinate_labels, coordinate_metadata):
    """Validate deep learning model on same ground truth as classical ML"""
    
    print(f"\n🔍 GROUND TRUTH VALIDATION: {model_name}")
    
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    model.eval()
    
    detected_piles = []
    detection_confidences = []
    
    # Test on same pile locations as classical ML
    positive_indices = [i for i, meta in enumerate(coordinate_metadata) if meta.get('confidence') != 'synthetic']
    
    with torch.no_grad():
        for idx in positive_indices:
            patch = coordinate_patches[idx:idx+1]  # Single patch
            patch_tensor = torch.FloatTensor(patch).to(device)
            
            # Normalize same as training
            centroid = torch.mean(patch_tensor, dim=1, keepdim=True)
            centered = patch_tensor - centroid
            max_dist = torch.max(torch.norm(centered, dim=2, keepdim=True))
            if max_dist > 1e-6:
                normalized = centered / max_dist
            else:
                normalized = centered
            
            outputs = model(normalized)
            probabilities = F.softmax(outputs, dim=1)
            prediction = torch.argmax(outputs, dim=1).item()
            confidence = probabilities[0, 1].item()  # Probability of pile class
            
            detected_piles.append(prediction)
            detection_confidences.append(confidence)
    
    # Calculate metrics
    detection_rate = np.mean(detected_piles)
    avg_confidence = np.mean(detection_confidences)
    
    # Assessment
    if detection_rate >= 0.9:
        status = "EXCELLENT"
        color = "🟢"
    elif detection_rate >= 0.8:
        status = "GOOD"
        color = "🟡"
    elif detection_rate >= 0.6:
        status = "MODERATE"
        color = "🟠"
    else:
        status = "POOR"
        color = "🔴"
    
    print(f"{color} {model_name} Ground Truth Results:")
    print(f"   Detection Rate: {detection_rate*100:.1f}% ({np.sum(detected_piles)}/{len(detected_piles)})")
    print(f"   Average Confidence: {avg_confidence:.3f}")
    print(f"   Status: {status}")
    
    return {
        'detection_rate': detection_rate,
        'avg_confidence': avg_confidence,
        'status': status,
        'detected_piles': detected_piles,
        'detection_confidences': detection_confidences
    }

# Run ground truth validation for deep learning models
print("\n🔍 VALIDATING DEEP LEARNING MODELS ON GROUND TRUTH")
print("="*60)

pointnet_ground_truth = validate_deep_learning_on_ground_truth(
    pointnet_results['model'], "PointNet++", coordinate_patches, coordinate_labels, coordinate_metadata
)

dgcnn_ground_truth = validate_deep_learning_on_ground_truth(
    dgcnn_results['model'], "DGCNN", coordinate_patches, coordinate_labels, coordinate_metadata
)

# Update results with ground truth validation
pointnet_results['ground_truth_detection'] = pointnet_ground_truth['detection_rate']
pointnet_results['ground_truth_confidence'] = pointnet_ground_truth['avg_confidence']
pointnet_results['ground_truth_status'] = pointnet_ground_truth['status']

dgcnn_results['ground_truth_detection'] = dgcnn_ground_truth['detection_rate']
dgcnn_results['ground_truth_confidence'] = dgcnn_ground_truth['avg_confidence']
dgcnn_results['ground_truth_status'] = dgcnn_ground_truth['status']

# Run comprehensive comparison
final_comparison = comprehensive_algorithm_comparison()

# Save comprehensive comparison results
timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

comparison_results = {
    'timestamp': timestamp,
    'site': SITE_NAME,
    'comparison_type': 'classical_ml_vs_deep_learning',
    'approaches_tested': ['Classical ML (Unbiased)', 'PointNet++', 'DGCNN'],
    'results': {
        'classical_ml': {
            'detection_rate': unbiased_validation_results['detection_rate'],
            'avg_confidence': unbiased_validation_results['avg_confidence'],
            'status': unbiased_validation_results['status'],
            'training_time_seconds': 30,
            'interpretability': 'High',
            'complexity': 'Low'
        },
        'pointnet': {
            'test_accuracy': pointnet_results['final_accuracy'],
            'detection_rate': pointnet_results['ground_truth_detection'],
            'avg_confidence': pointnet_results['ground_truth_confidence'],
            'status': pointnet_results['ground_truth_status'],
            'training_time_seconds': pointnet_results['training_time'],
            'interpretability': 'Low',
            'complexity': 'High'
        },
        'dgcnn': {
            'test_accuracy': dgcnn_results['final_accuracy'],
            'detection_rate': dgcnn_results['ground_truth_detection'],
            'avg_confidence': dgcnn_results['ground_truth_confidence'],
            'status': dgcnn_results['ground_truth_status'],
            'training_time_seconds': dgcnn_results['training_time'],
            'interpretability': 'Low',
            'complexity': 'Very High'
        }
    },
    'conclusions': {
        'best_accuracy': max([
            ('Classical ML', unbiased_validation_results['detection_rate']),
            ('PointNet++', pointnet_results['ground_truth_detection']),
            ('DGCNN', dgcnn_results['ground_truth_detection'])
        ], key=lambda x: x[1]),
        'fastest_training': 'Classical ML',
        'most_interpretable': 'Classical ML',
        'recommended_for_production': 'TBD based on results'
    }
}

# Save results
comparison_file = output_dir / f"algorithm_comparison_{timestamp}.json"
with open(comparison_file, 'w') as f:
    json.dump(comparison_results, f, indent=2, default=str)

print(f"\n💾 COMPARISON RESULTS SAVED")
print(f"   File: {comparison_file.name}")

# Create final visualization
def create_comparison_visualization():
    """Create comprehensive comparison visualization"""
    
    fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(15, 12))
    
    # Plot 1: Detection Rate Comparison
    methods = ['Classical ML', 'PointNet++', 'DGCNN']
    detection_rates = [
        unbiased_validation_results['detection_rate'],
        pointnet_results['ground_truth_detection'],
        dgcnn_results['ground_truth_detection']
    ]
    
    colors = ['green', 'blue', 'orange']
    bars1 = ax1.bar(methods, detection_rates, color=colors, alpha=0.7)
    ax1.set_title('Ground Truth Detection Rate')
    ax1.set_ylabel('Detection Rate')
    ax1.set_ylim(0, 1.1)
    
    # Add value labels on bars
    for bar, rate in zip(bars1, detection_rates):
        height = bar.get_height()
        ax1.text(bar.get_x() + bar.get_width()/2., height + 0.01,
                f'{rate:.3f}', ha='center', va='bottom')
    
    # Plot 2: Training Time Comparison
    training_times = [30, pointnet_results['training_time'], dgcnn_results['training_time']]
    bars2 = ax2.bar(methods, training_times, color=colors, alpha=0.7)
    ax2.set_title('Training Time (seconds)')
    ax2.set_ylabel('Time (s)')
    ax2.set_yscale('log')  # Log scale due to large differences
    
    for bar, time in zip(bars2, training_times):
        height = bar.get_height()
        ax2.text(bar.get_x() + bar.get_width()/2., height * 1.1,
                f'{time:.1f}s', ha='center', va='bottom')
    
    # Plot 3: Complexity vs Performance
    complexity_scores = [1, 3, 4]  # 1=Low, 3=High, 4=Very High
    ax3.scatter(complexity_scores, detection_rates, c=colors, s=200, alpha=0.7)
    
    for i, method in enumerate(methods):
        ax3.annotate(method, (complexity_scores[i], detection_rates[i]), 
                    xytext=(5, 5), textcoords='offset points')
    
    ax3.set_xlabel('Complexity (1=Low, 4=Very High)')
    ax3.set_ylabel('Detection Rate')
    ax3.set_title('Complexity vs Performance Trade-off')
    ax3.grid(True, alpha=0.3)
    
    # Plot 4: Multi-criteria Radar Chart (simplified as bar chart)
    criteria = ['Accuracy', 'Speed', 'Interpretability', 'Simplicity']
    
    # Normalize scores to 0-1 scale
    classical_scores = [
        unbiased_validation_results['detection_rate'],  # Accuracy
        1.0,  # Speed (fastest)
        1.0,  # Interpretability (highest)
        1.0   # Simplicity (highest)
    ]
    
    pointnet_scores = [
        pointnet_results['ground_truth_detection'],  # Accuracy
        0.6,  # Speed (medium)
        0.2,  # Interpretability (low)
        0.3   # Simplicity (low)
    ]
    
    dgcnn_scores = [
        dgcnn_results['ground_truth_detection'],  # Accuracy
        0.3,  # Speed (slow)
        0.1,  # Interpretability (very low)
        0.1   # Simplicity (very low)
    ]
    
    x = np.arange(len(criteria))
    width = 0.25
    
    ax4.bar(x - width, classical_scores, width, label='Classical ML', color='green', alpha=0.7)
    ax4.bar(x, pointnet_scores, width, label='PointNet++', color='blue', alpha=0.7)
    ax4.bar(x + width, dgcnn_scores, width, label='DGCNN', color='orange', alpha=0.7)
    
    ax4.set_xlabel('Criteria')
    ax4.set_ylabel('Score (0-1)')
    ax4.set_title('Multi-Criteria Comparison')
    ax4.set_xticks(x)
    ax4.set_xticklabels(criteria)
    ax4.legend()
    ax4.grid(True, alpha=0.3)
    
    plt.tight_layout()
    
    # Save plot
    plot_file = output_dir / f"algorithm_comparison_plot_{timestamp}.png"
    plt.savefig(plot_file, dpi=300, bbox_inches='tight')
    plt.show()
    
    print(f"📊 Comparison visualization saved: {plot_file.name}")
    return plot_file

comparison_plot = create_comparison_visualization()

print("\n🎉 COMPREHENSIVE ALGORITHM COMPARISON COMPLETE!")
print("="*60)
print(f"📊 Results Summary:")
print(f"   Classical ML: {unbiased_validation_results['detection_rate']*100:.1f}% detection")
print(f"   PointNet++: {pointnet_results['ground_truth_detection']*100:.1f}% detection")
print(f"   DGCNN: {dgcnn_results['ground_truth_detection']*100:.1f}% detection")
print(f"\n📁 Files Generated:")
print(f"   • {comparison_file.name}")
print(f"   • {comparison_plot.name}")
print(f"\n💡 This comparison provides objective evidence for your thesis methodology choice!")

