{"cells": [{"cell_type": "markdown", "id": "52d7f55f", "metadata": {}, "source": ["# Spatial Relationship Analysis\n", "\n", "Assess spatial alignment between IFC (design) and KML (as-built) pile locations to detect deviations and validate geometric accuracy.\n", "\n", "**Objectives:**\n", "- Match KML centroids to IFC piles within defined tolerance\n", "- Filter data based on quality metrics and confidence scores\n", "- Analyze alignment patterns and systematic offsets\n", "- Generate geometric QA reports for validation\n", "\n", "**Author**: <PERSON><PERSON><PERSON>  \n", "**Date**: July 2025  \n", "**Project**: As-Built Foundation Analysis"]}, {"cell_type": "code", "execution_count": 57, "id": "0c4051bd", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Notebooks root: /Users/<USER>/Documents/GitHub/asbuilt-foundation-analysis/notebooks\n"]}], "source": ["# Papermill parameters - these will be injected by Papermill\n", "from datetime import datetime\n", "import sys\n", "from pathlib import Path\n", "\n", "# Add the `notebooks` folder to sys.path\n", "notebooks_root = Path(__file__).resolve().parents[2] if '__file__' in globals() else Path.cwd().parents[2]\n", "print(f\"Notebooks root: {notebooks_root}\")\n", "\n", "if str(notebooks_root) not in sys.path:\n", "    sys.path.insert(0, str(notebooks_root))\n", "\n", "# Now import from shared package\n", "from shared.config import (\n", "    get_data_path,\n", "    get_processed_data_path\n", ")\n", "site_name = \"trino_enel\"  # Site name for output file naming\n", "\n", "timestamp = datetime.now().strftime(\"%Y%m%d_%H%M%S\")"]}, {"cell_type": "code", "execution_count": 58, "id": "b41dea7d", "metadata": {}, "outputs": [], "source": ["\n", "# Basic imports\n", "import numpy as np\n", "import pandas as pd\n", "import open3d as o3d\n", "import geopandas as gpd\n", "import matplotlib.pyplot as plt\n", "from scipy.spatial import cKDTree\n", "from scipy.spatial.distance import cdist\n", "from pathlib import Path"]}, {"cell_type": "code", "execution_count": 59, "id": "06196fcb", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Loaded point cloud: 517,002 points\n"]}], "source": ["# Load point cloud\n", "ply_path = get_processed_data_path(site_name, \"ml_local_alignment\") / \"trino_enel_ml_corrected.ply\"\n", "#'/Users/<USER>/Documents/GitHub/asbuilt-foundation-analysis/data/processed/trino_enel/ml_local_alignment/trino_enel_ml_corrected.ply'\n", "point_cloud = o3d.io.read_point_cloud(ply_path)\n", "points = np.asarray(point_cloud.points)\n", "\n", "print(f\"Loaded point cloud: {len(points):,} points\")\n", "\n"]}, {"cell_type": "code", "execution_count": 60, "id": "4360dd70", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Loaded IFC data: 14460 pile locations\n"]}], "source": ["# Load IFC metadata\n", "ifc_path = get_processed_data_path(site_name, \"ifc_metadata\") / \"GRE.EEC.S.00.IT.P.14353.00.265_enhanced_metadata.csv\"\n", "#\"/Users/<USER>/Documents/GitHub/asbuilt-foundation-analysis/data/processed/trino_enel/ifc_metadata/GRE.EEC.S.00.IT.P.14353.00.265_enhanced_metadata.csv\"\n", "ifc_df = pd.read_csv(ifc_path)\n", "ifc_coords = ifc_df[['X', 'Y', 'Z']].values\n", "\n", "print(f\"Loaded IFC data: {len(ifc_coords)} pile locations\")\n"]}, {"cell_type": "code", "execution_count": 61, "id": "a84c3887", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Loaded KML data: 1288 pile locations\n"]}], "source": ["# Load and transform KML data\n", "kml_path = get_data_path(site_name) / \"kml\" / \"pile.kml\"\n", "#\"/Users/<USER>/Documents/GitHub/asbuilt-foundation-analysis/data/raw/trino_enel/kml/pile.kml\"\n", "gdf_kml = gpd.read_file(kml_path, driver='KML')\n", "\n", "# Ensure we have valid geometries\n", "gdf_kml = gdf_kml[gdf_kml.geometry.notnull()].copy()\n", "\n", "# Set CRS and reproject\n", "gdf_kml = gdf_kml.set_crs(epsg=4326)\n", "gdf_kml_utm = gdf_kml.to_crs(epsg=32632)\n", "\n", "# Convert to centroids\n", "gdf_kml_utm['geometry'] = gdf_kml_utm.geometry.apply(\n", "    lambda geom: geom.centroid if geom.geom_type != 'Point' else geom\n", ")\n", "\n", "# Extract KML coordinates\n", "kml_coords = np.stack([\n", "    gdf_kml_utm.geometry.x.values,\n", "    gdf_kml_utm.geometry.y.values,\n", "    np.zeros(len(gdf_kml_utm))\n", "], axis=1)\n", "\n", "print(f\"Loaded KML data: {len(kml_coords)} pile locations\")\n"]}, {"cell_type": "code", "execution_count": 62, "id": "408b5e94", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Loaded KML quality assessment: 1288 records\n", "Loaded IFC assessment: 14460 records\n", "\n", "Analyzing spatial relationships between KML and IFC...\n"]}], "source": ["# Load quality assessments from previous notebooks (if available)\n", "try:\n", "    kml_quality_df = pd.read_csv(\"kml_quality_assessment.csv\")\n", "    print(f\"Loaded KML quality assessment: {len(kml_quality_df)} records\")\n", "    has_kml_quality = True\n", "except FileNotFoundError:\n", "    print(\"KML quality assessment not found - will analyze all KML locations\")\n", "    has_kml_quality = False\n", "\n", "try:\n", "    ifc_assessment_df = pd.read_csv(\"ifc_assessment.csv\")\n", "    print(f\"Loaded IFC assessment: {len(ifc_assessment_df)} records\")\n", "    has_ifc_assessment = True\n", "except FileNotFoundError:\n", "    print(\"IFC assessment not found - will analyze all IFC locations\")\n", "    has_ifc_assessment = False\n", "\n", "print(\"\\nAnalyzing spatial relationships between KML and IFC...\")\n"]}, {"cell_type": "code", "execution_count": 63, "id": "14a620a1", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Using quality-filtered data:\n", "Good KML locations (>=10 points): 0\n", "Usable IFC locations: 11089\n"]}], "source": ["# If quality assessments exist, filter to high-quality data\n", "if has_kml_quality and has_ifc_assessment:\n", "    # FIXED: Use correct threshold (>=10 instead of >10)\n", "    good_kml_indices = kml_quality_df[kml_quality_df['point_count'] >= 10]['kml_idx'].values\n", "    good_ifc_indices = ifc_assessment_df[\n", "        ifc_assessment_df['classification'].isin(['potential_pile', 'unclear']) &\n", "        ifc_assessment_df['confidence'].isin(['high', 'medium'])\n", "    ]['ifc_idx'].values\n", "    \n", "    good_kml_coords = kml_coords[good_kml_indices]\n", "    good_ifc_coords = ifc_coords[good_ifc_indices]\n", "    \n", "    print(f\"Using quality-filtered data:\")\n", "    print(f\"Good KML locations (>=10 points): {len(good_kml_coords)}\")\n", "    print(f\"Usable IFC locations: {len(good_ifc_coords)}\")\n", "else:\n", "    # Use all data\n", "    good_kml_coords = kml_coords\n", "    good_ifc_coords = ifc_coords\n", "    good_kml_indices = np.arange(len(kml_coords))\n", "    good_ifc_indices = np.arange(len(ifc_coords))\n", "    \n", "    print(f\"Using all available data:\")\n", "    print(f\"All KML locations: {len(good_kml_coords)}\")\n", "    print(f\"All IFC locations: {len(good_ifc_coords)}\")\n", "\n"]}, {"cell_type": "code", "execution_count": 64, "id": "18ba85a4", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["No valid data available for spatial relationship analysis\n", "\n", "Spatial relationship analysis complete.\n"]}], "source": ["# Distance analysis between KML and IFC\n", "if len(good_kml_coords) > 0 and len(good_ifc_coords) > 0:\n", "    distances = cdist(good_kml_coords[:, :2], good_ifc_coords[:, :2])\n", "    \n", "    # Find matches within threshold\n", "    max_distance = 10.0  # meters\n", "    matches = []\n", "    used_ifc_indices = set()  # Track used IFC locations to avoid duplicates\n", "    \n", "    for kml_idx, kml_coord in enumerate(good_kml_coords):\n", "        distances_to_ifc = distances[kml_idx]\n", "        closest_ifc_idx = np.argmin(distances_to_ifc)\n", "        closest_distance = distances_to_ifc[closest_ifc_idx]\n", "        \n", "        # Check if within distance threshold and not already matched\n", "        if closest_distance <= max_distance and closest_ifc_idx not in used_ifc_indices:\n", "            # Get quality scores if available\n", "            if has_kml_quality:\n", "                original_kml_idx = good_kml_indices[kml_idx]\n", "                kml_record = kml_quality_df[kml_quality_df['kml_idx'] == original_kml_idx].iloc[0]\n", "                kml_quality = kml_record['quality_score']\n", "                kml_point_count = kml_record['point_count']\n", "            else:\n", "                kml_quality = 1.0  # Default\n", "                kml_point_count = 0\n", "                \n", "            if has_ifc_assessment:\n", "                original_ifc_idx = good_ifc_indices[closest_ifc_idx]\n", "                ifc_record = ifc_assessment_df[ifc_assessment_df['ifc_idx'] == original_ifc_idx].iloc[0]\n", "                ifc_confidence = ifc_record['confidence']\n", "                ifc_classification = ifc_record['classification']\n", "            else:\n", "                ifc_confidence = 'unknown'\n", "                ifc_classification = 'unknown'\n", "            \n", "            matches.append({\n", "                'kml_idx': good_kml_indices[kml_idx],\n", "                'ifc_idx': good_ifc_indices[closest_ifc_idx],\n", "                'distance': closest_distance,\n", "                'kml_quality': kml_quality,\n", "                'kml_point_count': kml_point_count,\n", "                'ifc_confidence': ifc_confidence,\n", "                'ifc_classification': ifc_classification\n", "            })\n", "            \n", "            used_ifc_indices.add(closest_ifc_idx)\n", "    \n", "    matches_df = pd.DataFrame(matches)\n", "    print(f\"\\nMatched pairs within {max_distance}m: {len(matches_df)}\")\n", "    \n", "    if len(matches_df) > 0:\n", "        print(f\"Average match distance: {matches_df['distance'].mean():.2f}m\")\n", "        print(f\"Median match distance: {matches_df['distance'].median():.2f}m\")\n", "        print(f\"Distance range: {matches_df['distance'].min():.2f}m to {matches_df['distance'].max():.2f}m\")\n", "        \n", "        # Distance distribution\n", "        plt.figure(figsize=(12, 4))\n", "        plt.subplot(1, 3, 1)\n", "        plt.hist(matches_df['distance'], bins=20, alpha=0.7, edgecolor='black')\n", "        plt.xlabel(\"Distance (m)\")\n", "        plt.ylabel(\"Matched Pairs\")\n", "        plt.title(\"KML-IFC Match Distances\")\n", "        plt.grid(True, alpha=0.3)\n", "        \n", "        # Quality distribution of matched pairs\n", "        plt.subplot(1, 3, 2)\n", "        plt.hist(matches_df['kml_point_count'], bins=15, alpha=0.7, edgecolor='black')\n", "        plt.xlabel(\"KML Point Count\")\n", "        plt.ylabel(\"Matched Pairs\")\n", "        plt.title(\"KML Quality in Matched Pairs\")\n", "        plt.grid(True, alpha=0.3)\n", "        \n", "        # Spatial distribution\n", "        plt.subplot(1, 3, 3)\n", "        # Sample point cloud for background\n", "        sample_points = points[::1000]\n", "        plt.scatter(sample_points[:, 0], sample_points[:, 1], s=0.1, alpha=0.2, color='gray', label='Point Cloud')\n", "        \n", "        # Plot matched pairs\n", "        matched_kml = good_kml_coords[:len(matches_df)]\n", "        matched_ifc = good_ifc_coords[[np.where(good_ifc_indices == idx)[0][0] for idx in matches_df['ifc_idx']]]\n", "        \n", "        plt.scatter(matched_kml[:, 0], matched_kml[:, 1], s=20, alpha=0.8, color='blue', label=f'KML Matched ({len(matches_df)})')\n", "        plt.scatter(matched_ifc[:, 0], matched_ifc[:, 1], s=10, alpha=0.6, color='red', label=f'IFC Matched ({len(matches_df)})')\n", "        \n", "        plt.xlabel(\"X (m)\")\n", "        plt.ylabel(\"Y (m)\")\n", "        plt.title(\"Spatial Distribution of Matches\")\n", "        plt.legend()\n", "        plt.grid(True, alpha=0.3)\n", "        plt.tight_layout()\n", "        plt.show()\n", "        \n", "        # Match quality analysis\n", "        print(f\"\\nMatch quality analysis:\")\n", "        print(f\"High quality KML matches (>30 points): {(matches_df['kml_point_count'] > 30).sum()}\")\n", "        print(f\"Medium quality KML matches (10-30 points): {((matches_df['kml_point_count'] >= 10) & (matches_df['kml_point_count'] <= 30)).sum()}\")\n", "        \n", "        ifc_confidence_counts = matches_df['ifc_confidence'].value_counts()\n", "        print(f\"IFC confidence distribution in matches:\")\n", "        for conf, count in ifc_confidence_counts.items():\n", "            print(f\"  {conf}: {count}\")\n", "        \n", "        # Save matches\n", "        matches_df.to_csv(\"kml_ifc_matches.csv\", index=False)\n", "        print(f\"\\nMatches saved to: kml_ifc_matches.csv\")\n", "    \n", "    # Identify unmatched locations\n", "    matched_kml_indices = set(matches_df['kml_idx'].values) if len(matches_df) > 0 else set()\n", "    matched_ifc_indices = set(matches_df['ifc_idx'].values) if len(matches_df) > 0 else set()\n", "    \n", "    unmatched_kml = [idx for idx in good_kml_indices if idx not in matched_kml_indices]\n", "    unmatched_ifc = [idx for idx in good_ifc_indices if idx not in matched_ifc_indices]\n", "    \n", "    print(f\"\\nUnmatched analysis:\")\n", "    print(f\"Unmatched KML locations: {len(unmatched_kml)}\")\n", "    print(f\"Unmatched IFC locations: {len(unmatched_ifc)}\")\n", "    \n", "    # Summary statistics\n", "    total_good_kml = len(good_kml_indices)\n", "    total_good_ifc = len(good_ifc_indices)\n", "    \n", "    print(f\"\\nSummary statistics:\")\n", "    print(f\"KML match rate: {len(matched_kml_indices)}/{total_good_kml} ({len(matched_kml_indices)/total_good_kml*100:.1f}%)\")\n", "    print(f\"IFC match rate: {len(matched_ifc_indices)}/{total_good_ifc} ({len(matched_ifc_indices)/total_good_ifc*100:.1f}%)\")\n", "\n", "else:\n", "    print(\"No valid data available for spatial relationship analysis\")\n", "\n", "print(\"\\nSpatial relationship analysis complete.\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 5}