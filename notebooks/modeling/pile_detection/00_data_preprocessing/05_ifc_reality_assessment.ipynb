{"cells": [{"cell_type": "markdown", "id": "7b3a0ce1", "metadata": {}, "source": ["# IFC vs Reality Assessment\n", "\n", "Compare IFC-designed pile locations with as-built point cloud data to detect construction deviations and validate design implementation.\n", "\n", "**Objectives:**\n", "- Validate IFC pile locations against point cloud coverage\n", "- Analyze local 3D geometry and construction status\n", "- Classify pile construction state with confidence scoring\n", "- Generate deviation analysis and quality assessment reports\n", "\n", "**Author**: <PERSON><PERSON><PERSON>  \n", "**Date**: August 2025  \n", "**Project**: As-Built Foundation Analysis"]}, {"cell_type": "code", "execution_count": 53, "id": "949b3ab8", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Notebooks root: /Users/<USER>/Documents/GitHub/asbuilt-foundation-analysis/notebooks\n", "Output path: /Users/<USER>/Documents/GitHub/asbuilt-foundation-analysis/data/processed/trino_enel/reference-data\n"]}], "source": ["# Papermill parameters - these will be injected by Papermill\n", "from datetime import datetime\n", "import sys\n", "from pathlib import Path\n", "\n", "# Add the `notebooks` folder to sys.path\n", "notebooks_root = Path(__file__).resolve().parents[2] if '__file__' in globals() else Path.cwd().parents[2]\n", "print(f\"Notebooks root: {notebooks_root}\")\n", "\n", "if str(notebooks_root) not in sys.path:\n", "    sys.path.insert(0, str(notebooks_root))\n", "\n", "# Now import from shared package\n", "from shared.config import (\n", "    get_data_path,\n", "    get_processed_data_path\n", ")\n", "site_name = \"trino_enel\"  # Site name for output file naming\n", "\n", "timestamp = datetime.now().strftime(\"%Y%m%d_%H%M%S\")\n", "\n", "# Standardized paths\n", "output_path = get_processed_data_path(site_name, f\"reference-data\")\n", "print(f\"Output path: {output_path}\")\n", "output_path.mkdir(parents=True, exist_ok=True)\n", "CRS = \"EPSG:32632\""]}, {"cell_type": "code", "execution_count": 54, "id": "4a3a0ec4", "metadata": {}, "outputs": [], "source": ["# Notebook 4: IFC vs Reality Assessment\n", "\n", "# Basic imports\n", "import numpy as np\n", "import pandas as pd\n", "import open3d as o3d\n", "import matplotlib.pyplot as plt\n", "from scipy.spatial import cKDTree\n", "from pathlib import Path\n"]}, {"cell_type": "code", "execution_count": 55, "id": "1b2733a9", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Loaded point cloud: 517,002 points\n", "Point cloud bounds: X(435220.8, 436795.4) Y(5010813.8, 5012552.6) Z(154.9, 179.5)\n"]}], "source": ["# Load point cloud\n", "ply_path = get_processed_data_path(site_name, \"ml_local_alignment\") / \"trino_enel_ml_corrected.ply\"\n", "point_cloud = o3d.io.read_point_cloud(ply_path)\n", "points = np.asarray(point_cloud.points)\n", "\n", "print(f\"Loaded point cloud: {len(points):,} points\")\n", "print(f\"Point cloud bounds: X({points[:, 0].min():.1f}, {points[:, 0].max():.1f}) Y({points[:, 1].min():.1f}, {points[:, 1].max():.1f}) Z({points[:, 2].min():.1f}, {points[:, 2].max():.1f})\")\n"]}, {"cell_type": "code", "execution_count": 56, "id": "14c92429", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Loaded IFC data: 14460 pile locations\n", "IFC bounds: X(435267.2, 436719.9) Y(5010900.7, 5012462.4) Z(155.0, 159.5)\n"]}], "source": ["# Load IFC metadata\n", "ifc_path = get_processed_data_path(site_name, \"ifc_metadata\") / \"GRE.EEC.S.00.IT.P.14353.00.265_enhanced_metadata.csv\"\n", "ifc_df = pd.read_csv(ifc_path)\n", "ifc_coords = ifc_df[['X', 'Y', 'Z']].values\n", "\n", "print(f\"Loaded IFC data: {len(ifc_coords)} pile locations\")\n", "print(f\"IFC bounds: X({ifc_coords[:, 0].min():.1f}, {ifc_coords[:, 0].max():.1f}) Y({ifc_coords[:, 1].min():.1f}, {ifc_coords[:, 1].max():.1f}) Z({ifc_coords[:, 2].min():.1f}, {ifc_coords[:, 2].max():.1f})\")\n", "\n"]}, {"cell_type": "code", "execution_count": 57, "id": "4a70cc33", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["IFC locations within point cloud bounds: 14460/14460\n"]}], "source": ["# Quick overlap check\n", "overlap_check = (\n", "    (ifc_coords[:, 0] >= points[:, 0].min()) & \n", "    (ifc_coords[:, 0] <= points[:, 0].max()) &\n", "    (ifc_coords[:, 1] >= points[:, 1].min()) & \n", "    (ifc_coords[:, 1] <= points[:, 1].max())\n", ").sum()\n", "\n", "print(f\"IFC locations within point cloud bounds: {overlap_check}/{len(ifc_coords)}\")\n"]}, {"cell_type": "code", "execution_count": 58, "id": "a15b7c0e", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "Analyzing IFC locations against point cloud reality...\n", "\n", "IFC classification results:\n", "potential_pile: 8288 (57.3%)\n", "unclear: 3480 (24.1%)\n", "no_data: 1950 (13.5%)\n", "infrastructure: 742 (5.1%)\n"]}], "source": ["# Build spatial index for point cloud\n", "point_tree = cKDTree(points)\n", "search_radius = 5.0\n", "\n", "print(f\"\\nAnalyzing IFC locations against point cloud reality...\")\n", "\n", "# Analyze IFC locations against point cloud\n", "ifc_assessment = []\n", "for i, ifc_coord in enumerate(ifc_coords):\n", "    indices = point_tree.query_ball_point(ifc_coord, search_radius)\n", "    nearby_points = points[indices]\n", "    \n", "    if len(nearby_points) > 0:\n", "        z_mean = np.mean(nearby_points[:, 2])\n", "        z_std = np.std(nearby_points[:, 2])\n", "        \n", "        # Compare with expected pile height\n", "        expected_pile_height = ifc_coord[2]\n", "        height_diff = abs(z_mean - expected_pile_height)\n", "        \n", "        # Classify based on point characteristics\n", "        if len(nearby_points) > 50 and z_std < 0.5:\n", "            classification = \"infrastructure\"  # Flat, dense = panels/equipment\n", "        elif len(nearby_points) > 20 and z_std < 1.0:\n", "            classification = \"potential_pile\"  # Moderate density, low variation\n", "        elif len(nearby_points) > 5:\n", "            classification = \"unclear\"  # Some points, unclear what\n", "        else:\n", "            classification = \"no_data\"  # Very few points\n", "            \n", "        confidence = \"high\" if len(nearby_points) > 30 and z_std < 1.0 else \"medium\" if len(nearby_points) > 10 else \"low\"\n", "        \n", "        ifc_assessment.append({\n", "            'ifc_idx': i,\n", "            'ifc_id': ifc_df.iloc[i]['Name'],\n", "            'point_count': len(nearby_points),\n", "            'height_mean': z_mean,\n", "            'height_std': z_std,\n", "            'height_diff': height_diff,\n", "            'classification': classification,\n", "            'confidence': confidence\n", "        })\n", "    else:\n", "        ifc_assessment.append({\n", "            'ifc_idx': i,\n", "            'ifc_id': ifc_df.iloc[i]['Name'],\n", "            'point_count': 0,\n", "            'height_mean': np.nan,\n", "            'height_std': np.nan,\n", "            'height_diff': np.nan,\n", "            'classification': \"no_data\",\n", "            'confidence': \"none\"\n", "        })\n", "\n", "ifc_assessment_df = pd.DataFrame(ifc_assessment)\n", "\n", "# Classification summary\n", "classification_counts = ifc_assessment_df['classification'].value_counts()\n", "print(f\"\\nIFC classification results:\")\n", "for classification, count in classification_counts.items():\n", "    print(f\"{classification}: {count} ({count/len(ifc_assessment_df)*100:.1f}%)\")"]}, {"cell_type": "code", "execution_count": 59, "id": "135eb690", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "Usable IFC locations: 11089\n", "High confidence: 3381\n", "Medium confidence: 7708\n"]}], "source": ["# Filter usable IFC locations\n", "usable_ifc = ifc_assessment_df[\n", "    (ifc_assessment_df['classification'] == 'potential_pile') |\n", "    ((ifc_assessment_df['classification'] == 'unclear') & \n", "     (ifc_assessment_df['confidence'].isin(['medium', 'high'])))\n", "]\n", "\n", "print(f\"\\nUsable IFC locations: {len(usable_ifc)}\")\n", "print(f\"High confidence: {(usable_ifc['confidence'] == 'high').sum()}\")\n", "print(f\"Medium confidence: {(usable_ifc['confidence'] == 'medium').sum()}\")"]}, {"cell_type": "code", "execution_count": 60, "id": "a3e6b964", "metadata": {}, "outputs": [{"data": {"image/png": "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**************************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", "text/plain": ["<Figure size 1200x400 with 3 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["Saved IFC assessment to: /Users/<USER>/Documents/GitHub/asbuilt-foundation-analysis/data/processed/trino_enel/reference-data/ifc_assessment.csv\n", "\n", "IFC assessment complete.\n"]}], "source": ["# Visualization\n", "plt.figure(figsize=(12, 4))\n", "plt.subplot(1, 3, 1)\n", "classification_counts.plot(kind='bar')\n", "plt.title(\"IFC Classification\")\n", "plt.xticks(rotation=45)\n", "\n", "plt.subplot(1, 3, 2)\n", "valid_heights = ifc_assessment_df['height_diff'].dropna()\n", "plt.hist(valid_heights, bins=30, alpha=0.7)\n", "plt.xlabel(\"Height Difference (m)\")\n", "plt.ylabel(\"IFC Locations\")\n", "plt.title(\"Height Accuracy\")\n", "\n", "plt.subplot(1, 3, 3)\n", "plt.hist(ifc_assessment_df['point_count'], bins=50, alpha=0.7)\n", "plt.xlabel(\"Point Count\")\n", "plt.ylabel(\"IFC Locations\")\n", "plt.title(\"Point Density at IFC Locations\")\n", "plt.tight_layout()\n", "plt.show()\n", "\n", "# Save assessment\n", "ifc_assessment_path = get_processed_data_path(site_name, \"reference-data\") / \"ifc_assessment.csv\"\n", "ifc_assessment_df.to_csv(ifc_assessment_path, index=False)\n", "print(f\"Saved IFC assessment to: {ifc_assessment_path}\")\n", "\n", "print(\"\\nIFC assessment complete.\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 5}