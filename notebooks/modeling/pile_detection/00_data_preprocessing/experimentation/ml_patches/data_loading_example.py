
# Example data loading code:
import h5py
import numpy as np

# Load training data
with h5py.File('ml_patches/train_patches.h5', 'r') as f:
    train_points = f['points'][:]
    train_labels = f['labels'][:]

# Or load with NumPy
data = np.load('ml_patches/train_data.npz')
train_points = data['points']
train_labels = data['labels']

print(f"Training data shape: {train_points.shape}")
print(f"Labels shape: {train_labels.shape}")
