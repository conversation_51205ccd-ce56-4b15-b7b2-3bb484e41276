
# Example: Loading improved patch data for ML training

import pickle
import numpy as np

# Load training data
with open('ml_patches_improved/training_data_improved.pkl', 'rb') as f:
    train_data = pickle.load(f)

train_patches = train_data['patches']    # Shape: (N, 1024, 11)
train_labels = train_data['labels']      # Shape: (N,)
train_metadata = train_data['metadata'] # List of metadata dicts
dataset_info = train_data['dataset_info']

print(f"Training patches shape: {train_patches.shape}")
print(f"Features: {dataset_info['feature_names']}")

# Similarly for validation and test sets
with open('ml_patches_improved/validation_data_improved.pkl', 'rb') as f:
    val_data = pickle.load(f)

with open('ml_patches_improved/test_data_improved.pkl', 'rb') as f:
    test_data = pickle.load(f)
