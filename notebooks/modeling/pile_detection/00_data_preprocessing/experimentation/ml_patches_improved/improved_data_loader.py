
# Improved PyTorch Data Loader for Balanced Pile Detection
import pickle
import torch
from torch.utils.data import Dataset, DataLoader
import numpy as np

class ImprovedPileDataset(Dataset):
    def __init__(self, data_file, max_points=None, augment=False):
        with open(data_file, 'rb') as f:
            data = pickle.load(f)

        self.patches = data['patches']
        self.labels = data['labels']
        self.metadata = data['metadata']
        self.dataset_info = data['dataset_info']
        self.max_points = max_points or max(len(p) for p in self.patches)
        self.augment = augment

        print(f"Loaded dataset: {len(self.patches)} patches")
        print(f"  Positive: {sum(self.labels)} ({sum(self.labels)/len(self.labels)*100:.1f}%)")
        print(f"  Negative: {len(self.labels) - sum(self.labels)} ({(len(self.labels) - sum(self.labels))/len(self.labels)*100:.1f}%)")

    def __len__(self):
        return len(self.patches)

    def __getitem__(self, idx):
        patch = self.patches[idx]
        label = self.labels[idx]

        # Handle variable point counts
        n_points = len(patch)
        if n_points >= self.max_points:
            # Random sampling
            indices = torch.randperm(n_points)[:self.max_points]
            patch = patch[indices]
        else:
            # Pad with zeros
            padding = torch.zeros(self.max_points - n_points, patch.shape[1])
            patch = torch.cat([torch.from_numpy(patch), padding], dim=0)

        # Data augmentation (optional)
        if self.augment and torch.rand(1) > 0.5:
            # Random rotation around Z axis
            angle = torch.rand(1) * 2 * torch.pi
            cos_a, sin_a = torch.cos(angle), torch.sin(angle)
            rotation_matrix = torch.tensor([[cos_a, -sin_a, 0, 0],
                                           [sin_a, cos_a, 0, 0],
                                           [0, 0, 1, 0],
                                           [0, 0, 0, 1]], dtype=torch.float32)
            patch = patch @ rotation_matrix.T

        return patch.float(), torch.tensor(label, dtype=torch.long)

# Usage:
train_dataset = ImprovedPileDataset('ml_patches_improved/train_data_improved.pkl', max_points=64, augment=True)
val_dataset = ImprovedPileDataset('ml_patches_improved/val_data_improved.pkl', max_points=64, augment=False)

train_loader = DataLoader(train_dataset, batch_size=32, shuffle=True)
val_loader = DataLoader(val_dataset, batch_size=32, shuffle=False)

# Test the loader
for batch_points, batch_labels in train_loader:
    print(f"Batch shape: {batch_points.shape}")
    print(f"Labels shape: {batch_labels.shape}")
    print(f"Label distribution: {torch.bincount(batch_labels)}")
    break
