{"cells": [{"cell_type": "code", "execution_count": null, "id": "e9ac698a", "metadata": {}, "outputs": [], "source": ["# Notebook 8: Improved ML Patch Extraction for PointNet++ and DGCNN Training\n", "\n", "import numpy as np\n", "import pandas as pd\n", "import open3d as o3d\n", "from scipy.spatial import cKDTree\n", "import json\n", "from pathlib import Path\n", "import pickle\n", "import matplotlib.pyplot as plt\n", "from tqdm import tqdm\n", "from collections import Counter\n", "import warnings\n", "warnings.filterwarnings('ignore')\n", "\n", "print(\"Improved ML Patch Extraction for PointNet++ and DGCNN Training\")\n", "print(\"=\"*60)\n", "\n", "# Load configuration\n", "with open(\"patch_extraction_config.json\", \"r\") as f:\n", "    config = json.load(f)\n", "\n", "print(\"Configuration loaded:\")\n", "print(f\" Patch radius: {config['patch_radius']}m\")\n", "print(f\" Min points per patch: {config['min_points_per_patch']}\")\n", "\n", "# Load point cloud\n", "# ply_path = \"../../../../data/processed/trino_enel/denoising/trino_enel_denoised.ply\"\n", "ply_path = '../../../../data/processed/trino_enel/ml_local_alignment/trino_enel_ml_corrected.ply'\n", "\n", "point_cloud = o3d.io.read_point_cloud(ply_path)\n", "points = np.asarray(point_cloud.points)\n", "\n", "print(f\"\\nLoaded point cloud: {len(points):,} points\")\n", "print(\"Point cloud bounds:\")\n", "print(f\" X: {points[:, 0].min():.2f} to {points[:, 0].max():.2f}\")\n", "print(f\" Y: {points[:, 1].min():.2f} to {points[:, 1].max():.2f}\")\n", "print(f\" Z: {points[:, 2].min():.2f} to {points[:, 2].max():.2f}\")\n", "\n", "# Load harmonized dataset\n", "harmonized_df = pd.read_csv(\"harmonized_pile_dataset_final.csv\")\n", "print(f\"\\nLoaded harmonized dataset: {len(harmonized_df)} pile locations\")\n", "print(\"Pile coordinate bounds:\")\n", "print(f\" X: {harmonized_df['x'].min():.2f} to {harmonized_df['x'].max():.2f}\")\n", "print(f\" Y: {harmonized_df['y'].min():.2f} to {harmonized_df['y'].max():.2f}\")\n", "\n", "# Check coordinate system alignment\n", "x_overlap = not (points[:, 0].max() < harmonized_df['x'].min() or points[:, 0].min() > harmonized_df['x'].max())\n", "y_overlap = not (points[:, 1].max() < harmonized_df['y'].min() or points[:, 1].min() > harmonized_df['y'].max())\n", "\n", "print(f\"\\nCoordinate overlap check:\")\n", "print(f\" X overlap: {x_overlap}\")\n", "print(f\" Y overlap: {y_overlap}\")\n", "\n", "if not (x_overlap and y_overlap):\n", "    print(\"WARNING: Point cloud and pile coordinates don't overlap!\")\n", "    print(\"This may indicate a coordinate system mismatch.\")\n", "\n", "# IMPROVED PATCH EXTRACTION PARAMETERS\n", "PATCH_RADIUS = 2.0  # Reduced from 8m - more reasonable for pile detection\n", "MIN_POINTS = 50     # Increased threshold for meaningful patches\n", "MAX_PATCH_SIZE = 2048  # Maximum points per patch\n", "TARGET_PATCH_SIZE = 1024  # Target size for ML models\n", "\n", "# Create output directories\n", "output_dir = Path(\"ml_patches_improved\")\n", "output_dir.mkdir(exist_ok=True)\n", "print(f\"\\nOutput directory: {output_dir}\")\n", "\n", "# Build spatial index\n", "print(\"Building spatial index...\")\n", "point_tree_2d = cKDTree(points[:, :2])\n", "\n", "def validate_patch_quality(patch_points, pile_location):\n", "    \"\"\"Validate patch quality with stricter criteria\"\"\"\n", "    if len(patch_points) < MIN_POINTS:\n", "        return False, \"insufficient_points\"\n", "    \n", "    # Check spatial distribution\n", "    centroid = np.mean(patch_points[:, :2], axis=0)\n", "    pile_coord = np.array([pile_location['x'], pile_location['y']])\n", "    \n", "    # Pile should be near patch center\n", "    center_distance = np.linalg.norm(centroid - pile_coord)\n", "    if center_distance > PATCH_RADIUS * 0.3:  # <PERSON><PERSON> should be within 30% of radius from center\n", "        return False, \"pile_not_centered\"\n", "    \n", "    # Check Z variation (should have some structure)\n", "    z_range = patch_points[:, 2].max() - patch_points[:, 2].min()\n", "    z_std = np.std(patch_points[:, 2])\n", "    \n", "    if z_range < 0.1:  # Too flat\n", "        return False, \"too_flat\"\n", "    if z_range > 15.0:  # Unrealistic height variation\n", "        return False, \"excessive_height_variation\"\n", "    if z_std > 5.0:  # Too much noise\n", "        return False, \"too_noisy\"\n", "    \n", "    # Check point density distribution\n", "    distances_to_center = np.linalg.norm(patch_points[:, :2] - centroid, axis=1)\n", "    \n", "    # Should have points distributed across the patch\n", "    points_in_center = np.sum(distances_to_center < PATCH_RADIUS * 0.3)\n", "    points_in_middle = np.sum((distances_to_center >= PATCH_RADIUS * 0.3) & \n", "                             (distances_to_center < PATCH_RADIUS * 0.7))\n", "    \n", "    if points_in_center < MIN_POINTS * 0.2:  # Need some points near center\n", "        return False, \"sparse_center\"\n", "    \n", "    return True, \"valid\"\n", "\n", "def extract_enhanced_features(patch_points, pile_location):\n", "    \"\"\"Extract enhanced features for each point\"\"\"\n", "    if len(patch_points) == 0:\n", "        return np.array([])\n", "    \n", "    # Basic coordinates\n", "    xyz = patch_points[:, :3].copy()\n", "    \n", "    # Center the patch at pile location\n", "    pile_coord = np.array([pile_location['x'], pile_location['y'], pile_location['z']])\n", "    centered_xyz = xyz - pile_coord\n", "    \n", "    # Height features\n", "    ground_level = np.percentile(xyz[:, 2], 10)  # Robust ground estimation\n", "    height_above_ground = xyz[:, 2] - ground_level\n", "    relative_height = centered_xyz[:, 2]\n", "    \n", "    # Distance features\n", "    dist_from_pile = np.linalg.norm(centered_xyz[:, :2], axis=1)\n", "    dist_3d = np.linalg.norm(centered_xyz, axis=1)\n", "    \n", "    # Local neighborhood features\n", "    tree = cKDTree(xyz)\n", "    densities = []\n", "    local_heights = []\n", "    \n", "    for i, point in enumerate(xyz):\n", "        # Points within 1m radius\n", "        neighbors = tree.query_ball_point(point, 1.0)\n", "        densities.append(len(neighbors))\n", "        \n", "        # Local height variation\n", "        if len(neighbors) > 1:\n", "            neighbor_heights = xyz[neighbors, 2]\n", "            local_heights.append(np.std(neighbor_heights))\n", "        else:\n", "            local_heights.append(0.0)\n", "    \n", "    densities = np.array(densities)\n", "    local_heights = np.array(local_heights)\n", "    \n", "    # Cylindrical coordinates (useful for pile detection)\n", "    radius = np.linalg.norm(centered_xyz[:, :2], axis=1)\n", "    theta = np.arctan2(centered_xyz[:, 1], centered_xyz[:, 0])\n", "    \n", "    # Combine all features\n", "    features = np.column_stack([\n", "        centered_xyz,                    # [0:3] - centered x, y, z\n", "        height_above_ground,             # [3] - height above estimated ground\n", "        relative_height,                 # [4] - height relative to pile base\n", "        dist_from_pile,                  # [5] - 2D distance from pile center\n", "        dist_3d,                        # [6] - 3D distance from pile center\n", "        densities / np.max(densities),   # [7] - normalized local density\n", "        local_heights,                   # [8] - local height variation\n", "        radius,                         # [9] - cylindrical radius\n", "        theta,                          # [10] - cylindrical angle\n", "    ])\n", "    \n", "    return features.astype(np.float32)\n", "\n", "def resample_patch_intelligent(patch_points, target_size=TARGET_PATCH_SIZE):\n", "    \"\"\"Intelligent resampling that preserves important structure\"\"\"\n", "    n_points = len(patch_points)\n", "    \n", "    if n_points <= target_size:\n", "        # Upsample by duplicating points with small noise\n", "        indices = np.random.choice(n_points, target_size, replace=True)\n", "        resampled = patch_points[indices].copy()\n", "        \n", "        # Add small amount of noise to duplicated points\n", "        noise_mask = np.random.random(target_size) < (target_size - n_points) / target_size\n", "        noise = np.random.normal(0, 0.01, (np.sum(noise_mask), patch_points.shape[1]))\n", "        resampled[noise_mask, :3] += noise[:, :3]  # Only add noise to xyz coordinates\n", "        \n", "        return resampled\n", "    else:\n", "        # Downsample intelligently\n", "        # Ensure we keep points from different regions\n", "        \n", "        # Calculate distances from center\n", "        center = np.mean(patch_points[:, :3], axis=0)\n", "        distances = np.linalg.norm(patch_points[:, :3] - center, axis=1)\n", "        \n", "        # Stratified sampling by distance\n", "        n_rings = 4\n", "        indices = []\n", "        \n", "        for ring in range(n_rings):\n", "            ring_start = ring / n_rings\n", "            ring_end = (ring + 1) / n_rings\n", "            max_dist = np.max(distances)\n", "            \n", "            ring_mask = (distances >= ring_start * max_dist) & (distances < ring_end * max_dist)\n", "            ring_indices = np.where(ring_mask)[0]\n", "            \n", "            if len(ring_indices) > 0:\n", "                # Sample proportionally from each ring\n", "                n_from_ring = max(1, target_size // n_rings)\n", "                if len(ring_indices) >= n_from_ring:\n", "                    selected = np.random.choice(ring_indices, n_from_ring, replace=False)\n", "                else:\n", "                    selected = ring_indices\n", "                indices.extend(selected)\n", "        \n", "        # Fill remaining slots randomly\n", "        remaining = target_size - len(indices)\n", "        if remaining > 0:\n", "            available = list(set(range(n_points)) - set(indices))\n", "            if len(available) >= remaining:\n", "                additional = np.random.choice(available, remaining, replace=False)\n", "            else:\n", "                additional = np.random.choice(n_points, remaining, replace=True)\n", "            indices.extend(additional)\n", "        \n", "        # Ensure we have exactly target_size\n", "        indices = indices[:target_size]\n", "        return patch_points[indices]\n", "\n", "def extract_patch_improved(pile_location, pile_id, source, confidence):\n", "    \"\"\"Improved patch extraction with better validation and features\"\"\"\n", "    pile_coord = [pile_location['x'], pile_location['y']]\n", "    \n", "    # Find points within radius\n", "    indices = point_tree_2d.query_ball_point(pile_coord, PATCH_RADIUS)\n", "    \n", "    if len(indices) == 0:\n", "        return None\n", "    \n", "    patch_points = points[indices]\n", "    \n", "    # Validate patch quality\n", "    is_valid, reason = validate_patch_quality(patch_points, pile_location)\n", "    if not is_valid:\n", "        return None\n", "    \n", "    # Extract enhanced features\n", "    features = extract_enhanced_features(patch_points, pile_location)\n", "    \n", "    # Intelligent resampling\n", "    resampled_features = resample_patch_intelligent(features, TARGET_PATCH_SIZE)\n", "    \n", "    metadata = {\n", "        'pile_id': pile_id,\n", "        'source': source,\n", "        'confidence': confidence,\n", "        'pile_location': [pile_location['x'], pile_location['y'], pile_location['z']],\n", "        'original_point_count': len(patch_points),\n", "        'final_point_count': len(resampled_features),\n", "        'patch_radius': PATCH_RADIUS,\n", "        'feature_names': [\n", "            'x_centered', 'y_centered', 'z_centered',\n", "            'height_above_ground', 'relative_height',\n", "            'dist_from_pile_2d', 'dist_from_pile_3d',\n", "            'local_density', 'local_height_var',\n", "            'cylindrical_radius', 'cylindrical_angle'\n", "        ],\n", "        'validation_passed': True,\n", "        'validation_reason': reason\n", "    }\n", "    \n", "    return {\n", "        'points': resampled_features,\n", "        'metadata': metadata\n", "    }\n", "\n", "# Split dataset with better stratification\n", "print(\"\\nSplitting dataset...\")\n", "\n", "# Group by confidence and source for better stratification\n", "grouped_indices = {}\n", "for confidence in harmonized_df['confidence'].unique():\n", "    for source in harmonized_df['source'].unique():\n", "        mask = (harmonized_df['confidence'] == confidence) & (harmonized_df['source'] == source)\n", "        subset_indices = harmonized_df[mask].index.tolist()\n", "        if len(subset_indices) > 0:\n", "            grouped_indices[(confidence, source)] = subset_indices\n", "\n", "train_indices = []\n", "val_indices = []\n", "test_indices = []\n", "\n", "for (conf, src), indices in grouped_indices.items():\n", "    n_samples = len(indices)\n", "    if n_samples == 1:\n", "        train_indices.extend(indices)\n", "    elif n_samples == 2:\n", "        train_indices.extend(indices[:1])\n", "        val_indices.extend(indices[1:])\n", "    else:\n", "        # 70/20/10 split\n", "        n_train = max(1, int(0.7 * n_samples))\n", "        n_val = max(1, int(0.2 * n_samples))\n", "        \n", "        shuffled = np.random.RandomState(42).permutation(indices)\n", "        train_indices.extend(shuffled[:n_train])\n", "        val_indices.extend(shuffled[n_train:n_train + n_val])\n", "        test_indices.extend(shuffled[n_train + n_val:])\n", "\n", "print(f\"Dataset split:\")\n", "print(f\" Training: {len(train_indices)} samples\")\n", "print(f\" Validation: {len(val_indices)} samples\") \n", "print(f\" Test: {len(test_indices)} samples\")\n", "\n", "# Extract patches for each split\n", "def extract_split_improved(indices, split_name):\n", "    \"\"\"Extract patches with improved error handling and reporting\"\"\"\n", "    print(f\"\\nExtracting {split_name} patches...\")\n", "    \n", "    patches = []\n", "    labels = []\n", "    metadata_list = []\n", "    \n", "    failed_reasons = Counter()\n", "    \n", "    for idx in tqdm(indices, desc=f\"Processing {split_name}\"):\n", "        pile_data = harmonized_df.iloc[idx]\n", "        \n", "        try:\n", "            patch_data = extract_patch_improved(\n", "                pile_data,\n", "                pile_data['pile_id'],\n", "                pile_data['source'],\n", "                pile_data['confidence']\n", "            )\n", "            \n", "            if patch_data is not None:\n", "                patches.append(patch_data['points'])\n", "                labels.append(1)  # Positive class (pile present)\n", "                metadata_list.append(patch_data['metadata'])\n", "            else:\n", "                failed_reasons['validation_failed'] += 1\n", "                \n", "        except Exception as e:\n", "            failed_reasons[f'error_{type(e).__name__}'] += 1\n", "            print(f\"Error processing pile {pile_data['pile_id']}: {e}\")\n", "    \n", "    success_rate = len(patches) / (len(patches) + sum(failed_reasons.values())) * 100\n", "    \n", "    print(f\"{split_name} extraction complete:\")\n", "    print(f\" Successful: {len(patches)}\")\n", "    print(f\" Failed: {sum(failed_reasons.values())}\")\n", "    print(f\" Success rate: {success_rate:.1f}%\")\n", "    \n", "    if failed_reasons:\n", "        print(f\" Failure reasons: {dict(failed_reasons)}\")\n", "    \n", "    return patches, labels, metadata_list\n", "\n", "# Extract all splits\n", "train_patches, train_labels, train_metadata = extract_split_improved(train_indices, \"Training\")\n", "val_patches, val_labels, val_metadata = extract_split_improved(val_indices, \"Validation\")\n", "test_patches, test_labels, test_metadata = extract_split_improved(test_indices, \"Test\")\n", "\n", "# Save improved data\n", "def save_improved_data(patches, labels, metadata, split_name):\n", "    \"\"\"Save data with comprehensive metadata\"\"\"\n", "    \n", "    if len(patches) == 0:\n", "        print(f\"Warning: No patches extracted for {split_name}\")\n", "        return None\n", "    \n", "    # Convert to numpy arrays for consistent shape\n", "    patch_array = np.array(patches)  # Should be (N, TARGET_PATCH_SIZE, 11)\n", "    label_array = np.array(labels)\n", "    \n", "    data = {\n", "        'patches': patch_array,\n", "        'labels': label_array,\n", "        'metadata': metadata,\n", "        'dataset_info': {\n", "            'n_samples': len(patches),\n", "            'patch_size': TARGET_PATCH_SIZE,\n", "            'n_features': patch_array.shape[2],\n", "            'feature_names': metadata[0]['feature_names'] if metadata else [],\n", "            'patch_radius': PATCH_RADIUS,\n", "            'min_points_threshold': MIN_POINTS,\n", "            'extraction_config': config\n", "        }\n", "    }\n", "    \n", "    filename = output_dir / f\"{split_name.lower()}_data_improved.pkl\"\n", "    with open(filename, 'wb') as f:\n", "        pickle.dump(data, f)\n", "    \n", "    print(f\"Saved {split_name}: {len(patches)} patches to {filename}\")\n", "    print(f\" Shape: {patch_array.shape}\")\n", "    \n", "    return filename\n", "\n", "# Save all splits\n", "train_file = save_improved_data(train_patches, train_labels, train_metadata, \"Training\")\n", "val_file = save_improved_data(val_patches, val_labels, val_metadata, \"Validation\")\n", "test_file = save_improved_data(test_patches, test_labels, test_metadata, \"Test\")\n", "\n", "# Generate comprehensive summary\n", "total_extracted = len(train_patches) + len(val_patches) + len(test_patches)\n", "total_attempted = len(harmonized_df)\n", "\n", "print(f\"\\n\" + \"=\"*60)\n", "print(\"IMPROVED PATCH EXTRACTION SUMMARY\")\n", "print(\"=\"*60)\n", "print(f\"Total patches extracted: {total_extracted}/{total_attempted}\")\n", "print(f\"Overall success rate: {total_extracted/total_attempted*100:.1f}%\")\n", "print(f\"\\nDataset composition:\")\n", "print(f\" Training: {len(train_patches)} patches\")\n", "print(f\" Validation: {len(val_patches)} patches\")\n", "print(f\" Test: {len(test_patches)} patches\")\n", "\n", "if total_extracted > 0:\n", "    print(f\"\\nPatch specifications:\")\n", "    print(f\" Patch size: {TARGET_PATCH_SIZE} points\")\n", "    print(f\" Features per point: {train_patches[0].shape[1] if len(train_patches) > 0 else 'N/A'}\")\n", "    print(f\" Patch radius: {PATCH_RADIUS}m\")\n", "    print(f\" Data type: {train_patches[0].dtype if len(train_patches) > 0 else 'N/A'}\")\n", "    \n", "    # Quality analysis\n", "    if train_metadata:\n", "        print(f\"\\nTraining set composition:\")\n", "        train_sources = [m['source'] for m in train_metadata]\n", "        train_confidences = [m['confidence'] for m in train_metadata]\n", "        \n", "        source_counts = Counter(train_sources)\n", "        confidence_counts = Counter(train_confidences)\n", "        \n", "        print(f\" By source: {dict(source_counts)}\")\n", "        print(f\" By confidence: {dict(confidence_counts)}\")\n", "\n", "# Save final configuration\n", "final_config = {\n", "    'extraction_date': pd.Timestamp.now().isoformat(),\n", "    'extraction_version': 'improved_v2',\n", "    'total_patches_extracted': int(total_extracted),\n", "    'success_rate': float(total_extracted/total_attempted) if total_attempted > 0 else 0.0,\n", "    'splits': {\n", "        'train': int(len(train_patches)),\n", "        'val': int(len(val_patches)),\n", "        'test': int(len(test_patches))\n", "    },\n", "    'patch_specs': {\n", "        'target_size': int(TARGET_PATCH_SIZE),\n", "        'patch_radius': float(PATCH_RADIUS),\n", "        'min_points_threshold': int(MIN_POINTS),\n", "        'n_features': 11,\n", "        'feature_names': [\n", "            'x_centered', 'y_centered', 'z_centered',\n", "            'height_above_ground', 'relative_height', \n", "            'dist_from_pile_2d', 'dist_from_pile_3d',\n", "            'local_density', 'local_height_var',\n", "            'cylindrical_radius', 'cylindrical_angle'\n", "        ]\n", "    },\n", "    'improvements': [\n", "        'Better patch validation',\n", "        'Enhanced feature extraction',\n", "        'Intelligent resampling',\n", "        'Stratified dataset splitting',\n", "        'Comprehensive error reporting'\n", "    ]\n", "}\n", "\n", "with open(output_dir / \"extraction_config_improved.json\", 'w') as f:\n", "    json.dump(final_config, f, indent=2)\n", "\n", "print(f\"\\nExtraction complete! Ready for ML training.\")\n", "print(f\"Data saved to: {output_dir}\")\n", "\n", "# Create data loading example\n", "loader_example = f'''\n", "# Example: Loading improved patch data for ML training\n", "\n", "import pickle\n", "import numpy as np\n", "\n", "# Load training data\n", "with open('{output_dir}/training_data_improved.pkl', 'rb') as f:\n", "    train_data = pickle.load(f)\n", "\n", "train_patches = train_data['patches']    # Shape: (N, {TARGET_PATCH_SIZE}, 11)\n", "train_labels = train_data['labels']      # Shape: (N,)\n", "train_metadata = train_data['metadata'] # List of metadata dicts\n", "dataset_info = train_data['dataset_info']\n", "\n", "print(f\"Training patches shape: {{train_patches.shape}}\")\n", "print(f\"Features: {{dataset_info['feature_names']}}\")\n", "\n", "# Similarly for validation and test sets\n", "with open('{output_dir}/validation_data_improved.pkl', 'rb') as f:\n", "    val_data = pickle.load(f)\n", "\n", "with open('{output_dir}/test_data_improved.pkl', 'rb') as f:\n", "    test_data = pickle.load(f)\n", "'''\n", "\n", "with open(output_dir / \"data_loading_example.py\", 'w') as f:\n", "    f.write(loader_example)\n", "\n", "print(f\"\\nData loading example saved to: {output_dir}/data_loading_example.py\")"]}, {"cell_type": "code", "execution_count": null, "id": "b395aca4", "metadata": {}, "outputs": [], "source": ["# Debug the patch extraction to understand why validation is failing\n", "\n", "import numpy as np\n", "import pandas as pd\n", "import open3d as o3d\n", "from scipy.spatial import cKDTree\n", "import matplotlib.pyplot as plt\n", "\n", "# Load the data (reuse your existing loaded data)\n", "print(\"Debugging patch extraction failures...\")\n", "\n", "# Test with a few sample piles to understand the issues\n", "sample_piles = harmonized_df.head(10)  # Test first 10 piles\n", "\n", "PATCH_RADIUS = 2.0  # Use our improved radius\n", "MIN_POINTS = 50\n", "\n", "def debug_patch_extraction(pile_data, point_tree_2d, points):\n", "    \"\"\"Debug a single patch extraction to see what's failing\"\"\"\n", "    pile_coord = [pile_data['x'], pile_data['y']]\n", "    \n", "    # Find points within radius\n", "    indices = point_tree_2d.query_ball_point(pile_coord, PATCH_RADIUS)\n", "    patch_points = points[indices]\n", "    \n", "    print(f\"\\nPile ID: {pile_data['pile_id']}\")\n", "    print(f\"Pile location: ({pile_data['x']:.2f}, {pile_data['y']:.2f}, {pile_data['z']:.2f})\")\n", "    print(f\"Points found in {PATCH_RADIUS}m radius: {len(patch_points)}\")\n", "    \n", "    if len(patch_points) == 0:\n", "        print(\"❌ FAIL: No points found in radius\")\n", "        return False\n", "    \n", "    if len(patch_points) < MIN_POINTS:\n", "        print(f\"❌ FAIL: Not enough points ({len(patch_points)} < {MIN_POINTS})\")\n", "        return False\n", "    \n", "    # Check spatial distribution\n", "    centroid = np.mean(patch_points[:, :2], axis=0)\n", "    pile_coord_np = np.array([pile_data['x'], pile_data['y']])\n", "    center_distance = np.linalg.norm(centroid - pile_coord_np)\n", "    \n", "    print(f\"Patch centroid: ({centroid[0]:.2f}, {centroid[1]:.2f})\")\n", "    print(f\"Distance from pile to centroid: {center_distance:.2f}m\")\n", "    print(f\"Allowed distance (30% of radius): {PATCH_RADIUS * 0.3:.2f}m\")\n", "    \n", "    if center_distance > PATCH_RADIUS * 0.3:\n", "        print(f\"❌ FAIL: <PERSON><PERSON> not centered (distance {center_distance:.2f} > {PATCH_RADIUS * 0.3:.2f})\")\n", "        return False\n", "    \n", "    # Check Z variation\n", "    z_range = patch_points[:, 2].max() - patch_points[:, 2].min()\n", "    z_std = np.std(patch_points[:, 2])\n", "    z_mean = np.mean(patch_points[:, 2])\n", "    \n", "    print(f\"Z statistics:\")\n", "    print(f\"  Range: {z_range:.2f}m (min: {patch_points[:, 2].min():.2f}, max: {patch_points[:, 2].max():.2f})\")\n", "    print(f\"  Std: {z_std:.2f}m\")\n", "    print(f\"  Mean: {z_mean:.2f}m\")\n", "    \n", "    if z_range < 0.1:\n", "        print(f\"❌ FAIL: Too flat (range {z_range:.2f} < 0.1)\")\n", "        return False\n", "    if z_range > 15.0:\n", "        print(f\"❌ FAIL: Excessive height variation (range {z_range:.2f} > 15.0)\")\n", "        return False\n", "    if z_std > 5.0:\n", "        print(f\"❌ FAIL: Too noisy (std {z_std:.2f} > 5.0)\")\n", "        return False\n", "    \n", "    # Check point density distribution\n", "    distances_to_center = np.linalg.norm(patch_points[:, :2] - centroid, axis=1)\n", "    points_in_center = np.sum(distances_to_center < PATCH_RADIUS * 0.3)\n", "    \n", "    print(f\"Points in center (< {PATCH_RADIUS * 0.3:.2f}m): {points_in_center}\")\n", "    print(f\"Required center points (20% of {MIN_POINTS}): {MIN_POINTS * 0.2:.0f}\")\n", "    \n", "    if points_in_center < MIN_POINTS * 0.2:\n", "        print(f\"❌ FAIL: Sparse center ({points_in_center} < {MIN_POINTS * 0.2:.0f})\")\n", "        return False\n", "    \n", "    print(\"✅ PASS: All validation criteria met\")\n", "    return True\n", "\n", "# Test different radii to find what works\n", "print(\"Testing different patch radii...\")\n", "test_radii = [1.0, 1.5, 2.0, 3.0, 5.0, 8.0]\n", "test_pile = harmonized_df.iloc[0]\n", "\n", "for radius in test_radii:\n", "    indices = point_tree_2d.query_ball_point([test_pile['x'], test_pile['y']], radius)\n", "    print(f\"Radius {radius}m: {len(indices)} points\")\n", "\n", "print(\"\\n\" + \"=\"*50)\n", "print(\"DETAILED ANALYSIS OF FIRST 5 PILES\")\n", "print(\"=\"*50)\n", "\n", "for i in range(min(5, len(harmonized_df))):\n", "    pile = harmonized_df.iloc[i]\n", "    debug_patch_extraction(pile, point_tree_2d, points)\n", "\n", "# Let's also check what the original validation notebook found\n", "print(\"\\n\" + \"=\"*50)\n", "print(\"COMPARISON WITH ORIGINAL VALIDATION\")\n", "print(\"=\"*50)\n", "\n", "# Replicate the original validation logic\n", "patch_radius = 8.0  # Original radius\n", "min_points_per_patch = 30  # Original threshold\n", "\n", "validation_results = []\n", "for i in range(min(10, len(harmonized_df))):\n", "    pile = harmonized_df.iloc[i]\n", "    pile_coord = [pile['x'], pile['y']]\n", "    indices = point_tree_2d.query_ball_point(pile_coord, patch_radius)\n", "    patch_points = points[indices]\n", "    \n", "    if len(patch_points) >= min_points_per_patch:\n", "        z_range = patch_points[:, 2].max() - patch_points[:, 2].min()\n", "        z_std = np.std(patch_points[:, 2])\n", "        patch_area = np.pi * patch_radius**2\n", "        point_density = len(patch_points) / patch_area\n", "        \n", "        is_valid_patch = all([\n", "            len(patch_points) >= min_points_per_patch,\n", "            z_range < 10.0,\n", "            point_density > 0.3,\n", "            z_std < 3.0\n", "        ])\n", "        \n", "        print(f\"Pile {i}: {len(patch_points)} points, z_range={z_range:.2f}, \"\n", "              f\"density={point_density:.3f}, z_std={z_std:.2f}, valid={is_valid_patch}\")\n", "    else:\n", "        print(f\"Pile {i}: {len(patch_points)} points (insufficient)\")\n", "\n", "print(\"\\n\" + \"=\"*50)\n", "print(\"RECOMMENDED FIXES\")\n", "print(\"=\"*50)\n", "\n", "# Analyze what parameters would work\n", "point_counts = []\n", "z_ranges = []\n", "z_stds = []\n", "center_distances = []\n", "\n", "for i in range(min(50, len(harmonized_df))):\n", "    pile = harmonized_df.iloc[i]\n", "    pile_coord = [pile['x'], pile['y']]\n", "    \n", "    # Test with 2m radius\n", "    indices = point_tree_2d.query_ball_point(pile_coord, 2.0)\n", "    if len(indices) > 0:\n", "        patch_points = points[indices]\n", "        point_counts.append(len(patch_points))\n", "        \n", "        if len(patch_points) > 1:\n", "            z_ranges.append(patch_points[:, 2].max() - patch_points[:, 2].min())\n", "            z_stds.append(np.std(patch_points[:, 2]))\n", "            \n", "            centroid = np.mean(patch_points[:, :2], axis=0)\n", "            pile_coord_np = np.array([pile['x'], pile['y']])\n", "            center_distances.append(np.linalg.norm(centroid - pile_coord_np))\n", "\n", "if point_counts:\n", "    print(f\"Point count statistics (2m radius, n={len(point_counts)}):\")\n", "    print(f\"  Min: {min(point_counts)}, Max: {max(point_counts)}\")\n", "    print(f\"  Mean: {np.mean(point_counts):.1f}, Median: {np.median(point_counts):.1f}\")\n", "    print(f\"  25th percentile: {np.percentile(point_counts, 25):.1f}\")\n", "    \n", "    if z_ranges:\n", "        print(f\"\\nZ range statistics:\")\n", "        print(f\"  Min: {min(z_ranges):.3f}, Max: {max(z_ranges):.3f}\")\n", "        print(f\"  Mean: {np.mean(z_ranges):.3f}, Median: {np.median(z_ranges):.3f}\")\n", "        \n", "        print(f\"\\nZ std statistics:\")\n", "        print(f\"  Min: {min(z_stds):.3f}, Max: {max(z_stds):.3f}\")\n", "        print(f\"  Mean: {np.mean(z_stds):.3f}, Median: {np.median(z_stds):.3f}\")\n", "        \n", "        print(f\"\\nCenter distance statistics:\")\n", "        print(f\"  Min: {min(center_distances):.3f}, Max: {max(center_distances):.3f}\")\n", "        print(f\"  Mean: {np.mean(center_distances):.3f}, Median: {np.median(center_distances):.3f}\")\n", "\n", "# Suggest better parameters\n", "if point_counts:\n", "    min_points_recommendation = max(1, int(np.percentile(point_counts, 10)))\n", "    print(f\"\\n🔧 RECOMMENDED PARAMETERS:\")\n", "    print(f\"   MIN_POINTS = {min_points_recommendation}  (currently {MIN_POINTS})\")\n", "    print(f\"   PATCH_RADIUS = 2.0  (good choice)\")\n", "    \n", "    if z_ranges:\n", "        z_range_95th = np.percentile(z_ranges, 95)\n", "        z_std_95th = np.percentile(z_stds, 95)\n", "        center_dist_95th = np.percentile(center_distances, 95)\n", "        \n", "        print(f\"   Max Z range = {z_range_95th:.2f}  (currently 15.0)\")\n", "        print(f\"   Max Z std = {z_std_95th:.2f}  (currently 5.0)\")\n", "        print(f\"   Max center distance = {center_dist_95th:.2f}  (currently {2.0 * 0.3:.2f})\")"]}, {"cell_type": "code", "execution_count": null, "id": "122230f4", "metadata": {}, "outputs": [], "source": ["# Quick diagnostic - run this to see what's actually in your patches\n", "sample_pile = harmonized_df.iloc[0]\n", "pile_coord = [sample_pile['x'], sample_pile['y']]\n", "\n", "# Test different radii\n", "for radius in [1.0, 2.0, 3.0, 5.0, 8.0]:\n", "    indices = point_tree_2d.query_ball_point(pile_coord, radius)\n", "    if len(indices) > 0:\n", "        patch_points = points[indices]\n", "        z_range = patch_points[:, 2].max() - patch_points[:, 2].min()\n", "        print(f\"Radius {radius}m: {len(indices)} points, z_range={z_range:.3f}\")\n", "    else:\n", "        print(f\"Radius {radius}m: 0 points\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 5}