
# Loading sparse point cloud data

import pickle
import numpy as np

# Load training data
with open('ml_patches_sparse/training_data_sparse.pkl', 'rb') as f:
    data = pickle.load(f)

patches = data['patches']  # Shape: (N, 64, 6)
labels = data['labels']    # Shape: (N,)
info = data['dataset_info']

print(f"Patches shape: {patches.shape}")
print(f"Features: {info['feature_names']}")
print(f"Data type: {info['data_type']}")

# Note: This data is very sparse and heavily augmented
# Consider using simpler ML approaches
