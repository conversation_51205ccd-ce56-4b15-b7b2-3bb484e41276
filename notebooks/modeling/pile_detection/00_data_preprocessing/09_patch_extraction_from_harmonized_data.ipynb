{"cells": [{"cell_type": "markdown", "id": "5167ed94", "metadata": {}, "source": ["# PointNet-Ready Patch Extraction Pipeline\n"]}, {"cell_type": "code", "execution_count": 1, "id": "beb9e10b", "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "import pandas as pd\n", "import open3d as o3d\n", "from scipy.spatial import cKDTree\n", "from sklearn.model_selection import train_test_split\n", "from pathlib import Path\n", "import pickle\n", "import torch\n"]}, {"cell_type": "code", "execution_count": 2, "id": "e4bb8bd8", "metadata": {}, "outputs": [], "source": ["# PointNet-optimized parameters\n", "PATCH_RADIUS = 8.0  # Reduced from 8m for more focused features\n", "MIN_POINTS = 20     # Increased for better reliability\n", "TARGET_PATCH_SIZE = 1024  # Standard PointNet size (512/1024/2048)\n", "MAX_PATCH_SIZE = 2048\n"]}, {"cell_type": "code", "execution_count": 3, "id": "f084b5e6", "metadata": {}, "outputs": [], "source": ["def calculate_local_density(points, radius=1.0):\n", "    \"\"\"Calculate local point density around each point\"\"\"\n", "    if len(points) < 3:\n", "        return np.ones(len(points))\n", "        \n", "    tree = cKDTree(points[:, :2])\n", "    densities = []\n", "    \n", "    for point in points[:, :2]:\n", "        neighbors = tree.query_ball_point(point, radius)\n", "        density = len(neighbors) / (np.pi * radius**2)  # points per m²\n", "        densities.append(density)\n", "        \n", "    # Normalize densities\n", "    densities = np.array(densities)\n", "    max_density = densities.max()\n", "    return densities / max_density if max_density > 0 else densities\n"]}, {"cell_type": "code", "execution_count": 4, "id": "64c454b3", "metadata": {}, "outputs": [], "source": ["def extract_patch_features(patch_points, pile_location):\n", "    \"\"\"Extract features using Cell 33 approach\"\"\"\n", "    if len(patch_points) == 0:\n", "        return np.zeros((TARGET_PATCH_SIZE, 6))\n", "    \n", "    pile_coord = np.array([pile_location['x'], pile_location['y'], \n", "                          pile_location.get('z', patch_points[:, 2].mean())])\n", "    centered_xyz = patch_points[:, :3] - pile_coord\n", "    \n", "    if len(patch_points) > 1:\n", "        ground_level = np.min(patch_points[:, 2])\n", "        height_above_ground = patch_points[:, 2] - ground_level\n", "        dist_from_pile = np.linalg.norm(centered_xyz[:, :2], axis=1)\n", "        close_points = np.sum(dist_from_pile < 2.0)\n", "        density_indicator = np.full(len(patch_points), close_points / len(patch_points))\n", "    else:\n", "        height_above_ground = np.array([0.0])\n", "        dist_from_pile = np.array([0.0])\n", "        density_indicator = np.array([1.0])\n", "    \n", "    features = np.column_stack([\n", "        centered_xyz,\n", "        height_above_ground,\n", "        dist_from_pile,\n", "        density_indicator\n", "    ])\n", "    \n", "    return features.astype(np.float32)"]}, {"cell_type": "code", "execution_count": 5, "id": "99195102", "metadata": {}, "outputs": [], "source": ["def extract_label_agnostic_features(patch_points):\n", "    \"\"\"Extract features without assuming pile-centered coordinates.\"\"\"\n", "    \n", "    # Filter out padded points (zeros or very small values)\n", "    valid_mask = (np.abs(patch_points[:, 0]) + np.abs(patch_points[:, 1]) + np.abs(patch_points[:, 2])) > 1e-6\n", "    \n", "    if np.sum(valid_mask) == 0:\n", "        return [0] * 20  # Return default features for empty patches\n", "    \n", "    # Get real points only\n", "    real_points = patch_points[valid_mask]\n", "    \n", "    # Absolute coordinates\n", "    x_abs = real_points[:, 0]\n", "    y_abs = real_points[:, 1] \n", "    z_abs = real_points[:, 2]\n", "    \n", "    # Spatial distribution\n", "    patch_center_x = np.mean(x_abs)\n", "    patch_center_y = np.mean(y_abs)\n", "    distances_from_center = np.sqrt((x_abs - patch_center_x) ** 2 + (y_abs - patch_center_y) ** 2)\n", "\n", "    # Distribution ratios\n", "    radius_33 = np.percentile(distances_from_center, 33)\n", "    radius_66 = np.percentile(distances_from_center, 66)\n", "    \n", "    # Elevated points\n", "    z_mean = np.mean(z_abs)\n", "    z_std = np.std(z_abs)\n", "    elevated_ratio = np.sum(z_abs > z_mean + 0.5 * z_std) / len(real_points) if z_std > 0 else 0\n", "\n", "    # Clustering indicator\n", "    close_pairs = 0\n", "    for i in range(min(50, len(real_points))):  # Sample to reduce cost\n", "        point = real_points[i]\n", "        distances_to_point = np.sqrt((x_abs - point[0])**2 + (y_abs - point[1])**2)\n", "        close_pairs += np.sum(distances_to_point < 1.0) - 1  # Exclude self\n", "    clustering_density = close_pairs / len(real_points)\n", "\n", "    # Final feature vector\n", "    feature_vector = [\n", "        len(real_points),                            # Point count\n", "        len(real_points) / 1024,                     # Point density\n", "        np.mean(z_abs),                              # Z mean\n", "        z_std,                                       # Z std\n", "        np.min(z_abs),                               # Z min\n", "        np.max(z_abs),                               # Z max\n", "        np.max(z_abs) - np.min(z_abs),               # Z range\n", "        np.percentile(z_abs, 75) - np.percentile(z_abs, 25),  # Z IQR\n", "        np.std(x_abs),                               # X spread\n", "        np.std(y_abs),                               # Y spread\n", "        np.corrcoef(x_abs, y_abs)[0, 1] if len(real_points) > 1 and np.std(x_abs) > 0 and np.std(y_abs) > 0 else 0,\n", "        np.mean(distances_from_center),              # Center dist mean\n", "        np.std(distances_from_center),               # Center dist std\n", "        np.min(distances_from_center),               # Center dist min\n", "        np.max(distances_from_center),               # Center dist max\n", "        np.sum(distances_from_center <= radius_33) / len(real_points),   # Inner 33%\n", "        np.sum((distances_from_center > radius_33) & (distances_from_center <= radius_66)) / len(real_points),  # Middle 33%\n", "        np.sum(distances_from_center > radius_66) / len(real_points),    # Outer 33%\n", "        elevated_ratio,                              # Elevated points ratio\n", "        clustering_density                           # Clustering density\n", "    ]\n", "    \n", "    return np.array([feature_vector])\n"]}, {"cell_type": "code", "execution_count": 6, "id": "4e053ca4", "metadata": {}, "outputs": [], "source": ["def resample_patch(patch_features, target_size=TARGET_PATCH_SIZE):\n", "    \"\"\"Resample to target size using PointNet best practices\"\"\"\n", "    n_points = len(patch_features)\n", "    \n", "    if n_points == 0:\n", "        return np.zeros((target_size, 6))\n", "    \n", "    if n_points >= target_size:\n", "        # Random sampling for downsampling\n", "        indices = np.random.choice(n_points, target_size, replace=False)\n", "        return patch_features[indices]\n", "    else:\n", "        # Intelligent upsampling\n", "        # 1. Keep all original points\n", "        # 2. Add interpolated points between nearby points\n", "        # 3. Add noise-augmented duplicates\n", "        \n", "        upsampled = patch_features.copy()\n", "        needed = target_size - n_points\n", "        \n", "        if needed > 0:\n", "            # Method 1: Add noise-augmented duplicates (preserves distribution)\n", "            noise_factor = 0.02  # 2cm noise\n", "            for _ in range(needed):\n", "                idx = np.random.randint(0, n_points)\n", "                point = patch_features[idx].copy()\n", "                # Add small noise to XYZ coordinates only\n", "                point[:3] += np.random.normal(0, noise_factor, 3)\n", "                upsampled = np.vstack([upsampled, point])\n", "        \n", "        return upsampled[:target_size]  # Ensure exact size\n"]}, {"cell_type": "code", "execution_count": 7, "id": "b774e67d", "metadata": {}, "outputs": [], "source": ["def validate_patch(patch_points, pile_location, min_points=3):\n", "    \"\"\"Simple validation matching original Cell 33\"\"\"\n", "    if len(patch_points) < min_points:\n", "        return False, f\"insufficient_points_{len(patch_points)}\"\n", "    \n", "    z_range = patch_points[:, 2].max() - patch_points[:, 2].min()\n", "    if z_range > 20.0:  # Only upper limit like original\n", "        return False, f\"z_range_too_large_{z_range:.2f}\"\n", "    \n", "    return True, \"valid\""]}, {"cell_type": "code", "execution_count": 8, "id": "503197eb", "metadata": {}, "outputs": [], "source": ["def extract_positive_patches(points, pile_df, pile_indices, point_tree, \n", "                             patch_radius=PATCH_RADIUS, target_size=TARGET_PATCH_SIZE):\n", "    \"\"\"Extract positive patches for given pile indices\"\"\"\n", "    positive_patches = []\n", "    positive_labels = []\n", "    positive_metadata = []\n", "    \n", "    for idx in pile_indices:\n", "        pile = pile_df.iloc[idx]\n", "        pile_coord = [pile['x'], pile['y']]\n", "        \n", "        # Find points within radius\n", "        point_indices = point_tree.query_ball_point(pile_coord, patch_radius)\n", "        if not point_indices:\n", "            continue\n", "            \n", "        patch_points = points[point_indices]\n", "        \n", "        # Validate patch\n", "        is_valid, reason = validate_patch(patch_points, pile, patch_radius)\n", "        if not is_valid:\n", "            continue\n", "        \n", "        # Extract features (ensure it returns a 2D array: [N, C])\n", "        features = extract_label_agnostic_features(patch_points)\n", "        if features.ndim != 2:\n", "            print(f\"⚠️ Feature extraction failed at idx {idx} – shape: {features.shape}\")\n", "            continue\n", "        \n", "        try:\n", "            resampled = resample_patch(features, target_size)\n", "        except Exception as e:\n", "            print(f\"Resampling failed for patch {idx} — {e}\")\n", "            continue\n", "        \n", "        positive_patches.append(resampled)\n", "        positive_labels.append(1)\n", "        positive_metadata.append({\n", "            'pile_id': pile.get('pile_id', f'pile_{idx}'),\n", "            'source': pile.get('source', 'unknown'),\n", "            'confidence': pile.get('confidence', 'medium'),\n", "            'original_points': len(patch_points)\n", "        })\n", "    \n", "    return positive_patches, positive_labels, positive_metadata\n"]}, {"cell_type": "code", "execution_count": 9, "id": "bace248c", "metadata": {}, "outputs": [], "source": ["def extract_negative_patches(points, negative_coords, point_tree, n_negatives,\n", "                             patch_radius=PATCH_RADIUS, target_size=TARGET_PATCH_SIZE, min_points=MIN_POINTS):\n", "    \"\"\"Extract negative patches for given negative coordinates.\"\"\"\n", "    negative_patches = []\n", "    negative_labels = []\n", "    negative_metadata = []\n", "\n", "    for i, (neg_x, neg_y) in enumerate(negative_coords[:n_negatives]):\n", "        neg_coord = [neg_x, neg_y]\n", "        point_indices = point_tree.query_ball_point(neg_coord, patch_radius)\n", "\n", "        # Skip if no nearby points or too few points\n", "        if not point_indices or len(point_indices) < min_points:\n", "            continue\n", "\n", "        patch_points = points[point_indices]\n", "\n", "        # Create dummy pile info for feature extraction (used if function requires center/z)\n", "        fake_pile = {'x': neg_x, 'y': neg_y, 'z': patch_points[:, 2].mean()}\n", "\n", "        # Extract features (label-agnostic version)\n", "        features = extract_label_agnostic_features(patch_points)\n", "        resampled = resample_patch(features, target_size)\n", "\n", "        negative_patches.append(resampled)\n", "        negative_labels.append(0)\n", "        negative_metadata.append({\n", "            'pile_id': f'negative_{i}',\n", "            'source': 'negative_sampling',\n", "            'confidence': 'synthetic',\n", "            'original_points': len(patch_points)\n", "        })\n", "\n", "    return negative_patches, negative_labels, negative_metadata\n"]}, {"cell_type": "code", "execution_count": 10, "id": "0b75ff6e", "metadata": {}, "outputs": [], "source": ["def save_pointnet_dataset(patches, labels, metadata, split_name, output_dir, target_size=TARGET_PATCH_SIZE):\n", "    \"\"\"Save dataset in PointNet-ready format\"\"\"\n", "    output_dir = Path(output_dir)\n", "    output_dir.mkdir(exist_ok=True)\n", "    \n", "    # Convert to numpy arrays\n", "    patches_array = np.array(patches, dtype=np.float32)  # (N, 1024, 6)\n", "    labels_array = np.array(labels, dtype=np.int64)      # (N,)\n", "    \n", "    # Save in PointNet-ready format\n", "    dataset = {\n", "        'points': patches_array,          # (N, num_points, num_features)\n", "        'labels': labels_array,           # (N,)\n", "        'metadata': metadata,\n", "        'num_classes': 2,\n", "        'num_points': target_size,\n", "        'num_features': 6,\n", "        'feature_names': ['x_rel', 'y_rel', 'z_rel', 'height_norm', 'distance_norm', 'density']\n", "    }\n", "    \n", "    # Save as pickle (for Python) and optionally as NPZ (for other frameworks)\n", "    pickle_path = output_dir / f\"{split_name}_pointnet.pkl\"\n", "    with open(pickle_path, 'wb') as f:\n", "        pickle.dump(dataset, f)\n", "        \n", "    npz_path = output_dir / f\"{split_name}_pointnet.npz\"\n", "    np.savez_compressed(npz_path, \n", "                       points=patches_array, \n", "                       labels=labels_array)\n", "    \n", "    print(f\"  {split_name}: {np.sum(labels_array == 1)} positive + {np.sum(labels_array == 0)} negative\")\n", "    print(f\"  Saved to {pickle_path} and {npz_path}\")\n", "    print(f\"  Shape: {patches_array.shape}\")\n", "    \n", "    return pickle_path, npz_path\n", "\n"]}, {"cell_type": "code", "execution_count": 11, "id": "2609ac4c", "metadata": {}, "outputs": [], "source": ["def create_pointnet_dataset(points, pile_df, negative_coords, \n", "                           patch_radius=PATCH_RADIUS, target_size=TARGET_PATCH_SIZE,\n", "                           output_dir=\"pointnet_data\"):\n", "    \"\"\"Create train/val/test datasets for PointNet\"\"\"\n", "    \n", "    # Create spatial index\n", "    point_tree = cKDTree(points[:, :2])\n", "    \n", "    # Split data\n", "    train_idx, temp_idx = train_test_split(pile_df.index, test_size=0.4, random_state=42)\n", "    val_idx, test_idx = train_test_split(temp_idx, test_size=0.5, random_state=42)\n", "    \n", "    splits = {\"train\": train_idx, \"val\": val_idx, \"test\": test_idx}\n", "    \n", "    for split_name, pile_indices in splits.items():\n", "        print(f\"\\nProcessing {split_name} split...\")\n", "        \n", "        # Extract positive patches\n", "        pos_patches, pos_labels, pos_metadata = extract_positive_patches(\n", "            points, pile_df, pile_indices, point_tree, patch_radius, target_size\n", "        )\n", "        \n", "        # Extract negative patches (same number as positives)\n", "        n_negatives = len(pos_patches)\n", "        neg_patches, neg_labels, neg_metadata = extract_negative_patches(\n", "            points, negative_coords, point_tree, n_negatives, patch_radius, target_size\n", "        )\n", "        \n", "        # Combine positive and negative\n", "        all_patches = pos_patches + neg_patches\n", "        all_labels = pos_labels + neg_labels\n", "        all_metadata = pos_metadata + neg_metadata\n", "        \n", "        # Save dataset\n", "        save_pointnet_dataset(all_patches, all_labels, all_metadata, split_name, output_dir, target_size)\n", "\n"]}, {"cell_type": "code", "execution_count": 12, "id": "1dfb64bf", "metadata": {}, "outputs": [], "source": ["def sample_improved_negatives(pile_coords, point_cloud_bounds, n_samples, min_distance=10.0):\n", "    \"\"\"Sample negative coordinates with improved strategy\"\"\"\n", "    pile_tree = cKDTree(pile_coords)\n", "    xmin, ymin, xmax, ymax = point_cloud_bounds\n", "    \n", "    negatives = []\n", "    attempts = 0\n", "    max_attempts = n_samples * 5\n", "    \n", "    while len(negatives) < n_samples and attempts < max_attempts:\n", "        x = np.random.uniform(xmin, xmax)\n", "        y = np.random.uniform(ymin, ymax)\n", "        \n", "        # Check distance to nearest pile\n", "        dist, _ = pile_tree.query([x, y])\n", "        if dist > min_distance:\n", "            negatives.append([x, y])\n", "        \n", "        attempts += 1\n", "    \n", "    return np.array(negatives)\n"]}, {"cell_type": "code", "execution_count": 13, "id": "e5210b58", "metadata": {}, "outputs": [], "source": ["def analyze_dataset_statistics(output_dir=\"pointnet_data\"):\n", "    \"\"\"Analyze the created dataset statistics\"\"\"\n", "    output_dir = Path(output_dir)\n", "    \n", "    print(\"\\n📊 Dataset Statistics:\")\n", "    print(\"=\" * 50)\n", "    \n", "    for split in [\"train\", \"val\", \"test\"]:\n", "        try:\n", "            with open(output_dir / f\"{split}_pointnet.pkl\", 'rb') as f:\n", "                data = pickle.load(f)\n", "            \n", "            points = data['points']\n", "            labels = data['labels']\n", "            \n", "            n_positive = np.sum(labels == 1)\n", "            n_negative = np.sum(labels == 0)\n", "            \n", "            print(f\"{split.upper()}:\")\n", "            print(f\"  Total samples: {len(labels)}\")\n", "            print(f\"  Positive (piles): {n_positive}\")\n", "            print(f\"  Negative (no piles): {n_negative}\")\n", "            print(f\"  Balance ratio: {n_positive/n_negative:.2f}\")\n", "            print(f\"  Points shape: {points.shape}\")\n", "            print(f\"  Features: {data['feature_names']}\")\n", "            print()\n", "            \n", "        except FileNotFoundError:\n", "            print(f\"  {split.upper()}: File not found\")\n", "\n"]}, {"cell_type": "code", "execution_count": 14, "id": "404dd9cf", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Loaded: 983,884 points, 1435 piles\n", "\n", "Sampling negative coordinates...\n", "Sampled 1435 negative locations\n", "\n", "Creating PointNet dataset...\n", "\n", "Processing train split...\n", "  train: 861 positive + 426 negative\n", "  Saved to pointnet_data/train_pointnet.pkl and pointnet_data/train_pointnet.npz\n", "  Shape: (1287, 1024, 20)\n", "\n", "Processing val split...\n", "  val: 287 positive + 131 negative\n", "  Saved to pointnet_data/val_pointnet.pkl and pointnet_data/val_pointnet.npz\n", "  Shape: (418, 1024, 20)\n", "\n", "Processing test split...\n", "  test: 287 positive + 131 negative\n", "  Saved to pointnet_data/test_pointnet.pkl and pointnet_data/test_pointnet.npz\n", "  Shape: (418, 1024, 20)\n", "\n", "📊 Dataset Statistics:\n", "==================================================\n", "TRAIN:\n", "  Total samples: 1287\n", "  Positive (piles): 861\n", "  Negative (no piles): 426\n", "  Balance ratio: 2.02\n", "  Points shape: (1287, 1024, 20)\n", "  Features: ['x_rel', 'y_rel', 'z_rel', 'height_norm', 'distance_norm', 'density']\n", "\n", "VAL:\n", "  Total samples: 418\n", "  Positive (piles): 287\n", "  Negative (no piles): 131\n", "  Balance ratio: 2.19\n", "  Points shape: (418, 1024, 20)\n", "  Features: ['x_rel', 'y_rel', 'z_rel', 'height_norm', 'distance_norm', 'density']\n", "\n", "TEST:\n", "  Total samples: 418\n", "  Positive (piles): 287\n", "  Negative (no piles): 131\n", "  Balance ratio: 2.19\n", "  Points shape: (418, 1024, 20)\n", "  Features: ['x_rel', 'y_rel', 'z_rel', 'height_norm', 'distance_norm', 'density']\n", "\n", "\n", "PointNet dataset creation complete!\n", "Files created in ./pointnet_data/:\n", "   - train_pointnet.pkl / train_pointnet.npz\n", "   - val_pointnet.pkl / val_pointnet.npz\n", "   - test_pointnet.pkl / test_pointnet.npz\n", "\n", "Ready for PointNet++ training!\n"]}], "source": ["# Example usage and main execution\n", "if __name__ == \"__main__\":\n", "    # Load your data (replace with actual file paths)\n", "    try:\n", "        #point_cloud = o3d.io.read_point_cloud(\"../../../../data/processed/trino_enel/denoising/trino_enel_denoised.ply\")\n", "        point_cloud = o3d.io.read_point_cloud(\"../../../../data/processed/trino_enel/denoising/trino_enel_denoised_for_registration.ply\")\n", "        points = np.asarray(point_cloud.points)\n", "        pile_df = pd.read_csv(\"harmonized_pile_dataset_final.csv\")\n", "        print(f\"Loaded: {len(points):,} points, {len(pile_df)} piles\")\n", "    except FileNotFoundError:\n", "        print(\"Data files not found. Please update paths.\")\n", "        exit(1)\n", "    \n", "    # Configuration\n", "    patch_radius = 5.0        # Smaller, more focused patches\n", "    target_size = 1024        # Standard PointNet size\n", "    # min_distance = 10.0       # Minimum distance for negative sampling\n", "    min_distance = PATCH_RADIUS * 2  # 16m like Cell 33\n", "\n", "    # Sample negative coordinates\n", "    pile_coords = pile_df[['x', 'y']].values\n", "    bounds = (points[:, 0].min(), points[:, 1].min(), \n", "              points[:, 0].max(), points[:, 1].max())\n", "    \n", "    print(f\"\\nSampling negative coordinates...\")\n", "    negative_coords = sample_improved_negatives(pile_coords, bounds, len(pile_df), min_distance)\n", "    print(f\"Sampled {len(negative_coords)} negative locations\")\n", "    \n", "    # Create PointNet-ready dataset\n", "    print(f\"\\nCreating PointNet dataset...\")\n", "    create_pointnet_dataset(\n", "        points=points, \n", "        pile_df=pile_df, \n", "        negative_coords=negative_coords,\n", "        patch_radius=patch_radius,\n", "        target_size=target_size,\n", "        output_dir=\"pointnet_data\"\n", "    )\n", "    \n", "    # Analyze results\n", "    analyze_dataset_statistics(\"pointnet_data\")\n", "    \n", "    print(\"\\nPointNet dataset creation complete!\")\n", "    print(\"Files created in ./pointnet_data/:\")\n", "    print(\"   - train_pointnet.pkl / train_pointnet.npz\")\n", "    print(\"   - val_pointnet.pkl / val_pointnet.npz\") \n", "    print(\"   - test_pointnet.pkl / test_pointnet.npz\")\n", "    print(\"\\nReady for PointNet++ training!\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"name": "python", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 5}