{"cells": [{"cell_type": "markdown", "id": "560fff55", "metadata": {}, "source": ["# Construction State Analysis\n", "\n", "Analyze point cloud data to characterize construction zones, height distributions, and spatial density patterns for project assessment.\n", "\n", "**Objectives:**\n", "- Analyze height distribution and identify ground vs structure levels\n", "- Compute point density patterns across the construction site\n", "- Classify construction zones based on elevation profiles\n", "- Generate spatial visualization of construction progress\n", "\n", "**Author**: <PERSON><PERSON><PERSON>  \n", "**Date**: August 2025  \n", "**Project**: As-Built Foundation Analysis"]}, {"cell_type": "code", "execution_count": 15, "id": "9900cb86", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Notebooks root: /Users/<USER>/Documents/GitHub/asbuilt-foundation-analysis/notebooks\n"]}], "source": ["# Imports\n", "import sys\n", "from pathlib import Path\n", "\n", "# Add the `notebooks` folder to sys.path\n", "notebooks_root = Path(__file__).resolve().parents[2] if '__file__' in globals() else Path.cwd().parents[2]\n", "print(f\"Notebooks root: {notebooks_root}\")\n", "\n", "if str(notebooks_root) not in sys.path:\n", "    sys.path.insert(0, str(notebooks_root))\n", "\n", "# Now import from shared package\n", "from shared.config import (\n", "    get_data_path,\n", "    get_processed_data_path\n", ")\n", "\n", "import numpy as np\n", "import pandas as pd\n", "import open3d as o3d\n", "import matplotlib.pyplot as plt\n", "from scipy import stats\n", "from pathlib import Path\n"]}, {"cell_type": "code", "execution_count": 16, "id": "53ace690", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Point cloud path: /Users/<USER>/Documents/GitHub/asbuilt-foundation-analysis/data/processed/trino_enel/ml_local_alignment\n", "Loaded point cloud: 517,002 points\n", "Coordinate ranges:\n", "X: 435220.82 to 436795.41\n", "Y: 5010813.78 to 5012552.58\n", "Z: 154.91 to 179.54\n"]}], "source": ["# Load point cloud\n", "site_name = \"trino_enel\"\n", "ply_path = get_processed_data_path(site_name , \"ml_local_alignment\")\n", "print(f\"Point cloud path: {ply_path}\")\n", "point_cloud = o3d.io.read_point_cloud(f'{ply_path}/trino_enel_ml_corrected.ply')\n", "points = np.asarray(point_cloud.points)\n", "\n", "print(f\"Loaded point cloud: {len(points):,} points\")\n", "print(f\"Coordinate ranges:\")\n", "print(f\"X: {points[:, 0].min():.2f} to {points[:, 0].max():.2f}\")\n", "print(f\"Y: {points[:, 1].min():.2f} to {points[:, 1].max():.2f}\")\n", "print(f\"Z: {points[:, 2].min():.2f} to {points[:, 2].max():.2f}\")\n"]}, {"cell_type": "code", "execution_count": 17, "id": "b1404312", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Height analysis:\n", "Ground level (10th percentile): 156.01m\n", "25th percentile: 156.70m\n", "Median: 157.53m\n", "75th percentile: 158.27m\n", "90th percentile: 158.51m\n"]}], "source": ["# Analyze height distribution\n", "z_values = points[:, 2]\n", "z_percentiles = np.percentile(z_values, [10, 25, 50, 75, 90])\n", "\n", "print(\"Height analysis:\")\n", "print(f\"Ground level (10th percentile): {z_percentiles[0]:.2f}m\")\n", "print(f\"25th percentile: {z_percentiles[1]:.2f}m\")\n", "print(f\"Median: {z_percentiles[2]:.2f}m\")\n", "print(f\"75th percentile: {z_percentiles[3]:.2f}m\")\n", "print(f\"90th percentile: {z_percentiles[4]:.2f}m\")\n"]}, {"cell_type": "code", "execution_count": 18, "id": "006f1045", "metadata": {}, "outputs": [{"data": {"text/plain": ["<matplotlib.legend.Legend at 0x2b04b6cd0>"]}, "execution_count": 18, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAAg0AAAGJCAYAAAAJ0QDHAAAAOnRFWHRTb2Z0d2FyZQBNYXRwbG90bGliIHZlcnNpb24zLjEwLjMsIGh0dHBzOi8vbWF0cGxvdGxpYi5vcmcvZiW1igAAAAlwSFlzAAAPYQAAD2EBqD+naQAAS95JREFUeJzt3Qd4FFUXBuCTTu9dkSq9dxRBEEEEBUVFQSRUQToKiFKkiYJ0BBSRIqjAr6CCIEiXXkSRrgZBWlApEhLS5n++E2bdTTZhElIm2e99nnFnZ2dnb4Z158wt53oZhmEIERER0R1432kHIiIiImDQQERERJYwaCAiIiJLGDQQERGRJQwaiIiIyBIGDURERGQJgwYiIiKyhEEDERERWcKggYiIiCxh0ECUwQUGBkrx4sWT/N5s2bKJHSxcuFC8vLzk9OnTqX7O8Jn47Pfee09Sw1tvvaWfR2Q3DBqIbHRB3L9/v9vXH374YalUqZLY1c2bN/VCt2XLFkv7Yz/8veYSEBAgBQsW1L/z7bfflsuXL6dJuVKTnctGFB8GDUQZ3Lx58+TEiRMpfgEcPXp0oi+A/fr1k08++UQ+/PBDGTx4sOTJk0dGjRol5cuXl02bNrns27FjRwkNDZVixYqleLnS+pwNHz5c/1Yiu/FN6wIQUcry8/MTu3rooYfkmWeecdn2008/SbNmzaRt27Zy9OhRKVy4sG738fHRJSWFhIRI1qxZ0/yc+fr66kJkN6xpIErHlixZIjVr1pTMmTPrXfrzzz8vZ8+evWOfhr///lvv3HPkyCG5cuWSTp066cUaTQVoKont3Llz0qZNG+3fkD9/fnnttdckKirK0d6PbYA7Z7PJAVXvSVG1alWZNm2aXL16VWbNmpVgnwY05zRv3lzy5cun56BEiRLSpUsXS+Uy+2v89ttv8vjjj0v27NmlQ4cO8Z4z09SpU7W2A5/XqFEj+eWXX1xeRxMLlticj3mnsrnr0xAZGSljx46VUqVKaXMOjvXGG2/IrVu3XPbD9latWskPP/wgderUkUyZMknJkiVl8eLFifhXIHKPQQORjVy7dk3++uuvOEtEREScfcePHy8vvfSS3H///TJlyhQZMGCAbNy4URo2bKgX3PhER0fLE088IZ999pkGCzjOhQsXdN0dBAe4MOfNm1c7AuJCOXnyZG1SAFz85syZo+tPPfWUNjdgefrpp5N8HlD7gIvy+vXr490nODhYayRwAX799ddl5syZetHfvXu35XLhQoy/rUCBAvq3oXYjIbjwzpgxQ3r37i3Dhg3TgKFJkyZy6dKlRP19STln3bp1k5EjR0qNGjU0cMG/w4QJEzRQjO3XX3/Vc/joo4/qv1Xu3Lk1aDly5EiiykkUh0FEaW7BggUG/ndMaKlYsaJj/9OnTxs+Pj7G+PHjXY5z+PBhw9fX12V7p06djGLFijmef/HFF3q8adOmObZFRUUZTZo00e0oi/N7sW3MmDEun1O9enWjZs2ajueXL1/W/UaNGmXp7928ebPuv2LFinj3qVq1qpE7d+445ygoKEifr1y5Up/v27cv3mMkVC7zb3v99dfdvuZ8zvCZ2Ddz5szGn3/+6di+Z88e3T5w4EDHtkaNGulyp2MmVDZsc/55PnTokD7v1q2by36vvfaabt+0aZNjGz4D27Zt2+bYFhwcbAQEBBivvvpqPGeKyBrWNBDZyPvvvy8bNmyIs1SpUsVlvy+//FJrDJ577jmXGolChQppzcPmzZvj/Yx169Zpm3337t0d27y9vfXuOT49e/aM0xfh999/l5SEpoN///033tfRrAKrV692WxNjVa9evSzviyaae+65x/Ec1f9169aVb7/9VlKSefxBgwa5bH/********************************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", "text/plain": ["<Figure size 1200x400 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["\n", "\n", "# Height distribution plot\n", "plt.figure(figsize=(12, 4))\n", "plt.subplot(1, 2, 1)\n", "plt.hist(z_values, bins=50, alpha=0.7)\n", "plt.axvline(z_percentiles[0], color='red', linestyle='--', label='Ground (10%)')\n", "plt.axvline(z_percentiles[4], color='orange', linestyle='--', label='Structure (90%)')\n", "plt.xlabel(\"Height (m)\")\n", "plt.ylabel(\"Point Count\")\n", "plt.title(\"Height Distribution\")\n", "plt.legend()\n", "\n"]}, {"cell_type": "code", "execution_count": 19, "id": "0c63dce3", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "Point density analysis (25m radius):\n", "Mean density: 193.1 points\n", "Density range: 0 to 860 points\n"]}], "source": ["# Point density analysis\n", "from scipy.spatial import cKDTree\n", "tree = cKDTree(points[:, :2])\n", "\n", "# Sample grid for density analysis\n", "x_min, x_max = points[:, 0].min(), points[:, 0].max()\n", "y_min, y_max = points[:, 1].min(), points[:, 1].max()\n", "grid_spacing = 50  # 50m grid\n", "\n", "x_grid = np.arange(x_min, x_max, grid_spacing)\n", "y_grid = np.arange(y_min, y_max, grid_spacing)\n", "densities = []\n", "\n", "for x in x_grid[::10]:  # Sample every 10th point for speed\n", "    for y in y_grid[::10]:\n", "        indices = tree.query_ball_point([x, y], 25)  # 25m radius\n", "        densities.append(len(indices))\n", "\n", "print(f\"\\nPoint density analysis (25m radius):\")\n", "print(f\"Mean density: {np.mean(densities):.1f} points\")\n", "print(f\"Density range: {np.min(densities)} to {np.max(densities)} points\")\n", "\n"]}, {"cell_type": "code", "execution_count": 20, "id": "da197b24", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "Construction state analysis:\n", "Ground-level points: 325,111 (62.9%)\n", "Structure-level points: 2,760 (0.5%)\n"]}], "source": ["# Identify construction zones based on height\n", "ground_mask = z_values < (z_percentiles[0] + 2)  # Within 2m of ground\n", "structure_mask = z_values > (z_percentiles[2] + 3)  # 3m above median\n", "\n", "ground_points = points[ground_mask]\n", "structure_points = points[structure_mask]\n", "\n", "print(f\"\\nConstruction state analysis:\")\n", "print(f\"Ground-level points: {len(ground_points):,} ({len(ground_points)/len(points)*100:.1f}%)\")\n", "print(f\"Structure-level points: {len(structure_points):,} ({len(structure_points)/len(points)*100:.1f}%)\")\n", "\n"]}, {"cell_type": "code", "execution_count": 21, "id": "0341a137", "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "Construction state analysis complete.\n"]}], "source": ["plt.subplot(1, 2, 2)\n", "plt.scatter(ground_points[::100, 0], ground_points[::100, 1], \n", "           s=0.1, alpha=0.5, label='Ground Level')\n", "plt.scatter(structure_points[::100, 0], structure_points[::100, 1], \n", "           s=0.1, alpha=0.5, label='Structure Level')\n", "plt.xlabel(\"X (m)\")\n", "plt.ylabel(\"Y (m)\")\n", "plt.title(\"Construction Zones\")\n", "plt.legend()\n", "plt.tight_layout()\n", "plt.show()\n", "\n", "print(\"\\nConstruction state analysis complete.\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 5}