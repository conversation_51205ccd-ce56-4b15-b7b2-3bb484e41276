import sys
from pathlib import Path

# Add the `notebooks` folder to sys.path
notebooks_root = Path(__file__).resolve().parents[2] if '__file__' in globals() else Path.cwd().parents[2]
print(f"Notebooks root: {notebooks_root}")

if str(notebooks_root) not in sys.path:
    sys.path.insert(0, str(notebooks_root))

# Now import from shared package
from shared.config import (
    get_data_path,
    get_processed_data_path
)

import geopandas as gpd
import pandas as pd


# Papermill parameters - these will be injected by Papermill
from datetime import datetime

site_name = "trino_enel"  # Site name for output file naming

timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

# Standardized paths
output_path = get_processed_data_path(site_name, f"reference-data")
output_path.mkdir(parents=True, exist_ok=True)
CRS = "EPSG:32632"

# Load KML file
kml_path = get_data_path("trino_enel") / "kml" / "pile.kml"
gdf = gpd.read_file(kml_path, driver='KML')

# Reproject to UTM Zone 32N *before* computing centroids
gdf = gdf.to_crs(CRS)

# Convert polygons to points (centroids) in projected CRS
gdf['geometry'] = gdf.geometry.centroid

# Extract coordinates
gdf['X'] = gdf.geometry.x
gdf['Y'] = gdf.geometry.y
gdf['Z'] = 0  # KML doesn't have Z, will be extracted later

# Select relevant columns and export to CSV
output_df = gdf[['Name', 'Description', 'X', 'Y', 'Z']].copy()
output_df.to_csv(f'{output_path}/kml_piles_utm.csv', index=False)

print(f"Output path: {output_path}")
print(f"Exported {len(output_df)} pile locations to kml_piles_utm.csv")
print("Coordinate ranges:")
print(f"X: {output_df['X'].min():.2f} to {output_df['X'].max():.2f}")
print(f"Y: {output_df['Y'].min():.2f} to {output_df['Y'].max():.2f}")
