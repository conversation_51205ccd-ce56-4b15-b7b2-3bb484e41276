{"cells": [{"cell_type": "markdown", "id": "b6a494a6", "metadata": {}, "source": ["# Harmonized Dataset Creation\n", "\n", "Create a unified, high-confidence dataset of pile locations by integrating IFC, KML, and point cloud data sources.\n", "\n", "**Objectives:**\n", "- Merge pile data from IFC (design), KML (field), and point cloud (as-built) sources\n", "- Apply tiered validation and quality filtering\n", "- Generate ML-ready harmonized dataset with confidence scores\n", "- Export validated dataset and configuration summary\n", "\n", "**Author**: <PERSON><PERSON><PERSON>  \n", "**Date**: July 2025  \n", "**Project**: As-Built Foundation Analysis"]}, {"cell_type": "code", "execution_count": 28, "id": "24e31d67", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Notebooks root: /Users/<USER>/Documents/GitHub/asbuilt-foundation-analysis/notebooks\n"]}], "source": ["# Papermill parameters - these will be injected by Papermill\n", "from datetime import datetime\n", "import sys\n", "from pathlib import Path\n", "\n", "# Add the `notebooks` folder to sys.path\n", "notebooks_root = Path(__file__).resolve().parents[2] if '__file__' in globals() else Path.cwd().parents[2]\n", "print(f\"Notebooks root: {notebooks_root}\")\n", "\n", "if str(notebooks_root) not in sys.path:\n", "    sys.path.insert(0, str(notebooks_root))\n", "\n", "# Now import from shared package\n", "from shared.config import (\n", "    get_data_path,\n", "    get_processed_data_path\n", ")\n", "site_name = \"trino_enel\"  # Site name for output file naming\n", "\n", "timestamp = datetime.now().strftime(\"%Y%m%d_%H%M%S\")"]}, {"cell_type": "code", "execution_count": 29, "id": "3c9422c1", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Creating harmonized dataset for ML training...\n"]}], "source": ["# Notebook 6: Harmonized Dataset Creation\n", "\n", "# Basic imports\n", "import numpy as np\n", "import pandas as pd\n", "import open3d as o3d\n", "import geopandas as gpd\n", "import matplotlib.pyplot as plt\n", "from scipy.spatial import cKDTree\n", "from pathlib import Path\n", "import json\n", "\n", "print(\"Creating harmonized dataset for ML training...\")\n"]}, {"cell_type": "code", "execution_count": 30, "id": "e1a704e0", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Loaded point cloud: 517,002 points\n", "Loaded IFC data: 14460 pile locations\n"]}], "source": ["# Load point cloud\n", "ply_path = get_processed_data_path(site_name, \"ml_local_alignment\") / \"trino_enel_ml_corrected.ply\"\n", "point_cloud = o3d.io.read_point_cloud(ply_path)\n", "points = np.asarray(point_cloud.points)\n", "\n", "print(f\"Loaded point cloud: {len(points):,} points\")\n", "\n", "# Load IFC metadata\n", "ifc_path = get_processed_data_path(site_name, \"ifc_metadata\") / \"GRE.EEC.S.00.IT.P.14353.00.265_enhanced_metadata.csv\"\n", "ifc_df = pd.read_csv(ifc_path)\n", "ifc_coords = ifc_df[['X', 'Y', 'Z']].values\n", "\n", "print(f\"Loaded IFC data: {len(ifc_coords)} pile locations\")\n"]}, {"cell_type": "code", "execution_count": 31, "id": "049035ea", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Loaded KML data: 1288 pile locations\n"]}], "source": ["# Load and transform KML data\n", "kml_path = get_data_path(site_name) / \"kml\" / \"pile.kml\"\n", "gdf_kml = gpd.read_file(kml_path, driver='KML')\n", "gdf_kml = gdf_kml[gdf_kml.geometry.notnull()].copy()\n", "gdf_kml = gdf_kml.set_crs(epsg=4326)\n", "gdf_kml_utm = gdf_kml.to_crs(epsg=32632)\n", "gdf_kml_utm['geometry'] = gdf_kml_utm.geometry.apply(\n", "    lambda geom: geom.centroid if geom.geom_type != 'Point' else geom\n", ")\n", "\n", "kml_coords = np.stack([\n", "    gdf_kml_utm.geometry.x.values,\n", "    gdf_kml_utm.geometry.y.values,\n", "    np.zeros(len(gdf_kml_utm))\n", "], axis=1)\n", "\n", "print(f\"Loaded KML data: {len(kml_coords)} pile locations\")\n"]}, {"cell_type": "code", "execution_count": 32, "id": "3b106c21", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Loaded KML quality assessment: 1288 records\n", "Loaded IFC assessment: 14460 records\n", "No matches file found - proceeding with separate datasets\n"]}], "source": ["# Load quality assessments\n", "kml_quality_path = get_processed_data_path(site_name, \"reference-data\") / \"kml_quality_assessment.csv\"\n", "ifc_assessment_path = get_processed_data_path(site_name, \"reference-data\") / \"ifc_assessment.csv\"\n", "\n", "kml_quality_df = pd.read_csv(kml_quality_path)\n", "ifc_assessment_df = pd.read_csv(ifc_assessment_path)\n", "print(f\"Loaded KML quality assessment: {len(kml_quality_df)} records\")\n", "print(f\"Loaded IFC assessment: {len(ifc_assessment_df)} records\")\n", "\n", "# Load spatial matches\n", "try:\n", "    matches_path = get_processed_data_path(site_name, \"reference-data\") / \"kml_ifc_matches.csv\"\n", "    matches_df = pd.read_csv(matches_path)\n", "    print(f\"Loaded KML-IFC matches: {len(matches_df)} matched pairs\")\n", "    has_matches = True\n", "except FileNotFoundError:\n", "    print(\"No matches file found - proceeding with separate datasets\")\n", "    has_matches = False\n"]}, {"cell_type": "code", "execution_count": 33, "id": "b44d45ae", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "==================================================\n", "CREATING HARMONIZED DATASET\n", "==================================================\n", "\n", "Tier 2: Processing unmatched high-quality KML locations...\n", "Added 348 unmatched KML locations to dataset\n", "\n", "Tier 3: Processing high-confidence unmatched IFC locations...\n"]}], "source": ["\n", "# Create harmonized dataset\n", "print(\"\\n\" + \"=\"*50)\n", "print(\"CREATING HARMONIZED DATASET\")\n", "print(\"=\"*50)\n", "\n", "harmonized_records = []\n", "\n", "# TIER 1: MATCHED PAIRS (Highest Confidence)\n", "if has_matches and len(matches_df) > 0:\n", "    print(f\"\\nTier 1: Processing {len(matches_df)} matched KML-IFC pairs...\")\n", "    \n", "    for _, match in matches_df.iterrows():\n", "        kml_idx = int(match['kml_idx'])\n", "        ifc_idx = int(match['ifc_idx'])\n", "        \n", "        # Get KML data (use as ground truth coordinates)\n", "        kml_coord = kml_coords[kml_idx]\n", "        kml_quality = kml_quality_df[kml_quality_df['kml_idx'] == kml_idx].iloc[0]\n", "        \n", "        # Get IFC data for metadata\n", "        ifc_record = ifc_df.iloc[ifc_idx]\n", "        ifc_quality = ifc_assessment_df[ifc_assessment_df['ifc_idx'] == ifc_idx].iloc[0]\n", "        \n", "        # Determine confidence level\n", "        if kml_quality['point_count'] > 30 and ifc_quality['confidence'] == 'high':\n", "            confidence = 'very_high'\n", "        elif kml_quality['point_count'] > 30 or ifc_quality['confidence'] == 'high':\n", "            confidence = 'high'\n", "        else:\n", "            confidence = 'medium'\n", "        \n", "        harmonized_records.append({\n", "            'pile_id': f\"matched_{kml_idx}_{ifc_idx}\",\n", "            'source': 'matched',\n", "            'confidence': confidence,\n", "            'x': kml_coord[0],\n", "            'y': kml_coord[1],\n", "            'z': kml_quality['height_mean'],\n", "            'point_count': kml_quality['point_count'],\n", "            'match_distance': match['distance'],\n", "            'kml_quality_score': kml_quality['quality_score'],\n", "            'ifc_name': ifc_record['Name'],\n", "            'ifc_classification': ifc_quality['classification'],\n", "            'ifc_confidence': ifc_quality['confidence'],\n", "            'original_kml_idx': kml_idx,\n", "            'original_ifc_idx': ifc_idx\n", "        })\n", "    \n", "    print(f\"Added {len(matches_df)} matched pairs to harmonized dataset\")\n", "\n", "# TIER 2: HIGH-QUALITY UNMATCHED KML (Ground Truth without IFC match)\n", "print(f\"\\nTier 2: Processing unmatched high-quality KML locations...\")\n", "\n", "if has_matches:\n", "    matched_kml_indices = set(matches_df['kml_idx'].values)\n", "else:\n", "    matched_kml_indices = set()\n", "\n", "# Get high-quality unmatched KML\n", "good_kml = kml_quality_df[kml_quality_df['point_count'] >= 20]  # Slightly higher threshold for unmatched\n", "unmatched_good_kml = good_kml[~good_kml['kml_idx'].isin(matched_kml_indices)]\n", "\n", "for _, kml_record in unmatched_good_kml.iterrows():\n", "    kml_idx = int(kml_record['kml_idx'])\n", "    kml_coord = kml_coords[kml_idx]\n", "    \n", "    # Confidence based on point count\n", "    if kml_record['point_count'] > 40:\n", "        confidence = 'high'\n", "    elif kml_record['point_count'] > 30:\n", "        confidence = 'medium'\n", "    else:\n", "        confidence = 'low'\n", "    \n", "    harmonized_records.append({\n", "        'pile_id': f\"kml_only_{kml_idx}\",\n", "        'source': 'kml_only',\n", "        'confidence': confidence,\n", "        'x': kml_coord[0],\n", "        'y': kml_coord[1],\n", "        'z': kml_record['height_mean'],\n", "        'point_count': kml_record['point_count'],\n", "        'match_distance': np.nan,\n", "        'kml_quality_score': kml_record['quality_score'],\n", "        'ifc_name': np.nan,\n", "        'ifc_classification': np.nan,\n", "        'ifc_confidence': np.nan,\n", "        'original_kml_idx': kml_idx,\n", "        'original_ifc_idx': np.nan\n", "    })\n", "\n", "print(f\"Added {len(unmatched_good_kml)} unmatched KML locations to dataset\")\n", "\n", "# TIER 3: HIGH-CONFIDENCE UNMATCHED IFC (Design locations with strong signatures)\n", "print(f\"\\nTier 3: Processing high-confidence unmatched IFC locations...\")\n", "\n", "if has_matches:\n", "    matched_ifc_indices = set(matches_df['ifc_idx'].values)\n", "else:\n", "    matched_ifc_indices = set()\n", "\n", "# Get high-confidence unmatched IFC\n", "high_conf_ifc = ifc_assessment_df[\n", "    (ifc_assessment_df['classification'] == 'potential_pile') &\n", "    (ifc_assessment_df['confidence'] == 'high') &\n", "    (ifc_assessment_df['point_count'] > 25)  # Higher threshold for unmatched\n", "]\n", "unmatched_high_conf_ifc = high_conf_ifc[~high_conf_ifc['ifc_idx'].isin(matched_ifc_indices)]\n", "\n"]}, {"cell_type": "code", "execution_count": 34, "id": "ba28c8cb", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Added 1000 high-confidence IFC locations to dataset\n", "\n", "==================================================\n", "HARMONIZED DATASET SUMMARY\n", "==================================================\n", "Total records: 1348\n", "\n", "By source:\n", "  ifc_only: 1000\n", "  kml_only: 348\n", "\n", "By confidence:\n", "  medium: 1098\n", "  low: 210\n", "  high: 40\n", "\n", "==================================================\n", "APPLYING FINAL QUALITY FILTERS\n", "==================================================\n", "Applying minimum point count filter: 15 points\n", "Retained: 1348/1348 records\n", "Validating point density using point cloud...\n", "After point cloud verification: 1348/1348 records\n", "\n", "==================================================\n", "FINAL DATASET STATISTICS\n", "==================================================\n", "Final dataset size: 1348\n", "\n", "Final source distribution:\n", "  ifc_only: 1000\n", "  kml_only: 348\n", "\n", "Final confidence distribution:\n", "  medium: 1098\n", "  low: 210\n", "  high: 40\n", "\n", "Point count statistics:\n", "  Mean: 42.0\n", "  Median: 45.0\n", "  Min: 20\n", "  Max: 67\n"]}], "source": ["# Limit to reasonable number to avoid overwhelming dataset\n", "max_ifc_unmatched = 1000\n", "if len(unmatched_high_conf_ifc) > max_ifc_unmatched:\n", "    # Sort by point count and take top ones\n", "    unmatched_high_conf_ifc = unmatched_high_conf_ifc.nlargest(max_ifc_unmatched, 'point_count')\n", "\n", "for _, ifc_record in unmatched_high_conf_ifc.iterrows():\n", "    ifc_idx = int(ifc_record['ifc_idx'])\n", "    ifc_coord = ifc_coords[ifc_idx]\n", "    ifc_metadata = ifc_df.iloc[ifc_idx]\n", "    \n", "    harmonized_records.append({\n", "        'pile_id': f\"ifc_only_{ifc_idx}\",\n", "        'source': 'ifc_only',\n", "        'confidence': 'medium',  # Lower confidence since no ground truth\n", "        'x': ifc_coord[0],\n", "        'y': ifc_coord[1],\n", "        'z': ifc_record['height_mean'],\n", "        'point_count': ifc_record['point_count'],\n", "        'match_distance': np.nan,\n", "        'kml_quality_score': np.nan,\n", "        'ifc_name': ifc_metadata['Name'],\n", "        'ifc_classification': ifc_record['classification'],\n", "        'ifc_confidence': ifc_record['confidence'],\n", "        'original_kml_idx': np.nan,\n", "        'original_ifc_idx': ifc_idx\n", "    })\n", "\n", "print(f\"Added {len(unmatched_high_conf_ifc)} high-confidence IFC locations to dataset\")\n", "\n", "# Create harmonized DataFrame\n", "harmonized_df = pd.DataFrame(harmonized_records)\n", "\n", "print(f\"\\n\" + \"=\"*50)\n", "print(\"HARMONIZED DATASET SUMMARY\")\n", "print(\"=\"*50)\n", "\n", "print(f\"Total records: {len(harmonized_df)}\")\n", "print(f\"\\nBy source:\")\n", "source_counts = harmonized_df['source'].value_counts()\n", "for source, count in source_counts.items():\n", "    print(f\"  {source}: {count}\")\n", "\n", "print(f\"\\nBy confidence:\")\n", "confidence_counts = harmonized_df['confidence'].value_counts()\n", "for conf, count in confidence_counts.items():\n", "    print(f\"  {conf}: {count}\")\n", "\n", "# Apply final quality filters\n", "print(f\"\\n\" + \"=\"*50)\n", "print(\"APPLYING FINAL QUALITY FILTERS\")\n", "print(\"=\"*50)\n", "\n", "# Minimum point count filter\n", "min_points = 15\n", "print(f\"Applying minimum point count filter: {min_points} points\")\n", "\n", "final_dataset = harmonized_df[harmonized_df['point_count'] >= min_points].copy()\n", "print(f\"Retained: {len(final_dataset)}/{len(harmonized_df)} records\")\n", "\n", "# Point density validation using point cloud\n", "print(f\"Validating point density using point cloud...\")\n", "point_tree_2d = cKDTree(points[:, :2])\n", "patch_radius = 5.0\n", "\n", "final_point_counts = []\n", "for _, pile in final_dataset.iterrows():\n", "    pile_coord = [pile['x'], pile['y']]\n", "    indices = point_tree_2d.query_ball_point(pile_coord, patch_radius)\n", "    final_point_counts.append(len(indices))\n", "\n", "final_dataset['verified_point_count'] = final_point_counts\n", "\n", "# Filter based on verified point count\n", "min_verified_points = 15\n", "verified_dataset = final_dataset[final_dataset['verified_point_count'] >= min_verified_points].copy()\n", "\n", "print(f\"After point cloud verification: {len(verified_dataset)}/{len(final_dataset)} records\")\n", "\n", "print(f\"\\n\" + \"=\"*50)\n", "print(\"FINAL DATASET STATISTICS\")\n", "print(\"=\"*50)\n", "\n", "print(f\"Final dataset size: {len(verified_dataset)}\")\n", "print(f\"\\nFinal source distribution:\")\n", "final_source_counts = verified_dataset['source'].value_counts()\n", "for source, count in final_source_counts.items():\n", "    print(f\"  {source}: {count}\")\n", "\n", "print(f\"\\nFinal confidence distribution:\")\n", "final_confidence_counts = verified_dataset['confidence'].value_counts()\n", "for conf, count in final_confidence_counts.items():\n", "    print(f\"  {conf}: {count}\")\n", "\n", "print(f\"\\nPoint count statistics:\")\n", "print(f\"  Mean: {verified_dataset['verified_point_count'].mean():.1f}\")\n", "print(f\"  Median: {verified_dataset['verified_point_count'].median():.1f}\")\n", "print(f\"  Min: {verified_dataset['verified_point_count'].min()}\")\n", "print(f\"  Max: {verified_dataset['verified_point_count'].max()}\")\n", "\n"]}, {"cell_type": "code", "execution_count": 35, "id": "00c5aaa6", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "Recommended usage:\n", "  Training set (very_high + high): 40 samples\n", "  Validation set (medium): 1098 samples\n"]}], "source": ["# Training/validation split recommendation\n", "very_high_conf = len(verified_dataset[verified_dataset['confidence'] == 'very_high'])\n", "high_conf = len(verified_dataset[verified_dataset['confidence'] == 'high'])\n", "medium_conf = len(verified_dataset[verified_dataset['confidence'] == 'medium'])\n", "\n", "print(f\"\\nRecommended usage:\")\n", "print(f\"  Training set (very_high + high): {very_high_conf + high_conf} samples\")\n", "print(f\"  Validation set (medium): {medium_conf} samples\")\n"]}, {"cell_type": "code", "execution_count": 36, "id": "1c820555", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "Dataset saved to: /Users/<USER>/Documents/GitHub/asbuilt-foundation-analysis/data/processed/trino_enel/modelling/harmonized_pile_dataset_final.csv\n", "Configuration saved to: /Users/<USER>/Documents/GitHub/asbuilt-foundation-analysis/data/processed/trino_enel/modelling/harmonized_dataset_config.json\n"]}], "source": ["\n", "# Save harmonized dataset\n", "output_csv_path = get_processed_data_path(site_name, \"modelling\")/\"harmonized_pile_dataset_final.csv\"\n", "verified_dataset.to_csv(output_csv_path, index=False)\n", "print(f\"\\nDataset saved to: {output_csv_path}\")\n", "\n", "# Save configuration\n", "config = {\n", "    'total_samples': int(len(verified_dataset)),\n", "    'source_distribution': {k: int(v) for k, v in final_source_counts.items()},\n", "    'confidence_distribution': {k: int(v) for k, v in final_confidence_counts.items()},\n", "    'point_count_stats': {\n", "        'mean': float(verified_dataset['verified_point_count'].mean()),\n", "        'median': float(verified_dataset['verified_point_count'].median()),\n", "        'min': int(verified_dataset['verified_point_count'].min()),\n", "        'max': int(verified_dataset['verified_point_count'].max())\n", "    },\n", "    'patch_radius': float(patch_radius),\n", "    'min_points_threshold': int(min_verified_points),\n", "    'recommended_training_samples': int(very_high_conf + high_conf),\n", "    'recommended_validation_samples': int(medium_conf)\n", "}\n", "\n", "output_json_path = get_processed_data_path(site_name, \"modelling\")/\"harmonized_dataset_config.json\"\n", "with open(output_json_path, \"w\") as f:\n", "    json.dump(config, f, indent=2)\n", "    \n", "print(f\"Configuration saved to: {output_json_path}\")\n", "\n"]}, {"cell_type": "code", "execution_count": 37, "id": "abed3916", "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 1500x500 with 3 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 1200x800 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "==================================================\n", "HARMONIZED DATASET CREATION COMPLETE\n", "==================================================\n", "Ready for patch extraction with 1348 high-quality pile locations!\n"]}], "source": ["# Visualization\n", "plt.figure(figsize=(15, 5))\n", "\n", "# Source distribution\n", "plt.subplot(1, 3, 1)\n", "final_source_counts.plot(kind='bar', color=['green', 'blue', 'orange'])\n", "plt.title(\"Final Dataset by Source\")\n", "plt.ylabel(\"Number of Samples\")\n", "plt.xticks(rotation=45)\n", "plt.grid(True, alpha=0.3)\n", "\n", "# Confidence distribution\n", "plt.subplot(1, 3, 2)\n", "final_confidence_counts.plot(kind='bar', color=['red', 'orange', 'green', 'darkgreen'])\n", "plt.title(\"Final Dataset by Confidence\")\n", "plt.ylabel(\"Number of Samples\")\n", "plt.xticks(rotation=45)\n", "plt.grid(True, alpha=0.3)\n", "\n", "# Point count distribution\n", "plt.subplot(1, 3, 3)\n", "plt.hist(verified_dataset['verified_point_count'], bins=30, alpha=0.7, edgecolor='black')\n", "plt.xlabel(\"Verified Point Count\")\n", "plt.ylabel(\"Number of Samples\")\n", "plt.title(\"Point Density Distribution\")\n", "plt.grid(True, alpha=0.3)\n", "\n", "plt.tight_layout()\n", "plt.show()\n", "\n", "# Spatial distribution\n", "plt.figure(figsize=(12, 8))\n", "colors = {'matched': 'green', 'kml_only': 'blue', 'ifc_only': 'orange'}\n", "sizes = {'very_high': 50, 'high': 30, 'medium': 15, 'low': 10}\n", "\n", "for source in verified_dataset['source'].unique():\n", "    subset = verified_dataset[verified_dataset['source'] == source]\n", "    scatter_sizes = [sizes[conf] for conf in subset['confidence']]\n", "    plt.scatter(subset['x'], subset['y'], \n", "               c=colors[source], s=scatter_sizes, alpha=0.7, \n", "               label=f\"{source} ({len(subset)})\")\n", "\n", "plt.xlabel(\"X (m)\")\n", "plt.ylabel(\"Y (m)\")\n", "plt.title(\"Harmonized Dataset Spatial Distribution\\n(Size = Confidence Level)\")\n", "plt.legend()\n", "plt.grid(True, alpha=0.3)\n", "plt.show()\n", "\n", "print(\"\\n\" + \"=\"*50)\n", "print(\"HARMONIZED DATASET CREATION COMPLETE\")\n", "print(\"=\"*50)\n", "print(f\"Ready for patch extraction with {len(verified_dataset)} high-quality pile locations!\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 5}