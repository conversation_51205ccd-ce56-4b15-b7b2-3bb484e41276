{"cells": [{"cell_type": "markdown", "id": "c616f9e0", "metadata": {}, "source": ["# Pile Alignment & Distance Analysis\n", "\n", "This notebook evaluates the spatial alignment between KML, IFC, and point cloud data to verify coordinate consistency and identify systematic offsets. It also analyzes pile-level distances and elevation agreement.\n", "\n", "**Objectives:**\n", "- Compare coordinate ranges and overlaps (KML vs IFC vs Point Cloud)\n", "- Measure point cloud density near pile locations\n", "- Compute nearest distances from pile locations to point cloud\n", "- Detect systematic XY offsets\n", "- Visualize alignment and deviation patterns\n", "\n", "**Author**: <PERSON><PERSON><PERSON>  \n", "**Date**: August 2025  \n", "**Project**: As-Built Foundation Analysis\n"]}, {"cell_type": "code", "execution_count": 58, "id": "f71f316c", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Notebooks root: /Users/<USER>/Documents/GitHub/asbuilt-foundation-analysis/notebooks\n"]}], "source": ["# Papermill parameters - these will be injected by Papermill\n", "import sys\n", "from pathlib import Path\n", "\n", "# Add the `notebooks` folder to sys.path\n", "notebooks_root = Path(__file__).resolve().parents[2] if '__file__' in globals() else Path.cwd().parents[2]\n", "print(f\"Notebooks root: {notebooks_root}\")\n", "\n", "if str(notebooks_root) not in sys.path:\n", "    sys.path.insert(0, str(notebooks_root))\n", "\n", "# Now import from shared package\n", "from shared.config import (\n", "    get_data_path,\n", "    get_output_path,\n", "    get_processed_data_path\n", ")\n", "\n", "# Papermill parameters - these will be injected by Papermill\n", "from datetime import datetime\n", "\n", "site_name = \"trino_enel\"  # Site name for output file naming\n", "\n", "timestamp = datetime.now().strftime(\"%Y%m%d_%H%M%S\")\n", "\n", "# Standardized paths\n", "input_path = get_processed_data_path(site_name, f\"reference-data\")\n", "CRS = \"EPSG:32632\""]}, {"cell_type": "markdown", "id": "c1100dab", "metadata": {}, "source": ["## Import required libraries"]}, {"cell_type": "code", "execution_count": 59, "id": "565b44d0", "metadata": {}, "outputs": [], "source": ["import geopandas as gpd\n", "import pandas as pd\n", "\n", "import geopandas as gpd\n", "import pandas as pd\n", "import numpy as np\n", "import open3d as o3d\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "from scipy.spatial.distance import cdist\n", "from scipy.spatial import cKDTree\n", "from pathlib import Path\n", "\n", "# Set plotting style\n", "sns.set(style=\"whitegrid\")\n"]}, {"cell_type": "code", "execution_count": 60, "id": "31a1687b", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["=== LOADING KML DATA ===\n", "KML path: /Users/<USER>/Documents/GitHub/asbuilt-foundation-analysis/data/processed/trino_enel/reference-data/kml_piles_utm.csv\n", "KML piles: 1288 records\n", "Columns: ['Name', 'Description', 'X', 'Y', 'Z']\n"]}], "source": ["# Cell 2: Load KML data\n", "print(\"=== LOADING KML DATA ===\")\n", "\n", "kml_path = input_path / \"kml_piles_utm.csv\"\n", "print(f\"KML path: {kml_path}\")\n", "kml_df = pd.read_csv(kml_path)\n", "\n", "if 'Z' not in kml_df.columns:\n", "    kml_df['Z'] = 0\n", "print(f\"KML piles: {len(kml_df)} records\")\n", "print(f\"Columns: {list(kml_df.columns)}\")"]}, {"cell_type": "code", "execution_count": 61, "id": "0e6e9e9f", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["=== LOADING IFC DATA ===\n", "Loaded IFC enhanced metadata: 14460 records\n", "Columns: ['GlobalId', 'Name', 'Type', 'Description', 'Tag', 'Site_Latitude', 'Site_Longitude', 'Site_Elevation', 'X', 'Y', 'Z', 'GeometryExtracted', 'Longitude', 'Latitude']\n"]}], "source": ["# Cell 3: Load IFC data\n", "print(\"=== LOADING IFC DATA ===\")\n", "ifc_enhanced_path = '/Users/<USER>/Documents/GitHub/asbuilt-foundation-analysis/data/processed/trino_enel/ifc_metadata/GRE.EEC.S.00.IT.P.14353.00.265_enhanced_metadata.csv'\n", "ifc_piles_path = '/Users/<USER>/Documents/GitHub/asbuilt-foundation-analysis/data/processed/trino_enel/ifc_metadata/GRE.EEC.S.00.IT.P.14353.00.265_piles.csv'\n", "\n", "try:\n", "    ifc_df = pd.read_csv(ifc_enhanced_path)\n", "    print(f\"Loaded IFC enhanced metadata: {len(ifc_df)} records\")\n", "except FileNotFoundError:\n", "    ifc_df = pd.read_csv(ifc_piles_path)\n", "    print(f\"Loaded IFC piles file: {len(ifc_df)} records\")\n", "\n", "print(f\"Columns: {list(ifc_df.columns)}\")"]}, {"cell_type": "code", "execution_count": 62, "id": "0c93d5be", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["=== LOADING POINT CLOUD DATA ===\n", "Point cloud points: 517,002\n", "Point cloud shape: (517002, 3)\n"]}], "source": ["# Cell 4: Load point cloud data\n", "print(\"=== LOADING POINT CLOUD DATA ===\")\n", "ply_path = '/Users/<USER>/Documents/GitHub/asbuilt-foundation-analysis/data/processed/trino_enel/ml_local_alignment/trino_enel_ml_corrected.ply'\n", "point_cloud = o3d.io.read_point_cloud(ply_path)\n", "ply_points = np.asarray(point_cloud.points)\n", "print(f\"Point cloud points: {len(ply_points):,}\")\n", "print(f\"Point cloud shape: {ply_points.shape}\")\n"]}, {"cell_type": "code", "execution_count": 63, "id": "9a3bb77d", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["=== LOADING HARMONIZED DATA ===\n", "Harmonized file not found - skipping\n", "=== DATASET SUMMARY ===\n", "KML piles: 1288\n", "IFC piles: 14460\n", "Point cloud points: 517,002\n"]}], "source": ["# Cell 5: Load harmonized data (optional)\n", "print(\"=== LOADING HARMONIZED DATA ===\")\n", "harmonized_path = Path('/Users/<USER>/Documents/GitHub/asbuilt-foundation-analysis/notebooks/modeling/pile_detection/00_data_preparation/output/ml_patch_data/trino_enel_harmonized_piles_corrected.csv')\n", "\n", "harmonized_df = None\n", "if harmonized_path.exists():\n", "    harmonized_df = pd.read_csv(harmonized_path)\n", "    print(f\"Harmonized piles: {len(harmonized_df)} records\")\n", "else:\n", "    print(\"Harmonized file not found - skipping\")\n", "\n", "# %%\n", "# Cell 6: Dataset summary\n", "print(\"=== DATASET SUMMARY ===\")\n", "print(f\"KML piles: {len(kml_df)}\")\n", "print(f\"IFC piles: {len(ifc_df)}\")\n", "print(f\"Point cloud points: {len(ply_points):,}\")\n", "if harmonized_df is not None:\n", "    print(f\"Harmonized piles: {len(harmonized_df)}\")"]}, {"cell_type": "code", "execution_count": 64, "id": "e585dd1a", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["=== COORDINATE RANGES ===\n", "KML coordinates:\n", "  X: 435628.57 to 436255.19\n", "  Y: 5011261.36 to 5012200.75\n", "IFC coordinates:\n", "  X: 435267.20 to 436719.95\n", "  Y: 5010900.71 to 5012462.41\n", "Point cloud coordinates:\n", "  X: 435220.82 to 436795.41\n", "  Y: 5010813.78 to 5012552.58\n", "  Z: 154.91 to 179.54\n", "\n", "=== ALIGNMENT CHECK ===\n", "Coordinate overlaps:\n", "  KML vs IFC: X=Yes, Y=Yes\n", "  KML vs PLY: X=Yes, Y=Yes\n"]}], "source": ["# Cell 7: Coordinate range analysis\n", "def analyze_coordinate_ranges(kml_df, ifc_df, ply_points):\n", "    \"\"\"Compare coordinate ranges between all datasets.\"\"\"\n", "    print(\"=== COORDINATE RANGES ===\")\n", "    \n", "    # KML coordinates\n", "    print(\"KML coordinates:\")\n", "    print(f\"  X: {kml_df['X'].min():.2f} to {kml_df['X'].max():.2f}\")\n", "    print(f\"  Y: {kml_df['Y'].min():.2f} to {kml_df['Y'].max():.2f}\")\n", "    \n", "    # IFC coordinates\n", "    print(\"IFC coordinates:\")\n", "    print(f\"  X: {ifc_df['X'].min():.2f} to {ifc_df['X'].max():.2f}\")\n", "    print(f\"  Y: {ifc_df['Y'].min():.2f} to {ifc_df['Y'].max():.2f}\")\n", "    \n", "    # Point cloud coordinates\n", "    print(\"Point cloud coordinates:\")\n", "    print(f\"  X: {ply_points[:, 0].min():.2f} to {ply_points[:, 0].max():.2f}\")\n", "    print(f\"  Y: {ply_points[:, 1].min():.2f} to {ply_points[:, 1].max():.2f}\")\n", "    print(f\"  Z: {ply_points[:, 2].min():.2f} to {ply_points[:, 2].max():.2f}\")\n", "    \n", "    # Check overlaps\n", "    print(\"\\n=== ALIGNMENT CHECK ===\")\n", "    kml_x_range = (kml_df['X'].min(), kml_df['X'].max())\n", "    kml_y_range = (kml_df['Y'].min(), kml_df['Y'].max())\n", "    ifc_x_range = (ifc_df['X'].min(), ifc_df['X'].max())\n", "    ifc_y_range = (ifc_df['Y'].min(), ifc_df['Y'].max())\n", "    ply_x_range = (ply_points[:, 0].min(), ply_points[:, 0].max())\n", "    ply_y_range = (ply_points[:, 1].min(), ply_points[:, 1].max())\n", "    \n", "    # Check overlaps\n", "    kml_ifc_x_overlap = kml_x_range[1] > ifc_x_range[0] and kml_x_range[0] < ifc_x_range[1]\n", "    kml_ifc_y_overlap = kml_y_range[1] > ifc_y_range[0] and kml_y_range[0] < ifc_y_range[1]\n", "    kml_ply_x_overlap = kml_x_range[1] > ply_x_range[0] and kml_x_range[0] < ply_x_range[1]\n", "    kml_ply_y_overlap = kml_y_range[1] > ply_y_range[0] and kml_y_range[0] < ply_y_range[1]\n", "    \n", "    print(\"Coordinate overlaps:\")\n", "    print(f\"  KML vs IFC: X={'Yes' if kml_ifc_x_overlap else 'No'}, Y={'Yes' if kml_ifc_y_overlap else 'No'}\")\n", "    print(f\"  KML vs PLY: X={'Yes' if kml_ply_x_overlap else 'No'}, Y={'Yes' if kml_ply_y_overlap else 'No'}\")\n", "    \n", "    return {\n", "        'kml_ifc_overlap': (kml_ifc_x_overlap, kml_ifc_y_overlap),\n", "        'kml_ply_overlap': (kml_ply_x_overlap, kml_ply_y_overlap)\n", "    }\n", "\n", "# Analyze coordinate ranges\n", "overlap_results = analyze_coordinate_ranges(kml_df, ifc_df, ply_points)\n"]}, {"cell_type": "code", "execution_count": 65, "id": "d16ee02b", "metadata": {}, "outputs": [], "source": ["# Cell 8: Distance analysis functions\n", "def compute_nearest_distances(coordinates, ply_points):\n", "    \"\"\"\n", "    For each coordinate point, find the distance to the nearest point cloud point.\n", "    \n", "    Parameters:\n", "    coordinates: numpy array of [X, Y] coordinates\n", "    ply_points: numpy array of point cloud coordinates [X, Y, Z]\n", "    \n", "    Returns:\n", "    distances: array of distances to nearest points\n", "    nearest_z_values: array of Z values of nearest points\n", "    nearest_indices: array of indices of nearest points\n", "    \"\"\"\n", "    # Build spatial index for point cloud\n", "    tree = cKDTree(ply_points[:, :2])  # X,Y only\n", "    \n", "    distances_to_nearest = []\n", "    nearest_z_values = []\n", "    nearest_indices = []\n", "    \n", "    for coord_point in coordinates:\n", "        # Find nearest point cloud point\n", "        distance, index = tree.query(coord_point)\n", "        distances_to_nearest.append(distance)\n", "        nearest_z_values.append(ply_points[index, 2])  # Z coordinate\n", "        nearest_indices.append(index)\n", "    \n", "    return np.array(distances_to_nearest), np.array(nearest_z_values), np.array(nearest_indices)\n", "\n", "def analyze_distance_statistics(distances, dataset_name):\n", "    \"\"\"Print comprehensive distance statistics.\"\"\"\n", "    print(f\"\\nDistance from {dataset_name} to nearest PLY point:\")\n", "    print(f\"  Mean: {np.mean(distances):.2f}m\")\n", "    print(f\"  Median: {np.median(distances):.2f}m\")\n", "    print(f\"  Min: {np.min(distances):.2f}m\")\n", "    print(f\"  Max: {np.max(distances):.2f}m\")\n", "    print(f\"  Standard deviation: {np.std(distances):.2f}m\")\n", "    \n", "    print(f\"\\nPiles within different distance thresholds:\")\n", "    for threshold in [1, 2, 5, 10, 20]:\n", "        count = sum(1 for d in distances if d <= threshold)\n", "        percentage = (count / len(distances)) * 100\n", "        print(f\"  Within {threshold}m: {count}/{len(distances)} ({percentage:.1f}%)\")\n", "\n", "def analyze_systematic_offset(coordinates, ply_points, dataset_name):\n", "    \"\"\"Check for systematic offset patterns between coordinate data and point cloud.\"\"\"\n", "    print(f\"\\n=== SYSTEMATIC OFFSET CHECK ({dataset_name}) ===\")\n", "    \n", "    # Build spatial index\n", "    tree = cKDTree(ply_points[:, :2])\n", "    \n", "    x_offsets = []\n", "    y_offsets = []\n", "    \n", "    for coord_point in coordinates:\n", "        _, index = tree.query(coord_point)\n", "        nearest_ply_point = ply_points[index, :2]\n", "        x_offset = coord_point[0] - nearest_ply_point[0]\n", "        y_offset = coord_point[1] - nearest_ply_point[1]\n", "        x_offsets.append(x_offset)\n", "        y_offsets.append(y_offset)\n", "    \n", "    print(f\"Offset analysis ({dataset_name} - PLY):\")\n", "    print(f\"  X offset - Mean: {np.mean(x_offsets):.2f}m, Median: {np.median(x_offsets):.2f}m\")\n", "    print(f\"  Y offset - Mean: {np.mean(y_offsets):.2f}m, Median: {np.median(y_offsets):.2f}m\")\n", "    print(f\"  X offset std: {np.std(x_offsets):.2f}m\")\n", "    print(f\"  Y offset std: {np.std(y_offsets):.2f}m\")\n", "    \n", "    # Check if there's a consistent directional bias\n", "    if abs(np.median(x_offsets)) > 1 or abs(np.median(y_offsets)) > 1:\n", "        print(f\"  POTENTIAL SYSTEMATIC OFFSET DETECTED\")\n", "    else:\n", "        print(f\"  NO SIGNIFICANT SYSTEMATIC OFFSET\")\n", "    \n", "    return x_offsets, y_offsets"]}, {"cell_type": "code", "execution_count": 66, "id": "06fbee8c", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["=== POINT DENSITY CHECK (KML) ===\n", "Points within 5.0m of KML piles:\n", "  Mean: 12.3\n", "  Median: 8.0\n", "  Min: 0\n", "  Max: 60\n", "  <PERSON><PERSON> with >0 points: 1030/1288\n", "=== POINT DENSITY CHECK (IFC) ===\n", "Points within 5.0m of IFC piles:\n", "  Mean: 24.3\n", "  Median: 24.0\n", "  Min: 0\n", "  Max: 85\n", "  <PERSON><PERSON> with >0 points: 13440/14460\n"]}], "source": ["# Cell 9: Point density analysis\n", "def analyze_point_density(pile_df, ply_points, dataset_name, search_radius=5.0):\n", "    \"\"\"For each pile location, count nearby point cloud points within search radius.\"\"\"\n", "    print(f\"=== POINT DENSITY CHECK ({dataset_name}) ===\")\n", "    \n", "    # Build spatial index for point cloud\n", "    tree = cKDTree(ply_points[:, :2])  # X,Y only\n", "    \n", "    nearby_counts = []\n", "    for _, pile in pile_df.iterrows():\n", "        pile_coords = [pile['X'], pile['Y']]\n", "        indices = tree.query_ball_point(pile_coords, search_radius)\n", "        nearby_counts.append(len(indices))\n", "    \n", "    pile_df[f'nearby_points_{search_radius}m'] = nearby_counts\n", "    \n", "    print(f\"Points within {search_radius}m of {dataset_name} piles:\")\n", "    print(f\"  Mean: {np.mean(nearby_counts):.1f}\")\n", "    print(f\"  Median: {np.median(nearby_counts):.1f}\")\n", "    print(f\"  Min: {np.min(nearby_counts)}\")\n", "    print(f\"  Max: {np.max(nearby_counts)}\")\n", "    print(f\"  Piles with >0 points: {sum(1 for x in nearby_counts if x > 0)}/{len(nearby_counts)}\")\n", "    \n", "    return nearby_counts\n", "\n", "# Analyze point density for both datasets\n", "kml_density = analyze_point_density(kml_df, ply_points, \"KML\")\n", "ifc_density = analyze_point_density(ifc_df, ply_points, \"IFC\")"]}, {"cell_type": "code", "execution_count": 67, "id": "e177c309", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["=== KML vs ML-CORRECTED PLY DISTANCE ANALYSIS ===\n", "\n", "Distance from KML to nearest PLY point:\n", "  Mean: 3.24m\n", "  Median: 1.81m\n", "  Min: 0.03m\n", "  Max: 24.88m\n", "  Standard deviation: 3.73m\n", "\n", "Piles within different distance thresholds:\n", "  Within 1m: 382/1288 (29.7%)\n", "  Within 2m: 676/1288 (52.5%)\n", "  Within 5m: 1030/1288 (80.0%)\n", "  Within 10m: 1208/1288 (93.8%)\n", "  Within 20m: 1281/1288 (99.5%)\n", "\n", "Elevation analysis (nearest PLY points):\n", "  Z range: 156.16m to 158.58m\n", "  Z mean: 157.28m\n", "\n", "=== SYSTEMATIC OFFSET CHECK (KML) ===\n", "Offset analysis (KML - PLY):\n", "  X offset - Mean: 0.04m, Median: 0.05m\n", "  Y offset - Mean: 0.18m, Median: 0.03m\n", "  X offset std: 3.36m\n", "  Y offset std: 3.62m\n", "  NO SIGNIFICANT SYSTEMATIC OFFSET\n"]}], "source": ["# Cell 10: KML vs Point Cloud analysis\n", "print(\"=== KML vs ML-CORRECTED PLY DISTANCE ANALYSIS ===\")\n", "\n", "kml_coords = kml_df[['X', 'Y']].values\n", "kml_distances, kml_nearest_z, _ = compute_nearest_distances(kml_coords, ply_points)\n", "\n", "# Add results to dataframe\n", "kml_df['distance_to_nearest_ply'] = kml_distances\n", "kml_df['nearest_ply_z'] = kml_nearest_z\n", "\n", "# Print statistics\n", "analyze_distance_statistics(kml_distances, \"KML\")\n", "\n", "print(f\"\\nElevation analysis (nearest PLY points):\")\n", "print(f\"  Z range: {np.min(kml_nearest_z):.2f}m to {np.max(kml_nearest_z):.2f}m\")\n", "print(f\"  Z mean: {np.mean(kml_nearest_z):.2f}m\")\n", "\n", "# Analyze systematic offset\n", "kml_x_offsets, kml_y_offsets = analyze_systematic_offset(kml_coords, ply_points, \"KML\")\n"]}, {"cell_type": "code", "execution_count": 68, "id": "f58558c2", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["=== IFC vs ML-CORRECTED PLY DISTANCE ANALYSIS ===\n", "Processing 14460 IFC pile locations...\n", "\n", "Distance from IFC to nearest PLY point:\n", "  Mean: 1.83m\n", "  Median: 1.05m\n", "  Min: 0.00m\n", "  Max: 46.43m\n", "  Standard deviation: 2.70m\n", "\n", "Piles within different distance thresholds:\n", "  Within 1m: 6948/14460 (48.0%)\n", "  Within 2m: 10882/14460 (75.3%)\n", "  Within 5m: 13440/14460 (92.9%)\n", "  Within 10m: 14174/14460 (98.0%)\n", "  Within 20m: 14410/14460 (99.7%)\n", "\n", "Elevation analysis (nearest PLY points):\n", "  Z range: 155.35m to 159.80m\n", "  Z mean: 157.39m\n", "  Z difference (IFC - PLY): Mean -0.06m, Std 0.29m\n", "\n", "=== SYSTEMATIC OFFSET CHECK (IFC) ===\n", "Offset analysis (IFC - PLY):\n", "  X offset - Mean: -0.07m, Median: -0.04m\n", "  Y offset - Mean: -0.04m, Median: 0.01m\n", "  X offset std: 2.30m\n", "  Y offset std: 2.32m\n", "  NO SIGNIFICANT SYSTEMATIC OFFSET\n"]}], "source": ["# Cell 11: IFC vs Point Cloud analysis\n", "print(\"=== IFC vs ML-CORRECTED PLY DISTANCE ANALYSIS ===\")\n", "\n", "ifc_coords = ifc_df[['X', 'Y']].values\n", "print(f\"Processing {len(ifc_coords)} IFC pile locations...\")\n", "\n", "ifc_distances, ifc_nearest_z, _ = compute_nearest_distances(ifc_coords, ply_points)\n", "\n", "# Add results to dataframe\n", "ifc_df['distance_to_nearest_ply'] = ifc_distances\n", "ifc_df['nearest_ply_z'] = ifc_nearest_z\n", "\n", "# Print statistics\n", "analyze_distance_statistics(ifc_distances, \"IFC\")\n", "\n", "print(f\"\\nElevation analysis (nearest PLY points):\")\n", "print(f\"  Z range: {np.min(ifc_nearest_z):.2f}m to {np.max(ifc_nearest_z):.2f}m\")\n", "print(f\"  Z mean: {np.mean(ifc_nearest_z):.2f}m\")\n", "\n", "# Compare IFC Z with nearest PLY Z if Z column exists\n", "if 'Z' in ifc_df.columns:\n", "    ifc_z_values = ifc_df['Z'].values\n", "    z_differences = ifc_z_values - ifc_nearest_z\n", "    print(f\"  Z difference (IFC - PLY): Mean {np.mean(z_differences):.2f}m, Std {np.std(z_differences):.2f}m\")\n", "\n", "# Analyze systematic offset\n", "ifc_x_offsets, ifc_y_offsets = analyze_systematic_offset(ifc_coords, ply_points, \"IFC\")\n"]}, {"cell_type": "code", "execution_count": 69, "id": "2c04b2dc", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["=== COMPARISON SUMMARY ===\n", "KML vs PLY: 3.24m mean, 1.81m median\n", "IFC vs PLY: 1.83m mean, 1.05m median\n", "Difference: -1.41m mean, -0.76m median\n"]}], "source": ["# Cell 12: Comparison summary\n", "print(\"=== COMPARISON SUMMARY ===\")\n", "print(f\"KML vs PLY: {np.mean(kml_distances):.2f}m mean, {np.median(kml_distances):.2f}m median\")\n", "print(f\"IFC vs PLY: {np.mean(ifc_distances):.2f}m mean, {np.median(ifc_distances):.2f}m median\")\n", "print(f\"Difference: {np.mean(ifc_distances) - np.mean(kml_distances):.2f}m mean, {np.median(ifc_distances) - np.median(kml_distances):.2f}m median\")\n"]}, {"cell_type": "code", "execution_count": 70, "id": "5368b19c", "metadata": {}, "outputs": [], "source": ["# Cell 13: Visualization functions\n", "def plot_distance_histogram(distances, dataset_name, bins=40):\n", "    \"\"\"Create histogram plot of distance distributions.\"\"\"\n", "    plt.figure(figsize=(10, 6))\n", "    plt.hist(distances, bins=bins, alpha=0.7, edgecolor='black')\n", "    plt.title(f'Distance Distribution: {dataset_name} to Nearest Point Cloud Point')\n", "    plt.xlabel('Distance (meters)')\n", "    plt.ylabel('Frequency')\n", "    plt.grid(True, alpha=0.3)\n", "    \n", "    # Add statistics text\n", "    stats_text = f'Mean: {np.mean(distances):.2f}m\\nMedian: {np.median(distances):.2f}m\\nStd: {np.std(distances):.2f}m'\n", "    plt.text(0.7, 0.8, stats_text, transform=plt.gca().transAxes, \n", "             bbox=dict(boxstyle='round', facecolor='white', alpha=0.8))\n", "    \n", "    plt.tight_layout()\n", "    plt.show()\n", "\n", "def plot_offset_scatter(x_offsets, y_offsets, dataset_name):\n", "    \"\"\"Create scatter plot of X,Y offsets to visualize systematic bias.\"\"\"\n", "    plt.figure(figsize=(10, 8))\n", "    plt.scatter(x_offsets, y_offsets, alpha=0.6)\n", "    plt.axhline(y=0, color='red', linestyle='--', alpha=0.7)\n", "    plt.axvline(x=0, color='red', linestyle='--', alpha=0.7)\n", "    plt.xlabel('X Offset (meters)')\n", "    plt.ylabel('Y Offset (meters)')\n", "    plt.title(f'Systematic Offset Analysis: {dataset_name}')\n", "    plt.grid(True, alpha=0.3)\n", "    \n", "    # Add mean offset marker\n", "    mean_x, mean_y = np.mean(x_offsets), np.mean(y_offsets)\n", "    plt.scatter(mean_x, mean_y, color='red', s=100, marker='x', label=f'Mean Offset ({mean_x:.2f}, {mean_y:.2f})')\n", "    plt.legend()\n", "    \n", "    plt.tight_layout()\n", "    plt.show()\n", "\n", "def plot_comparative_histogram(kml_distances, ifc_distances):\n", "    \"\"\"Create comparative histogram of both datasets.\"\"\"\n", "    plt.figure(figsize=(12, 6))\n", "    plt.hist(kml_distances, bins=30, alpha=0.7, label='KML', edgecolor='black')\n", "    plt.hist(ifc_distances, bins=30, alpha=0.7, label='IFC', edgecolor='black')\n", "    plt.title('Distance Comparison: KML vs IFC to Nearest Point Cloud Point')\n", "    plt.xlabel('Distance (meters)')\n", "    plt.ylabel('Frequency')\n", "    plt.legend()\n", "    plt.grid(True, alpha=0.3)\n", "    plt.tight_layout()\n", "    plt.show()\n"]}, {"cell_type": "code", "execution_count": 71, "id": "df98feaf", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Creating visualizations...\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 1000x600 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 1000x600 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 1200x600 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "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**************************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", "text/plain": ["<Figure size 1000x800 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "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***************************************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", "text/plain": ["<Figure size 1000x800 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["Analysis complete!\n"]}], "source": ["# Cell 14: Create all visualizations\n", "print(\"Creating visualizations...\")\n", "\n", "# Individual histograms\n", "plot_distance_histogram(kml_distances, \"KML\")\n", "plot_distance_histogram(ifc_distances, \"IFC\")\n", "\n", "# Comparative histogram\n", "plot_comparative_histogram(kml_distances, ifc_distances)\n", "\n", "# Offset scatter plots\n", "plot_offset_scatter(kml_x_offsets, kml_y_offsets, \"KML\")\n", "plot_offset_scatter(ifc_x_offsets, ifc_y_offsets, \"IFC\")\n", "\n", "print(\"Analysis complete!\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 5}