{"cells": [{"cell_type": "markdown", "id": "7f951454", "metadata": {}, "source": ["# # Notebook 7: Pre-Patch Extraction Validation\n"]}, {"cell_type": "code", "execution_count": 12, "id": "d406107e", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Pre-patch extraction validation...\n"]}], "source": ["# Basic imports\n", "import numpy as np\n", "import pandas as pd\n", "import open3d as o3d\n", "import matplotlib.pyplot as plt\n", "from scipy.spatial import cKDTree\n", "import json\n", "from pathlib import Path\n", "\n", "print(\"Pre-patch extraction validation...\")\n"]}, {"cell_type": "code", "execution_count": 13, "id": "dd7fda81", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Loaded point cloud: 983,884 points\n"]}], "source": ["# Load point cloud\n", "ply_path = \"../../../../data/processed/trino_enel/denoising/trino_enel_denoised_for_registration.ply\"\n", "point_cloud = o3d.io.read_point_cloud(ply_path)\n", "points = np.asarray(point_cloud.points)\n", "print(f\"Loaded point cloud: {len(points):,} points\")\n"]}, {"cell_type": "code", "execution_count": 14, "id": "c0cfe22c", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Loaded harmonized dataset: 1435 pile locations\n"]}], "source": ["# Load harmonized dataset\n", "harmonized_df = pd.read_csv(\"harmonized_pile_dataset_final.csv\")\n", "print(f\"Loaded harmonized dataset: {len(harmonized_df)} pile locations\")\n"]}, {"cell_type": "code", "execution_count": 15, "id": "296479db", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Selected 100 samples across confidence levels\n"]}], "source": ["# Sample subset\n", "sample_indices = []\n", "for confidence in harmonized_df['confidence'].unique():\n", "    subset = harmonized_df[harmonized_df['confidence'] == confidence]\n", "    n_samples = min(25, len(subset))\n", "    if len(subset) > 0:\n", "        sampled = subset.sample(n=n_samples, random_state=42)\n", "        sample_indices.extend(sampled.index.tolist())\n", "\n", "validation_sample = harmonized_df.loc[sample_indices].copy()\n", "print(f\"Selected {len(validation_sample)} samples across confidence levels\")"]}, {"cell_type": "code", "execution_count": 16, "id": "39f1c513", "metadata": {}, "outputs": [], "source": ["# Preprocessing\n", "patch_radius = 8.0  # meters\n", "min_points_per_patch = 30\n", "point_tree_2d = cKDTree(points[:, :2])\n"]}, {"cell_type": "code", "execution_count": 17, "id": "5f6b0258", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "Analyzing patch characteristics...\n", "  Processed 25/100 samples...\n", "  Processed 50/100 samples...\n", "  Processed 75/100 samples...\n", "  Processed 100/100 samples...\n", "\n", "Validation complete.\n", "Valid patches: 100/100 (100.0%)\n", "Saved to patch_validation_results.csv\n"]}], "source": ["validation_results = []\n", "\n", "print(f\"\\nAnalyzing patch characteristics...\")\n", "for i, (_, pile) in enumerate(validation_sample.iterrows()):\n", "    pile_coord = [pile['x'], pile['y']]\n", "    indices = point_tree_2d.query_ball_point(pile_coord, patch_radius)\n", "    patch_points = points[indices]\n", "\n", "    if len(patch_points) >= min_points_per_patch:\n", "        z_range = patch_points[:, 2].max() - patch_points[:, 2].min()\n", "        z_std = np.std(patch_points[:, 2])\n", "        z_mean = np.mean(patch_points[:, 2])\n", "        center_point = np.array([pile['x'], pile['y']])\n", "        distances_to_center = np.linalg.norm(patch_points[:, :2] - center_point, axis=1)\n", "        avg_distance_to_center = np.mean(distances_to_center)\n", "        patch_area = np.pi * patch_radius**2\n", "        point_density = len(patch_points) / patch_area\n", "\n", "        is_valid_patch = all([\n", "            len(patch_points) >= min_points_per_patch,\n", "            z_range < 10.0,\n", "            point_density > 0.3,\n", "            z_std < 3.0\n", "        ])\n", "\n", "        validation_results.append({\n", "            'pile_id': pile['pile_id'],\n", "            'confidence': pile['confidence'],\n", "            'point_count': len(patch_points),\n", "            'z_range': z_range,\n", "            'z_std': z_std,\n", "            'z_mean': z_mean,\n", "            'point_density': point_density,\n", "            'avg_distance_to_center': avg_distance_to_center,\n", "            'is_valid': is_valid_patch\n", "        })\n", "    else:\n", "        validation_results.append({\n", "            'pile_id': pile['pile_id'],\n", "            'confidence': pile['confidence'],\n", "            'point_count': len(patch_points),\n", "            'z_range': np.nan,\n", "            'z_std': np.nan,\n", "            'z_mean': np.nan,\n", "            'point_density': np.nan,\n", "            'avg_distance_to_center': np.nan,\n", "            'is_valid': False\n", "        })\n", "\n", "    if (i + 1) % 25 == 0:\n", "        print(f\"  Processed {i + 1}/{len(validation_sample)} samples...\")\n", "\n", "validation_df = pd.DataFrame(validation_results)\n", "\n", "# Basic result summary\n", "print(f\"\\nValidation complete.\")\n", "print(f\"Valid patches: {validation_df['is_valid'].sum()}/{len(validation_df)} ({validation_df['is_valid'].mean()*100:.1f}%)\")\n", "\n", "# Save results\n", "validation_df.to_csv(\"patch_validation_results.csv\", index=False)\n", "print(f\"Saved to patch_validation_results.csv\")"]}, {"cell_type": "code", "execution_count": 18, "id": "a98b515e", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/var/folders/gf/nl47jt2n0w9dh5kzsz1rf1mc0000gn/T/ipykernel_3447/3702600134.py:28: FutureWarning: Setting an item of incompatible dtype is deprecated and will raise an error in a future version of pandas. Value 'N/A' has dtype incompatible with float64, please explicitly cast to a compatible dtype first.\n", "  summary.fillna('N/A', inplace=True)\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Feature</th>\n", "      <th>Valid Mean</th>\n", "      <th><PERSON><PERSON></th>\n", "      <th>Invalid Mean</th>\n", "      <th>Invalid Std</th>\n", "      <th>Mean Difference</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>point_count</td>\n", "      <td>119.300</td>\n", "      <td>21.787</td>\n", "      <td>N/A</td>\n", "      <td>N/A</td>\n", "      <td>N/A</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>z_range</td>\n", "      <td>1.393</td>\n", "      <td>1.382</td>\n", "      <td>N/A</td>\n", "      <td>N/A</td>\n", "      <td>N/A</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>z_std</td>\n", "      <td>0.451</td>\n", "      <td>0.517</td>\n", "      <td>N/A</td>\n", "      <td>N/A</td>\n", "      <td>N/A</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>z_mean</td>\n", "      <td>0.627</td>\n", "      <td>0.614</td>\n", "      <td>N/A</td>\n", "      <td>N/A</td>\n", "      <td>N/A</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>point_density</td>\n", "      <td>0.593</td>\n", "      <td>0.108</td>\n", "      <td>N/A</td>\n", "      <td>N/A</td>\n", "      <td>N/A</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>avg_distance_to_center</td>\n", "      <td>5.298</td>\n", "      <td>0.194</td>\n", "      <td>N/A</td>\n", "      <td>N/A</td>\n", "      <td>N/A</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                  Feature  Valid Mean  Valid Std Invalid Mean Invalid Std  \\\n", "0             point_count     119.300     21.787          N/A         N/A   \n", "1                 z_range       1.393      1.382          N/A         N/A   \n", "2                   z_std       0.451      0.517          N/A         N/A   \n", "3                  z_mean       0.627      0.614          N/A         N/A   \n", "4           point_density       0.593      0.108          N/A         N/A   \n", "5  avg_distance_to_center       5.298      0.194          N/A         N/A   \n", "\n", "  Mean Difference  \n", "0             N/A  \n", "1             N/A  \n", "2             N/A  \n", "3             N/A  \n", "4             N/A  \n", "5             N/A  "]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Summary table: valid vs invalid feature means\n", "valid = validation_df[validation_df['is_valid'] == True]\n", "invalid = validation_df[validation_df['is_valid'] == False]\n", "\n", "features = ['point_count', 'z_range', 'z_std', 'z_mean', 'point_density', 'avg_distance_to_center']\n", "\n", "summary_data = []\n", "for f in features:\n", "    valid_mean = valid[f].mean()\n", "    valid_std = valid[f].std()\n", "    if not invalid.empty and invalid[f].notna().sum() > 0:\n", "        invalid_mean = invalid[f].mean()\n", "        invalid_std = invalid[f].std()\n", "        mean_diff = valid_mean - invalid_mean\n", "    else:\n", "        invalid_mean = np.nan\n", "        invalid_std = np.nan\n", "        mean_diff = np.nan\n", "    summary_data.append([f, valid_mean, valid_std, invalid_mean, invalid_std, mean_diff])\n", "\n", "summary = pd.DataFrame(summary_data, columns=[\n", "    'Feature', 'Valid Mean', 'Valid Std', 'Invalid Mean', 'Invalid Std', 'Mean Difference'\n", "])\n", "\n", "for col in ['Valid Mean', 'Valid Std', 'Invalid Mean', 'Invalid Std', 'Mean Difference']:\n", "    summary[col] = pd.to_numeric(summary[col], errors='coerce').round(3)\n", "\n", "summary.fillna('N/A', inplace=True)\n", "\n", "display(summary)\n"]}, {"cell_type": "code", "execution_count": 19, "id": "fa8f87d7", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "PATCH EXTRACTION SUMMARY\n", "Total samples: 100\n", "Valid patches: 100 (100.0%)\n", "Invalid patches: 0\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Final summary and optional histogram\n", "print(\"\\nPATCH EXTRACTION SUMMARY\")\n", "print(f\"Total samples: {len(validation_df)}\")\n", "print(f\"Valid patches: {validation_df['is_valid'].sum()} ({validation_df['is_valid'].mean() * 100:.1f}%)\")\n", "print(f\"Invalid patches: {(~validation_df['is_valid']).sum()}\")\n", "\n", "# Optional histogram\n", "validation_df[validation_df['is_valid']]['point_count'].hist(bins=30, edgecolor='black')\n", "plt.title(\"Distribution of Points in Valid Patches\")\n", "plt.xlabel(\"Number of Points\")\n", "plt.ylabel(\"Frequency\")\n", "plt.grid(True)\n", "plt.show()"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 5}