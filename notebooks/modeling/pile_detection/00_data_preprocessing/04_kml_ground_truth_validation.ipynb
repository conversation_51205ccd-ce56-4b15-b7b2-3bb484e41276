{"cells": [{"cell_type": "markdown", "id": "be09d4ca", "metadata": {}, "source": ["# KML Ground Truth Validation\n", "\n", "Validate KML pile annotations against point cloud data to assess annotation quality and establish ground truth reliability for machine learning training.\n", "\n", "**Objectives:**\n", "- Validate KML pile locations against point cloud density\n", "- Compute 2D spatial matches and quality scores  \n", "- Analyze height profiles and point coverage metrics\n", "- Generate quality assessment reports for ground truth establishment\n", "\n", "**Author**: <PERSON><PERSON><PERSON>  \n", "**Date**: August 2025  \n", "**Project**: As-Built Foundation Analysis"]}, {"cell_type": "code", "execution_count": 116, "id": "2b9d3d6f", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Notebooks root: /Users/<USER>/Documents/GitHub/asbuilt-foundation-analysis/notebooks\n"]}], "source": ["# Papermill parameters - these will be injected by Papermill\n", "from datetime import datetime\n", "import sys\n", "from pathlib import Path\n", "\n", "# Add the `notebooks` folder to sys.path\n", "notebooks_root = Path(__file__).resolve().parents[2] if '__file__' in globals() else Path.cwd().parents[2]\n", "print(f\"Notebooks root: {notebooks_root}\")\n", "\n", "if str(notebooks_root) not in sys.path:\n", "    sys.path.insert(0, str(notebooks_root))\n", "\n", "# Now import from shared package\n", "from shared.config import (\n", "    get_data_path,\n", "    get_processed_data_path\n", ")\n", "site_name = \"trino_enel\"  # Site name for output file naming\n", "\n", "timestamp = datetime.now().strftime(\"%Y%m%d_%H%M%S\")"]}, {"cell_type": "code", "execution_count": 117, "id": "68440d28", "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "import pandas as pd\n", "import open3d as o3d\n", "import geopandas as gpd\n", "import matplotlib.pyplot as plt\n", "from scipy.spatial import cKDTree\n", "from pathlib import Path\n", "import json\n", "import seaborn as sns\n"]}, {"cell_type": "markdown", "id": "239fc60f", "metadata": {}, "source": ["## Load point cloud"]}, {"cell_type": "code", "execution_count": 118, "id": "4f236b53", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Loaded point cloud: 517,002 points\n", "Point cloud bounds: X(435220.8, 436795.4) Y(5010813.8, 5012552.6) Z(154.9, 179.5)\n"]}], "source": ["ply_path = get_processed_data_path(site_name, \"ml_local_alignment\") / \"trino_enel_ml_corrected.ply\"\n", "point_cloud = o3d.io.read_point_cloud(ply_path)\n", "points = np.asarray(point_cloud.points)\n", "\n", "print(f\"Loaded point cloud: {len(points):,} points\")\n", "print(f\"Point cloud bounds: X({points[:, 0].min():.1f}, {points[:, 0].max():.1f}) Y({points[:, 1].min():.1f}, {points[:, 1].max():.1f}) Z({points[:, 2].min():.1f}, {points[:, 2].max():.1f})\")"]}, {"cell_type": "markdown", "id": "c5659038", "metadata": {}, "source": ["## Load and transform KML data"]}, {"cell_type": "code", "execution_count": 119, "id": "36d8982b", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Loaded KML data: 1288 pile locations\n", "KML bounds: X(435628.6, 436255.2) Y(5011261.4, 5012200.8)\n"]}], "source": ["kml_path = get_data_path(site_name) / \"kml\" / \"pile.kml\"\n", "gdf_kml = gpd.read_file(kml_path, driver='KML')\n", "\n", "# Ensure we have valid geometries\n", "gdf_kml = gdf_kml[gdf_kml.geometry.notnull()].copy()\n", "\n", "# Set CRS and reproject\n", "gdf_kml = gdf_kml.set_crs(epsg=4326)\n", "gdf_kml_utm = gdf_kml.to_crs(epsg=32632)\n", "\n", "# Convert to centroids (handles both polygons and points)\n", "gdf_kml_utm['geometry'] = gdf_kml_utm.geometry.apply(\n", "    lambda geom: geom.centroid if geom.geom_type != 'Point' else geom\n", ")\n", "\n", "# Extract coordinates\n", "kml_coords = np.stack([\n", "    gdf_kml_utm.geometry.x.values,\n", "    gdf_kml_utm.geometry.y.values,\n", "    np.zeros(len(gdf_kml_utm))\n", "], axis=1)\n", "\n", "print(f\"Loaded KML data: {len(kml_coords)} pile locations\")\n", "print(f\"KML bounds: X({kml_coords[:, 0].min():.1f}, {kml_coords[:, 0].max():.1f}) Y({kml_coords[:, 1].min():.1f}, {kml_coords[:, 1].max():.1f})\")"]}, {"cell_type": "markdown", "id": "75154b41", "metadata": {}, "source": ["## Quick overlap check"]}, {"cell_type": "code", "execution_count": 120, "id": "76a3da84", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["KML locations within point cloud bounds: 1288/1288\n"]}], "source": ["# Quick overlap check\n", "overlap_check = (\n", "    (kml_coords[:, 0] >= points[:, 0].min()) & \n", "    (kml_coords[:, 0] <= points[:, 0].max()) &\n", "    (kml_coords[:, 1] >= points[:, 1].min()) & \n", "    (kml_coords[:, 1] <= points[:, 1].max())\n", ").sum()\n", "\n", "print(f\"KML locations within point cloud bounds: {overlap_check}/{len(kml_coords)}\")"]}, {"cell_type": "markdown", "id": "bf2ce904", "metadata": {}, "source": ["## Build spatial index for point cloud (X,Y only)"]}, {"cell_type": "code", "execution_count": 121, "id": "59435159", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Validating KML locations against point cloud (2D search)...\n", "KML validation results:\n", "Total KML locations: 1288\n", "Locations with points: 1030\n", "High quality locations (>30 points): 138\n", "Medium quality locations (10-30 points): 469\n"]}], "source": ["# Build 2D spatial index (X,Y only)\n", "point_tree_2d = cKDTree(points[:, :2])\n", "search_radius = 5.0\n", "\n", "print(\"Validating KML locations against point cloud (2D search)...\")\n", "\n", "# Analyze each KML location\n", "kml_quality = []\n", "for i, kml_coord in enumerate(kml_coords):\n", "    # Use only X,Y coordinates for search\n", "    indices = point_tree_2d.query_ball_point(kml_coord[:2], search_radius)\n", "    nearby_points = points[indices]\n", "    \n", "    if len(nearby_points) > 0:\n", "        # Height analysis\n", "        z_mean = np.mean(nearby_points[:, 2])\n", "        z_std = np.std(nearby_points[:, 2])\n", "        \n", "        # Height above ground calculation\n", "        ground_level = np.percentile(points[:, 2], 10)\n", "        height_above_ground = z_mean - ground_level\n", "        \n", "        quality_score = min(len(nearby_points) / 50.0, 1.0)\n", "        \n", "        kml_quality.append({\n", "            'kml_idx': i,\n", "            'point_count': len(nearby_points),\n", "            'height_mean': z_mean,\n", "            'height_std': z_std,\n", "            'height_above_ground': height_above_ground,\n", "            'quality_score': quality_score\n", "        })\n", "    else:\n", "        # This shouldn't happen now, but keep for safety\n", "        kml_quality.append({\n", "            'kml_idx': i,\n", "            'point_count': 0,\n", "            'height_mean': np.nan,\n", "            'height_std': np.nan,\n", "            'height_above_ground': np.nan,\n", "            'quality_score': 0.0\n", "        })\n", "\n", "kml_quality_df = pd.DataFrame(kml_quality)\n", "\n", "print(f\"KML validation results:\")\n", "print(f\"Total KML locations: {len(kml_coords)}\")\n", "print(f\"Locations with points: {(kml_quality_df['point_count'] > 0).sum()}\")\n", "print(f\"High quality locations (>30 points): {(kml_quality_df['point_count'] > 30).sum()}\")\n", "print(f\"Medium quality locations (10-30 points): {((kml_quality_df['point_count'] >= 10) & (kml_quality_df['point_count'] <= 30)).sum()}\")\n"]}, {"cell_type": "markdown", "id": "53b2410b", "metadata": {}, "source": ["# Debug: Check first few KML locations in detail\n"]}, {"cell_type": "code", "execution_count": 122, "id": "4cdddd13", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Debug: Checking first 5 KML locations...\n", "\n", "KML 0: [435628.79, 5012103.14, 0.00]\n", "  Radius 5m: 25 points\n", "  Radius 10m: 66 points\n", "  Radius 20m: 258 points\n", "  Radius 50m: 1370 points\n", "  Nearest point: [435628.79, 5012103.32, 158.31]\n", "  Distance: 0.18m\n", "\n", "KML 1: [435628.75, 5012110.89, 0.00]\n", "  Radius 5m: 24 points\n", "  Radius 10m: 62 points\n", "  Radius 20m: 283 points\n", "  Radius 50m: 1500 points\n", "  Nearest point: [435629.53, 5012110.92, 158.27]\n", "  Distance: 0.78m\n", "\n", "KML 2: [435628.57, 5012069.48, 0.00]\n", "  Radius 5m: 30 points\n", "  Radius 10m: 81 points\n", "  Radius 20m: 298 points\n", "  Radius 50m: 1480 points\n", "  Nearest point: [435629.15, 5012069.25, 158.27]\n", "  Distance: 0.62m\n", "\n", "KML 3: [435628.62, 5012076.95, 0.00]\n", "  Radius 5m: 12 points\n", "  Radius 10m: 60 points\n", "  Radius 20m: 283 points\n", "  Radius 50m: 1459 points\n", "  Nearest point: [435628.54, 5012077.53, 158.26]\n", "  Distance: 0.59m\n", "\n", "KML 4: [435628.59, 5012084.33, 0.00]\n", "  Radius 5m: 30 points\n", "  Radius 10m: 66 points\n", "  Radius 20m: 270 points\n", "  Radius 50m: 1392 points\n", "  Nearest point: [435628.75, 5012084.67, 158.23]\n", "  Distance: 0.38m\n", "\n", "Trying 2D search...\n", "2D search result: 25 points found\n"]}], "source": ["print(\"Debug: Checking first 5 KML locations...\")\n", "for i in range(min(5, len(kml_coords))):\n", "    kml_coord = kml_coords[i]\n", "    print(f\"\\nKML {i}: [{kml_coord[0]:.2f}, {kml_coord[1]:.2f}, {kml_coord[2]:.2f}]\")\n", "    \n", "    # Try different search radii\n", "    for radius in [5, 10, 20, 50]:\n", "        # indices = point_tree.query_ball_point(kml_coord, radius)\n", "        indices = point_tree_2d.query_ball_point(kml_coord[:2], radius)\n", "        print(f\"  Radius {radius}m: {len(indices)} points\")\n", "    \n", "    # Check nearest point manually\n", "    # distance, nearest_idx = point_tree.query(kml_coord)\n", "    distance, nearest_idx = point_tree_2d.query(kml_coord[:2])\n", "    nearest_point = points[nearest_idx]\n", "    print(f\"  Nearest point: [{nearest_point[0]:.2f}, {nearest_point[1]:.2f}, {nearest_point[2]:.2f}]\")\n", "    print(f\"  Distance: {distance:.2f}m\")\n", "\n", "# Check if we should use 2D search instead\n", "print(\"\\nTrying 2D search...\")\n", "point_tree_2d = cKDTree(points[:, :2])  # Only X,Y coordinates\n", "test_kml = kml_coords[0][:2]  # Only X,Y coordinates\n", "indices_2d = point_tree_2d.query_ball_point(test_kml, 5.0)\n", "print(f\"2D search result: {len(indices_2d)} points found\")"]}, {"cell_type": "markdown", "id": "2772c08e", "metadata": {}, "source": ["## Quality Assessment of KML Pile Locations\n"]}, {"cell_type": "code", "execution_count": 123, "id": "cdbe7647", "metadata": {}, "outputs": [{"data": {"image/png": "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*******************************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", "text/plain": ["<Figure size 1200x400 with 3 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Quality distribution\n", "plt.figure(figsize=(12, 4))\n", "plt.subplot(1, 3, 1)\n", "plt.hist(kml_quality_df['point_count'], bins=30, alpha=0.7)\n", "plt.xlabel(\"Point Count\")\n", "plt.ylabel(\"KML Locations\")\n", "plt.title(\"Point Density at KML Locations\")\n", "\n", "plt.subplot(1, 3, 2)\n", "valid_heights = kml_quality_df['height_above_ground'].dropna()\n", "plt.hist(valid_heights, bins=30, alpha=0.7)\n", "plt.xlabel(\"Height Above Ground (m)\")\n", "plt.ylabel(\"KML Locations\")\n", "plt.title(\"Height Profile at KML Locations\")\n", "\n", "plt.subplot(1, 3, 3)\n", "plt.hist(kml_quality_df['quality_score'], bins=20, alpha=0.7)\n", "plt.xlabel(\"Quality Score\")\n", "plt.ylabel(\"KML Locations\")\n", "plt.title(\"KML Quality Distribution\")\n", "plt.tight_layout()\n", "plt.show()\n"]}, {"cell_type": "code", "execution_count": 124, "id": "c9edfc8b", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Saved KML quality assessment to: /Users/<USER>/Documents/GitHub/asbuilt-foundation-analysis/data/processed/trino_enel/reference-data/kml_quality_assessment.csv\n", "\n", "Quality summary:\n", "High quality KML locations: 138\n", "Medium quality KML locations: 469\n", "Recommended for training: 607\n", "\n", "KML validation complete.\n"]}], "source": ["# Save quality assessment\n", "kml_quality_path = get_processed_data_path(site_name, \"reference-data\") / \"kml_quality_assessment.csv\"\n", "kml_quality_df.to_csv(kml_quality_path, index=False)\n", "print(f\"Saved KML quality assessment to: {kml_quality_path}\")\n", "\n", "# Define quality thresholds\n", "high_quality_kml = kml_quality_df[kml_quality_df['point_count'] > 30]\n", "medium_quality_kml = kml_quality_df[(kml_quality_df['point_count'] >= 10) & \n", "                                   (kml_quality_df['point_count'] <= 30)]\n", "\n", "print(f\"\\nQuality summary:\")\n", "print(f\"High quality KML locations: {len(high_quality_kml)}\")\n", "print(f\"Medium quality KML locations: {len(medium_quality_kml)}\")\n", "print(f\"Recommended for training: {len(high_quality_kml) + len(medium_quality_kml)}\")\n", "\n", "print(\"\\nKML validation complete.\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 5}