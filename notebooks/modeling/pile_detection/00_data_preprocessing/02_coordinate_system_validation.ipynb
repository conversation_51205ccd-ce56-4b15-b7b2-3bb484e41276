{"cells": [{"cell_type": "markdown", "id": "b02def75", "metadata": {}, "source": ["# Load Point Cloud and <PERSON><PERSON>\n", "\n", "This section loads the aligned 3D point cloud captured by drone, along with pile metadata from IFC and KML sources. It establishes the spatial reference consistency required for accurate downstream comparison and analysis.\n", "\n", "**Objectives:**\n", "- Load and visualize point cloud spatial extent (X, Y, Z range)\n", "- Load IFC-derived pile coordinates from CSV (design truth)\n", "- Convert KML pile buffers to centroid points and reproject to UTM Zone 32N\n", "- Ensure all datasets share a common coordinate system\n", "- Check how many pile locations fall within point cloud bounds\n", "- Visualize the spatial overlay of:\n", "  - Subsampled point cloud\n", "  - IFC pile positions\n", "  - KML centroids\n", "\n", "**Author**: <PERSON><PERSON><PERSON>  \n", "**Date**: August 2025  \n", "**Project**: As-Built Foundation Analysis\n"]}, {"cell_type": "code", "execution_count": 19, "id": "616a15b8", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Notebooks root: /Users/<USER>/Documents/GitHub/asbuilt-foundation-analysis/notebooks\n"]}], "source": ["# Basic imports\n", "import sys\n", "from pathlib import Path\n", "\n", "# Add the `notebooks` folder to sys.path\n", "notebooks_root = Path(__file__).resolve().parents[2] if '__file__' in globals() else Path.cwd().parents[2]\n", "print(f\"Notebooks root: {notebooks_root}\")\n", "\n", "if str(notebooks_root) not in sys.path:\n", "    sys.path.insert(0, str(notebooks_root))\n", "\n", "# Now import from shared package\n", "from shared.config import (\n", "    get_data_path,\n", "    get_processed_data_path\n", ")\n", "\n", "import numpy as np\n", "import pandas as pd\n", "import open3d as o3d\n", "import geopandas as gpd\n", "from pathlib import Path\n", "import matplotlib.pyplot as plt"]}, {"cell_type": "code", "execution_count": 20, "id": "4ea847e9", "metadata": {}, "outputs": [], "source": ["# Papermill parameters - these will be injected by Papermill\n", "from datetime import datetime\n", "\n", "site_name = \"trino_enel\"  # Site name for output file naming\n", "\n", "timestamp = datetime.now().strftime(\"%Y%m%d_%H%M%S\")\n", "\n", "# Standardized paths\n", "output_path = get_processed_data_path(site_name, f\"reference-data\")\n", "output_path.mkdir(parents=True, exist_ok=True)\n", "CRS = \"EPSG:32632\""]}, {"cell_type": "code", "execution_count": 22, "id": "7d8d8ba0", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Point cloud: 517,002 points\n", "X range: 435220.82 to 436795.41\n", "Y range: 5010813.78 to 5012552.58\n", "Z range: 154.91 to 179.54\n"]}], "source": ["# Load point cloud\n", "ply_path = get_processed_data_path(site_name, \"ml_local_alignment/trino_enel_ml_corrected.ply\")\n", "\n", "point_cloud = o3d.io.read_point_cloud(ply_path)\n", "points = np.asarray(point_cloud.points)\n", "\n", "print(f\"Point cloud: {len(points):,} points\")\n", "print(f\"X range: {points[:, 0].min():.2f} to {points[:, 0].max():.2f}\")\n", "print(f\"Y range: {points[:, 1].min():.2f} to {points[:, 1].max():.2f}\")\n", "print(f\"Z range: {points[:, 2].min():.2f} to {points[:, 2].max():.2f}\")\n"]}, {"cell_type": "code", "execution_count": 23, "id": "311eded6", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "IFC data: 14460 piles\n", "X range: 435267.20 to 436719.95\n", "Y range: 5010900.71 to 5012462.41\n", "Z range: 154.99 to 159.52\n"]}], "source": ["# Load IFC data\n", "ifc_path = get_processed_data_path(site_name, \"ifc_metadata/GRE.EEC.S.00.IT.P.14353.00.265_enhanced_metadata.csv\")\n", "ifc_df = pd.read_csv(ifc_path)\n", "ifc_coords = ifc_df[['X', 'Y', 'Z']].values\n", "\n", "print(f\"\\nIFC data: {len(ifc_coords)} piles\")\n", "print(f\"X range: {ifc_coords[:, 0].min():.2f} to {ifc_coords[:, 0].max():.2f}\")\n", "print(f\"Y range: {ifc_coords[:, 1].min():.2f} to {ifc_coords[:, 1].max():.2f}\")\n", "print(f\"Z range: {ifc_coords[:, 2].min():.2f} to {ifc_coords[:, 2].max():.2f}\")"]}, {"cell_type": "code", "execution_count": 24, "id": "427bb5db", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "KML data: 1288 piles\n", "X range: 435628.57 to 436255.19\n", "Y range: 5011261.36 to 5012200.75\n"]}], "source": ["import geopandas as gpd\n", "import numpy as np\n", "\n", "# Load and transform KML data\n", "kml_path = get_data_path(site_name) / \"kml\" / \"pile.kml\"\n", "gdf_kml = gpd.read_file(kml_path, driver='KML')\n", "gdf_kml = gdf_kml.set_crs(epsg=4326)\n", "\n", "# Reproject to UTM zone 32N (you can adjust if needed)\n", "gdf_kml_utm = gdf_kml.to_crs(epsg=32632)\n", "\n", "# Convert polygons to centroids\n", "gdf_kml_utm['geometry'] = gdf_kml_utm.geometry.centroid\n", "\n", "# Extract centroid coordinates (x, y) and set z = 0\n", "kml_coords = np.stack([\n", "    gdf_kml_utm.geometry.x.values,\n", "    gdf_kml_utm.geometry.y.values,\n", "    np.zeros(len(gdf_kml_utm))\n", "], axis=1)\n", "\n", "print(f\"\\nKML data: {len(kml_coords)} piles\")\n", "print(f\"X range: {kml_coords[:, 0].min():.2f} to {kml_coords[:, 0].max():.2f}\")\n", "print(f\"Y range: {kml_coords[:, 1].min():.2f} to {kml_coords[:, 1].max():.2f}\")\n"]}, {"cell_type": "code", "execution_count": 25, "id": "96bf15ec", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "Spatial overlap:\n", "IFC piles in point cloud bounds: 14460/14460\n", "KML piles in point cloud bounds: 1288/1288\n"]}], "source": ["\n", "# Check overlap\n", "pc_bounds = {\n", "    'x_min': points[:, 0].min(), 'x_max': points[:, 0].max(),\n", "    'y_min': points[:, 1].min(), 'y_max': points[:, 1].max()\n", "}\n", "\n", "ifc_in_bounds = (\n", "    (ifc_coords[:, 0] >= pc_bounds['x_min']) & \n", "    (ifc_coords[:, 0] <= pc_bounds['x_max']) &\n", "    (ifc_coords[:, 1] >= pc_bounds['y_min']) & \n", "    (ifc_coords[:, 1] <= pc_bounds['y_max'])\n", ").sum()\n", "\n", "kml_in_bounds = (\n", "    (kml_coords[:, 0] >= pc_bounds['x_min']) & \n", "    (kml_coords[:, 0] <= pc_bounds['x_max']) &\n", "    (kml_coords[:, 1] >= pc_bounds['y_min']) & \n", "    (kml_coords[:, 1] <= pc_bounds['y_max'])\n", ").sum()\n", "\n", "print(f\"\\nSpatial overlap:\")\n", "print(f\"IFC piles in point cloud bounds: {ifc_in_bounds}/{len(ifc_coords)}\")\n", "print(f\"KML piles in point cloud bounds: {kml_in_bounds}/{len(kml_coords)}\")"]}, {"cell_type": "code", "execution_count": 26, "id": "200d46b6", "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 1000x800 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "Coordinate system validation complete.\n"]}], "source": ["# Simple visualization\n", "fig, ax = plt.subplots(figsize=(10, 8))\n", "sample_points = points[::1000]  # Subsample for visualization\n", "ax.scatter(sample_points[:, 0], sample_points[:, 1], s=0.1, alpha=0.3, label='Point Cloud')\n", "ax.scatter(ifc_coords[::10, 0], ifc_coords[::10, 1], s=1, alpha=0.7, label='IFC')\n", "ax.scatter(kml_coords[:, 0], kml_coords[:, 1], s=2, alpha=0.8, label='KML')\n", "ax.legend()\n", "ax.set_title(\"Spatial Alignment Check\")\n", "plt.show()\n", "\n", "print(\"\\nCoordinate system validation complete.\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 5}