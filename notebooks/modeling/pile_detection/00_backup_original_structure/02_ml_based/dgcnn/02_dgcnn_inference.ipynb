{"cells": [{"cell_type": "markdown", "id": "4e86c2a3", "metadata": {}, "source": ["# DGCNN Site Inference Pipeline\n", "This notebook applies the trained PointNet++ model to a new construction site\n", "with different data sources (DWG files instead of KML regions).\n", "\n", "**Workflow:**\n", "1. Simple exploratory pipeline to test DGCNN on multiple sites.\n", "2. Tests our trained DGCNN model on 3 different construction sites.\n", "\n", "**Author**: <PERSON><PERSON><PERSON>  \n", "**Date**: July 2025  \n", "**Project**: As-Built Foundation Analysis - Site Transfer"]}, {"cell_type": "code", "execution_count": 1, "id": "parameters", "metadata": {"tags": ["parameters"]}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["DGCNN Multi-Site Inference Pipeline\n", "Site Name: northan_res\n"]}], "source": ["# Model and processing parameters\n", "SITE_NAME = \"northan_res\"  # Will be overridden by papermill\n", "POINT_CLOUD_PATH = \"../../../../../data/raw/nortan_res/pointcloud/Block_11_2m.las\"  # Will be overridden\n", "\n", "RUN_NAME = f\"inference_{SITE_NAME}\"\n", "OUTPUT_DIR = \"output_runs/dgcnn_inference\"\n", "\n", "MODEL_PATH = \"best_dgcnn_fixed.pth\"\n", "CONFIDENCE_THRESHOLD = 0.95\n", "BATCH_SIZE = 16\n", "GRID_SPACING = 5.0\n", "PATCH_SIZE = 3.0\n", "NUM_POINTS = 256\n", "K_NEIGHBORS = 20\n", "\n", "EXPERIMENT_NAME = \"dgcnn_inference\"\n", "\n", "# MLflow configuration\n", "\n", "print(\"DGCNN Multi-Site Inference Pipeline\")\n", "print(f\"Site Name: {SITE_NAME}\")"]}, {"cell_type": "code", "execution_count": 2, "id": "6b291543", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Using device: cpu\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages/torchvision/io/image.py:14: UserWarning: Failed to load image Python extension: 'dlopen(/Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages/torchvision/image.so, 0x0006): Library not loaded: @rpath/libjpeg.9.dylib\n", "  Referenced from: <EB3FF92A-5EB1-3EE8-AF8B-5923C1265422> /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages/torchvision/image.so\n", "  Reason: tried: '/Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages/torchvision/../../../libjpeg.9.dylib' (no such file), '/Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages/torchvision/../../../libjpeg.9.dylib' (no such file), '/Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/lib-dynload/../../libjpeg.9.dylib' (no such file), '/Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/bin/../lib/libjpeg.9.dylib' (no such file)'If you don't plan on using image functionality from `torchvision.io`, you can ignore this warning. Otherwise, there might be something wrong with your environment. Did you have `libjpeg` or `libpng` installed before building `torchvision` from source?\n", "  warn(\n"]}], "source": ["import torch\n", "import torch.nn as nn\n", "import torch.nn.functional as F\n", "import numpy as np\n", "import pandas as pd\n", "from pathlib import Path\n", "import matplotlib.pyplot as plt\n", "import mlflow\n", "import mlflow.pytorch\n", "import os\n", "from datetime import datetime\n", "import warnings\n", "warnings.filterwarnings('ignore')\n", "\n", "device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')\n", "print(f\"Using device: {device}\")"]}, {"cell_type": "code", "execution_count": 3, "id": "setup_output_dir", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Output directory created: output_runs/dgcnn_inference\n", "MLflow experiment initialized\n"]}], "source": ["# Setup output directory and initialize MLflow\n", "os.makedirs(OUTPUT_DIR, exist_ok=True)\n", "print(f\"Output directory created: {OUTPUT_DIR}\")\n", "\n", "if mlflow.active_run() is not None:\n", "    print(f\"Ending previous active run: {mlflow.active_run().info.run_id}\")\n", "    mlflow.end_run()\n", "\n", "mlflow.set_experiment(EXPERIMENT_NAME)\n", "mlflow.start_run(run_name=RUN_NAME)\n", "\n", "# Log parameters\n", "mlflow.log_param(\"site_name\", SITE_NAME)\n", "mlflow.log_param(\"model_type\", \"DGCNN\")\n", "mlflow.log_param(\"patch_size\", PATCH_SIZE)\n", "mlflow.log_param(\"num_points\", NUM_POINTS)\n", "mlflow.log_param(\"k_neighbors\", K_NEIGHBORS)\n", "mlflow.log_param(\"confidence_threshold\", CONFIDENCE_THRESHOLD)\n", "mlflow.log_param(\"batch_size\", BATCH_SIZE)\n", "mlflow.log_param(\"grid_spacing\", GRID_SPACING)\n", "mlflow.log_param(\"model_path\", MODEL_PATH)\n", "\n", "print(\"MLflow experiment initialized\")"]}, {"cell_type": "markdown", "id": "085ade32", "metadata": {}, "source": ["## DGCNN Architecture (same as training)"]}, {"cell_type": "code", "execution_count": 4, "id": "f547b445", "metadata": {}, "outputs": [], "source": ["def knn(x, k):\n", "    \"\"\"Find k nearest neighbors\"\"\"\n", "    inner = -2 * torch.matmul(x.transpose(2, 1), x)\n", "    xx = torch.sum(x**2, dim=1, keepdim=True)\n", "    pairwise_distance = -xx - inner - xx.transpose(2, 1)\n", "    idx = pairwise_distance.topk(k=k, dim=-1)[1]\n", "    return idx\n", "\n", "def get_graph_feature(x, k=20, idx=None):\n", "    \"\"\"Create graph features from k-NN\"\"\"\n", "    batch_size = x.size(0)\n", "    num_points = x.size(2)\n", "    x = x.view(batch_size, -1, num_points)\n", "    \n", "    if idx is None:\n", "        idx = knn(x, k=k)\n", "    \n", "    device = x.device\n", "    idx_base = torch.arange(0, batch_size, device=device).view(-1, 1, 1) * num_points\n", "    idx = idx + idx_base\n", "    idx = idx.view(-1)\n", "    \n", "    _, num_dims, _ = x.size()\n", "    x = x.transpose(2, 1).contiguous()\n", "    feature = x.view(batch_size * num_points, -1)[idx, :]\n", "    feature = feature.view(batch_size, num_points, k, num_dims)\n", "    x = x.view(batch_size, num_points, 1, num_dims).repeat(1, 1, k, 1)\n", "    \n", "    feature = torch.cat((feature - x, x), dim=3).permute(0, 3, 1, 2).contiguous()\n", "    return feature"]}, {"cell_type": "code", "execution_count": 5, "id": "bf8dd055", "metadata": {}, "outputs": [], "source": ["class EdgeConv(nn.Module):\n", "    def __init__(self, in_channels, out_channels, k=20):\n", "        super(<PERSON><PERSON><PERSON><PERSON>, self).__init__()\n", "        self.k = k\n", "        self.conv = nn.Sequential(\n", "            nn.Conv2d(in_channels * 2, out_channels, kernel_size=1, bias=False),\n", "            nn.BatchNorm2d(out_channels),\n", "            nn.LeakyReLU(negative_slope=0.2)\n", "        )\n", "    \n", "    def forward(self, x):\n", "        x = get_graph_feature(x, k=self.k)\n", "        x = self.conv(x)\n", "        x = x.max(dim=-1, keepdim=False)[0]\n", "        return x"]}, {"cell_type": "code", "execution_count": 6, "id": "b2b14a92", "metadata": {}, "outputs": [], "source": ["class DGCNN(nn.Module):\n", "    \"\"\"Fixed DGCNN with 20 feature input - exact copy from training\"\"\"\n", "    def __init__(self, num_classes=2, in_channels=20, k=20, dropout=0.3):\n", "        super(DGC<PERSON><PERSON>, self).__init__()\n", "        self.k = k\n", "\n", "        # Split input: 3D coords + features\n", "        self.coord_conv1 = EdgeConv(3, 64, k)\n", "        self.coord_conv2 = EdgeConv(64, 64, k)\n", "        self.coord_conv3 = EdgeConv(64, 128, k)\n", "\n", "        # Feature processing\n", "        self.feature_conv = nn.Sequential(\n", "            nn.Conv1d(in_channels - 3, 64, 1),\n", "            nn.<PERSON><PERSON><PERSON>orm1d(64),\n", "            nn.LeakyReLU(negative_slope=0.2)\n", "        )\n", "\n", "        # Combined processing\n", "        self.conv4 = EdgeConv(128 + 64, 256, k)\n", "\n", "        # Global feature aggregation\n", "        self.conv5 = nn.Sequential(\n", "            nn.Conv1d(64 + 64 + 128 + 256, 1024, kernel_size=1, bias=False),\n", "            nn.<PERSON>ch<PERSON>orm1d(1024),\n", "            nn.LeakyReLU(negative_slope=0.2)\n", "        )\n", "\n", "        # Classification head - simplified\n", "        self.classifier = nn.Sequential(\n", "            nn.<PERSON>(1024, 512),\n", "            nn.BatchNorm1d(512),\n", "            nn.LeakyReLU(negative_slope=0.2),\n", "            nn.Dropout(dropout),\n", "            nn.<PERSON><PERSON>(512, 256),\n", "            nn.BatchNorm1d(256),\n", "            nn.LeakyReLU(negative_slope=0.2),\n", "            nn.Dropout(dropout),\n", "            nn.Linear(256, num_classes)\n", "        )\n", "\n", "    def forward(self, x):\n", "        batch_size = x.size(0)\n", "\n", "        # Split coordinates and features\n", "        coords = x[:, :, :3].transpose(2, 1)  # (B, 3, N)\n", "        features = x[:, :, 3:].transpose(2, 1)  # (B, 17, N)\n", "\n", "        # Process coordinates with EdgeConv\n", "        x1 = self.coord_conv1(coords)  # (B, 64, N)\n", "        x2 = self.coord_conv2(x1)      # (B, 64, N)\n", "        x3 = self.coord_conv3(x2)      # (B, 128, N)\n", "\n", "        # Process features\n", "        feat = self.feature_conv(features)  # (B, 64, N)\n", "\n", "        # Combine and process\n", "        combined = torch.cat([x3, feat], dim=1)  # (B, 192, N)\n", "        x4 = self.conv4(combined)  # (B, 256, N)\n", "\n", "        # Concatenate all features\n", "        x = torch.cat((x1, x2, x3, x4), dim=1)  # (B, 512, N)\n", "\n", "        # Global feature extraction\n", "        x = self.conv5(x)  # (B, 1024, N)\n", "        x = F.adaptive_max_pool1d(x, 1).view(batch_size, -1)  # (B, 1024)\n", "\n", "        # Classification\n", "        x = self.classifier(x)\n", "        return x"]}, {"cell_type": "markdown", "id": "6e12b384", "metadata": {}, "source": ["## Load Data and DGCNN Model\n"]}, {"cell_type": "code", "execution_count": 7, "id": "1035c7b5", "metadata": {}, "outputs": [], "source": ["def load_point_cloud(file_path):\n", "    \"\"\"Load point cloud from LAS or PLY files\"\"\"\n", "    file_path = Path(file_path)\n", "    \n", "    if not file_path.exists():\n", "        print(f\"File not found: {file_path}\")\n", "        return None\n", "    \n", "    try:\n", "        if file_path.suffix.lower() == '.las':\n", "            import laspy\n", "            las_file = laspy.read(file_path)\n", "            points = np.vstack([las_file.x, las_file.y, las_file.z]).T\n", "            print(f\"Loaded {len(points):,} points from LAS\")\n", "            return points\n", "        elif file_path.suffix.lower() == '.ply':\n", "            import open3d as o3d\n", "            pcd = o3d.io.read_point_cloud(str(file_path))\n", "            points = np.asarray(pcd.points)\n", "            print(f\"Loaded {len(points):,} points from PLY\")\n", "            return points\n", "        else:\n", "            print(f\"Unsupported format: {file_path.suffix}\")\n", "            return None\n", "    except Exception as e:\n", "        print(f\"Error loading {file_path}: {e}\")\n", "        return None"]}, {"cell_type": "code", "execution_count": 8, "id": "0767778f", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Loaded 35,565,352 points from LAS\n", "Point cloud bounds:\n", "  X: 385723.95 to 385809.63\n", "  Y: 3529182.83 to 3529447.01\n", "  Z: 553.38 to 556.27\n"]}], "source": ["# Load point cloud for current site\n", "point_cloud = load_point_cloud(POINT_CLOUD_PATH)\n", "\n", "if point_cloud is None:\n", "    print(f\"Generating synthetic data for {SITE_NAME}\")\n", "    np.random.seed(hash(SITE_NAME) % 2**32)\n", "    point_cloud = np.random.randn(5000, 3) * 20\n", "\n", "print(f\"Point cloud bounds:\")\n", "print(f\"  X: {point_cloud[:, 0].min():.2f} to {point_cloud[:, 0].max():.2f}\")\n", "print(f\"  Y: {point_cloud[:, 1].min():.2f} to {point_cloud[:, 1].max():.2f}\")\n", "print(f\"  Z: {point_cloud[:, 2].min():.2f} to {point_cloud[:, 2].max():.2f}\")\n", "\n", "# Log point cloud metrics to MLflow\n", "mlflow.log_metric(\"point_cloud_size\", len(point_cloud))\n", "mlflow.log_metric(\"x_range\", point_cloud[:, 0].max() - point_cloud[:, 0].min())\n", "mlflow.log_metric(\"y_range\", point_cloud[:, 1].max() - point_cloud[:, 1].min())\n", "mlflow.log_metric(\"z_range\", point_cloud[:, 2].max() - point_cloud[:, 2].min())\n"]}, {"cell_type": "code", "execution_count": 9, "id": "61fee98e", "metadata": {}, "outputs": [], "source": ["def create_analysis_grid(point_cloud, grid_spacing=5.0):\n", "    \"\"\"Create regular grid of analysis points\"\"\"\n", "    x_min, x_max = point_cloud[:, 0].min(), point_cloud[:, 0].max()\n", "    y_min, y_max = point_cloud[:, 1].min(), point_cloud[:, 1].max()\n", "    \n", "    padding = grid_spacing * 0.5\n", "    x_coords = np.arange(x_min - padding, x_max + padding, grid_spacing)\n", "    y_coords = np.arange(y_min - padding, y_max + padding, grid_spacing)\n", "    \n", "    return np.array([[x, y] for x in x_coords for y in y_coords])"]}, {"cell_type": "code", "execution_count": 10, "id": "6de0bd17", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Limiting to 1000 grid points for testing\n", "Created 1000 analysis locations\n"]}], "source": ["# Create analysis grid\n", "grid_points = create_analysis_grid(point_cloud, GRID_SPACING)\n", "\n", "# Limit grid size for testing\n", "MAX_GRID_POINTS = 1000\n", "if len(grid_points) > MAX_GRID_POINTS:\n", "    print(f\"Limiting to {MAX_GRID_POINTS} grid points for testing\")\n", "    grid_points = grid_points[:MAX_GRID_POINTS]\n", "\n", "print(f\"Created {len(grid_points)} analysis locations\")\n", "\n", "# Log grid metrics to MLflow\n", "mlflow.log_metric(\"analysis_grid_points\", len(grid_points))"]}, {"cell_type": "code", "execution_count": 11, "id": "3fb10d9f", "metadata": {}, "outputs": [], "source": ["def extract_patch_around_point(point_cloud, center_xy, radius=3.0, num_points=256):\n", "    \"\"\"Extract patch with 20-channel features - exact same method as training\"\"\"\n", "    center_x, center_y = center_xy\n", "    \n", "    # Calculate distances to center (XY only)\n", "    distances = np.sqrt((point_cloud[:, 0] - center_x)**2 + \n", "                       (point_cloud[:, 1] - center_y)**2)\n", "    \n", "    # Select points within radius\n", "    mask = distances <= radius\n", "    patch_xyz = point_cloud[mask]\n", "    \n", "    if len(patch_xyz) < 10:\n", "        return None\n", "    \n", "    # Calculate patch statistics for feature engineering\n", "    patch_center = np.mean(patch_xyz, axis=0)\n", "    z_min, z_max = patch_xyz[:, 2].min(), patch_xyz[:, 2].max()\n", "    z_mean, z_std = patch_xyz[:, 2].mean(), patch_xyz[:, 2].std()\n", "    if z_std == 0:\n", "        z_std = 1e-6\n", "    \n", "    # Calculate distance to center for each point\n", "    dist_to_center = np.sqrt((patch_xyz[:, 0] - center_x)**2 + (patch_xyz[:, 1] - center_y)**2)\n", "    \n", "    # Generate 20-channel features for each point\n", "    features_list = []\n", "    for i, (px, py, pz) in enumerate(patch_xyz):\n", "        # Match the exact feature engineering from training data preparation\n", "        feature_vector = [\n", "            px,  # 0: raw X coordinate\n", "            py - center_y,  # 1: relative Y (small values like training)\n", "            pz,  # 2: raw Z coordinate  \n", "            (pz - z_mean) / z_std,  # 3: height_norm\n", "            dist_to_center[i],  # 4: distance_norm\n", "            len(patch_xyz) / 1000.0,  # 5: density\n", "            px - center_x,  # 6: relative_x\n", "            py - center_y,  # 7: relative_y  \n", "            pz - patch_center[2],  # 8: relative_z\n", "            pz - z_min,  # 9: height_above_min\n", "            z_max - pz,  # 10: depth_below_max\n", "            abs(pz - z_mean),  # 11: abs_height_deviation\n", "            np.sqrt(px**2 + py**2),  # 12: distance_from_origin\n", "            np.arctan2(py - center_y, px - center_x),  # 13: angle\n", "            (px - center_x) * (py - center_y),  # 14: interaction\n", "            px - patch_center[0],  # 15: patch_relative_x\n", "            py - patch_center[1],  # 16: patch_relative_y\n", "            pz / (dist_to_center[i] + 1e-6),  # 17: height_distance_ratio\n", "            np.sqrt((px - center_x)**2 + (py - center_y)**2 + (pz - z_mean)**2),  # 18: 3d_distance\n", "            dist_to_center[i] + np.random.normal(0, 0.01)  # 19: distance with slight variation\n", "        ]\n", "        features_list.append(feature_vector)\n", "    \n", "    patch_features = np.array(features_list, dtype=np.float32)\n", "    n = len(patch_features)\n", "    \n", "    # Sample to fixed size\n", "    if n >= num_points:\n", "        indices = np.random.choice(n, num_points, replace=False)\n", "        patch_fixed = patch_features[indices]\n", "    else:\n", "        # Pad with jittered copies\n", "        extra_indices = np.random.choice(n, num_points - n, replace=True)\n", "        extra = patch_features[extra_indices].copy()\n", "        # Add small noise to coordinates only (first 3 features)\n", "        extra[:, :3] += np.random.normal(0, 0.01, (len(extra), 3))\n", "        patch_fixed = np.vstack([patch_features, extra])\n", "    \n", "    # Normalize spatial coordinates only (first 3 features)\n", "    spatial_coords = patch_fixed[:, :3]\n", "    max_dist = np.max(np.linalg.norm(spatial_coords, axis=1))\n", "    if max_dist > 0:\n", "        patch_fixed[:, :3] /= max_dist\n", "    \n", "    return patch_fixed.astype(np.float32)"]}, {"cell_type": "code", "execution_count": null, "id": "model_validation", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Loaded DGCNN model from best_dgcnn.pth\n", "Model has 1,276,034 parameters\n", "Testing model with dummy data...\n", "Dummy test - Output shape: torch.Size([1, 2])\n", "Dummy test - Probabilities: [0.03399833 0.9660017 ]\n", "⚠ Model output might be problematic: pile_prob=0.9660\n"]}], "source": ["# Load trained DGCNN model with validation\n", "try:\n", "    model = DGCNN(num_classes=2, in_channels=20, k=K_NEIGHBORS, dropout=0.3).to(device)\n", "    model.load_state_dict(torch.load(MODEL_PATH, map_location=device))\n", "    model.eval()\n", "    print(f\"Loaded DGCNN model from {MODEL_PATH}\")\n", "    \n", "    # Log model info\n", "    param_count = sum(p.numel() for p in model.parameters())\n", "    print(f\"Model has {param_count:,} parameters\")\n", "    \n", "    # Test model with dummy data to verify it works\n", "    print(\"Testing model with dummy data...\")\n", "    dummy_input = torch.randn(1, 256, 20).to(device)\n", "    with torch.no_grad():\n", "        dummy_output = model(dummy_input)\n", "        dummy_probs = torch.softmax(dummy_output, dim=1)\n", "        print(f\"Dummy test - Output shape: {dummy_output.shape}\")\n", "        print(f\"Dummy test - Probabilities: {dummy_probs[0].cpu().numpy()}\")\n", "        \n", "        # Check if probabilities are reasonable\n", "        pile_prob = dummy_probs[0, 1].item()\n", "        if pile_prob > 0.1 and pile_prob < 0.9:\n", "            print(\"Model producing reasonable probability ranges\")\n", "        else:\n", "            print(f\"Model output might be problematic: pile_prob={pile_prob:.4f}\")\n", "    \n", "    MODEL_LOADED = True\n", "    \n", "except Exception as e:\n", "    print(f\"Error loading model: {e}\")\n", "    print(\"Creating untrained model for testing...\")\n", "    model = DGCNN(num_classes=2, in_channels=20, k=K_NEIGHBORS, dropout=0.3).to(device)\n", "    model.eval()\n", "    MODEL_LOADED = False"]}, {"cell_type": "markdown", "id": "6b0a249e", "metadata": {}, "source": ["## Process site with DGCNN\n"]}, {"cell_type": "code", "execution_count": 13, "id": "a5f0d927", "metadata": {}, "outputs": [], "source": ["def process_site_dgcnn(point_cloud, grid_points, model, device, batch_size=16):\n", "    \"\"\"Process site with DGCNN model\"\"\"\n", "    results = []\n", "    \n", "    for i in range(0, len(grid_points), batch_size):\n", "        batch_centers = grid_points[i:i+batch_size]\n", "        batch_patches = []\n", "        valid_indices = []\n", "        \n", "        # Extract valid patches\n", "        for j, center in enumerate(batch_centers):\n", "            patch = extract_patch_around_point(point_cloud, center, radius=PATCH_SIZE, num_points=NUM_POINTS)\n", "\n", "            if patch is not None:\n", "                batch_patches.append(patch)\n", "                valid_indices.append(i + j)\n", "        \n", "        if len(batch_patches) == 0:\n", "            continue\n", "        \n", "        # Run DGCNN inference\n", "        batch_tensor = torch.FloatTensor(np.array(batch_patches)).to(device)\n", "        \n", "        with torch.no_grad():\n", "            outputs = model(batch_tensor)\n", "            probabilities = torch.softmax(outputs, dim=1)\n", "            pile_probs = probabilities[:, 1].cpu().numpy()\n", "        \n", "        # Store results\n", "        for j, (idx, prob) in enumerate(zip(valid_indices, pile_probs)):\n", "            center = grid_points[idx]\n", "            results.append({\n", "                'x': center[0],\n", "                'y': center[1],\n", "                'pile_probability': prob,\n", "                'prediction': 'PILE' if prob > CONFIDENCE_THRESHOLD else 'NON-PILE'\n", "            })\n", "    \n", "    return results\n", "\n"]}, {"cell_type": "code", "execution_count": 14, "id": "8444b060", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Running DGCNN inference on 1000 locations...\n", "DGCNN Results for northan_res:\n", "  Total locations analyzed: 880\n", "  Pile detections: 130 (14.8%)\n", "  Average confidence: 0.210\n", "  High confidence (>0.9): 146\n"]}], "source": ["# Process site with DGCNN\n", "print(f\"Running DGCNN inference on {len(grid_points)} locations...\")\n", "\n", "results = process_site_dgcnn(point_cloud, grid_points, model, device, BATCH_SIZE)\n", "\n", "if results:\n", "    results_df = pd.DataFrame(results)\n", "    pile_count = len(results_df[results_df['prediction'] == 'PILE'])\n", "    avg_confidence = results_df['pile_probability'].mean()\n", "    \n", "    print(f\"DGCNN Results for {SITE_NAME}:\")\n", "    print(f\"  Total locations analyzed: {len(results_df)}\")\n", "    print(f\"  Pile detections: {pile_count} ({pile_count/len(results_df)*100:.1f}%)\")\n", "    print(f\"  Average confidence: {avg_confidence:.3f}\")\n", "    print(f\"  High confidence (>0.9): {sum(results_df['pile_probability'] > 0.9)}\")\n", "    \n", "    # Log results to MLflow\n", "    mlflow.log_metric(\"total_locations\", len(results_df))\n", "    mlflow.log_metric(\"pile_detections\", pile_count)\n", "    mlflow.log_metric(\"detection_rate\", pile_count/len(results_df))\n", "    mlflow.log_metric(\"avg_confidence\", avg_confidence)\n", "    mlflow.log_metric(\"high_confidence_count\", sum(results_df['pile_probability'] > 0.9))\n", "else:\n", "    print(f\"No valid results for {SITE_NAME}\")\n", "    results_df = pd.DataFrame()\n", "    pile_count = 0\n", "    avg_confidence = 0.0\n"]}, {"cell_type": "markdown", "id": "c4f86d3f", "metadata": {}, "source": ["## Visualization\n"]}, {"cell_type": "code", "execution_count": 15, "id": "a9c7014e", "metadata": {}, "outputs": [{"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAA6oAAAMWCAYAAAD4dHsQAAAAOnRFWHRTb2Z0d2FyZQBNYXRwbG90bGliIHZlcnNpb24zLjEwLjMsIGh0dHBzOi8vbWF0cGxvdGxpYi5vcmcvZiW1igAAAAlwSFlzAAAPYQAAD2EBqD+naQABAABJREFUeJzsnQV429f1/l/ZkiwzM8VhZuYmbQppu668dStsK29t163bOvxv+43arszMKzOkacPMzI6ZGWRbFlj/5z2OXNmWA23sqzT300eNLcnW8f3e77n33EMGt9vthkaj0Wg0Go1Go9FoNH5CgGoBNBqNRqPRaDQajUaj8UYbqhqNRqPRaDQajUaj8Su0oarRaDQajUaj0Wg0Gr9CG6oajUaj0Wg0Go1Go/ErtKGq0Wg0Go1Go9FoNBq/QhuqGo1Go9FoNBqNRqPxK7ShqtFoNBqNRqPRaDQav0IbqhqNRqPRaDQajUaj8Su0oarRaDQajUaj0Wg0Gr9CG6oajUbjp+Tl5cFgMODFF1/seO7//b//J89pjo9+/frh2muvVS3GKQHnGefW5s2bVYui0Wg0Go02VDUaTd9ugj0Pi8WClJQUnH322Xj44YfR2NjY48/u3LkT1113HbKysuTnwsLCMHbsWPzmN79BTk6Oz59Zvnw5Lr74YiQlJcFsNiMhIQEXXHAB3nvvvW6GIB/vvvtut9/hMQqrqqo6nqPRw+dGjx4Nt9vd7Wf42s9//vPjMqC8x4PyzZo1C++//z5OletyLPbu3StjyHHuTdauXSufU1dX16uf813h8ccf73T4odFoNBqNP6INVY1G06f87W9/wyuvvIInnngCv/jFL+S5O+64A6NGjRKDtCvPPPMMxo8fj88//1wMz0ceeQT33nsvZsyYgZdffhlDhw6Fy+Xq9DN/+ctfcMYZZ2D37t248cYb8eSTT+Kuu+6C1WrFJZdcgtdff92nXL4Mz57YtWtXJ6P3m0Bjm2PBx69//WuUlJTI30h5SWZmJlpaWvDjH/8Y/nZdjtdQ/etf/9onhio/x5eheuDAAZlDmq/RhqpGo9FoTgWMqgXQaDSnF+eeey4mTpzY8f3dd9+NpUuX4vzzz8eFF16Iffv2ITg4uMMAufnmm8Uo/eSTTxAeHt7pd/33v//FP/7xj07PvfPOO2J0XXrppWKQmkymjtdorH7xxRdwOBzdDMbt27eLN5OG4rGgfOnp6fI5fP83DcVNTU3Fj370o47vr776agwcOBAPPPAAbrrppg4Pp79dl1OJoKAg1SL4Dc3NzQgJCcF3BR4s2Wy2U3JeajQajebYaI+qRqNRzrx58/CnP/0J+fn5ePXVVzuep5eMxtprr73WzUglNOL+/ve/IzAwsOM5/p6YmBg8//zznYxUDwxppfHlzZVXXonBgwcft1c1ICAAf/zjH8XTeDJDdRmmPGzYMOTm5vaYo9oTHLcJEybIpp1/P/+mwsLCXrkuZP/+/XIYwM/idaCR+9FHH3W8Tpkvu+wy+ZrebU9oMUOyPdBLznDn0NBQub4LFy7Enj17usnBz7r88ssRHx8vf9+QIUPwhz/8QV5jyC8PIAhDwz2f4/Hi+spRZbg4ZaPsNNymTp2KTz/9tNN7KCd/z1tvvSWHIWlpafJ3zp8/H9nZ2eiN8Os1a9bgzjvvlL+TY/L9738flZWVPj2iI0aMECOcYdq33nprN2/y3LlzMXLkSGzZsgWzZ8+Wv/P3v/+9jAfHeMWKFR1jxfd609raekw5PvzwQ7le/HzKMWDAALkXu0Y3eOSgd53zgHLwgOaee+454XGi7Lx3edjE+ca58NRTT8lr/PsZAcADJMrDA5///Oc/aGtr6/Q73njjDblPON8iIiIkYuChhx46YVk0Go1G0/toQ1Wj0fgFnvDWxYsXd3h/6NHjRpdGwvFw6NAhMWouuugin4ZtT9DQpeG5Y8eO4zY8f/jDH2LQoEEnHDJ8NOjppXEZGxt7Qj9HQ4reWMpz//33y4Z9yZIlYqB827zNrteF0NChcUcv6+9+9zvxbNOg4bh7xo+ffdttt8nXNJA8Ic40xAm/pqHDfGMaFDSIaczMnDmzU6gwDwOmTJkic+H6668Xo4Kf8/HHH8vr9Gj/4Ac/kK/pifZ8Do0sX5SXl2P69Oli7Nxyyy0ydvTK0Wvs69r/+9//lucZmk0v8/r163HVVVehN2DINecgQ9cZScC/sWu+Mw1zGqY0EDnuDGWnsbZgwYJukQLV1dXiKWfEwIMPPiiGIv/l/cSQec9YeYz+E5GDxjWvHQ1aXhMaf3/+859lPnSltrYW55xzDsaMGSMy87N/+9vfykHFicJQbl7vs846Sz6Xfxt1xZw5c+QwhfcBc6sZhcHrRfk8fPnll/Kz0dHRMud4balfeECg0Wg0Gj/ErdFoNH3ACy+8QGvOvWnTph7fExkZ6R43bpx8vWPHDnn/HXfc0e191dXV7srKyo5Ha2urPP/hhx/KzzzwwAPHJVNubq68/95773U7nU73oEGD3GPGjHG3tbXJ63/5y1/kdX6Gh2uuucYdGhoqX7/00kvy+nvvvdfxOr+/9dZbj/nZmZmZ7gULFnT8Dfx7r7zySvn5X/ziF53k49h58MjkIS8vzx0YGOj+xz/+0en379q1y200Grs9/22vC5k/f7571KhRbpvN1vEcx2z69Okyhh7efvtt+d3Lli3r9PsaGxvdUVFR7uuvv77T82VlZfJZ3s/Pnj3bHR4e7s7Pz+/0Xs81Irx+/ByOl69x5jXzwPnE965ataqTPFlZWe5+/fq5XS6XPEeZ+b5hw4Z1zC/y0EMPyfMc35OF5xqceeaZnf6uX/7yl3Jt6+rq5PuKigq32WyWeeORkzz66KPy888//3zHc3PmzJHnnnzyyW6fN2LECHn9m8pBmpubu/38jTfe6A4JCek0LzxyvPzyyx3PcTyTkpLcl1xyyQmNE68lf9eiRYs6Pf/3v/9d7smDBw92ev53v/udyF1QUCDf33777e6IiAi51zUajUbj/2iPqkaj8RvoofFUmW1oaOh4riv9+/cXj5nn4Qk59fzMiXhTfXlVP/jgg+P6GXrWvo1XlV5Kz99Ab9Pbb78tHkx6e44XFnRieCNDY1md2PNgGDFlW7ZsGU7mdampqRHvJj+Pz3k+j947hlXTq11cXHzU30fPFj299G55y8xrQO+pR2aGm65cuRI/+clPkJGR0el3fNO84M8++wyTJ08Wz63333fDDTeIJ5deXW9YbZpVoz0wVJn0VG3620AZvP8ufhZDaRl6Tb766ivY7XbxmDP83AM9zQxj7Rq+zBBYyn+y5SDeeaGeecD30bvJqAZvOL7eudgcT16DbzKGDO/mPPOG9w0/m55S7/l05plnitycQyQqKgpNTU0y/zQajUbj/+hiShqNxm9gVV62afE2NvlcV5gfxzBHGpUMyfTAzTr5pi1VaHgyz46GJ8NLj9e4veaaa8S4ZS7fiUCj7P/+7//EKGDuHsNiuZk+EWgY0kimUeoLX3m63+a6MD+Tn8dQXT58UVFRIXmIR5PZkwPrC8919BgyzHE8WdDY4rh3xROSzNe9P6+rgUxjyBPO2hM0JmnQe8PDCO9cal8c67M8hiJzdL2h4cfDG29DkvAaeBvZx8vx/M0M/+bc56GF54DIQ319fafvGWrc9WCBv/ObVJOmoeprPvF39RTuzflIGOrNnGOGQ3NsGC7NAxeGJWs0Go3G/9CGqkaj8QuKiopkg8siKIT/Go1GaTHTFeajEb7uDXPfPK1jvgkew5PFd2gM94Zx601cXJx4fb4N9KbSCGC+ny9DyJdH+ttcF09xGh4QdPVsefC892gyE+ZH0vPbla7XVSU9GZdH86CzWjXzQb1hgSwWAzrZn3U0vmk13GPJQW8470EeKHDes5ASC01t3bpVck+7FjA6mX+Xr7+Jn8ecVfZV9gULpREetrC6N/OTeb/w8cILL0he60svvXTCsmg0Go2md/Gf3YBGozmtodFCPMYPi/Ow0AmrkzKU9GgeOu8NKb1NNDJZaOWbGGkMUaSXkxWHWWCnN4zbkwmNBG746WnybMh787rQc+fx1B7LyO4pPJcyewyHo/0Oz2f5Oqw4ns/xBXvTsiBPVzzhqnz928Iw7q7hpb4M8hPFIxvl94yNx4NLQ/h4Dz2+adi0d0Vkhnoz7JxFszx4qlX3NZxP9Pofz99PD/MFF1wgDxq49LKyGBWjA451wKLRaDSavkXnqGo0GuUwfJBeSRpb3hVVWUWUOWY0Hn2FAPvyyNDA5Cb6Zz/7GZxOp8+8UPZkPZbhSc+Ld7uVo0H5uMnlZ/c1rHpLmfnZXceD33MsTuZ1oXHJAwRu7ktLS7v9jHcbEx42kK6Vh2n00hv3z3/+s1ulWu/fwVBOGkJsNVRQUNDtbzvW5/jivPPOw8aNG7Fu3bqO55i3+PTTT4vHc/jw4fi2MKyVRpP342T0w+XvoaHFqrbef/9zzz0nXm9WUT4eOF7fphq0x0PqLQONZbbNUQHDd3k96SntCv9Ojx7oei8wz3f06NEdLXk0Go1G419oj6pGo+lTGG5H7xU3j2wVQmOI3id6i2gYem/oWSDl0UcflXYZzMGkscTwXm6KDx48KP1VuXH39lZdccUVEvrLtiPbtm2Tgj383dykLlq0SNq2vP7668cVzktj9Xg37mzx8U0K15wMbxI9wGzFwWJAntY89G6xrQoL43jn8Z6M6/LYY49JMSL2oGQhH3r3+DM0FhgqzNxhwtYhHBsWh6IhxeI+zEulsfvEE09I4ajx48dLz1capTRGWRCIrUV43QmNMn4W38e/hUYz/06+z3N92BqF8Brwd9HbS4+Zx4D1hu1T/ve//0meItvnsJcqwz45Xu+++26nIkX+BseI15mHEsyrpMef3lUaiJMmTepUsOhocLw4/pw3PGDh9egpX9gXbO9DY5y52RxDemjpeT9ZbZpOFPbR5Rxlj1VGNvDv4+ED9cA777wj84Vh9jy8Yu4w/1bmzTKn95FHHpF56slR1mg0Go0fobrssEajOT3wtL7wPNhmgy0qzjrrLGn50dDQ0OPPbtu2zX311Ve7MzIy5OfYimL06NHuX/3qV+7s7GyfP7NkyRL39773PXdCQoK0aYmPj3dfcMEF0sLGV3uao8nbU3sabxwOh3vAgAEn1J5m4cKFR33P8bSn8fDuu++6Z86cKbLxMXToUJHjwIEDvXJdDh8+LNeE7zWZTO7U1FT3+eef737nnXc6ve+ZZ55x9+/fX9qEdG1Vw6/PPvtsaUljsVhk/K699lr35s2bO/2O3bt3u7///e9LSxu+b8iQIe4//elP3VqUUIaAgIBOrWq6tqfxyH7ppZd2/L7Jkye7P/nkk07v8bSnYYudY12Tb0tPLYI8MnRt78N2NLy+HPfExET3zTff7K6tre30HraFYRsaX7ANEOce2/7w93ta1ZyIHGvWrHFPnTrVHRwc7E5JSXH/5je/cX/xxRfd3teTHLwmvDYnwtHuGbYYuvvuu90DBw6UORwXFyftku677z633W6X93BusrUPdQLfQ33CljqlpaUnJIdGo9Fo+gYD/6faWNZoNBqNRqPRaDQajcaD/8Y4aTQajUaj0Wg0Go3mtETnqGo0Go1Go1EGi2exaFpPMA+ducQajUajOb3Qob8ajUaj0WiUwWrLLGzUE+zZypY4Go1Gozm90B5VjUaj0Wg0ymD17paWlh5fZ4VhjUaj0Zx+aI+qRqPRaDQajUaj0Wj8Cl1MSaPRaDQajUaj0Wg0foU2VDUajUZzUvIMr7322o7vmVNoMBj8Irfw//2//yeynI7k5eXJ3/7iiy+qFkWj0Wg0mhNCG6oajUbjB1itVvzlL3/BOeecIxVOj2ZcPPPMM1JgJjExEUFBQcjKysJ1110nRokvnnvuOQwbNgwWiwWDBg3CI4880st/zXeH119/HQ8++GCvf87jjz+ujUmNRqPRaLzQxZQ0Go3GD6iqqsLf/vY3ZGRkYMyYMUf1RG7btk2M0wsvvFAKzeTm5orx+sknn2DHjh1ISUnpeO9TTz2Fm266CZdccgnuvPNOrFq1Crfddhuam5vx29/+9qTJf+DAAQQEBHwnDdXdu3fjjjvu6HVDNS4urpNXWqPRaDSa0xltqGo0Go0fkJycjNLSUiQlJWHz5s2YNGnSUY2arlx00UWYOHEiXn75Zfzud7+T51hJ9Q9/+AMWLlyId955R567/vrr0dbWhr///e+44YYbTlpFVXp2NRqNRqPRaE4W373jb41GozkFoaFHI/Xb5IiSurq6jueWLVuG6upq3HLLLZ3ee+utt6KpqQmffvrpceV27t+/H5dffjkiIiIQGxuL22+/HTabrdvnH483cMOGDRLeHBkZiZCQEAlhXrNmTaf3NDY2igeTv5PjkpCQgLPOOgtbt2495u9fvXq1GPkMcx4wYIB4lHvi1VdfxYQJExAcHCzh1ldeeSUKCws7Xp87d66MEXt8chz48IwzaW1tlXDtgQMHipzp6en4zW9+I8/7+qzJkyfL38zDgdmzZ2Px4sUdY7dnzx6sWLGi43P42R54TTke/P38HH7ef/7zHzlw8Ibv4zXg2EZFReGaa67pNB80Go1GozmV0B5VjUajOUWhEepyuVBQUCBhw2T+/PmdQoQJPa3e0DhjmC5f/9GPfnTMz6GRSmPqX//6F9avX4+HH34YtbW14r09EZYuXYpzzz1XPp8GHmV44YUXMG/ePAlJpiFHGKpMD/DPf/5zDB8+XP5OGqD79u3D+PHje/z9u3btwoIFCxAfHy9GttPplM9hLm9X/vGPf+BPf/qT/G0/+9nPUFlZKbm7NCA5LjT06I2ur69HUVERHnjgAfm5sLAw+ZdGIkOvKRc908wB5ufzfQcPHsQHH3zQ8Vl//etfRZ7p06fLdTKbzWKwczwoL3Ngf/GLX8jv5mcSj8wM0aYxX1xcjBtvvFFCw9euXYu7775bPPCe/Fl2mvve974n8nD8KM/7778vxqpGo9FoNKck7KOq0Wg0Gv9h06ZN7G/tfuGFF476vqCgIHkfH7Gxse6HH3640+u33nqrOzAw0OfPxsfHu6+88sqj/v6//OUv8rsvvPDCTs/fcsst8vyOHTs6nsvMzHRfc801Hd8vW7ZM3sN/SVtbm3vQoEHus88+W7720Nzc7M7KynKfddZZHc9FRkaK7CfKRRdd5LZYLO78/PyO5/bu3Stj4L3c5eXlyXP/+Mc/Ov38rl273EajsdPzCxculL+tK6+88oo7ICDAvWrVqk7PP/nkk/JZa9aske8PHTok7/v+97/vdrlcnd7rPQ4jRoxwz5kzp9vn/P3vf3eHhoa6Dx482On53/3ud/I3FBQUyPcffPCBfO4999zT8R6n0+meNWvWcc0ljUaj0Wj8DR36q9FoNKcon3/+OT777DP897//FU8bw3m9YY4qvXe+YGgsXz8eGCrsDb1/hJ99vGzfvh2HDh3CD3/4Q/GQsngUH5SZXuCVK1d2hLLSm0mPY0lJyXH/fnqWv/jiC8nV5Vh4oGfx7LPP7vTe9957Tz6L3lSPHHww9JpVkRkyfSzefvtt+d1Dhw7t9DvoHSae30HPKj/rz3/+c7diU8fTMoefM2vWLAkX9v6cM888U/5mjpvnWhiNRtx8880dPxsYGNhxrTQajUajOdXQhuo3hCFf3Axxs8ciKD/+8Y+PualizpEn/8jzYIiWB1br/MEPfiB5SMyZ4ibooYce6vZ7HnvsMXmN7xkyZMgJh995YO7VlClT5PdwE8QNnkajOXU444wzJJSW1Xxp0DDE9NFHH+14nfe23W73+bPMMeXrxwONN2+Y+0mjq6d2OL6gkUoYisrQXO/Hs88+K3mdDLMl99xzj1TapS5kODDDZnNyco76+xm6S8O7q6yEerKrLAyV5Xu7ysLw4oqKiuP6e5hX2vXnBw8eLK97fsfhw4dlrBjC/E3g5yxatKjb59BQ9f4c5tFyLfKEJvf0t2s0Go1Gc6qgc1SPYViyMIWvAiHcIP7+97+XjQFzh37961/j0ksvldyho8GKm55cMsLCGh62bNkiRUNYdIMbNP4u5j7xVJy5WuSJJ56Q3CS2omDBkI0bN8rvpKF5wQUXHPff9u6778rP/fOf/xQPAHO5uDHUaDSnJjQex40bh9dee61DX1A/0etGY4a6xQONV3o1vdvYnAjH4wnsisdbeu+992Ls2LE+3+MxsujppBeROZYsOMSfYfEgekJpmH9bKAv/BnqkqV97kuNYv2PUqFG4//77fb5OHX4y4OewkBSLNPnCYxhrNBqNRvNdQxuq35Bf/vKXHV9nZmZKOwh6JB0OB0wmU48/R8O0p8qeP/nJTzp9379/f6xbt042Z56N5yuvvCIFNa644oqO92zatEk2cd6GKj0UDAdkf0UWQWHfRE/lTxqlrNrJzd9Pf/rTjp/5pif+Go3GP6BH0bvirMcgZLub8847r+N5fk8DqCeD0ZdXj31bPWRnZ8vPe1fAPR5DmrBysMcbeDRoZFNn8UFDm0WUWACpJ0OVXkZ6iD2e2649XrvKQo8q/6ZjGXo9GeX8HYyCYdjy0Qx3vo9jtXfv3qOO99E+x2q1HnPMuA4tWbJE3uttaHf92zUajUajOVXQob8ngZqaGvFisKLj0YxUwvexqfvIkSPFM8qKjkeDoXBsm+CBm1CGG3vDzRk9qzSSPZ/BfChu6hjGRq8pq1u+9NJL8jpbPNALzHA0emC4IeTmT3tUNRr/hwdNrLjbFeoAVp31rvDLaAnqD0ZieMPveWjG/qrHA9MNvGF1XHIi3k1W+qXRdd9994kx5St0l9AD7AkB9kBvML2/vtq+eKBnlLmozAllFWQP1IHMXfXm4osvlvczVJoGqzf8nt5mD6Ghod3k8Xh9qUcZ3eLrwMCTL8wDTOpaRtJ0bSfj/dn8HF+tZPg5PLDs+jcQvp/zgfAggl97X2uOpedaaTQajUZzqqE9qt+C3/72t5IPRmNz6tSp+OSTT476fhYR4ak3N1w7d+6Un+dpNz2mvmDo75tvvtmp1yE3YvSWcvNDDwPDhfk9jVQW2KDRyXYM9KZyM0boNeBpPvsJMj/Mk+vFvC+GrdErwvcz1JltFbwNY41G03dQn9D48OS7f/zxx9IahbAoDvtj0shjWCmjKkaMGCEGDg1Utnnh6zyU8j7E+vvf/y7FkC677DLRH2wDw/QCHmQd773OyAzm5bP/KY0m/jz12ZgxY477b6OxRl1F45ZyX3fddUhNTRVjj4WH6Gnl38seqmlpaZJKwd9P7+BXX30lkSPUU0eDhifzORk2TE8sDTcaavw86lwPNJj/7//+Tw4LmWdLfRoeHi5/J8ONmXLBdA6PgU09zDxgpltQHkavsC7BW2+9JXUGKP+MGTPEMGTPWT5Pw5KHBux5ypYzvA6Ui3qZvVD593AtYMsfz+fQyKRc/Bka5zxouOuuu/DRRx/h/PPPlzQUvo9GMK85W/hQfh5+UibKwOgePscIGa4tvoxsjUaj0WhOCVSXHfYn2JKAbQA8D7YUYPsH7+e82x5UVla6Dxw44F68eLF7xowZ7vPOO69Tu4FjsWTJEmkbkJ2d3e01tkmIi4uT1gTesJXDddddJy0U2JogJSXF/Zvf/EZ+T1lZmdtqtcrXwcHBneTm35GQkCC/47XXXpP3PPXUUx2/12azyeextYJGo1ED26B42s10feTm5sp7Wltb3bfffrt79OjR7oiICLfJZJKf++lPf9rxnq48/fTT7iFDhrjNZrN7wIAB7gceeOC4dJWnPQ1bvFx66aXu8PBwd3R0tPvnP/+5u6WlpZvsR2tP42Hbtm3uiy++WNrpUC/x5y6//HLRh56/76677nKPGTNGPo/6i18//vjjxzWGK1ascE+YMEH+1v79+4tO8/wdXXn33XfdM2fO7NCTQ4cOlbY41OseqFN/+MMfuqOiouR3eLeqsdvt7v/85z/SWoZ/C8eGn/3Xv/7VXV9f3+mznn/+efe4ceM63sdWNF9++WXH69TfbIXDv5mf492qprGx0X333Xe7Bw4cKH8XdfX06dPd9913n8jgobq62v3jH/9Y5gVb/PBrjrduT6PRaDSaUxED/6faWPanEF4+PFx11VW45JJLOjyThN5HtgDoCr0engJI06ZNO67P46k4T+fpAfBun0DvJ4s1sQk9vR6+oAe1vLxcPKhPP/20eGfpiWH4HHNg6fFgRV9vGOpG7ypP/3lST8/KzJkzO17n+5kH1dNnajSa0wtGXdBLSb1Cr51Go9FoNBpNX6FDf71gGJx3KBzD5hh+xTCsY+HJPTpaDpWvvoKExqYHtjugEckQ3aMZjMyFZXgceeONNyQsjKF1iYmJEk7G8F4a2r5g6BhDzxh27DFUafgyXIyhyRqNRqPRaDQajUajEm2ofgPYiJ75RTTy2BaGffKYF8a8J483lXlXrAbJHqfsA8j3vP7661LwIjY2VvKlWDl49uzZGD16tPwMixnRSKV3lflQZWVlHZ5QVrQkzCFl0RR6P1lQhTmm/DlPoSRCDwir/DJfjTllNJ5Z5ZPv5+9lLhjzqpjLSi8wjVNWACbMY9NoNBqNRqPRaDQalWhD9RvAapksUkFDj+G79IjSIPzjH/8onkqPh5IeS09VX7PZLAVBHnzwQfkZGogMK+bPeGBhDIbYMWyXDw80JOntJCzWwYIi/N30qjJEmOHG3m0iGDJMGWl8shAHi62w398dd9zR8R6+xhBmFgRhhUoavkuXLhXDW6PRaDQajUaj0WhUonNUNRqNRqPRaDQajeY7wsqVK8Upxe4gpaWlUtGeFe6PxvLlyyXykmmIdKjRmcZq8yrRfVQ1Go1Go9FoNBqN5jtCU1OTtHjr2ge9J9iejb3VGanJGjqMwmSEpq8e3n2J9qhqNBqNRqPRaDQazXcQg8FwTI8qu4d8+umnUvfGw5VXXikdRdidRBU6R/VIxd6SkhJp+M6LqdFoNBqNRqPRaE4O9Is1NjZKZwp2qTiVsNlssNvtfjGGhi52CmvjeOrjfBvWrVsnLSq9YXFX7/o2KtCGKiBGKmOxNRqNRqPRaDQaTe9QWFjY0V7xVDFSU6NiUdPaXhxVJWFhYbBarZ2eY2FX9jz/trDTCFtcesPvGxoapOgqW3aqQBuqgHhSPTcPW7ecLC8tK/iyrcypdnKkOTXRc07Tl+j5pulr9JzT9CV6vp1caPDQKeTZc58q0JNKI/Wt+VchxGhWJkez047Ll7zWzVY5Gd5Uf0Ybqkditwkv/Mk0VHkKw9+nFZymL9BzTtOX6Pmm6Wv0nNP0JXq+9Q6naoodjdRQkzpD1cPJtFW8SUpKQnl5eafn+D0/S5U3lWhDVaPRaDQajUaj0Wh6IMDQ/lD5+b3JtGnT8Nlnn3V67ssvv5TnVaKPiDQajUaj0Wg0Go3mO4LVapU2M3x42s/w64KCAvn+7rvvxtVXX93x/ptuugk5OTn4zW9+g/379+Pxxx/HW2+9hV/+8pdQiTZUNRqNRqPRaDQajeY7wubNmzFu3Dh5kDvvvFO+/vOf/yzfl5aWdhitJCsrS9rT0IvK/qv//e9/8eyzz0rlX5Xo0F+NRqPRaDQajUaj6QFDQPtD5eefCHPnzpV2Nj3x4osv+vyZbdu2wZ/QHlWNRqPRaDQajUaj0fgV2qOq0Wg0Go1Go9FoNKdpMSV/RXtUNRqNRqPRaDQajUbjV2hDVaPRaDQajUaj0Wg0foUO/dVoNBqNRqPRaDSa70gxpe8Kp+mfrdFoNBqNRqPRaDQaf0UbqhqNRqPRaDQajUaj8St06K9Go9FoNBqNRqPR9ACL7qosvGvA6Yn2qGo0Go1Go9FoNBqNxq/wG0P13//+NwwGA+64446O5+bOnSvPeT9uuummTj9XUFCAhQsXIiQkBAkJCbjrrrvgdDoV/AUajUaj0Wg0Go3mu0ZAgPrH6YhfhP5u2rQJTz31FEaPHt3tteuvvx5/+9vfOr6nQerB5XKJkZqUlIS1a9eitLQUV199NUwmE/75z3/2mfwajUaj0Wg0Go1Gozl5KLfPrVYrrrrqKjzzzDOIjo7u9joNUxqinkdERETHa4sXL8bevXvx6quvYuzYsTj33HPx97//HY899hjsdnsf/yUajUaj0Wg0Go1Go/lOGKq33nqreEXPPPNMn6+/9tpriIuLw8iRI3H33Xejubm547V169Zh1KhRSExM7Hju7LPPRkNDA/bs2dMn8ms0Go1Go9FoNJrvMAbAoPCB07SaktLQ3zfeeANbt26V0F9f/PCHP0RmZiZSUlKwc+dO/Pa3v8WBAwfw3nvvyetlZWWdjFTi+Z6v9URra6s8PNCwJW1tbfI4GfD3uN3uk/b7NJpjoeecpi/R803T1+g5p+lL9Hw7uehx1JxShmphYSFuv/12fPnll7BYLD7fc8MNN3R8Tc9pcnIy5s+fj8OHD2PAgAHf+LP/9a9/4a9//Wu35ysrK2Gz2XCybsj6+npRcgGnawa0pk/Rc07Tl+j5pulr9JzT9CV6vp1cGhsbVYugOQVRZqhu2bIFFRUVGD9+fKfiSCtXrsSjjz4qHs/AwMBOPzNlyhT5Nzs7WwxV5qxu3Lix03vKy8vlX77WEwwhvvPOOzt5VNPT0xEfH98pB/bbKjhWKebv1ApO0xfoOafpS/R80/Q1es5p+hI9304uPTmlThUCDO0PlZ9/OqLMUKVndNeuXZ2eu+666zB06FAJ8e1qpJLt27fLv/SskmnTpuEf//iHGLxsTUPooaWxOXz48B4/OygoSB5doSI6mcqICu5k/06N5mjoOafpS/R80/Q1es5p+hI9304eegw1p5ShGh4eLgWSvAkNDUVsbKw8z/De119/Heedd548xxzVX/7yl5g9e3ZHG5sFCxaIQfrjH/8Y99xzj+Sl/vGPf5QCTb4MUY1Go9FoNBqNRqM5EQwB7Q+Vn3864hd9VH1hNpvx1Vdf4cEHH0RTU5OE5l5yySViiHqg1/WTTz7BzTffLN5VGrrXXHNNp76rGo1Go9FoNBqNRqM5tfArQ3X58uUdX9MwXbFixTF/hlWBP/vss16W7PTG6WpDgIS/+F+AvMvVXkUuMND/jppYgMETOuSPsrlcbhiN/jlunHPGwAC/GzvK5nY6YTAa/U424nC6/HLciNPZhsBAg9/KRv3mjzquzeWSfwN8pMOopq2NFVH9V4/4s47zZ9n8Wf/6q2z+ruPauG4FBMhDozmV8CtDVeNf7Nxbga9W5WJ/drUYqhNGJ+GsOVnolx6lWjQcPFSFL77MxvYdpbJ4jRqZhLPPGojhw9pzlVXiaGtDs9OJVpcLNFVNAQEIMRoRxEVC8QJWWdmEL748hNVrC9Da6kRGeiTOnDcAM2dkKpetvt6GL5ccxorVeWiy2pGQEIp5c/vjjDlZMJnUbtKdzS0oeOcLFL63GK2VNQiKj0Ha9+Yj8/JzYQwNUW6crliRhyXLDqOs3IrQEBPmzM7CWWcORFSk2uIVvDfXrC/A0uU5yC+og9lsxIypGVhw5gAkxIdBNVu3lWDxV9k4cLBKDrsmTUzFOQsGITNDvY6r2bYX+W98iqq126R/Xty0cej3g/MRPXaYatHkWi5afAibNhfLYeHQwXEy38aPS1EtGsrLrXJNV6/Nh93ukmt51vwBmD4tQ7mOq6u34atlOaLjmpsdSEwMw7w5WZg7s59yo7WlxSHjtnxlLurqbIiODsa8uVmYP28Agi0mpbI5HC4sW5EreqSiogmhYWbMmdkPC84ciIgItWlePKjhXPtqyWEUFtUjKMiIWTOo4wYhPj5Uuf6tWLkJBW99jtod+xFgMiJx/jT0u3IhwgdmKpXtVKSjn6nCzz8dMbg9bp/TGFb9jYyMlDLkJ7Pqr6fI06mYQL50dR5efnsXWmwOhIaY0eZ2y8IaExWM2342CcMHxymTbcvWYjzy+HoxbMLCzNIF2Wq1IzzMjBtvmIwZ0zKUyWZva0NdaytcbnfHpsh95Otwkwmhxt47GzrWnCspbcR/7l2J/MJ6hAQbYTQFoLnJIV6k8xcOxY9/OEbZRo7X8j//XYX9ByploTebA2XjRO00a0YmbrlxirKNnLPFhq13/gsVyzfCYDLBGBoshqvb7kD8rImY8MDdyoxVnuA//ewm2cDx2gWHGGVz3mpzYfDgWPzu17Nlw6livnHev/HOLnz4yX4xZqhHKG9ziwPpqZG465czkZZ6cvTtN+GLxYfw0qvbYLM5RY9ww2ltsiM+LhS/umMGhg6JVyZb2ZJ12PGHB2CvbYApMlyeczQ0whwdiTH/dweS5k9TJhvv0f8+uAaVVU0ICzWL/qD+tViMuOZH43D2gkG99tnHmnNFxfW4575VKCiijjOJjmuyOmA0GvC9C4bhB1eMVqbjautacM8Da3AguxqWIzqO9wJPM8+Y3Q/XXztBmY6jrr3vwTXYtq0EJlOAXMsWm1Pu14njU3Hn7TPkORVQhsee2oDVq/MlRy842NSu41qdco/+9lezENlLB3LHo+NeeX0HPvl0v+iPkFATnI422SvxgOR3v5mN5KT2+1cFua98gP0PvAiXzQ5jRBjcThecjVaEpCZi/IO/R/Tooaf8Xrsv5V5x6XUIM3HPqQarw44577xwyo3ft+XUs6A0vU5NXQve+HCvbC7TUiIQHWVBbHQw0lLCUVtvwyvv7JLwGxVwcXrplW2yMUpPj0RMTAhiYoKRnh4BW6sTr7y2DU1NdiWycdFqtNvFSA00GDoeRi5wbjesDgecChtev/3uLvGEpKWGy0lvdFQwUlMjZOFf9MVB8Sqp4tPPD2DfgUpZ1BMTwkS2lOQI8QjytHrjpiJlshV98BUqVmyCJSUBoZkpCIqLRmhGCoJTE1G5arN4WVWxZVsJlq3MlY1aSkq4jBvHLzk5DAcPVuHjT/crk+1wTg0+XXRQNuVpKZEiG41A6hR6Ht56r3PV976ERtYbb++SgxDqERrzsbEhEmFQVd2Ml17dLhtPFTibmrH3nmfgtDYhbFAmLImx8ggbmCmbzL33PCsHJSrgmHBsOEYcK44Zx45jKAcTb++SsVXFG2/tQkFhvRyAeHRcWloEgixGfPr5QWQfrlEm2yeLDuLAoSqkJIUhMYGyWZCaHI7IyCAsX5WHrTtKlcm2ZFmOGKmMYklKCkdUVLDoYt6vm7cWY8XKXGWyUfdzDYiKssia0KHjksKxb38lPlt0UJls+w9UYdGig7KGci0VHRcfKnOOa+1b7+xWJltTYSkOPv66VOAJG5ABS3wMgpPjRY80l1Rg333Pd6QnaTT+jDZUNd3YuqsM9Q2tiIvt7CXiSXRcbDAKixuQnaNmwd+5qxwlZY2yGHifjLf3OgtFVVUztm1Xs+A73W442Bjc0D1Hhc/RK81wYBXU1DRjy9ZSMWi65vNyo8QT9Q0bC5WFda1aky8ekK4hvqGhZsnlWrOuAKoo+miJ5PUYQzp7JgODLTCYjCj6cKky2dasy4fL2XYksuBrOI4hIWYZVx7uqGDD5iLxLHQNP+b846Zzx85yVCkyahiySi9+XJwPHRcXgtzcGmRnVyuRrXLtNjQXlSM4NambjuNzzYWlqFyzVYlsHBOODceoq46LiwuV0FaOrQpoIO/YWSZzq6uO4xxsbrYrO/DiPch0C+qzrjqOXmke/K7doEb/Ehqi9OYymsUbelEDAwwSDqyK1evy0eZyy9h113EmrFydJ2uICjZuKpSoM66h3nD+ca1lakFtrZpDpfKl6yUiw5LUOfqNa5klKR71uw+iYd9hJbKdqtDnoPpxOnKa/tmao0EjlfgqLBJkDoTd4UK9VY3XsqHBJosWw6a6woWWB4QNje3y9zU0RCXM18drnk2dKn8qx4TXzVf4VnufOANqam1KZGtpcYpBQ8+bL3itq2uaoQpbeTUCQ3yHltFYtVVUKTuZrq5q7jF/12IJlLBWjq0KmOfm69BGZAsyynysO6Jr+hoaqT3pOI9sHj3Y19hr6um6RIC5e16gPOd2t79HARwT0SM+7lWOpeGIjlYiW71NQkJ70nF81CgyGpqaHRJK26OOMwWiulqNjqPuon6l19kXNF6ra9SMG+G4+FrvPbIxfJpriAqqa1skJNinjrMEotVOPaLmfmitrpOkRl/Fk7huuVrtaK2pUyKbRnMiaENV0w2GJHlX1PWGiy2N1RhFRVoYZsbTSl9eIs+pakwv5eQdC8+m3JfJ4jFk+B4VMCSJmyR6Tn1WoGwD4rt4l/oKnorTI+hLNsLNJ4uOqCIkLUnCMX3ham5BSBfPV1/CcenJm8DNG70QXT0RfUVsTEjH4U2PekTRvcp0gR51XIsDQWZjx3v6GktCDAyBAbKR7IrL1iqv8T0qiI62yNj4ulc9Y8l0DBVwLtFw8WW0SLVut1tZcRt6TUNDTeJ984XD0SbhrCqg7kpICIOtB2OPh11JCvUvQ5G5BvjC1uJAeFiQrCEqiI8NlXB4nzquxYngIKOsvcr0COe9Dx3nampGoCUIloRYJbKdqhi8CiopeeD0RBuqmm5MHJ0sOakVVc2dFDAVck1NC/pnRmNgVrQS2UaNTJT8D1b+85aNX5eXNyE5KQxjxyQrkc1oMMAcECA5ql1l43M0Ui2KWkwwDGnK5DQ0NLR2M2x4TcNCTZg2VU0RKnrC587JkhzjrgcQ9ATz9ZnT1VUoZHVf+oocjZ3DVJ3WZrjb2pD2/TOVyTZrRj/xqHY9tec40piYO7tfj96I3mbqlHQ5gOjqjWFxFHq/WKRFlaE6eVKaHHpVVDZ103H04AweFIv+inRc3PTxCO2XipaiMplfHvh1S3E5QvulyXtUMKB/jIxNdXVLpxxeqS5a2SRjysrJKqCBzM/m3OIc84byhoUFYdqUdCWy8R6cMyNTjJdWe2cdx3uXBYxmKiwCyMrqvIZdoy+amtsPS+bM7qdIMoju5xrQNVKKOs7W6sLc2VnKilBNm5ouBZS4hnrDNZZr7eTJab1W6OlYJJ05Hea4aNEZnXScywVbeRViJoxE+CB111WjOV60oarpRkR4EK6+bBSCLUYUlTRK4YyKqiYUlzYiIT4U11w+SpkHiZvyn103QYp4FBbWo6LCKhskfs08kZ9eN1FZdUJPZV+2o6Fh6mpra//3SNXfCJNJiiup4rJLRsoms7TMitKyRskP5LgxB/Tii4bLJlQV5509WA4YeC2LSxpkzrHgDgtjsQXBBIVtL1IvOAMpC+fAXlUDa04BWkor5V9bRTWSF8xE2vfUGapjxyTh3LMHoaXZIePFHO2SkkYZx9GjkrDwvCHKZOuXEYXLvj9CvKoiW3UTysobUVLWgAEDYnDFpSOVyUYvx7U/HidRBoWFDdK2iW1NiorqkZwcgeuuGa9MxwUGmTHyj7dI0S5rdgGai8vlYc3OR1B8NEb+8WZ5jwo4JtdePR7JyeEyVhwzjh3HkGN53dXjlXmQyJWXjcLAgbFyD5R56Thu1C+/dKTStkPnnzMEo0YkoqKiWSqwt+u4BjQ3O3H2mQMxZlSiMtnOmNNfDpaYT1lc3CB6hP/W19mkddnsmeoMGur+s+YPlJZl7XqkWdYI5iSPO6L/VME185KLhssaynnG+ca1tazMiiGD43D5Jep0XHBSPIbf9VMEBJlEd7SUVEh+e9PhAimuNOyunypv16TRHA+6PY1uT9Mj2Xm1WL4mH3sOVMqJ5cQxyZg7PROJinuDedoQsFLh1q0l8j035fPn9Ue/TDVeEG9ooDa7XF/3UTUYpI+quZe9qccz5+htYD+6tesLxAjkQstNCg0e1YsWPYAsjMECQNwwsYoiT8unTk73mUvYl7jsdpR+vhJFHy1Fc1GZVPxNu2CeGLCBZnXl6gk9WywSs3xFLgqLeWBjkZY+c2Zl9WpI3PHMNy4vO3eXS2ViVgFmwSx6IebOylLe45WwMvKS5YexZ2+leLWmTE6XvpYMhVRNw6E8qShduWqLfB8/awLSL16ACD/wgvCAcOmKXCnAxrDVEcMTMH9ufwzu5bZlxzPnWNBp+YocKcBGDyZ1HPuBco1QrePooVy1pgCr1xWgrsGG9NQI6Qc6eUKqch3nKWq3clWeHHSxMjF1yIzpGcr7WDOsfMPGIixflSsGND33s44Y0Ky4q1rHsYDjshU5yMmtlVQLtsjj2qXKm9q1HzP1SM2WPXLARU9r+sVnITi573vOn+rtadZccR3CFK73VrsdM948/drTaENVG6qa7wh6zmn6Ej3fNH2NnnOavkTPt5OLNlS/HdbT1FDVd55Go9FoNBqNRqPRaPwKNcl8mlOGRmsrCgrqpNJuVr/obn3WVMJKrA37c+Tr8CFZMIWpD0n2DqPKyauVoh5sPh8ZoT4EyDuMKjevVqo5MteM1Vk1x4bBJ8yNqq21Sa9GXlfVoYTeMF+QIXusLsp71Z9k82caG1tRUFgHozFQxk1V8amewuF5rxLK1pthjicK29TkFdaLjktPiUB4l16+KmFfUsrGwkXJCWGIUZg360uPcN1yNFgRkpooVcX9Sbai4gZpLcUKz6kp/qXjysqtkgfKIm3MN/Yn2ViEjfmprJHBe7VrL1+VMOWH6VLUcSwSpzqU+1TFU31X5eefjviP1aHxK2hovfv+Hny1NEeqErJiLQspXXD+UJw1f4DSBYLVL3Nf+RB5r32ElrIqMBGUpdgzr1yI/tddjACjUelCv2J1Hj78eL8UjmH+YHhEEObM6CfFjHrqo9dXbN5ajLfe2S1FKWiwhgSbMXVKGq66cgzCwzs3Ldd8Dcfr5Ve3Y+/+CthbnTCbjRg6NB4//uEY5XnRLC7yymvbpbm8zeaQzQjz8n545WgMH9b3eUinCqwa+va7uyVnmxU6mSPIvLzvXTAM887or1TH8d78+NP9WPTFoY7en6xqe86CQbhg4RClm2DquOVrC/DxV4dQWdUsxbIiwoIwZ1oGvn/uEGk7pJJNO0rx/mcHUFTaIEVumKc9dXwKrrhwuLSJUUn15l048OBLqN+bjTa7E4GhwUiYPRHDfvUTKX6jkrz8Wrzy+g7s318Ju90Jc5ARw4cm4OofjUV6WqRS2SoqrXj1jR3YvrNMDlhpaA2kjrtiNIYM6t286GPBvsGvvbET6zcUornZjkBjADLSonDFZSMxYbyaCtgeOFZvvbMLK1bldei4pKQwfP/C4VLJ2Z8MfY2mJ/znyEfjN3Aj8tyLW8Sg4Yl+XGyInK5ysXj2+c1YtPiQUvmyn34T++59FrbKWlgS42BJioO9tgH7H3wRBx55Ralsy1fm4ennN6OkrBFRkcGIjwuFvdWFDz7Zj6ef2+Sz31pfsW1HKR5+bD1ycmuksjNlc8ONxV9l48FH1vbYq+50h4Vj/nPfKmzaXCSbcBbaCbIEYsvWYtzz39VyIKEKq9WOe+9fhRUrcxEYaEB8Qqh4VPfuq8B9D6zGoexqZbL5M7wPn31hM977YK+0RYqLb9dx9Ng88/xmfLnksFL53nlvD159fYccEsbGBsujvr4Fr76+XV5TyZLV+Xj+jR3ivY+KCEJcdLCM4QeLDuDFN3co1XFbd5XhiZe2ILewTvprcu3iYeEXy3Pw2ItburWt6Utqd+7H1l/+C9Wbd8MYES4F2QzGQBR/tBRbbv+HeFhVQR1GXUadZrEc0XHmQNF51H2s7KwKGlj/fWgtVq8tgCkwQA7MQ4NN2L2nHP99aE1HxIEKuGY++PBaLP7ykKyl7NPLtZWF4x56dJ0UWVIF78Onnt2I9z/cJ4dy8fEhEgnEitNPPrtJDug0JwbTlFU/TkdO0z9bczTyC+qwanU+IrgJiQuRUDiG/LLxNk/kPvxon8+G732BrapGvKkBFouETLFpNR9c9I1hoSh483Np5aACLgbvf7wPbS43UpLCJQSIJ78MrY2OsmD9piIcVGQ4cNF6/8O9UumX4VwMIaRsbCWREB8mlVm3bm+voKzpzFdLD6O4uF7699LrzArY3ATTy1BS2oCvFBo1a9bm49ChaqSkhEuFSZMxUKpOUlZWTv7kswPKZPNnuJFcs7YAkVEWMWbMpnYdx1B43isffLhXvBGqPOSff3FQ9AcNBnrv+eDXfO7zLw5JmKEKOCYff3lQvk6iMRN0RMdFByMyPAhrtxQjt6BOiWw0SD9YdBDNLQ6kJIZJezWTMQDRkRYkxIZix94K7NirZm0guS99AFt5NcIGZsIUHooAswlBMVHSF5dGbMnnK5XJ9uVXh1FS0iA6jf1mRceFB4keoe6jDlTF6nX5yM6pQWpyBCIivtZxlJX3yqeL2uejCmjYc+3kvcm1lPcC19bU1HA0NtplzVV1cHPwUDXWbShCdIxF2vlRNuqPlORwtLnaRDZ9OK05FdCGqqYbO3aWyWJPQ7UrMTHBsjjs3V+pRLbqDTthr6mXUN+usO+gvb4RVeu2KZGNi2l5hVXGqCsMOaMhu3NXmRLZ2NeNpfO5mHYN9+HixVBDlae//gxDujhGXcMteWjDTcna9YXKZNu0pRiGAEO3nCPp2xsRhB07SsXrqunMzl3lYnT5yqtkiC1DWg8cVKPjdu8uFy8SW3B0hc8x1HDXbjUG18HcGlRVt8jBmy8dxzHdpWhtKC5rREFxPaIje9ZxO/dVKJGN3tKqtdtgju2eV0mD1RAQgPJl65XIRkOK7coYIt21RQ51XpDFKDpQFZs2F0u0CI3nbjouLEgOWJnyoCpKydXm7ta7nbJxH3A4t0b6lqvax7XanD7D3Skbaxocyq5SIptGcyLoHFVNN9gXj8uVr/wFLhY8uXY61JzEtdkd7SeUvmIgDAaRm+9RgcPukrHxlT/GseR/DkWhZyx80i6b75wUyqdPV33DcekpJ9AYGCAHEJyTKvJ9aBj0dE0pM+9lh1NfV1/3A5WFr2vG8eTmk2OnTLYeCmd4nvO8p69h6Cz1COe9Tx1nUKfjPLL1dD8Qu6Jryj7MbHVCo9QXhkAjnM02+LOOUwXDyn3NN+97VVVIN8etJ7VP2RhdpWqvxDojhh51XPs+TpWOO1XRxZTUoD2qmm5kZETKyaqvxYkn/RJ2kx6lRLbwQZkwhgTD2dg9Z8bV1IKAILO8RwVpDJsKNUsVUZ+LRgCQka6mKEViQhiiIi0+ZeOCRds/K0ttUSB/ZfDgODQ3+z78YCj1kMFxyopS8LN5QOIrvKyhsRXJSeF+VXHaX8hMj5QCcb4OZxiyFxZqUnavspopC9kwqqUrnId8je9RQVpKuHjeGqzd9QiNZ54fZqSo6e+XeCQ/0NrUPYKAxgzv0H6KigKZoyMRmpECR21Dt9d477a1tiJ6zFAlslF3UY9Ql/mCc27IEHWFngYPihVj1aeOs9qRkhIhexIVsLqvu619De0K11pGHjAsWAXUX3Jw5MNQpmysnKy6SJZGczxoQ1XTjXFjkqWEOUNDvJUc81JZ5nzihFTJc1BB5IhBiJ08GraySrhavj6Bdtla0VJSjuixwxAzYaQS2ZjrNm1KmiwC3oYNx5Bl69NTIzFxXIoS2RiadOa8AWi1u8SA8Sz6DIdjnmV8XAhmTMtQIpu/M/+M/jJ+LB7jGTf+W1nVJDl68+cNUCbbnFn9EBkZLOFlns0SZauta68Uu+DMgd3C+TTA+HEpYuyVlTV20nG8b1nAaOqUdGUbTG7Mhw+JQ1Vlc6fDQn7N3FS+xveogLmek8cmo6HR3smQpte+vKIJGSmRGDMiUYlsIcEmzJ2eIVEGjU32jnuVrWpKyxtFx01RpH8DAgORecW5vDvRWlXztR5xudCcX4yg2CikXjgPqqAOoy6jTvPWcdR51H2sgq2KubOy5ACChc466bjaFtFtCxR2IZgxPVPmFfN7uZZ6ZOMay7X2zPkDu4UF9xXcp6WnR6K01NpNx3GPMn1qhuSuajT+jg791XSD+W6/uHWaVLPLy6/rWBxMpgCMH5+K664ep0w2Lkij/3Ybtv66BbVb96DtSFijITAQ0eOHY8w/fin5Pqpgmxf2oNu6oxRVNfT6MhyOXuoo3HbLVFgs6vogsq0Fc2hXrclH3RFDhuPJKoq33DRF91PtgTGjk3H1VePwv7d2orCoQTwzvCOY33j5FaMwfmyyMtk4r266fpJUqi0ubvfWcJ9Jr9f3zm9vs6LpDjflt/18mlS7Liiol80lrymL70yZmIYfXzVWmWy8J2++aYro3wMHq8QbSOEYSsh2Q3xNZVuJqy4eKRvxHfsqUX2kdQ4NBnqpb71ugtL2NBcuGIzK6has21yEuvr2g0wOVUJcKG6+erxElagi/eIFaCooQf7rn8KanS+pIDC4ERQfi5F/ugURg/opk4067KofjJFWJkVFR/QIIAbiDy4fjTGj1PV6pdfyZ9dNwAsvb0NRiccjzbZDZnz/gmGYM1PduPFw+ue3TMXjT21ASUmj6F5C4/Ss+QNlzVUF6yfcdutUqT7Ma9q+j3NLYbapk9PlemtODJ75qjz3DThNz5wNbpW15P2EhoYGREZGor6+HhERJydsifkoFRUVSEhIQMApWlO6xebAli0lUiGTm6QRwxMxamRit6IGqnJ+qtZsRc3WvbIzjxozFAmzJyEwSH3TeZ6s7t1XiV17y+Ukk302J41PFeOhNzmeOcfbnS1Ltm4vFc8DPeNctHwVztJ0hl7xDRuLxMhnhd0pk9Ik7MwfqKlpxroNhdJGgmFwPE3vl9m9cMvJ5Duh41oc2Li5GHl5tdL/cNSIBNFz/qDjqDvYG5fGKmH4JQ2KroWzVEAv5d6DVdhzoFIMaYbUThidhGCLf+i4gzk12LGnQsKRU5LCMGlMis/CWX2NeNsO5KBi2QYpsMRq9ckLZsKSoMZD3hV6BjdsKpLIqaioYEyZnCbpA/4AizhSNnp9GbY6cXyqHI74g47jeFE2jh+N1PFjUzBoUKxf9CmlB5VthnLz6+Qgjnu4EcMTlPRi7o29dl/KvelH1yHMrE6PWO12THr1hVNu/L4t2lDVhqrmO4Kec5q+RM83TV+j55ymL9Hz7eRyqhuqm69Wb6hOfPn0M1T1nafRaDQajUaj0Wg0Gr9C56hqeoQl33fvKcfh3Nr20N9hCRg4IMYvwlkYCGB1uNB0pLx6qCkAYaZAv5Gtfm82qjfskFY5EUP7I37GeASY1OWnemPNKUTFqs1wNbcgNCsNCXMmwxjsH5VhGbrK3qCsQBkfFyohrAzz8geY87Z5c7EUKmLo76SJqdKX1h9wNVnRvGkjnBXlCAgNQ8jEyTAlqilq05VWu1NCzYtLG2EJMmLsqCSkpfrHaTCLALGnam5uLYxGA0aOSMSA/v6h45hTtndfRUfo79AhcRg2lJ4d/9BxTAnh+uB0uqViOPMY/SFkmhQW1WP7jvb0htTUCEwYlyJ5yf5ARYUVm7eWiI5jfQDqOFVVa7tC3ca+pQxlpW6bODFVaV6vN002B3ZkV6O6wYYQixFjB8Yh1k8qmjtbbKhYsRFNuUUIDAlGwuyJCMtKhz/AFILtO8qQX0AdFyihvyyW6Q86TqM5HvxDc2v8jopKKx55YgMOZle3V7NzswBJICZNTMONP5koifqqcLa5kddgQ6PdCakPwJoULG5jDkS/cAtMCnIvvKsP7/rbYyj9YhWcTS3tvQUDAxE5YiDG/ufXCOuXpky2NpcLBx58Cflvfib5UbJQGQwIH5CBMf/8JaJGDVG68f3kswN45709UpGQsvE5Vl694acTpUqrSlauzsPLr25DTW2LFEGhbG++HYwf/3As5s7JUipb8/atqHryUTjKy9tLoLjdCHjjVURffBkiL7pE6YaEDe8ff3YTCosbpNAIx4252vNnZ+GqK0b32B+xLygrb8TDj66XnG1PxU7mlzEv74afTer1fMujQUPhkSfWY9eu9jx3wtzUUaMS8Yubp8pBicraBU8/u0nytWkIEua7DRoYi9t+PhVJieFKD1dffX07vlp6WHLzPH0k2YbjF7dMRf/+Mcpk49z/8ON9eP/DfR06jiQmhOLG6ydJ0TaVLFuRg1df3yHVdEX/wo2Yt4NxzY/GYZbCgkVkZ041XvvqEKqPFMjiWH60Jg8Lp2XirAlpSnVc7c792PH7B+QAmEpOcqQfew2ZV56HIbdfIxWfVcGc2YceW4ecnBq4XO4OHceKv9f/dKLfHN6cKug+qmrwj+NPjV/BxZ5G6p59FYiJCpa2KvSAsPz/qjV5eOV/25XJxkWgoNGGulYnAg0GBAUaYAkMgNFgQH2rE/lerVdUcOCRV1D03mIEBlsQNjBTHpakONRu34dtd90DV6vvXnV9Qf7rH+Pw8++yRGeHbMHpSWg8lI+tv74HrdW1ymRjIaDX/rdDPFz0gKSlRSA5OVwKZzz6xHoUFdcrk23f/ko889xmqXTK4lOULSUlHFarHc++sBl79tJAVIOjpBiVjzwAR0U5TMnJMKdnwJSWDricqHn9FVhXrVAmW6O1FQ8/uQH5hfWIjw8VHcIHjZpPFx/CZ4sPKZONxt9Dj6zD3v0ViI0NljYOvK7cxC1fmYfX39ipTDbqryef2SjeexY5o2x88Gs+99Szm5TqOI7N8hV5MlYcM8oWExMsY8kx5T2sCh52ffzZAYkAomzsbc25x+r1rPDsq490X7F6TT7+9+YuWV89Oi4pKUza+jzy+AaUlDYqk42e8ede2CI6jbpNdFxyuOi8p5/bjP0HKpXJVlLVhBc/3y+e1PgoC5JjQ5AUGwKHqw3vrcrFliMRByrgmrntrnthzS6QtVTW1QEZssZyreWaqwr2iH7wkXU4eLBa2tB4dFyQxYgly3Pwxtu7lMmm0ZwI2lDV+Fy06ElNjA/r6AHGE8uwsCCEhwVh7fpCMSBUYHO1od7uhCnAgMAAtn5pP2Li1+aAAPGyNjnb1MhWVYOiD5fAGBEmDd47ZAu2ICQjBQ37DqNi5SYlstFAzn/jUwSYjLDEfx3aGGg2IyQrDU35xShZtEqJbNx0L/riEByONgn39YQ2MowwNSVcTvhXrMyFKuid4QYuKTGso1Ii/01MDBWvzZdLDiuTrXHFMjirKmFKTYXBeOReDQiAMS4ebpcTDYs+VWbUrN9UhOKyRjlwMB+pVMt5x1BCege/XHoYNq8+oX0JQ+GyD1fLNfV4FShbeHiQhJqvWp0vlZRVkJNbix07y2Rz6V0pnF/TqGZIK0OVVcAx4diEh5tlrDx6hOsEx5JjumNHmRLZ6N1d/OUhaaPGirUe2czmQDG+ioob5EBMBbwHP//ikHju4+JCOuu41HBUVzdJ1IYqqMOoy6jTvHUcryl1H3WgKtbtKUNdkx1J0cEdERgBBoOE/dLoX7atWJmO45rJPrhcQ7mWevQv19iAQKNEL7FDgQpYMTwntwZJSaGddBxbDoWGmmRN9bRw0mj8GW2oarqRk1crC4CvRtWRERZZuHLz1GyUmJPa1gYE+giB4NrPUOAWr+bWfUnjgVzYaxtgjonq9lqgJQhuGtl71HiR2L+vpbRSDOiuBBgDJaakbudBJbJZm+zi8QiP6J6nxYWVG83deyugij17ysVI6Bpexu/5PA92VG2UWvbsgsFkhsHQXZUHRkTCXpAPV32dEtmyaUy54TO8l5slhlGz5Y8KuIFjKJyv0LfIiCDxvKnScZStxeaUzWRXmMtIg4wh1SrgmHBsfLWz4lhyTJm7qoKS0gaZU7x+XaFByDuUhrQKaBAUFdUj3Ids1CM8uNmrSMdRdzEq5Fg6ThUHiuqlrYqv8N6wYCMKK61oPhKC3tfU7TwAt8HQvoZ2wRwTiZaSCjTllyiRjfchW0exb6qvfVxDQyvy89VFUZ2KcJkNUPgwnKYW22n6Z2uORsARK9DX5rvN7ZY4eVUFPY4rRl9RHD89WoYAA9y0pH3BsTvi9eprJE/GEHBU2Xwttn0BDRkqYbckHHeHT6vMZeQml/PeF3xeZQEZg8kEt7uHa8prLTnSauYcoxyY5+YL0SMBBmV6hKGhPeq4No+OU3Nd6cnqaVQ84qrogUg4JhwbjlFXPGOpSjYZN4NBpr1PFN6r1F/ta8NR9IhJnR7h2B1Vx6nUvxy3HmVrN6Y9e5a+JoBrfg+yyVobEKBsXWVf6B7U7xEdR/2rTQCN/6NnqaYbI4YmSHVOerq6wjDM6OhgDB0cp0S2cFOgLFxOH4sDn+N6Gm5SszGPGjkIwcnxaK3s7lFwNDYhwGJG7OTRSmQL7ZeK8EGZaK2q8VkAiuFKcdPGKpGNhblYbbWhwd5tQ8JQOaejDRPGp0IVkyalwdbi6LY55/fNTQ5MnqiumEfI+IkcJLidnT0KHEdXQz2CR4xEYLia4jYj2VQ+IACtPsJ7WV00NTkcqSlqqv+OGJ4onnqGO3aTrbZFwm4HD4pVItvwYfEICzX7DMtjkSWGJrMCuwo4JrExIait7S5bU5MD5qBAjByhRjbOJeZA8/p1hXOQxhj1jAoYJj1saDzq67vXUGD0Ej3R48eqKRhH3cUK5i3NvnUcdd/kSeqKAI4eEAunyy3ewW6V/1scGJYRhWAfXsO+IHbqGDFGfdWe4D4gYnA/hGaqWbuoI46m4xiCPnCguuJip3IxJZWP0xFtqGq6wRY0XLgYRsUNJRcrLqbMS2WC/rkLBkm+qgrMgQGItRjBAnZ2V5ssVnywsAILd8ZYTLCoOjUPDUHWNd+H2+lCc1EZ2hwOOVVlwQVbaQXiZ05E7MSRSmSjITrgp5dKCHJTXrEsrJTNXteA5oISRI8dhsR5U6GK888bgsjIIBQXN8qmkteU7RuKixuQkR6JObPUVZ08a94AJCaEoaioAc0tDpGtpcUhOW8JCaE4c/5AZbKFz54Lc78sOIqL4LJaRba2Vpt8HxAegcjzL1Im24SxyRg2OA7lFVYJF5X71OFCWbkVZmMgLjx3iDJPDVu98PCjuroZdXW2Dh1XUdkk/3I+qmoZwqq5c+dmwdpoF/l4WMMHv+bh4Rlz+iMxMUyJbByThecNgdPp6hgrjh3HkPmrE8enYoiiQ0x6S793wTAJo2VIud3hkjnHgkBlZVYxFNmmRhUXLBwiBmtxSWcdV1LSiH6ZUZg1I1OZbAvOHIS4uFDRadRtlI26jjqPuu/MeQOUyTZ1eCJSY0NRWt0sIb6UrZV6pKYF4SFmnDlBnRGdNH+arJ3MU+VayjWVayvXWNam6H/dJbL2qmDE8ASMG5uMqqpmOfTifcpCZ2yPxK8vWDhUaWVzjeZ4MbhVlg/0ExoaGhAZGYn6+npERJycE/62tjZUVFQgIYF970698wAuVq++sQNr1hVITirDlqKjLGKkXrhwqLLwLk8oUmmTHVUtjnbPKkNDAwxiwKaEBUmhBVXwdsp95UPkvvgeWsqrJfzSFBGGpLNmYPhvfiZf9xbHM+eKPl6KQ4+/Loa02+GEMTwUcdPGYcQfbkJwopoNpnfxB7aW4GbJwRzpIKNsen963QRpL6GSg4eq8MLL25CbW4NWuwtB5kBkZkbhuqvHY+iQeKWy2YuLUPXsk7Dt2wu3rQUGowmm1DTE/OhqhE6aqnS+8aDrhVe3Y/vuMjnZp95g78iLLxiKuTP7KW0rQSPh5de2Y936dh3HMGRWr1147hDZxKnsV0qDnlU5ly7LQX1De6Va5l7OO6M/rrxslBhjquAm9+NP9+PTzw+gpqb9IJNe3unTMqRdU28a+Meac9S/y1bk4t339kiLNXoqmWM5dkwyfnrtBIkGUgl7lL7+xg4Ul7TruGCLUfTHz66bgBRF0QUeWNn3hZe3Ij+/rkPH9c+KwbVXj8PgQWrXBlb+/d/SQ8gubhAjlS3oWP330jkDMDIrRqmOaymrxJ5/PoWqddvgbGyCwWRESFoSBt3yQ6RdMA8qoV576dVt2LCxsEPHMVqEhybUc32tf3tjr92Xcm/7yXUIP1I0SwWNdjvGPf/CKTd+3xZtqGpD9ajQi8oCGlRwDPdV5Un1haOtTYorkRBjgHhb/QVHoxW12/ahze5A+JAshKb3fo+8451zDPWt2bIbzmabhARHDFLbI88bnvgeOFAliypDkwb0/7pCsWq4IWfPTYZNsY8lwyBVHth4QzVuzzksbWoCw8JgGTpc8ld7kxPRcTx8YAVgbn6pR3gI4S/Qw5CbXwdjoAFDBseL0eUv0NDnnCPsUxodpdbQ8ob36IGDlRKWmZUZJT2Pe5vjnXMsOMW2Una7s70VTKrag66uhxD7D1TJQQkPbbKyov1Gx9Fzf/BQtYSY06jnnFN5YNNVx+WVN6KmoRWhFiMGpUb2uv49ER3XcChPPKnGEAtiJoyU6CV/gT2jeQBhNLXrX1XRIqe6obrjp+oN1THPaUP1tEQbqprvAnrOafoSPd80fY2ec5q+RM+3k4s2VL8djaepoarvPI1Go9FoNBqNRqPR+BX+E3+l8TuYR7N8Za70d2OhChYfmTUz0y/CzxiSvHJVHnbsLJOc1TGjkjB7Zj9lRUa8YdjUqjX52LK1GK2tLiniwWJAGRnd+6v2NSw6VVDfgqJ6m3wdHWxCZnQI4kLMfhESt2FjEdauK0BtfQvSUiIwc0YmRo9KUh4ax7DfLdtKsGZtPsormqSI0oypGZgwPkV5+C+DYnbvqcCq1XkoLK5HVKQFU6dkYNqUdKn6qBqGrq5YlSf9QRlyNmlCKmZMy1AWfuZNYVE9Vq7Kxd59lZL3yes5a2Y/GUN/CEleuTofO3aVyfdjR7fruPj4UNWiSXEWzret20olXJ86jrKpziUnDKllbYVNm4rQ1GyXPMu5c7IwcICaKs7esBjhug2FWL+hQMYwPTVS5hsrJavWcQz73bK9FGvWF6CyqhmJ8e06bvzYZOXhv9RxO3eVYfWafBSVNCA6MlhyoqdMTlOar+3h4MEq0XG5ee06bsqkdMyYniEV7VVTXm/DjvwaFFQ3S27v4ORwjE6PlvBpzYmhuvKuwT+i8PscHfqrQ399sn1HKR55fD2qa1pgNrP/JqtiupGZEYlf3zlTac4PG1n/98E1KC1tlN5zvHcdjjYkJoTijtumKy1uw1yQe+9fjZzcWunVyF6S3JxERQXj5hsnSyuT3uJYc87mdGFtfi0qmtqLswQa2tv8MLd3TFIEBsaq2wCzCubDj62Xog9USdx8cNyCgoy46MJhuPzSkco2ctzAPffSFixZmiNVTmn8UTb2qZs3Jws/u26isv6MHKt339+D997fixabE0FBgXIv8KaYPCEVt/1iWq9VdjweHbd0eQ5efGUbrNZWaTzvYpVYuDF8aAJ+dccMpQYhD5Iee3KD5BzzmrL9hetIvuWv75yFlGQ1bX08xv39D69FeXkjTEf6MNIgTEwMx523TZfcQVWUlDbgvgfWIC+/rkPHscBNTFQwbr1pCsb3YmXdY805Gn/33b8ae/dXSFE9HiLxXmXe8XXXjMe8uf2hihabAw8/sg6bthRLP1yTia2bXFJQ6eLvD8cl3x+hTMdRrz370lYsW5Ur92i7jmsTvXbmGf1x3Y/GKjuQo4578+1d+ODj/bC3OkU2HmpyrKZMTsdtt06VdUKVjlv8VTZefnVbR3smjiUvMFshUcdFRKjTcfuK6/HRliJYbU7Jwee848F+clQwrpyeiejQvs2jPdVDf3f+TH3o7+hndeivRiMn0k89u0laDmSkRyA5KVwqEqamhiO/oA7PPr+lxwbcvQ0Xgaef3YSyskakpUXIZjI5OVy+ZruEp57ZJIuYKl58eRsOH64RudjXLykpHOnpkVJ45NnnNvvsjdhX7K2worypFaFmIyItJoQFGREZZGw/rS5rQJ2te7+1vuLzLw5J9dXomGCkpUWKZ5zjZjIH4P2P9slpuironflqSY70tqTHiO0a+G9EmBlLl+eK91wVe/ZW4L0P9iLQFICMjPZx470QGxOMDZuK8NnnB5VGZLz86na5HzleSYlhUtgmMT4Mu/eU4613diuTrdHaiqef24yGhlaZZ7xPeb/yvmXxuBdeUqfjaJA++ewm8ajyQDDZo+NSI+U56ma+RwUcE6l+nVfbScdlpEVKG5inn98sY6sKGjScW7xHOdeSkqhHImQOvvTKNpSUNCiTjfci70lWluY9ynuV9yzvXd7De/dVKJNt1dp8LF2Zi4iwIC8dF4GwUBO+XHoY6zYWKZONup9GKg/Mea+267hIWSvWbSjAosWHlMlWUFAnlep5yMV5Rh3HXr6MuKHc77y3R5lsVpsDn24vRovDhYSIIMSGBSEuvP3f4tpmLNpRqky2UxXVPVQNp6lHVRuqmm5s3lKMioomWeS9T3h5osrS5gxzocdQBVzMuUli+Jt3OBK/TkwMlVC+7TvUGDVFxfXYtadcNiLeHjaOIWWrqm4Wj6EKWp1tKKhrEe8pW/l4yxZiCkSrqw2FdS3KDh+WLc8RL2pIl1AphpnT28qwKlUw/J2n0OyB6A0rYDPSYPmKHGWy0UhuaXEipkvbDYac8XSf46rq4Gbt+gLUN9iksqm3HqFHhGO5fkOhsoObjZuKJH2gq47jfcv7l3qGh3IqYBh3YWG9jFtXHcfnCgrr5T0q4JhwbGJjQrrruIQwGVO2YFEB59K69YWIiAjqFPJO2Wg4NDTYZE6qgPcgWw3xnuwaDsp7l9EQDPNWxfJVeXII0bXiNe9Tpj0sW5mrTDbqfnpSu6Ycca1gtAHHVbyYig4x2SM6Pi6ki44zSgiwvK7o4GZfcQMamh2ICTF33scFGBBhMSGnworKBnUH5xrN8aINVU036JnkouUrnJE96bioVlY2KZGNn8tFyeIjv4KLA50g3CypgMZ9q80pY9QVT9iUqnHjqSrb+Zh95BpxEeOzVrtTiWzNzXZpwxEa6jtElS1NiovVeUL42QzP80VwiAnFJY3KvG9FRfU95qGGhpikBye9+Soor7CKceUrnJGysVdzlaJ7Ve5D99f3pTchIeYjOq5ZmWwMN/cVzsjn+Joy/VvVLK1ffOk4WS/c7euHCjiXOKd8ySY6zmBAaZlViWw0Zuhx5rz3hdkUKPeyCqi7GP3Q9ZDQQ3CwUV5XqX9p4PsiJNQkawfXEBWUljdKSLBPHRdqQlOzA9XVavRIXbOdt6PP/GILU2ucLtQ1q4ui0miOF22oarrBU1UqOJ6kdoX5PsytUVUIhbJxUfDlJeIGjjsllbJxs8Yx6orHkFHVo9HEXLIjOam+4NNBinKQLBaTbMCZr+UL5lxGRanL82HPVF/XlPB5enBU5ZbRy+DsIQyU40kj1hKspmgGwwg5730Z8a12l+SXq+rL7NERvmRj7016ano6OOltGGLO+eTLS8Tn+Brfo0o26n+OUVe4XnA4lckW1i4b51ZXPPMwMkLNfOOBFu/FnnQc72H2LVUB5xPzKI+m4yIV5llS9zvsvj2m9tb2OgaqihZFhlskj7VH/WtUp+OCjxxg+tJxLKQYGBCAYD8oRHUqwTRl1Y/TkdP0z9YcjYnjUxERHoTqms4ngVR4PFFnDsbQoXFKZGMFWIa/UY6uCriqqhkxMSFSpVAFA/rHIDMzSuToKlttrU02xxMnpCqRLdRsRGJYEGyOtm6yMSyY4cCpkWo2StzAsQosc6O7bs5ZgIQ24PRpmVDFrBmZIlfXjRy/54PVTlUxbWq6bDSbWxzdDm3oSeXrvVVM6VhMmpgmm8jGRns3g4YFjEYOT5CcLhXwPqRhU1PTOdyd9wbv34z0SAwepEbHjRmdhNjYYPEQet+r7fq3CXGxIfIeFbCIU3palKQxdNdxLQgPV6fjGHo8YlgCamtauh2ycg7yQEyVbLwHWYWb92T7gerX8N7lPTxtagZUMXtGZoc+84bfs4gidaAqpk/NkDWAa4E31Mms6jxzeqayyr+TJqZKJBc95t5w/rHGx+jRSXK/qmBIcoQYoo22zodKvG/rWxxIirIgJUZ9BweN5lhoQ1XTDeZ/sgKhy+mWsBsWHKHSLSxskNPyq34wpqMSZV/Dk9MfXjlawkGLihqkFQzz4Bg2xRCXKy4d1S2XsK9gGOGPfjBWKpkyx6yurkXCvRg2xTzLCxYOUVoteURCuBRQqrc5JRSYBmpjq1P+7RcdggSF7UIWnjcE/bOiZb4xVIoLf3m5FVWVzdLOhJs8VZwxtz9GDE9AWblVwhopGw0GVngePiwe8+epqyQ6eVKaVL6srmqW8aJsHD/eG/36ReHChUOVyTZ0SBzmz+0vOVolpY0iG40Z5pGzIMrll45SJhsLxH3vgmGyEe+q48LDLaLjVFVy5oHWDy4fLZ9fVNwg+k10XHGDeAyvvHyUsqgRynTVlaMRHhYk15FjJjquuAF2hwsXXTBM2eEDjb0rLh8ln0/9y7nGOSdzz9qK+Wf0lzY6qrjw/KFyT/Le9NZxvHd5D0+eqMaIJvPnZGHYkHgJjaZu43hViI6zYsSweJwxW+VhXIYcMHAt8NZxnHNsPXTeuYOVycbKvmx9RP3BLgSUjYdfvDdYAO2yS0Yqky0+woLpg+PEe1rZaENzqxONNgfKG1sRZjHizJHJUhlbo/F3dHsa3Z7GJ5wWa9cXYtHig8jPr5NNAJXywnMHY/iwBL9on/Pp5wdw8FC1hJsNHBCDc88eLCecqjl4qAqffn4QO3aWyskqjdOzzxqI2bP69WqI6PHMuXqbAwermlDc0AI6HULMgRgQHYoBsSHKFy1ukD757ID0UWWub2SURdpJnHP2IGVeQQ/cuPGasrAHPb/MNaOXYeG5QyQ0WCU8BGHV5KXLDovhwHwueiHOP28IEhLClM43ej3YoubLJYelYi1b+kwcnyKyZaRHKddx7Mu46MtDYthw/o8cmYgLFg7FkMFqvKnebN1Wgs++OIhD2TXy/aCBMVh4zmCMG9t77V+Ol/0HKkX/7tpTIQXF6IFecNYgzJyeoVzHsRIr9QiLOrna2LYsDGfOH4D5ZwxQdvjggfeA6Lj1BRK2GhUdLDru3LMH9VqLleOFhyGffnFIKgA3NzvkMGQOddzZg5WlrHigN/XzRYekOBwPp4MsRumjSj0SHxeqXMd9uSQbXy05LGsY5xijSVQfTHt03I6COmw6XIXKxlbRcQMSwzBtYDzSFHh6T/X2NHtv/AnCgxS2p2m1Y/hTz59y4/dt0YaqNlSPCqcHTwnpLVR1in802axN9k55Xf6EJ5S1r3IYT2TO8ZTVdaSHqmoD1ZfhxWI2vKaqN5ZdYW40C2TQUPWHRvPdQuGaGOJo7JNN74nMN08oMsO8VeWT9YTWcd8czjf2nmXIr7/pOBZW8vRQVdUD9Gg6jkWpON+0jjsxHcf7IdifdRwrOys+WPWlR5pbXdL3mIWUVKEN1W9H42lqqKo9wtP4PZ5CC/4qG0PQ/BV/2/R6YwoMgH8tpV/DDYhq70JPcOMWFelfmzcP3PCq9u72BA0Ff5VN67jvpo7jgYi/HYp40Drum+s4ptb4I/6u40J7qFyvOX4MrGLvo4pyX37+6Yh/HeVp/A6GrjIfhCXg/c35Tnlqaprl4Y+yNbDtSmNrt+IZ/oAnz1JVj82j4Wi0oqW0Ai6bmv5zxwpB47jRW+NvcLw4bhw/f4OeEBYp6lp0xB9oc7nQUlYJW1WNX+oRf9ZxXBdYWMmvdVwPVbFVe6IpGz2r/oazqVn0iLNZTV/to8Hx4rhx/PwNrqWe/F5/w+lqQ1VNs7K+1RrNt0EfsWh63ISsWJmHzxcfRHExe4UBgwbF4cKFQzBmtJqqut4w/+iTz/YjJ7dWvs/MiMK55wzG9CNVUFVyqLgeX24rRnZJg+RvxUdZMHtUMqYPS/TZ06wvyc2rxYcf78O27aWyuWSV5DPnDZAcKdVhXk2FpTj8/Dso+3It2lrtMEWFI/2is5B19fdgCldToMUDF/iPPt6HVWvyxUilp4aVilmQR1VbCe+NZe7LH6Dw/S9hr21AQJAZSfOnYcBPLkFoZqpyA3XRV9lYsixHNkrGwACpWHshi2f1i1au44re/xJ5//sETfklclodPW44+l97MeKmjFEu24ZNRfhs0UHk5dfJc/0yo7DwnCGYMjkNqtm1uxwffbIfBw5Wgt05UpLDcfaCgZJvqVr/5uTU4IOP92HHzjLRcbGxIThr/gCcvWCQsiKAHljY6cOP92LjpmIxbFj4j2PGXEvV3l9bZY3o35LPVsLV3AJjaDBSzj8DA667GEGxau9VGqYff7ofy1fmtofXmgIxZUo6vnfBUCQlhiuVjdeR9+mSpYdRU9sintXx45JlbeiXGa3c0bB0ZS6+XHYYpeVWBAYYpGjW+ecMxvAh6gqLaTQngs5R1TmqPnn3/T148+1dUqgoPMIsBld9fauEev385ilSpVAVy1bk4NnnN8NmcyEysj0srr6hVQyta380TorvqGJPfi1e+OKAVNcLDzaDdqmV5eHdbpw9IQ3fm9571ROPNecO59TgP/etkoIeHDeOFxd9Vus8Y05/3HLjZGW5XM3F5dh405/ReCAPpugIBFqC4GxsklP9xDmTMeGhP8hzqjwz/7pnJfbtr0BIiBnBwUbJL+PYDRkSh9//Zo6ykC9Xqx1b7/wXypesQ0BIMEzhoeJZddTWI3xQP0x64v8hNCNFyXzjJunpFzZjyfJcCdljriANV+qRuLgQ3HX7dAwaEAtVHHj0VWQ/9YboOHNMJOBqQ2t1LcxRERj7718hce4UZbJ9tfQwnn9p65Eell/rOFY7v+6a8XK4pIrNW4rx8GPr0Gi1IyoySA7fKBtz3Vkt/orLeq+a87HmHAvZ3Xv/alRWNsk9yXlntbZKi5Uz5/XHTTdMVmZIl5Y14l//WYGCwnqERwTJtWQuKHXJ5Ilp+NUdMySHWwWtNXXYdPP/Q+32fTBGhMEYEgxnUwucjVbETBgpesQcqcYg5Pjc+8BqKS7G3NSQEJP0KKVeZju4P/x2Tq8VjTvWfONByONPbZQ9CY1n6jgartRxLOL121/PQv/+MVDF/97ZhQ8+PSDtfXgoQnlZoZjtB2+7aQrGjurbNleneo7q/lt/qjxHdehjz51y4/dtOfUsKE2vw7YbH368XxbNlJRwyZFiDldaWoSEPr7+5k5lIaM8WX3zrV2y4aU8VL58sLerAW68/d5uqQqoAi4CH63Lh7XVieToEIQHmxBqMSExKhgWsxHLd5aipLoJqnjnvd1ipKanRyIqKlgOHdgmJDoqWKqf7t5Trkw2egRppIYOyIAlIRamiDAEpyYiODUJFas3o/SLVcpkozdw3/5KJCWHi4HFcaOXhvfGwYPV8roqyr5cg/LlG2BJSURIaqKMG8cvdGAmGrPzkfPS+8pk27u/EivX5MuhCFuGsBgQ88sy0iNQVd2Ed97fqyyc1ZpXhNxXPkRgcDDCstJkE05jNWxgJhwNjTjw0Mtoc6oJy2To4Fvv7JaxoV7z1nGuNre8piq8kGG0r7+xQwys9LQIWRfCwoKQmhIhOZesaFtS0qBENo7Xm+/sFiM1I4M6jrKZkZQULnOQFbv37quAKuiBppHKdSsmul3/sid4fHwItmwtxoaNhcpkK3znC9Tu2I/QfmkITopv17/J8QjJTEXt1j0ofG+xMtnWbSjA9u2lR8YqVMYtJiZYxpEdCTjnVME1k2sn11CupZSNayvX2PIKK955f48y2QqK6vH5V9lysMp2XNS/kdzHpUbIIdMb7+72y5B9jaYr2lDVdGPrtlI5he4a0siTaDavZr8wbtxVhZxVVjUjzkdJehoPbM/BsFYV5FdYUVLdjOjQ7lV+I0JMaLE7sSuvPVS5r+HmbfeeCrmmXcOPuZmj54bh1Kq8gqWLVsEYGYaALqF5xhAL4AZKFqkzVLkRMZsD5MTcG3qkg4ICsXJVrjLZShevlvFhmJ43AYGBMEVGoHTxGgkNVsGWbSWwtbq6FQPivRETFYy9BypRXqHm4KZi+UY46hsRFB/dTTZLciKsOYXiXVIBQ1YZQhgb0719BPUvX+N7VHDoULX0c6UcXXVcdLRFogx43VXAnp8HDlSKEdNVNhr69MLRG6wCpgvQEKUcXaNWWCGW5zXrNqgzVIs/XiYRKwHmzuHHgUFmGMxmlHy6XJlsbFcGA6SaeSfZAtujNKTVj13NwTnXTLvd2a19D9dY3g/t+xU1Om7rjlJpM9S1+BTvjdjYYDFkDx9JndJo/BltqGq64SlU4Cufkl5Wh6NNFKAq2dxw+yzp79kANCsqdMPy7862Nph8yNa+cTKgRVHxjKZmOxzOth5DyxjVxFNWFTAfytXaisAg36G9zLm017Tn6amgodHWY/6uyRwo46bKM2ivqUeAyXduW4DFLLm+zmY1EQZsI9FTSjbnobTTaVYz5xyNTXJPGnyE8wVy3OwOCT1XAXUr55MvHcfnpNWEMv3rkMIsvvSIjKeBukbd2sC1qScdR9lU6Tgaqu2y+d5y8boyJFMFnE/2uvbcdl/wfmDuuyo4LiZTQI/6l0aqqqJUDdbWHlO7ZK/kVLtXIr5C3XnoSv2raq90ysKxVP04DdGGqqYbDGGhcvNVLZEbFYaSJCSEKpONBTF8VV3lYsU1I1GRbPGRFulR5ssYZcgeXV9xitpg0NvMvni+qiVyo8J8QhZEUYHxSLhqT9VqXS02hA/Ogioy0qPQ0uJ7I9TcxBDISGV5b8xDddlsPg1lZ4MVlvhomKPUXFeGm1EsX7K192k0i2dOBSFp7blZbY7u19XRYEVgaDCCj7ynr2FuW6AxADYfeoT5enyN71EjW6h4AH3pEW58Pe9RQXxcqOQvco3qCucgH5yTKmBOKkORe5KNxhbDRVVA3RU2MANOq++DGae1GeEDM6GKjPRI8Yb7gvOQkQe87ipITWY4fptvHdfkQFioyWdkRF8gOsLQnpLUTbZmh3ioGU6t0fg72lDVdGPi+FQkJYWhvKxJDBjvjQjbJAwbmoAsRRU7hw2NR/+saFRWdm6JQDnLy5vEqBjdxwUCPCRGB2N4ZhTqm+3idfDARayyvgUx4RaMVVQ8huGXs2ZkyuLpffpM2SoqmiTXbMb0DCWyMUw1/ZKz4XY4xNPlLZutvFrCWlPPPwOqOGNOljRKr2/o7JlsaORpugFnzO2vTLbU8+fCGB4KW3lVp80SN5f0pqZfvKBHj2tvM31KuhS0YXivt2ycf/RsTZuSJjlTKmBV5OC0RDQXlMDNsrVHoCe1taIacVPHKtucjxiegH4ZUXJfeus4fl1R2SSv8T0qoDE1ckQiampaOh1k8vqWlVnlIHHSBDVViTnXWImb+btddVx5uVVyB6dPU6PjeLg6/4z+YpB6e9ja2w+1iKFF/awK6glGF9hr67sVWTIYA5H2/TOVyTZ7VhaCg4wyTt56hEYq9yTz5/VXVgSQayZ1WIUPHce1dub0zG5hwX3FpPEpiI8JkZB4b9lYX6SurgVjRyYhRdHBzaneR1Xl43REt6fRdIOL5s03TMZDj65DUVE9jMZAtPFEus2NrKwY/OwnE5R5kLgg3Xj9JNz/4BoUFjdIufX2U0M3kpPDpaqjyjYrl8zsj+qGVuSVN8oYsRImjdaosCD88IyBCFPYguCSi0dIMY8du8okr5HGl93RhohwM669ehzSUtWc6JPMHyxE/e5DKPl8JVrLq2AwmdDW2gpjWCgG3nglYiePVibbtKkZOJRdjUWLD0m1PYahOR1tMs/OPmsgZioy8En0+BEYfOtVOPjYa7AeykNAUBDaHA4x/pPPmYV+V12oTDYWULr2qrF4/pVtKCxqaB83l5u3K8aMTMTl3x+hTDYWixn919uw4/f3w5qdL7l5NFjdvFdHD8GIu29UpuMYBkr9+98H16C4pFHuU0Idl5oSLq/5CgvuCzgmP7lmvBxYsoo4h4ihj06nS+oG3HT9JGUbc8KKw8yhZW4glRzXC27MeRDHasmqPKrk3LMHI/twjeSqsje50RQAh90llcQvv3SkHMKqIuW8OajbeQAFb30Oe1Wt5KVSjzBHtd9VFyB5wUxlso0ckSBrFzsRFBY2wGRu17+8trNn9sPZZ6mr8s81k2vnCy9tRUFhA8zUcc42uU/YoubSi0cqk40G9A3XTcATz22We4I6gwf6NFoHD4zD1T9Q24JLozledHsa3Z6mR1ghduVqVkqslE3muDEpcoLIghCqYaN5FrnxFBXhYjZrRj8JcVVNs82JLdlV2J1XIzkq/ZMjMHlIPBKierff5vHMOZ70rt9YhE2bi+REmp7xWTP7KfOQe8MqqxUrNkmBoNbKGoRmpSF14Vzpbam6NyPV5M5dZViztkC8WqzUOX1qpvQEVd0bl7DwT/Gny6UIkDkmCskLZiBx7uRe9aYer47LK6jD6rX5yM2vk7DRSRNSMGViWrfiKKp69xZ/vBQ1W/fKpjxh9iQx8FW14vCGxuDqtQXYtbtdx40amSSHIux9rBp6LVnkhi1DmHtJI2vWzMxe72l5PHOOlek3bizCxs3FaG62o39WjMimuqcloRd6y5YSrN9QKBEarJZM/TtkcJxq0eSgpnLtVpR+vgotpRVSdT3l3NmImzbOL/QvCzhSj5SUNEqhoqlTMjBhXEqvHtocr47Lya3BqtX5yMuvlcq/kyelYcqkNKmErRq2RVq1rgAHs6vlcHX8mCRMm5wuVYD7mlO9Pc2B265X3p5myMPPnHLj923Rhqo2VDXfEfSc0/Qler5p+ho95zR9iZ5vJ5dT3VA9eId6Q3Xwg6efoarvPI1Go9FoNBqNRqPR+BXq4xI0fo27vgJtVYUwBATCkNgfhhD/OcVpKavs6HUYNWoIQlIT4S/U19skVIn5Kv36RSnN/+wKi3ns2VsBm82BlJQIKU6lOrTLg93hwt6DVVKVMC4mGIOyYvwitJbwWvKaMuycvemGD0tQlivYFQbGHDxULU3mGXo2cniCX4SdeWTLy6+TPCn2nR0xLEFk9BeKSxqQm1sr15IhrCzK4y+wByOvKxk8KFYq2/qjjsvKipYwVn+BaQ179lWg1eZEamqEpDb4i45j+sXuvRUiIyuz8rr6i2wMTd4vOs4mPbeHDYn3Gx3H/MqDh6qkjzrzoKlHempFpELH5eTWoqSkARaLSYqdqapE7Iui4nrk5dX5pY7TaI6Ff+xkNH6Hu7UFzg3vwp23A257izxnCA5HwNCZCBx3jhiuqnC12nHgoZdQ9MFXaK1pr1JojoqQCqhDf/UTGIMtShfTDz7ai08/P4ja2hbWLJK2MOPHp+Bn106Qoh4qF9MlS3Pwznu7ZbGnrGw1NHx4Am74yUQkKGp54WHzjlL876O9KKuwSjsf9nrrnxGF664YjUzFhv7efRVSMCO/oE5y8piznZYWKYVlWAVVJQUFdXjm+c1S8IltHFh8JykpHFddOVoKQak2tCgbD0bYUoqHDnGxobj4ouE4a/4ApRt05lk+98IWbN5SLBU6KQpbiLDoDeVTVUnUY8y8+r8dWLEyD42N7f01IyKCMHtWP/zoB2OUHkKw+vB7H+zF518cRF0dWyOhPS9vYiquu3a8VBhXqeMWf5mN9z7ciyovHTdieCJu+NlE5Yb+uvUFeO2NnSgra5TiWDy4GTQwFtf/ZCIyMqKUyrZ7TzleeHmrFClsLxYXIFX0qeNUFnry5Lk/+8IWHM6tkcrJ1HEsjHXVFaMxeaKaKtPetTyeem4z9u2jjmOLPAPi40KkkBIrEqvUcQ0NNjz74hZs3Voih78s7kgdt/DcwbjowuF+cwh8ysDxUjlmAafn9fKPozKNX8HF3rn6dbgPrGPvEBiikmCITARcDri2fwHX9sVK5dv/wAvIeeE9tLnaENY/A2EDMqTyb+6rH2Hvv59SKtvHn+7H/97cJQU9UlLCkZ4WIQUMVq3Kw8OPr/fZ06yvYCGg517cIqfliUmhSE+PQHCwCVu2FOO/D63x2Zu2r9h7qApPvrZNjNS4mBCkJYUjIsyM/dnVeOi5Taipaz8sUQErJT/w8FqpckovQ0ZGJGJigpGfX4sHH1krBTRUQe/ufQ+sxu49FVLkjNc0Pj5U2nE8/tTG9grPimDPT47bps3FUkSJ/WZZbIdFZLghZqE2VdCAeeyJDVi+Mleqr3LceL/S0H/z7V348OP2SA1VvPzqNnz62QGpXJuWFiEP6mU+x9dUwrHhGHGsRMelR8BoMmDpihwZU++WZn0NDXvOLXp72WLNo+M2bynCAw+tlTmpih07S+We5L3Je5Sy8Z7lvct7mPeyKqjDHnx0nUQ+xFLHpUciOipYigTxHi4s6ty2pi+pqm7G/Q+twb79FYgMD0JGWiTiY0NRUtqIx5/eKJ5zVXDN5Nq5ZWuxeFB5Tbm28lpyreWaqwruNbjn4N6DexDuRZKTw2Rv8vqbO/Hp59QvGo3/ow1VTTfcFXlwF+4BQqPEi8oTQfZYM4RGw2A0o23/arhtViWyNReXiyfVGBkOS0IsDIEBIltQXAzMsVEo+WwlGg8XKAup/XzRQRiNPFENFY8Mx46eELZu2LWrXEK+VC1aH32yX1o1cAPHvn7S6D3MjKTkcGRnV2PDxiKo4ovlObBa7UhOCBNPKmWjcZOSGIbiMitWb1In21dLssUzSGPBU6mWHi2GFHIT9eVXh5XJtnJVnrR+SU0Nl40Sx43hcNyQWJvs+OTTAz6b0fcFm7YUS9gqPR+cZ5SNoWcMd2S46MefHpB/VcBN7/YdpYiLC5E2DpSN9yu/5/gt+uKQzEcVMHxw5ep8MWJ4MEKvBx/8ms/xNW7SVXmhP//ikIwRx8qj4ziGcbEhMqb7D1QqkY1z6aNP9omuYz9XzrUOHZcUjgOHqsR7rgLeg598dkDuSd6bHD/KxnuW9y7vYVaNVcXirw6juqYZaakRHd566jp+X1ndhCXL1Om4VWvyJDyfoeU8dCAcv9TkcOnHTM++KrhmZmfXICU5XKIKeE25tnK+ca3lwbWqw2l6yHfuLENcfKjsQTw6jnsTkzFADr1UHk6fitAjrfpxOqINVU033GXZcDvtgNlHO5WQCLibG+Auz1UhGmq27IajrhFBsd3DpMzRkXBam+Q9Ksg+XI3qmhZERXcfN25IuHAxPEhViXrmqTDspys0DNvagF172Huw72lucWBfdhXCjxgz3nBh5aK6XZFsZMvWEoQEtxuB3vD70BAzNm8tVmYM0jAINLZvQLrKFhkRJDldqgwuhkvTu+Yrj4zeGhpbnJNqZKuUEEJe165ERVtQU9siodQq2HegUvIXIyO7h9DyOb5GQ1sF3JQzpYFj5EvHcUx53VXAuVRaZkWUjzZgnINtLreEoKuA4dsHDlbLPelLx/Ee3ra9RIls1F1sM0Rd5kvHhVjokVYjG9m6vVQ8gr50XER4EPbsq5RDYhVwzaSO89W7nWstPdFce1XA/HG2x/Ot44Jlr8K+vhqNv6MNVU13jmy6fedW8Dm+rmZj7pawMgOF8/06DHArOsHkgs/H0U69VIXFcZPGS2boIceBIquSrX3c2k8rfcF56KIlrQjmy/aUZ6Ry3Dyy9ThuAQYZW75HBe3j4vuzDQFfX3cVeK6Zr+vK8aRcbYqEc7ednPf0Bm1HuVc9Y6nqVuU15X+cWz2/R41w7dONsvm+VzmeavVIW09LqsisSocQjkuPshk4qm5l9+rRZRPhlN4PPe1E2nWcW9n9oNGcCNpQ1XTDEJ8BQ4ARboet+4u2RhgsYTDEpqsQDVEjB8EYHgJHXUO315wNVhhDLPIeFbChPKvpMT/KV3EUFoAYMCBWiWzJyeGITwhFgw/ZPOGXQxU1neeJb1Z6JBqaunv+uJjaHU6MGKRGNjJ6ZKKc2Hf1mvJ7FuEZNTJRWcGMkcMTxVPva5PbUN+KzMwo8eKogEViuMn1Fd5L2RgOz5A5FQwcGCseGl85i/X1rRIqx2rYKhgwIEYKAPnyhPM5hj/yPSro3y9axoZj1BWOZaAxQJlsDA1lfjvnVlc4B3mLDhqoRo/wHszMiPIpG+9dFmhjwScVUHexIFxTTzqu2Y5RIxOgipEjEtBqd/mMWqGnekC/GClYqALPmukrvJe5+AkJoUhOUlOkkHsNpgxw79FNtnqb7FVYDVtzghaTp6CSkgdOS07TP1tzNAzJg2BI6g93Yw3c9nbDRrwfzEttbUbAgIkwhKlRcOEDM5F4xlS0VtbAUd/Y4cV0NFhhK69C/PRxiBw5WIlsVPzz5vYXo4YLgWdhZfGCsjIrBg6IxfixyUpkY2jSuQsGy8l4TU17NUxitztRUtIouUjTpqYr2ygtmN0fQaYAVFQ3d5zes1VCSbkVcdEhmDVZXfXaM+cPkNxAhhV6jC7+y++Z/3bW/IHKZJs9K1MKszCvkQYr4bWtqGySqp2sYKvKiJ4yOQ3pqZES4uvZLPGeYFgtx++cswYqq147ZlQShgyOk8I2njwtysbNJUNr587OkvBkFfTLjML4cSkSYktZPDqORirHbvy4ZHmPCpgny7GhXByrDh3X4pCx5MadY6sCzqVzzh4sc4vj5JGNc486jkXQpk5Rp+MoG+9J3pse/ct7lvdufHyI3MuqWDB/IMJCzT50XKPkH581T52OmzOzn+Q/F5d+reNoGFZUWmE2G3H2WQOV6TjOJ66dnF9cSwmvLfN9+S/1r6+w4L6Aew0eFnLvwT1Ih46rt8keZf4Z/ZV2IdBojheDW1VylR/R0NCAyMhI1NfXIyLi5PSCY0hFRUUFEhISEBBw6p0HuJvq4Fz5CtxlOXC7HO0hJCYLDFnjYJx2KQwmdS0IaJTu/MvDqFixCa5mG9wGwGgJQtz0cRj999sRFKOuzD9ztF58ZasUufHkzbCoBzfFP795qhT56C2ONee4cL797m6p9ifeGjmkM8hJ/603T0H/LDWeEEI19NXqPLz72X7UN7bCcCRoKTE+FD/7wRiMGKy2PcL6DYVSTZQtL45IjNiYEFzz43GYMV3dBpPs2VuOJ5/eJJtKanOOJQ9NLr14BM47p/cM1ePRcWznw0qweQW10o6DcEN8zoJBuPzSkUpbwFRWNuGRx9fjwMEqORRhmF5wiAkzp2dKSw6VLWBYtOiJJzdi247SDiOf8owbm4ybb5ystAUM5Xn+pa1YvTYfLdRxBkgBmaFD4vDzW6b2aguYY805GjCsSLxo8ZFiWAYgMMAg0S6UjbpOFbwvP1t0EO+8t0eMBSlSaIAUG7vphknKPKoe1qzLx0uvbkdNjaf6sFtaSV179ThMnazGwPewa3c5nnp+M8ormO/ZHrZKA/ryS0ZiQS+2uToeHcfKyNRx1HUSguwGwsKCpAXMZZeMVNoChodHjz7RruM8BxDMJWebq+uuHt/nRnRv7LX7Uu7su25EeJA63dvY2oqB9z51yo3ft0UbqtpQ7RG3uw3ussNwVxUCrPqbNAiGmBS/aE4uJ4N7DqFm617JqY0eMxRRY4b6hWyeliasuscT4H79ojFyeEKvb8qPd86x79u27aVosTklXG7smCRlp75dqWuwYdvucgk3Yxjf2BGJsCg0GLzhxnfLtpL2YjKRFkyYkKLUYOgadrl1ewkqKprEEKTXLSYmxC/mG+8Btsmh14F9I8eMTkZSLx7YnAg0bFhYKTevVkLzR41IVN7P0lvHHTpULdVqCQ+7JJzaX3RcQZ0Uk+EBBEMIhw+L9xsdV1beiB07ymBrdYrHa/ToJDGm/QFGtGzdVioVgBkaOn5sSkc1cdXwgITF42hIM6KA/b+pT/wBeu15cMMe4IxkmTA22WfhLFU6bvuOMqlOHGxpP1BS3ZfcW8ex20BeXq2s8wzzZvshFZzqhurh36o3VAf8RxuqpyXaUNV8F9BzTtOX6Pmm6Wv0nNP0JXq+nVy0ofrtaDxNDVX/OMbTaDQajUaj0Wg0Gj/EYGBvZnUHFgaFn60SbahqeoQFMzZsKsLhwzUSFjdiRKIk6PtDmKjd6cLu/FrkHGl83y8xDCMzY2Dx0bOxr2lzOFC5dhuq12+Hy+5E5NAsJC2YCXNkuHrZ2tzS55B98zyhv9Onpvd6mOhxIcEdVsBdA7iZ+2YBDHHt//oBjdn5KPtyDWwVNQiKi0bSWdMRMTgL/gDDudeuL0D5kdDfSRNSMWiQf4SJ2qpqUPrFalgPFyIwOAgJsyYiZtIoBAQG+kWo48aNRcjJrZVccobF+UsoPMO5N28p/jr0d1AcJk5I9YswUU+oI9MbmPvGCslTJqdLSKZqWIztQH4tdh2uht3hQnJcKCYNS0CkH4TpM4CtbucBlC9bD0e9FSFpiUg+ZzZCUtXmp3pgniXz8evqbYiJDpZrqipM1Fc497r1hVKMij23J01Mw8ABMX6h4xjOvXZ9oYT+soo9Q3+HD6MXVr1sbkczUL4H7sZSGAICgZgBQOxA6eyg0ZwK6NBfHfrrk7z8Wjzw8FoUFtYf6XXolvyjEcMTcMdt05VVxCQ1ja144cuDyK9o7OhRxiFOjwvDNWcOQoJC2ez1jdh21z2oWrcNbfb2YkqGgACE9kvFuHvuQtSoIb322ceacyyC8uQzG7F2XaEUfRLZDJCKijfdMFkqjSpDGlfmAe4ybjW9XjABAVlAgLr2CFSRuS+9j4NP/A+OusaOVsKmyDAMvP5yDPjZZUo3SytX5+GFl7airo4Vutv7DDNPav78AbjmR+PEAOsNjkfHVa7dip1/fBDNJRXtsrkNCLSYkbxgphQ+C7SoMx5ycmrw4CNrUVTccKRCrAFGowGjRibh9p9Pk4JUKjflDzy0FtmHazp6HXKMBw6MwZ23zejVomzHgvmLDz26Drt2l8HpbO+Vy/mflhqJO26b1qtF2Y6p41jM7tN92Ly/Ug4zeU15Z8ZGWvCT84dhRH91BePanE7su/dZFLz9BZxNRwoWGSCHXiN+dwNSzz9DmWyc/+9/uBfvfbBPDqjbu5NCcvBZ9Oz884Yo1XHLVuTgpZe3ob6h9WsdF8yKv4Pw46vG9ppBeDw6joe+Tzy9EdXVzR19oc3mQEyfloGbb5gsX6vCXV8I9+63gKZq/jXtE06M1YEwjLocBlPfHlCf6qG/Ob+7WXnob/9/P3HKjd+35dSzoDS9Do2YRx5bj4KCeum/ybL+LDASGxuCHTvLpPqpygX1jRWHkVPWiNhwC1JiQ+QRG2ERw/X15YeVNiff99/nUbFiI4LiYxE+OEseIf1S0ZRbhO13/xfOJk/V2L7nw4/3YcXKPPF6tF/TSKSmRkgrh8ef2oDKqiZlssFdAbhLj6ikYMDABZQHDi6gLQdwt3vOVVC5egsOPPwy3K42hA3KRPigfvIvOfjYa6hYvkGZbCwC9OzzW9DcbEdaWoTcp+npETAHBeLzRQexZNlhZbLZKqqx448PoqWsCmH90xE+iPdDP5iiIlD08VJkP/2mOtlsTjz8+HoUFjUc0XFRcj8wsoAbz5de3aY06uGJpzaKJ5XFdtpli5KvWb2T96rK82WOzbbtJTJWnrWBY1hYVC/rhq/etH3Fp2vzsG53OcJCjEhLCEMa+1jGhaCm0YbnPtmL2sbufUz7ioJ3vkDuax8jIDioXY8M7oewARlwNDZh9/89gfr96u7VTVuK8dY7u8UIpP6QOZceKXPx9Td2ivdcFYeyq/H8i1ulMJZHNv7LqIdPPjsgRqzKyuG8H1lgj2upZ13lGrtiZa6suapwO21w73kHaKoCQmKBsCQgLBEwhwOV++E+9IUy2U5VeFbD6FtlDwNOS7ShqukGNyEMAUpKCuvkjWHIGfvobd1aIiEuKsivsOJwaQNiwswweclmCgxATLil43UVtJRWoGzxGphjomAM/dqrG2A0IiQzFdacIpQtXa9GthYHli7LQZDF2Ck8jyfR3GRWV7dgzdp8JbK191ThRsgNGExfa2P5l6eXTqCNHjk1FL63GK6WVgQnx3d4FfivJTFOvOYF7y5WJhs3Q2x6Tw+bx6tA2egNNAQY8OWSwz6b0fcFJZ+vREtJhUQTGLzCfE0RYTCGhqDog6/gaLQqkW3L1mKJFklO7q7joqIs2LS5WLyaKth/oFIMUrZ58fbG8Gs+x9f27a9UIhvHhGPD+eUdgswxTEoOk2rnrIytgmabA6t3lsISFIgQi6njed4XSTEhqKlvxaa95Upka3O5UPD255JjxvZpHXokIAAh6cmw19aj+KOlUMWSpTkSJs3DaG8dFxcXIpE4Kg+8qOOs1lY5qPGWjfcpWfxVtrKDG7b0oSeVa6i3V5drLNfaJctyOnqY9jmV+4GmynYjlV5UD8YgwBwKlO+Gu6VOjWwazQmgDVVNN3gyzpNUXyEr4eFmNDU7UFRUr0S2stoW2J1tPnNR+ZzT1YbSGjVey8bDBbLxNkV1z0UNMJvEILMeLlAiG/N6GDbF69dNtgCDnNbl5atatJyAm6FwPnJmZGNCNaXGoCHMKQsM8x0iZQwLRd3O/co2SgwNNZtZ4KH7USvzuNhHr0GRF6nhYK78622kejBFR6C1ph7NhWo8NYVHwn195aKGhwcd0XFqDrwYisyoFoY2doXP8TW+RwWFhQ3SOopj1BWzKVDGVNXaUFHbAmuzA2HBXxupHjxGRHGlmqgRO+d6UbnPtYH3boAlCHW7DiqRjdcs+3A1QkN85xez72Z2NkNH1XlU2UPYl46jQVhaapVWPyrIzauTueUr9Jj6l2HylRWKIpWs5e2HwL5yURny67QBzWoOvDSaE0EbqppumM1GyQLxtflm4Qw2UOd7VODxovqK7pVm25S/l3LyjkVgkFkKxLgd3UPfOJZ88D0q4KFDQKABriNNv7viboPCfqW8XkcSP33C59Xl+QSGWOCWfDffeWfGkGBl+Vs0XJwu3+PGQxsWQaMBoQJjcPCRAlnd4T1iMAYqy1ENMtOoOpaOUzNucr0MvofO8xzlVwFDygMDAmSMuuIZS2XjZgwUg8HpKVzQBWZdqroXmJcdEBgAt9N3WDSf56GXKmgIUl/4gtea3kFVBIeYfM43QpmZV66qRy6jCnqYbl/r3yBFa1fgkQMbn4rE1R5LGtD9UEdzFHggofpxGqINVU03xo5OQmiICfX13T0xDBGNjw/F0KFxSmQbnBqJiBAz6n2coDY02RFqMWJIWpQS2aLGDEVIRgps5e1VOr1x1DWIQRM/c4IS2ZISwzBoQCxqa23dNucMTeKCOk5VMSUDN+YxRzyr7u4WtIQEx6qRDUDKObPham4Ro7RrOJ+rqVmqdqpi8qQ0uZ6e4lge+FxDfatUnwwNVXM4Ej9rgkQSOK3N3WSzVVQhcvhAhGalKZFtzOgkhIQYfXqbGcqXmBCKIYPV6LjRoxIRGW5BTc2Rgjte8DmG3Y4elaREtqGD4yQEs9pH1EpDQ6scnHBsVcBc1MykcNQ12rvruFYnjIEBGDVATTElU3gY4mdNFM+qu4tl42q1i95Lmj9ViWw8ZJsxLR0tzY5uaQL8nqG/M6a15+SrYMqkNInwYqVpb/hcY0MrJo5XVwmbXRACjYZu4b1S3bnWhoEDYpGYoKjwWewgINAMsOpvV2wN7SHBkWr0r0ZzImhDVdMNFis4Y05/yQthyCgXKuY4lpQ0SiTmxRcNR7BXDlBfEh5swrwxyXJaWVlvQ6vDJY+qBn7dhtkjkxDjIyytLwg0mzHwxitkc27NKZQNOnMbmadnr6pDysI5iBw5WNlmhNeNIXsMaWRlR7vdKRvfioomqfjLRVcZATSSed1a6F5oN1DdPIywAYaI9jY1iki/ZAEihmSh6XABWmvqZGPJDSe/Dx+YiYzLzlEm2/SpGRg2JB6lZY2orWsRg9VqtUuIJg+ULlg4VJls8TPGI2HOZLSUlMvhjcvWKoVjmrLzYY6MwMAbLlfmic7qF43Zs7LEuKr00nHMvWd184u/P0K8TCpgkaLzFw5Bq92JsnKrFCfio6zMKvcsK7CyVoAKOCaXfH+EeFU5Vhwzjh3HkEY/x5RjqwLOpfNn9EOYxYSSymY025ySd1nb0IrqehvGDIrDiF6sSHws+l97seS5W7ML4KhvFD3SWlWD5rxiRI8bgeSzZyqT7az5A6V4UnFxo9wT1CP1DTZZKzIzozDvjP7KZJs5I1NabXH/wcrmlI1tpRhiTiNw4Xm9V0n/WHDdHDcmBZUVzbKW8v7k2spxi4gIkntFWbXkiDQgaQxgtwK2OsDlAJyt7XmrgUYYsubqFjUnisEg11PVA6dpNSXdnka3p/GJw+nCRx/vl2IsrGjHKJGUpHBceP4wzJndT2mpek7ZNXvLsXxXqbSqocMtOsyMWSOTMWtkkoTtqaTk8xU4/Nw70jeSp+dBsVFIu3iBtDLpzdDf45lzO3aW4q13dyM3txYulxuhoSbMmJ6JKy8bpczz1gEr+7YVHKnwy5P9I57WgAzAoLYHojWvCAceehmVa7aizdYqOWVx08Zi6O3XSEVbldBA/d8bO6XnMQ0H5l0OGRSLK68YjcGD4pTON4e1SSojl3y6XPpGMtyXRv+gW36IhJkToRJ6aNiSgwVPuAFm8anU5HB878JhmDUjU6mOo7do0eJD+GzRQTECSUJ8KM49ZzDOWTBIaX9G6t9Va/Lx4Uf7UFzaCHebWwrbnDlvAC66cFiv9qA9njm3M7saH63ORWG5VcYxNNiEKSMScdHsLAQrS29op3bHfhx85BXUbN8Ht92BwNBgJJ05HUNuvxqWOHVGNCkqrhc9smNXuRhcPJRgT+EfXDEGKcnhyvuUsvrwxs3FsNkcMJkDMXRIPH5w+SjxWqqcbzwYfPOdXVKMsKmpPTqJLZouu2QExoxWePgrEb4OuPNWAMVb2g1WbuRC42HoNweGpFF9Ls+p3p4m94+3IkJhS7UGWyuy/u+xU278vi3aUNWG6lHhxpfFlehlYNl1VbkgvnA421ByJAQtOSZYcpT8BYaJNh7Kl6qwYf3TJPSr1z/zOOccb3lW56SXhiHBKvtFdkPUEUMeGWYbpNxA7UpzcTlaK2tgjo1CaLraTYivzVxlVbMcPqSmRPS6oXUiOo5VTZsKSiVXj+19WO3UX6AHhN5BhoYymqS3+s5+E+it5L1K6PFS5eX1BfMGCwrqJLqFPVRZdKe3Od45RwO1uNIKu6MNCTHBCO+hUJAKqH/ZrszRYEVwSgIsCerSGnqq7My0n+goCxJUha32QFV1s4Tms4gSjWd/0nEsnMQICIYh815VedDVFbejpd2TSg9qWCIM3lWA+xBtqH5LOWynp6HqP6uexi8JDjb1qlfm2xZWyvSzhdS7JU3ksAHwR7iAZmaoyeM9JrK4920T8hMhJDVRHv4IQ0b58EfM0ZHy8EcYSeCvOo6G6aCB/mXIeKBB37+/Wi9gT9DjnJ6o1gt4NP2rOgrjaCQlhsvDH4mLDZGHP8IDX7869PXCYAoGojJUi3HK4+lnqvLzT0dO0z9bo9FoNBqNRqPRaDT+ivaoanqkzd0EV1st3GCOlAEBhnAEGmJg8INwzLz8Wny59DC27SiVHFVWmpx/xgAM9IMTfob/LFmZg41bSqTwAytlzp+ThZHDElSLJvnGXy09jNVr8tHc4pBcGhbKmDwxVXmoUovdhR2FtdhdVAdrqxNxYUEYnR6FEalRyvOOXQwlbLShqLEVLU4XLMZApIUHIS3col42VxtWry3A8hU57UU8IoMwa0Y/zJ/XH+FhQcrDHDdvLcHSZYdxOLcWIcEmKQBF2WL9wPu7Z2+53A9791VK6O/kyWk4a94ApKSoD6uq35uN/Lc/R+WqLfJ9wuyJyLjsXL+I1GDOdsHbi1C+ZB3aHE7ETByJjEvPRuyk0apFk9BQXtO16wokdYVeX64NEyekKNdxLAK0ZGkOVq3Jk6rcaWkRmDunP2ZOz5D0GtVh5stX5GL5ylxUVTVLdec5s7Mwd3aWspZDHhhevnZTEVauL0BJuRWREUGYOTkdc6dlSocC1Tpu46YiLF2ei5zcGgmBnzk9U3K2VRU982b3/kosXZWL/YeqJbd38rgUzJ/VD0l+Go2m0XRF56jqHFWfuNx1cLWVwA0XDNLDkl3o2mCAGcaAdAQY1G0yd+4uw8OPr0dNbYtsfLn5aG62S9jNLTdMlnL1qsjNr8X9j61HaXkjgi1GufY0CCnn1VeOEYO1tzjWnKuosOLf962SQkpBQYEwmgLQ3OyQvGNWBL780pHKNnItdife3lSA/KpmMfxMgQa0OjnfgDEZ0ThvdIqyAjI0UrdXNKLM2iryUL72tn5uJIaaMS4xQpmxyly8517Ygi+XZMvXbA/SanfB4WjDiGEJuOvOmb0WjnY8Ou7d9/fgnff3yIENN3DMa+SGuF9mNH7zq1mSI62Kpctz8PyLW9DU7JDNLq+zrcWBxMQw/PqXM3u1SMuxqFi1Gdt/dx9aq2phDG/vr+lsbEJQfAzG/vtXSgtR1e0+iC13/BPNhaUwhobwhhDZTOGhGPGHm5F+0Zm99tnHmnPMr/zPfauQl1fXruOM7TqOhtZll4yUKqyqYA7jvfevxp59FTCZAqQXbkuLU/Qaq+7+9LoJynQc6xU8+MhabNpcDA6rxWKS57g9nDI5Hbf/fJoyY5UHcc+/sQPL1xaIPMwBpT6h8TpicDx+ecNkhPVSIcBjzTfK89Y7u/HeB3ulAKXoOAd1nAtZWdG4+67ZUn1dFUtW5uLlt3fJgU1IsFn+npYjtSl+dfMU9Evv2xSgUz1HNf8vP1eeo5r510dPufH7tpx6FpSm13G7nXC1lclG3IAgGGASA5Vfu2GHq620W5+6voIL1AuvbJPS+RlpkYiPC5WclfS0SKm+9+Kr20Qpq4Bj8sqbO1FabkVaSoTIFhsTjLSUcFls//fuLikGoYq339uNnJwapKSEy2acHi2OGzd0H32yH4eyq5XJtjG3BnlVTYgKMSEm1Ixwi0k8qiFmI3YW1uJQOSsBq4GeVBqpFmMAQs1G8aaGmQPl3/ImOwobbcpk27a9RIxUFhdJTY2QHNXkJOaYhYm38JPPDyiTLTevFu9/tE9yyTnPON/YTiIlOUJeY6VMlYWnXn19uxRkS0+LQGxsiFTVTUuLRHl5E156ZZsyHcc2Pnv+9RTsdQ0IG9QPwckJ8uDXbIu0559PyXtUwDHZe8+zaC4qQ9jATASnJiI4KV6+brM7se++52CrUKdH3nhrlxzEdeg4rg3pkWIYssIzPV6q+OSz/di9t1zuTd6jvFd5z/LepQeY97Iqlq3IESM1Ni5YogliYvhvOKJjgrF+YyFWrMpVJtuWXWVYvq4A4SygxHGLChZvYEJsKHYfqMSi5TnKZOOa+dEn+2QN7dBxiWEydlxr3353tzLZKqub8b/396DN5Zb9CPci3JPwa0Z9vfL2bmU6TqM5EbShqulGGxrglqqrNFC/PuHl1zRa3bDBLZVZ+x4u9KzQmRAX2sn7x6+50SyvsLaHAysgr6AOhw7XIDbG0ulknLLRmK5vaMXGrcXKWphs3FQsYaFdq5qytQS9Dus3FCqRzdXWhp0FdTAHBsDUJfwt2Bwoni6GA6ui6Ign1djlRN0Y0H5HFDWoM1RXr80XLyX743pD70dwiAmrVuWJB1MF6zYUornJ3i38jfOPXt6t20qUHdxwU86WNNQZ3nqE921cXAiyD9fIQwVsgdSUX4LgtKRuOo7P8TW+RwX1uw+iftdBWJLiO1VuFtlSE2CvrkPZ0vVKZONc4pyiPuuq4zgHWd15w8YiJbLxHly5Kl88bl09k7x32SqJ97IqGPIbEGjo1h+9PWIJWLEyT5ls6zYXyUFvV68px9ESZMSqDQWiA1XANbO52SlzzhvOP661bBnGtVcFm7aVoKHRLnuPrjqORuuhw9XIL2qvKK7R+DPaUNV0x92+sfU2Ur8mQEKAATVey9o6m5wQms3d06ulf58b4m1VQV19e6N0Lp5d4eJgOCK/qrAzhoR23YgQLmLcc1bXqFlQWx1tsDlcMPfQFoS5g3WKvOSkxeHqMbSXz9tcbcpOpisqmnrsW8nQc4a18hBCldeSvUl9hZNTNnurC3WKNnGyeTS035ddYWhhq90phqwKGO6LtjYEmruHM0of5ra29vcoko3eXGNo99w7Q2D7PGT7JhVwLjHkktevK5yDfKg6GKGRzBQQX/qX8B7mvawC6q7Kqia5J33B9aziSC9fFVRWt8Dcg46zBAVK79Jmmxodx/lEA78nHcc1l2uvCur4uT3puCAj7A6X7Fk0x4+sZ4ofpyPaUNV0x2A8kpPqa/PNvEFOGzUFDKIi272VNAi7wlNpHv9GRqgpER8VGSSnvDYfHizmD3I02ZtOBfRgMSeKzdJ9bVTa2iCnrCoIMgXAYgqAvYdTceYiRQWrK5gRbGz36vrCxZypwABlub3Mf5J57wPmItGD0xf9LX0REx2MNrfbpxHP/DfeK1FRauZcVGSwHGrxvvQtm7Gbl6SvCIqL5u4SLru922uuVru8Ju9RAPsHB1qC4GzufsDgdrnkWjOPVgWcSwzB5PXrJtuReaiqtQlbILHVW0sPBhXvYRYvUkF7xE+o6Atf0Phn5IEq4mKCJefeF7bW9rzQkB4OAHobhpa39XBQyfHkmquqZQ33Sj3quFanGP/cs2g0/o42VDXdCEC4hPjSa+ptrLabrg4YYIEBajaYo0YkIjUlHBVV1k6LA7+uqGqSBXXcmGQlsvXLiJKqw9U1tm6LQ1VNMyLCgzBpXIoS2aKjgjFpYqo0cu8aJkXPUUiIEVOnqOntFxgQgFHpUWKo0ijtWgmYRSyGp6rrwcnqvryaTlrzXjh5+OB2I03RwQiZMS1TwswaGzufjPO0vKXZgdkzM6UXpwpYhCU0xIza2s4eBYbx8bR//LgUZYbD5EmpspGrrGzqpEd437LiKe/jgQPUGFxx08chNCMZLUVl3XRcS3GZvBY/Y7wS2aJGDUHkiIGwlVTC7XU/iGwlFWJAJ82bqkQ2zqVxY1NEn3GOecM5GBJqxpRJaUpk4z3Ie5H3JO9Nb3jv8h7mvayKuXOyJFKpqyHNeg+8trNn9VMm2/SJaeK1tDZ3PrjhONLgmj01o1uod18xbXK6HEB0jb7gGsuqzlxzufaqYOLYZESEm2Xv4Q11HKOnBvWPQWaaf/a29lvo0VT9OA3RhqqmGwaDCYEBSRL860arGKftD+bqmREYkKzMg0QvzDU/GofIcAsKi+ol9IaPgqJ62RRf+6NxsnCogGPy4ytGIykhFEUlDaisakZ1bQsKixsQYAjAlZeMlGIGqrj8kpHonxWNkpJGyeWtqWlBUVG9nJhfsHAoBg1UV+V0cv9Y9IsLRW2zHTVNdjTaHKiytqLZ7sSY9CgMTlJX4S413IKkMDNszjY02V2wOV0d/yaEmqVFjSrGj0vG/HkD0NhoR3Fxg1zTsrJGlJdZMXxYAs4/b6gy2TjXvnfBUPEW8V6lbKw8XVTcgH6ZUVJlWhUsZHPVD8cg0BggLX3Y0oRGa2FhPRITQnHNj8cp03HGYAtG3H0jzFERsB7KQ0tppTys2fnyHF+jV1MFHJPhv70eIWmJIg+NU1t5FZqy8xFgMmLYr34CS4I6PXLl5aPQr1+U3Auca5xznHucgxddMExa1aiCOnbY0AS5N3mPUjbKyXuX9zAPblQxb25/TJyQiuqqFlkfKBv/ra5uEeN+jkJDdfzoJMyeki75lsVljRK2X1bZJAfTwwfH4ew5/ZXJNmhQLC48f6isoVxLOW7l5Vapo8G5xjVXFazjceX3RyDAYJA9CPciLLDEvQl13I8uG6W8XZNGczzo9jS6Pc0x+qjWwI1mrz6q0TAY1G3MPbBqqKePKqfwmFFJ0v9QZUsJD2xNs3RlLtZvKpaS9UMHxWHe7CyMHpHYq597PHOOeYNLluVIH9WmZjuy+kVLj8Epk9OUL1r0nm4v6NxHdUyG//RRLfLqo8pw4NQjfVRZVEklPL1fs64Ay5bnyKacPQZnz8rq9T6qxzPfeG+ycNHSFTk4fLhGDpHYY5C9e1V5U73ZtdvTR5UtQwKln/BZZw5Eqh/0UWUbGPYqrVzd3kc1fuYEZFx+LqJGDFItGqy5hSJbGfuotjoQO3kU0i89B3GTe7eP6vHMOR5cLmGv6LX54hEc2D8W8+b1x6QJqX7TR3XlqlwprsdKsWfM9Y8+qgyZZvXfFStz5ZCV0Un0tLKPqqqoDA+MtFmzsQgr1hfI+sr0npmT03DGdPZR7Z3WNCei41ika8myw7IvoTwzZ2Ri/hn95UBMNbv2VmDp6jzsO1Ql4b5TJqRi3sx+SFbQGuxUb09T8PfblLenyfjTw6fc+H1btKGqDVXNdwQ95zR9iZ5vmr5GzzlNX6Ln28nlVDdUC/9PvaGa/sfTz1DVd55Go9FoNBqNRqPRaPwKtfEcGr+HoVPMt2Cl3Yz0KGVFC3zBvKOCwnoJvWEIlerwJG/anE40ZufD7XAiNCsNpjB1uald4XhRNleLDaHpyTBH64IKxwvzGJkjxUI8CQl9Hzp1NGqZg1TVJP0Gk5PDlYc5etPYZEdFdbNUwUxNDPMr2ajjmDcbGOh/Oo7VzRnOTajjuvbgVB1yXlBYB5fLjbTUCGW1AXrSccw3ZrGd5KTwbn2GVctWWtoIa5NdahZ07TOsGub2stgZiwCxqrg/wdQVhnb7o45raLChrNwKi8WE9LQIv5KNLcq4j6Nuox7xJx2n0RwL/9nZa/wKbkI+/nQ/vvgyWzbA7N+UkhSG710wTCoAqlTCXOi/XHIYn3x2QBZVwny3c84ejPPOGeyzb1hfUrJoJQ4/+w6sOQVwu9oQFBsl+VsDfnapz96IfUnlum049MT/UL/nENxOF0wRYUg5fy4G3/JDmML9y/DyJ0rLGvG/N3di2/ZSMR5oMIwZnYQfXDFaeT4jDec33tqFDRsL0dLihNEUgKGD43HFZSMxeFCcUtnY3/CjpYexZlsxmlocUuE5MyUCF84bgNGD45UfdH348T58teSw9Dem3uC1vOjCYZgxPUOpjmNlzi++PITPFh2UwxFCo2HhOUNw9oKByvUvc9w//Hi/bH4pK9tunTl/gKwPPfX17St4j77z/m7k5taKER0WZpZiQJddMlK5MX3wUBXefHs39h+shNPRhuBgo1THZhEoVdVhPfBaUsft2FnWoePGjU0WHUdjX7WBKjpuU5Hk0ppMARg2NB5XXj4aAxQWyPL0yH3rnV1YxboPTQ458KJMnG+jR7EopVod9/6HeyUvmocP1HE8VLroe8MxY1qGUtlORVT3MjWcplV/taGq8clLr27DZ58flMUqOtoimxGe7D/xzEbpacZNiSo++mQ/XvvfDrZMPdLrsL2R+0uvbJVF44rLRimTrfjT5dj554fgstml+qUhMAD22gYcfOQVtJRWYPTfble2yazasAPbfvVv2GvrEZQQhwCzCY76RuS88C6a8oox4aE/KDek/RFWhP3PfSuRl18nntSYmGDpR8vNekFBHf70+zOUeR54Un7fA6uxZ28FIiKCEBNjkSbzW7eViLfr7t/MRv+sGGUFUJ5+ayc27ylHSLAJUREWOQA7kFeDx99oxK1XjsUoRcYqja3nX9oqxqAlyNih4/Lya/H4kxtEThaSUQU3l9yYc2Pp6edaUdGE517cIkXQLvn+CGWyrViZh6ee3SSbYN4LlJGFgV5/cydqaltw/U8mKtNx23eU4oGH18JqbRXZ6DmyWu14/8N94u268/YZyrxJObk1uPf+1dL+KDrGIq1Dmpod+GLxIRQV1+MPv52rrOcxD0P+c+9KiVDivRAWFiyRBitX54ke+dPdZ0jPUFUFqDhu+w5USqE40XGtLmzeUiJe89//dg4yMqKUyMZ74MFH1mHzliI5EKFs3B9RHxcWrcWv7piBUSN7t4ji0XTcM89tlmJxluB2HceDGxZ8evzJ9XC3uaXok0bj72j/v6Yb3HyzgigVL5uQM6SWJ9EpKRGi3N79YE+Pjct7m/p6mxiq3GwkJYVLmI3FYkRiYpjIKR6IqnYPRF/jsttx6Mn/SRXMsP7pMIaFIDDYguCUBJhjo1Dy2QrxZKpatLKfehOtNfUIHZApnlS2uLAkxiE4JQmVqzZ3VBfVdIYLfX5+nZxEs3k7D28iIixIS4uQjd2XS7KVybZ2XQH27a8Ujwc9MmazUSr9pqdHyObzk08PKJNtz6EqbN9fgdioYMRw3EyBYrCmJITJBv3j5Yc79QntS7hZY+VV9jbmIYNHx6WmRoiB/e77e9Da6lTmPeJ14zxLSgoT/cYHv+ZzjCRhlIsKOCbvvLdb+pRyrDhmHDtWiOVY0ojlgY4KOJd43Wik8t4MDTWLbDSw4uJCsGlLMXbsKoMqGKHEe5L3Ju9R3qu8ZxnCum9fpdzLqli8JFt0GceNuo3zjLqOOi8/vx5Ll+cok23NmgIcOFiFlORwRHl0HCuup0XI4cOnn6vTcVu3l2Lb9hLEJ4RKhV/KxnlH2bhX4XxUpeMOZVfLYWpklEVCzHkv8CCE9y095m+/t1sMbc2Je1RVPk5HtKGq6QYXc3pqIiO75/Vw0ediu39/lbJ2EnV1LXJa3hXm+rB5+s6dajYjdTsPoDm/BJak7uGWpqgIOJtaxCBUQXNhKep2HUAQvbxdvB3G0GC0OVyoWLlJiWz+DjeQXOS7to/g99yoszWMKjZuLpLr2TV3kc9FRAZh6/YSiTJQwc6DlXC42hBsMXaTjd7V3OIGlB4Ja+1rGN7Y3OwUL3RXYmODZQPMzbEqHVffaOtBx1lkA7xTkcHFMSmvaPLpXeNY0gtHr6aq0FV6LTluXXUcN+gMtVUlG+9BhiTznuwqG+9dbkB5L6tizdoCBIeYfOq4oKBAafWjivWbCsVr3zWknM/RYOUBBMOBVbBtW4l4KYMtnT3hvMb0mmcfrpae5ap0XEurE+Fh3aOkeP8yT5rGrEbj72hDVdMNjyfBV/gWPZnsKWm3q1kYWu1O8IDSVx6q5zmGPqrAZWtFm8sFg6l7RD3Hkg9Xq12ZbMxJDTD2EO0fYICr2dbXYp0SsCBLT+GCfJ6bJFWn5jxQMgb6PmU1BgbIJsqu6NTc1uqSZvO+oMwqZaOOYLcJ3zou8IiOUycb3JQNfqfjGHLpamvzeT94xtKuUjaXu+fQXgPQqsig4ZgcTTbeD7yXVUDdxTWf+sIXlFnVuJEW6rij6F+n0y39ylXQYnOKHjmq/m1Vp0d4R/rScTyAYKqDqntVozkRtKGq6UZaaqRsiHwpscZGO0KPhI+oID01EuYgo89FneHIPHVluJIKGO7LgkSOusZur7XZHbLz5HtUEJKaCHNMJBz1Dd1ec7e1sXoLwgery8nzZwYOiO1xE8nn+bqqnLyBA2LkPvVlKDda7UhMCJOQTBWkJ4fLoRI3RF2xNjsQHmpGQoyavLd00REGn6FvjMqgjmP4ngqov+hlY2GsrvA5vqZKxzFsNTTELGPUFY4l7wNVsiUlts91X7LJHHRTfjUVzultZng018+u8N6129vkXlYBrxmL/zD3uScdN2BALFQxaGCsGNK+dBzzj5OTwqQKsAr6ZUbJ3PKl46h/GT6tqn4BdRyvrS8jnuMWEmJWto87ZeE6r/pxGqINVU03xo9LkeIEZWVWKSrigYsFK4yOH5sixqwKBg2KxfAhcVLgxtuQ5iapsqJZFvsRwxOUyBaSkoikM6fDXlMnYb7erWoYEhyWlYak+dOUyGYMDUH6xQtELkfD16FIrErclFeE4KR4pJw3W4ls/s68M/pL6C8Ldnk2S/yXc5DVJ+fP669MNlbgDg+3oLzc2km2+gab5JOfNX9At3C+vmLyqGTERllQVtXUaSPX3OKQkLRZE1IkZ1UFE8anSguJrjqO3nGG1k6amIqkRDWVTocOiZdqzcy19/Y482s+N2RwnFQ8VQHHZNKEVNTV2TqFW3IMS0utYghybFXA3EAWwGpucXYKd+fcY5hjfFyIskqnvAcXnDlQ7km5N4/cq5SN9y5DWHkvq2L+vAGSQ15d01nHUedR980/Q52OmzM7C2FhQVJMrJOOq2+PAOK4qjoo5HzyhNF66zgagvRCz5ubpazS9KSJaZLXW1ZqlZxy7wN9ttKZNiVNclc1Gn9HV/3VdIMn9r+4ZSoefHgtCovrRQEbYECg0YAxo5Jw3bXjlcnGBemmGybjvw+tQfbhGrSJAjaIB3jAgBjcetMUZRtzMuzXP4GtohpVa7ehxXHECxdgECN1zL/uFINRFQN+eima8otR+sVq2Moq22PhDEBwcjxG//U2MbQ13Rk/NlnaR7zz3h4UFtIj7e7YGLMFATfuqmBF359eNwEvvry1QzZKx5ypcxYMkg2oKlhA6WeXjMKz7+1GSaX1yLC5Jeph2uhkXDBXnWwsTvSLW6eJjisubW+xwq0udcfYMcm45kfjlMlGXXbLTZNx/4NrcDin9sgG2I2AgAAMGRSHm2+crLQ9zTU/HicHlsyl5QaY0nnaXnDd4Niq4uKLRqC0zIqNm4rkIEkwGMRI5drAgjeq4L3IyvlLluWgoK5e5hsVMKs6X3v1eGXVucnkiamiy1htmpV02zFIQcXLLx8pbWpUelSvu2Y8XnplWyf9SwNw4bmDlVbnZi/tm2+cgief2oiioiP61w0x7ufM7iftmlTBvOxf3DoVDz+6DiUljSIXMRoNGD8+FT+6aqwy2TSaE8HgVpVc5Uc0NDQgMjIS9fX1iIg4OaEQbW1tqKioQEJCgmwwTkV4KsjejNk5NZILMnJ4gnhbVffJIzzN37ylWCqeksGDYjF5UpryPnmkzeGQCrpV63dIyG/EsP5IXjAT5qjeDbM5njnHMN/qTbukcBK9qwxFTjlnlrTS0Ry7Gva6DYXSgoOFu6ZOTkO/zGj4A2XljVi3vlAKd9D7QOOZ90RvGjTHq+PqGmzYsKsMpZVW8dqMHhKPYf1jpKeqatj6Yv2GQuTk1sJEHTcyEePGJPuFjqPnY9PmYhw40F7UaciQOPH0di3cogJGsGzbUYrdu8vhcLZhQFYMpkxJk2q2vcnxzDkaz7v3VkjxIkYBsTcuPV+8Z1XD7dbBQ9VSAKjJ2iqGzrSp6cq8911he6b1G4ukqnRMdDCmTUlX1vqlKyWl1HEFUsyRHmjeCzRi/UHH8VBk7foClJQ0SCcC7pMY2aW6pzthKDzXLVY6p44bNTIJY8cmwWQM/E7stftS7tJ77kREcJA6OVpakfyb+0+58fu2aENVG6qa7wh6zmn6Ej3fNH2NnnOavkTPt5OLNlS/pRwtp6ehqkN/NRqNRqPRaDQajaYneFah8rwiAKcl2lDV9AjDRJv37IEtOxsINCJ01EhYsvyjMiwDAQ7n1GD/kbC4wQNjpdCSytwtb4qK6yWHi0VGGB46fFi80txZbyprW7A7u0rahyTFhWDkwDgJCfIH6upt2LarDNYmO+JjQzB2VBIsQf6hphyNVlSs2ARbZQ2C4qKROGcyTBFh8AcY4shQRwn9DTVj/PgUREepD3UkrDq5c1c5iosbpCcjQ2sZ8ugPMP9z774KCYsLZFjc8ERllWF96ThHGx/thVBMAQEwsem7n+i4gsJ67N7TnqfaPysaw4b6R6gjqaiwSmgyW9awsunoUYlKQh19wbDardtKRMexKjfzP5nT6A/Y6xslLaS1qhaW+BgkzJkklez9AbebhcWYB8qqzhyvSBgM6sPgPaHw7D/P8GSuV9Rxqqr9doX35569FcgvqJMUrlEjE5UVw9Rovgn+oR0B/Pvf/8bdd9+N22+/HQ8++KA8Z7PZ8Ktf/QpvvPEGWltbcfbZZ+Pxxx9HYuLXRV8KCgpw8803Y9myZQgLC8M111yDf/3rXzD21C9Sc1w4qqtRfP/9aN69u721CguNhAQjYtYsJN90EwIsFmWysaLjU89ukhxVT+VJLvRjRifhlhsnIyLConTBevGVbVixMrejpQnz3QYPjsUvbp6qdIPOTflHyw/ji3UF0orA09s1IykcP7tkpPyrkq9W5ODtD/eitt52pAq7AcmJYfjZj8Zh5DA1lZw9lH21Fnv+/TRaSiq+LuaRnIBhd/0UKeeorZZMQ+vJZzZJfhQTOWjgsC0Ci6Oce/YgpYYN83offWI9cvPqJIyO8rFAy3nnDMbll45Satiwgu5jT26Qwy4HW/wcKUAya0Ymrrt6vBSVU0Wb2406uwOtLFZ0JDmHlzEoMABRZlOP/Wn76lDkhZe3YvWafDQ1O6QoEHUcKxHfevMUpZVEuSl/+93d+GzRQamxwGFiyGZWv2j8/OYpSvMteV9+/sUhkY+Vf1mgkPKlpETgpusnYbhiHVeyaCX23ftcu447on+DUxIw4nc3SDV7lbjdDXCjgLtCr2dNgDsFBoOaCtgeeMj12FMbkJ9fJ9e4XccF4fxzB+PSi0co1b88sHnkifU4eLC6oxUXddzc2VlSFM0fcvE1vc9jjz2Ge++9F2VlZRgzZgweeeQRTJ48ucf30wZ74oknxL6Ki4vDpZdeKnaVRdG+3y/cKJs2bcJTTz2F0aNHd3r+l7/8JT7++GO8/fbbWLFiBUpKSnDxxRd3vO5yubBw4ULY7XasXbsWL730El588UX8+c9/VvBXfHdwu1wovu8+WDdvQWBEBIIy0uVhMJtRu+gLlL/0slL5nntxC1atzoMl2Ci9DvkIDTVJ4Sdu2FWmXXMT8vmig+KdoVz0zkRGBol39cFH1vns29hXLNtUhA+W5YjBmhwfhpSEMMREWZBTXI/H39iBxh766PUFm7eX4KU3d4pxn5IYhrTkCCTGhaC03IrHntuEkrLuvWn7itqd+7Hzzw/BVlaFkIwUhA3IREhGqlR33vWXR1Czba8y2dja4qFH1qGoqB7xCaEy51JSwuUA5+XXtkuhIFXQULj/4TU4lF2N2NhgpKVFineLtycrKH/+xUFlsjHS4eHH1mPHzjLpccn7lO1qGFmw+KtsvPH2LmWySesNuwM2Z1t7JWJD+4Nf8zm+plLHcWwWf5kt3hmOGccuIjII23eWSoVR73Y/fc2ixYdkbnF4ONc45zj3DmVX4f6H18qcVAXvRd6TtlantA3hvcp7lvfuQ4+uk3tZFTVb94gus1XUIKRf6hEdlyI6j7qvbtcBZbK53Ta4kXfESGV+ICNFuGHm4VIh3O46ZbI1NLTiwUfWIienBnGxIeKpZPEudiN4+73d+HLJYWWyca/BPQf3HtyD8D7lnOPehAc5b7+3R5lspyoGRrQofpwob775Ju6880785S9/wdatW8VQpdOPude+eP311/G73/1O3r9v3z4899xz8jt+//vfQxXKDVWr1YqrrroKzzzzDKKjv66gyWRhDtD999+PefPmYcKECXjhhRfEIF2/fr28Z/Hixdi7dy9effVVjB07Fueeey7+/ve/y+kBjVfNN6Np50407dkDU2LC/2fvPeAjra7z4We6ZkYa9d7L9t4Lu+zC0rsbBgy4YIN7je3kHydf4iRO4ji2sbGNscHGFGN6X2B7770XadV7l2ZG07/fc0azqIyWJSDdEfs++U1Wmhm8Z+9777mnPgcGu/185s2YmAhDQgK61q+HrzVccjvWIL0/Rw+Q0p8MkxHZOCqEowcOHmqQkmAV4Fw3jh5gxDIpMe58JJVMxBxGT2OJ5WgqQGbOtTurJYPFsSGRTJbFZEBOul3Gh+w+0qRENhrdb60rh9fjR0a6/XyJNKO9uVnxaG13Y/OOKqhC9fNvw9PWKQac3hSu1OCftsJcKZWrfna1Mtk2b61Ec0uvGOVk1CW4fhkZdskSMoOjyqmhYV5d3SUZo0hpo+y/FKvISKeC2TkVOHK0EadOt0rpZWSWK88rM9E8vxs3nZMRLCrgD4XQFwhyqpVkTiM6jj/zPX7G76gA14Rrw8Bg4gAdxzXkWnJNDx9pVCIb99Jbb58RQ5x77LyOsxhlD1bXdEowUwV4Bukc8EwO1HE8szy7nBHKs6wK1GHUZbbCHOiNA3RcUa7ovurn3lImWwhtLErud04jJquu32nleKRmZTpux65q1NZ1Iyd7sI7jbFWeDepfVcFp2hq0OWh7RKYhUCbaJlabCes3lMs8VQ0fbfz85z/Hl770JXz+85/H1KlT8fDDD8Nms+Gxxx6L+n36WJdddhnuuusuFBUV4ZprrsGdd96J3bt345J1VL/2ta9JVvSqq64a9P6+ffvg8/kGvT958mQUFBRgx44d8jv/nDFjxqBSYEYKyNB17JgWLfq/wn36ND0bGGzDZ84ZkxIR6O6G+8wZJbKdLW+TrBvp6YeCxhMzSWfPtiuj9qezyuzCUPAS8wdCOHuWl+7Yo6nNidZONxx287DPwkaTDmerO5TIxvLBc9UdUZ8pL1azSY9j/WOIVKB1xwEYE8IBm6GysX+rdcdBZYbSkWNNMJoMUUtomSnknmTUXwVoJHFZmHkbisQkC1paXair71YkW7uUiUab+ZnoiEN3jwfnzqk5D+xL5bpFi53zPX7G76gAx/hwP9FJHQquJbOpnG+tAtxLLW1OyR4NBfdgKAicPqMmwNrV7ZEeQZ7JoeDZNZr00u+rAtRdrTsPii6LpuOo+6jj1IHVNJQr2ong+eW8XDXOIAMzIkU0HeeIE86AxkY1mXLaGrQ5ovU/84x0dvZJ2bKG8Yfu7u5BL7ZGRgMTdvSlBvpRbIXg7xE/aiiWLl0q/03EMa2oqMCbb76JG264AaqgtJGTvadMRbP0dyhYS202m5GUNLinhE4pP4t8Z6CTGvk88tlI4EMd+GD5oAn2UPH1YSDcjxX60P73xhR6PSg1X0MvrpBOh5BeL/1cKv5tcl2JSKEo11Yo/JlO3brT6NCJbMMNScmQ6Edv3S6053S6EPQ6eWrneyyH/NejKtuFwedGmSKv4TDoderOktEYfqJReo1EIqNBZFPRi8QYw0j7Lbyu4tqMytq9l46TsyAlq9GfKc8DP1eiR/Rh/RFNNjkr4diNEtlkXfl6V9mdR6QHLnxXjf1+498YntIxfM9RtnBPaEiRjgtnoDHicw3vSSV6JBTRD9HPKt9TpX8lyGYwhG+GKDpMTkq/jlOBcExmpLsh/J7oQJ0KHYeRddx5PaLGHuHfLTp2xP0WrtQYS9nGpT08AJHqFpV/P5Gfn4+BYJnuv/zLv2AoWltbpUUymp908uRJRAMzqfzvli1bJnvf7/fjy1/+stLSX2WOak1NjRAnrVmzZswbdNkU/K//+q/D3m9paRECpw/rQLJ8mQ96vM3f8hQWwl1WCk9cHAzWwcyh/q4u6KZMRm96Otwj1LiPJrIydSgrsQA6r5T7DgQzrSXFZuRmG0asvx9NJDr8mDTRCpfbh0TH4Gfu9QZQkG9EYYFp1GS70J7TBUOYmGdCS7sbSdbBF1cgEEJ2UgilWUYl60bMm+7AqfJ2pDgCg4zzYCiIgD+I2dPilcmWcMMyONdthy87FboB6ypKXB9AyspFojtUYNYMh1wqSUkMNAy+QINBP8pKU+BydcLt/vAv1/fScaWlFuSfMMLhCAzPOLR7UZhnh9nUp+S5FuaZUFBggs3qFybiodmviWVWJCcGlMgW4Lr6/P2O9JBnGgnQmYzoVXCvJCcFMHGCFR6Pd1h2kKW3hfkmFOSp0XEmUxDTptgkU5+SMviZMtObn2fEhNI4Jc+U8s6d40A5dVzK4HuLnAGBoB6zZzqU6bj4m5ejeeNu+HLSBhniDJhQx8VftVSZbKEQd727P2s68DyE+kuC46HXjU6l0nvpuIkTbDhzxoBER0BKzgfJ3e5FSVE8DHoXmpvHvsSWtgZtDrvdP4wYrqvLJ7YKbZaxfK49Peq4Jj5KqKmpGTRH1WL58Ga7bty4ET/5yU+EuHbRokU4e/as+Gpsq/ynf/onXFKOKlPLPCBz5849/x49/82bN+Ohhx7C22+/LWnrzs7OQVnVpqYmZGVlyc/8c2jdND+PfDYSyC7M5uKBGVVGKNLT0z+0IbqRDAv/N8eboxpKTwfeWYPurVthTE6GwZEg9Wb+jg6YXG6k33kn0ktKlMiWkQFMnNQm/T7xdrKbhg9od7dXyvVWXVGCadPUjdCZN6cbT//tMJrMQSQnx0mEv8fpQ2eHG3Pm5GD+vImjNqbmvfbc4rlB/PnlY2jt9SM5MU6ylO4+P9q7PCjJT8aiuWWwKhqTsPwyPfYc3oHmNjfSUqzSn9rX5xPHmuRKy5ZMRkry8FL0sUDctSvQ9eI69J3ZD2tOJgzWOATcfehraEJcagomX7cSDm5MBbh8eQI2bGrE4SPdSEu3w2oxwBcIoqXFhTiLCZcvnzYsmjpW+22JIxlr1zXiyNFW6dmy24wIBENoa3dLGeatN09BdvbIeno0kZaWjnWbmrBnXx2SE61IiDcJ025HJ6tt/LjjU5NRWJgLVbB5fHD5A+EsYP97kvEKATajAYkWdWM55s7pwjPPHoElLoTkJEtYx/X60NnpxoL5eZg7d8KosTm/555bMgWP/HEvmlpcSGUvtF4Hp8uPtjYXJk9Kw+LFk6OWe48FViwPYe++7Whs6kV6ug1Ggx5uTwCtLU4hpVq+bCqSFI2Usly7Et0vbYTn7H5Ys6njLGEdV98Ea0YqJl27EgmKdFwolISQOKl09ujkG/pPA51UO3QogU6XoGS/LV2cjLXrG3D4eDvSUmywWcM6rrXNLWf347dNvaAtOppISUnD2g1NOHCgHknJViTY+3VcR58Ezj9zx1Tk5+eMqUyqWGM/NISJA9T+/WBbj+OifBUy9hoMhvN+UTQ/aijojN5zzz344he/KL+zvdLpdOL+++/HP/7jPyrxZ5Q5qqtWrcKRI4OZFdnsyz7UH/7wh+I4mkwmrFu3Dp/4xCfk81OnTgld8pIlS+R3/vkf//Ef4vBm9CtRZmj5ANk0PBIYfYgWgeAD+DAfghBgfMj/m2OFvG9/Cw3WOPTs2AFvdbVE9g2JiUj99O3IuOPTgzJLY4177poDvU6PjZvPobY2HKHjyIvrrp6Ae+6erXS9b75pCgJB4M3Vp1FX1xs2LO0mLFtahPs+Pw+mfjKe0cKF9tzSWTnw+oJ4bWOFOIS8tOLMRsyalIF7b54Cu3V4/+pYgeNnHvjsfPz1xaNobO6VLAOd1UllafjCZ+YgLVXdWJ+kicWY+z8/wLGf/B49Z6sQ9PmgN5rgKCvCtH94AEmTS5XJlpJix3e/vRyPPLpHsjWt3oA4CZmZ8bjrjlmYPStH2X6z2yz43reX4feP7sHx481oa3P3E41Y8fFbp2HlimJlZVQU9+tfXiwM4vsO1KO6NjwSiUQjt9w4GbfcNFWpHkmKM0Pv9cMdCPSXPoZtFKvBAIfZqLT87Nabp0oVBsmwRMfJs2ZQpBj3fW4ejKM8r/RCe+6KFSXweoJ48ZVjaGhwih6xWo2YNy8XD9y3ADabOh3Hs/jl+xfi6WcOCcMvZWOma8qUDNx/3wI5y6qQPKUMc3/6fRz7z9+jt7wGQb8PepMJiZNKMO3/PYDECUXKZCOJUihU2j+ehv2oHPvG/R8HHXKh0yUq228cQ0Mdx+DIiVMt4qBSx6Wl2fDxW6fKqCtVZ9Vs1uObX12CR/+0D/sO1qOjPazjkpOt+Nit03DTjZPHXMeNR1t4PMNsNgsRLf2o22677Xzwhb9//etfj/rfuFyuYc+Jzi6hiotDF1LJcz8EK1euFPbeyBxVzkdlEy9HztD5/MY3vnGelSqSgeX3c3Jy8NOf/lT6UiORAKauLxbMqCYmJkqJx4eZUY040OP5cHpqatFXfhbQG2CfPh3GlHeZmVWDcxBPnQqTGUwoSxXjPFZAUqVjJ5ql5KyoMBkF+aM/YPti9xxLk0+ca4fHG0BWmh3FuQ6lhu9AeLx+HD/Zgl6nT+j+J5WlKp21ORBBvx/te4+ir6UdltQkpC6YIcZcLIBGL4k9mluciLebMH1aZlQSDRX7jVcMSXjIjsls1vSpGcPK9lWCclWca5fyZM6ypLMaK/AHQ/D193WZ9HoYY+QsEJ1dfTLDlzqupDhZRnOMNi52z3HW9tHjzUKul5/rQHFxcuzoOI9fiJOo48gAPGliWuzoOJ8PbXuOCNNvXHoKUuZPP88CrBphU7W3P5NKmRKg0+ljRsdx2kB9fU9Yx03LFPbwWEF1TZcQ65n6dVw0MrSxwGjY2mMpd9OD34fDalEnh9uDzG/9z/taP46W+exnPysjQDk7lf7Vs88+Kz2qrLa69957kZubKy2RBHtdyRT8yCOPnC/9pS9Gh5f/WyoQGxpoBPziF78Q5cCMKsmPyOjLuumBXv7rr78ui8jsqt1ulwfy4x//WKncHyVY8vPkFYvgYHmVw+UvBF4ESxcXIBbBURLzpo5OOegHhcVsxJyZ2YhF0GBLWzwbsQgaulMmp8sr1kAHobQkRV6xiDzO28yNTaOJjqlRP7oZyv8rkmJYxzEQsmhBbN5bDCDNm6uurPxCYOAtfem77VixhHCgYXRKfD8M2cpKU+UVi2CgfCyC5R95MFahMu+kf///yac//Wnh0Pjnf/5nSeYxuffWW2+dbwlilerAIMyPfvQj2c/8s66uTsreb775ZqleVYWYyqiqgpZR1fBRgLbnNIwltP2mYayh7TkNYwltv324GPcZ1V/HQEb1G+8vo/pRQExnVDWoRXNzL7Zsq8KJky1SFjdnVjaWLi1AQry6g3oeQQ8QaAQCnNlHev0UwJAF6NWX7bHsbMfOGuw/UA+Pz49JE9KxfFkhsrMSYqLsbPeeWuzZWydyFhUli2wsT9YwMhjPO3a2DbsON6C13YW0ZCsWzszG9AmDWTJVgfMht27n8PkuyeYvXpCPuXOzYRrlfsHxDhLbbN1RJWXT7BecPSMLSxYXID4GSpM7ezzYebgBxyvCc0mnlqRg8cxsJEWZNzzWcPb5sPdkC45WtMPrD6AsLxGLp2YiXREZ0HiBzx/A/v312Lm7Bl1dHuTmOrB8aSEmTkyLCR136HAjduyslhaCjIx4LF1SgJnTM2NCx8UyWFa7ZWsVKis7JJu/YH4uFi7IG/X2i4tBQ2OPyHbqTAssJiPmzsnBksX5MdV+oUHDhaBlVLWMalQcO96EBx/aiZYWJwxGHUL9Q+jZ6/P97y5DVqZCpyvYA/QdBoLsV4lcoCFAbwMsMwFDotK+2Z/9YivOnG0TAiqWZPr9AaSm2PC1ryzGnNmjV9b6Xnuut9eLX/x6Ow4ebBCjhHT6fl8ACQlx+MJn5wq5jYbo/Z/PvHkCa7dXw+sLj1rx+YMwmwy4cnE+PiPEO+oMudfeOIm/PX9UxjOxB8kfCMJg0GHJogJ89YGFo2YsjXcdd/hoIx56eJcwwvKZBvt1HEuUv/etpchIV9fzXlXfjd88exgNLb0w9K8tx9Zkp8fja7fPRGGOumh6a6cbv335GCobu8NEMzqd7LkURxzuu3EKphaNXtBrPO85Bgl/8/AuCWIGAsGwHvEFYLObccenZuDmGycrk43y/PkvB/DO2rMiU1i2oIxuuu7aCbj3M3Nipo92LHEx+23jpnN47PF96OnxwGgyIEg2RZ0Os2dl4zvfWCpEj6pw4GADfvO7nWhrp47jLNwQ+H/k9Pj+d5cLD8RYYtxnVH/zQ/UZ1a/997hbvw+K8aXpNYwJ3G4ffvv73WhrcyIvz4HcHAfy8hKRnZ2Aiop2/PFP+5Sxf4kl6TkedlJ1NkBvD790diDoCn/G2ReK8PgTByQ7k5UZL2uXk5OA/PxEIR55+A+75TJThRdeOibRfDISUqac7LBsJDD601/2o66+W5lssYw9RxrxzrYqxFkMyMtKEAKq/KwE2OKM4rzuPFSvTDYyTf7tuTB7Okdc8Izm5yUiKdGKrdurhJlVw3D0Or14+I970NHZJyRAOdnsU02Uc3umvA1/euKAMtno9D328jE0NPeKY5qdbu9/xct7/IzfUQHq/b+uO4tzDd3ITLYhJ9WOrBQbctLs6Oz14PG3TsLV51ciW6xj9dtn5EwmJcWJ3pWz2t83yHE/rFxSBVZjvL3mjBAARWQrKEgUYiAy2O/YVaNMtlgGK1h4d3LcS+ROpa1EB5B37YsvH1MmW3d32Obo6OoLy5ZD2Ryi42ijPP7EfmWyadDwfqA5qhqGgbMFGxt7kZkVPyiKyihrSopNRk1UVnWqES7YAQS7AV0cMJDxj6VJOisQ6gUCozP8+71QX9+Ng4cakJwUngP6rmg6ZGXFS3Z61+5aJbIxm7plWyXsdtOgOYKULTPDLpfatu2k/9cwFJv31so4DseQkvcEu1ki1Jv21KqTbWslXG6/zIwcWJ5Hg5MZ3/UbK6TcUMNg7N1XJ+WNWRmDdRzPbUqSVVhZa2q7lMh28lw7qht7kJ5ikzmgEfBnzhjmZ/yOCtS1OnGiqgNJ8RaZAxoBs6oZSVa0dPbh4NkwE7uGd8EzuG5DuZzJgWywPLM8u2Ri51lWhQ0bK0SXORyDdRzbCKj7Nm6qUCZbLIN3Ju/OjAz7IP3LO5Z37eatVXL3qsCuPbVic2RnxQ+SjTqONsqBg41is2h4H9DFwOsShOaoahgGznhj5DxafxuVr7vPL99RgqA7XOari1LOqDOEM64hfkdNvxvHIXCNhsJg0MtlwRmhKtDa5pRxCNH6UsJz4nRaRnUE1Db2wDrAuR8IDniva+pRVmFQU9MlvZXResg4v5cZw94eNYZSLCNyDhl8GwqeEdFxis5qc7sbgWAIFvNw/cu5x3QcWjrU6LjWzj70eQNSTTAUdFy5DZsVyRbL6On2oLOzL+rdwLNrNunlLKsAdVd1TSesI4xT4RxajjfRMBx1Dd1SEjyS/iUPBMtuVSBio9H2GAruw74+H5qanQok06Dh/UFzVDUMQyTiywjrUET6V5TNCKODSrGilfeef08NgQENXK4N+xijGQPBUAh2Retmt5mlf5ElStHAZ52gsJcmlsG5pNz30cA+rnibWRnZCDMgI2VMfd6gPPOBGXQNYfAc8kxGCzB4Feu4iBPIvsGhkPd0gFURSQv/XqNBJz3aUXVcMBTVib3UYbWaYDJR/0Yv2fb7Q3AoIsmi7iJBom+Eu4G6LyEGCLxiEVJVM0KrEfWvUj1iC9/nF9JxquwRDRreDzRHVcMwzJubIwQAHUMi41R4La0u5GQlqJvXSHZfMvuG+oZ/FvIAejNgVMOgSBIW9ge2tbqGXQ4STbeZMG9OjhLZ0tPtmDY1A52d7mGysTTJbDYKU6GG4Vg6J1cM86EOId/zeANYquiZEmT3paHJ6PhQh6an14NFC/PFSNYwGHNn5wizLzPOA8Gz0drqlL78iWVqZiJOL0tDssOCtq7hOo7v8TN+RwVKcx3ITrWjrbtvuI7r9cIeZ8LMGJ0lqRI8g2SBJUfB0ACEnF0dsHhxvjL5li8rErKnoQE5/k7netnSQmWyxTIWzM+TyrOh5b0M2NB+mj4tQ9ms9/lzc8RJpu0xEDy3tFFoq5SWxuZs61iFTq9T/roUoTmqGoaBjL633DRZsm/19T2ihLu7Pait7YY1zoQ7Pz1zUA/mmEJnAkyl4f7UkBMI+cKvoDNcv28qAXRqMoOMUHJt4uMtUsbF3hWuXX1Dj/QgXXfNBBQUJEEVPvWJ6UhLs0sZFy8vliWxPKij041llxVi+rTwAGgNg3H5/DxMKEpGQ4sTbZ1uON0+tHe60djSi7LCJKxcqM7AXLQwT4IfzS0u6bnkM6WBVFvXjbxcB26+YZIy2WIZdERvuHaiOAk8nyRX4nllXyorI+781AxhyVSBeJsJt11RKgGI+uZe9Lq88uLP7AXlZ/yOCrC892PLi2GzGKVftcfllVE1je0ueHwBXD0/T8iVNAzHLTdNEaId3qM8ozyrPLPNzS7Mn5ODRQvylMl29apSTJyQJvc9WbApW2ubS35nUHrVFSXKZItlzJieKU4+nyfvUq4b71be/xnpdnzy49OVyUZbgzaH2+0P67heL7qo42q6xEa5645ZUcuCNWiINWjjabTxNFHBbbFh0zm89fYZ1DeExxDwIrv5xklCu64c/mbAVxUeVUPo4wFTAWDIDBMrKR7t8/KrJ3HqdItEVjMzE3Dt1WW46srSUaX4v5g9V3GuHa+8dlJIn2SkRLIVq64oxY3XT1QXfBgH4EzLNzdWYMehevR5/NIruGhWFm5cUYrkxDjlLN2vvXEKGzafk4wNCVvmz8vBbbdMETbb0cJHQcet21iBd9aVy6xBOoGTJqTi5hsmiwGqWra9x5rwzo5q1DSGdVx+dgKuXVKI+TEQUDpe2YF39lSjvK5bWhrIALxyTg6Wz8we1TL48b7nSF7z8qsnhLCQ5Zcsub1iRbEEhlVXPrS3u0S27Tuq4fb4YYsz4bKlBbj1lilCvnMp4mL2G7POb6w+jXXryyWAyWAObaRbb56MkmK1GUvaH2vXl+Ptd85Izz3tj0kT03HbLZMxberY65HxPp6m+eG/Vz6eJuPL/zXu1u+DQnNUNUf1gmCZUnuHWxgnk5MHM4sqhxAn9Y970VmUO6gDwWPFkTR+X1DWLRppy4eN97PnmD0i8RNl0xzUiwdJdpjdYl/qSARLqsDSva6uPiFFoQE82vio6Di/Pygl8cwucHSILsb0SHt3WMelOCwxJ1uX0yvET2QBHshQPFr4qOw5luW7XT5h1R2tOccfJPDFgBf7UlU7z6rxfvYbHVZmVskJ4HCoDV5G03GUzWjSIylRnY4b947qI3RU1T3bbncfMu6/9BzV2NKQGmIONN5U9Vi8J2QkTWxdCBHwIojlKDQv0ktIz31ooHMaaw5qBDR4MzLiVYsx7sAgEkviYxEyvkRxxv5CstFB1fD+wUDSWAST/i+gc3qpO6j/FzDgG6v6lzqOPBUaNIxHjN+QpIYxi5oz+utyDSZriRXZ2BPC3otYLAzwO13wdnbHpGzMpjKrGo3ZWTXYG83MIKPAsQYSKlG2kViAVYLrxR4kZlZjDdxn7HPnvos18Hx6u3rg63XGpGzUb7Gq43gv8H6IRdliWcdFqh9iUccFvF542jsR9PliU8d19Y3IXq9ex4UrlWLSjuvxSLZcwweArt9rUvXS4ZJEbKYGNMSEYtuxq0Z6VCurOsGqrunTM3Hj9ZPUMf4OwKHDDXhz9WmcPB0eLl9Wmoobrp+ojFV3ILqOnkLVk6+gdcd+3l6wlxYg/1M3IvuGlcpL92rruqSfcc/eWrn0M9LjhUhj1ZWlY1KefCGQvOP1N05i245qMeRYFkcSj+uumah8xAqN8dVvncHGzeckOEI2xcuXF+HG6ybJiBiV4Fq9va4c6zedkxJWzlVdurgAN147QXk1BPfY+o0V0idFshHusfnzcnHT9ZOQn5+oXMfVr96MqmfeQM/pSmFUTFs6B8V334rk2VOgGgcONuDNt07h9Jk2+X3ihFTccN0kzJmtniPg1OlWvLH6FA4faaSKQ2FBohC3LF1SoFzHkSzujTdPSh8o919mRjyuvqoMV64sUa7jWlqceO3NU9i+o0qcLVbdXCk6boLyEmBvRxcq/vIy6l7bAH+PE6bEBOTdugpFd98Cc2KCUtno/K1++7ToEjqqXCsyEd94wySkpaol7+IeY3/qmnXlaG4J6zgyPN90wyTk5arXcZu3VuGdtWdQU8OZr5D+WdpxExSxmmvQ8H6h9ahqPapR8dobJ/HUXw/JnMgEhxkhiRZ6xSj/5teXKHUIt22vwu8e2QOXKywPwWwNB5N/4XPzhBxIFdr3HMahH/wnPC3tMCcnQmcwwNvZJX+WPnAXSu+/c9T+7vfac1XVnfiv/9mMhoYe6T9iv4pkaoIhMeTuv2++MiOTTupP/nuTkD1x5qvFYpBsDY25xYvy8e1vLBUHTAXomP7051tx5Ggj4uJMUvrLXlUyxpKQ4gffW6asjI+Z3V8/vBvbd9fIzFSOQOLIHKfTh6LCJPz9d5cJ+6SK/car5dE/78fba87IvuLIKxp1jOxnZcbjB99bjuKiZKjC2T8+h9O/fgJBvx/mpESEggF427tgSUvGnP/5AdKXzlUm26YtlXjk0T3SxzhIx9lMuP++BVixvEhpkPCXv9ohPfi8G9ib2tPjlTaROz89A7fdMnXU/u732nPUHz/93y1obOx9V8f1hLPR1187EV/43FxlOq65uRc/+elmVFZ2wB5vhsVskHPq9wewZHEBvvX1Jcr4AlhRsPfrP0bb7sMwxttgsFnhd7oRcLmRtng25v/6RzAlqClr5R3wi19tx67dNXIHMEjo8QTgdHlRWpyCf/jhilFzVi9Gxz3y6F6sWXtWAl2i43xhHZeT48Df/91ypUz/z71wVF4yJz3BLP3kPd0e4ab43rcvG3NCpXHfo/qHv4fDprBH1dWHjC9dej2q48+D0jDqaGl14vkXj0Fv0CEvz4FERxySkqzIz3eIY/PU0weHzZQcK7B0hQ40s0giW2KcvJid4dD0Z549ItkvFQgFgzj94J/gbetEfFkRLGkp4qzGFxfAYLGg8vEX4TxXA1XghUUnlWuVkmKVAfM52QliCG/YWIFjx5uVyfbGm6dQUdEuY0PS0mxiZGZmxsvPu/fUSnZfFch+ffRYkzhXdPooG//kGCeu2foNFcpk27O/Hjv31CI1xSryUTYabbm5Cais6sDrb51WJtvJU62SAUmIN4f3WYJFWKY5v6+xqRfPPn9EmWzOmgaU//E56MwmxJcUwJySKOc1fkKROKsnf/FncWCVyOb04q/PMEgYGKTj+DPf42f8jgow0PDk04fQ3eOR+4AZQfa75+Y6JJP04ssn0NjUz8SuAM8+f1Sc1EE6LidBjHRm9bknVeHV10+Kk8q14hnlWc3KikdKig07d9dg9946ZbLVPP8W2vYcga0wF9acTJiTHLDlZsKWn422XYdQ+/JaZbJR97MCiHdBZkTHpdnkriivaMebq08pk+3osWa5O3mHntdxKbSVEoXh+dkXjiqtnuKeM1sMsud4TnleKRtH6Dzx9KGYLIuPZTDIpfp1KUJzVDUMw959dRIRTB0yD4+HJD3dJjMaTym68Flq1tTsFDmGHlpeXu3tbuw/0KBEtq5jp9Fz+hziMtOHyWZJT4GvuwfNm3Ypy1hy7chqOnREDi9+Ov50CFVFzLdur5L5lUNL80jqwZqPHTuqoQqbt1bKyAGzeXBpHqP7ZrMem7ZWKpNtx+4aMTZsQ8hPKC+j+/xcVc8UjUtmnbm/BoL7jxH9o8ebZWSCCjRt2CVZpLiMweVvPLdx2RlSCtx5WI0BfPBwI1paXVK2PVCPiP5Ns8tn/I4KnD7TKqW1qWnDGeBpoDNIuHdfvRLZ6CDTcUhOsUbVcdyLvNtUwN3nE4eLZ3KojmOGMBgIYcdOdTqu9tX10FvMMFgGzyA3xFmgMxmlHFgVWCbNO2AowRPXkXfGlm1VyjgDeGfy7oym43jX8s7l3asC3OucD83g4DCCtjSbBDLp6GvQEOvQHFUNw8CsKRFt5ieNc5YDR74z1oiQikQrkYoYAKqyDb6uXgS9XujjhpeB6vR6uSB8nWqyDb29Hnh9wRH7oPisu/rHYIw1aEDysme5bzSYTHp0dLqhCuyJYlQ6Gui8dnepI5NhZJzrEw0Ws1GCADSSVaC71zNiFJhlj5ESORXwdfUILwXP5VAYrBYEvT75jqqzGkIoaj8l3+Nn/I4KsFSV1TTcW9F0CNdUlWy8G+iwcG8NRWQfslxZBVjCzbM4so4zyPgQZWRi7V3ilEYD3/e0dkAVRMeZo+s46mXeHapIgrp6+kZs7eKz5p2r8jyIjruA/lVlx2nQ8H6gOaoahiFCwBKNkZB9gyS2YfZSBdLS7WKsRcsSeb1+6PThzKoKWLMzYLRa4Y/CHhoKBEBXxpqToUQ2ZsdtVmNU9mYaKszKZWaoId6x2cwy220kZmkaeCxdUgWWmLEnNRpoILHkS1VJTm5OArwjZBOcrnA2M94+OEsyViCJDfufoznxokes1CNq9pw1O0wIF628l0QyBlucsrNK/WvQ66OyN/M9fqaKJIuVLHGW6Hokcl+oki011SZZtwvpOJbHqwDPIctCL6Tj8hTpOOoue1Fu1Hsrwl5vL86DSv3r9UTXcS6nT8pZeYeoQFZGvPSxRtNx1L+8c7kvVSByDgOB6HacJc4o51nD+wCDEqpflyAuzX+1hguCrJycB0aWzoEKmAqvrc2FSRPTUFqSokS2aVMyhCSG7IkD+yv4c1OTU1j2Zs9Uw4ppL8lHysJZ8LS0Ieh718jkGrpqGxCXmYqMVUuVGUpkgmUGayC1P2VjKSE/J4uiCjDwQOZLyf4NiYwzk2q2GHH5MnXkMSsvLxbW66HZP4lY64CVK4qVybZsSaE4DkMzznSsPV4/rry8WBlBy5JFBUhwxMlZHahH+JzZ40iSLAYoVCDrqqXirLprGgbJRse1r6kVqfNnIGGimuc6Y3qm9JE1Nw/XcXyvID8RM2aMLQlKBIUFSZgyJUNaLAYGMrmGLOOmcUzGUxWgw7JoYZ6QTg3TcS1O6fMlK7EK8AxesbJESICGVjgwk0qCtuUKdVz+x68JV/wMqSLgeDVWHeR/7GplsnFdWMlFRvOB4F3BDPoVCtmcL1taKER6vEOH6jiSeF22JPy5CixcmCcBarZKDZSN57a9w40Z0zKVsxJr0HAx0MbTaBgG9tE88MUFeOh3O1FT0yWXBO0llooU5Cfhvs/PU5ZB4oV0/xcX4Oe/3Iba2u5w2aMO8HkD4lyTuVYVOyzXZNLffQnu+iZ0nyyH3mgUtt9AXx/MqcmY8vdfhSVVHcvppz4xXZh/j51ohl6nk7UkQyyZYj9zxyyl7IQcQXPmTBt27alFKOSW58rLnk7qrTdNxqyZWcpko3F74nQL1m2okNJB7i/Kxj5QOrEqnehpU9Jx202T8fLrJ1Fd2yUlXSw34/FcOC8X119dpkw2ZojuuWsWHn/yAGpqu6UUjkYSHa6pUzJw+yemK5ONZDHT/+mrOPSjB9F7phKGuDhh/WWAyTG5BFP/4X5lOo5OzZep4x7s13H9ZY8+bxCZmXbRzSajOh1332fnoq3NiaqqLtEhLPllNQsJ97503/xh/XpjiTtun4Ha2i6cONkiZIA8o3QOeafdc9dsyc6pwo3XT8SZs23Yt78OoZBLnnO4HNiIj906BdOmqsngE7k3X4H2/cdQ+/I6Yaxn+0qwzwOd0YiCT16H7OsvVybb7FlZuOWmyXj1jZPn7RG2H/F4LlmUj+uvnaBMNgZuPnPnLDzx9MGwjjP367hQSAJOn/z4NGWyMXDzxS/MkwkJ4XUzIhAMIhAIoaQ4BZ+7d44y2cYrWLHHl8q//1KENp5GG09zQda4TZvP4djxFjFI5s3NEaOcRCixwEy8eUulzBrkFp45IwsrLi8SJlbV8LZ3omH1RjRv2Imgx4ukudOQc+OVo56huZg9x5Kf7TuqhdiDvbzFxcnibDFLrho02sh+SfmYZWC5L8dw8NmqZrtjNcG+A/XYtr06nDlKt+OyJQWYPzdX+WxG7n8SE23ZXo2a2i7JUtKAWzQ/d1RnM16sjqNxznEr5RVtUqK3aEGerB2JUFSj+0wl6l5dj/a9R6GPMyNz5SLk3LgCcWlqKkaGjjPhuh3qJ05isIbngQE51WAGf+u2KiFsYek5K10uX14s2d7RxMXsOeo1zmLmOBP21JaVpsjM44kT1Os4lm7v3F0rBEHsveR6MWNIp0a1jmM1AUnGOFvYXdcEW34Wcm5YgYwVC6E3qAmMDNRxPAcktqur6xYbhAFE6rnRrBi5WB3HucKcsX3uXIfoNcpF+UiUpRoMTtOOO3GqRXrLWTG3/LJCqTAYa4z38TStf/5H5eNp0j73H+Nu/T4oNEdVc1Q1fESg7TkNYwltv2kYa2h7TsNYQttvHy40R/UDyuG6NB1VrfRXgwYNGjRo0KBBgwYNGkYCySqiTMMY07//EoTmqGq4IDgDjOUs7PeZNCFNen1iBRwbcra8TWaslZamSE9GrIAltiwH4iiHooKkmCjXG1h+dvJ0qzAnkzVWI1S4OLC3knPnWJbMsqkJZalRRzipAAtjKqs6hTSGpWcTJ6Yq62OMhvrGHtQ19kjp2aSylKjjTVSBa8a1M1DHTUyLiZLkCNgTffZsm/xcVpaqjHxqpBJb6jj2vBUVJSlj+42GQJ9Hei4Dbg/iS/IQX5yPWAHvhNOnW9Hr9CEj3S7kgKrLfgfqOJbqc98lJ8WhrDR2dFysg/Pl6xu6ZSoCbaXRbLv4v7QRVFZ3yp1AHRcLJckaNFwsYuckaYgpkFHvyb8ekj4kDnEn+Q4HR99w/UTcctMUpZcXL/oXXjyONWvPnp+L53BYcMWKYtxx+0xlZEoRh+HNt07j1ddPCkMyL34692RAvffu2cqNYPb4PPfCUTQ29krfJS+s2bOy8YXPzkVKikZVPxIYEPnTXw6g4ly7jErg/L6iomR87u45yvt7aRw99vh+IZBh8IE9syQyIkHW3Dk5SmXj+Xz82cM4eKwJLrcPBoMeGWl2fPz6SbhcEQPrwGDSE08dlH7GXmdYx5El86YbJ+HG6yep1XG+AJ59/ijWbShHV1eYbTox0YJVV5Ti9k9OV8bkTFCnvbH6FF5746Sw/5I4Jt5ukb7jez4zW6kRTP1b9+o6nPndX+Gqa0LIH4Ax3oaMy+dj6j88oLz3mH3uTz19UJwaku7QqZkyOQP3fW4uchQSPREMOvz5L/txrrIjTGRnNgi7/+c/O1ccVg3RQQZd6t9DRxtlXi4DXpmZCbj949OUMekPDCb95cmD2LGrWljqqdM4LofkVDdcNzFmAiQaNFwIWtG9hqiX/e//uAdvrj4tP5MpMTMzHr1OL5766yG88toJpfI98+wRcbb6vH5kZ8fLy+8P4OVXTwjDqEqsfvsMHn/iALq7+5CRYRdCIF4G76w9i4d+t2vQuImxxs5dNfj9H/agsalXZs3m5TnEGGEw4n9/uS3qbFoNYUeQ60NHkKMGuG6cicisCJlZSWCksqrgZ7/Yhn3762WGJB1UBpRIoPGr3+7EsePNymTjeJwH/7AbW/fUivOck5WA1BQrWtqcePSvB7F9b60y2ajXfvv7XXjrnTNkajiv4zg2h4YdHTGVePLpQ3jh5WPiMER0HH/me/xMJbg2XKOeHq+smTDp6kKyllxTlbQXDW9txpEf/wau+mbEZWfIyDC9xYy61zdi//f+SzKtqnDseBMe/PUOVFV3ISXFKnqEZ3b/gTr87Bdb5SyrAnXY//5yqzirDPpSNrI3Hz/Rgp/9fCvq67uVyRbL6Ovz4ee/2o7tu6phMRlE/6al2mW038OP7hEWe1WgrfHQb3eK7UEbhLYIbRLaJrRRRPdp+L+V/qp8XYLQHFUNw3C2vB27d9ciOSVO2PUYhaOhSaZTRvLpwDLLqortd+26conap6XaJEPDF7OBvFjJBKzqUmUWmlkGrhdLfblW/DkpKQ6pqVbsP1CPEyeblV1aDDD0efzIyU4QB5WXF9csOzsBJ0+1Yo9CxyGWsW59hRgeNEK477huNDBpoDe3OLFmXbky2bZur5KyVcoSbzeLbCw54+80SJjdV4X9hxtx8mwbMtPt4tjzLJhNBmRlxMPjC+D1NWeiDqMfC9Ag37u/Ts4lWwYiOo6GnNGow+tvnpKMqwo0NvVgw6YKqcRIHaDj+DOfMT/jflSVoeHacI3CaxUeT8M1pPO1d389Tp8JlyqrYK0tf+wFBPq8sBfmwmAxywxQc3IibPk5aN97DM2b90AVXnvjlMxizs1NkDPKs8pnzEzquapObNlWpUy2d9aclRm9dGao2ygbdR0d1sbmXhnNpWE49uyrx8nTLcjOTJC7lOvGu5V3LAO/r7xxUllw+viJZuw/2IDUNKvYIDyntElom/BnVn0NnemrQUMsQnNUNUSN/Lr7/GIUDQUdV5a6MJukAozw8rKn4h0KlsbRkOKoDlUOfmurK+r4Hpb8MiNy9Jga2Roae1Bd0yl9R0Mhc3KDIRw+Gh6DoWEwdu+tRZzVNKwUlL/TmFPp4NMQYanZ0BE5NJgSHXGSUVUVVDp2qhWBYEhmCw5FcmIc6hrYt6rG4eI5ZAl3tDJVnl+W7Z8+o07HsUyPz28o2BvNzzgLWQXohHJtous4k/S/Hz3WpES23ooa9JZXw5IxvLzXYLVIGXDrrsNKZOOdxYoM3lFDyy15dg16HfYfrFciGzPg1HE2e3QdR8d1t8LMYCzjyLFGWb9o7UYpSVa5cxl4UgHaQbQ57Lbodhz5R8rL25XINm6hi4HXJQjNUdUwDCTH4F0arX+BFxcVsz+gJkoYycBcqLVCVZaGfy/XZqTeNsrMviQVCHAIeTD8/GJNtlgH12WkdeP7/FxVuSN7GS8kG/sHle05brgRILIFQ0rPKjGyjlOrR0bScXyPO02VbBzXwbWJtue4lnxX1X4LUf8GQ5JFjQodEPSpySD5+2Ub8awadHKWVYH7aUTZdDrhhdAwHNzr4V2P6HokqFD/8u8dwU6KyKZKj2jQ8H6gOaoahqG4KFmivNF6FllOyHKl4sIkJbKVFCdLhJdZhaFgqR5LqkqK1RBmFBYmSflPd/fwDBYjm7wcSE6hAllZCVIqHU22yGU1oUwtKVCsYurUDNlbQ51R/u50+uRzVaQUUyany96K5iiz3zI/L1EZU2xJYbKsSzRjiLIx45CdqYYNmzqCmWhmAIfJ1u2Rc0w9qAL8e0myw701FHzPGqdOxxUVJiMhwRxVj3AtuaaqZLMX5SIuIxXe9q6oZcE02pOmT1AiG89gXl5i1HXj2fV5A5g6OUOJbDyj06ZmwuWMruOo+/i5huEgydRIgaOubg9SU+3IykxQZisxU8/7IZodx3aMwkI1Ok6DhvcDzVHVMAwzZ2TJ6A32QUWcVV5YdA67u724bEmhsnErhQVJmDsnW0aEDHQe2B/Kstvp0zIwcYIahkL2aa28vFhIp1jqFZGNBhxZdnlxqGJhZWnStddMkOhuR6f7vGyM4tfX94gju1QxC2us4qorSqUMniRUEYOEfzY1O6V09OorS5XJtmJ5kfQu1jf0nI/cM1PJsi4GRq69ukyZE71oTg5yMuPR0NR7PlvEfdfZ3QefL4irLi9GnKIRDrNnZaG0JKzjIs4qZWOZNEmCli8rVMaCzWAWdTBLbAf2yfJnvjdrZpboEhXgXlt2WaGsEddqoI7jWtJw59qqgNEah8I7bkDQ44WnteO8bAGPF85zdTKiJvuaZUpk4xm87toJciZ5NiN9izyzdXU9wjbNs6wKV11ZKrqMOi0iG3UcdR4D0/xcw3AsXZQvwTa2MQzUcbxjmUW/9qoyZVMIaGtQT9D2GKTjejwyGmnF5cUxNe5qXEA1kZL+0qz91cbTaBgGZlO/9fUlePChHTh9tk1KSHjnM8q/7LLwCAJV4IX/pS/MR19fAEeONorhRpAkYM7sbHz1gUVKKdc//akZki3asbManZ1hFkdmGeg8c01Vjs657poJsl5r1p1FTW13v87TSdbta19ZpF1aF8hafvHz8/CXpw+KQxgB1+szd87C9Gnqsg052Q58/cuL8PtH90ofMkEzMyHejE98bAaWX6ZuPAIj9l//wnz87vF9qG3oER1C6WxWE264shTXr1Jn/FJffOsbS/Crh3ZIb3kkAMGKjBWXF+GuO2Yqk43668v3L4TPH8TRo02DdNy8ebl44EsLleo4jj1i0HLX7lp0dkR0nB6TJ6XLmqocnVN8723oa25DzQvvwHm2ur+vSwfHxELM+sl3YU5SNwKGZ5HP8qVXTqCurjssWwjCnPzAFxcoHU8zY3omvvC5eXjy6YODZGMv4713zxEdqGE4kpKs+NbXluA3v9+FuoZu0XF0BtkXesuNk3Hd1WXKZKMu+/Y3loodx7FqbOmifFarEVeuLMEdt89QJpsGDe8HupBKLvkYQXd3NxITE9HV1QWHw/Gh9fI0NzcjIyMD+pF6ZmIcjPYeOtKIiop2MUSmT83AhAmpMTF7i1FfMuiSrZY7mHJRPsqpGjxS5RXtOHK0SdaQ5XLMMoy2AXexe46jCA4cbJBsOdkJ583LgTVOGwD+XmCUfM/eOglAkMxrwbzcqKQyKsAs/t59dcJCbLeZMG9uLrJGuaz2YvcbmaYPHGlEfVOvECvNnJqJgly1MyMjYO/doUONMjuSAToGHcpKU2JCx9F5JrESGYoJzuudNpVrrYsJHXfmbJuQdVHHMXMzc2YWTMbY0HHdZyrRsnUfgn0e2IvzkLFioWRcYwEk19m7r14y5BnpdiyYn6t8vnYErFQisRJ1HHUbZWOl0KWKi9ZxfT7sPVCPhoYeCejPmZWNvNxExAKY6T14qBGVVWEdx6AEqzZU6LjRsLXHUu7Wv/4zHDZ1eqTb1Ye0O3887tbvg0JzVDVHVcNHBNqe0zCW0PabhrGGtuc0jCW0/fbhYtw7qn/7/9Q7qp/+13G3fh8U2snToEGDBg0aNGjQoEGDBg0xBa1HVcOIYGnovv11UsbKklqWnbE0bujMRmVlySeacboiPAesrCgJs6dmKu2PGliWzJI49tCSNKaoKBkL5+cKW7FqsICioq4bh8+0wuP1IyvVjvlTMxAfZdaaClRXd2L33jopZU1Ps2HJonxlpDbRSvbYl9fB0t/EOCxamIfsLDWMjtFK9nbsqpHS33i7CfPn5QrxWCyUsJJ0Z9euWtTVd8PSXxbHnu1YkI0D7/furZPSX2lvmJ4ZMy0ELNk7dLIFZys75PcJRcmYOTk9JnQcy5I5p5E9tPyZTL+x0kIgRWKBDsDfwpk1gCEeMGZBp1ev44Tht7oSrn27EXT2wpieCduipTAmq2FKHgr2uUvvcVefzNymjlPFWjsUbe0u7NpXJ6SJJHiaPycHBXmxUV5LgqKdu2uEmFBKf2dnCyFlTOg4t0/u1MrKDphMLP3NipkWgvEGnV4nL5V//6UIrfRXK/2Nitq6LvziV9tRWdkpsxgJk1EvjJPf/NoSGd+gCu2dbvzm8X04SaKnfoZC0rCXFSXj65+bj/RUdY6N0+nFbx/ehb376+H1Bfq5PHQoyE8UohH2q44W3mvPkZzlyTdPYvuhevR5I7IB6clW3HfbdExRNFaCoBp69vmjeO2Nk7KGXDM+2ZRkq5B8XKaYkfjtNWfw9DOHZbyEzLIMAQ6HRcizbrhuolLZdu2uwR8e24v2drf8TtlsdhNuuHYC7vz0rFEzSC5Gxx0/0YyHfrsTjc29Qs4SIWUjscyX7puv1OliUOSXD+1AVVVnP9NpCGazEbNnZeMbX10sxrAqtHW48dAT+3HmXMf5ebR0numsfv2euUhV2BtNIqVf/3YnDh5qgNdLNlGd7DGO5/r215egoGD0Rpe9154L0TF1HwF89UAoLJtAbwNss6EzqmGEF9mCQXQ+8wR63nkDQafz/KBcY0oqUr7wZdgWLlEnWyiEN986LTp4qI4jsdi1V6sZ6xPB9t01+NOTB4UnQOTlOCKbCTddOxGfum3qqDmEF6PjyEXxm4d3oZk6rl//coTU5ZcX4b7Pzxv1vu0LgX2pD/56B6pruuQZc93MJgPmz83B176yWJiexxLjvfS37bl/UV76m/qpfxl36/dBMf48KA1jEsn/1UM7hUQpI8MuThZfJJAhYcuf/rJfmWxUto8+cwhHT7XKHMb8bIe8UpNtOFnehkeeOnCeXl8Fnn7mELbvrEaCwyxrlp+fiMxMOyqrOuXCiDabdqzwzo4qbNxbizizAXkZduRlxiMr1YaWjj784cWj6OgOM3iqwKbNlXjhpWMSMeSa8ZWX6xAG5Uf+uEcyXqpAQrG/PHlQ5tHl5Tn65XPIOSFLJompVDpbDz+yR2b25ea+KxsDNy+/ehIbNlUok41G5a9+sxNNzb2SeY7IRtbJtevL8fKrJ5TJxnENZMM8d65dzmdBQaI4WDTMd++pxRNPH1Sq4/74t8M4cbYNqclx7+q4pDh5j5+pjC8/8dQBWSOuFdeMa8c15Fo++JsdUWfTjhk85YC3Jlwspk8ADAmA3g4EXYDrIEJBdTqud+NadL/2InQGA0z5BTDnF8CUm4dAdyfa/vAQvNVVymSjDnvqr4dEp/GMiv7Nc4jOo+47fKRRmWznqjrwhz/vl8qM3ByHsNTn5zqg1+nw0usnsGVHtTLZyOL80O92oqXFiZycd3UcK0feWXsWr79xSmm1CG0O2h48n5SNNgnnINNGeeqZQ8pk06Dh/UBzVDUMAxnizlXSgIsfNE6FpauJiXHCfBoZhTHWqKjuxLHTdFLjBs1gJJtoWooNpyracaqiTdmltW1HtWSbSU8fAbNG2dnxqK7tEkdfBTzeADbuq4XZpEeC3Xw+As0sTXaaDa2dbuw62qRENhrdb689KwEGZlAjsjFLk50VLxH+jZvPQRXWb6iQ8ikGbSLZScqYnm6XwMPadWeVybZpSyU6utyyTgNli7ARv7MmvK4qsH1HtWQZsrMTzrcLUDaeD2ZV160vl3VVZZhXVXfK/OCBWV1mGOiAcbxUJEMy1jhT2YET5W1IS7bCYh6o44ySSeVn/I4KcE1YYs41GpiN4RpyLZmdVhW4CTGD6uVIGgPAMt9Ilk2nB/TxQNAJ+BTJFgyiZ81q+dmQlHxex+n0ehizchDo7IRz60YlshE8i9Rl1GkD9S91Hs/oug3qAl6btlWhu6dPWMyH6jhWVL2zvlxZ4Ib3fXNz2EmNtAtQNp4P2iRr1p5VFrjZt69eMqm0PQbqODJMUwdv216N9vbw6CsNFwmeDdWvSxCao6phGGjAceYW53ANBRUcSzOZyVEBzmPkuAt7lH5Pq8UoPaE1nAOnALwUWBYXrSyaF0UoGJK1VYHmDhc6ezxIiFLqw8uf+q+yXs26sR+Vs/uirRsvfbPFgFOnwiM6VODUqZYRS6R46Z883arMUOLoEgaTopW+MXLOua9dijLlbBvgqkTr9+SzZh8cxzkoka063NIQba4x183p9Ml5VoGahh5pG2D54FDY4owSdOJ3lOk4p0/WaCi4llxTVTpOHNGQB9BFKdmW86EDAmp0XLC7C/7GBugThvd78uzqzGZ4TqupMKDuog4baUQOdR91oCqcPtMmtkhUHRdvkd53nlcV4HxSiYNEaa+gjiOfQVOTmoAXzyGfbbT2CsrW2+tRpuM0aHg/0BxVDcPAhvvI4OqhYL8UlbJRUW8ZMzOR/pmhCL8XUiab2ayXtWFfy1BIf0go7LCqgMmgl1KpSE/vUFA2ZltVPVO2/5CUJRr4PjNwqkAD/EKyjWREjQW4LsFA9GfKYBP3o6oeKXECR/DfuW4q9Qj77UPBEXScrFtYD6qA+bz+Hf5Z5D1VZ5VrwrhDND0ia6lQx4XNGZ7DkYJGIXUmj9Eo2VMEAtE/DwSgs8TFrI4zRwlajxWoX3kmR9YjemUEj1w36pEL6V9lOo7B8ZHsuH79q0rHadDwfqDtUg3DwIHQjKKSyW4oSNiSmmrDlEnpSmSbOiENjngLOrqGZ4m6ejwSFZ6hSLay0lRkZMSjrZ/UZiAY8WXfCsmoVCAz1YbCHAc6ejzDLi5maHhpzShLUyIbmULJktgTRTayO9MRI4utKixalA93n3+YIceSWpfbJ8zEqrBgXq7Iwd6ygeA6dnf3yX5TRQo0c2YWjEYd+vp8w2Tr6OhDYWGy9CErkW1GlvTKsgIimo5LT7Nj4kQ152FKWZqU57MCYig6u/vgiDdjqqKzyjVJS7OjvW24juNaMnAyc0amEtmkvNfgANiHOtQ4F2IlRh/U3A2G+ATEzZiFQHeY1GaQaD6flAbb5i1UIhuDbNRh1GVD2wSo86j7Fi/MgyosmJsjcvAuGAiuI++MObOylAUyZ83MhsGgG1beG9ZxbpSUJEtbhio7jjZHtGxze4cbmRnxYrNoeB9gQFqv8KXTSn81aBAUFyVj2WWF6OryoLXNJUYwFXFjY684DbfePGXM2eIiSE6Mw7UrimW0SnObS0rk+Gptd8lFe+XSQmWsv4z8fuyWKZK5ZEkj14xrR1p9Xgy87DmWQ5UxctPyYtjjTGhocUn5tD8QRFevB83tbkwtScEsRYY5cdMNk2UMTU1NN5wurxgldLTq67tRUpKCy5cVKpPt6itLkZebKOXJJPSgbDTKa2u7kJPtwFWrSpXJtnRpgYxBiJT4UjaXy4fa2m7p4brphknKZJs7OxszZ2ZLDxeJlSgbCT44woFO4sdvnapsREJZaQqWLC6Q0ry2ATou0nt/261TlY1aYW/qVZcVylq1tL+r4/iz2+OXz1Sx/nJNbrtlivzMtTqv49pc6Ozsw9LFBSgtUcMeLlUNljJAZwSCZJn2szk07LiSTMmYHn4pguPG22BIToGvtgZBlxMhv18cV19DHSxlE2FbulyZbFdfVSa6jDqNuo1nlbqOOo+676or1em4yxbno6QoGfUN3UKud17H1XUjNcWGGxQyEjNQyJF9tI06IzrO7UNdXY8EzT9+6zRl1TaTJ6WJzcHAG22Q8zquoUdslNtunRK1vUuDhliDNp5GG08TFWT7e/7Fo1i/8Ry6uvokkJORbsfNN07GNVeXKZ0PxujqWxsr8PamCrR39p13YFctK8RNV02AUeEMRB6nDZvOCaMpe1O4D9gPsmJ5sYwyGc3I78XsuQMnm/HyxgrUNfdK+Z7VYsD8qZn41FUTJIujEhxlwhEwnNvr9wdgsZgwe1YW7v3MbMlUqwR7sv/y9EGcONEiIzk4xoSGwN13zZbAjkq0tDrxxFNh9mFmL41Gg8j0mTtnYtrUTKX7jcYu2UR37KyBy+WVflWyE3/q49OxWGEmmqDR9uzzR7Bxc6UQdkXIY269aQpWXVmiVMcxiLR6YwXWbCVZ1rs67uplRbh+ZYlyHbdufQVeef2EBCGYhSN5zMoVxbj9E9NH1fi9mD0XImFS3+l+ZzUE6EyAKQuIm6J8lmrf8SPo+OsT8FZWAH4fdHFxsM6eh+TPfB7GNHVONEFmdbKYnzzVel7HTZmSjnvvmj2qI4cuBpwP/eTfDuPQ0Ub0eQJSul9SnIw7PzF9VKu7Lma/0T6ijtu5uxZud1jHkZn49k/OwIL56iqBCBJk/e25I9i05Zxkn/lvICnVrbdMwRUrisdcx4338TTtL/0YDrvC8TTOPqR87J/H3fp9UGiOquaoXhBUbqQ3Z3kLI+WxFIFjxuFcPxlAYV5iVIIllY5+2OEKCiU82ZJHGxe75+joVzLj6/UjM8WG1CR1MxmHguqIBlNPr1fKL3OyY2PYfES2mtpuyQ7SaSDdfywMdI+gsalHHId4uxnFxe8yi44W3o+OY2UGI/lkwmSGXFVP2UiGJklFYlHHOd0+VPeTwxXmOmCLIR1HR586jr14sabjQsykBng3BGQ8jU4fQzouGBRHlbNUjRkZMGVmI6Z0XE2XBEeSOf4tzxFTOo5Z/JY2FxLizSgqSIopHceAIXUcg9ElxbGl40hcx+dKmajjopHIjQU0R/UDyuHUHNVLFpqjquGjAG3PaRhLaPtNw1hD23MaxhLafvtwoTmqH1AO56XpqMZO6FiDBg0aNGjQoEGDBg0aYg0MVqgMWOgvzWCJ5qhqGBFny9tk0Pfx481SMkLm1StXliAzU22/IFHf1IvNe2tw8ATnu4UwfWI6VizIR34MlIqyBGjjpnPYtbtWen2mTE7HlVeUYpJCsqIISFDEnrztO6plHi7LMNmrQnZY1SVeLOXeuqsG23bXSqlSTlYCLl9SgIVzcpSR7kQQ9PnQ8NZm1L++Ae66RlhzMpFz4xXIuv5yGMxq+95YFLN7Ty02ba5EdW2XlGAuX1qIFZcXwaq4VJSyHT7ahI2bKnC2vF1KV0lixD03FqWi74Wz1R3YvLsWJyvape9t3vRMXL4gD2nJagjZBoLlyOs3lOPQ4Ub5ffasbFx5RYn0v6lGS4sT6zdWYPfeWpldPXVKutwNEyfEgI7r8WDzrmrsPFAPt9uPksIkXL6oADMmq+0BJUi0w3O6ZXuVlJwX5CXKOV24IE+5/g14vah/czPqRMc1wZaXhdybr0DO9ZdDb1KsR4JBNK7djtpX16O3ogaWtGTk3rhS5DParOp13MkWbNlVjXO1XbBbzVg0JwfLF+bJhALV4KxtntWTp1qk5Jd7beXlxdJao0HDeIBW+quV/kbFzl01+N0ju8WxibOaEAqGpCcpOysBf/edZeLgqMKpinY89NQBtHW6Edffa9HnDSDZEYcH7piFmYrG00SMy5/9fIv0MpotBhj0OjGWOCLki1+YjxXLi0bt736vPUdmzp/+fAtOn26T+WlGkx59br/05JHoKcLmqQJOlw8PPrILh441i1PKC5VEECSmuGZlMe69faYyZ5UG3NH/70E0vLlRjBKD1Yqg2y1U8VnXLMeM//iuMmeV8vzlyYN4863T0g8dF2eQcUM8rzOmZ+F737kMCaNkLF2Mjnv9zZNCkMVnGWc1iox0bMpKUvCDv1uu1FjacaAef3rxCHpdPlgtRiEF8vgCyE6341v3zkNBjrrSqiNHm/DgQzvQ2uoUhmSCa5iaase3vr5ERk+oQmVVB/7n51uFkZu6g+eSI0zYN/jAFxfgsqWjx9D9XnuO7O+/+MNulFd3SuCBpFNkSo6jjrt5Cq6/Qh17LYnF/vcX23DkaCN0ep30a/f1BSQIfMN1E3Hv3bOVOasBjxeH/t/P0fDWFpk2a7DGIeByi5w5N67EzH/7ljodFwzi+H8/gsq/viFMyQabFcE+L0LBANKXzcfc//0hTAnxynTca2vO4IW3TgkvBfcZidB8/iDKCpPx3S8ukF5fVdi8pRJ//PM+9PZ6JGjJfnIGz8nk/P3vLBOehbHEuC/9feXf1Zf+3vqjcbd+HxTjz4PSMOqgc/rY4/sk+ktFRrZfZlHz8hLR0NiLRx/fF3WI9FjA5w/g8ZeOor2rD3mZ8UhPscmLP3f3evCXl48JSZAKcE0ef+IAamq7kJubIOx66el25Oc75HJ4/In9aG93QRWefeEoTp1qRXZ2PLKzE8RJ4PMliQwZnisq2pXJ9vaGchw42iSjhZhJTeMzzXHAbjNhzaZzOHAknFVSgYY3NoqTak5LRnxJAazZ6bCXFMCSnoLGtzej/pW1ymQ7eKgRq98+Lc5MXp5DZlzm5jhk3zET9+bq08pkq6ruxN+eOyrGN/cZ9xsDXSTIOnO2TdgoVaGz24OnXjsuTj11B0fCZKTakJsRjwYyjL56XJmOo8H7x8f2yhzGgoJEYbzmi2vI9/74p73yHRXgmvzp8f3ipPI+yIzouDyHONL8jNUQqvD8GydxtrID2enxyEqPFz2Sn+2QcRzPv3nyPDGVCrzx5ikcOtwg68UzyrPKM8uzyzPMs6wKta+sRf3qzTBnpCC+JF90XHxpAcypyah/YyPq39ikTLbmTbtR9dc3YHLEI760ENbsDNiL86SqpXnzHlQ+/boy2SqqO/HyO6dhNOiQl817y4qsdLu8zpxrx4tvq9O/HEnz+JMHxPZgFQb1L20S7r3aui48/tQBZTpuXHtMql+XIC7Rf7aGC2HPvjph6eSohoERXkbO09JswvRIQ1MFTpxtR21TL9KTrYNk48/pKVY0tTpx+BTLgdUY5idPNcs8UGYCB8pGQ5NzBklhrwIsM9u9u1bGSJhMgxn/UlKs6HX6sGNXtRLZGIHetL1aotFDGVcdCRbJwLEcWBXqXlsn5eVDo/bGeLv0jNS9qs5R3bKtUpyWoWW0XEdmMDkqifPzVGDHzmqJ5KemDs4oMIPkSLRgz946ZYGb/ceb0NHdJ0GuoTouJTFOMnKVipwaBhjqGrqH6d+wHrGJk3hYUeCm4lwHTp9pQ2qqbVCFQ1g2u8yLZhm6CrR3urHvSKPojKGMqylJceh1erHnUL0S2XhGN24+J9VJQ3Uczy4/37a9CqpQ+9Ja6PR6mKjTBoDOIZ2Z2pfV6bi6NzYh6PPDnDw4+8esL1+1L61B0K8mOL37YD2cbr+wwA8Es/msME9R5lwAAQAASURBVNhzsEFK0VVg155wC01GevwgPULbJCXZhhMnWySorkFDrENzVDUMAwdEs/5noLMVAaO/nr5A+DsqZOvuk/mfLJsaCpPRIGPzOrrUXAxcE48ncL5UbyAiRp0qw5wjVVgCF61nkZcYH3VzixrZOLydZXHWEWbMsgy4sbkXquCqboDBFr1nkWVortpGZZFpzuodGniIwGY1Sh9yb68XKtDS6pK9Fa2ckb2qbCWgY6MCrMjQgft+uGwsA2amNTK/dMxl63BJGXK0ERKcbUn9x2yJMh3nja5HIveFMh3X1SfPLZoe4R5kVpWjTVSAARun0ydnMhp4hjl6RQWou6jDDPboJapGuw2umgaogqumHvq46O0LRrsV3s4e+HucUAGWmnPbR9Nx3Id9Hr8ExFQgYqNFa5kRO84TQJsiO27cgs9Z9esShOaoahgGZt0IGktDQeOSBlTkO2MNh90M6l1vlCwRM3M8xwl2Nb00kWwlL4Ch4FpyNVURyDDLYDYZ5OKMZqhQvpRkNbLxQqfhG23dCD7rlGR1fT5xmakIuKMbG3w/LiNNWW9ZapptxIwp+984089mU0OEkpwUJ3srmhPPMlHux0SHovNgN4MnMqqO8wakh9sRr0aPcE24n6I9V75HB1vZujnCeoT3wEg6zqFKtgTqX31UPSI6LhRCkiL9a7eb5SzyTEYDnytLgVWAe82SkYKAO3qAN+DqQ1ymOpIs/t1BzwiyuT0wxttGdLJHG9xPwWB4fw1Fn4d6xKCMUIlnlWJF03F9ETsuQT3ZkwYN7wXNUdUwDAvm5YoCJrPjQFAZt7S4ZLi7KnbHaRPSkJlmR2uHe9DlILK1u5GaZMUsReyOHKTNV2trOCMyEMyA8FJYMD9XiWwsR543NxtdnX0IBIKDPuvqZjbThIUL8pXIxst82cI8uNy+Yca50+WVrNdlC/OgCjk3XYlQIAi/yz3MSQ35/PK5Kly2pFAyWUOzplxHZlOXXVY4rNRwrEB2SQYghvYscv/xvZkzs6RfTwXmTsuUgBYJ2QaCeqSty43CHAdK8pOUyEYG7syMeNG1w3Rci0v6QvkdFZhQloqiomTRcUONc76X6FCn41jGTWbfzp5w1c1AdPV4JIs/f2a2Etl4BpctLZQzOVTH8ezyDC9dUgBVyLv1KoR8vmEBOeo8khnl3qJOx+XcsELKkn1DsqYkufP3OpF70xXKiJ64nywWA7qH6F/qOO65OdMyh5UFjxUWzs9DQoJlWPUFbRMSK9JWKSlOViKbBg3vB5qjqiGqU3PnHTPl8qyp6RICDyo2/pycbMVn75kzrAdorMCS37tumiIkO+xVZbkXS2vqmnoRZzHgjhsni0GiKjJ972dmIz3dJmvFNePasQ8kFAQ+9YnpyMpUNz7nUx+fjoL8JNTWdUsQguXAdXXdUnp73bUTMHmSuqj5DVeVYVJZKhqaetHc4hRHpr6xR/6kk7pgTo4y2XJvvQoZly9AX0MzXNX18LR2yJ8c4ZB22TzkffwaZbLNm5MjTNLsQa6v75E+6ObmXjQ09Mg4pFtunKxMNjo1N94wSZila2u7Zb9xdBN/JuvkHbfPUCYbyZM+cc1EObN1TT3o7O6THsfaxl6kJFpx501TlLFM07mnHmF5HnUHS/j44s9877N38zM1Oo5rQtlSU2zCcH5ex9V0STXLHZ+aqZTJ+VM3TkFuVoL0+LIsk8+********************************************************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******************************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", "text/plain": ["<Figure size 1000x800 with 2 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Visualize results for current site\n", "if not results_df.empty:\n", "    plt.figure(figsize=(10, 8))\n", "    \n", "    # Color by pile probability\n", "    scatter = plt.scatter(\n", "        results_df['x'], results_df['y'],\n", "        c=results_df['pile_probability'],\n", "        cmap='RdYlBu_r', s=30, alpha=0.7\n", "    )\n", "    \n", "    plt.title(f'DGCNN Pile Detection - {SITE_NAME}\\n{pile_count} piles detected')\n", "    plt.xlabel('X Coordinate')\n", "    plt.ylabel('Y Coordinate')\n", "    \n", "    # Add colorbar\n", "    cbar = plt.colorbar(scatter)\n", "    cbar.set_label('Pile Probability')\n", "    \n", "    plt.grid(True, alpha=0.3)\n", "    plt.tight_layout()\n", "    plt.savefig(f'dgcnn_{SITE_NAME}_results.png', dpi=300, bbox_inches='tight')\n", "    plt.show()\n", "else:\n", "    print(f\"No results to visualize for {SITE_NAME}\")\n"]}, {"cell_type": "markdown", "id": "dfafef67", "metadata": {}, "source": ["## Export Results\n"]}, {"cell_type": "code", "execution_count": 16, "id": "f0afd0b2", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Saved northan_res results to dgcnn_northan_res_detections.csv\n", "\n", "Summary for northan_res:\n", "  site_name: northan_res\n", "  total_locations: 880\n", "  pile_detections: 130\n", "  detection_rate: 0.148\n", "  avg_confidence: 0.21045847237110138\n", "  high_confidence_count: 146\n", "  model_loaded: True\n"]}], "source": ["# Export results for current site\n", "if not results_df.empty:\n", "    # Save to CSV\n", "    output_file = f\"dgcnn_{SITE_NAME}_detections.csv\"\n", "    results_df.to_csv(output_file, index=False)\n", "    print(f\"Saved {SITE_NAME} results to {output_file}\")\n", "    \n", "    # Create summary\n", "    summary = {\n", "        'site_name': SITE_NAME,\n", "        'total_locations': len(results_df),\n", "        'pile_detections': pile_count,\n", "        'detection_rate': pile_count/len(results_df),\n", "        'avg_confidence': avg_confidence,\n", "        'high_confidence_count': sum(results_df['pile_probability'] > 0.9),\n", "        'model_loaded': MODEL_LOADED\n", "    }\n", "    \n", "    print(f\"\\nSummary for {SITE_NAME}:\")\n", "    for key, value in summary.items():\n", "        if isinstance(value, float):\n", "            print(f\"  {key}: {value:.3f}\")\n", "        else:\n", "            print(f\"  {key}: {value}\")\n", "else:\n", "    print(f\"No results to export for {SITE_NAME}\")\n"]}, {"cell_type": "code", "execution_count": 17, "id": "80762bff", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Exported all detections to KML: output_runs/dgcnn_inference/dgcnn_northan_res_all_detections.kml\n", "Exported pile detections to KML: output_runs/dgcnn_inference/dgcnn_northan_res_pile_detections.kml\n", "Exported high confidence detections to KML: output_runs/dgcnn_inference/dgcnn_northan_res_high_confidence.kml\n", "\n", "KML Export Summary:\n", "  All detections: 880 points\n", "  Pile detections: 130 points\n", "  High confidence: 146 points\n", "MLflow run completed\n", "\n", "DGCNN inference complete for northan_res!\n", "Output directory: output_runs/dgcnn_inference\n"]}], "source": ["# Export results to KML format\n", "from shapely.geometry import Point\n", "import geopandas as gpd\n", "\n", "if not results_df.empty:\n", "    try:\n", "        # Create GeoDataFrame from results\n", "        geometry = [Point(xy) for xy in zip(results_df['x'], results_df['y'])]\n", "        \n", "        # Create GeoDataFrame\n", "        gdf = gpd.GeoDataFrame(results_df, geometry=geometry)\n", "        \n", "        # Set coordinate reference system (assuming local coordinates)\n", "        # You may need to adjust this based on your actual coordinate system\n", "        gdf.crs = \"EPSG:4326\"  # WGS84 - adjust if needed\n", "        \n", "        # Add additional attributes for better KML visualization\n", "        gdf['name'] = gdf.apply(lambda row: f\"Pile_{row.name}\" if row['prediction'] == 'PILE' else f\"NonPile_{row.name}\", axis=1)\n", "        gdf['description'] = gdf.apply(lambda row: f\"Confidence: {row['pile_probability']:.3f}\\nPrediction: {row['prediction']}\", axis=1)\n", "        \n", "        # Color coding based on confidence\n", "        gdf['confidence_level'] = pd.cut(gdf['pile_probability'], \n", "                                       bins=[0, 0.5, 0.8, 0.95, 1.0], \n", "                                       labels=['Low', 'Medium', 'High', 'Very High'])\n", "        \n", "        # Export all detections to KML\n", "        kml_all_file = Path(OUTPUT_DIR) / f\"dgcnn_{SITE_NAME}_all_detections.kml\"\n", "        gdf.to_file(kml_all_file, driver='KML')\n", "        print(f\"Exported all detections to KML: {kml_all_file}\")\n", "        \n", "        # Export only pile detections to separate KML\n", "        pile_gdf = gdf[gdf['prediction'] == 'PILE'].copy()\n", "        if not pile_gdf.empty:\n", "            kml_piles_file = Path(OUTPUT_DIR) / f\"dgcnn_{SITE_NAME}_pile_detections.kml\"\n", "            pile_gdf.to_file(kml_piles_file, driver='KML')\n", "            print(f\"Exported pile detections to KML: {kml_piles_file}\")\n", "            \n", "            # Log KML files to MLflow\n", "            mlflow.log_artifact(str(kml_piles_file))\n", "        \n", "        # Export high confidence detections (>0.9) to separate KML\n", "        high_conf_gdf = gdf[gdf['pile_probability'] > 0.9].copy()\n", "        if not high_conf_gdf.empty:\n", "            kml_high_conf_file = Path(OUTPUT_DIR) / f\"dgcnn_{SITE_NAME}_high_confidence.kml\"\n", "            high_conf_gdf.to_file(kml_high_conf_file, driver='KML')\n", "            print(f\"Exported high confidence detections to KML: {kml_high_conf_file}\")\n", "            \n", "            # Log to MLflow\n", "            mlflow.log_artifact(str(kml_high_conf_file))\n", "        \n", "        # Log main KML to MLflow\n", "        mlflow.log_artifact(str(kml_all_file))\n", "        \n", "        print(f\"\\nKML Export Summary:\")\n", "        print(f\"  All detections: {len(gdf)} points\")\n", "        print(f\"  Pile detections: {len(pile_gdf)} points\")\n", "        print(f\"  High confidence: {len(high_conf_gdf)} points\")\n", "        \n", "    except Exception as e:\n", "        print(f\"Error exporting to KML: {e}\")\n", "else:\n", "    print(\"No results available for KML export\")\n", "\n", "\n", "# Close MLflow run\n", "mlflow.end_run()\n", "print(\"MLflow run completed\")\n", "\n", "print(f\"\\nDGCNN inference complete for {SITE_NAME}!\")\n", "print(f\"Output directory: {OUTPUT_DIR}\")"]}, {"cell_type": "code", "execution_count": 18, "id": "72e057a7", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Prediction Distribution Analysis:\n", "Min probability: 0.0000\n", "Max probability: 1.0000\n", "Mean probability: 0.2105\n", "Std probability: 0.3780\n", "Detections > 0.5: 179 (20.3%)\n", "Detections > 0.7: 164 (18.6%)\n", "Detections > 0.8: 155 (17.6%)\n", "Detections > 0.9: 146 (16.6%)\n", "Detections > 0.95: 130 (14.8%)\n", "\n", "Top 10 predictions:\n", "          x            y  pile_probability prediction\n", "385726.4523 3529225.3306               1.0       PILE\n", "385726.4523 3529235.3306               1.0       PILE\n", "385726.4523 3529360.3306               1.0       PILE\n", "385731.4523 3529245.3306               1.0       PILE\n", "385731.4523 3529325.3306               1.0       PILE\n", "385731.4523 3529440.3306               1.0       PILE\n", "385736.4523 3529220.3306               1.0       PILE\n", "385736.4523 3529345.3306               1.0       PILE\n", "385736.4523 3529405.3306               1.0       PILE\n", "385736.4523 3529415.3306               1.0       PILE\n", "\n", "Model loaded successfully: True\n"]}], "source": ["# %%\n", "# Diagnostic: Check model predictions in detail\n", "if not results_df.empty:\n", "    print(\"Prediction Distribution Analysis:\")\n", "    print(f\"Min probability: {results_df['pile_probability'].min():.4f}\")\n", "    print(f\"Max probability: {results_df['pile_probability'].max():.4f}\")\n", "    print(f\"Mean probability: {results_df['pile_probability'].mean():.4f}\")\n", "    print(f\"Std probability: {results_df['pile_probability'].std():.4f}\")\n", "    \n", "    # Check distribution at different thresholds\n", "    for threshold in [0.5, 0.7, 0.8, 0.9, 0.95]:\n", "        count = sum(results_df['pile_probability'] > threshold)\n", "        print(f\"Detections > {threshold}: {count} ({count/len(results_df)*100:.1f}%)\")\n", "    \n", "    # Show top 10 predictions\n", "    print(\"\\nTop 10 predictions:\")\n", "    top_preds = results_df.nlargest(10, 'pile_probability')[['x', 'y', 'pile_probability', 'prediction']]\n", "    print(top_preds.to_string(index=False))\n", "else:\n", "    print(\"No results to analyze\")\n", "\n", "print(f\"\\nModel loaded successfully: {MODEL_LOADED}\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 5}