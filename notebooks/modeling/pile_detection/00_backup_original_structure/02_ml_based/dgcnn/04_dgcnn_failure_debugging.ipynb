{"cells": [{"cell_type": "markdown", "id": "116aeea2", "metadata": {}, "source": ["# DGCNN Debugging: Why No Piles Detected?\n", "\n", "**Problem Statement**: DGCNN inference on northan_res site returned 0 pile detections. \n", "This notebook investigates why and determines if this is a model issue or correct site assessment.\n", "\n", "**Initial Results from Inference**:\n", "- 880 locations analyzed\n", "- 0 pile detections at 0.95 threshold  \n", " \n", "**Author**: <PERSON><PERSON><PERSON>  \n", "**Date**: July 2025  \n", "**Investigation**: northan_res site analysis\n"]}, {"cell_type": "code", "execution_count": 49, "id": "bf86c395", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["DGCNN DEBUGGING INVESTIGATION\n", "========================================\n", "Site: northan_res\n", "Loading results from: dgcnn_northan_res_detections.csv\n"]}], "source": ["# Papermill parameters\n", "SITE_NAME = \"northan_res\"\n", "RESULTS_FILE = f\"dgcnn_{SITE_NAME}_detections.csv\"\n", "\n", "print(\"DGCNN DEBUGGING INVESTIGATION\")\n", "print(\"=\" * 40)\n", "print(f\"Site: {SITE_NAME}\")\n", "print(f\"Loading results from: {RESULTS_FILE}\")\n"]}, {"cell_type": "code", "execution_count": 50, "id": "0735028a", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Imports completed successfully\n"]}], "source": ["import pandas as pd\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "import torch\n", "import torch.nn as nn\n", "import torch.nn.functional as F\n", "from pathlib import Path\n", "import scipy.stats\n", "import warnings\n", "warnings.filterwarnings('ignore')\n", "\n", "print(\"Imports completed successfully\")"]}, {"cell_type": "code", "execution_count": 51, "id": "4a92ffa6", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Loaded 880 analysis results\n"]}], "source": ["# Load results from inference notebook\n", "try:\n", "    results_df = pd.read_csv(RESULTS_FILE)\n", "    print(f\"Loaded {len(results_df)} analysis results\")\n", "except FileNotFoundError:\n", "    print(\"Results file not found - run inference notebook first\")"]}, {"cell_type": "markdown", "id": "e1dd5f54", "metadata": {}, "source": ["## Step 1: Initial Problem Analysis\n", "\n", "### Expected vs Actual Results\n", "- **Expected**: Some pile detections (based on training performance)\n", "- **Initial Questions**:\n", "  1. Is the model working correctly?\n", "  2. Is this the correct assessment for this site?\n", "  3. Are there domain differences from training data?\n", "  4. Should we adjust confidence thresholds?"]}, {"cell_type": "code", "execution_count": 52, "id": "62dce243", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Actual: 0 pile detections\n", "Avg Confidence: 0.0405\n", "\n", "Confidence Distribution\n", "- Min:   0.039552\n", "- Max:   0.041739\n", "- Mean:  0.040526\n", "- Std:   0.000374\n", "- Range: 0.002187\n"]}], "source": ["# Basic stats\n", "pile_count = sum(results_df['prediction'] == 'PILE')\n", "avg_conf = results_df['pile_probability'].mean()\n", "min_conf = results_df['pile_probability'].min()\n", "max_conf = results_df['pile_probability'].max()\n", "std_conf = results_df['pile_probability'].std()\n", "\n", "print(f\"Actual: {pile_count} pile detections\")\n", "print(f\"Avg Confidence: {avg_conf:.4f}\")\n", "print(\"\\nConfidence Distribution\")\n", "print(f\"- Min:   {min_conf:.6f}\")\n", "print(f\"- Max:   {max_conf:.6f}\")\n", "print(f\"- Mean:  {avg_conf:.6f}\")\n", "print(f\"- Std:   {std_conf:.6f}\")\n", "print(f\"- Range: {(max_conf - min_conf):.6f}\")"]}, {"cell_type": "markdown", "id": "10e6804c", "metadata": {}, "source": ["## Step 2: Model Sanity Check"]}, {"cell_type": "code", "execution_count": 53, "id": "330a5106", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Model loaded successfully: True\n", "\n", "Threshold Analysis:\n", "  > 0.04: 813 detections ( 92.4%)\n", "  > 0.05:   0 detections (  0.0%)\n", "  > 0.1:   0 detections (  0.0%)\n", "  > 0.5:   0 detections (  0.0%)\n", "  > 0.7:   0 detections (  0.0%)\n", "  > 0.9:   0 detections (  0.0%)\n", "  > 0.95:   0 detections (  0.0%)\n"]}], "source": ["# Check if model loaded properly (from inference notebook)\n", "MODEL_LOADED = True  # This would come from inference notebook\n", "print(f\"Model loaded successfully: {MODEL_LOADED}\")\n", "\n", "# Check confidence thresholds\n", "print(f\"\\nThreshold Analysis:\")\n", "for threshold in [0.04, 0.05, 0.1, 0.5, 0.7, 0.9, 0.95]:\n", "    count = sum(results_df['pile_probability'] > threshold)\n", "    percentage = count / len(results_df) * 100\n", "    print(f\"  > {threshold}: {count:3d} detections ({percentage:5.1f}%)\")"]}, {"cell_type": "markdown", "id": "c7f32bf5", "metadata": {}, "source": ["## Step 3: Visualize Spatial Patterns"]}, {"cell_type": "code", "execution_count": 54, "id": "6625b356", "metadata": {}, "outputs": [{"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAA80AAAMWCAYAAADLT2QZAAAAOnRFWHRTb2Z0d2FyZQBNYXRwbG90bGliIHZlcnNpb24zLjEwLjMsIGh0dHBzOi8vbWF0cGxvdGxpYi5vcmcvZiW1igAAAAlwSFlzAAAPYQAAD2EBqD+naQABAABJREFUeJzsvQd4ZVd57/3f9fRz1KWRNJruGY+7MTYGgjEYm+CQcENII8AlBAKhkxBCQkKvuQS4FxIgCSQ3gc+5EEgBB5saSIyxsXHv04t6Ozp9t+/5r60ja8bSFHs8e8l+f362pbPnSOfVXnuvtd5uRFEUQRAEQRAEQRAEQRCER2A+8pQgCIIgCIIgCIIgCESUZkEQBEEQBEEQBEFYBVGaBUEQBEEQBEEQBGEVRGkWBEEQBEEQBEEQhFUQpVkQBEEQBEEQBEEQVkGUZkEQBEEQBEEQBEFYBVGaBUEQBEEQBEEQBGEVRGkWBEEQBEEQBEEQhFUQpVkQBEEQBEEQBEEQVkGUZkEQTgv/83/+T2zcuPGIc4Zh4D3veQ/WGpSZsk9NTZ2y38lr8wu/8AvHfd8PfvAD9dn8ulavLWWlzI83e/fuVdfh7/7u75bO8XPz+TxOF0mPw+/93u/hec973qP++Wc/+9nqONY1PR2sdI+fDr71rW+p+2VycvK0f7YgCIKgD6I0C4LwmODmmZvo9pFOp3HGGWfgDW94A8bHx0+rLNxUL5elr68PP/dzP4evf/3reLJzww03KOVtbm7ulP5eKlTt622aJorFIrZv346Xvexl+Pa3v33KPufaa6/V1gigq2x79uzB3/zN3+CP//iPV/z3e++9d+mZPdX3Rdu40z4cx8HmzZvx8pe/HLt378ZaGZ/nP//52Lp1Kz784Q+fcrkEQRCEtYOdtACCIDwxeN/73odNmzah0Wjgv/7rv/BXf/VXarN61113IZvN4q//+q8RhuHjLsf555+P3//931ffHz58GJ/73Ofwy7/8y0qe1772tVjrPOtZz0K9Xofrusd8H99j2/YRSvN73/te5bHr6Og4pTINDw8vKRXVahUPPfQQvva1r+Ef//Ef8au/+qvqK5WmNvfff79SsE8G3kuf+cxnTkr52bBhg7oOyz/78eBYsh09DqeTT33qU+qZvPzyy1f8d47LwMAAZmdn8dWvfhW/8zu/c8pleNOb3oSnPvWp8DwPt956Kz7/+c/jm9/8Ju68804MDg6e8O95LPPHo7l3lvO7v/u7+IM/+AP1/BQKhUf1OwRBEIS1jSjNgiCcEn7+538eF110kfqem+/u7m78xV/8Bf71X/8Vv/Ebv/G4Ky5thoaG8Fu/9VtLr+nZoqfoE5/4xKpKs+/7akN+PEVUB6hs0jN4PE7kPaeKUql0xDUnH/nIR5TC9Jd/+ZcqAuCjH/3o0r+lUqnHVZ7l43k6r8NKJPX5VFK/9KUvrXrPR1GEL3/5y/jN3/xN5ZHmex8PpZmRHr/yK7+ivn/lK1+polB4X/z93/893vnOd57w7zld88dKvPjFL8Yb3/hGfOUrX8Fv//ZvJyaHIAiCkBwSni0IwuPCc57zHPWVG/KTyUk8dOiQ2pj29/cr5eqss87CF77whUctBz1pZ5555pIc7ZzM//W//hc++clPYsuWLepz7rnnHvXv3/ve99RGP5fLKY/sL/3SL6kw1pVgTjM9qQxJppHgzW9+s/K0L+eLX/yiuhYMFefn7Ny5U3m9V+P6669X3nIqW3wvPbbHy2k+Xi4tv7797W9X39Pz2A6Z5bW47LLLcN555634OxhmfdVVV+HRYFkW/vf//t/qb/j0pz+N+fn5VXOaqeDRi7dt2zb1d/NaPvOZz1wK7+Z76Sls/13t43jjeaz8W4YI82/jONPjyUgJKpLHu85H/85jydY+d7SH82c/+5kyMvG+Yb7sc5/7XNx4440rpj3893//N972treht7dXyfo//sf/OKH8WkZ78P684oorVvx3/l7+Lb/+67+ujh/+8Ic4ePAgTve8QGhY4XPOceNYvP71r39EuPjR88fycaf3uj3u9GrffPPNR/zcscbnmmuuwVOe8hTlQeZ4nHPOOcpDvxw+u+eee64yAAqCIAhPTsTTLAjC48KuXbvUVypAJwpzoJ/2tKepTS1zoqko/Md//Ade9apXoVwu4y1vectJy0GF7MCBA4+Qg8osFdzXvOY1arPd1dWF73znO0qZYe4lFR2G1v6f//N/8IxnPEOFlh6t9FNh5jmGJlPpoZLIUNf/+3//79J7qCBTIfjFX/xFFab77//+76o4Ez2hVA6W8+CDD+LXfu3XlHfwFa94hZLxJS95iSpG9FiKOTE8/YEHHsD/9//9f8rj3tPTo87z+jL3+NWvfrUKoz/77LOXfoaKB3/mXe9616P+XCrOjDL40z/9U6XEXX311Su+j9ea15CezosvvliN9U9/+lN1zfl3MzyWofZUov/hH/5hxd+x0niuFs4bBIHKVeW99rGPfUxd33e/+93KQ03l+WQ4EdmWc/fddyujDBW0P/zDP1QeVKYQMDf8P//zP3HJJZcc8X56ODs7O5V8VBRpGOCz8U//9E/H/ByG4/M5uuCCC1b8d3qWqWhSyeS4M4WC90fbuHK65gWOPQ0mVO5f97rXqdB9PjO8/6jYH8/DTG/5wsKCGgf+vRxP3u80ivBnjzU+PMf7k0aLdiQEDWT8XBrAlkPF+l/+5V9O8dUQBEEQ1gyRIAjCY+CLX/wi3XPRd77znWhycjI6cOBAdM0110Td3d1RJpOJDh48qN73ile8ItqwYcMRP8ufe/e73730+lWvelW0bt26aGpq6oj3/fqv/3pUKpWiWq12TFn4+6+88kolB4/bb79d/Sw/541vfKN6z549e9TrYrEYTUxMHPHz559/ftTX1xdNT08vnePvME0zevnLX750jjLzd/ziL/7iET//e7/3e+o8f6bNSjJfddVV0ebNmx8hO3/2n//5n5fOzc/Pq+txwQUXLJ37/ve/r97Hr21O5Nr++Z//uTrHv385c3NzUTqdjt7xjncccf5Nb3pTlMvlokqlEh2Lyy67LDrrrLNW/fevf/3r6nM/9alPHfG3UuY25513XnT11Vcf83Ne//rXq99zNMcaz/a/8R5tw89dfj+QMAzV57uuq+6b1a7zar9zNdlWGocXvehF6nN27dq1dO7w4cNRoVCInvWsZz3iubriiiuUfG3e+ta3RpZlqXE7Fr/1W7+lnsGVaLVa6t/+5E/+ZOncb/7mb6pxWGl8eRzr71+J9vX7whe+oK4p/8ZvfvOb0caNGyPDMKKbb75ZjRevBZ/ZIAiWfvbTn/700s+udo+35eDfMTMzs3T+X//1X9X5f//3fz/u+Lz5zW9W943v+9Hx+NCHPqR+x/j4+HHfKwiCIDzxkPBsQRBOCfQU0XO5fv16Fe7JsFNWrWaO8YlA/eKf//mf8cIXvlB9z9DS9sEwWob30vN4PBjeTDl4MOyYeYj0pi7PqW3nKfI9bUZHR3HbbbepcE56KdswLJPeThYTOpqjPcX0CpLl781kMkvf82/g38OQaHrClocsE4amMvy2Db2RzMlmOO/Y2Bger3xkhqDTy9gOT6Ynlp7MF73oRSok+LHQbu9Eb+BqMAyeHlh62h8tR4/n8aC3tk07sqHVaqlog8cLXlfen7yujGZos27dOpVbTG88vezLoed8eTgxvdT8Pfv27TvmZ01PTysP9UoweoP/Ti9rG35/++23q3E4lTDVguPCe5uRBiwUx3xm1j/gteY1ZwTJ8sJwjHzgvc+CYceDkRnL/05eH3IiFbp531GeE6ny3v6MU9lmThAEQVg7iNL8KGGo5cjIiMq/44aHm3KGgJ1oa5b2sbxICzcs3LhQ6eBGm3mYR+dWEeZn8d/4HuYcLg8FPRm4IWEoIH8PNwTcyAnCo4X3JTef3//+91U+aTtn9ERhnibzGJmf2FZ62wcLCJGJiYnj/h7e05SDG3KGqHKTy2dkufLazu1dTlsJ4TN1NHze+Hu4wV4Oc3CXw3BXbv4ZRtuGoZ40KLRzpPn3tFsAHa00s2DZcgWJsHASWf47TzVUzPfv348f/ehH6jWvHUPlOa89ViqVivp6rKrDDInm2PNvZU4pQ4TvuOOOk/qco8fzWHCMliutp+s68x6v1Wqr3mMMJ2cqwXK4zqykvDEN4Hgsz9E+umo2rxfD2FnpnAfvXYZoM2z7VPJnf/Zn6nlkrQCOKdfJ9n212jPHAm4cn+MZBh7r9WGaBMedKRmsAE8Fn6H6x7qWRz+fgiAIwpMDyWk+jpJLr9PygjVt2MKDG18qzCxcxHYUrBDKTfqxoAV9ec4cNyltbrnlFlVwhBsaKs78XfQyMC+w7RVhrhcrjrL9BnPRbrrpJvU7uVGgh+5EoUePP/ehD31IFWZhLh9zGgXh0cJc1Hb17EdDO/+UVZiZz7sS9PoeD+brrlb8aDlHK9GngqM31MzfZL7kjh07VCVxPtdUCOiJZm7x6WjBdSLQuMHCa5x72NKq3YroRK7j8WjPKzQIrAY/k9eKhZboiWVvYV6fz372sydc0flUj+dqyhG9vKcTzv8noxC3Yc7wSoojPdnMq2f+99FGn3aO8Ac/+MFTphzSCHIq7qNTfX0I11tGl1x33XXK+86DufE0ItEbvpz2tWzXAxAEQRCeXIjS/Ch561vfekQv0D/6oz9SnloWHTpW4RIqydyMrsTRrSxoaf/xj3+sque2lWYWMmFhE4aktd/DgikMPV2uNHPT+fGPf1xVKGWhIrb4oFWdUEFmkZM///M/VwWW2rDKrSAkBT2w9EZSKXk8N9mrweeYsBDR0dx3331qs3x0qDLDiZd7OOmxoyLcLhhG5aTZbOLf/u3fjvCI0Ru/Evx5bvaXKywsxkVOpPL4sTiWEkTFg+HBrNjMuYQFj2hUW00hOVE4llTCOO+xGvaxYEg8Iwp40DtNRZpFotpK86n08HGMGAnR9i6vdJ3bHsujqziv5P08Udl4j/NarHaP0QNOw8qpgIYaeo0ZzcAQ/DZcT6gw0wB7tAJIuVj4jdERxxuvU/3MLff8M2Sba9epmgeONT40YnHt5MH7guskC7OxeN1yQw/l4fU6mRQAQRAE4YmDhGefAmZmZtTm5OlPf/pxK33yfVx4Wa2UHmOG6h0LbniW51dyA3503096WOhxpsLe/gyGxNFbwEqg9CZzA9C2nDMvlN5xbtBYWZXecoaniadZSBIqaMxLZRTESvfiibTZeSzwOWCrJz4nyxUlykLv5wte8IJH/Ey7lU0bVtomfJ5IW+lc7vXiM01v1kowdJV54Mu9ggwtp1yrGdtOlLbCf7QS2IYhs/Sm0ShHpfXovsuPRmGmsY5zEL8yR3U1mF97dB40FRbOdycq/8nCNlhtOD58zfmbkQFthY7jx1ZMy2F7pKM5Udn4+6688krlUV8eBs5QeBoXqKge6zqdDJdeeqn6uxjBtBxGEVBBZWoQo6OWH4yY4rU/1SHaq0GlmEorq84vf0b+9m//Vj0nq1VbP1lWG5+j7zuuie1oluX3HuF15DUVBEEQnpyIp/kx8I53vENttKj4snXJN77xjWO+n54cbsRYEIW5Xfx5WtiP7sPahuHZLMazvBgKwyjpRaZX+8ILL1QLOV9TYWbOJTf+bE1CLzPbbhB6wphjSus5w17bBVLoxWHIKD0rfD/D0eltWa6kC8Lp5CMf+YjywjIvmZ5ORj/QKEVDD/Ns+f3jCaMvqPByc8wojHbLKXrqju612/Y+sb4B2xcxKoQKCZ/zdt9jKkhtT1ZbGWVqBcNCWXjsaOj55OcyeoTh0uxPTYVqNSX7ZGDLHPInf/InqlAbFUTK1VYoaECjMY+F05hfy/nlRKGCw7+dcD6kx5zzGkOu+Vnvf//7j/nzHGfOP5SR8w/bTX31q189olhXW34q4JwHqYDydz8aaHhk7irnQ95rDMvlPMuUm7YnkWPOdl8cf3oqmfPLOX6lvPqTke0DH/iAyvGlgkyvJtuQcW6mksZ2SacK/n6GaPO5afdGplGGzxflXAnmOFN+3gNUZI9nBH6s8FrTeMyWU3yG+CxxTaRhgulHj9Vwc7zxYRQD5xReH+Y0M4qA400jFZ+BNhxzrtlHF/4TBEEQnkQkXb5bJz74wQ+qFivtg21mUqnUEef27du39H620bj//vuj66+/PnrGM54RveAFLziiNcjx+O53v6taWDz00EOP+Lc777wz6unpid7//vcfcZ7ta175yldGtm2rtiODg4PRH/7hH6rfMzY2ptrD8Hu2+lkuN/8OttIhX/rSl9R7Pve5zy393kajoT7vs5/97KO8esKTlXZrHLaQORYn0haJsKULW8SsX78+chwnGhgYiJ773OdGn//8548rC3//8VoXtVvVsAXTSrB1Fp9nPkNsR/PCF74wuueee454T7vlFM//yq/8imoX1NnZGb3hDW+I6vX6Ee/9t3/7t+jcc89VbZ3YbuejH/2oaqVzdPuntuzXXXedej+f2R07dkRf+cpXjvh9j7blFOF8MjQ0pOa2ldpPfexjH1Pn2V7nRGE7Iv5M+8jn89G2bdtUyyPOjStxdMupD3zgA9HFF18cdXR0qOvOv5vzMVsjtWFbILaJ6u3tVS2L2svXscZztZZTnBPZ8omtjrLZbNTf36+u1fK2R+05/sUvfrF6D8f3d3/3d6O77rrrEb9zNdlWG4dbb71VtR3jteLvvvzyy6MbbrjhhJ6r1VphrQTbhm3dunXp9cc//nH1s1x7VuPv/u7v1HvYuulUtJw6+v5dCbaY4pjzeedYvO51r4tmZ2ePeM9qLadWGvejr/lq4/PVr35V3QNcG9n6amRkRI3x6OjoEb/vr/7qr9Q4lcvl4/4tgiAIwhMTg/9LWnHXBVqcl3uyXvrSl6pw0bbHltArS8/A0Rw8eHCpeNeJhnCxEi9D4ejxWF5lmF5hFhqjFZwh1itBzzI9UPQss9owvdYMPWMIK8M46fWhB2U5tK7T60xPAy3rrJS7PG+N72e43GqfKQjCExtW62e9BoYOH12VWFh7MKqIuc30pLfDzoWTh1EYjIRgcTpBEAThyYmEZy+DYYHLQ5OZK8wwymNVfW3TroJ7dB7UsWDVTkLFtw17ZFKhZdjgsZRXhs0xnIxcc801+IVf+AWVj8WQToZ/c7NEpX+1UDWG4TEMrq00UwnnRrldmEUQhCcXtJ8yl5Q9pEVhfmLA3GWG+zPtQZTmRweN2iz4xwrbgiAIwpMXUZofBT/5yU9UziEVTlZYZd4eC20x563tZWahLW5SWMSHrXj4HhZ6YTEh5pkxP4oeHVaIbRceYcEhKsz0Or/tbW/D2NjYkoe4nWfHnGMW/aJXmEV7mJPMn1veHoP5YczdYk4e88SoyDNHkO/n72WhGRaBYe4zveNUlJnLSZjDJwjCkwdGvLC6NyNQ7rzzTlWkSnjiwCrZwqOHa2i717ggCILw5EWU5kcBW4awyA2VTm446SnmwspWHfTgtj239OS2q2OzGBALsnzyk59UP0NllaHf/Jk2LHzD8GqGVreL6hAqte1Kq6xIy6Jd/N30NjOMmyHhy9vRMKybMlIRfvvb364K/bBX5lve8pal9/DfGGbOirksdkQl/Hvf+95SmxVBEJ4ccM5h8bKOjg5VCIvFmARBEARBEISHkZxmQRAEQRAEQRAEQVgF6dMsCIIgCIIgCIIgCKsgSrMgCIIgCIIgCIIgrILkNC9Wvj58+DAKhQIMw0haHEEQBEEQBEF4QsBM0IWFBdXdhZ1e1hKNRgOtVgs6wPpI6XQ6aTGetIjSDCiFmYW5BEEQBEEQBEE49Rw4cGCpXepaUZiHO7ox3YyL+ibNwMAA9uzZI4pzQojSDCgPc/thZjumUwGrZ19//fW48sorVZVrQXi8kXtOOJ3I/SacTuR+E043cs+dOsrlsnJOtffbawV6mKkw//uVL0XOdhOVpeq38MLrv6RkEqU5GURpZgnxxZBsKsynUmlm2yf+PplshdOB3HPC6UTuN+F0IvebcLqRe+7Us1ZTIKkw551klWYheURpFgRBEARBEARBWAHDio9EZQiT/XxBqmcLgiAIgiAIgiAIwqqI0iwIgiAIgiAIgiAIqyDh2YIgCIIgCIIgCCtgWIY6EpUhXJv54E8kxNMsCIIgCIIgCIIgCKsgSrMgCIIgCIIgCIIgrIKEZwuCIAiCIAiCIKyAYRvqSFSGSMKzk0Y8zYIgCIIgCIIgCIKwCuJpFgRBEARBEARBWAHDNNSRtAxCsoinWRAEQRAEQRAEQRBWQZRmQRAEQRAEQRAEQVgFCc8WBEEQBEEQBEFYrU9z0oXApE9z4oinWRAEQRAEQRAEQRBWQZRmQRAEQRAEQRAEQdBdaf7IRz4CwzDwlre8Zencs5/9bHVu+fHa1772iJ/bv38/rr76amSzWfT19eHtb387fN9P4C8QBEEQBEEQBOGJhGHpcQjJokVO880334zPfe5zOPfccx/xb69+9avxvve9b+k1leM2QRAohXlgYAA33HADRkdH8fKXvxyO4+BDH/rQaZNfEARBEARBEARBeGKSuKe5UqngpS99Kf76r/8anZ2dj/h3KslUittHsVhc+rfrr78e99xzD/7xH/8R559/Pn7+538e73//+/GZz3wGrVbrNP8lgiAIgiAIgiAIwhONxD3Nr3/965W3+IorrsAHPvCBR/z7l770JaUUU2F+4QtfiD/90z9d8jb/+Mc/xjnnnIP+/v6l91911VV43eteh7vvvhsXXHDBip/ZbDbV0aZcLquvnuep41TQ/j2n6vcJwvGQe044ncj9JpxO5H4TTjdyz5061vo1VNWzrYSrZyf8+ULCSvM111yDW2+9VYVnr8Rv/uZvYsOGDRgcHMQdd9yBd7zjHbj//vvxta99Tf372NjYEQozab/mv63Ghz/8Ybz3ve99xHl6rpeHf58Kvv3tb5/S3ycIx0PuOeF0IvebcDqR+0043cg999ip1WpJiyAIa1dpPnDgAN785jerySidTq/4nte85jVL39OjvG7dOjz3uc/Frl27sGXLlkf92e985zvxtre97QhP8/r163HllVceEf79WK1q/Nue97znqRxrQXi8kXtOOJ3I/SacTuR+E043cs+dOtoRnWsV8TQLiSrNt9xyCyYmJnDhhRceUdjrhz/8IT796U+r8GnLOrJU3CWXXKK+PvTQQ0ppZsj2TTfddMR7xsfH1Vf+22qkUil1HA0nxVM9MT4ev1MQjoXcc8LpRO434XQi95twupF77rEj1094IpBYITB6jO+8807cdtttS8dFF12kioLx+6MVZsLzhB5ncumll6rfQeW7Da2C9Bbv3LnzNP41giAIgiAIgiAIwhORxDzNhUIBZ5999hHncrkcuru71XmGYH/5y1/GC17wAnWOOc1vfetb8axnPWupNRXDqakcv+xlL8PHPvYxlcf8rne9SxUXW8mTLAiCIAiCIAiCcKIYJo+Ew7MT73ckJF49ezVc18V3vvMdfPKTn0S1WlU5xy9+8YuVUtyG3uhvfOMbqlo2vc5Uul/xilcc0ddZEARBEARBEARBEJ4QSvMPfvCDpe+pJP/nf/7ncX+G1bWvvfbax1kyQRAEQRAEQRAE4cmIVkqzIAiCIAiCIAiCXuHZycsgJIsozcIx2X+4jgd2V5FyTVxwVhH5nD63TNMLUK37MAwgn3Hg2PrMKNUmMDEff99XAnIapdj7QYhKzUOECNm0g5TzyKJ7SeEHEeaqAYKQY2oil9JnTIMwwkLNU18zroVsWp9nIQwjzFZDdf0yroFCxoTBB0MDojBEODsGtOow0nkYHX36yBZFCMrTiCibm4ZV7NFKttkqUGtGcG2gp2DATDinbjlevYqgWYdhWXBzJRimPs9qo+mh0fJhGgZyWReWRrJVGhHmaxE4lN0FA66tz5jOVSIcnIwAA9jQz3lEH9miRhXh1CE+GDC718HInpr2oKcCL4gwXw0oGvJpExmd1i3KVvPVmppNmcil9VnvBWGtoc+uT9CO2+4p42++fAALFV+93jiSxVt/ZyM6ism3Dqg1fBycrMLzQ/Wait9Ifw6uBgrgfA248QGg2ohfZ9PA07YBHbmkJQNaXoCDkxU0WxxTA7ZtYqgnp5TnpPH8CA+OtlBthIg4OVkGNvU56MwnP6ZUlA9MVJWRhlB5GejKoLPgaqEw7xrzMFeLN22WCQx1OxjoSH56j6IQ/u7bEE4dpPYMmBbMdVtgrz8zceWUSqk3+hD8yYPqe1rf7K51cIe3Jy4b2TcVYt9khJAPA4DeooGdw6ZSBJOmMT+N+tQoojBQr5vZAgrrNsAwk39WF6pNTMxUEITx2pCpOBjsLcLig5EwM5UI9x4KoKZfFkRNGzh7xETaSX5Mx2YjfOvmAAs1mlOBzryBqy+x0FVIXrawMgf/tu8jqi5aojN5OOddBrPUm7RoaHkhHhprod6M7ze1bvW7KGaTfxZoRN091kC1EcRrqmlgfW8Knfnk1wZBWIskv4oIWsJN5Ff+fRSVqo/B/hT6elzs3lfD92+Yhg5MzjWUwuw6pjoaXoDpchM6cP9hehOAQiY+qDzznA7MLjSUB4bGBV433w8wOVeHDkyWfVQaHFOAOjwV1YPTXqzQJMx8pYVK3VfRDLxuFGlirqEU1qShh5kKM+1F9DKTwzM+Wn7yskXzU7HC7LixZ8i0EI7tQVRfSFo0RI0K/OnDgGnDTOdgWA6C2TGE7Y15gjRaEQ5MRyqKJpcykLKBqXKE6YXkxzQMAtRnxtVzaTopmLYLv7aAViX560aZpuaqCMMQrm3BsS3Umx7KbQtmwuyZCJXCnHX5rAILjQiHZ5IfU3Lz/QHKtQidBSrMXCsi3PpgbBRJmmDf3Qirc0CuCORKav4Idt0OHZgsB6g1Q6QcQx1UVA9Ne9CB6QWuqYGKZqBhhmvq4emWFmvqWoOGVFbPTvTQwGD6ZEeUZmHVkJ5yxUc2Y6kHlcoCH9fywqKJPGGoMNPbpyYydQD+otc5aeqt2NtHmXgwarzW1CeM7OFrxnBPc8lbnzT+4v7MXJTN4pgGKhovcfwwVmDa95xlGkph5iYkabhJiz3M8YLKey+MInU+aSKfG7QQsBYjGWyXWhfgJf9ARF4r9pTai14Xy1avKXPStAJGEEAZQtpjyjH2NJh+o9BXwtGrHG8k421E6CcvHO97Ppec19T8trjJ1OJZiCI0/UitB8tl4zkdqNQBx16UzTRgWQ9HSyVOo7Z4v8XjSgMXw7V1oL1+ttdUzsNcZ3VQTNv3fXvdohecaxZDtQVBOHlEaRZWhGG7G4czKjS73ggwv+CDe6P1gxnoQCZlLSktzNFl7FGa7hgN6MzRSwrlUeDBNbUrDy1gLi5337xmDF/kNcxoct3oJTUWw7TVuIYcZz3yONOL2gs3SJTNC+IoB25CdLhuVKqaXnzdeM/Rs5DSIFfSyBRgUFFu1hAFPsCNrpOCkUn+gTDoXbZdRK2Gkk3lNduu8jonDb2QzGOmAY4Gm4bHFotALp38mJqWA9NxEQaeMjKEvqesg1YqnbRoShFlFE08tzHHP1TGLh3qNlBpKWYMNa9RmWlHguQ1GFPS32koo0yzFalIB65hfR16yIZitzLURF5TGbsQeDA0CM0mzF/mVeKYxutWpGpx6OAVzLiUjeMay0ZlPu2aar0QTo7EvcyLh5As8ugIq/KyFw/hjC15VOsszBTh8qd347KndUEH+jozqviXCo+NgFLeRXdRj2pbO4aAwa7YS8pjsBM4cxha0FlIo5RLKa8Vrx2LWfV1ZqEDPUULfR3c3NIaHhdU2dibfK41KWRt9JZSagPOZyHtWhjszmqxMWLu3HC3ozwc3JCnXeaCu7A0UOjNXAn2xrNjTzO9y6kMnM3nw3CTN76ZbjrOX3bceBNuO3CHztBCaXYsAzuGLBXGS0WGm9wt/SZK2eTHlJ7lXN8wbDeNkJ56A8h09cPJFpIWTT2P/d15pF17KQqkI59BQZNKjFv7TXTk4ueU0q3rMDDUmfyYkqedaWJjvxEben1g25CBC7fpsUW0N50Dc2AjQMMbn9Xe9bC3XQgd6C3Z6Cnaak3lulVImxju0WPdYj2QvlJsFA8CFgKzMNLLdUyPe04Q1hp6uJgELenrSeGPXr8ZE1MtuK6Jnk5Hm8mW4eIs/MUiHNy0uQwf10Q2eohY+It5zSSXpgcEWkCv7bqeHHr8uNiWTteNcoz0sIAVQyzpHdLDy9yWjYaazkJKbcZ53XSRjfR32GqDRG8Hr1s7VFsHrN4RmB39yqNrpDKx51kT7FIvrFyH8mBRedZJts6cgYu2WMrLzLBZHSIH2tjpLIrrtyLwPJiWBdPWQ0kgrmNjuL8Dnh+oZ9S29JnjaNA6b8RE3YvXBNZu0EW2bCou/DVfjdOKSrnYc68DNGjZ516GqFZWkVKsj6BLtXZeo5FeBwOdtirap9YtXa6bYWCw20VvyQEjtTmH6LRuCcJaQ5Rm4bjK6dBA8mF3qy0IKYYbawjXTBYB0/W66VBlfNUx1aCS7LGeB33UgyNhSLZO7WuWYzAk29HD27fShpyHjjD8X4Pi8SvCHFM7padwVAxStF5qKpsmju9HQGNbV/IBAyuicoZzJeiIzutWvN7rKdtaQofw6KQ/X5DwbEEQBEEQBEEQBEFYFVGaBUEQBEEQBEEQBGEV9IxfEgRBEARBEARBSJh2S7GkZRCSRTzNgiAIgiAIgiAIgrAK4mkWjknTC1FrBqoaZD5jaVWVN/SaCKvzcY/QfAcMtrXRhKhZRzh1SH1vdg/CSOvR1omEjRqaux5QvWndDVtglzqTFmlNwOsVThxUrZOMjl6YRT3ar5GIfWmnDwPNOoxCJ8xST9IirQmiKEI4dRhRZR5GtgCzb1gra34wPYZoflrNH+a6DTAMfezcU/MhJufZfs3ASJ9eFdun5gKMTgWqANLmIRuORgXywoU5BBOHVONte3ATDFefqmDsVR7OTao11ezo06p4H1thTbF4NoDuPCuRQxuiVhP+4T2qV5fVPwyz0AG91q0Dcauujj6YeX1kE4S1hijNwqpU6gH2TTTQ8kMYMJBLm9g0kFEVXZMmqFfQ3H8PwlZdvbYyBaQ2nAVTg0U+rMyjddN/qM0uMYrdcC++CmYheeU0WJjH9Jc+D+/QXqUw2F296Pq134Y7tCFp0bQm8j14N12PYHQPdyEwUlk4Fz4H1tAWLRRm744fIjz8UNyM00nBOetSWOu3Jy2a9nh3/wT+3Teq8VVtbc64EM75P6eF4uw98DO0bv3PuFWXZcPefDbcp12pheL8wMEQ3/uZj3orgmUY2DJo4Kqn2lqsDQ8d8PD1/6xjoRaqtk5bhm386nNzSLnJy+aPH0Tj+19DuLBo7O0bRuZ5L4GZSb43eFiZg3/3f8dtnbhuFTrhnPVMGJl80qKh1gRuehCYq8Wv2ZniqVvYoz5pyYCwXkXt+v+HYPyAaodFhTnz3F+G3b8+adEQeS20br4e4cT+uFVXOhevW/0jSYu25uC0m/TUm/TnCxKeLawCFapD0020/Ahpx1StbCqNENNlDzrgje9BSK+am4XhZBDUyvAmD0AH/Pt/imh+CsiXgHwHovKUOqcDlRu+h9aB3bA6e2D3DMCfnsT89f+atFjaExx4AMHoboARA4Uu1dfXu/1HStlKmnB8H8JDDwJOGsh3AmEA796bEDUXd5jCitCj5t9zE3sAwSh1A5YN/4FbEU6NJi0awuoCWj/7kTKIGIwacNPwd92J4NCepEVDy4vwwzt8ND2gq2AgkwYeOhwqRTppwijCf9zYQLUeorfDRDFn4qEDPm69vwUdaN70HeVpNjq6YeQ7EIztg3f3zdCBYO+dCGvzQLYAZAoIF2bg778nabEUD44CszUgnwLyaWChDtwbB3IlTuvumxCM7lPKstnZrYwPjRu/DR0I9t+HcGIfQMNHoVOtCd6d/6XmFUEQTh7xNAsrwhAozw9hm+0CCPFZep11QCnMlhV7hAwDkWEqj4wORAwZt2zVx1S9tpz4nAb4s9OAaS31pTXTGQQzk0mLpT1RvaLuf8OJYwIjN43IawC85xLu8Rs1qrEXYTHKInIzgFdXKQL0iAsrE9UqiPwWjEKHmkeidFZFh8RjnbBslIGyZfLxHJfKKI9WVFvQwutHhTmbavenBar1CFUNpt9mi7KEyKTioj3u4qNZroV6pAKUZwE3E0cL2KZatxiZpAO855jipGTjem9aiBrJPwuk2gQYxGAuunm4L9HhfiNhpaz2IIYdb6cNN63GmeOddMSKmkeiuBe9eu2mVQoP5xYa4oQTR/o0C0Q8zcKKMIc55ZjwgwhhGKmvnH3pddYBM51HFASIwkDl7LRDj3RAeYYCX23IlSeSuURFPXJMnb51yhMZNuoIW02E9RqcdcNJi6U9cR6YqRRRdb81anHYoga56kautLjBrapnAo0KDG7MNQir1BmlLKfSiKoLakyjalnll+qQj2jmS8rgQZmUbLUFtSnXIY8+l44PKi1cF2qNCKZpoDOf/IaOea6dBRPVRgTPp2yhUgC7i8mvW1SgzO5+pbRwXYiasdZnduixNhiMUgm8+H7juhUGMHLJPwukmGEUAeAFvOcALwRKeiz3sBbHj+OpxrVRh9U9kLjCTBjNoJwKlI3rFhXmbFGl8AiCcPIkv5II2jLcm0ImZcELIgRhhM68g+6iHsW23HWbYDGMjBbTwIPNvOHe5HOIiLP9IpiUhZuiZg1mzzCcM58KHcg//XJkzjxXea2YV+eu34TSVb+ctFjaYw5vg7X57DhnmB6ZfAnuhc9ZiiZIVLbeYVhbzo1fNBaU8cg+55laFfHREdYYcC+4PI4eoMJs2XDOfxbMzr6kRVOFv9yLr4hzXatxjqlz9qUwNciTZFGt515oq3zSSh0IQuC8LSY2D2qgJBgGfuGZGfR2WChXQ1U86oIzXHXoQPppV8LqXaeMIIyMsjftgLvzIuiAvelcGKVegHVCWOywax3skbOgA2cMAn2luBhYw48LgZ05BC1wz3oqnE1nImw2VFoFxzd96VXQAWtkO6yRHWqPhHpV5am751+mhUIvCGsRCc8WViXjWtg2mEG9FaqwqIxrajPZmm4G6U3nIWT4GC346TyMduyWDhveS6+O85oXPc/ckOuAmc6i6zdeA2/soPJKOv2DMFMSpnU8eG85518Ge9PZKp/ZLHZqE/rMZ9I+4yJYg1vVhpfeIZ2qteuMvfksVTFbeZlZPVsDL3Mbe+QMmD3rEJVnYWRyMJl3rQnre038+uUOZiqMPqInN/kepm2Gem38zi/mMDEXqloc/d2mipzSxSuZvfrlCGYmVHoRPc86GN4I5wznvMsRVebicGN2pNBENtcGLtnGMPs4dYyeZ1sP0ZTRLXPFi5GaHlfeXKurT4Vo6wDHz7ngctg0qqpUlC5tZFtzsPZF0nvMpD9fEKVZODaWFbea0hFuOiyGpmoIlWSjawC6Xjepln3yqNx+TUIpV5RNVWdPvkL7WoOh0Kpon4aYjKbhoSHZtKEOHcmkTWwY0HODyRQAeyD5iIFV1y1N29VZJtCpacYJlVOrdxDarg2ajqkgrDX0XFUEQRAEQRAEQRAEQQPE0ywIgiAIgiAIgrASDKpJOrAm6c8XxNMsCIIgCIIgCIIgCKshSrMgCIIgCIIgCIIgrIKEZwvHJPQ9hI2qqtpnZfIwDH3sLGGzjmDisKr0ycIqhq1HOyzCKpphLW4VY2aL2lTPJi0vxO79dQRBhI3DGeSy+hR6i6Iwvm5hEFdE16htUhQBM5W47UkpC2T1EU3JNlfj2AL5DJDTSDYSsIc0q467aZiaVB1v43kefN+HZdlwXX3mEFJvRepwWJAxHRf10YWgPIdgehxGOhPPvxrJFqke9Auq6CH7mOu0brG3db0ZcNlCNm1pU9m7LVulESnZ8mkDlqmPbFEYIKhX1GSn9iIaralRFKHZbKl5mHOIxaplmhBGEeaqcWu4QgZIO/qM6VqCveh5JC2DkCz6zDqCdnCBqh96CKHXUMkUdr4DmcGtaiOSNMHcFKrX/b9402YYsAY3IHflr8Z9TROG/Tebe+88Qml2N56jFIakqdYCfPZLB3H/7ppa4Af7XPzuS4cx2J/SYlPU3H8PwvK02oSYqQzckZ2wssWkRUMYArfsBvZPcRPC9mvAU7cA/Rp0KOI43n0Q2DsRb4xSDnDuBmBQg0LaHMfW5AF404eUIQSWjVT/RjidelSWX1ioYG5+HmEYqQ1JsVBAqZT8/UYm5kPsGgvg8bKZHE8TG/v0aPvX2ns/qt/+mupLa9g23B0XIPecX0q+JQuf1cosWnvuQNRsAKYBq9QHZ9M5WrRPorK8b6KBpheq14WMhQ19GdWlImkarQgPHPZRbVI2A4WMgTMGbdW2K2lCv4XGgQcQ1BfUazOdQ2b4DC3W1DAMMTk1h3qzqSZj27bR19OphQEuCCPcsQ8Yn3943TpvQ4SufPJjKghrkeRXOEFbGmP7ELLvq5OCYTnwF2bRmhuHDtR/fD2CydG4X26uCP/AbjRu+2/ogDe2B2F1TrUW4cHvvbHd0IHrfzSNux6oolSw0d1pY/9oA1+5Vo8x9acPI5ifBGwXRiqDsFlD69ADSvFKGirL+ybjfqH0+NWbwK17YiU1abgh2jMRK1a5dOwJv2t//DVp6O3zpg6p7w03o6wPzfG9CFs0xCXvYabCzPvLti1lfCiXF9BstZIWDS0/wu7xAH4Yb3SpJx+aCTFXTf5ZiLwWqt/7N6Uwmx1dgOOiefdP0XrwTj2MNPvvQdSsAekMYDkIZscQLN6DSXN4polGK4RjG7AtA+VagMly8vcb2T/lY6ERImUbap4r10IcnA6gA62pQ/BrZRhcG+wUwnoFzYn90IHyQhW1RkN55TmPeL6Pmdl56MDBGWB0DnDsODKq1gTuPhA/J8LJYZiGFoeQLKI0C6uHydLDzL6NhrnkXdZhs0vCmclYKaV8jguYFsLZSehAHM5uKc+G8m6YFiKe04CxyZZSrtIpE45tqvDAw+NN6AA99MRYuufc+FyUvGZajZ0IajPJdSvtAg0PaHpJSxZvhMJFD7OSzYkV5roGe3HOF1Hox5td9gu1XYCpCyp6JVl8P1AeZoshvIxWsUyEUahCtZOG95UfxPcbZaO3j54i3nNJQ2U5qldh5PJqflPRPVwv5meTFk3dW+CcYafiOWQxZSdq1ZOWTCkqVJg5/zIkux363PY6Jw3nC8rEiAt+5X1H77MuqVhq/lBrKi+gpYyqOsB5hJhmHAXC60fFWQfFlMZdiuFwG2LEawTnEEavCIJw8ojSLKwINxwm80mDQE3+DJ0FIphO8uFQxOzoUQpVRPl8j/FbMDt6oQNGOqtCUaMwVAe/NzTJ4+zrdpR3tNUK49y6RoiB3uRDs4mhQu0MlQ+u7rmgFec0a5CPqPKXjVgZ5Sak0QLSdrwJ0UE2boioaFE2fqVngcpz0jB8khtdPqNqTH3uzO14bkkYeoW4wQ2CUMnGMEvTMGFrkCtJZdm22vdbpDzP7U1v0pjZPIxMFlGtEhtXGzX1jJpFDXIVOHa8t/zW4hziL5tbkoUKVcoxEVAudb9Fak7hOR1gRANlYg4sv1LGtKuHZ4vzSLwP4bMar6kmI1c0gPMIaV8zfmWItg5pFDTuUgwqyWpt8OM5hEq0IAgnjx6ztaAl6YGNMN1UrJz6Hux8J9zOPuhA5tLnweoZUPmvYWUe9tAmpM9/OnTAGdgEM1uKr1urob53BjZDB573c93YsSWLmXkfE9MtDA2k8JIX6DGmdtc6WMXueMPbrKlwXnfoDC02HyM9wEh3rMSU6/Fm5PxNcUh00vSXgA29cah4hU42Czh7vSYKVqYAp3tIGdyiZl3t4FJ9G7TY8DqOo/KXeXspb1EEFIoFLXIRU46BTX2Wur/oAeSGd12Xic5c8s8CI3xyz36h8jCHM1Mqdzi143y4285JWjQ1VzgjO2MlmdE9XhNWRz+snmHowFB3CinHQtOP4AWRymnuKbrQgfU9NnJpE00vUvIVMiaGuvTQrtyeYVjZAiK/GRcUTOeQ6huBDhQKOWTSKWV88/wAjm2jq1OPugjru+P1getWtQFkHOCsYb0KCq4ZeM10OIRESd6kLmgLK1RmN5ylKt8yJMrKFLQo9EKsrj7kf+mVCMYPqsre9roRbSotUyFIbb0AYTXOa6LSrEtl72Lexlt+ewQP7a0pT/Om9Rl1TgcYlp3acLa6boxsMLMFLTyShMrLU7cCmxeAVgB0ZOP8YR3gOnrOCDDUFW+OmHPNKqk6oEKL+0ZgF7tULiyVGSudfLG+Niz8lU6llNLMMO1USg8FhvR3mKoYU60VwbVYmEmfza67+UxYv/rauBBjKgN7cIM+a0OhC+aOSxDWFlQYr5nv1Ea2TMrC1nUZ1JqMauAcEkc76EDGNXDWehsL9dgDXszoUz3bdFxkR3YiqJeVcUtVz9ZkTbVME309XaoWAj3NKVU9Ww9jA8fvgo0RZqtQ9RGKmXicBUF4dOixWxa0hYsVDx2hp8PcuB06wsJpVrEHOpJyTZx1Rh46oowzBQ3KPq8A9929JWgJdanuArRE5QtnCoAmivzRuK4LV88pDtmUoQ4dsTq61aEjjFKxNIhmWAnWkijZeijxR8PiZJ2aVlZmXRVGu+kIDR/0Nusqm65rgyCsNURpFgRBEARBEARBWAGD/yUc6UMZhGTR09wpCIIgCIIgCIIgCBogSrMgCIIgCIIgCIIgrIKEZwuCIAiCIAiCIKyAYcVH0jIIySJKs3BMWl6E6YVI9fXrKhowNaneSthih+1/WOCTlWU1KfSpCMII81VV6BMdubiKpS6wwqfnxb2Q2XZHl+qtRPXibFTY9BJGOqcqamslm+oN7sNMZbSSjQReC2EQwOKYaiZb0wtVi52UbcKx9bnfSMjrxjY2quihXsV8Qt+LZbMc1f5PJ9hip+mHam5zbTPxfL/l8BkN2fXBsmGksnrJFgYIWg2Vn6jmEZ1kY09wL+5t7Tp69BpeLhuvm6qenUrBMPQJlFT9mZs11RtOjalpaSWb73mqv7Wt1nt9ZBOEtYZeOytBK6bLIa690cN0OQLXpzOGTTzvKY6qsJk0tSZwy25gvha/7isBF2yCUu6TptGK8KN7gPG5h2X7ubMiLVo9hGGIqek51BsN9dp1HPT2dMK2k58KojCEd+BeBLNjfKGUZnfjOarXrw4bj+b4XnhzE0AYKQUmPbhV9Q7VQbbq3DTq5VlElM22UOwZgJvRo7XT5LyPwzOeMiTZpoH1vQ46NWlz5s1PozGxD1EQqI1uqm8Ybocefcu9yhwaY3sRBr5SENzudUh1DWihyNQaPg5PV+H5ceukjkIKfR16KIBhfQHNffcgasV9wdmn2R0+Qwsli0pfbXQPQvYsZyXtfAcyAxu1aInFtmsT0/Notjz1OpN20dtVgqVBM3oaAyvjB+HVK+q1k84g378epgZtp2gEaRzeBX9hNlaa01lkhrYp5VmHtWF+Zgr1akXJRqW5o6cPjmYGuLUA57ak57ekP1+QnGbhGHz3Vh9jM5HqR+tawN17Q9yxO4AO3HMQmK4Arh0ryodngV1j0II79gKHpoGUEx+HZ4Db90ALygtV1Op1mGztZJpoNluYmStDB4LpQ+pQvZ2cFKLaArwD90EH/PI0vBneYIbqDxo062iM7lLW+6TxGjXU5me4J4JpWQg8D+WpcYRh8s9qtRHi0LSHMGS0iqG8zQcmPbS85K9b2GqgMb6oMNuuMto0Jw4goMcoadl8T8nGr7FiEKE1dRjBotKQqGxhhNHpqhpDZUA1gJlyE5V6rGwlbtw6cD9CRqvYLnvYwZ85jEA9u8lT5/1Vr8QecNNCqzyDFg1xGjAzX0G92VSRRzyq9Qbmysnfb6Q+OwmvVlbXjHOcV6+iNj0OHeC64M9PKdm4NoT1Cppje6ED9eoCagtlpWyp69ZqYn56Uj0ngiCcPKI0CytCr9DUfIhsmn0lDWRShtqU0+usA/QwU1m2LcoXh2a3vc5JM70Qy0WFnge/5zkd8DxPNS2gwmwuHq1Fz0LSRA3Gs0dKgVHhbbajQrVpyU+akF4rehEWZeNXhvVGfvLXzvdayvtt2dyIm7C4cWN4qgayMSybcwnnEG7EXceAH0ZoepEWSjPDeA0npa6b4bhxWG8zjsJIkshrxgqzw/vNVF+jKIjvw4TxA4bah7AXx9S2zFhZ1cAQgsBXHub2HMKvDOdVobM6hPA2aksKczu9gwY4HeA6YBoPrwsMH2+HaidNsBg1QMVPrQ00hmhy3drj1x5XmDaCRlULxZRh2SS+bqZK2/F9TxkIBUE4eURpFlaESmg+Y6DeXMyJCeIFgOd0IOty86b0GKa/UmdAVpOIo3w6lo0y8eD3hTS0wLIslWet8nO5iWOek6VBTDs3HYv5pFSS1YYj8AE7pTZISaM238tkiwLviI1vkqj8ZYPPQSwbvzIU1TCTl43eZc4lrD9A+CywLoIOec1qTE1TjaWC9xuvmwYhn5TBWJQtvt8oWxzlkDQM12UecxDEcwiNIpRNh7QdUHGx7Iev26LBrf38Jony9jGCJlicQ1SUSmyI0wHbtlQUQXtt4BTMczqgrtGSXJFK36EhSQdiOXivhYv3nA/DZc518s8DlWXSlo1rA3OadUgHWGuoNVWDQ0gWGQFhRTjhX3aeg3zWwHQZqqjVcI+J87bosYjuGI6V5EoDqDaBjiywpR9acO7GuPhXuQqUa/H3PKcDxUIeKdeB5/vqYC5zZ0cROmD1DMMsdAEs9tKsKs+fo3IRk998OKUe2IVORH4r9mQx/7V/gxYFX1LZPFK5gtqM0ztJD1G+s0d5npMmnzHRXbSVYtVoxZpzf4eNtJv80sOcQ7drQFnd6ImkguV29sHK5JMWTSlXqZ4hlQ5AjzhzOp1iN+xcR9KiKYWZ+cs0fjBEm8pzIWOjSEtmwnCucAa3wbAcRBxTrwGr0Am7exA6kO4dUkpW6DVUgTc7k1f3nA50lvJwHEvlNnt+ANe10VHUoy5CprMXlpteLNrXUs9HtkuP6+Z0DcDMFtW9xrVBPbt9G6AD2XwBqXQGQeCrtB0qzMXObi3WVEFYiyS/qxK0ZUO/iV99toPR6UiFGG8cMJFy9JhsO3PAM7bHYc+c/3uLcf6wDpRyBq68IMLYbPx6oJMKvh7XjZ6Dvt4eNBpN2saRTrlaFAEj9KK5Wy5AWJ5SCoyZK8FMJ6/AECrH6eHtCCosthXATOdgpfXYUHIDVOxdh1a+qJQrm8aGdPJFaNqyre9xUMpZ8PxIzR/5dPIKc1s2KqZ2trhUPdvKFrXZUKY6+9U9xqJRfDbsXEkb2Ur5FFKuhUYrUEp0LuNo01nBLvXATF2AsLZAVxusQpcWESHEzhaQW789zk2ngp8raiMbjamDfV2oN1sqhYeFwBiZpAOW46I4tAleLc6xttNZdU4XL3h25Ez4lTnlAeccYrp6hJZRSe7sG0CzXlORDY6bhuPqcd0EYS2ix2wtaEtXwURX8gWCV4SeZl1Cso+GSvLmAWgJwytzOT2UqqPhBtLq1PPCMaTNLnZDR5QCmNXDwLCSbKWsHpvvlWSzc3pEWqwEPZHQwPO9EmmXEQN6biFobNPF4HY0lptSh47QqFqw9VwbmIaSKiQfabHausVoJB1hfnomp+ezsKagrTdpe2/Sny/IEAiCIAiCIAiCIDzR+MxnPoONGzcinU7jkksuwU033XTM93/lK1/Bjh071PvPOeccXHvttau+97Wvfa0yPn/yk5884vwHP/hBPP3pT0c2m0VHR8cx23gtP6655poj3vODH/wAF154IVKpFLZu3Yq/+7u/Q5KI0iwIgiAIgiAIgvAE4p/+6Z/wtre9De9+97tx66234rzzzsNVV12FiYmVW93dcMMN+I3f+A286lWvws9+9jO86EUvUsddd931iPd+/etfx4033ojBwUfWjGi1WnjJS16C173udceU74tf/CJGR0eXDn5Wmz179uDqq6/G5Zdfjttuuw1vectb8Du/8zu47rrrkBSiNAuCIAiCIAiCIJygVzSJ42T5i7/4C7z61a/GK1/5SuzcuROf/exnlff3C1/4worv/9SnPoXnP//5ePvb344zzzwT73//+5Wn99Of/vQR7zt06BDe+MY34ktf+hIc55EFhd773vfirW99q/JUHwt6oQcGBpYOerfbUNZNmzbh4x//uJLlDW94A37lV34Fn/jEJ5AUojQLgiAIgiAIgiBoTrlcPuJoNpsrvo/e3ltuuQVXXHHFETnufP3jH/94xZ/h+eXvJ/RML39/GIZ42ctephTrs8466zH9La9//evR09ODiy++WCnyy/ubn4gspxs9q3gI2sAbuOVHqtcq+3DqUr2VsBpkxMqy7F3npjWTjb2t4x6h7IOsl2xATfXfBrLpuCe3TrBtEseWVUl1um6k4QGeHxegszQzOTY9oOUDGZf3HLSCfYYjr6XaiOlSLXj5BsAPwsX+w3oNqh9EqqVe2mF3AEM72cq1CGnHQDatl2xqbfCaqnq2Ln2Q27D9WrkSUTQUs7qtqfF6T4nYS1032dSYIoLh6LfeB0GoOlLott4TjmkQAimbSpNesq0VDNNQR9IykPXr1x9xnqHX73nPex7x/qmpKQRBgP7+I/ux8vV999234meMjY2t+H6eb/PRj35UdV1505vehMfC+973PjznOc9Rnu/rr78ev/d7v4dKpbL0e1eThYaCer2OTOb0Fy3Ua/ciaAV7cB6crKPeCtQi0JF3sK4rpcWCwMXT230bwoUZ9drsXAdn4zkwNGiRwU341Mw8ao3Y+pdJpdDbXVIWvqThwnnLLuDAJLceQE8RuOSMWNHSYeNRmx5HY2FObYxsN4V837AWrUVoYLj/MPDgqGrri3wGuGgLUMpCC3aPA/cdAvwQYLvcCzYB3ZpUvfdnx9Ea3QUEftxWbHi7agOkA9V6E5MzFbXhNS0TvR155HN6VDaemI9w4/2xgYtGkPM3Rdi6Lvm5l4zPhrjuJg+z1bgd4cXbbVy0XQ9lIWjW0Ti8S31VfZs7+5DqXa+FbOVqiH/9YR2HpgLVouvsLTauvDitDNI6GEH2TzRQacTG3lLWxnBvSrUUSxr2oG8efhBBeVqtDVa+A6mh7Wo+0WHdmpmdR7VWVwuF67ro6e5Ulch1kG3/VICxuQghjeQucMagjYyb/JgKj54DBw6gWHy46wOLZJ0ubrnlFhXCzfzoxzqn/umf/unS9xdccAGq1Sr+/M///DEr448nye/iBW05PB0voLFlMsJ0uYXZBQ864O2/F+HsuPIkwDARTh1AML4HOjA3X0Gl1oCx+F+13sDsfNxfMmkeOAzsGot7W9smcHgGuE2Py4bmwhzq8/GmiGPq1WuoTB6GDozOAvcehNp4OBYwWwFu2R0r0EkztQDcfSBWmB0bKNeBn+0BvHjvmyhho4LW4QcR+S3AshG2GmgevG/RY5Qsnh9gYnoBvh/Pcb4fYmJmAS2GEiRMy4vw4/uAcg1gVydGENDYNVV+OHQtSeXqups9jM+FygPOgJr/vtvH3rFQCyWhMbYHQX1BtYhTntPpUfhK2Uqe637SwO5DAVzlxY1wy70ebrlPjzV1dKaJ+ZqvlHkeMxUfE3Mt6EBr6gD8uXG1LsCw4M9PoTW+FzqwUKmqg7sk3nONZhMzc/PQgamFCIdmQvUcWGaEhXqEh8b8I0JghbUHFeblx2pKM8Oe2Wt9fHz8iPN8zfzhleD5Y73/Rz/6kSoiNjIyorzNPPbt24ff//3fVxW6Hwus7H3w4MGlcPPVZOHfnISXmYjSLKxISK9fMwAdt7Q021a8AaHXWQeiyixgOTAYwutwwjAQ8pwGNFqeUkpVuKdlqu+bLT02H9ML8de0C7hOvCGfKkMLglZDWepN24HJEDfLVuciDTTT+RqWLPVUTOmZr9SBZvL6lZLND2KZqNAzdJzeSR5JE9arSmE23IwaT35lmHbYrCUtGlpeoMKy6RFiFIhjmwjCUJ1PmoVGPH65dHy/5dNxWsBsNWnJeN9HmKtEKGQMFTLOEGPef5PzGmzEwwBhowaDa4Nlw2SUCkNnG8nfb1w/D04ESKcYfWQgn+GaCoxNJ3+/kVojVMoyvd48aCvnOR0Ia1y4DOVZVt5l00ZQ00MxbbVioweVE6Z3cC5pNltaKKbVRqTuMdeJx5RzCecVRpwJJ8daLATGqIenPOUp+O53v3tEJCRfX3rppSv+DM8vfz/59re/vfT+l73sZbjjjjtUNev2werZzG9+rFWt+bs6OzuXjADHkyUJJDxbWBE+mpxkm16EyGxP/rHyrANUlMP6AhAtVtqLwkXlOXl4jbjXaC+a/GLRI64BVKyo/PHgGFM/6MhBC9q5rkvXLQziTa8GYZU0LhBuNriZpAJDowOV1KRhnhovEWVjRCBl4+3WljlJ1CaX3qHQV0YuFaJtmkqpSRoaA6kkhGEEyzKUoZCbEh3CUTmmnGrpYeaYUinlGPN80qTdePPNHHoa3zw/WjqfOLy3TAshIxsiO558mQOrQR497y0qyqO1QM1xnIMpXi5j6LFu2QbCFpWseDz5PPCcDhiszss89bYiGgXa5KpTWSZL1y0M4bqc95K/dpw7KBXnOIrjL+U1Jy2ZcLpgu6lXvOIVuOiii1SxLfZTZhg0q2mTl7/85RgaGsKHP/xh9frNb34zLrvsMlWxmu2e2Df5pz/9KT7/+c+rf+/u7lbHclg9m17h7du3L53bv38/ZmZm1FfmVVMhJuy1nM/n8e///u/Ka/y0pz1NVcymMvyhD30If/AHf3BED2hW7f7DP/xD/PZv/za+973v4f/9v/+Hb37zm0iK5FcSQUs44Q90pnBwqqGKSJBMykJXIfnNLrGHtyN8qAJQcaYFLpOHNbAZOlAq5tFseSrsk1eOhUE6inpopmcMAmOzcdgntWaGV549Ai1IFzrQqpbhNxvxRte0kO3q12LzMdIDHJyOQ6Hbm5Ezh/UouDXQAfSVgPFFxwt1vh0DeuSpm/kOWB198Ocm4kJg9GT1DMNIJ/88pFwbxXwa85UGAs9XshVyKaRTyc9x+YyBHcMR7t4PzNO7bADru4GhI/cqiUDl+Gk7bfzwDn8pXHykz8SO9cnvxGmgSfWtVyHaESNXqNRkCnA6eqEDlz8lpXKapxa98v3dJi7aocGDynmk00WjFaLhxbKlHBN9peSfBeL2rEdYLSNs1ZQWaDopOH0boAOFfBb1egMtb9HjbJroLD2cb5ok/SUT0wshFhpxcTcaBEd6LGUsFJ4c/Nqv/RomJyfxZ3/2Z6qw1vnnn49vfetbSwW2qNQur7fz9Kc/HV/+8pfxrne9C3/8x3+Mbdu24V/+5V9w9tlnn9Tn8vP+/u///oicZfL9738fz372s5Wi/ZnPfEa1paLBicp0uz1WG7abooLM9zCPenh4GH/zN3+jKmgnhRHpEEOSMKzEViqVMD8/f0Ry/WPB8zxce+21eMELXrBiD7O1AkO0a8xrNoBiztbG00zCWhlheUopzVbHAIxUMjkOK+F5PuqNplKas+kUHLpmHvfPPLF7juFZh2bifFwqW515aEMY+EpxjsIIdjoDJ61JpS2VAxtft7Z3vlePfZGCnsjDs7H3r5iJx/Xx3hed6P3GSsbB/CSiVlM9o1axRwtDCOHyV623VH4z57Z8Vo9Ch23ZWHOA4ff06G7ooVdLD9nI/okQE7OhUqLPGDZVCOjjycmsqX5tAWG9okIu7EKnSvnQBYZj7xuLU5+2j9goZPVZU6k0L9SZc8Kwe0spzrrAegjBwrRSmq18J8zTsDac6D3HThn1WkNVz06nUsrTrAteEGF6gdWz45SKYsZ8wuyzT6fc93/wj1BY1kM4CRYaDWz/k4+suWv4REI8zcIxyaYsdeiImS2qQ0eoJJ8ORfnRwJzXbeugJaZlI13Uo7Ly0XA4N/ZBS+jxpjdcR+j9szuObBuhCypklg+EprINaeJdXgl6l3noiJ0tADw0ZKDbUoeOpF0TaVcPz/fRmG4aZvcQdITRZIVC8tEzK+FYBgY69DG2CcJaRs8VTxAEQRAEQRAEQRA0QE9XmCAIgiAIgiAIQsIwayfpzJ2kP18QT7MgCIIgCIIgCIIgrIp4mgVBEARBEARBEFaCFaaT7tWV9OcL4mkWjl/BlX04WXlRR9mCQE/Z2lUr2z1MdYNtxBrLenLqRBT4iLymnrKFoarwraNs7MXJcdX1ukXNuvqqG6zuzXZY/KobHEuOKXvm6iibH4Tayhb5LdXrXc/rpue6pfpHc47T8Dklke/F46rjPcd1gfJpKBvHk71ydZRNENYS4mkWVqXpRbjnUITZSpxLMdITYXOfoUVLFm44Dk81FttjsG2Sg4GulDay3XMAGJ2LXw90RNipevoaWihWNz8Y4b4DbI4BrO8BnrGTFVMNPYwg++9FcPB+alkwiz2wt1+sTSuxenkWtdkpJZvlplHoXQfL0aPS7Ph8iN3joWo9lU0Z2D5oIp9OfkxJOHkA3r0/AVoNGOk87LOeDrNTjzLkYWUWrf33Al4DsF0468+EVdSjXHWlEeHBwwHqXgTbNLCp30RvUQ87d8vzMT5dQcsLYJhAdymHUj7ZdizLWxN5B+5VLQkpnN03Art3RIu1gYbUfRMeyvVQ9c3tLVkY6rK1kI2K1czsvOo5THK5LDo7ilrIRuOHv/sOhBP71GuzaxD21gtgaNBKTDkWxvfCnz7EFzDznUgNnwHDdvVoqbdQxkK5rL53XRcd3T2wbdn6C8KjQY8VWNCS+w5HGJ+LFeYgBHaNP6wIJs3YbAOzFY/LgloMpsotTJf5Onl2jQH7pgA6Enjw+4fGoAX3Hohw+256ruLXDx4CbrpfD+tzOLEfwd47gSDuExpOH4L/4E+hA61aBdXpceVNIF69ioXJw1pY7sv1CA8cDlWPZvZTn69FuPdQqIUnK6zOw7vzv4BFBSZcmIF/5w8RteKNeZIwmqG19y5EjQoiw0DUrMHbdxfCZj1p0ZQn8v5DARYakZp/acB8aDRQinTS8J6nwlxvekrxY7TP5GwFtYanhwJz4D4E5SlOIYhCH97oLoTzk9CBA1MeZir0fsfRA6OzPqbKenjD5+YXUKnW1Pe8y8oLFcyXK9CB4OADCA89qJRSEo7vgb/vbuhAMDMKj8o8vfOGgWB+Aq3Du6ADjXoN83OzS5EDjUYdczNTWqxbaw0aj3Q4hGQRpVlYES7oM5W4N61rAxk3VgDnqnpMttV6oNI7bMuEY8e3cbURKzRJM7UQGxrSTnxQkZlegBaMztDIAOTScb9m1wEOTesxptHCtPIoGOkcDDcNOCm12Y2C5DeVPkOL6WF2XNVLmoffaqpQbR2UZi+In1HHNtQ9V2tGqLeSlgyIytNKGUW2pMbUyBQQ1SqIKrNJi4awXkHUqgNuJvYKpbJKmY/q5aRFU2NXb0VIOXGf1bRLLyWwUE/+WWVIdtPz1dxrcf61TBXB0qDVJmnCAGFtXo0nD/b2pTLDc0lDRaVcC2GZ8Zi6tqHm4oWGHqHQjWZTbcoty1J9hw0YaDY1mER47Wj0oNKQysRrg2UjmpuADgS8t6Iwnt9431kOgsqsFoppqxmHstOzzHE1TWvpnCAIJ48ozcKK0J5lW7GHmfOrmmOjeLHXAcs0uE7FuWvqiM/pAI0MNDC0rxuNvDQ+6EDKMY6QjeG83JBrgeWo+24pt5QKqUWrQ/LTlGFa6mt7s0EZleVXA9loM6KRpu1Y5jPLR2HRlpQoDJ80GL/bNi4EdIfT2pX8TWdYdixLO++VX1Wxl+QfVs697QgfosaWYxrfholiLno8qCgfcV6H+VeNn7WUy7w0l3CsE4bXjOMXz78P1x7Q4TkllmkuW0+ZvhPpMabEceO6CO3rxvlEk9QYKslYft1o+LX0CLlvj98R65YpHktBeLRoMl0LusFJdUufoazilWZ80Ds51AUt6O1wYVmGClvkQat9d1GPRXRzf+xhrjTig94intOBnSMGillgdgEqV50K/vlb9JgGrIFNMLIlFcrLsF6Vj7jhLC0W+FS+CNtNI/Bb8L2msiBlSt3Kcp80PQUDpayBRguoNllgCBjsNNR9lzRG1yDM3vWIGlWElTnAa8JctxlGIfmJxMgWYXX0Ayws1Kgq2axSr8pJTBrOHwMdphpLRg3QiduRNdCVT/5ZoHe5I59WIbz0OLf8AOmUjQJDVxKGBhqnf6Pykob1KqJGDWY6B6tzHXRgsNNRBi1GsvNIOwZ6S8kr9KRYzCvF2fN9ddDbXCzkoQPW4DYY6axaGyKmejhp2MM7oAN21zqYjFJpVtVBQ6rTvwE6kMnl4TgufN+D5zFqwEChWNJiTV2TfZrNhA8ZtsTRY7YWtGSwy1Dhu7NVWqGBdR0MAdXjqS1mHWzoN1CpB8o7WczZSLvJKzCEG9uLt0YYZ0RgBPSVgI6cHtetu2jg6qea2D0W5zUPdRtY16WHbEYmD+e8ZyNgfljgw6QC06XHZpfh2KWBETQq88qTYKcycLN6bChZYO6cERNjc6zWHhu3+op6eBMMhnqedxnMww8pBYaKqjm4RQ/ZDAPOyFlKSWaYtuGkYXUPahE9QNk295vIZwylNNMo2F+iETP560a6Slm4ro1my1cyFXJppUzrgNU9pEJlleHNtGB1DsRh2hrQVWDos4sFFgIzaARhEUY9rlsmnUZfbzfqDRoFgWwmDZcbAA0wi91wznkWwqnFYltdA+qcDtAok9p8HgKmEoUhrHwHLA0Mb4Rh2T19/ajVqko2N5VCKq1HYU1BWIuI0iwc14vVU4CW5NK2OnSEnr9SFlrSkTdw4VY9Nt9Hw3xme2QndMS0bWQ79NioHQ3TJtZ3azqmtgNr5EzoCBVku2cYOkKlioqyrrLRs6yDd/loVF5usUcdOlLMWurQkVTKVYeOmLmSOnSEnmazTw/v8tFYtq28y4IgPHb01DgEQRAEQRAEQRCSph0jnbQMQqLICAiCIAiCIAiCIAjCKoinWRAEQRAEQRAEYbVCYAlnyyT9+YJ4mgVBEARBEARBEARhVURpFo4L+3G2+/zpxvJ+l7ohsj0xZQt1lu2o3rk6oeuY6i6bzvMvnwVdZdN5HtF5THW+biLbE2/dEoS1hIRnC6tSb4b41o/rePCAD8cGnn5uGhfvdLVoF8O2P82JA/DKMypkxekcgNu9Tg/ZohDNycPw5qfUa6fUjVTPkBatbLh4Hp4JMTYXqJZT3QUTG3otLVrZULbGwhzqc9PqGrqZHHLdAzAtPSrNHp6N8OAo4AVsKxZh5xCQ1qQF24HJCDc/GKHeAnqKES7dwX7cesjm1yuoTR5C5LdU659s7zCslB5tT+rNAIenm2h6oWrrNNidRjatx/02X43w4/sizCwALFL91DPiFnE60GhFuPdwhLkq1NqwbQDaVPoOggAzs/NoNFswDQOlYgH5vB6tDJpehO/+tIn797MPMtR6evGZjhbrlh9EuPmBCA8djtSaumO9gQu3GDB1WRtmxtAqT6s2jk6hExmu95qsqfMLdcwt1NX32UwKPZ051fNaB8bnQxycDhAEQClnYHOfBcdOfkzXGrzXkr7fkv58QTzNwjGgwnzLfS14foRyNcJ1N9Zx714POtCcPITWzJhSnsPAR3PyALy5SehAa3oMrenDiEJfHa3pUSWrDkzMh9g/FaDlA0EIjM6G2D8ZQAdatQqq0+MIfU/1lGwszKM6rcd1m6lEuGs/UG3Su0YFGrhjvx4eypmFCD+8K8JshR4sYP8E8IM7IwQaeJ0Dr4nq2F4Ejaq6Vn5tQb2OuIPTQEnYP9FApe4r2WqNAPsn6vD8UAvZ/vOuCAen4vttpgI1xnPV5MeU1+qugxHG51TLXFQbwF0HIszVkpeNUGGu1mIFxvepQM+hXm9AB7730yZ+ep+n1tRKPVag79rtQwd+tivC7bsjtTY0POCWByPcvV+PMW3NT6HJ9T4IYoP57AQaM+PQgUqtiam5KvwgVBEE5Uod07QmacBsJcTu8QANL/Y0T5ZD7BpPfu4VhLWKKM3CinDD/cB+H5mUgWLORFfRRMuLsPuQHgu8X5kFTBOm48J0Umr35lfnoAN+dV5VbKBcSjbDgF/RQ7b5Wqg24fSQphwDlgnMVpNXEojXqCll2XJcWLYD07TQqlHZSl4+Ki30MOdSQNqJj7ka0NTgcaDyQg8z+4LTI5nPUJGmpzJpyYCgUUPoxR5mk2PqpBC2mghadS28zPQwpxwTjmWqry0/Qq2Z/P3GPTeNIIVMPKYc23oTmNBgGqFCNV+Ln4GUEz8TfhDLmzQhjW2NpvLy2ZYFx7GVskCvc9JQiX/goI+0C7WmdhZMdd32jmkwiYDGtggM6uH8wftOnZvUQ2n2qmU6mBfXexeGacCrzUMHao0WojCCY1uwbUtFN1RrLS0MquV6bDzNuAZcx1DRDfO1SBnlBEE4eURpFlaEEVkMu+Oi/nC+DtTEqwMqTIVutXY+YsSQMj3CKg3TYvz4wzlOURif02Rcl+deUYHWIfyOtEMU27IpZVnJlrx87aio9laDtx6lotEhadoytB3LjCCgvNwgJY2heluqmy4+wTFVQ6rDmBpHirb4VYfHgWNHOTiWS2Nr6HG/US5eova+O1o8dJBNzSGGsWwOib/qEP5MmAJw9JqqS6gs13sGgCwup+qeczSYQ5bW+8V1q32ouUUDqCRHi+sqUcq9DpNIey5T49l+HuKpVxPx1haGJoeQKHrMOoJ2cJPBHGYyPhNgfCZUlvHztrnQAbdrHWBaCJs1RM06DMeF09kHHXA7+2BYNqK2bJatjWz9HRZStoFaM0K1EarFc7BTj2kglS/BchwV0uu3mmqByJa6tNjwrusA8mmg0gAW6gAjeEd6uKlMXraRXqCnFHv/6BGnJ3DzwMPeoiSxs3nYmTxCr4mgWVeh906uBCuVfI5pJmWikLHhBREaXqi8zPmMhZwGOc30LG/sZ+5w7MEt14DeIrC+J2nJ6F02MNQVK/QLjThloZBmTrMe61axkFOKS6vlwfN82LaNXDajhWxPO8tVc+7ETICJ2QilvIHztjjQgbM3mnCdOEqFR8YFztqgx9rglnpgWg7CVkMdVJhTHXqsqcV8WkWqtPwALc9Xek1HIaPFutVTNFVUGaNUao3YSMP1XhelXhDWGlIITFiVS85ykc8Y2DvKQmCGUpj7u5LfUBKn1APDsuAtzCkrtFPshpXJQwfsfAcyw9vglWfVaxYtsXNF6EAhY+LMYRtTC8y/AjryJrryemyMbDeF4sB6NBfmEbIQWDoLV5PrxvC2p26JcGAa8HwWVAGGu6AF3BRdeQFw74G4QFNn3sD2YT28a4ywyK3bpHIS22HaKT67GshGD9H6vjRmyl5cCMwx0V1gWoChh9HyTBbqY6h2HF65Y1ifSJ9t6wwVls3wT64NfBaoTOtAsZCHZVloshCYaSKfy6gwbR04f5uNTCqDPVxTLQNnb7Yx0K3Hmrp5wFDK376JuBAYX6/r0mNMnWwBucFNaC0wPyFShjdHk7Uh5ToY7CthodpUqQDZtIscLQ4awHnjrPW2KgYWBJFa/3uLeoypIKxF9FhJBC3hxu3sLa46dMTOd6pDR+xsUR06ks+Y6tAR203D7o4jHHQjmzKwfRDayvaUrfxOvw2RadlIdw1AR1g1vrfD1Va2nSOajqlhYLhbT9m4buVzWXXoKNuODbY6dGR9r6EOHWHECg8doeLMQ0eoOG/s1cMws5bhs5u0sTfpzxckPFsQBEEQBEEQBEEQVkVPc6cgCIIgCIIgCELSqKKWCfsZk/58QTzNgiAIgiAIgiAIgrAaojQLgiAIgiAIgiAIwipIeLYgCIIgCIIgCMJKaBCdLW7O5BGlWViVMIwwNttEuRbANIHekovOvB4VIqMoRGNmAl5ljiUFkSp1wy12a1FdkLJ59/wU3oN38AWcbefC2flU1RoraaIowlzFw8xCU/VsLGZt9HSkVTVcHQgO70aw507A92D0DsM+4ykwbD3uuaA8BX9sNyK2Tip0wRncpo1s9aaHqbka/CBE2rXR25GDbSd/v5GwPI3ggVsQ1cowCl2wt18EQ5PK8mGrjsb4AdXv3UxlkOpdDyutR9XlphfhwJSHaiNS7ZyGu21tqt57foR9UwEW6qFqnbS+20Ipp4dsfqWKfZ/9R8z95GewCnmsf/mL0f3sS6HPmtrCPNdUw0BvyUZn3tZk3aJsPqYXAtVyqrdoo7dkaSNbeOA+BAcfAMII5rpNsDado82a2qrMoTE3BUShaoWV6ezXQjZyYAq49yDQ8oGBDuDcjYArO39BeFTIoyOsyuhME5NlD2xbGkbAwamGaoNCRStpqDA3ZsaUwkzlL5g8pMyAqWLyzXO9+29D8+bvLbVjaf70e6DVwd351KRFQ7nmYXSmrhZ6AwYm55uIAPR3ZpIWDeHkQfh3/kgpzDAtRLvvhB/4cM55ZtKiIazOw9t7JyK/pWQLJg8oOZ1N5ya+qWx5AUYnF+AFodqIl72mUp6H+4qJyxY1qvBv+z7CyrwyMPArzzlPvQqGnWyrpyjwUT/4EIJGRY0pFeiw1UB2w06YCRtDqFztGvNQroWwTKDeClUP7h3rXaQT7ofMuWPXuI+ZSgjLMFBvRXhw1Ff933Pp5BWF3Z/4G4z/y3Uws2mEew/ggfd+EjsLeZSeck7SomF0toWJOU8ppVy3DkyFqi94Ry75NXV8zsehGU+tWlwTDky1YBiuUuyTJjz0EPz7blp8ZSB46Gdq7bc3n5uwZIBXLaPK/Qc3SYaBYHZSXcBsz7qkRcPEPHDjAzRysYUdcP9hwA+Ap21PWjJBWJskv8IJWqI8klUflkGrpImUbSAIIpRrPnSg7WE2nRQsN4UoDNXipQP+nvvUjsgsdqqDC6i/9z7owELNVxvylGPBdUylVJWreoxpOHkIkdcEciUY2QLguAjH9iIKgqRFQ7gwrTzMSOVguJlYtoVpgEp0wtQanlKY+Zw6tgnbMtBo+mhxd5Qw4ewEouo8DI5pJq/GNZqfRlSeSVo0BI0qgmYNhpNW84jhphE26wjqC0mLphTRaiNEyoHyMrPXasOLsFALkxZNeazKtUh5mJVsDtAKIszXqGolS1CrY+Y/b4RdKiC9rh+ZjevhzZcxe+OtmkT5MGqL181E2jXjNVWT+XemEs8XSjbHVDrgXDX5OYSEE/toSVIRKmptMAy1NuiAV1tAFAZqH2I5rvIw0/PM8U6a0dn4eS1mgHwaSNnAwZlYiRZODsMwtTiEZJEREFZFWcOPPKNNGG/bw0zai1PSXrUlaNJdvmDye9OCDrQv0dKCHhvH9cA86oajjKrNgwYCHi2Dun6GFrI9QoS2aIuRDomiQhTbvit+CWOBdQhdXLpwDz8L8ZAaGj2ni18Xz2kg2iNki18YeshmWWoeWTK0tdcGnteA2MMcabluKTEesaZCD7gORNGydSvSYw5ZtlF6eFz1uXBqST1qK2LoI54grDk0mXUE3eBC3l1w1CTbaIVoeiFs20BJgzAywhxmTvxBMw6pNC0LbqETOuCccX7siZydVAdsB84Z50EHSjlXeSI5no0WN5YRugrJhsm2YZ6akckhWphFxEgCWu9HdmiRG2aW+mCkMkCjqsKLEfgwuwYSDzEmubSrIgdafqhCtf0wQj7jKq9z0phd62B09ALVeeVxRr0Kg+cK3UmLBiudh5UtqugGepgjrwErXYCVST7fmp7ljpypvET0Ojda8blSNvkxdSygu2Cq+4yy1VoR0i7QmU9eNjPlYuBFz0dQb6C25wBqu/Yh1d+D7uc+Q4s1tafIsH9DrakMt+dczJxmHWAOs2Ey3D5E3YtUKlZ3QQ/ZzKGtak2NKrNqfaDCbK3XI8bYzXeodI7AayJoNdSamlb7k+Q10/U9XB+AuRowXwO8ANgyANh62JAEYc2hx4woaElfR6xgLbBoidooucil9ZhtWfSL1mePoZ8w4RY7VQEOHXA27lALprfnXmWBtjftgL1BjwU+n7Ex3JtVxcBoGS9kHZRyehSzMku9cJ5yBYL996l8YbNnEOb6HdABM52Du/kC+FP7Ac+Dme+A1TsCHWDBr8HeImYX6vD9ECnXQlcxq8WmzXBcOBc8B/6eu4DaAlDohL3xbC08fzTGZIa2ojU9qvKZTTcNt3udHrIZBjYPOMi4PmrNCK5tYF2XDcfWYEwNA5v6LJVbvdBgITAT6zrj1zow8prfhNvVgblb7oRdyGHdr7wA+W2boAPMD2YgkiquaQBdRQf5TPL3G+kuWEqm2WqgvJFdBRsdOT1ks/pGYJz3bASHd8WpT/0jMAf0GFMnk0N+YATN8qxKE3NzBW0M+B054Fk7gQdH4zDt/hKwLflU67UJbYJJ2wWT/nxBlGbhON7mootuPXTRR8jGol86FP5aCSrJuijKR5PPcKOmh6J8NGZnvzp0xMwW4I6cBR1xHQv9XXnoiJHOwTnzEugIPUTpfj2MH0dDT99wj6bPqWlgqJsKlR5K1XJM28bgr/+iOvRcUx116CgbFWUeOmL2DqtDR5xMXh060l2ID0EQHjt6zo6CIAiCIAiCIAgaGJWSjt5K+vMFcfYLgiAIgiAIgiAIwqqI0iwIgiAIgiAIgiAIqyDh2YIgCIIgCIIgCCuhQ8+/pD9fEE+zIAiCIAiCIAiCIKyGeJqFVWH7mv/47hhuv3sembSFKy7rx3lnlaADbJfkzxyGPz+liiPYnetgsx+sJrLtGQf2TMSvN/YBm/v1KeJwcDqWLQiB4S5g8wBUqxHh2CzUQxyeDeAHEQoZE0NdlqpwrAOVRoQ9E+ynDhSzBjb1Glq0JyKVBnDPQaDaAEpZYOcwVF9fHWj5EQ5O+arXMPsgD3Xb2rRO8oIIB6YCVBsRUg4w3G0jm9JDNj4DeyYi1f/VtYGNvewhrYdsYRhhdNZHuR7CNg30d1goZi1t1obx+QBzlYCthlULqs6cPrJxbRidi5RDa6jLwLoOPdYtylatNVCp1lQbx1wujXxOj7Z6ZLYSYGI+QBixzZOp7jlTE9lGZyLcsz9Sa8O6LuCcjYZqJSoIwskjSrOwKl+/9jD+9VujanH3/Qj3767gbb+7DTu2Jd+/wJ8+hNbobkRcQaMIQXVeha7YpZ6kRVMK808ejJVSMjGnRMRWDfojjs4CNz/EDXmsKE+VYzm3DyUtmd7UmiEeGPXQ9CJYhqH6rHp+hM39duIbt3orwu37QtSabFMEzFUjde7cETNx2bhRu+F+ygSw/fFkGSjXgWfuAOyEdYUgjPDAYQ/lehT3zq2HqDYj7Bx2Et9UhlGEBw/7mK2Eqr1TuRah0vBw9noHbsJKPRWYew9FGJ2L55AwZN/hCBduBnIaKPUHpz2MzwVgs2HOu5VGiG2DBvLp5APrDs/6ODzjq++VbHUP5joak5JXnA9MQ40r5YqoCFYjGDCwToOWw7V6A9Mzc+q54B3WaLWUnMVCLmnRMF8LsHvcg7+4ptK4SsPNUHfybcUm5yN8/w6uB/HaMDbHOTnC03Yk/5yuNaR6tkCSX0UELQmCCP/54ym4romBvjSG1qVRXvBw822z0AFvZkwt7GYqCzOdQxT48OcXXbsJ0/bi0qvGI4hiRVoHDkzRuxbLVcjEKTJtj7iwOrPV2IubdQ2kXVrqgZlKqDZKSTNToeJMhQXIuOzZHJ+jEp00VJLna/G9lk8DuTQwvQDlodTBO88j7UB5l+lpptJM5Tlp6pSjFikFmfdbJmUoQ8hcLXnZmj4wtRB7mLOpeEzrXmyASxoqK9MLNDTEY8qxpcd+rpr8g0pjw3Q5UErfkmxhpLyUOnBoJlaYOZ58VoOASj5X2eSpVOtKYXYdG45jK4sDz+nA7ALXAUaqMILGUIrzZDlU4500BybjtaEjxwik+JndPRY/E4IgnDyiNAsnjppnNZ1sNbbA6SuZ8MQYR02fSUEQ1taTaqyJCU+rq6mHFE8GoQUheURpFlbEsgw885JuNFshxiYaODTWQLHg4CnnaRCrBcDp7IeBCGGzhrBRhWFasEt65DQzh5nRnfSwlWuxPr+xH1ow3B1bmynXQj0OEaS8wrHpzJnqutHb1/Ai5WHmuaRDjElXnl5SoNqMvQr0iHfmYi9g0vQU46gG3mvMaebRXQA6sklLRm8aQ3YNNDyoMeXYMry4mEl+WaRnmbnpTT9CoxXLRk94KZu8bCkb6CkALS+OZuCY0mvKc0nDUPaugqkifTimHFvHMtChQd4wQyt7Cpaacykbn1Pb5LOavGxksJPB2PF4sg4B17B1nXpozrlsRuUIe56vDi6qzGnWga68qXLnOffyWWVec08h+dQYsr43jj5iegzXfD6zm/qhTb2LNYVh6nEIiSI5zcKqvPjqIVUA7La74kJgVz67DzvPKEIH7J5htXCyEBi/Ol3rYBWTz2cmLPrFjZEKe47iRWrLALRgsAu4aGscLs6N5VAXsFUT2XQmmzKxbdDB4Zm4EFgxExeN0mFjRGWK+cvLC4Ft7ks+/4pQmbr0DODeg8BCI1aWd65PPp+ZsIjbtnUODk7HhcDSjon13Xbi+cyECsK2dbYqBMYQ8pRjYLjHUl+ThvfVmUNAymHYc5wOwMJzuXTyspH13Y5SYhhmzzzO/g5bi3xmMthlq/Bdhtnza1/JRkkTpXmkJx7b0dkjC4HpQC6bBlBCpVJXdUyoRBfyeijNHD8W05yYC1QqVkfOwECnHlvr3pKBy88F7t4XG5AGu4FzN+rxnArCWkSPJ1vQEscx8UvPH1SHbnBxd3qG1aGjbCz6pUPhr5VY3x0fwslBD2RxSI/N99EUMgbO3aDH5vtomM988TZoCZXQLQPJF+xZCXqDNg/ouUTTsHDGOj033/Q2swiTjrUNuTas63JUFWMdZaPiPNJjaCkbPcu6eJePpjNvqUNH1tH40aXfmK7JNs0JL/8a2MGf9Oi5AxQEQRAEQRAEQRAEDRClWRAEQRAEQRAEQRBWQc/YL0EQBEEQBEEQhKSR+GxBPM2CIAiCIAiCIAiCsAaU5o985COq2MNb3vKWpXONRgOvf/3r0d3djXw+jxe/+MUYHx8/4uf279+Pq6++GtlsFn19fXj7298O3/cT+AsEQRAEQRAEQRCEJxpahGfffPPN+NznPodzzz33iPNvfetb8c1vfhNf+cpXUCqV8IY3vAG//Mu/jP/+7/9W/x4EgVKYBwYGcMMNN2B0dBQvf/nL4TgOPvShDyX01zxxiKII85UWqg1PtccoFdLIskmnJrKFM4cRzI6rkBWrZxiWJn2ayUwlxHQ5UN93FSx0F7SxT2GyDByYguon2V+KezfrEvUTVmbhT+wHAh9mqQdW73oYSYdELdJqeahUKgjCEKmUi0I+r0VbJ8K+r2OzPjw/Qi5tor/DUi2VdCD0PdTnphB6HqxUCulSD0xLj0qzQRBirlxDy/fh2BY6ijnY7FOkAWEYorJQhtdqwbIs5Isl2LYe8y/brt18r4dDk4FqNfXUM130dJjarA2VShWNZhOmaaKQz8F1XWizplZbqNY9NXd05FPIpm1tZFuoeViotdR6UMy5yLPJryaEU4cQjO0BohBm7wjM/g3azL/NZgPVSlVdw3Q6jWwup41s5XqEg9MR/IA9wVlZPm5pJ5wcOrRJTvrzBQ2UZm5CX/rSl+Kv//qv8YEPfGDp/Pz8PP72b/8WX/7yl/Gc5zxHnfviF7+IM888EzfeeCOe9rSn4frrr8c999yD73znO+jv78f555+P97///XjHO96B97znPdoslGuVmXIDk3N11XNYbUTqHob7CshooDiHkwfg7btLKVcRX8+NA1ufooXiPL0Q4qFRHx6bNvJ1JUIU2egpJj/jTZWBmx+C6ufLdXN0FvBDYFNf0pIBYXUe3gM/Rdiqqw1HODOKyGvCGTojadHg+T4mp6aWolhqtTp8P0BXZ/KNTFt+hAcOtVBrxv1Vef/VmqFqpZT0xi0MAiyM7YffqHHJB6oR/EYDhXXJG0M4p41PlVFrNOPXND40fQz2lZSylbRss9NTqNeqMGCo3rStZgPdfQNKgU5atutvauKW+z2OqOr3vvuwj5delUVHPvk5bn6+jPnyQjyiEVCvN9DX2wOXDaUTZnahifFZrqkcUcRram9eC8W5XG1hbKaKMORli1CpeRjsAfLZ5PdR4eRBeHf+UK0HfB7Cif2wwwDW4JakRUOz2cT05BSCMDaS1+s1hFGIQqGYtGiqx/tteyPUW1SUgfFyhKYPbB0QpVkQHg2Jr3AMv6a3+Iorrjji/C233ALP8444v2PHDoyMjODHP/6xes2v55xzjlKY21x11VUol8u4++67T+Nf8cRDbdoWmmoTnnItdXhBqKzQOuCP71UWZyNbhJEpIPJaCKYOQgfG5wKliNLjx4ObyvH5eEFNGnqYqTAXM/FBg8jeCWhBMH0YERXmTEEdME2EE/sQcReXMHWlJPvK08dIFvaCrVZrylOZNHPVEPVmBDqFMq4BxwbmKiGaXmy0SRK/XoXfqMN0XFhuCqbtwKtXEDQbSYuGRtNDvdmCbVlwHVt5muNzXtKiwfc8NOt1pSDbjgPbdtBqtdBs1JMWDZV6hLv3eMikoLzLvR0GZsoRHtiffFqUMu5Wq8pYxOfUdmwEgY9ava6FbDMLsYFGramOCT8IMV+NzyXNXKWpoo9i2SwEYaTO6UBw+KFYYc51wMh3MEQEwcH7oQO1alXdY+21gVQWFtR4J834PNBoAfk09yOAZQAHZ2joSl42QViLJGrevOaaa3Drrbeq8OyjGRsbU57ijo4jPTlUkPlv7fcsV5jb/97+t2NZBnm0oZJNqKTzOBW0f8+p+n2nG074vu+pRdQ04gk2DHwVoqrD3+QHAaLIgLE4+XN9Cj0fhg6y+R6iMEIYxNZcfu97Bh5v0U7knmOIlhpOFT0Qf0/nqQaXTXlzuZhzTGms4fhSYMNrwTCT9a55vqfSQbgZp2xtZdnzWgjDhGXzfOXRpaOD8kVBpAw1Lc+A9Th6c0/kfuN147NqmhYMI1IGEMra8jxEVrI3nZKNN79tqnuNcx7HmNfTs5O1J/P6qOsWmWoOfli25OffViuEEfkq/N9AqNYH+sI934TnGYnebwxpD/xAeUqDgBE+cRqXr8F1i9dUP5YxiO+vWDYrcdnazwOfzcCM11R6TuN1SwfZGFFmAlwTlHjm4trgJb+mqrUhhGHFawKvIWXkzyQd6eN7fAa4F1lMweJcEvAZNmBTgz6N6HAfPRYMmOpIWgbhSao0HzhwAG9+85vx7W9/W+WAnE4+/OEP473vfe8jzjPcmwXFTiX8+4THk9jgoZg4DNx/GDoyDWDvnafns453z3GprCx7zcDZa/dBIxhauYzD10FXboe+jJ0mR8yjmuPu3QNduS9pAdYA2/LLXhjAui5gag9w7WkYVllTn6xzXAewFKBSBOohcO21p+WT1/o9d3Rcz/W7T78MtRp3GoKwtklMaWb49cTEBC688MKlc7S6/vCHP8SnP/1pXHfddSocbW5u7ghvM6tns/AX4debbrrpiN/brq7dfs9KvPOd78Tb3va2IzzN69evx5VXXolisXjKrGqcaJ/3vOctheysNcIwwtR8fbEQmIHOQloVB9GBiFbwsd2IZsZUso7ZM7JYNCr5XB1adsfmQkyyEFgE9BYt9Heaj3vxjRO55+h5YYj2vsVCYAMdzG8CNKl9hGBmFAFD7wMfRrEH9tA2GFby+X7tav7z8wsqXy3luiiVionnl7apNEIcnGYhsDgMb7jbgWsnf78Rv9VAfWYCgdeE7WaQ6eqD5egxj7Q8H7Nz1cVCYDa6Siwapcf9Rq/kwtys8jrzPit2dMB1U9CBWiPED37WwsHJUBUCe8a5DjYO2Frcb/TkMq9ZFQIzDBSLBWQyGWizppYbS4XAOgspFLPJ1x5YCh8vN7FQayq3ZCmXQkfe1Ua28NADCA7tUosYi4BZG3bCeJxrD5zYmhqhXqup+jztQmCFYjHxughtJsoR9k8xygHoLACb+ww4p9nLvDyic83C4Ux6SJP+fCE5pfm5z30u7rzzSNfbK1/5SpW3zEJeVGI5SX33u99VrabI/fffr1pMXXrppeo1v37wgx9UyjfbTRFOcFR8d+7cuepnp1IpdRwNP+9UK7iPx+88nQz16bG5fSQO3A07AR4aMtIXH0lwvHtuy2B86IjTPwLw0BBe00KhAB3pdLghSml5v/HfMjk9rxtly2X1UKhWvG6aKHtHU3KAX3qWnvcb6etLviDkagz16rqmAgM9Lgag57OKTWfHh4b3HFMJS0elEuoCq2XzSJq1vA8WhMSVZm4+zz77yAkwl8upnszt86961auUR7irq0spwm984xuVoszK2YSeYSrHL3vZy/Cxj31M5TG/613vUsXFVlKKBUEQBEEQBEEQBOFk0CMObRU+8YlPqBAXeppZuIuVsf/yL/9y6d8ZsvaNb3wDr3vd65QyTaX7Fa94Bd73vvclKrcgCIIgCIIgCE8ApFGzoJvS/IMf/OCI18wN+cxnPqOO1diwYQOuPU3FIARBEARBEARBEIQnF2K2EARBEARBEARBEIS14GkWBEEQBEEQBEHQBYN96c1kK8kn/fmCKM3CMWD7hPF5YLoStyQa6gQKGX0e2nLNR7kWqJYYHTkLubQe7X/IvjEf9+/z2XEK20dsbFynz6MWzk8hPLyL/U9g9g7D7FuftEhrgrAyD3/XnYhaTVjdA7A2nQlDkxyjqFGDv/du9dUsdZ+WdixPBPwgwoFpoNaMkHENrO9BIu1YVpPt1vs9TM0FyGVMXLTDUV91IIwi7B4DZipAyga2rmOrMz2um+5r6tQCMFuJwMdzoMNAXqPrNjEPteZTonWdQLemhbR1o9KIMFkOEYRAZ85AV97QolUXqTYjHJqJ55OOnIF1HdBGNkFYa+izkxe0Y/80cO9BqIWAyt/oLHDR5kgLxXm24mP/ZBNBQMmAmQUTmwdSWijOuw75+Ofv19VCakTAbQ96+OXLMti2PvnHLZybhPfT6xHVq+q1ceB+2Oc8E9bQ1qRF05qwtoDmD76mrh93lL5pwanMwT33GUmLhqjVQOvGbyKciXvUB4aBcH4aznnPks3RcRS/O/ezp3o8vxmIlIHwwk00EhqJK1fX3tDAzx70EIURIhh46KCPl16VRdpNfkxv3wPccyDu9c6LR8PDc8+NDQ/C6vBee2A0hB/Er8fnIpy7wdRiTT08C9y6G2j68et9U8DFW4HeYtKS6Q3X+bsPBGi0IrU2jM8BWwYsZRBJGhoDf7orQpWtt8HnNELDM7A5oXaYgrDW0cNsLWgHN2274z04ChmgkKbFEjg0Cy2YmPMQhpHaQPLw/BBTZQ86cPM9LdQaIfo7DfR1Gag3I9x0Tws6EOy/TynMRqETZrELCHwEu4/sly48kmDf/cpDb5S6YXb0wrAd+A/ephTWpAlG9yCcnQDyHTCK3YCbRnDwAUS1haRF05r5GjBRBlJuPMelXWBaeQGTlgyYmg9x9x4P2TTnEAvdJQMHJwKlOCcNlYMHRwHbAjpyQDEbX7P9k0lLpv+aum8qZIAP8mkglwIaHo3RseE3aXaNAV4AFDPx0fSAPRNJS6U/Y3OheiayKY6poQxJB6dDNd46GEK4b+P9ls/QwwzsnYgQKGuXcHIYmhxCkojSLKwIp1R6mNsRnm2HVdtCnjSc9OkMoictPlS0sRZwAbWstlyG2lw2PU0WqcBbkkth2YCvh0KvNcGiQWb5deMNFySvxCgZuEFrh4qbbdn0MCLpioqgiYB2NDZTUPia55PG9+MhdKxlsgFoaTCP+O3rpunaoDMrram6KDAcv3hNffiQMT0+NN7H1yseUF5DHeYQQjmMZfcaZeOzq8ktJ5wm2IFo48aNqiPRJZdcgptuuumY7//KV76CHTt2qPefc845x+xQ9NrXvlbd+5/85CePOP/BD34QT3/605HNZtHR0fGIn7v99tvxG7/xG1i/fj0ymQzOPPNMfOpTn3pER6WH9/gPH2NjY0gKUZqFFTENQ4VleX5sDa81AdsEuvPQglLOQhgZaPkRWh5XKAOFbPKh2WTrelstSvOVUB1cuLYO6yGb2T2odm30QkaNKuB7MPpGkhZLe5j7bTgpRAuziOoVRLUKzJ51QDqXtGgwu9fBSGWAyqzKaUZ9AWaxG0aulLRoWsPoGXqHOLfRq1ZtAJlU7DlNmu6Sid5OC7MLESq1EFPzEfIZA+v7kp9HeM268kC9CdRbwEI9zmvuk9vtmHCz15M3lNGB9xuvHRXojpwe27D+jliZolx8JqhnyZgen1LOVEopjeU0jnN8dclp5nNqWQ/PcS0f6MzHeznh0RUCS/o4Wf7pn/4Jb3vb2/Dud78bt956K8477zxcddVVmJhYOYzkhhtuUMrsq171KvzsZz/Di170InXcddddj3jv17/+ddx4440YHBx8xL+1Wi285CUvwete97oVP+eWW25BX18f/vEf/xF33303/uRP/gTvfOc78elPf/oR773//vsxOjq6dPDnkkIeHWFVzhwChrtj62TKAXYMsXAJtGCg00VPyVbKvWWZWNfpoLuQfM4wufQsF88414XrGHBtA08/x8XTz0lBB8z122Ftv0iF8NIjaW44E/b2pyQtlvZY/evhXnwFTCqihglr5Ay4l1ylxcbILPXAufC5MApdSh6zfwTORVfAoDdcWJWUY+C8DSwiGCsIpSzUax3ycjl3/I/L0tg8ZKkx7esw8aJnZZQinTScc5++AxjuidcGhrZfcgbQW0r+uunOlgEDg52GUpYdG9jSb2izpp6xjvLEEQSMcNg+CMl9PQH6igY29VlwbENdO+Yyb+rTY2vdWzRw5pCBtBN7mwc7gbOG9VDohdPDX/zFX+DVr341XvnKV2Lnzp347Gc/q7y/X/jCF1Z8P729z3/+8/H2t79deX/f//7348ILL3yEMnvo0CG88Y1vxJe+9CU4jvOI3/Pe974Xb33rW5WneiV++7d/W33WZZddhs2bN+O3fuu3lIxf+9rXHvFeKskDAwNLh5lgkVPZVQmrQoXvvA2PDD/SARbqWd+TwnB3HGeklWyWgSsuSuPyC2NFOemiQstR4eJbzoO1+VwgCmGYyW/C1wr2xp2wNpwJhIF2Cqm1biPMAfWwwqBrQTghSlkDl55hHJHuoQu9HRZe8fM5VfWWm3GdZGOl7MvPOTJNRjg+thUrMTquqUwjOncDcPZiMwUpvn9icAyHuw0MdbGUYGxU0on13QaGu+IoAp32IsLjD7299OjSg9uGCucVV1yBH//4xyv+DM/TM70ceqb/5V/+Zel1GIZ42ctephTrs84665TJOz8/j66urkecP//889FsNnH22WfjPe95D57xjOQKsOq18xO0xNR4otVp03E0lu7XzRDl6lFdN80U5iNlkzF9oj2rVLR0RefrpjM6r6miLD86VL4l9JVN42lkbdBO9E9aBrZbLZePOJ1KpdRxNFNTUwiCAP39/Uec5+v77rtvxY9gvvBK71+eR/zRj34Utm3jTW96E04VDAtnKPk3v/nNpXPr1q1TnvGLLrpIKc1/8zd/g2c/+9n4yU9+orzfSaDn7k8QBEEQBEEQBEFYgsWzlsN8ZXpgTwe33HKLCqtmfvSpcloxX/qXfumX1N9x5ZVXLp3fvn27OtqwsNiuXbvwiU98Av/wD/+AJBClWRAEQRAEQRAEQXMOHDiAYvHhBuoreZlJT08PLMvC+Phi/9hF+Jq5wSvB88d6/49+9CNVRGxk5OECsvRm//7v/76qoL13796T+lvuuecePPe5z8VrXvMavOtd7zru+y+++GL813/9F5JCAnEEQRAEQRAEQRBWYnkvtiQPQCnMy4/VlGbXdfGUpzwF3/3ud4/IR+brSy+9dMWf4fnl7yff/va3l97/spe9DHfccQduu+22pYPVs5nffN11153UJWXV7MsvvxyveMUrVIuqE4Gfx7DtpBBPsyAIgiAIgiAIwhMIFvWiUsq8YHpp6Q2uVquqUjV5+ctfjqGhIXz4wx9Wr9/85jeritYf//jHcfXVV+Oaa67BT3/6U3z+859X/97d3a2O5bB6Nj3Ry0Op9+/fj5mZGfWVnmgqu2Tr1q3I5/MqJPs5z3mOKjJGGds50/SM9/b2qu8p66ZNm1SxsUajoXKav/e97+H6669HUojSLByTg1MRRqcjVVlz6yB7IetTTaLVbKDZqKsezelsFo7jQhdmK8Chmfj7wa64X6IuzFcj7JtUhZaVbH0d+owp+wwHUwcY76N6DZudRxakSJIgCLFQbajKtynXRjbjalOIjv1B7zsQqt653UVDPau6yMbrVanV4fsBHMdGPpvWRrYoDBHMHEbUrMFwM7C6B7WpKB9FEaYWOKaRamfTVzRVZX5dZJsoxz2a2Z5oXWfcbUEXGvU6mq2WqmTM9iqWrc9WZ6EeolwLldOoK28i7eoT8FdrBqjUfNWDrZi1kXb1eBZIWCsjnBnlzQezox9moRO64Ps+qrW6ko1ev3RajxaTxPNDzFZ8hFGEbMpS4yo8efi1X/s1TE5O4s/+7M+UYspK1N/61reWin1RqV3ewol5w1/+8pdVqPQf//EfY9u2bapyNitXnwz8vL//+79fen3BBReor9///vdVMa+vfvWrSi72aebRZsOGDUsh3qz+zbBvtrfiPH7uuefiO9/5jvJOJ4URcfV7ksNKdKVSSZU7X54n8FjwPA/XXnstXvCCF6zYw2wtcP/BED+6M0TT5y4J6CwYuPpiE6Vc8pujRr2G2alJhGGgXnNT1N07AMdNXnGeLAM/ugdKgeHmI+MCzzwT6Cs9vp97IvfcbCXCd24HFmqxbOy//XM7gZHe5Mc0alTh3f1fiKpl1brDsB3YW86H1b9RC4X58MQcmi1PvabS192RR0cxm7RoaHkRvvGTAAenQvWcsj3RBdtMPH2nnfj9xuVlfGoONfUwxBTyGfR0FhNXnCmbt/dOBNOH1WaXWozV2Q9n83kwDDNx2fZNBjg8E89vfB46ciZ2DNlaVKzePR5i1zgQ8LJRtixw4SZDKfePFye6plYqFczNzqprSPjent5eVe01aWYWAuwe9+CH8aCmXQPbhxxkNFCcK3UfBybq8IJIjSnHcqQ/qxStpAkXZuHddyPQ5MJF4dJwtl+klOfHkxO55/ieickppTgTKiBdXZ3IZbNaKMy7x+qot+K1gbrRUHcK3UX3CbHPPp1yj17zaRSzmWRlqdWx7tffsOau4ROJ5GdqQUu44fjpAxG8AOjMAx15LvgR7j2gh41lYX5O5WbYVKxsBwEtvQtHluFPivsOxgpzKQuUMkC9Bdx7ENrIVq5xAx5vdKkD3r4HWhCM70VUnQeyeRjZguqH7O+/Z2nzmyT0MFNhdmwLrhNvvmfLNXUPJs3usUgpzNTfu4oGqBvcuSfCQi3561ZvtJTCbFmm8jJzQ1mpNtDy4g1mkvBeC2bGANuBkckDjotgbkJt0JOm4QHjc6HyLGdSJlKOgfkqPUbJj6nnR9g7FafXFdJA1gXmasDYvB7rVnk+FoRKDhVlKjW12qKylTCHpn0EIZBxYmNqoxVhYi42jCTN5HwTPqNoHAOuY6DlR5ieb0EHgtGHVDQIMsX48JvwDz4AHahUqvA9X91rPBhZMz+vx15kpuKj3gyRsg0V0cCldHy2pbzOgiCcPMmbXgUt4ZxKDxb1A9V/kDUIEKHZ0mOypYc5lqvt2TCWvM46bHjp7WuLxojKhh57DzQ9gI6qtmwMu+c5LfA9RAbli215EfshBz5jaBPvKd3eZLTvN/ZZ5QadG6Sk+5ryOW17mAmfWW7GOa6FZEVTRoXoqOvm+6G6bkkTte8tczGU0rSBsAkEyT8QQRCBl4ihz4TPLP1/gQbXjYZUPg6cO0h7LvE1mH75TPJo32/trzoYtygXPczx2tBetyL4dNdrAOWgVA9fO8qW/HVTeC0V/dGWLeJ64OmxqKp7a9lehHMc57fl92Fysh25bjFKhacosiZZKGsGwzTUkbQMQrKIp1lYEU78g92GUvaYU1epx8pBf6ceD20qnUEUharAQMDNLyK4qTR0oL8Uhy3Sw8yD3/d3QAv6KIfB3LVYNjr81nVBC4xCJwzDUh6FyGsCXlPlNeuQY8ocZuZHej7vt1BtJulxpgc1aZiTnnLpiYyVZYbed+QMlHJJS8bqnQ5sy1T5zLxunk+PzMPe+iQxM3kYblqFfEa+BzSq6rVBT1bCMGw37RgqV53hsjTEUUnNpZOff9MukEvFxsGWH88jfAw0yFRQygErxsbrQhCHzC6e00G2QsZQxgV66+nJVd76TPJzCMmnbaVQcW5jWC/JpZN/TolR7IkNIq2GOhD6MEpxsaCkcVPxvRXPcYFSotMpPepdMEqF+7aWzzUrNtDwnAbLliCsSeTREVblmWeb2DQQWyY5yT5lm4Htw8kvBKTY0YlMtl1dy0C+UEKukPxml+xcD2xd1gJvywBw1sMt7RJlxxBw9kjsHaINelM/8JQt0AKzdwTWyM7Y40crfdc62FsuhA5k0y66O/OwTFNdN77u6y5osTEa6DJx2bmWUqiC0FCGrec9xX5c80tPFCrHPV1FpSgrw5bjoK+7pIWxgQqys+lcFZptRCGMdA7OxrNhppPX/mzLwLZBC/mUqZzhrMe0pd9CPp38daPx6Oz1BjpzjMCIIxu2Dxrozid/v/F57OzqQjoT5x4apqnyETOLr5NmQ6+jin/F0RcsoGahp5S8UZD0dabQmWfebuw17S446Cklb2wg1tBWWOu2xAn0RgSzbwT2yJnQgXwuh2KxoJ4Lks1k0Nmph5W8lLUx0JlSHuYIEQpZC+t79CnEKAhrDT3MiIKWcBP+gqeaypPAPa9O1VFN00JnT6+y6nIBWF79L2l4rS45Azh/U/zaVSHu0CaC4KnbgPM2RsoDnnaWhwomC+XgRsga3KrymeGktJKtVMiikMsgjEKlPOsiG9mx3lQVsxmSzVxJjrMu5DJpZNMpBKF+180qdME86xlxqKftKiVLF6ggn7Mx9kzapl5jmk8buHhL7GnmfKdDcbI2zCtlyxId1wYasrYNOmpM+RjQOKILHMPh3vRiCoChlWyMNnK2nIdow5mLuQF6eHKXDDUdJZSKBeUN5/2mk2x9HS66i44K1eaY6iLb2kNZbDSQQUgSUZqFY8IJNqtP94RHyMaebrrCytS6wkIvusKq2YCeF4+Kiwk97zluiNp5prqhDCKaPquqUjbDtDWE3isa3XQdU13nOJ3XBsqmQXbCMZ5TndcGPTzfK6GTcWYlg4hOhi1BWKvo+5QLgiAIgiAIgiAIQsJoau8UBEEQBEEQBEFIGHrqk/bWJ/35gniaBUEQBEEQBEEQBGE1RGkWBEEQBEEQBEEQhFWQ8GzhmFQaEcr1UBWkYUsRHdrYtAmadfi1BVWG1MmVYDr6FAnxpiZRvftO1WYnu/McuL190AX2k6w36qrnVCqVUr10heMTBT6CuQn11cwWYOU7oQtRGKJRqyAMA9hOCm46o02VVFaUna0EaAUR0o6JUtbUSragPK16vxpuClaxRyvZOL8FrQZM21FznE7VveuNJlqerwog5TIprQohNZstNFstmIaJTCatRYuzNvW9e1G5914Yto3SU58Kp0OP9kSktvcgZn/8M3Wfdf3cRUgP9ict0pog9FoIFmY4EcPKFWGm2+0wk4dzm7//QcBvwexZB6tnMGmR1iTG4n9JyyAkiyjNwqrMVEI8OBrAY28iAGMpA2cO20hpUHmZm8na2B6Enqdet1JpZAe3wNKgCm7jwH6Mfu5/ozU1oaY4u7sXg695A9IbFntQJYjn+5icnILn+eo1N5M93V1Ip5O/bjoTBR5au29HUJmNe0hbNpzh7bC7h7RQmOcmx9CsV5WRhtWg8x3dyJU6tVD89o63MF0JlJGG+mh/h42hbidx5ZSytUZ3wZ8+jIh9mlk5uHMA7tAZictGGjPjaMyOA2EYGwbzJeT6N2ihOJcXapiZK6trSCrpFPp7OrRQnKvVOqZn51TLKZJyXfT1dmlRTbt8223Y93/+D7y5efUsTP7Ht7DlnX8Et7c3adFQvv0+3P37H0RzdFJ1tsmMDOHs//1nyG3dkLRoWhO2GmjsuwdhoxKrNbaDFNeGggbzb6OGxg++jmDq0FJv+tTFz4O9UY8e14Kw1kh+hRO0hJuhfZMB/CBSfV/TLlBpRhifjzciSdOYHlUKs+mm1EGvc3N2Ajowe/030Joch9s/CKd/EN7kBGa+9Q3owMLCAryWB9u21BEEIebmy0mLpT3BzJhSmLnpMNI5pWR5o7sQBUHSoqFRqyqF2bRs5WUmlfkZBH5sGEkSRqnMVALYpoG0a6qIlYn5AI1WrGwlSVivwJ8ZpeUIZjoHWA782XGE1fmkRUPgNdGco/JiwExl1Ebcq8zDqyX/rFIZnSsv0Aai5hAqo41GE9VaQ4t1i/NZFEZwbFsd9DhXqjXowOg118CfLyO9fhipdetQ37MHk9ddBx3Y97kvK4U5u2UE2U3rUd97EAe++NWkxdIeziFUmA0+p6kMH154E3uhA/7uuxBMHoKR74BR7EbktdC67UeIwuTXrTUHrVxmwocGxtwnO6I0CyvCDRGdkTTO0+vCzS5PtvzkN7sk8j3lcaFsyitkGOqcDvizszAcN5bPNJVS78/OQAeoJNOL0L5uPAINFD/difxW7MU1rfi6WQ4vptogJY3aAEUP9wmljPSGM1Q7aWh0CyP2CY1fcz4Jo2gpeiXp6AHlxbUWA64sWxlD1PmEoTFGeb/NRdnYSzqKVGpA0gRhiDCMVN9XtTYsVnTleR2UZir1lGlpbaCiH+ghmzczAyuXjWWzbfVg+HNz0IHmxAysbDpetywLRspFc1KPdUtnwsV9ByN81P1mWko5bUdhJEnENCyu95Yd33NuGlGrCXhczwRBOFlEaRZWhEpyPmOAzipufKkscz3Ip/SwdFmZnFIWuIkMuZHkxjyTgw6kN29Vi2ZQqSCoVlS+U3rLNugAQxUJFWVuLrkxb58TVsfMFJTiwvww3nOR14SRygIa5NHbLg00BgLfU2PKr/Q6W3byueoZ14RtGWj6EYIwUl8dK/Y6J42ZyirjVtRcHNNWXXl0zVTy8wjrM5iWo+4zNc/RaGNasNxM0qLBtiw4tgU/oPIcqhoJ3JC7TvLZXpTDcZxFxT6EHwQqD9DRoG4DZcudcQb8Ba4LVXjz80q2zMbk03ZI8bzt8Kt1ePML8GbnEbU8FM8+I2mxtEftO5TRvhXPI4s1L3RI8TA7e2EYFqJaRSnLUaMKs6Mb0CCNTRDWIsnvXARt2dxnoZg1oIz0kYGBThN9HXrcMumeIdi5ksrnpAfGLXYj1aFHsa2u578QhQsvRug1EbaaKFzwFHS/4BehA/l8DvlcrBQwhJG5zB0aFaLRFbPUC7t/UxweFXhqU+Ru2Km8C0njprModMYFrKhg2Y6LUk+/Fvml2ZSJkV5HKc5+CKRsAxv7XLgaFBQ03TTcoW0qEgTc6NouUoPbYKazSYumjB7Z/hElG73OjB7I9g7C1sAwyPusp7tDKcn0OPN1RzGPTDqlhWzdXSVlCGznNBcKOeSyyRsbyND//J8onHsOglodkR+g+3lXoOeqK6EDm97wcvRc/jQlW9hsoe/qZ2P9q16StFjaY3eug9M5oPYhCANYuQ64A5uhA9bGHbB3XqS8zfCaqghY6pKrtFDo1xyLEY2JH0KiJG8aFrQl7Ro4e8RGoxWHV7p2vCnRAVaTzQ1tVYppHOrmaiOblc1i3atfrypoE6enV4viPYSKVFdXJ0pBUYWP2XYctiUcG+VJW7cFUc+wCt813EwcBq0J2WIH0rk8wiBQz4YOCnOb7oKNUtaC50dKWbYsfe43u9ijNrkqcoBziAbe+TZONo/iyHYVqcLwSo6rLqRcB4P9PcqTy1Boep91gZ7mAcrm+8qoxbxrXXB7erD1Xe9Cc3wcpuvC6dGnWrvTWVKFv+oHx9R6lR4e0EY2neG1cge3wuldr6pnq7oXGhhTCeVIXXAZnO0XqpBsI19Sc4kgCI8OeXqE44ZpZ5N3IKwIF3QdqmWvupD26dmuQ1UJZj6dcNIYTkodOkLvJA8doaeZh46ofD9NrxsNMxaLC2kIlWW3nXOtGe0wbR1hLnN6KPmq+yvBXObsBj1l05l2vrCuMDJKEITHjp4rniAIgiAIgiAIQtLQ5pt05IWeducnFXrEkAiCIAiCIAiCIAiChojSLAiCIAiCIAiCIAirIOHZgiAIgiAIgiAIK8HibkkXeEv68wVRmoVjU65FmCrH1bMHuwBHg1YxbVhV1q9XVZ6HnS1oVQSJPUwr9UB9n0uzr6k+k50XRJirRAgjoJRlz1x9xlT13V6YUa2TzGwRZjr5FjttwihCte6rfsNp11KHTrKVayG8gL2RDeTT+txvrNIeVmYXe1tnYGZL2lTlpWyNpgfPD2HbJjIpRxvZSK3po+WFqohaLq1XpftKI8JCHXAsoCsfFwbThbBRQ1hfiHtbFzq1qnTv+T6azZYaS7bp0qnSPXtu15ueSp7MpNkqTh/ZOH+E81P8DmaxW3Uw0AV2LfDrC3FHinQWlkbFIv0gwqFpoOUDvUWgI6/PcyoIaw19tAxBO8ZmI/zoHm7c4tf9HcCzz4mQdpKfdP1GDdXRvQi4EWebp3QW+XWbYDpu0qKh6QXYN15HsxX3CU25Jkb6MlooWU0vwt0HAizUI/WaCvOZwxaKmeTHNPI9tPbegaAyp3peskq1u+EsWIWupEVT/WgPTtawUPdUO04qMYPdWRRzjhYK8+4xDzMLoZKN3X/YG7mvlPz9xk1k6/BDCKYPI4rYb9iG3bcBbv8GLWSbma9hrlxT15CdAkqFDLo7cloopzPlJsZnG8pIQ3E6ci4GezJayDY+F+H+0RCezw4LQE8ROGvY1EJx9svTaB68H5HfggEDZr4D6Q1naVEhvdFsYXJ6VimnvFKu66K/txOWBi27mi0fo1NltGh5U+uWjcHeIhwNWnZFjRq8B36CqDoPrlxmOg/7jKfCzJWSFg2h76Eyti824Kt2mC5yAyNwNOip3vIjfPf2CIen4zbSmRTwrLOA9b3JP6drsxCYBjIIiaKPGVHQCm4of/pQrDCXskA+DYzPAg8cghbUp0aVwmyyJ6KbUkp0Y3YCOjAx10KjFcJ1DHXwe57TgYPTIcr1CBkHyLpAoxVhz3i8SUoaf/oQgoVZNZ5GOqs8C96hB9S9mDTzVU8pzPS8pBxTKTJjM3WlTCfNXCXE9AK9kTSCUIkGDkzRe5q8bGF1DsHMYaXJc6PLRd+f3K88gUlD5WCOrlIDcB3VhB7zC3U06JJJGHq+J+YaSkHg/WabJuaqLVTqvhaeq4fGQwQBkEsBrg1MloHxeehhpBndFSvMqSzgphBW5uDPjEEHZufKSmGmIsr+0c1WC+WFWNlKmun5qnomXNtSB5VoGpV0wD/8gBpHpPMwMnmE9QqCA/dBBxrz0/DqVaUsm04Kgd9CbeowdID7NXqZuX/ryAH1FnDj/YwyS35tEIS1iCjNwopw411tAKl4L6k25KTtdU6a0G+qkDvVH1HlmhgIfT0UU4ZT0uESy2ao71t+7HXWwdPMh54eIdWv2QQajMbTACrJZGlcbSc+FyV/7bwglsFaum6x4sxDB28Cofdb9ae16BmPw/CTJvJaKtQe1qJH3nLV68hvapFCEYYhrMXwWI4tN5PB4lgnLxtgL95vFntcRw/fh0lCR6QfxMry8rWhfR8mSuiriBXOHWoOMS1E/E+D+40KPRXm9tzbjhjgWOuAp9ath2Xjfx4HWgPoaVbrgmmq9Z69rqOmHsYG7juUE1LJZsA0LeV91sHY296v8Rnl7UZjeaNFo1zSkgnC2kSUZmFFuIGkZZIKFdfNxTQn5XXWASuVjTffPOjyYHilJjlO6ZSpjA5tpYrfZ1w9HrVc2kC46C2ibF4YW6F1wKRniLoBNxxKsWrBSOe0yEdMqZx0Y1GZiZTywjx1KqpJwxB7boiotFA2Okody4CrQf0B5jAzLJbGjygMEXkN9VqHfER606gwqzGlQhOEKr9Uh3BU3ltUlL1F2eh5NkwDKVpEEobKMo2pTZ9zXPxVbch1qI1g2modiLz2HBIvXMrrnDBUqFzHUc8oj4BWESxGOWgAw7F5r7Vlo7GB53TAyBVZ8CJeGwIfoGFEg9BsYrlpFRESBn48x4UBbEbAaZBGwf0apaCizH1crQUUGIChx7CuKZSxRoNDSBYZAWFVLjkD6CrEIT20TG7uB7YNQgsyPetgZ3KIfC5UPpxcCenOPuhAf0cKhYy1pDTnMxb6OvQoDDLUZaK3YICObypXxayBzf3Jb8SJ1T0Eq3Mddx/KQ2lmCnCHt0MHmLvcVXBVXhgVGSovg9165JeWsiYGO+Mx5JhSWd7Yb2uh0FvZIpyBzfF1UgqzA3dom0qrSBrHsdDblVeeP3qX6WXr6chpoSgwDYD3F79SYebOt7eUQjZlaWFQ3T5kqlQAKsx8JtZ3G6rIkBaK6fC2uICg11Jzid05ALujHzrQ1VlEynVipTSMkMumUcgnr9CTno6sKv5F2ag45zIuuop6yGYPnQGjYwBgNBnrmJR6Ya/fCR1Il3qQypcQRSHCwIOVziDbq8dGaesgcMYQ1HpfbQKdeeCZOx+OchAE4eRIfncgaAurLD7/wghzVYYJxhOuLpMtq1MWhrYiaNWVJ8GiR0sT2bjR3TCQVbnMJO2aakOuA1SkzlxvqdB7bnZz6XgTrAMMb3NHdiJqjMTVs+llbof1JgzvrYGuNDoLrtpQMs/U0qSyLGUb7nHQXbSUN4GF+nSqcu/0DMMqdKsQWXqYmfenC4VcGumUA3+xerYOXuY2hayDLSkLzcXq2Tp4mdt05gxctNlU4Z90lLI+gi7zr5UpILP1AoSNahzSy3lEE9kcx8ZAXzdanr/oedanIjpzrId6S0o2QuORLrIZtgtn+yWIavMqTcHIFmFoUDytvW7lBjYg3WqovBgrldYiOopwbX/mTuCskTitgtGDKQ0KuQrCWkWUZuGY0GvVp0cU1IqLla1RS6LlUEnWwSu0mmyF5KNjV0Tl0mUK0FU2HSqgr4YuKQArYaYyAA8NoaKsk7J8tAFOp7Y/R68NGjjlV4QpAJYm4btHwxSAdCr5Lg8rwagLGpF0ROUM5zuhI6rOhabzG2VjxKDwGKEBKWkjUtKfL0h4tiAIgiAIgiAIgiCshijNgiAIgiAIgiAIgrAKmgZXCYIgCIIgCIIgJIyEZwviaRYEQRAEQRAEQRCE1RFPs3BMqo0Ik/ORqp490GVo0camjer9WplT1jej0KWKv+gC+yDP11ShT9UrkX1zdYFtsJRsEVRBMB36+bZRvVXnp4DAj8dUo+IqURSh0fRU9WzXtbUqHkXZyvW45VQuBWRThl7XrRWqVl2uzVZF+lw3EtYriFp1GE4KZlaDvknL8Dwfnu+r4lFsVaRLNWPi+z5anrcom6uVbFGzjqg6D1gWjEK3KiKlC+w/H9YWWNkKZq6oTaVlwnZTzaanHFq83zi2usA2hOHUYTWfWN0Deq0NYYCwVlZ9mlm93XD0KfTG6+U3qko2FiszbT0LvemOKlKa8ByX9OcLojQLx2B8NsL1twSYq0ZgV6KRPgNXPcWCq0HLAm46/AduQlQrq5ZTRrFLtaTgxjdp6q0Id+yPUK7Fr4sZ4NwNrG6c/HXz/Ah3HQwxX42UQp9LGdg5bCKfTl62yPfg330DwpnDqnUHMnk4Zz0DZqlHi43HxPQCKrWGMjawonFfdwHZTEoL2R4YjXB4ln3BaQQBtg+a6C8Zely3uSam51vK2GBZBvo70+gq6rGp9Cf3wxvdpe49Gt3s/k1wBjZBByrVOqZny0qRURXv81l0dRS02DjV63VMz8yq/tYUJ5fNoqurUwvZwvIUvAd+CjRrsWLKPs1nXKSFUTWsL6C57x6EzZq6Vma+E6mRnTA0UGRooJmYnkOr5anXNIT09XSoVlQ6GLaaP/w3pTSrllOdvUj/3C/CLCZfTTsKfDT334OABvwoUsp8auRMpTwnDRXl6sQBtKplJRsV5lz/ejiZfNKiCcKaRB8zoqAdP7wzwGwlUr39silg71iEO/dS1UqeYN9dCOlJSOUAN41obgr+oQehA3sm4t7WaTc+6NXdNa7Hdds/HaoxZVeRjAtUGhF2jcf9pJMmPPwQwqkDgJMGMgVE9QX499+sFK+kWag21MGWLI5twg9CTM5UtJBtagE4NBMbttgvl21WHxgN0fKTl63eDJTCTFzHVLaQ8dkGWl7y9xz7+CqFOQpVL1/ij++J55WE8YMAM3NlhFGoIhqoiy5Uaqg34muZJGEYYmZ2TinMVKgMw0S1WkOtVtfDq7br9lhhTucBO4Vg5jDCiX3QgRbnuGY19pJStoVp+NOHoAOz8wtotjw1ppZlodFqYb5cgQ54d/0EwcQBIFsA8kWEM2No3f4j6IA3dQj+wgxgu0AqjbBRQ+vwLuhAc2EWrco8DNOG6aQQ+h5qk7G3XhCEk0eUZmH1EN5qpJQ+Kgr0LnOaLdf0mGypUNFzoHo30oNAjaGuxwJfbQJsrbp0WPE5Hai34loSlmko7xWdCLWmJmPKEDJ6EWwnHlcnpc4hDJIWDb5PJS+CRbkMQ3lM6QGk4pA0DS9CGMUeZhVW6cSKczN2GCVKyw/VXMK0DtXL1OZ1i9T5pGFINr1EvM+Uh5T3W+Aj9BpJi4bAD2LPvGkt3m8WQiqEQfLPAu/7MAhhWe1nwQTjVvzAT1o0IPDUuFIhpTKvPLgR55bFsJ8EoaKiPMwWw+y5btGDa6hzOkBPM9cE5QE3DTBmoOVrMKYqemAaMO14beB6b7sI56ehAyHvN9UbPDYgGbaNqFnTQjENvdjIZirZDHXtqDgznFw4SbjH1OEQEkWUZmFFqFSVcgbo2ODmreVFahEtZvV4aA16IgNfhR/xq9IaNAk5Yk4pdamlI4jP6QC9y1zLqbj8/+y9B5xdV3Xv/zvt9jK9azTqXS4yNjaY3g2BUB6BPOABgUDoNY2XUIJpoUMICSSQACGEB+RPCdVU44a7LVuyepvebi/nnP3//PaeO5ZkyUhC8tkj9pfP4c49M7536exz9tqr7LXUIlyf/a+M9tF2YaqsHNdmXUUANdjz53JTP5ShzMVQECgDmgZD1CQ8OkDUfmaOLY1lz1XGc9RwDzPnEu7xl1FAed0seT5qrFhSLiJlbQReON5vDiMyiahFg+M60nAJuMdf3m/BgpMr+meB973t2NJhpGSjA4SyRZ/+DBqksQTgc0xDOZdQcVmJVNSSKWM0noKgYU/ZpANEyHM64Hmu1AnSuA/V9p2Y6+qxUM11AqGvdAP1vd+Ane+EDtgxtbea46nuOR9WPKXFVgV7YW91KGUT8toxRVunffQGw1Ii+pWLQVsetcVBW8aSqcaVOjDSZ2HLSPSKgDjLN8NO54F6GWjUYLV1wR1cAx1Y0WPJlHY6HHiwENiqXj2u27JOG+0ZSxpWjDpzL/OqXj2mAXtgNeyuIYCRPmYSJLNw112ixeIjm07Ig4vJph/KPc3dHRktZOvKAoMdlvQbVRrKYF7bb2tR4C0Zd9CZVws3pmTTuOeeZqZqR42dSMPrXwULtspooHOkd4WaVyKGxnFHWw62ZaPpc8ELuac5ydSfiGFxqI72Nukw8qVsITLpFFKp6AszyWyGVRcCNERrJWk8Ox0DsHuWRy2aJMY5Lp6WhcqkbNlOuJ2D0IH2fFYW/+KY0kmTiMWQz+nhiPY2XwanZxnAAmqledgdfYhdcCV0wOsagJvtkIY86lXYiRRiA6ugA/FsO2KZPETIDJq6NJhT3QNa6C2DYSmihxvRoCW97Rae80gHk3NMr9SreradysLb9Egtq2ez4Ne2FcDcQtadTtWzaUhtHba1rJ7N1Dt3y5VaVs/mIoOFv3KZhHbVsynb2n6gr83Srnq2vG5tceRSnpbVs93uYdiZDi2rZ2fSSWnE6Fg9O5lMoq/Xk9WzGXmOaVQ92851Ibbl0QjLc2oLj0bVs+1kFonVF6nq2YzYp/Spns1Ic19Ph6yeTRJxfapn28kMEo97rqqezQrVXf366AbHQ3z5Ji2rZ/O+T/cOI26qZ58FOL9FPcdF/f0GPawMg7akExbSfXo+qHIvYnsvdITOBUYAdYTpsR16BBAeABeQuo4pjQIdIn0nk43OGW2vW9xBEnoYBydakOuyteNEhgwPHXFdVx46QoPK0cSoOh7LjcFhurGG0AGiQ0eAE0FD1Okfga56y8lEX8n7ZPOvqZZtMJwd9HAjGgwGg8FgMBgMBoPBoCF6uokNBoPBYDAYDAaDIWq4/STqLShRf7/BRJoNBoPBYDAYDAaDwWA4GSbSbDAYDAaDwWAwGAwnwkSaDcZoNvw2moFAscoCIUAuqYpK6AJ7DZfrqp5gOsG+7/rIxn6I4eyk/Nlu79amsjdha6LCUdWzNSkCLZF9Xxuqx6rDasYa9KU9WrbpgkCtIdCZs5HUpEJ1i9miQKUuZEuxVEIv2aoNIducsU943NNLNrbY8YNAtlDyNCtsxevG+ZfXLZvU67rxOZgvq37g+bReuoHzb1CvygJNdjyplWzsVV6qU18B2QS0k40tJikS2yayaKQuUCeEtepCb+ukNlXHCTsqTBe5JgE6svp0pGjpLc4jlI2dPXTpgGIwLEX0WiEYtKJYFbh5D1CqKSXa3w5csFxooUi5YLv3cIBSTUiruT1tYd2Ao4VCELUKar/8NsKJQ/K93T2I+JVPV1V6I6YZALfsASbm1ftcCrhkpXI66LAoKk0eQaNUgICA68WR6RmCG09osSi65tYm7tzDHqYCubSNqy73MNjlaLEouuGeALfsCuEHaiwff5GLkT49dt8cnA6xZ1wt2jxH9ZDubYv+OSWlcgUzswU5vrZtob0ti2wmDR04NC1w032q1zsdWxuWCWxapoeRNToj8Iu7BMo1gH6t9UPApWv1kC2olVE9sgdhQykuL9uBRP8ILCv656FcF7jzgFjUqeywsGlIdVuIGjrcfnUPMF1QnW1688AjNggtnFx0glSP7IZfpnCAk0wjObAKthd9pe+mL/DTO0LsmxDSEd2RsfDEi2y0ZaK/bqEQ2DfuY7oYQLBvs2dhdZ+HdCL6Z8FgWIqYJ8dwUu48ANnPl112uGg7NA3sV8HTyNk7EWC+KsBuLK4NTBUFDk2H0IHG7dciOLIXYMuTRBLB6D40b78WOrBnHBidVcZL3AVmS8DdB6EFtcIc6sV51b/U8eDXayhPHYEO7DgY4Nadvsy4yCQtzBRD/ODGpjSgo+bAhMBNO9W9n0kCxQpwza0+6s3oZStUBHaPCYShut/YR3rHaCidXlHD/sfSYBYhXNeWzoeZuSIaDdWnNkpqTWUwVxpAKsHYGnDXgfudXVFHI3+1XaBQVbLR+Lt7P7BvAlo4kGpj+xDWKrK1Ew3l5vyUPHRgxxEhdSqj89Rb4/N0KkELbtsLjM8q2WIucHhG3XM6UJ8+Ar+00Hfb9RBUiqhPKqd01Ny1X2DXESHnt1SczyifDz3WIlOFEJOFgCoVMQeo1kPsHW/K58RwmtDppsNhiBQzAoYTwshLsQbEPJWaTSXKaZYech2gHFx0MOpNL7214MXXgXB2gk1MYcXispc0XA/hzDh0gAs2XiuOJx0hNJ7nKtCCsFmXOeM2F0a2LRdIQbMBQYsrYpiWzbR29i33XEumytIg1OGemy0JBIEy5vksZFKMGimDNWpo9Pk0mD3pC0HCo7GqzkeN3/QRhiFcx5ERUqZn8z2N6agpVWk4A+m4mn+5GOcY01CNGt5bjDBTJs4hfOWzwVTtyAkDhI26NKzkHOJ6Mp1XRp0jhoaK1KmuGtNW++0ys6U0gA5U7k6gXJSRzyvP6UBQo+JSOkGmZduOOqcBnH+xEMX1HEvOcdQXOhimtYbSnZSLmTTUXXSmMuvHYDCcPsZoNpwQRg9aC1zO/a1JllFnHaAclImKiSlIVE8JDdLIiJXJc0UOEQbK4PObsDJt0IFUDOBQUiyOK9O1uTDXAbnv24K8ZhxXEfrSgNah+AUNUsrWaKrFUKXGtEW1RyxqaMhblkC9oWSr1tXCN63Bnmu5+LbUfUYagUrn5bWLGofGsm0hWHDKyBRtaTxHn3KfXDBIaTjzOeV+cD4G3Nusw9zLcWXaOGVj9gDRYYsHjSnOI0znVXOIuvFsN/oLR8dMkjo1UNdN3naC1zP655RkEswiUA4QyiYdcTqMKcePzmepF5RuoHNEh9RskklYcv3BDAzKxmeVTlUdtiq09laz/gtl4/jSsUqnjcFgOH3Mo2M4IZzwudeKCyRGdRld6MoAI93QgpFuRy42qg21eMsmLAx26nE7x7ZeAbu9B6IwC1GYgd3WhdgFj4AOrOoDOtKQhWgY9aDBvHEIWpDIdcBLpBH6TRl1tl0Pqc4+LRYfm0YcrB505D7/qYJAPGbhcRd50nMfNSv7LaxbZstnYabAmgPAFZscLYqBtaeBwQ4apswEUedGuiwtDHrPc5HLZqTh0mDUWQi5nznO9JqI4fXZulxlhTA7hIbpyl5goANaLMQvW8eoGjBXYsonMNILrOzTQ28leodhuS5EowbRbMBJ5+Hlu6ADa/qV4cxngc8ri20t64QWbB0B8im1vYMZDZ3cbz0MLYh39st9zHJMG1XYsQTi3Xoors0jFvrbLVmwj5F5bpG5fIMea5GunIN8ypbzB9dJfGaHu10tdOqSw9LkMESKKQRmOCldOQuPWCswU1ap0N05PQqWEHpyty53ZAqqqvSpUqN0wM51IPnEP0IwpjaE2X3LtCgCRhipunwdMFlQ0QQujJheqQOslJ3rX45GpSQjCl48CSemh3A0jp/5iBj2jan9uL3tNrrb9FgYcYvCE7e5WLeMUWZW9rakfDrAxdnafs4dloyaMtOBC3NdZGvLZZBMxOH7vowwJ+LcB6vHPLJu0EJnVshK93Re9rXr0yFgVb+FtgwwU1R71Qe79Km07GbakBregLBWlpFnN53TptIyC1ZuW6VS2Zn+3JFRqbM6wKr7T7hAYHxOZTX0tekTBaeRnFy2DkGlIMP0TioH24s+e4Ck4hauutTGoSmV9tzXbiGX0uO6cb22ZsDDfCWUsjEqnozpoRsMhqWIMZoNvzX1U4u0uxPAdOxEXg/ldDxWMg13xQboCFMrBzWIWJ0I7kOMZ3LQES5AGG3WERosK/o0fRYsSxoHOiIjk3H2wdJjAX4ix2WXno8DOrM06qElDh1uLMSoIamYJZ1HOkIDcEUvtISZR3ZOk7D8cXA/Mx1JuuqGjoyeestgWGoYo9lgMBgMBoPBYDAYToQO1auj/n6D2dNsMBgMBoPBYDAYDAbDyTCRZoPBYDAYDAaDwWA4EdzoH3VNiai/32AizQaDwWAwGAwGg8FgMJwME2k2PCjs7zdfEnBdC9mkKpyjC7JHc7MhvW8sEqKbbGwp0qpYraNs7MfJytm6VOQlsgdnvQIRBLKYmi5Vb1s0fNXrkj3MbU2qBR8tG1uLUDZdqty3aPoCdU1lY1siUa/AiidhadL7tUUYhggCH7btaNE/+mjYl7ZUBdihiwWkdIL9mUWlCIt6IZGGTnCOa/qhDBq5jq2dbmCfYcJe6paGukE2uU6kYGm0v5MisS2nqlCtKqPrhO8HsqWe5zpajanBsNQwRrPhpMyVBP7nxiYm5kJwnbthuYPHXOhq0VqEvXxLE4cQsK0ILHjpHNLdg7L6ctQ0A4HthwSmi+o9Kwez57UOPX3pBLnzADA2pxR9ewa4cLnQorWICEP4u29FOLqHb2Bl2uFtuBxWKqvFgu3QdIAjsyFFk9drTb+DdCL6+40cmBLYM6FantAw5f3GNmw6MDonsGs0hB8CMQdYP2jLyss6EEwchH/vDUCzBrhxuOsugdO3AjpQr9UwOz2FIAikgyaXb0c6G/2zQCbnBX5xl5C9adkXfMuIwAUrLC0W5GF5Hv6dv4Ios3eSDXtoLdw1F2shW9MPMDpVRq3hy5ar2XQMPR1pLRyXdGztGvNlG0cKx/ZYq/r00PciaMLfczvC2XG+g53rgrvqIi2cXJxzb94N7J9Ujmjq+yvWsfMItNBbs3NFlMoV+TN703d1tiPmmaX/6aNBerZp1Bw5eqz4DFpyza1NHJoIZR9OzhW37Q5w194AOlCZHoPPno2yoqGFenEWtfkp6MDeCYHxebWY5DExD2nQ6MC+CeDQtBpP9t6enAfuOQwtCMf2IDy0QykGN4ZwbgLNnTdBB2ZKNJpD6WhgwK9cC7FrTHnvdZBt15gymD3KVgfuPsSIePSylWoCO4+EaATqfmOv5nsOh6g3o5dNVEvw77kOoOPNjQONKvx7bpBGV9SEYSANZt9vwrZtGXGen5tBo17XwvH2y7uVU5BRZt53t+4GDmkw/dIw8Ldfh3B+AnA9aWCF++5WjjgNmJipoFLjmKppbr5Yx3yxBh04MBVgthRKnUU7eaogcHhaD30fHNmFcOqQCuE6LsKZMfgH74EO7BpVB4eUTkH2ub5Zj9sN5UoVhWIZgo4G20K90cT0zJyK2hsMhtPGGM2Gky6MxmZCJOMqqsZ+zWEoMDGnx2TrM53SdmA7rjzof/NrFejAfEUtOujM5UE9P6eHaItyxBdkcx1ghsF6DRDleYhQqDRZNwYrloAozkIEftSioVIXcqHBfpxML455FmoNpllCA8MUMorLbQAcT0aaaZy2tgdELVszAJLe/bIx/ZOGvQ73G+pVIJmVabx8RaMGUZqLWjT4TV+mZTuc32xbvtJwbjaiH9RyDShUVSSNPd8zSWU4tzJrIsVvQpRmYcUW5pB4CkKEch6JGs4ftbovI7eObcvUbGrTGj1KGkAHFw0rzm+ew6wBdU4HOKZ0kDOyzHGl4SyKM9CBmdJCxnhMPQ/UrVMFdS5qGk1fGsyu46h5xLbRbPpyLWcwGE4fYzQbTgiNPu5T4wKXyp5GNF3jaU32rtkLi0jKJg96UmVkIXqoNLmIpNLkES6kzOoA96lxKMMF2VrpvFrgxqXzQ+5HpHDNBiwvxsGOWjJp8Akox5GMZgXKGcLzUcOFGhe4lIlI2RacNlHDyDdloVFPmqHKvqDMkROLy8U3/AUL3m/INAIdUj5tudfVllsWCO85phfzvA5zCLMGGlI33H/f0WiIHI4njSruU6deCAOV0BiLPleW4+e6zBpY0FkLVhWNZx3gMxkcJRuzaLR4TkksuagX1Lj60jGiA3RWciT5qHJI6QNRdUyiloxzrbq3Fsc0DKXxrFs9jiVVPTvqwxApeszWBu2ggr9yqysXQowgzBaB3nYLW1c5eiiqjl44roewWZeHE0sgke+CDqzosWSBLUbTeDBav6Jbj8luRQ+QSyq5SnW1AF43AC1wBlbBynVCVIsAU++9GJyVF2qxF7E7ZyOfslBrsoiakIbgcJejxX6/7hzQlVURXEZ2xcI9mPCil4175nvz3C+p7jkuLIc66HyLWjLAynbCHlzDKjkq6tysw+5fBau9N2rR4LoeMtmcdAY2mw2EQYBEMiWPqGG2xcWrVSRyrgxZDGygE1jZF7VkDEbacv8yXBdg5kq1CKutG87gauhAV1sSjmOh0QzRbIZIxBy0ZTV4GAAMdTpyzqguZKkkYxYGOvTQ9w6fS2aCUDdwTGNJOEProANr+tU8V6gB81XlhL5gBFqQSacQj8XkXnoefD7a8lktdKrBsBTRxY9o0JBVAw6e+2gLhydDWT179YCNVEKPydZLZpAdWIFmtSQj4LF0TptIczZpYdvK+9MVOzP6VJdlpsDD16g914w2d2ZpROshG9OyvQseo/ausXp2vgt2rhM6wJTFDYMupkuhjKxlEhZyKT18jjTctw6rvfOsns3qrSxGowMscLRhyEZXjka9kAtxGvg6LNpk5G/dwxB29KnFeCIDu2eZFrKRbD6PWDwOv9mA7ThIptLayLZxmYX2tJrj6Hgb6dGj0CFhITcrmYGYm5T7mu2eYS2yB0g6GcNQbw7Vmi+dDplUTJtIczZpY+MyD/PlcKEQmC0dJDpgp3KIbbxisRCYle+BndRjkuM2hcdtAQ5Pq8ytnrwyonXAcWz09nSgUqmp7UUxD/G4DikhBsPSxBjNhgelt92Wh44wusxDR1IxCyk97L0HwD3qy7uhJdzH7AzoERU6HkaIevJ6RF5OZDj3t0NLaDgz2qxj5U8aoU7PMHSEsiWSSYCHhvR3WOjvgJbY+W6Ah4YkYszg0nPpRadWktWsNIT703WpbH88TMde3Q8tYYp2NhN9hsqSh+oraqelfir09w49rSGDwWAwGAwGg8FgMBg0QE93p8FgMBgMBoPBYDBEjQ6FuKL+foOJNBsMBoPBYDAYDAaDwXAyjNFsMBgMBoPBYDAYDAbDSTBGs+FBkT2ag1D299MN2XuwUYNoLvRZ1QxeNx6tfpw60QwEGv79vUJ1oumrtk46ysY+prpet1Bz2Vg9m71fdYPXq+mH2srGMeX101G2ui/kM6Gn3gq01Vuc4/xAz+tWa6hDRzieuupUPgfUqzrKxnuN86+Osi0Zou7P/Dukh3/605/GyMgIEokELrvsMtx4440P+vf/9V//hfXr18u/37JlC773ve+d9G9f9apXyaKVH/vYx445/973vhdXXHEFUqkU2traTvjfHjhwAFdddZX8m56eHrztbW+D7/vH/M3PfvYzXHzxxYjH41i9ejW+8IUvIErMnmbDSfGDAFMzBdTqDVm2L5dJoj2f0aLtCQ1l/65rEU4dlhOJPbAa7vqHwbKjr/zJBe7YbAXFSlO+zyY99HWkYGvQ05eGwc4jAodnVD9ftsPatAyIadAuhgr9jn3APYdUP9/uPPCIDUKLdl2UbWwuxMFpOpBUm5HVfa6sNqsDE/Mh9k8GsuUJe6uv6XOR1qQ93GRBYOdoiGagepiuH7DRltZDtlI1wMGpujSa2VZsqCuOXEoPtVisCtw3Gsje4KyOvrLXlv3CdaBSF7j3cIhyXcC22YfexkCHHrJx0TUzM4NmoyF1QzablYcOeovG8u4xH/MVIde/vW227Peug2w0qn52e4C9Y0q2tUMWrtzsyOdCh/l3ZraAUqUm3ycTcXR15GDz5tNAttGZBqaLPmiTZhIOlvXE4Wly3fZPAfsn6bBke0lg45Cqkm74/eA///M/8eY3vxn/+I//KA1mGrdPfvKTsWPHDmmoHs+vf/1rvOAFL8D73vc+PP3pT8dXvvIVPOtZz8Itt9yCzZs3H/O33/zmN3H99ddjYGDgAZ/TaDTwvOc9D5dffjk+//nPP+D3dGrSYO7r65PfOTo6ihe/+MXwPA9XX321/Ju9e/fKv6Fh/uUvfxk/+clP8Cd/8ifo7++X/4YoiH7GMWjL9GwB5QqjuJacfOeK5UWlFTX+jpsRju5WnjcRItx3F8L990AHpuZrmCvyutEsFZgrNTA1X4UOHJwC9k0CrfjL6BykEa0De8eB2/fSWcM2RUrW6+6FFsyVBfZNBNJjb9kC82UaNFwkRX/titUQe8YDNBZkK1VppPpaRABpVN1zOEStoZRNqQrcfSiU0dOooaG8f6KGWiOUbbEaTYEDk3XUm9FHJ3mf7TgSoFhTBgyvFw3oUk1o4XjjmM5VhHxO+bzuGg8xW45eNj6PszMzqNWUnhJhiPn5eVSresy/+yZ8TJVCOaZ8PA9PBxifj/5+IzfcG+CeAyG1vbyOt+8JccsuPWSbL5RRKFUomdSppUoVs/Ml6ACN5fE5X44nx3Wu7OPwlB7ZbxMFYNeYgM/W25bqq779sIk4/z7xkY98BK94xSvw0pe+FBs3bpTGMyO7//Iv/3LCv//4xz+OpzzlKTLqu2HDBrznPe+Rkd5PfepTx/zd4cOH8brXvU4aszR0j+dd73oX3vSmN8lI9Yn44Q9/iO3bt+NLX/oSLrzwQjz1qU+V38WoOA1uQllXrFiBD3/4w1KW1772tXjuc5+Lj370o4gKYzQbTp6mVW/Cdiw4jg3XdSCYYimjztEjZg4DbgxWPAkrkZYaIZwdgw5U6k0ZOXB53RxbKqty7diUk6jgwpbqkhG/uAe4DjCtx9oDUwWmuNFTr6KlSQ+YnFcGRNTQWKEY7HHNCELM4zjTmIEWsvmhkGNK2eIxqBRLlegQKcWqukbsY+q56pUGdFkD31u1EcrIX8Ljs2oh7llqa0A9ekOh2lBj2BpTvtI4ZfQ5aupN5Qzh/OG598tW0EA2mc7eaMjetI7jwHVdnlxchEUtGyPMrm3J68b7TcCSTi8dODQppD5IJSyZpUKHyOGp6MeUqGw36itHjiudXLW6HoZpuRZIY57ZWpxHHMdCqapHCnmhopwznHfZGpx6i+doRBvOxFzS4QAKhcIxR/0kzwLnvZtvvhlPeMIT7v9X2LZ8f911153wv+H5o/+eMKp79N+HYYgXvehF0rDetGnTGV1Nfh4N6t7e3mO+h/+eu++++5RleagxRrPhpFAx0VCWe4epACz1wGmBlwD85qJsIgwALw4d4IJtUa6FQ4cUN+I5cg25eNBI5TkdoFHFZUa4IBvTebnmdTS45VoytPa98rpxUamHbBYfTXndWrJJp40GsnERbi3IRLhY4xTC81pcN+t+2Xj95Jg6mlw3+1jZeCF1uG68r3idWrJJ1UDZNLjfeN/zaBksrVcd9JZypFpye8cxukGDbTskGbfk89mSi+NLY0sHOH5KZ91/3XQYU0LHOBVXSy5uz+IcokPKfWu+aOkGOrcoribLEcMZsmzZMuTz+cWDqdQnYmpqSqZBH22YEr4fGztxkInnf9vff+ADH5AOyde//vVn/G842fe0fvdgf0PDOqrsIT02bxm0gxN+Wz4jU7SbnGlBT6WLbCYJHXBXXYjmHT8HirPyvZXKwVm+ETrQmUug2vDRWEjzpFLtyCWgA8NdltxjWlqI9NH7vKpPDw26up/pi8AcI98WEHOAC0b0WHxwL+lEIVxIj1VpqYNdzCSIXraOjIVcykahwvtNpfMOtKtIVtR0pIGuHPc1A3VfGdCUjdkEUZOK22jPuJgp+rKAD2XLpx25JzFqGL3tb7NxeCaU+4dJe9qSYx01jJIOdVpyn2R5IV08l7LQk49eNs4VuXwec7OzaDZVqgVTB5mOqANDnY7c08xCh4R7S3vbor/fyLY1NibnBKaL6lnIJS1cuFoPwzSfTcloc2stQsd0Wy4DHejMujIlm9s8eOHojOtr18Pb0N8OjM0BxQV9T4N5ZY+lRX0Vw5lz8OBB5HK5xfcskvVQcfPNN8sUbu5x1mFt9lBjjGbDScmmk9LgY5o2H450KgFPh1AHPc89y+Bd8qT7C4H1jsDOnLhC30NNKuFiuCeD0kIhsEzKQ4LWqQZkkxYetgoYm1cRos6sWozrItsTLxTYN6484j1twECHHrLRUNg05ErDOQiATNLS5rrRcN8w6GCyYMlU6HTcQmdWD9m4ONu8zMbYnEoXT0kjQQ9HCGUY6o4jnXDQ8EPEXGVE6yLbih4bmYQljWamftIo5YJcB5Z32UjHlfONKqEvr7Yt6EAmk5FpvPVGQ2ZL0WB2mLKiAV05WzqfW4XAurK23PKhA8M9Np55BbBvXMm2qt9GZ04P2eLxGPp6OlCp1qTeSiVi8pwOJGI2VvcnMFdS+5qzSQeZpB7rJG49uWiFMpyZOZBP8Z7TY0yXHLxsUV+6he+nwXy00Xwyurq65HaG8fHxY87zPQtwnQief7C//+Uvf4mJiQkMDw8v/p7R7Le85S2yyNi+fftO6Z/Czzu+infre1vfdTJZ+G9PJqMJ4OmhSQzawiqVPHTEbuuRh47QSNbFUD4e7ldbpUGk70TQSNi8HFpCw3mwQ4/F0IkM5/52PWWjoTeoifPjeGhUdeYeWMREB2g46xC9PZls3Tke0JJEMikPHWFWSE6PwPcD6G230dsOLYl5LmKeHtHl44l7vG56GPEnMpxHuqOWwhAFsVgM27Ztk1WnWQG7tR+Z71lU60Sw2jV//8Y3vnHx3I9+9CN5nnAv84n2GfM8i42dKvw8tqWiAd6q4s3voUHMgmWtvzm+3dXRskSBnqt6g8FgMBgMBoPBYIga7qGPeh/9GXw/20295CUvwSWXXIJLL71URoPL5fKigcs2T4ODg4v7ot/whjfg0Y9+tKxYzXZPX/3qV/Gb3/wG//RP/yR/39nZKY+j4RYYRoXXrVt3TA9mtv7jKyPRt912mzzPXsvMBnrSk54kjWMa2x/84Afl/uV3vOMdeM1rXrOYbs5WU6za/fa3vx0ve9nLcM011+BrX/savvvd7yIqjNFsMBgMBoPBYDAYDOcRz3/+8zE5OYm/+Zu/kYYp2zt9//vfXyywRaP26KJ6V1xxhezNTAP2r/7qr7BmzRp861vfekCP5t8Gv++LX/zi4vuLLrpIvv70pz/FYx7zGJk2/p3vfAevfvWrZeQ4nU5L4/7d73734n/DdlM0kNm6ivuoh4aG8LnPfS6yHs3EGM0Gg8FgMBgMBoPBcJ7BVOyTpWP/7Gc/e8C55z3vefI4VfadYB/zF77wBXk8GMuXL39A+vXx0MC+9dZboQvGaDYYDAaDwWAwGAyGE6D6ZkQvgyFa9OgnYNAa9qZt9bvUjcUe0hrCSpqt/oi6IftJanrdVN/thSawmtHqw6kjussWaCobYZ93fecRfec4P9BXNp3HVGu9FWqst8JQb92g6ZjqfL8ZDEsJE2k2POiCaO+Ej7mykP39WJ23r83WoiULFWd5ZhyNUkGW4U9k25Fs79ZCNur0e48AB6fV+6EOYP2g6pEYNVSceyaA/ZNqYdSXF1I2HfoNy0XH2B74h3YCoQ+7rRfuygtgeXpUb2fbpL0ToWyH1ZYC1g7YWvRCJrOlAIemfTR9IfsPj/SwerutzXX7zX1Apc7KwQKXrQXaNeg3TMLSLPzdt0JUS7DiKdn/3c51QQcazQDjMyX5yr603e0ppJN6VOidL4X4zq8qODwZIBm38dhL4ti8Ug/ZRKMGf+dvEM6OAY4Hd2QT7P5VmuiGEDNzRdk6iYorn00jl01pIRvnte2HgEMzqrMNKy6vG4i+9lFL3we7b0NA3SAE7P6VcNdug+W4Wuit8bkAY3OB1KltKQvD3Z4WOpVUivMoz89AhAKxRBLZzm44Glw3g2EposF0aNAVGsyT86Hsi8j+r/snA0yX9PBWVmYnUKMiECHCIEBldhL14ix0YPc4sGtMLUJ48P3uMWjBoWlg5yjQCJRxv38K2DEKLQhnjsDfewfQqHKVhHBiP/w9t0MHZssCO4+EqDVUf+uJAnDPYT4b0T8PlXqIvRNNVBtKlvlKiN1jvhZR51JV4NrtwExJXbfxOeCX24F6U+hhXN17A8L5aSkcDejmjhsh6pWoRZNjNzpVRKXWlPdYveljbLokX3WQ7Zs/q+De/bzHgJlCgG//soqD49HLRvwdNyIc26u8grUK/J03Q0wfgQ7QYC6UKjIiKQ3o+SJKFRrQ0UO9QF3Ffr7NANhxBNLBqgPhgXsQ7L4daDaAwEew7274fK8B08UQB6f8xayLyYJ6rwP1ShnFmUkEvi9lq5WLKExPaKG3lhyWrcdhiBQzAoYTQqU+XxbSWxpzLdnrj4ulQkWP1KhGucRmobBdD46nIhyNSgk6MD6vXhkU4mEddS5qpopqLZmKsZc0I8zApCayifkpiCCAlczAiiUBL45wblymWUYNsy24kOR4xj0g7gKFinImRU2xGoK2VMJTvaQpX7URoqaBYTpZAMp1IJ8CknEgm1TXbbYctWSAKM+rCHMyDSuWkPcdamWEGjjfGn4gI8yeY8N1bMRcG34QolaP/oajXjgyFSCXtpFJ2ejI2ajWBfaPRf+cCr+JcHYc4HjGk7BSWYigiXAueuuPhkqlWpe9wV3Hges6ypCp1aEDdGjZlprjUnG1f3JCE90QTh2Wr1I3JNKwbAdi8hB0oFBVEWZmHXGt5NrKcamDYdqoV6VzxvVicFwXluOgUavKYIPBYDh9jNFsOCE09JiW1QpWyT0x0CPFmFgUbmGfjlROQqhzGuA56rotiCV/pnGqA5SD+rKlzxlV0EU22BTkqL1XNJZ5TgPv6vH3fSCkz0aL58G2Lfm8tpZojP7J51eD7EDeW9bCfYaFV143Liwjx3EWJrnwqAtny4Vl1Mixs9S8S/j/8pQGabwxV10231eytXSEp0PGJwXjfLHgaKNxYEnFpYNw6lldHFP5KqQRrQPucXqLhza6wfXUfuaWvuf4uh50wLEsVSRqYVx5DXWYe7EwX0jd0NKpXCfxnCb3nMGw1NBh6WLQEE6qAx1KY5brISoNIaPN3Tk9bplkW6c0ksNGHUGjDtt1kch1QAdW9KhIZLGqDi4yV/ZAC5Z1qkhCsUYPuTL6KK8O2D3DsBIZiEoBYVmFONzBNVoo+J68hXRCRU1LNWVfDXYwYhS9bO1pG+mEJVPHmarth0BHztFiv3V/O9DbxjRt7rtmBBwY7gI6slFLBliZDtjtfRCNqrrfahVYbT2w8t1RiwbPdZBNxWTxtHozQNMPkYi7SCejNxTSSRvb1sdQ9wUmZgNMzYXo63CwaUX0sjEC6QxvWEi3n5PZBEhl4fSORC2anMe4h5mvjaaPZjOA67jIZFLQgVV9yuFLnUXdwIwVXXSDM7xeZg6I0qw84MbgjGyCDnTlHbk2qi7Mv1RXfe2uFnorkc7KTDy/2UCzUZfGczrXBksDR/SSg+Opw2GIFD3crwYt6c3b8BxLpmTTgd+dc5CK6/HQxjN5uUBqVIpSOcXTebiJJHSgJw9cuhoYnVPv+9qALg2MBNKWtnDJKoHRWRUt7c4qg1AH7FQO3qZHIJw4ABH4sPNdsDsHoQNcFF2w3MborErTziUtOa46QMN9zUAMk/MBmgELgVnoyjlaLNoo26M3C9x3RDkccklgDYsLaSAbnW7uukthj+1BuFAIzGHBKJnxELFsloWejgwSMVcazUzRzmcSsiCYDjz+kgQ68w5Gp3ykEjYuXheTqdo6QKPZSqQQzk3Ccj1ZNIpp2jqQSSfhODaq1bq8/zKpBGKx6J0NZKAdcFffv5WI7zv1uGywO/rhbXsCwrF9qhBY9zLYXQPQgXTcxtoBD9PFQDpTcykbbWk9ngWmZbf3DqBaKshIvRdPIpHORC2WwbBkMUaz4UEXbp1ZHnoogOOJpTLy0BEuNnRZcBxPPmXJPaY6QsPZHtkMHUnGLKzsjd7YOxHcSzfYqed0zoj35uXQElbfdQbXInoz+YHQsdDGTeAawjRjGsrgoaHeYmRZh+jyiWRLJRPy0BE6fHnoiN3WIw8dYccCHjpCwznbrkdHgKWMANPwo9X/UX+/waRnGwwGg8FgMBgMBoPBcFKM0WwwGAwGg8FgMBgMBsNJ0DOfz2AwGAwGg8FgMBiiRodCXFF/v8FEmg0Gg8FgMBgMBoPBYNDSaP7MZz6DrVu3IpfLyePyyy/H//zP/yz+/jGPecxiT7nW8apXveqYzzhw4ACuuuoqpFIp9PT04G1vext834/gX2MwGAwGg8FgMBgMhvONSNOzh4aG8P73vx9r1qyR/eO++MUv4pnPfCZuvfVWbNqkevC94hWvwLvf/e7F/4bGcYsgCKTB3NfXh1//+tcYHR3Fi1/8Ynieh6uvvjqSf9P5BHuEHpgKMFMSsp/vQLuDnrweyQkiDOAf2YVw+ghLucLpWS4PHdrs8F6enG9iptSU7zsyHrrznjayBfvuhr/7TiDw4Qythrv+YbKKsOHBGZ8Ddo4CdV+1ENs4pHpw6wCf0R1HVB/k9jSwYRBIxKK/38hMUeCW3ar3a3sG2LaKbVn0kG2uLHD9PaHsIZ1PA5ets9GZ00O2Sh3YfogyAsm4GtMOTZoF1BpqTNmeiH3ft44AAx16XDfddSrnkLE5SJ26ohsY6lRVtaMmFAJ7J1Q7Qooz1GHJnuq66K1bdvq49T6fHaewccTBwzd5cGw9ZDsyCxyYEghCVX18Va+lhWzk8Ayw8wjQ8NlGFNi0jH3go5ZqCWLSsw1RG83PeMYzjnn/3ve+V0afr7/++kWjmUYyjeIT8cMf/hDbt2/Hj3/8Y/T29uLCCy/Ee97zHvz5n/853vnOdyIW068dxlJi32SA0dlQKnf2H9w9zn6hXLhFbzj7R3YjOLwTYE9VIeDvv1u1j+kailo0TBeaGJ2pL77nz5zruvPR34/hofvQvO0X9DqwUS38e34jr5+3+YqoRdMaGlW37AUaAeDafDbUIuRhq6LXY6WawC17gGpTyXZoGqg1gctWC9kaKEoqdYGf3wUUKmqhtn8CqNSAJ10k4LnRylZvCvz41hBT8wBb5c5XgGI1xDMuY/uYaGXj4pv321QR8BzV4/rmOnDFWiCdiN5I+PW9wMEp5TQqVoBfbQeecIFAR9Ys6h4MOrb2jEs/r5yC7zrIXuZAf3vUkgH7JgR2jS3MZ4KyqvljWWfUkgG37w7wk1vohBZSvl/eEcprePmm6HXqRAG457BAKNgmDtg7oRwQ6weifxYmC8DNu4Hmgt7aPa5+ftjqqCUzGJYm0Vs/R0WNv/rVr6JcLss07RZf/vKX0dXVhc2bN+Mv//IvUalUFn933XXXYcuWLdJgbvHkJz8ZhUIBd99990P+bzif4KQ/XVQRZvZZTcYt6SWfK4fQgXCGEWYHVjwFK5GWK5Bgdhw6MFdW2wPini0PMr9wLmqC0b2A34SVaYOVzgGOg+Dw7qjF0p7JojKSM3EVWYu7yqBh1DlqpovKSG7Jloip6CQNraiZmFcGcy6tjD22HZ4pqSNqaCzPFJVsmSTQlgbmSiqjIGpKtYUIc0wdmYSKPE9rcN0oB69RKq7GlBH6WgMyQml4cGcDrxF1aiqmrp0fKKNLB5g1QDOPsnFsaQROzAvowK5DPsJQoD1roy1jS4P53gMBdGCqKOQ4phfmX44vryXHW4f5l3orm1BjSicXsxxoOBsMhtMn8iSNO++8UxrJtVoNmUwG3/zmN7Fx40b5uxe+8IVYvnw5BgYGcMcdd8gI8o4dO/CNb3xD/n5sbOwYg5m03vN3J6Ner8ujBY1s0mw25XE2aH3O2fq8hxpO+CLwpaEc2pZ8H/pAGDhoNqNXBr6wIIIAVigWZA1hC8DS4HqL0JdOoICanQZ+EEqFf67vhVO555qwEXJpFNJjr8bVsjim0V83nRFcZIQqOiQvX6AiMoHPaxotYSCkXMwGsRdko6xBwHvBivR+E4G4//rZC9dNynxuZTsVRCjk0yDHdCGbRj6xgR25bMHCGPI6CY5peP/7qB/VIBCLYyhUoo+MTPI6Rn2/6Yyca8XCdeNcIu4f16jvN0kYStnk/HGMbNHHVhyLF40LEFXbhhfQtWwtdKrgdeN86yudwJ+thTGNOrW9NYZH6y0uS3z+cx7i+MdSfW4XMenZBg6BiNgd1mg0ZDGv+fl5fP3rX8fnPvc5/PznP180nI/mmmuuweMf/3js2rULq1atwitf+Urs378fP/jBDxb/hpHodDqN733ve3jqU596wu9k6va73vWuB5z/yle+csyeaYPBYDAYDAaDwXDmcG3OQBjX+iz8u1RgUC2fz2P8hh8gl0lHK0upjN7LnrzkruH5ROSRZu47Xr1abbDYtm0bbrrpJnz84x/HZz/72Qf87WWXXSZfW0Yz9zrfeOONx/zN+LhK0T3ZPmjCNO83v/nNxzwUy5Ytw5Oe9KSzdiPSq/ajH/0IT3ziE2VhsqUI/SkTBYH5itrXzCJg2UT0XmciI9+zYwjmJqT3zekcgJPrgi4UKv5iSnY+7SKXOveP2qnec8HkYQQHd0q3s903DGdwTeQe8aVAsQbsX9jLzGJby7uU114Hqg0h91kzTTafApZ3M03Q0uJ+o2z3HgLKNZXKu26QaYJ63G/1hsBd+znHCeSSFjaNWEhqUkCNKZS835jezrTPkR71qgPMQLrviNoaEPeAtYOQ1+9ccr7oVBaNmiqoNN7BTs4llkb6nvtgVSZBb5uFLo32qO8bC3Dv/kCmja8etLFmyDnneutU77m5ClPvVSGwzoyFvjY9CqgR1mrYN6H0FgtYUjdEobdaGZ1LFhNpNuhgNB9PGIbHpE4fzW233SZf+/v75SvTulk8bGJiQrabIpzgaPieKFLdIh6Py+N4OCmebWV8Lj7zoWSoC4i+tNZJ6B1Wh4Z05j105qP57t92z3kDLHU78pDKdD7Q4QEdWWgJh3trWtP7zQMuXQctoWwPP7mqiFy2DcugJRztLSsi+u4lrlNHetWhrb7Xx/d8DGuWeVizTM97rjuvDh3pyqsjapbyM2swaGE0M+LLFOrh4WEUi0WZHv2zn/1Mplvv3r1bvn/a056Gzs5Ouaf5TW96Ex71qEfJ3s6EkWEaxy960YvwwQ9+UO5jfsc73oHXvOY1JzSKDQaDwWAwGAwGg8FgWDJGMyPE7KvM/srcM0BjmAYzU2EOHjwoW0l97GMfkxW1mT79nOc8RxrFLRzHwXe+8x28+tWvllFn7mV+yUteckxfZ4PBYDAYDAaDwWA4I0x6tiFqo/nzn//8SX9HI5kFwX4brK7Nol8Gg8FgMBgMBoPBYDCcbTQpY2MwGAwGg8FgMBgMBoN+aFcIzGAwGAwGg8FgMBh0QMCWR9QyGKLFGM2GkyLCEP74XoTzU4Djwu0ZhpPvjlos7WHrjmDv3fD3bpfTnLt8A5xVW7RpQTFVCDE2FyIQbEFhYaDd1ka26ZLAoekQQQB0ZIDhLhv2OW6ddKrMlgT2TAB1X8m2uhdwHT1kq9YDTM7X0fRDpOIOutsS2sg2Uwxx/fYAcyWBrryFKza5yJzj9kSniqhX4R/eibBSgJXMwB1cCzsRbS/OFg1fYM+4QKEqkPSAFb02Mgk9rlujKfCbnSFGZwRSCeDi1Q562/WQTQQ+gkM7EM5NwHJjsqWe3aa6a+igG6rz02hUSrAsG4lcO+LprDay7Z8CRmfV+2WdwGCHHq2TKFupUkehVJM/Z9MJ5DIJLWQjbC85XWjIdljZlIOefEwb2Q5Ohrhtd4haQ2BZt4Vtax14mugGg2GpYYxmw0nxj+yCP7YHsGxa0GiW54BVF8HJdkQtmtbQYG7c+CPpdGDDy3DyCNhe1V2tqr5HyXQxxM7RQPZZpU4vViEV/bJOJ2rRMFcW2H4oQNNXfSTnq4AfhljdF71sNFxu2af6ILO/6mwJqDeBrcO8jtEuQBrNEAcnKqg3Q9iWhUotQMMPMdyTily2Sk3g27/2MTkfSiP+yLTAbLGJZ1/pwYu4VzONq+aumxEWZwDbgSjNolkpILbhcmlsRUkoBO4+GGKqCNBnNFcGCrUQ21baSHgRXzch8LM7Auw8JOA6gD8LjM8FeObDHbRr0NfX33sngtFd0ihlC8uwOA1v0yNgZzujFg2V2SlU5qZkH2QBgWa9CssaRCyViVo02RN8+yGlDwjvOU4fNJyjhgbzxHRRPhdUqrVGCfypLZuMWjQUKz4OTFZlj2aOa7kWcLmEvo7oO7iMz4b4/k0BqnUBxwFGp4XUYY+5wCz9DYYzwcT6DSePlk4fYYlyWIkUkEhDNBsIZsejFk17/H33QIQB7HwH7FwHhAjlOR2YKobwA4FU3EIyZkklPz4XQgemikIazOk4kIpZ0jgdnxcLC6VomSwogzmTAFJxIOZSNkYDo5YMKFV9aTDHPRsxz4bn2ChXA2lMR82hqRBT80IaU20ZC7k0ZHRyYi76MRXlOYR0BMbTsBhdTmQgKgVlREdMuQbMloG4p+433neVOjBTghay7Z/gHALk05bMuihVhTwXNXSEhFMHYTmezBywUlmIRg3h9KgWOrVempeOLMeLwXFjEEGAerkIHTg0Q0Ne3Ws8/BA4Ev2jIClW6lIPxDwXMc+RghZLNejAXNmXOjXuWnIOpqNhptSU4x01e8cEKnXOv+pZ5Xxy32Ehs1gMp4l1VAXtyI6oL4LBGM2G0yLqyNWSRHP9pMuQaiLG0pNXg04YJ0OKpalsD3xA9X1Q9ZXMcF6h6Y2m8xSis2wnG96lJLPBoBPGaDacEOkN7xoAN5eKWhmolWF5MW32humMO7IRluMinJ+Wh2XbcFdshA50ZW2ZJkvvc7XBBEGgN6/HNNCVs2QEt1xnVE3IdLe+vCVTjqOmJwckYgCDG5SPEebevIo4R00m6SLm2jLazOhyMwiR5jkv+nEd7LblPubZgpB7mgtlYKDTQk9b9GNqpdtgp9uBeuX+OS6V1yKNN50A2tMqu4ER5kpNZWB0Rp/FK2Ub6bVQaXAvp8BMEcgmLSzv1WBMHRd29zBE0ISoliAqRVixJOzOAS10ajybZ8gZAbO2/AZsx9FmT/NQp3K+cY7j4drAgAap2SSTiks90Gj68qDVxz3NOpBPu1Kn1n0h52AGmDsynhYBhhV9FtJxSz6jnH+pt9YMWpFvjVmKCMvS4jBEiwZLPoOuuP2rAdtFWJgEHA9u97DZz3wKOCs2yj3M/r7tC0b0BjgrNkEHOrM21g5AFgLjlmsWAutvj964IvmUhY1DDg7NMIVcGQjLOvWQjUbBxStUIbBGUxUCW9WrR+YFDebh3hQm51QhsORCITAdZOM2gGde4eG67T7mFwqBXb7J1WLRRgPLW7MN/uEd0sDiFhRvcC0s14taNGkgbFpmY+/E/YXARnpsxCPez0x4Xz16q4NM4thCYO2Z6GUj7oot0sEbzk7AZhr0wGrYmuitVFsXbNtGvVyCZVtIZNu12M9Mlnep/fMyJdtShcAG2qGN0UwK3Bsg1HtdjOZsysVwdxLTxSbCUEgjuisX/RxCetttPPVS4NZdoazBMdQNbFsTfY0Qg2GpYoxmw0lhhNTrXwnwMJzWotJduUkeOsJoMw8d6chwj6SeSr09bWHbCmhJIuZgWU8KOsK9zE+9VI9F5PFYsQS8FRdAR2KuhXUDehiiJ5Lt8o16PqeW7cAd3gjw0FA3JPOd8tBRtuEudiyAlrKxYjYPHaGhzENHBrtseRgMht8dPZ9yg8FgMBgMBoPBYNCjEpgGMhiixLifDAaDwWAwGAwGg8FgOAnGaDYYDAaDwWAwGAwGg+EkmPRsg8FgMBgMBoPBYDgRrV7JUctgiBQTaTYYDAaDwWAwGAwGg+EkmEiz4aQIIXBwGpgsqp6Nw53Qpq0IZfML02gWZ2BZNrx8F9xMG3Rh77jAntFQ/ryyz8aIJu2JSLVWR6lU4VVEMplAOpXURra5ssChGaFaTmUtDHXoc93CWhnN6SOyB6yTysHtGJAV5nWAfV+rc1MI/SbceBLJtk5ZRVgH2AZrulBD0w8Q9xx05hJwHD2um86IZh2Nu2+CmJ2Alc7B2/gw2Jk8dCAUAnPFKmq1phzLtlwScc8sJ05Fb4UT+xHOjAGOA6d3BHa+G/rIdgDh5EEZ0ZKydQ1GLZbhdyScGUdz1x1AowanZxmc1Vu00Q0Gw1LDaDnDSdk9DuwcVT+HApgsAJesFGhLR2/ENOcmUBvbR00P/s8vzSE5uFoLw3nPmMDP7gjR8NX7/RMhHrXZxuqBqCUDarU6JqdmEAahLMRYqdZkb8lcNh21aJivCNy2T6DWVFlIEwUhe0uu7ov+fgvrVdT2b0fYqErh6LAJGzXEB1ZHLZo0lIuj++E3arBgoVkuIGjUkekditzhEIQhDk2WUKn7ckyLlSZqjQBDPRnZi9hwYkQYov7r/4G/716AjpkwQDB+AMkn/hGsRCpy42pypoT5Eu832TYXlVoTQ715eK5ZjD8Y4ZFd8PfdyQFWRurMGLwNl8POR9/nKRzbC3/HTUDgS50aTh+Bu/FyOF1DUYtmOEPC+SnUf/VtiEqBvdgQHNkDt1ZCbOsjoxZtCaJBerapnh05xt1vOCFU6Aem1HotkwCyCUhjZnQOWtCYnZCvdjwJO5ZEGDTRnJ+CDtx7METTBzqy6mgG6pwOlMoVhGEIz3MR81Tv3GKpDB0Yn1f3GO83HrYFHJpRUa2oUUZyFVY8BTuegmW78OcnIfxm1KKhUSkiYBTBi8OJxWE5LpqVIsJmI2rRUK75qNZ9xDxbRpld15bnaDgbTo6Yn0ZweA+sVAZ2vhNWvhPh7KRc9EZNEAqUKg04to0Y5xHXkVkEPGd4cJ0ajO6WC28rlZOHaNQQMLKrAcGR3dKYtzJtsNJtQLOBcDT6+81w5gQHd0mD2cp1ws51SN0Q7LlbC71lMCxFTKTZcELEQnT5eL8Wz2mBoBGqpGM0jf/jokQHZBD3qAtH40+X69a6RK0IpPx/Ta6bFOOo60YR5bnjzkfDCYRjPEYIPUQ7hgXZHviLhx4pAp9OxeK10kA0nRGc33jzWwt+bfkqIDSYSHjP83/2A+786GVbEuPamnstC0I+qno4VJnNcLROFZQz1EQ2w+8wpkchtxMJbXT+0sL0aTaYSLPhJDB1sr8dcm8pAwjlOuA5QHcOWuDmOuViI2zUZZosF5Veth06sLLfkuui+TJQqKil5AoNUoxJKpmQC6Jm00fT92UUN5VKQgc6s+oe471WbagIfW+eej76a+cw+uJ6QKMq7zfhN+CkF85FjJdMw3Y9hM26TNXmnms3kZSR56hJxpnRYKPRZPaFOhIxB/GYSeN9MBhdtrv6IcrzEOUCxPyU3M/s9C6LWjS4jo1UwoMfcjwDNPxg4VwsatG0hvOu3b0MgunPtTJEpQjL8WB39EEH7N5hWlkQ1aKUjTrV7jap2UsZu385rFgSojiLkPNIvQZ7YCUszzyrBsOZYCLNhpOyrl9FSScKqhDYih6gJxe9AUPiXWqDsF+YkZ77WHuPMqQ1YOMySzp4dxxmJBJYN2Rh07Ae1y2VSqBD5FEsVWTEKJ1MIJ/PQge6shY2DQH7JgX8EOjOAqt69bhuTjKL+NA6NCcPyNQ2Gsyx3uWR7xmWssXiyPQOozozLrcpuPEcUp29WsjmuTaGutOYmK2iQYM56aCnLQlHA0eIzjCNMv7Ip6Nxy88RTo/B6RqAd+GVsLPR12zgfdXbkYVjl1GtN+HaNjraUojHzHLit+EOb5TRIjFzBLAdOAOrYXdoUOyC88jQepkSFY7vU4XAKFv/qqjFMvwOON1DiF36JDTv/Y0sBGb3LYe35fKoxTIYlixGyxlOiutY2DAIeegGK2Yn6AXX0BPOReWWFTygpWzZTFoeOtLXZslDR9xsuzx0xEum4A1qeMMBSMRcDPfq4ZhZStjpHBJXPgM6worZvUwNMZwWrFrsjWwGeGgGOwG4I5sAHobzBmdolTwMvxvciBX1Zqyov99g0rMNBoPBYDAYDAaDwWA4KcZoNhgMBoPBYDAYDAaD4SSY9GyDwWAwGAwGg8FgOAGiVVE+YhkM0WIizQaDwWAwGAwGg8FgMJwEYzQbDAaDwWAwGAwGg8FwEkx6tuGksF3SwWlgcp6VtIGRbiCvUdHlYH4KQWFKtsdw2/tgp/PQhXLNR6HckD/nUjGkk/o8arMlNa5hCPS2Qfbj1oVaI8BMUfWPTicctKVdLVonkWpDYHRWoBkI5JKqyrcusjV8gbG5UL6m4xZ622zZa10HKrUQ191exVwxQFe7g8u3phDz9JAtCAWmC03UmyFiro2unAfH0UO2MBTyOS3VBOKehWWdkK86wOfzyAwwXxGIuRaGOoFkTA/Z2EqvUZpHs1aR1aoT2TbZlk0X2djCcbooZNs1zr2cS3RhqigwOS/YFUvOb+1pfWTzq2XUS3MsIYxYJgcvpU/19mo9wEzJl89sNuUin3K00Q21Rih1Kuc66tT2jD6yLSksWx1Ry2CIFH1W8gbt2DUG3L4XsmcuDegDk8CjNuphOPszo2gevAci8GUh/mB2FLEVF8DJRG8BlqpNHJqswA+EfD9XbmKoK4VsyotaNEwXget30gBU7/dPAReOAMu79TCY94xV0WjyugnMFC34QRzd+VjUoqHWFLhjf4hyXY3pqCVQbVhY2etELZo04rcf8qVxRbgcopyreqNfHNUbIf7t23PYdYA3HJ0MAgdGm3jhU/ORG6c0/PZP1DBfDhbPlaoBVvQlYEfcR5rG1fZDAodm1dzL52GqAGxbBXgaGPX3jQrsm5RtfaVs4/PAJauAhAZGfW1+GuWZcSWcBTTK88j1LdfCcD48A9x7RPWh58COzgEXjQD5VPTXbXxO4M6DdArKIcWRWYELlwOd2ehla1ZKKI3tl33oKVujOIt07xBimej7llfqAfaO1lD3Qzn30kAd6oqjMxe9vqczcPdYTc7DZLroo+l76G2PXqcaDEsR47YwnBAu1HYclvoJ+ZQ6SnXIhZIO+JMHIMIAdjIDK5GBaNbhTx+GDswUGtJgjnu2PIKABmAdOrBvAqg0GN1QYxoEwK5RaMFsyZcGM6NpiZgjb77J+YY0IKKG0Rcaouk4kElYcGy1qGw5RqJkpiSkwZyIAam4JbNCpgoC9WbUkgG7Djax51ATHXkXfV0ucmkH9+xp4MgknV3RR8CLlUBGSpMxW74WqwFKtfuN6Kgo14GxeYCPQTYBpGPAXIXjCg0W4gKHZiCfASlbnNFwSMM5aoQIUZ2blg4aJ56A7cURNBuol6IXjvPYvikhbXlet0yCjkI1j+jA/illzGfkHMfsFWYk6SFbvTCNMPDheAk4sQSECFCbm4IOzBSbaPihnEOSceVEnZjTQ29Rp9JwTsQsJOO27DQ8Ma8yuQyniaXJYYgUYzQbTgjn1CBUCyPCgBWfVz/69aSC1p6thJPRNKathHoIxzQoKZKl0neZJstrqQOUg0G0VgCS4yujHhrQilq1oqN8YQq5Dur9eNuY17D1jEQNUwJxlD7ldeOCTQN7Hk1fyDF0F3KaXNeS8vJ81PAacQxbz4Ic04W5L2p4zTisrYB3S0Yt7jehrlEr4G0dJXPkUDDBOeQo3bAw1rqorcXrtrAI1mFMCfUAr5rSXere00U3iFBFcVs6lZLynA60xDhGb+lxu51ANmtx3jMYDKePMZoNJ4T2KPdb0dvMVF5GElwb6NFk27Cd75YrENGoIaxX5eLDyXZCBzIL+5fpfW76IQQEsprsaeb4cTHESBbHlYui/ugz3CSZhCPTYukZ53XjYjKbcrTYm9uWUhFcXjNG2uo+96pbiGkwrLmkLVN2GbXinuZGE0gnLCSjzw7EcJ+HjryNiRnu8Q8wNRegu0NFnaOGUaF4zJKpi7zfas0QcUad49GrxXRCRSOrTd5v6nmNe0CbBltjEp7KUqn5C7I1mDIOtGsgG/cwe8m03LYT+k0EzTos24aXTEUtmjRYunNAM+R2D5Xxw7m4IxP9/Ea6s8rY4xzX2r7TrUFqNpH7l+l8bjYQ+g1a0fDSOehANnm/3mos6K2cJnuaM0lb7p1XOlVlRlFenjMYDKdP9KsDg7ZcuAJY2auikamYes+CLzrg9a+E2zMM2A4sLwavfzWczkHoQFc+Lg8qJirTrlwcnfno99MR7l3etEwtwB0HWNULbFwGLeBCY7AzLtNked06si4GOxPQgba0hXUDtix2xLVQT97C+gFbi4URDeS1/Y5MzWYCXnvGxtp+N/J9uaQj7+AFT81L45nzyJphDy98ag6pRPSqx3UsLO9JIJPkApdpxg6W9yZkQbCo4dyxddiShgxlyyaBLcOW3BoQNbzntyyz0JtXsjGdd/MySz4jOpDu7kc82yajzY4bQ7qzHzFNikat7bcw3Kl0atwF1vVb2jgtV/ZaWNGjin56rtINy7qgBfF8JxLtvbCptGwH8fZuJNt7oANtGRcDnTHpuKSDtyvH93ro+1zKxVBnTM5pfG6pU4e69ZBtqSGY3aDBYYiW6N39Bm1hFO3SNSrFp5WypQuMKMSG1kMMrlPvNRKOsvS2J9HTltBQNmDtALCmX6WiamBXLcLr1JmLoSPraXfdSG/eRk9OLFw3vWSjoUyjRUfZVgzG8PoXdsq9/VEX/zqeVNzBmsGUTFnU7X6jM+SSVZbcf6jbmHKP5MUrlGyttFldsB0X2d6hxZRsnWSjo2bjkIX1Gl43OmrWDdABp991oyypzl4kO3q0lI3FKll5v/VeJ1iQjMayjrIZDEsNYzQbfisLW4e1RGcloLds+taU0Pu6WVpfN11lI7oZzEvlntPNYF4qspkxPf+um5Ht/JPNYDhXlMtlpNNnd9+QxuaQwWAwGAwGg8FgMERI1FWzTfXs06a3txcve9nL8Ktf/QpnC2M0GwwGg8FgMBgMBoPhvOBLX/oSZmZm8LjHPQ5r167F+9//fhw5cuR3+kxjNBsMBoPBYDAYDAaD4bzgWc96Fr71rW/h8OHDeNWrXoWvfOUrWL58OZ7+9KfjG9/4BnzfP+3PNEazwWAwGAwGg8FgMJwAYdlaHIbTp7u7G29+85txxx134CMf+Qh+/OMf47nPfS4GBgbwN3/zN6hUKqf8WaYQmOFBGZ8DJguqDcWyTtU/VBdq9Qaqtbrc5pFKJRFjnwxNmJgLsWdUVSFd2Wehp12fyc5vNlCrlGV12XgiiVgiCV1gP8nZUoAwFLIVENtQ6YJo1hFMH5E9YO1UDnZbjzYFVsIwQLVURBgEcGMxJFIZbWQLQoHpYiB7SCc8C51ZPXqYEhGGaBamETYbsnVdLNcp+/rqAJ/PRmkeQaMuK0LHcm2wbUcb2arVKpqNBmzbRiqdhsN2QJrINlMC5itCtnbqbbPkfacLfnkeQXle3mdurgt2TB+lWm80UKtWZaXIZJI6VYNm7wtU6yHmyoHsEJBPOUhr0LauBftH18sFiFDInuCxpAZNyxfg3NYszkCEAZxEBm46p838azA8FIyPj+OLX/wivvCFL2D//v3SYH75y1+OQ4cO4QMf+ACuv/56/PCHPzylz9LHyjBox74J4Na9QDOQTeqwdwK4cgOQ0UDHV6o1TE3PIQjYDwsoliro6e5APBa9kj88FeK7N/goVVVbkdt3W3jaZS6WdUev5JuNOmYnRhEspKWUbRttXT3SyNLBYN49Wke1EcrrZs/7WNYVQ2fO1cJgrt93M0SloE7YNryBNXD7VkQtGsIwxOzEGBq1qjphWUjn6si2dUa+OKLzY89YQy52pWgASlUXy3u8yGWTht/4fjQKM2qCkwZNAamBFbLHb9SyVabHUJufbomGRqWAbN+wbLcXNYVCQR5YaOtET31Xd7cWhvORWYH7RgWoGsjYnMAFy23ZJitqmvNTqI3uAQI1/zZmJ5AcXg8nnopaNNRq1KnT0vHGOaRUKqGrsxPxePR9fcu1ELvH6tLxRibnfYz0xqTxHDV+o475sUMI/IZ8Vu2CjUxXPxKZnBYGc/nwfQgWdAMdNYmeIcTb9OhxbTCcS5iC/a//+q/4wQ9+gI0bN+LP/uzP8L//9/9GW1vb4t9cccUV2LBhwyl/5hmtDJgHzvD2Zz/7WRSLRXmOm6s5yRrOD7gWuucwo0RALglkU0ChogxnHZgvlBCEITzPgec68P0AxWIZOnDLrkAazJ05Cx05C+WawK33KaMhairFAny/Ccfz5MFIW2l+FjowXfSlwcyoUCJmS8NhdLa52G81SoKZMWUwx1OwknQwWPDH98qoc9TUK2VpMDuuK6PMNEY5zqEGshWqIeYrAWKuhWTMln1qp0sBqg0NxrRWQbM4K6O4TiwJ24mhWZ6DXylpsditF2ZhWQ6cWBy266HJcdZAtiAIUCoWpQPE8zy4rotGo4HqaaS4nSvYN3rfpLq36NxNxYFSDRibj/5+I42pw0AYwIol5SHqVTRnx6EDdILQAecujCnHuajJmm5ivolGU2Wq8PADgfHZ6Oc3UivOIWjW4bgxOF5MXsPK7BR0oFGYRlCvymwGJ64yyurTYxBiwaNkOGUYAtHhMJw6L33pS2UK9rXXXovbbrsNr33ta48xmAl//9d//den/JmnHcJhaPspT3kKDhw4gHq9jic+8YnIZrMyxM33//iP/3i6H2nQENopTV+lZbd6+vKV53SABjN7XbaiVXyhstKBWp3X7X7ZeA2rdWiTxstuvovXzbYRtkIyEdMavpZsHF9GKnkvRp1N1jKOF1N3GVGjwDzvRBsJX7zvjxpTEYTyfNRxmNb42Qvjx8vn+4wCRm/EMF2RDpnFMeVrIKRREzV0ZlE2uxW5lWMrpMxRQ7mkbMelsdNgjRo+CjyYlk1a950f/WVT1y1o8gFdnOOEZUEwsqsBnC9kr/ejJltddCrHT65DFvW9gK/BHLJ4jY5Zi9j3zy1RZ9NwvpD6c0EOKdtxD4nBcJ4yOjqKVOrBs3i4DeVv//ZvT/kzT/upecMb3oBLLrkEs7Oz8sta/OEf/iF+8pOfnO7HGTSF66HuPNDwgVoTqNTVAqQzCy1IxuNykUZvuB+ofU6JRPRpZGRZtyWNgnJVyCgzbdKhHj08hLEFbzPTs4PAlwo0ftRzHCWpuC0N5UYzlJEELoq4b81urXwjxE7nAduBqFcgmg2gUYeVzAFe9PecF49Lw49jytTK0PdlFoHrelqMqedaqPlCjmm9KRD3mCob/YKNkRebkaFmXUblw2YNNqNFiehTZZ3YQtTKbyzI1oDluHAXnt9IZXMcGWHmvBssHFyUx2KxqEWTdgAzo6i3pO5qtM5FP4fwGjmcR2hQ+U1539HYclN6KNV4IiENvdaYynMapGaTzML+5aYv5EH/TC4Z/RxCvHhSBhUCjumCTvWS6cgNZuIm0soxLue4pnTaONxvrcEWj6VH1A2aTaPm04UB3YmJB6bHTk9Pn/FWotOedX75y1/iHe94xwMU5MjIiCzrbTh/uGgEGOxQUWcuPDYOAcNd0IL2tiwyKS4gZdwU+Wwa2Uz0i12yba2DrStZ7EhNcVtW2Lh0nR5KKpXNIZ1vVxEFOhrSGbn3VQfaMw76OzxpJPOe43614W49Fm1Ovgve0DpYrieTpOxcJ7wVm7VYGMXiCeQ71X5SLnppRHOfug4FrWgcj/TEkHDVmNKIXtHjwXOiv25MeU71jai0RRHCiSWQ7BuBrYEjhPuWM71DcLnXlZF6z0O6e0ALo5n3fEdnJxILBhUjzm3t7UgkElrItm7QRldWbQXnumhlr4Xu6LeXShK9I3BzncwZkM9nrGsQrib7S/O5HNLp+wtYZTMZ5LJ6GPS97S568u6iTmUxQeoKHUhk80i2dS3WQYhnssh06jGmbqYNia5BVQdB8H0eqd7lWugtg+Fcc7KtfcyKPlMnr3smqSgtL+TRsAoZrXrD+UMiBlyxTnnsaTQzzVgXuFDr6myT0WZZNEoDA6EFI2uPv8jFIzerBzauUeVWKstceycy+XbpDeHCTRcFSjn62j25OGLmHe85XWQjbs8wnK5BbuoEaDxrJFsynZXF3Dg/81nQSba2tIN8KiFTLNV2D31kY5Qvs3yjTL9nJFcr2eJJ5IZWyhRL9ZxqNMd5Hrp7erS837jndetyWxaw5BziaJCp0oJOt8TQWrUFgI5LjSJ+HMeO9naIhT1/OulUZiAt645hoFNFmXXSDZQj09GNdFvn4nYPnWSLd/Qi1tat5hHN5jiD4VzwiU98Qr7yXv/c5z6HTOb+Qre0X3/xi19g/fr1Z/TZpz0rPulJT8LHPvaxxfcUigXAmBP+tKc97YyEMOgL59e4p5fBfEy6m21rpdyPhsayTgbz0fCacb+kjgqUkeaj94TrBBe5bE2kpWx8HjQdU8pEZ5KusjHqrK1scqGr3xyn+/3G4nM6GcwtZJYPx1Qjg/mY+01jneporRv01alSNk3nuCWD2lQf/XEGfPrTn5bZwMwGuuyyy3DjjTc+6N//13/9lzQq+fdbtmzB9773vZP+7ate9Sp5Xx1tF5KZmRn88R//MXK5nCy+xRZPRxeLfuc737lYQ+Ho4+hsF7aIOv73p5LR9NGPflQedGKxzlbrPQ++Z7eHM62/ddqR5g9/+MN48pOfLMt3s0XBC1/4Qtx3333o6urCf/zHf5yREAaDwWAwGAwGg8FgODv853/+J9785jdLI5EGM41b2nA7duxAT88DtxH8+te/xgte8AK8733vw9Of/nR85StfwbOe9Szccsst2Lx58zF/+81vflP2OGYF6uOhwcxCXD/60Y/QbDZlJetXvvKV8vPIW9/6VmlwH83jH/94POxhDzvmHI1uytriVBw/e/fula+PfexjZdup9vZ2nC1O2504NDSE22+/XZboftOb3oSLLroI73//+3HrrbeecAAMBoPBYDAYDAaDwfDQ8ZGPfASveMUrpNHKYCeNZ1aU/pd/+ZcT/v3HP/5x2SHpbW97m+xf/J73vAcXX3wxPvWpTx3zd6xh9brXvQ5f/vKX5Xado7nnnnvw/e9/X6ZG01B/5CMfiU9+8pP46le/KtsTE6ZM9/X1LR7j4+PYvn27jEg/YNveUX/X29t7yv/2n/70p2fVYD6jSDNzwdkMml4EHkf3bubvHvWoR51VAQ0Gg8FgMBgMBoMhCnTok3y6399oNHDzzTfjL//yLxfPcevFE57wBFx33XUn/G94npHpo2Fk+lvf+tbie9ayeNGLXiQN602bNp3wM5iSzU5LLfid/O4bbrhBdls6HhrYa9euxZVXXnnMeaZ0L1++XH4njferr776hN/ZgrLT0Gea9/H/jhM5FM650cxwN0Pux0eV5+fn5e9OVCTMYDAYDAaDwWAwGAxnTqFQOOY9W8OdqD3c1NSUtMmOj87y/b333nvCzx4bGzvh3/N8iw984ANwXRevf/3rcSL4t8fbiPz7jo6OYz6nBbf6MmL9F3/xF8ecX7dunYyIb926VdqYf//3fy+DtnfffbfMej4RzHpmOnjr55Nxpvv7T9toPlnDdva9OnoDt+H8YKYITBZU646hDlVRWxfCaglBcUYVL8l3wY5F346lRa0hMFsO5c9taRvJmD4FOIoVgd1HAtk/erjHRnebPkVfhN+AX5iW1WXtVA5OSpNeMfTa+gIHJoC6D3TlgN42fca0GQjsPixQbQAdWY6rPsVywlBg/6Tq9Z5NAsu69Kl8K/vSFmcQNmqy1ZST69Cm6BZlmy0Led08F+jK6lXYateBOg5PNJFK2NiyNqFF7+0WfqWIsFZmWAVetkMW3tKFmaLA+LyqAL2sE0jG9RnTQhUYn1P1hvrbgHT0XcQWCRt1+OU52fWB/a5lqzhNYP/5sXnOdUBHhu0SLa3WInvG2N9a6ay+Dn1kM5wZy5YtO+Y9CzGzsNZDwc033yxTuLnH+Wzpce6NLhaLeMlLXnLM+csvv1weLWgwM2X8s5/9rIwmnywl+0Q/ny1OWZM8+9nPlq+8SP/n//yfY7wa9GTccccd8h9kOH84NA3cdB8VguqpvisNPGoje61GLRnkQrex/06IRl3KZk+mEVt5IexE9I6bUi3EjsM+asrZhbgXYP2Ai0wy+kXlbDHEN3/ZxHRBtcPiouiqh3tY3hd9JdewWUd9/10IKiU5plzoxgfXws13a2EwX3OHWlASzwEetkZgzYClhcH8/ZsC7B9X7VhY6f5h62xcslaDMQ0Ffn0vsG8Cso0Ybb71Q8C2VSd2vj7URml9bC/82fEFZzDglnoQH1gVuWzkwFSIg1OhvG5kKmthw6Ajq8tHzbW3lvGdnxfQaAr5rN68PY7/86x2LQzn5vw0amP7IEJfvZ+bRHJoraweHDWHZ9TzUG+o9zvSwGO3CGQS0Y/pdBG4biek443cl1AtJ/OpqCUDgnoF1UP3SecWsdwYkoOrZcu4qKnUBX6zByhW1fuYB1wwLNCTj35Mq3WB794YYnxOyD7N7ITyqC021g5F/5wuNYTFI2KdtfD1Bw8elAWyWpwoykxYoJkdDrhf+Gj4nvuDT0Rrf/HJ/v6Xv/wlJiYmMDw8fIwN+Ja3vEUWGdu3b5/8W/7N0XALLytqn+h7mZrNomO/bb8y906zjtauXbsQFadsNOfzefnKxQX7MSeT93v52CT64Q9/uNxsbjg/4OL7zv2qR3Mupd7PloDdY8CW5VFLB/hje6TBbC0YyWGtBH/yAGLLNkQtGg5PB9K724ogUHEdmg6wXgNFdct9ASbnBLryKpowXQCuvSvQwmj2Z8ekwWzJCIIF0ayiMbYXTq4rciNmz5gymDMJFSEq1YDb9gIjPUK2UoqSvaNCGsyZpOoRXqwK3LorxPplNjLJaGUbm1MGc8JTi0kuyO87AqzqA9rvb50YCYxE+nMTsvUPDSr2avbnJ+G1dctIVpRw/jg8E8pnNBWz4AcCMyV1dOWiHdNKLcSPrivKn/u6XGk433egjtvvreGyrdFaWEKEqE8ekq8WM4+YSVAtoTk/iXjnAyu8PtTcvlc5oVs6da4M7DwMXLwqasmAew6p55PZIKRQAXYcAS5dHbVkQGN6DGG9tqAbANGooj51BO7wuqhFw4FpFaGnbuCTWa4DO0eBnminEMm9h4Q0mOn4YBex+TJww70hVg+wtVj0Rr3hzKDBfLTRfDJom23btg0/+clPZAVswr3BfP/a1772hP8NI7v8/Rvf+MbFc6yA3Yr4vuhFL5L7k4/f88zzLDbW+oy5uTkZleb3k2uuuUZ+NwuDHV/pmhHh/+//+/9+67+Hxvmdd975oO2NWwHeU4GVtc+Z0fyv//qv8pW9vlgq3KRin98wusFIKdMCj24PJ6POGiCaddXrckEwYdnynA7Q0UCF1JKNyorndKBcFdLoaynMmCdQqi6EsiJG+OrmWkyPtV1pyECEDDtHKlvrvm/1K4+5gB+oceUzEiW1hgwiLMrGaEKtrs7TkI76ujFlsXWNeN3KNT3mERE01b3VikCyb67fUPdcxDSDY68bn1neazwfNdVaKA3lllMwxl70gnOL2o4SKWEIEQaqn/qC4uKzocOYMuBAo5RZKkfr1FZGUtRUm+o+a8nFnzmH6LJth4p0Ud/bjjqnAZzLKFXLBuU8zHMn28r4UNIaP8dRcsQ9gUag5pF49D78JQavYdSOhtP/fhbDYtozi3JdeumlMhpcLpcXDdwXv/jFGBwclC2myBve8AY8+tGPlu2Fr7rqKlnx+je/+Q3+6Z/+Sf6+s7NTHsdHgBlB5h5kwhRqVuBmIJXVurnHmEb6H/3RHz2gPRX3LPf39+OpT33qA2R/97vfLQOyq1evlkb4hz70Iezfvx9/8id/8lsDvOeK017uMXfecP5DhdmZYTqZ+pn7X6kU2iKODrWw0m0Ipw8BvqNc9kLAjjg61CKbtFCocA+RSl2kA4LndKC/08aOgyEqNSGNeaYJ0uusA3YiIxcZ0vmxsChys51yARw1jIryOaDBR0OG+0y780BSgz3+XXlLLsSZHpjwhHztyFoymhU1bWkgHluQLaauWzquIm1RY8dTMs1TNGuAE5NGtOV68nzU8FrFPQvVhkDMFdJBw/svrUEabz7joKvNxaFxZS3U6gKeZ2GgR4N9w8waiCcRlAvKYKbnwbLgaDCmnNu6c2p/P8eSeoE2FffA6gD1PbPJWg4tytcZffazxE5mgPK8cqzyogUBnJweF45RXK6TeN24RuLe4c52Peo2cPwoU7kmpDHP+Xeo05LOS8PvB89//vMxOTmJv/mbv5FFuC688ELZDqqVCn3gwAFZ1QPYhHcAAQAASURBVLoFt9myl/I73vEO/NVf/RXWrFkjK2cf36P5t8HCXjSU2XuZn/+c5zwHn/jEJ475G0aev/CFL8gtv0wjP57Z2VlpeFNuto5i1Jp9pNk667cFeM8VZ/TofP3rX8fXvvY1ebFZ0vxouDnccH7AlDF6JGdZTwXAmgFgpSatuGMDq9HwGwhLs9L75nQOwu2+f49FlAx1OrIwyFxZ7SPqzFhY1hW94UcuWuNgpiCw82AgF+IrB2w85sLo9/oRt70XYb0Cf3YUCH046TY5zjrA4lVbR4DtB9WiiIXArlh/f8Q+SgY6LTxik4Mb7w3kwq0za+FxFzkqAhgx7RkLl64RuHm3ipRmE8Bl61gXIXrZWPgr3r8KdW718GkwxxDvG5FGV9R4joW1Aw7uOxKg7qsF7/JuBzkNnG+ua+H5T2nDV78/h6k5X24JeOylGawbib7YBQ2VRN8IaqN7ENSr8r3X3g83d2xkJCouWa2eg6mCMmbW9iu9qgOblqn0bBb+JMNdwFpNZIt39ktnql+ck6n3brYd8e4TV899qBnuVNt1jswCfqgM1Y2D0II1gxZmSxbu2ifAgsJ97RYec8H9EXvD7wc0Xk+Wjv2zn/3sAeee97znyeNU2bdv3wPOsVI2je8Hg8Y092efjI9+9KPy0InTNprpKfjrv/5r6Rn47//+bxni3717N2666Sa85jWvOTdSGiKBe3Qeu1nt0XFtVvmENlheXBb+4t4mep4tL6GNInDlgteVVZZJXKa46yPbkx7m4vJNLkIhkEvps7eJ1yjevxJe16BMs7RicW0qGVM2Gs1rBoRc9Kq9zXpcN7JlhY3Vg5ZMy86klNGlCyt6LQx2qtTUVEztu9YFLr6d1IUyq4FRZp2qLLMC70Ur6YBT2Q06jelgr4fX/3EX5goBkgkLmZQeTkHCqsqp5RtktWXL4X51DdJBFqCz6HFbhNSpjDYzU0UX3cBtHSz8xWgkYcFPTUST2UaJ/pUQ3dyLIqT+1+W6UX9uGhJY3QsEQo2prYlsvEaXrXewZUTpLe5Xb6VqG87/Ps2/j1x88cVyTzYj0ywa9mDzxJkEeU97hfAP//APMrf9BS94gQyrv/3tb8fKlStl6J+V0QznF8zaaBUG0Q0+DJYGaXcnjXjoEcA9oWw5WZJAzwmYEUBdYeswHVKyl5psMVfflEAaVpaj5yRHx4wO3QpOBJ0f3R16DiqdbTq1JDreyNJXp+rVZuoB+l5T3SD1vaZzL0klLOi5UjIYzi7PfOYzF6uJt4qfnU1OW+MxJbvVWooVtNlbi7ByGjdsf+pTnzrrQhoMBoPBYDAYDAaDwfDb6m6dixpcp200s0IaI8rLly+Xfbquv/56XHDBBbJsOKsFGgwGg8FgMBgMBsN5AQsLRp12H/X3L1FY/fuee+6RP7OIWKsN1kNiND/ucY+T/bSYK879zG9605tkYTAKdTr9sQwGg8FgMBgMBoPBYDibHDp0SG4lvvbaa9HW1ibPsXUVs6XZSmtoaOjcG83cz8wy4YSFv9iviyXA/+AP/gB/+qd/etoCGAwGg8FgMBgMBoPBcDZgP2f2iGaUudVDeseOHTLgy9+x9dY5N5pZIvzonl5sVs3DcH5SqQsUqgIsuMj2May+rAthvSpbTrEIh53rlC1jdIEtp6ZL6mf24Uxo0P6nRRCEKNV8uZ0ilXARYz8bTRCBj6A4A4QB7FQOdkJWLNOCMBSYr4QIQoFU3JaHTrIdmQFqTSCfBrpz+txvvM9KVbY4CxHzeN0cbSrfUjYxOw5RLcJKpGF19GsjG/GrJYSNGizHg5vOaSXbTFHIdoQs8DbQoVc1+bAwg3B2QvXd7h2Wr7rA3tuFiljo0ayXTmXl7FbLqd426i1oQzMQKFYCdnFENmkjxpYemsAe70FhBhAh7HRei17vR+uGaq0uX+NxDzGW4jecPnxgo55/o/7+JcbPf/5zGdRtGcyEP3/yk5/ElVdeeUafeUZPD8PbN954IyYmJhajzi1e/OIXn5EgBv2YLYfYcVj1fmWh5XzSwqZljhYtY8LyPBp774BoVKRwVjKL+KoLYcWiL0targncul/I3o0kEwcuHGGbouivW9MPcXCihFojkA0MPNfBYHcaKfbFihj2yq3vuxNBaU6+t7wY4sMb4GSj77FKQ3n3WAPz5VAu2rjQHen20JGN3uHAxdC19wL7J2SnLtmeaNtqgbUDlhZG6ehMDbOFhrxuNPq68zH0tCe0kC3YfTuCfXcBgc9eLLCH1sNdd4kWxml9dgK1qcMQYSAXS7FsB5J9I1rItndc4Kb7INvq0VYe6gQeuUFo0c4mGNuH5i0/hahX5HWzuwcRu/QpWlRenq8I3HOIOlXVf8mmLGxe5sjq8lEzVwau2wkUq+p9PgU8Yr1qrxc19WaI3aMNVBtqvRn3LKzsi2vhuGT/6PreOxBW5qlSZavE2PLNcDLtUYuGIAwxMTWHaq0h3zu2ja7OHNJJDQbVYDjHLFu2TEaajycIAgwMnFkT+tNeKX/729/GH//xH6NUKiGXO9bzzZ+N0Xx+wAXlnrFQGszsrcoab1T4R2ZDLO+O3lBoHtmlFkWMRDJaVCmgOb4fsWXroxYNuyeUwZxeWKOV6sDucYELlke/MJop1FBt+Ih7XGxYaDQDTM5WsbwvG7Vo8KePIGDmAB0fli0dIo3Du5BY1xG5oTBdDKTBTIOURgIXvQenm2hLM/MmWtkOTimDmS1PPAfy3rttD7CsUyAZj1a2ci3AbLEhr5Hr2NJpM1VoIJ/2EI9FO4+I0iyC/dtlXz0r2SZ7voeHdkD0LYfV1hOpbGGzgdr0ETm32V5CZl40CzPwsu3wMmpvVlQ0fIFb9wB+qAwrPwAOTgP7JoFVfZGKBhGGaN55rRxLZNvldQsnDiHYfw/c1RdGKxyAPePKYG7pVEacD8+EWNETvU69+6AymGU7LKGM6HsPA5esiloyYGzWR6URLmZs8RoemWlidX/0jhB/6hDC8pxqf8liUbWKXJ84ax8WtWgolarSYHbYVs8CfD/AzGwRqYQ+fa6XCqZP89LjQx/6EF73utfh05/+NC655BJ5jvW33vCGN+Dv//7vHxqj+S1veQte9rKX4eqrr0YqpU8KiuHsIhYWR1yEy/6I8lkVKuqsAXJR5Lhq4m9VNeQ5Dag2INPZW7aUY6tzOtAMwoUsIyUcjRkaMjog/Lp8teyFBaTtQfgNmfIGK9pFZdMX8plopaC6joAfCGk4xCIOdvDeCoVKkyU0nusNlaqdjHhNyZRsGf1eWOwyEsn7jamWkS9361Ug4EVacBjROG3MQ/B8xIR+U6YNMC1bPquOizBoqvMRQx3Q8BntU9mCMtuzrskcx+vTqAGxxOJ143NLQ0YHRzSfSWYVt3Qql+GtqHPUlOtKNjnF8dLZKl1bB7gWsY/WW5ZAQ5PrFjZ4kSxYrW2LrgvRqMnxjtow9RcyQVuOXW6tZPSZ2Uk6ZIUYDGeb9vb2Y567crmMyy67DK6rFki+78ufaceeSR/n0zaaDx8+jNe//vXGYD7PsS0LqbiF2bKAYwu5KCfpiCNXLbjfNZgZlXtgpcuePrjW4jdickmmttNAlWsPBKE6pwNxekGEhSBQe+qYdpxORB/lINJTz2UkDWXbgQgaMsVt0YiOkETMlteLxjMXkxzbVIzR06glA3Kp+xe4NJzLNZVSmU7ocb9JQzkQckFOpw0jztzbHDVWKgcwZbdWguC9R6ebF1PnI8amHDSU/TpsNy7nOcuy4cSiH1RGSVNxRkmVgcVngfYCo86Rw+uWyUPMjEE4rkq7t2xYEUfnCRdyfC6ni4AbHq1To38WSHv6fr1FlUr5WB9BB5IxC/MVplUy/xngC9cnOmAn05CbneiwYYaU34STa4vcYCaxBUOBdUwoDw3mRMyLPDvKYDhXfOxjH8O55LSN5ic/+ckyvL1y5cpzI5FBG9b0O7jncIBKTRlYvW0W+tv1mGy9wTXSmxtWClKJ2vleuL0j0IFVvRbKdVUkh3Rl1Tkd6Mgl5H7mcrUpIzDcy9zboYdF73YMIKwUEcxPyJxPO5lFbHAtdKAjY6NcczBZCOSikou4kd6YdC5FTX87sGU5sP0gUK0rg/nh67hgil62ZNxBX3sC47M1GXWmwdzfmdCiiI+VysJdfyn8HTepqLPrwV1zMWym9UaM7XpI9i5HdXw/wqABy3IQ7+iDk8xELZp0gjx8ncB196roJB02G4bUvuaooWHgXfgYNH7zY4jynMxQcUY2wBmOftsOWd3noOkHsu4Fp46evIWBjuifU7J5WDncWMCSEg20AxsGoQV97R5qDYFileYpndAOBjr1KPzpdg1JvRUWJiGEDzuVhzekh97KpBOoNxoollXkm0XAujr0Kii4VBAa9GmO+vuXAi95yUv0MpqvuuoqvO1tb8P27duxZcsWeN6x5RXZespwfkBP7gXLHRnB4sKI0QVdJlvue42t2QZRpYZnIbCMjMToAI2Vi0fU3lJCI0YXzy7Ti4e606g3AxlNYCRQF9mY3sY96aJ7mSx+xMrZjLbpAO/7ZV0euvOuzBzg3jpdqt5Stq0jwIpeAdZ74Z7EREwP2UhHLoZMyoXvc0+4LQ9dcPpXwm7vg6iVZKYD5xFd8DJ5OIkNCJt12I4HOxZ5QvsiNPaecjE7KwCsIch7ThfdYOe7EH/UsyGKs9IRYmWPTdeLEjrbLhhxpLOB0y7rXugjG3DlRtYuUWn3+aTKINABzrWr+mOy8jj1Fms16OCwJMyEii3fBFEryz30ViIDy3G00Q2d7TnksmlZtNfzXFkMzGD4faNWq6HROHYPEetynS6nvSJ9xSteIV/f/e53P+B3Mv0jUJ5Aw/kBlRXTP3WEyspK56EjNES1vW6WhURrA6xmyL1+Ghkux8vGRa+uZJOWKuKjIYws6xBdPhFWIiUPHWHEmYeOsIJxt56iycr7VkcvdISOS1226xwPneNskajr/KtLSvZS01umzZTh95FyuYw///M/x9e+9jVMT08/4PdnYq+e9iqG3qqTHcZgNhgMBoPBYDAYDOdb9eyoD8Op8/a3vx3XXHMNPvOZzyAej+Nzn/sc3vWud8l2U//2b/+GM8G4nwwGg8FgMBgMBoPBcF7w7W9/WxrHj3nMY/DSl74UV155JVavXo3ly5fjy1/+smyffE6M5k984hN45StfiUQiIX9+MFhZ22AwGAwGg8FgMBgMhoeamZmZxaLV3L/M9+SRj3wkXv3qV5/RZ56S0fzRj35UWuQ0mvnzg+2dMEazwWAwGAwGg8FgOD9YaGAeuQyGU4UG8969ezE8PIz169fLvc2XXnqpjEC3tbWdO6OZX3qinw3nP6wIGc5PySrGVnufNlUhdUc06whnxuXPdkcvLPaD1QRWpg4qJQgRyhY2OhUaokzNWhUiDOHGE3A0ky0szQF+U7YrsmVfaT1gOxExOw5Rr8pqwbYGfWmPka0wLecS9kDWoaXT0VTqIWpNgbhrIZ3Qq1hZMDul5t9kGk7PkDaVlklYmocoTAFeAnZXvzbdC4jv+2g2GrIiP/eyaXXdqiWEU6OA46ox1WiOYx96VkQn7LutS4cA4gcCcxXVQ7otBXgatNRrEYYCxVooZWPfbZ1k4/xbqzekjPGYB9c1azjD7wcvfelLcfvtt+PRj340/uIv/gLPeMYz8KlPfQrNZhMf+chHzugzzZ5mw0kJ5ybQvO1ngOyFbMPqGoR34WNlZVLDyRGVIhq/+RHE3JR8b+U7EbvkibDSp1/e/mwT+k1UD+9CUC2ysgXseALJgdVwNKgeHIYBihOH0ayUISDguDFkewbgaSAbHQ3NfXcimB2noLBiCXgjm+Hku7VYFPl3/grBgXuBwAco29Yr4Qyu1kK2YNetCPbdDQRNwI3DXbtNm765Y7M+Dk/78ENVObi/3cFAh6uFkdXYeTtq1/0AolYFPA+xDduQePiTtJAtOLIHzVuugahV2LgZztBaeNseJzsa6NBahGl4ge/L98lkEh2dnbA1aLUTzIyj9vP/hijMyH5OTu8wEo9+Jqx49OW0K3WBuw6Gi60SWeF787At2+tFDZ1at+8XmC9LtSW7BFywnAaqpYUxv3usIXtI02hOxGys7I1p4YCjoTw5PYdKrS5lo8Hc3ZFHMmHWcKeLDoW4ov7+pcab3vSmxZ+f8IQn4J577sEtt9wi9zVv3br13BnNb37zm0/5A8/Uejfoh7/9eohKQbV1CgOIyYMIDtwDd9UFUYumNc2dN0PMTgAL7bDE3KQ8F7vosVGLhsbMGIJyARZ7vloWwnoVtYmDSA+vi1o01ApzaFRKcBxPyhY0GyhNjaFtcEXkhkIwM4pgZgxwY+yfJI2F5oHtsDc9MnJDIRzfj2D/PUq2ZBaozMO/69ewuwYjX4yLuQllMNNgSbQBtTL8+26B3TUgo85RUm2EODzjy0U4e9Q2A2B0NkA+7SCTiPZ+CytF1G74EdBswG7vlIZz4+7fwBtaBXdZtM4Q4TfQvP0XMqsBzBpo1hEc2CENQCfieYROmrnZWWkwu64r31erVdl6JJvNImrqN/0EYWEadq5DOt+C0b1o3nszYhc8MmrRsGc8RLEKpGLKMGW/5v2TAusGol+o750QmC0r2SgNo+H3jQpcOBK9bBPzPgqVADGXvaOBWiPEwakm1g9Fn11WLFdRrtbhOLbUob4fYGq2gKG+zsh1qsHwUDMyMiKP34VTMppvvfXWY97TUmf607p1SkHu3LkTjuNg27Ztv5MwBn1gZE1Ui9K4YoobF72M/olqKWrR9Kc0L1PvWqnswnEhinPQgbBRlwZpy9ATtgvRWMjH0yAKztWavN+Y1u44CBktoos8YgUvr5EQi6mUwotBNBuA3wBiERumlaJ0arUMZBFPS3llOnTURnO1BBE0YSXa5CJNUB6eq6pU7SipNwWCEEhIH40FzxGoNoBGk+GiSEWDKBUgGnXY6axMe2Z6dlCtyJToqKEBL+o1IJ5SC+9YQjqR5H2oQ2ZDEMiosuydS+dbECxGnaOWjRFmZqnI+dd2ZOQo1EQ3VBoq26IVkKcByOizDpTrSh7KR9jyned0oN4MF/tvq1dGxpmqLSI3TP2FNrCtLAu+8nlgBNrRKPXeYDhX/OQnP5G1uBhlJhs2bMAb3/hGGXk+E04pf+SnP/3p4sGccOaHHzp0SBrPPA4ePIjHPvaxuOqqq85ICIN+UKlzUSsaNWVA0ziAJfdyGh4c7illmqxYOPizldNjH6cTpzUg1JiGoZTN1iA1kDgerReVps0FR8jFrzwXvXK3Yikph/Cbcm8z/Lrap87obtSy8ZnkAryu9oIzmiudXcmMFrJZzByoVxZkq8hrRiMwauKeJRe4DemXEWj6asHL81FjZfKwY0mE5aK8bmGlxNxK2Nno96pbiRQsziMLYyojzjTsNdh+QiPFcV0ElItzCO85Gllu9DvRpBGf75IOBxEEyukGoc0e/1Qc0onUOkK5Pzf6Z4Gk4zIwvyibHwCZiB1bLeKeWkYHoUBIp00okPSU0yZqvAXHPZ8D6VAKA7iOA3vBwDecOoL6X4PDcOr8wz/8A57ylKfILKM3vOEN8mAV7ac97Wn49Kc/jTPhtDddfPjDH8b73vc+tLffP9Hz57/7u7+TvzOcP7gbL4edbgMYQajXYMv0Oz32IuqMu/Zi2B19MqLGw27vhbdWjyyMWEcfnHReGX/NOuxECvGeYehAItuOWDqrCpX5TTixGDKdfVosPpzOfjidA2rPMItteQl4yzdGnppN7N7lcFZsVrKV54FYHO7mR8qIVtRY+W44Kxf2DrE2guPAXXdJ5FFmkozZGOrk/mVGhtS5gQ5Hi72IdiqDxOVPkmMYzs8AQYDY5ofDGVTtM6KE2RbeRY+BlUir+43P6sgG2IOrohZNzhVcj3iuK7PhaDwnUymk0tE7aUj8ksfBae+CKM3JbAtncBW8DXrohlW9NnIpRk7V0ZYGlndHP/eSlT0WOjLqOeXBImVr+vSQrSfvyi0d3NvMLBXOK8u69SjulskkkU4lZGSZqdl8Lro6clroVIPhXHP11VfLKPN//Md/yM5OPL7yla/Ic/zdmXDa7tdCoYDJyckHnOe5YjH69CzD2cPOd8F7+NNl5Vsudq22Hi2MBN1hhM+7/GkIua+Z17G9B5YGEUnCKuipobUIaNALASeZlud0gGnZuZ4h+AsRUzeWgK1BhIgwRdYb2QK3exmE70ujxoo4LbsFF0Dupstl4S+mZduZdi2ifouyrboAds8yFWVm1fGFvf460NvmIpeyZao29ySm4tEbzC281Zvh9AwgmJuGnUwvVKjWY7Hr9I3AfuzzEM5Py6iz1d6rjWyslt3T24tGoyHTUWOxmDayOR09SD75jxFMj8l51+4e0Gb+TcYsXDRiH1M9u5VyHDXM/ti2EovVsymbp0l6MSuMr+6LoVwPZTQ8lbC1kc22LPR05lFvNKXhHIu5MtJsMPw+MDc3JyPNx/OkJz0Jf/7nf35Gn3nas/Uf/uEfyjLejCqz3xW54YYb8La3vQ3Pfvazz0gIg75wT6TVPRS1GEsOGsmOpteNxqmriVF1PHJvqQbVsk+aXpnRI5XyhLK190BX7GwHwENDGBliITAdYcEoWTRKQ5h672i6XYc1Vlg1W0eY3u5qkDFwMgOQEV0doQHfqalsTHfOJh1tdUMirukEt6QwfZqXGn/wB3+Ab37zm9I+PZr//u//xtOf/vSHxmj+x3/8R7z1rW/FC1/4QtnrSn6I6+LlL385PvShD52REAaDwWAwGAwGg8FgMJwJn/jEJxZ/3rhxI9773vfiZz/7GS6//HJ57vrrr8e1116Lt7zlLefeaGbVvd/85jdSCBrIu3fvludXrVqFtCZ7hgwGg8FgMBgMBoPB8PvDRz/60WPes8bF9u3b5dGira0N//Iv/4J3vOMd59ZoZsoTc8FZunvFihVn3BzaYDAYDAaDwWAwGHRHh+rVUX//UmDv3r3n9PNPu+rJ5s2bsWfPnnMjjcFgMBgMBoPBYDAYDGcBtlzj8ZAbzWwtxT3N3/nOdzA6OiqraR99GM4vWFV2dEZgcp49L3/3G+5swt6DtXoD9XrjrDwMZ5OmL3Bw3JcHf9YJjuPEnMDYrEBDM9k4jjMlgYl5PWWrNQKUaz58NgvVTLb5isD4nEC1rtd1I7PFEAcmAhTK+skW1qsIijMIWd1bMyp19ZzOa3jdyjWBA+MBJudUD1idYEu9oDwvuwToJhtbE81VBApV1ddXJ9gyad/hBvYfacDXbf4NA4TzUwjnJiHYXk8jZC/10izC4rS893SC9z/vtdkSe9HrNaZLCT6qOhyG0+Pf/u3fsGXLFlkYkgczpP/93/8dZ8ppFwJjU+hWVbKj2zjwweR77ns2nB/MFAV+fGuIuTJbFwAjvcBjttqywmbUsAfn5NQsGgvF6BKJOLo722WLkagpVUJ8/ZqKXFACAst6XTz3cSlkU7YWTpCf3h7i0BRAH0hnDnjChTby6ejHNAgErr8POEjZQsieoY9YL9CeiV42zm9jM1XMleigATzXwmBXGqmEq4Vsd+wD7j3MBTmrQQOXrRUY7Iz+upGbd/i49i5fOkESMQuPv9jDhuV6VJr1pw7DP3gvRNCUrX/cwbVwNelbfmBS4Lp7gWqDVY2BLcsFto6oarhRs38swHevq6NYUbJdsNrB47bFZIubqAmqRdQP7YCo11jWGE6uC/GB1bJrgA5OkLsPhdLhwEvVmbGwflAPnTpXCPDv357BgbGmrNG7clkc//vpbcikon9WRaOO5t2/gpgdlzrVynXC23yl6hUetWyBj+buWxHOjUurhlXlvdXbYCezWjjItx8SGJ1T+j4VB7YOs2VX9PebwXCu+chHPoL/+3//L1772tfiEY94hDz3q1/9Cq961aswNTWFN73pTaf9mae94vvpT3962l9iWJr86u4Q0wUglwYYWNt1BOhpE9i6IvoJd3auICPMLldsAKrVGuYLJbS3Rd9K6ee31rD7sI+2rA0LFvYc9vHTm2v4gyujb6V05z6BveNAOiHXk5iYBa67N8RTtkW/MNo1BuwbBxIxwPEgnTU33gc8+aKoJQMK5SZmi+z7ytoOFhrNEEemK1g1kI3ciBmdBbYfVOPJcS3X1HW7KicQ8yKWbTrEL+9sSkdDNgUUKwI/uaWJgS4L+XS0RkxYKyuDOfSBWBKiWYd/aAfsTBvsVLTzSLUhcP0OoFoHMkmg1oR0jPS0Af0Rdz1jtOp/bmhgriTQnrVQbwA37/SxrMfBumE3cgdS48guhPUKLC8JhAH8uXHZGsvr6EfU3DcaolgV0rFFI2aiIJBNCizvjl6nfu+XBew+2EBH3pHP6717avjJ9SU883HR91X3998FMXUYoJFsAWJ2Av7u2+BtUgvhKAnG9yKcOQJ4CfZzhCjNwd93F2IbVLXeKDkyCxyaBWIOELOBUg24+5DA5Wv0cL4ZDOeST37yk/jMZz6DF7/4xYvnGPDdtGkT3vnOdz40RvOjH/3o0/4Sw9IjCJnOAyTiKpLAg4txGjI60Gg0ZVS5FVm2gnCxBVrUjE2HMhIZXzBYYq6FselAm+wB6sq4p97HPEjHiA4Uq4wh3C8bX3mO6YxRR2Iafihlcx11v/HV90MpG8c6SniN6NSicUWScWVkletqfKNktiikUdWZV4u0XEqlGtPgykccJBL1iowwg73oudiNJSBqZQimaUdsNJeqKsJMJwhvuXQcmC0DhUr0RnOpKlCuCmRTlnwu3SRQmReYKWiQO8gU3kYNlhNTkWXbhggaEI1a1JJJg75UF/Ac1XPYWUiHZvRZB45MNBGPWYjTupKtREOMTuqhU0VpVo6l5aoJTbguRHEWOhBWS/L1ftliEJXCYvZllFAHUHHFFlb6cVfNK8xI8qJPklpi8LmIOlsl6u9fWnAL8RVXXPGA8zzH3z1kIzA3N4cPf/jD+JM/+RN5sMT3/Pz8GQlg0BOmY2cSkAteep05yRKe0wH2Bg+F2ksnDwh5Tgc6craMxtDxwINpqR256CO5JJe0IEKmQqtIR8NXadA6QGOP8F7jPddoqnMLdmqk0DiwoMaT91sQhmrhywclYhi14tqs3lTXrdaAXJgzYh81mZRanDFiyuumDHkL2WT0181iZMh2gOZCTQS/Id9bsYUbMUKYRskx5AKXonFs+RzwfNSkEjSsgEpNPQv1hpDpvBkdUj45fm4MCJjdIOQ+WD658lzE0IBKxiw05fymaoRIJ2FMg+sGoLPNldt3OMfRGcg9zR15PXSqlczKfcMcT0G97/uwkhnogB1LyodUySbUVo94KnKDmSQWnKZ+uKBTA2VALyToGQznNatXr8bXvva1B5z/z//8T6xZs+aMPvO0Z0T2aX7yk58sN1Rfeumli3nj7N38wx/+EBdffPEZCWLQC074l2+wcc3tIeZKalE+2AlsHI5eERCmYftTLLKlCoLEYx5yWT2U6KMuiuPIVIDxGeVp6Ot08JiLNVjtAtiywsKRGRbaUh7ofAq4bJ0GVimANX3A6AwwPqcizjQQtq3SI40sn46hWGmiVG2CtVQYae7tSMLWwGge6gRW9AL7JpSRxWjChStoTEcv27JuGxeudnDbrgAVGswucPkmRzqWosZOZeH2rYQ/uhuolQDbhdu7HFa6LWrRkE5YuHiVwG92qewexwFW96uxjhpm0Dzu4hh+eFMDU/NCbgtYP+xqsU+dc0WsbyUah3fKAm+cOpxMO9z2XujAql4b2w8F0nlE2dpSFoY6on9OyVMemcX4dBPj075MgR7q8/CEy/XQqe7yTRDzUxDFGam47HQezqoLoQNO3wqEhUmElE0AdiIl5dWBwQ5gsghMF5XRzOyt9QOWFjrVYDjXvOtd78Lzn/98/OIXv1jc03zttdfiJz/5yQmN6XNiNDMHnDnh//zP/7wY2WNRJkac3/jGN0rhDOcHA50W/uDhtjRiXBsY6lIFkHQgFvPQ29Ml9zVTwbMQmKNBoRfSmXfwf65KY++oMuhH+l1kknrIxsX4VZfaODytim31tTOtV5Mx9Sw8ZjOrBSvPeGcWWkQkCY3joZ40ylVfRmKSMQdxbhTTRLaHrxMY6VERSTpCOrJ6XDcuzh57kYfVg47cy9mesTHQpcezQNz+lXByHcrAiiVgZ9q1WVCuH7LQlWM6u8oaGOjQw4FENo646G6zMT4bSufMSL+tRdYFcbPtsFdsUWmztgMn0waLGQUawAJMF404mK8Kmc3VnlYp7jrQ3+3hz/6oC3sOKZ26ZjiOtCZ6i1Fl7+InIJwdk9af3dYjo7k6YHlxeOsfLit7U6na2XZtZOO9ddGIMpqpU/NJtQYwnD505Ee9kSLq719qPOc5z8GNN94oA7vf+ta35LkNGzbIcxdddNFDF2k+2mCWH+K6ePvb345LLrnkjIQw6EsuxX2I0BIWAXO5oU5DuNjYvDL6lMCTRYpW9kFLqOTpnNERVgbOpiLeJPwgstGo0hEaesO9ehgtJ5LNyrRLY1lHunI0nKElNJp56IgdT8lDR1hBnoeO5DIOLlyvp06lcer0LIeOWI4HR4NCcyeCzqye6Gu5GQwPKaxx9Kd/+qeyevaXvvSls/a5p63xcrkcDhw48IDzBw8eRDYbfYl9g8FgMBgMBoPBYDD8/uF5Hv7f//t/Z/1zT9toZn74y1/+crmRmoYyj69+9asyPfsFL3jBWRfQYDAYDAaDwWAwGKKA5Q51OAynzrOe9azFtOyzxWmnZ//93/+9TGlj3yvuZW5Z9K9+9avx/ve//6wKZzAYDAaDwWAwGAwGw6nCCtnvfve7ZfGvbdu2IZ0+ts/l61//epxzozkWi+HjH/843ve+92H37t3y3KpVq5BK6bl/yGAwGAwGg8FgMBjOBB0ivVF//1Lj85//PNra2nDzzTfL42gY/H1IjOYWNJLb21XxFGMwn78EgcB8RfUMzST1qd5KQiFkT1qKxH6EOsnGfo1ssUPYa5iFmnSSrdX/VUfZWAE6CFX/YR1aOh1N6DcgggC2F4elSbX2FkEQyIOFGW3NZCtXQxQrAm0ZG4m4XmMq2Ke5oapns9iQTojAR9isw3I92Br0Gj4aVpHn/MuerywuqBN8RkWtBMtxAU165raQ/YYbVam4rFhSO51K3WAt9n/XTTeo3tYJT6+2SbLvdlMpVTsWg2XZWsnW8FX1bK6TdKlybzCca/bu3XvWP/O0jeYwDPF3f/d3+PCHP4xSqSTPsQDYW97yFvz1X/+1dos1w5lTqAj89I4Q0wUaVsDqAQtXbNDDkGn4AjuPBChWQ6k82zMWVvc5WiiEpi9w50Eh2zyQjgywZZg9ai0tFrr3HFZ9mrn4YHuizcvUIkSHBduesRDj86E06FMJCxsGHCQ1MLK48KhNj6HOXqEihOPFkeodhpvQw2FYLBZRmJ+XcjqOg47OTsTjehiAt+1s4AfXV+WCl+1OnnFlCmuH9ahCHkwfgb/3DsBvAI4Hd2QznO5l0AG/PIf64V0QlM12EO8ZhqdJhV62D+P8W22q1klDHTaWddlaGDJhpQD/****************************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", "text/plain": ["<Figure size 1000x800 with 2 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAA94AAAMWCAYAAAAH1l7yAAAAOnRFWHRTb2Z0d2FyZQBNYXRwbG90bGliIHZlcnNpb24zLjEwLjMsIGh0dHBzOi8vbWF0cGxvdGxpYi5vcmcvZiW1igAAAAlwSFlzAAAPYQAAD2EBqD+naQAAmapJREFUeJzt3Qd4ZGXZxvEnC7sbCCxVygqEokJosoII4lKkCSggqCgIiMgqTRYUEUWqdJG1ALqAgJ8iiIIKIr2pgCBFECJICSBLEZGygzNsOd91v2GGSTaTbTkzz3Py/11XNnsmk5n3zpmTzHPOW9qyLMsMAAAAAADkYkQ+DwsAAAAAAITCGwAAAACAHFF4AwAAAACQIwpvAAAAAAByROENAAAAAECOKLwBAAAAAMgRhTcAAAAAADmi8AYAAAAAIEcU3gAAAAAA5IjCGwCAQZx22mm2xhpr2MyZM1vdlHA+97nP2corr9zntra2Njv22GMtGrVZbX/ppZeG7DH1s/noRz862/vdcsst6bn1eah+tvpePUYerrnmGltkkUXs3//+dy6PDwARUXgDQIFdeOGF6c34X//61wG/vvnmm9vaa689T4999tlnp8f34qSTTrLf/OY3Q/qYr732mp166ql2xBFH2IgRI1Khop/n7D7yKmjqNWqLThLUe+WVV2yPPfawJZZYwlZddVU7//zzZ3ksvT4WXnhhe/LJJ+fqdVX9aG9vt/e85z120EEH2QsvvGDNpAKyvi3LLLOMjR8/3q644gob7m6//fZUiOs10Ewf+chH7F3vepedfPLJTX1eAPBswVY3AAAQkwrvpZdeuilF5pwW3p/4xCds5513HrLH/MlPfmLTp0+3z3zmM2n7i1/8om211Va1r6tQPfroo23ChAmp2KtabbXVrBlGjx5t5513Xp/bFltssT7bX/3qV9OV0uOOO84ee+wx22+//ayrq8s++MEPpq9nWWZf/vKXbeLEibbKKqvM1fMff/zx6XvK5bL96U9/snPOOceuvvpq+/vf/54K+XPPPbcpPQXWW289+8pXvpL+P2XKFPvxj39su+yyS2rPl770JYtu0003tf/97382atSoQe+n+yy44IJ9Cm/tdx2jiy++eJ/7PvLII+lkUl50rOi1p+dfdNFFc3seAIiCwhsAgAYuuOAC23HHHdMVXdl4443TR/2VYhXeuu2zn/1s09unImt2z3vVVVel7vJ77bVX2n7ggQfsyiuvrBXeP//5z+2pp56yb3zjG3P9/Nttt51tsMEG6f9f+MIXbKmllrLvfve79tvf/jadrBg5cqQ1wzvf+c4+Pwdl1RXXM888s2HhrRMqOikwu2LWAxXI1dfgYObkPvUnbfK066672sEHH2yXXXaZff7zn8/1uQAgArqaAwBmKUhOOOGEdNVWb87VlVdFWaVSqd1Htz300EN266231rr4qtu6TJs2LV3leve7350KARVjH/rQh+z666+vfb+uwGkM6BNPPGHbbrutdXR02NixY9MVVF2BrVcqldLVzBVXXDG1Z/XVV7fvfOc7fe6n59f9Lrroolm6e7/++uvpaq7arO9XV+Stt97a7r333kF/DrqarSK1/gr3nFKxsf7669tCCy2UegWoKHz22Wf73GdufgaDmTFjRuoSP9hVUHUzr1pyySXtjTfeSP/Xz+zrX/966hKstsyvD3/4w+lztcv6QOOQB6KfjYqzZZddNu2jtdZaK/U2mFfLLbdcuqpfbUdPT096Teh1M2nSpNpr++GHH05fv+mmm1KPBe0DXRneaaedrLu7e8DH1hjvT33qUzZmzJj02j7kkEPSFf/+J2z0s9BrTc+z5pprpqvvjVx33XXpqr2OF9338ssvn+0Y74HUj/HW58MPPzz9X70SqseFfhaNxnirS7qOleqxppMXGmrRv9fCJZdckl7fupKtn8M666xj3/ve9/rcR9nXXXfddBIGAMAVbwAYFl599dUBJ4VSkdyfrlyqgFW3bRW8f/nLX1JhpkKkOm5WxYuuZqlY++Y3v5luU9FUfcOv++txNtxww1QU6sqwCl0VvPUFo8aCbrTRRumKrCZkOuaYY1Lhr+JTVIDqivPNN99s++67bypOrr322lRQqFjTFU35v//7v9rzqdt3fXdvXfH81a9+lcYfq6j5z3/+k7pFK8/73ve+hj8zddOVwe7TaPzzPvvsY+9///vTz0FjnlWU/PnPf7b77ruvT5ffOfkZDEYFtAoffVZxravMKpTqi2i1Q1ehNfZbRb6eQ13Aq93zdbV4zz33tKHw+OOPp88qSOeUfj7Kr6JQ++gd73iH/eEPf0j7W68dFYJzS6/rZ555ZpZ2qCBWkazXiApLnYS44YYb0pV7jX/Xa1cnKn7wgx/YJptskl6z/U8cqOjWbdq3d955p33/+9+3//73v/bTn/60dh8V2Tp5oNeueiWoh8EBBxyQCtgDDzywz+P985//tN122y29Tvfee+/Uxk9+8pNpP9UfL3NLXe0fffRR+8UvfpGOE50AEv18B6LX0GabbZaOK3UTX2mlldIxcOSRR9pzzz2XjnnRCTS9zrbccsv0WhMdS3p96yREPRXnQz3vAgCElQEACuuCCy7QpdNBP9Zaa63a/e+///502xe+8IU+j/PVr3413X7TTTfVbtP3bbbZZrM853vf+95shx12GLRde++9d3q8gw8+uHbbzJkz0/eNGjUq+/e//51u+81vfpPu9+1vf7vP93/iE5/I2trasscee6x2W0dHR3rc/hZbbLHswAMPzObWUUcdlZ779ddfb3ifu+++O91HP2d58803s2WWWSZbe+21s//973+1+1111VXpfkcfffRc/wwa+frXv54dccQR2aWXXpr94he/qD3eJptskk2bNq12vwceeCBbYYUVavt71113zWbMmJE98cQT2UILLZTdcccd8/y6uuGGG1I7n3nmmeySSy7JllpqqfSY//rXv2oZOzs7+3yvvu+YY46pbe+7777Z8ssvn7300kt97vfpT3867bs33nhj0Lbo8bfZZpvUDn387W9/S99b/7N98skn0/aYMWOyF198sc/3r7feemmf/ec//6ndpscYMWJEttdee9VuU5v1GDvuuGOf7z/ggAPS7fqeqoHavO2222arrrrqLG3X9/7617+u3fbqq6+mn8e4ceNqt918883pfvpcNSc/29NPPz3dpvwD/dzqj5cTTjghHUOPPvroLK+zBRZYIHv66afT9iGHHJJ+jtOnT89m56STTkrP/8ILL8z2vgBQdHQ1B4Bh4KyzzkpXqvp/qCtoPU2MJYcddlif26sTV/3+97+f7XPpiq66oetK3uzoCmdV9Yrnm2++ma5CVtuzwAILpMm/+rdHdYaujM5Je3TVXpNuzQ1dGdfVyrnpgq0r+y+++GK6ulk/3naHHXZIV5wH+vnN7mfQiK64nnLKKekK7Kc//el0pf3EE09MVx51hb9K3YC1L+6+++70WV/TmGH9DDUOV1eb1bX5ve99b+qSPDdd3dUNX1dQ1TVZbdDPSr0idBV9Tuh5fv3rX9vHPvax9H/1yqh+qPu9emrMbkhAtau22qEP5VBXf13Fr16RrVLe+iu+upJ7//33py7XuvpdpeNCV5urx0O9/les1fND6u+rIQb9e5voarJ6HGi7noYXfPzjH69tqweDxqird8Tzzz9vzaKfmbrbq+dE/X7QPlbPjNtuu612PGmIQv3QkUaqQxyGcgk2AIiKwnseqfuYumHpjdXyyy+f/sDP7k2dxj/2X/alftKXv/3tb6n7lt7A6I+2xqf1HzNVfQOtr+k+GutY371tbugN4Ac+8IH0OPrjOJQzAQPwRV2w9Qa6/0f92F/RJFsqyjS2s/+YWb3h1tdnR4WbxopqeSkVfeoWrrHS/el51L23nr5HquNQ9XwqTPrPiqzfgdWvz466cGuWbf1u1c9B3YlVAOWh2h79bu5PhXf/9s7Jz2BuHHrooekx+xft+lulSdCq+1VjmlWsqnDX7NYqmtWlW+Oq52aZuOoJHQ0F0Hjp6nj1OaV1nvVamTx5cq1wrn6ou77oRMbs6G+Z2qHc6h6tQk9/G+sLYOk/a/tg+0uvMT2Oisx6mrugnoY06Gdev7908kPHV3XMuPJUJ6/rX3hrn+j9wFC9BuaVTsqoe3v//VCd46C6H3RSSe1T9/wVVlghjc3X9w2kegKnfz4AGI4Y4z2bQllnwQdaKmeLLbZIf0RVdGs8lJbM0HjI6pjARrSMS/24PS23UnXPPfekyUh+9rOfpTeIeiyNQ9PVnuoVEY0b03grjc/TuL277rorPabePOuKwZzSFQZ9n8b3aQIYjSfUG1MAmN83ylr6SGN9NamSijstd6Uxpj/60Y/SOOxm0xXh6rrOas/pp5+eroTqKq+Kh0Y0Pli/GzU5W5TlkFRoqt0vv/xyw/vo6qXG4mpSNV2Z1kR6muG8WuhqfK9mOq9uD0YnMqqzms+L6qRdmnxO45sH0r9XxkA0fnlOJsHrX4jncazota/xzzrRorH1+nuumdN1RVzHQTOWV5sXapeu8n/ta18b8OvVkwF6n6JeApprQT1O9KFx6bpKr7kh6mnsu1THlwPAcEbhPY90VaGqs7MzvYHRFWNN6DLY8ikqtHXlaCD9l9vQVZA77rgjvTmsFt6aQEhvijQRS/U+6j6oN5H1hbfe6J5xxhlpRldNAqNumjpLLXojqTddevOpyWuqNOkQgOFNv8/0BlxXv6pXlasTYOnKpL4+J8W5uu2qcNPH1KlTUzGuK831hbeeR1dIq2/oRZNBSXVCKz2frmL2L37/8Y9/1L4+J+3RSVL9DtSHrtxpwjR1yx6s8FbhJPo9OifFX317dBW5OsN3lW6rb++c/gzmhn5OukrbaAKt6glc3U8njEW9tdSroEr/7z8De17UTu1XnQyYl9nj51f9/upPrzEVjLpqXU/HRv2Vc62Nrv1Y3V+aSE0rAPzud79LPeOq1CtgIPp+XRmuf/3Oz2tgXk+g6cq9jtU52Q86kaD3HPpQdh1XWjv9W9/6Vp/eMjp29DMc7PUIAMMFXc2HgK4s6OqArhjMbs1S3U9/hNZee+105bq6pEsj6pJWP+5Mf8z7r9OpM/i68l2dnVjPoXVl9aZSM43qqrb+GFbPRGu8nN5UqWvcuHHj0htSvfnkijeA7bffPn2uzmBcpSt31bHKVSpIVIwPNDa6nsb96s14/XJkVT/84Q9r/1fxoW39HtUVw2p7VJTV30905VBFRX3hPFB79L39u/bqip2Ky4HaU6+6XrfGbc8pXf3V4+vqfv3j66qgfh/X//zm9GcwEM3MreK5P1291mNopvRGf680a7pOvFb/lmg2+uqJDFE7G50gHmrq0aVx1+qFNdDfIHVFz5P+/mmmfP19rH/tqC3qHVE9Hvp3r6+nGdCl+lpUJqkfJ6/XoK4KD0QnPqqrBYhmclc3ebVrfvdD9aTBQMfpQD1DdLJfV7L70/frpP1Ax7feS1RPTPU/ptSTr37dewAYzrjiPR+OOOKI9AZJxbMmp7nqqqsGvf/uu++ezq7rDZ/GO+r7dZa9/3qdVepqfumll/aZjEdj53Q1W1fXdcVGf9S0raJbVzn0JkJvqnS1W0uJiM7Ma+ydzkarK191bKOuPunNtM6o6/7qWq+z7PWFPoDhRRNT6feExtzqzbYmhNKJPRUm+r2jYTb1SwXp6um3v/3tVFir4NRVXvWe0e8TfV2/T1S4VpfzqqfCT2ND9Xwao6viVL/vNIyneoVMV9T0nFqyTONd1T4VROrGrjHJ1SXDqu3R1XH9XtPvWf3u09hdjUPVUCB9r04C6D7qKaTfe4NRjyKdJNX9+/dIakQFs3og6Uq/fnaat6O6nJh+19b3lprTn8FANOmWTpzq8atX5lUwqTuzim6tQz0QnYTVuHstV1WlwldDoPbff//0N0p/K6onWppB48x1NVj5NQRKrx+dINBJYv3sB+s2PxR0EkJFswpE9QKrLie22GKL1dbErqeruJrnRT9nFaoaHqa/73p9yTbbbFO7IqwearqKrOFhOj40mVt/6u2g59VrUidBNM5er5lGhfrc0DEhOn40jl+vT7Wr/1V80VwMukr/0Y9+NA2x0/dqfPuDDz6Yjl8df7pwoF4r2ic61nVsaZy8fl46UVDfS0Y9S/Rep/9kdAAwbLV6WnVPTjzxxLSURvVDS4mMHj26z21PPfVU7f5atuSRRx7JrrvuurR8y/bbb5+WgplTN954Y1pmo345nKoHH3wwW3rppdPyHvW0RMk+++yTLbjggml5j7Fjx2Zf+9rX0uM8//zz2dSpU9P/tZxLfbuVQ8ulyM9//vN0nx//+Me1xy2Xy+n5fvSjH83jTw+AR9Vln7Ts1UC0HFj9cmKipaiOO+64bJVVVslGjhyZrbjiitmRRx6Zfk/U0+8cLX216KKLpueoLi2mpb823HDDbPHFF0+/i9ZYY430+1VLbVVpGSP9bnr88cfTUlALL7xwtuyyy6alkLTUVT0t53XooYem33dqz7vf/e60TFL/37f/+Mc/sk033TQ9p9qj56hUKtnhhx+eljhTO/Wc+v/ZZ589Rz+/7373u9kiiyzScEmr/suJVWmJLy0Hpd+9Sy65ZLbHHnvUltial59Bf//973+zz372s9m73vWu9H16Hu1HLd9U/3Oup2XFtEzZfffdN8vXLrzwwmzllVdOy4Eddthhs10qanavq7lZ8kq03JSWfNNrTft4ueWWy7bccsts8uTJ2ezo8We3fF11OTG9bgaiZdH0d1yvHS2V9bGPfSx7+OGH+9ynupyYbtdydno9LbHEEtlBBx3UZ+k4+d3vfpetu+66WXt7e/q5nnrqqdlPfvKTWZb2qrb92muvTffXftTxctlll/V5vHldTkz0PuKd73xnek9T//z9lxOrHms61vW60mtF7ws++MEPZt/5zndqr6tf/epX6fWq9xS6z0orrZR98YtfzJ577rk+j3XOOeek1+Zrr702yJ4BgOGjTf+0uvj3Qmdw68+s77HHHulKQPXKseiKhZaX6e9f//pXbUK0Oe1WpTPJuvqiqx31s8Dq6rSu8OissrqLD0RXuHVGXFe4dWVKV891dUrd8tQ1TWfgdfWgnrq/6QqQrizoTPUf//hH+9CHPlT7uu6vsV2NnhMAhoquqOkqmq4GeqYuwrryrZnR6+fEGE4/A2BeqEeGer5oWAgAgK7mfahLZH03a42dVtew/svqDKQ6S+nsxgzW06ygouK5SmvfqihWt8PBCmB1F1MXL7nkkktS1zCNs1I3NXWxVHdynTgYiLqPjR49OnVzrxbeKuTVjaz/xD8AMJypu7FmeVZ3ZHUf1+9ZAIPTBQVNQjfQeHEAGK4ovOfBX/7ylzQWS0WrlvHS0iEaN6exhtWr3Zq8TBPjaIIULbei+1x88cVpohYt9aJxTxrrp5l+q5OSaDIXFd26+n3YYYelMXzVK9XVsX4ag63xlro6rWU6NA5P31e/hMdxxx2XZjHXG0aNQdPJAI2x1P31uGPGjEnrh2ssuK7Sq9jWm0qpH/cHAOidz0MfAOaM3nvQkwMA+qLwngdaEkwToqlwVXdxXbHWH5mjjjoqXUmuXkHWFeXqrOWaaEWTxGimYH2PCl51Y9f3VKnLobqKq5u4PqpUGOtqdHWGXk0IpMfWVW91SVf39volR9RFXW1UMa3JUjSJiibT0UREVfqauszvueeeaSIZFfI33XRTOpEAAAAAABg6jPEGAAAAACBHDFYDAAAAACBHFN4AAAAAAOSIMd5vzUg+ZcoUW3TRRa2tra3VzQEAAAAAOKdR26+//npaVWp2K59QeJuloluTnQEAAAAAMDeeeeaZ2lLPjVB4m6Ur3dUfmJbaypNmO7/uuutsm222SbOSA8MVxwLwNo4H4G0cD0AvjgX/XnvttXQBt1pPDobCW1O7v9W9XEV3MwpvLfWl5+EAwnDGsQC8jeMBeBvHA9CLYyGOORmuzORqAAAAAADkiMIbAAAAAIAcUXgDAAAAAJAjxnjPoRkzZqRxFvNLj7HgggtauVxOj4l8aTzMAgss0OpmAAAAABjGKLznYG22559/3l555ZUhe7zlllsuzaDOmuHNsfjii6efOT9vAAAAAK1A4T0b1aJ7mWWWSbMKzm/xNnPmTJs6daotssgis11kHfN/kuONN96wF198MW0vv/zyrW4SAAAAgGGIwnsQ6gpeLbqXWmqpIXlMFd5vvvmmtbe3U3g3wUILLZQ+q/jWfqTbOQAAAIBmo/IbRHVMt650I67q/huKMfoAAAAAMLcovOcAY4NjY/8BAAAAaCUKbwAAAAAAckThPYzdcsst6Wpwdcb2Cy+8MM0AnqfNN9/cJk6cmOtzAAAAAIAnFN4Fd8cdd6QJxXbYYYcheTwV6tWPxRZbzDbZZBO76aab5vj7L7/8cjvhhBPm+P49PT3pue6///55bDEAAAAAtBaFd8Gdf/75dvDBB9ttt91mU6ZMGZLHvOCCC+y5556zP//5z7b00kvbRz/6UXviiSfm6HuXXHJJW3TRRYekHQAAAAAQAYV3gWm98EsvvdT233//dMVbXcmHgrqjL7fccrb22mvbOeecY//73//s+uuvT1+79dZbbcMNN7TRo0endbO//vWv2/Tp0xt2NV955ZXtpJNOss9//vOpIF9ppZVs8uTJta+vssoq6fO4cePSlW99f7WbvJ6no6MjtUdX3p966qkhyQcAAAAAhSy8TznllFRY1RdlKrLquzbr40tf+lKf73v66adTUaklo7RO8+GHH96n0BvOfvnLX9oaa6xhq6++un32s5+1n/zkJ5ZlWS7rZGtt8meffda23357e//7329/+9vfUlGuK+7f/va3B32MM844wzbYYAO777777IADDkgnCh555JH0tbvuuit9vuGGG9JVdnVV1/7deeedbbPNNrMHHnggdaefMGECs5cDAAAAcGlBc+Duu++2H//4x7buuuvO8rX99tvPjj/++Np2/ZraM2bMSEW3rr7efvvtqTDba6+9bOTIkekqqicqeEulkpXLZRsxYsSQF8ADUdGrgls+8pGP2KuvvpquSFevGs+vN954w4466qg0hlxF8Nlnn20rrrii/fCHP0xFsIp+dW8/4ogj7Oijj065B6JiXQW36L5nnnmm3XzzzemEwTve8Y50+1JLLZX2s7z88sspi7q4r7baaum2rq6uIckEAAAAAIW74q3u0HvssYede+65tsQSS8zydRXaKriqH2PGjKl97brrrrOHH37Yfvazn9l6661n2223XZq466yzzkpXYD1R0a2Jwp5//vlUjKpozZOuGOtq8Wc+85m0veCCC9puu+2WivH5pcdcZJFFUtfwX//61+kxddKku7vbNt544z5XntUFXPv4X//6V8PHqz/hou/Vfn7xxRcHHSf+uc99zrbddlv72Mc+Zt/73vfSSRcAAAAA8KjlV7wPPPDAdNV6q622GrBL8s9//vNUWKsYU5H1rW99q3bVW12M11lnHVt22WVr91cxpq7KDz30UBoXPJBKpZI+ql577bX0edq0aemjSv/XlemZM2emj/mhK93926DxyXk577zzUpfssWPH1m5TFo29/v73v59mJK9mquar355d13DtLz1G9Yq0vkePX/15VfV/jmo76u+jkwL12yq+1Zuhf5vq76Ni/6CDDrJrr702jWPXlXf9f6ONNpqlvdW2aX/q6jx8qB5r9cccMFxxPABv43gAenEs+Dc3+6alhfcll1xi9957b+pqPpDdd9/dOjs7U/GosbzqhqwruRrnK7p6XF90S3VbX2vk5JNPtuOOO26W23UFvb4ruwpCFfy6Yju/V9D7d7PWdrXgH2oquH/605+mExlbbLFFn6+p67lmJddkZtWr7q+//npqj04OqECdXbtUcGs8vdTfd9VVV7Urr7wydQOvXvW+8cYb05Vx9VTQfdU2/Syr36eiWM9b/zgqunViQrdVT5Do//3bpW7m6qKuj2222cYuuugiW3PNNWdpr55PE8BpZnfG//tTnZgPAMcDUI/jAejFseDX3PRiblnh/cwzz9ghhxySXkjt7e0D3kcTZlXpyrZmyd5yyy3t8ccfr43tnRdHHnmkHXbYYbVtFXQam6zirb4ruwpCtVPdqhu1cU6poNXYcxWSKnL1PI3GPM+v3/zmN/bKK6+kglRFcr1PfOIT9otf/CJNYlc9yVAtjJVRBXP9z6DRhGoD3UeP+aMf/ShdfVZPBp0kOfXUU+3QQw9NM49XT2aMGjWq9v36Geh56x9PV6V1ZV63qY16vj/96U9pzLfuqzHeGpqgHhA6KaPn0XJme++994Dt0n7UY2y66abzvR8xtGcIdfxvvfXW6dgAhjOOB+BtHA9AL44F/+bmQmrLCu977rknjeN93/ve1+dKp65KanIuFaj9uwV/4AMfSJ8fe+yxVHjranR11uuqF154IX2uTsQ1EBV1+uhPL+j6F7Xao0JUxeFQFMkqcNW9XDtoqB5zILqira7gA42ZV+F9+umn29///vfa81fbUr89mEZt18mLq6++Os0sr27+Gou97777puEB9fev/kwbbdffpiJdXeM1wd4xxxxj48ePT13LVWzrqv5//vOfdEJGhb6GGAzULt2mx+u/f+ED+wV4G8cD8DaOB6AXx4Jfc7NfWlZ468r1gw8+2Oe2ffbZJ82ErS7lA43Fvf/++9NnFVqiibxOPPHEVMBXuz7rrJCueg7U5Xi4UHfvRrT2df2M6vX/14Rl+hjM7GZj1+zm/U+G1NP62/U04Vyj/Vz1hS98IX3Uu+KKKwZtBwAAAAB40bLCW1d/11577T636Wqwlo3S7epOfvHFF6elpnSbxniry7K6C1dnwVbXcBXYe+65p5122mlpXHe1m/NAV7QBAAAAABh2s5o3oi7GN9xwg02aNCktxaVuzLvuumsqrKt0Vfyqq65KXYx19VuFu8b51q/7DQAAAABAK7kqvOu7IavQvvXWW2f7PZr1XOOKAQAAAADwKJ/ZvQAAAAAAQELhDQAAAADAcOlqjuLSbOgzZ85MH9XlyLTEV7QMmm9A64JrPXDNKUCG5iODD9EzRG+/kMEHMvhABh/I4EP0DFnw9jdC4Y2mUMH95ptv9pk8b6Al4zzTL4D65c80v4Bm54+EDD6QofWit1/I4AMZfCCDD2TwIXqGUvD2N0JXczSt8B5sOwKddatXqVQsGjL4QIbWi95+IYMPZPCBDD6QwYfoGcrB298IhTeaQl3LB9uOQF1dBtuOgAw+kKH1ordfyOADGXwggw9k8CF6hvbg7W+EruZoChXa6l5eP8Y7Go0vUVcXnXWrjjeJhgw+kKH1ordfyOADGXwggw9k8CF6ho7g7W+EwrugPve5z9krr7xiv/nNb2ZZK32LLbaw//73v7b44ovP9nFWXnllmzhxYvqYH5oQQWO653Rc91A971BSBo0viTzGhAw+kKH1ordfyOADGXwggw9k8CF6hrbg7W8k3mVHAAAAAAACofAe5n7961/bWmutZaNHj05Xmc8444za1zbffHN76qmn7NBDD01nnqrT+Ou2j33sY7bEEkukrh/6/quvvrp2RV33+/3vf2/rrrtu6h6y0UYb2d///vdcnxcAAAAAvKKrebNodr7ubmt79lnrWHZZs7XWMlt44ZY26Z577rFPfepTduyxx9puu+1mt99+ux1wwAG21FJLpa7ql19+ub33ve+1CRMm2H777Vf7vgMPPDAtDXbbbbelAvjhhx+2RRZZpM9jH3744fa9733PlltuOfvGN76RCuZHH33URo4cmevzAgAAAIA3FN7NKronTzabONHassxGtrVZNmmS2YQJmqYvt6e96qqrZilMZ8yYUfv/d7/7Xdtyyy3tW9/6Vtp+z3vek4rZ008/PRXASy65ZBqTrfEVKqCrnn76adt1111tnXXWSdurrrrqLM99zDHH2NZbb53+f9FFF9kKK6xgV1xxRSq483xeAAAAAPCGrubN0N2dim7Lst7tLLM2bev2HGkStfvvv7/Px3nnnVfXrG7bZJNN+nyPtv/5z3/2KdD7+/KXv2zf/va3031VYD/wwAOz3GfjjTeu/V+F9Oqrr56eL+/nBQAAAABvKLybYcqUt4vuKm3r9hypO/a73vWuPh/vfOc75/txv/CFL9gTTzxhe+65pz344IO2wQYb2A9+8IMhabPH5y1c74v77jP7/e97P2s7miJkKAL2A4ZCEV5HRcgAH3gtYahEfy2Vg7e/AQrvZhg7VvPi971N27q9hbq6uuzPf/5zn9u0ra7f1WW/tPb2QFehV1xxRfvSl76UxmN/5StfsXPPPbfP1++8887a/7V0mcZ366r3tGnTbI011sjtefOUZZlNnTrVXnrppfRZ26GUy5ZpyMP665t99KPpc9qO9MusCBmKoAD7IfzxXAQFeB0VIkNBjofwGXgtuRE+Q/TXUjl4+wdB4d0MXV1mGtNdLb6rY7x1ewupcL3xxhvthBNOSIWxxmL/8Ic/tK9+9au1+2jGcU1m9uyzz6ZfQKK1ta+99lp78skn7d5777Wbb745FfH1jj/++PTYms1c47aXXnpp22677Wz69Ol20EEH5fa8eSqVStbT02PPP/98+qxfxqFocr8WDHkYUkXIUAQF2A/hj+ciKMDrqBAZCnI8hM/Aa8mN8Bmiv5a6g7d/EBTezaAJ1DSR2j33WHbVVTbtL3+x7AtfyHVitTnxvve9z375y1/aJZdcYmuvvbYdffTRqWBWoVylbf3SWW211ewd73hHuk1XojXDuIrej3zkI+lK9dlnn93nsU855RQ75JBDbP3110+/uHSFWlexZdy4cXbxxRfn8rx5Kvc701apVCyUFg15GFJFyFAEBdgP4Y/nIijA66gQGQpyPITPwGvJjfAZor+WpgRv/yCY1bxZVGSPG2fZe99rpddeszE5F90XXnjhgLdrjez6LjOaJVwfjWgN7r/97W99bpuTcdUf+tCH+qzdraJZS4FVfeITn0hLiQ318+ZJa5IPth1myEP9LzMHQx6GXYYiKMB+CH88F0EBXkeFyFCQ4yF8Bl5LboTPEP21NDZ4+wfBFW80xYgRI9IV7wUXXDB91nY0mqyus7MzLXGmrvDaDqWrq3eIg7MhD8MuQxEUYD+EP56LoACvo0JkKMjxED4DryU3wmeI/lrqCt7+QXDFG03R1taWJk6rTp4WNYPWFtdHSO3t1qYhD+PH93bXGTvW2vRLLNKZ3CJkKIIC7Ifwx3MRFOB1VIgMBTkewmfgteRG+AzRX0vtwds/CApvDKn+Xdnhc8hD+oiqCBmKgP2AoVCE11ERMsAHXksYKtFfS+3B299AvP6+AAAAAAAEQuENAAAAAECOKLznwMyZM1vdBMwH9h8AAACAVmKM9yCqs29PmTIlrSWtbU24ML9FoJbV0hqBEWf2jkRjzfWz/ve//12bVR0AAAAAmo3CexAq1lZZZRV77rnnUvE9VMXg//73P1tooYXmu4jHnFl44YVtpZVW4kQHAAAAgJag8J4NXSVV0TZ9+nSbMWPGfD/etGnT7LbbbrNNN93URo4cOSRtRGNavkxrhw/JSY5y2ay7u7a0gRVkaQMAwDDH3zcAyB2F9xxQ0aYieSgKZRWCKuLb29uHVeGtK/2lUil1sVf2jo6OWFf8y2XLJk+2tokTFUYvCssmTepdZzDQm5Pw+4EMbkTPEL39QgYfwmfg75sbZPCBDK2XBW9/IxTeaAodPD09PbXtzs5OW3TRRS2M7u6335RIlvVujx8fao3B8PuBDG5EzxC9/UIGH8Jn4O+bG2TwgQytVwre/kYY9Iqm0BmrepVKxUJR97vqm5IqbQ/R2P9mCb8fyOBG9AzR2y9k8CF8Bv6+uUEGH8jQeuXg7W+EwhtNoW4ig227pzFv/bu4aFu3BxJ+P5DBjegZordfyOBD+Az8fXODDD6QofWit78RupqjKTQ2Q91EdMaqOlYjlK6u3jFv/cfAaQKaQMLvBzK4ET1D9PYLGXwIn4G/b26QwQcytF5H8PY3QuGNptCECBqbEXZ8Rnt770QzGvP21qyv6U1JsDNw4fcDGdyIniF6+4UMPoTPwN83N8jgAxlary14+xuh8AbmlN6EaKKZQJPNAAAwW/x9A4DcMcYbAAAAAIAcUXgDAAAAAJAjCm8AAAAAAHJE4Q0AAAAAQI4ovAEAAAAAyBGzmqMpsiyzUqlk5XK5th6flgqIhAw+kMGH6Bmit1/I4AMZfCCDD2TwIXqGLHj7G6HwRlPo4Onp6altd3Z2hlubjww+kMGH6Bmit1/I4AMZfCCDD2TwIXqGUvD2N0JXczSFzljVq1QqFg0ZfCCDD9EzRG+/kMEHMvhABh/I4EP0DOXg7W+EwhtNoW4ig21HQAYfyOBD9AzR2y9k8IEMPpDBBzL4ED1De/D2N0JXczSFxmaom4jOWFXHakRDBh/I4EP0DNHbL2TwgQw+kMEHMvgQPUNH8PY3QuGNptCECBqbEXl8Bhl8IIMP0TNEb7+QwQcy+EAGH8jgQ/QMbcHb3whdzQEAAAAAyBGFNwAAAAAAOaLwBgAAAAAgRxTeAAAAAADkiMIbAAAAAIAcUXgDAAAAAJAjlhNDU2RZZqVSycrlcm09Pi0VEAkZfCCDD9EzRG+/kMEHMvhABh/I4EP0DFnw9jdC4Y2m0MHT09NT2+7s7Ay3Nh8ZfCCDD9EzRG+/kMEHMvhABh/I4EP0DKXg7W+EruZoCp2xqlepVCwaMvhABh+iZ4jefiGDD2TwgQw+kMGH6BnKwdvfCIU3mkLdRAbbjoAMPpDBh+gZordfyOADGXwggw9k8CF6hvbg7W+EruZoCo3NUDcRnbGqjtWIhgw+kMGH6Bmit1/I4AMZfCCDD2TwIXqGjuDtb4TCG02hCRE0NiPy+Awy+EAGH6JniN5+IYMPZPCBDD6QwYfoGdqCt78RupoDAAAAAJAjCm8AAAAAAHJE4Q0AAAAAQI4ovAEAAAAAyBGFNwAAAAAAOaLwBgAAAAAgRywnhqbIssxKpZKVy+XaenxaKiASMvhABh+iZ4jefiGDD2TwgQw+kMGH6Bmy4O1vhMIbTaGDp6enp7bd2dkZbm0+MvhABh+iZ4jefiGDD2TwgQw+kMGH6BlKwdvfCF3N0RQ6Y1WvUqlYNGTwgQw+RM8Qvf1CBh/I4AMZfCCDD9EzlIO3vxEKbzSFuokMth0BGXwggw/RM0Rvv5DBBzL4QAYfyOBD9AztwdvfCF3N0RQam6FuIjpjVR2rEQ0ZfCCDD9EzRG+/kMEHMvhABh/I4EP0DB3B298IhTeaQhMiaGxG5PEZZPCBDD5EzxC9/UIGH8jgAxl8IIMP0TO0BW9/I3Q1BwAAAAAgRxTeAAAAAADkiMIbAAAAAIAcUXgDAAAAAJAjCm8AAAAAAHJE4Q0AAAAAQI5YTgxNkWWZlUolK5fLtfX4tFRAJGTwgQw+RM8Qvf1CBh/I4AMZfCCDD9EzZMHb3wiFN5pCB09PT09tu7OzM9zafGTwgQw+RM8Qvf1CBh/I4AMZfCCDD9EzlIK3vxG6mqMpdMaqXqVSsWjI4AMZfIieIXr7hQw+kMEHMvhABh+iZygHb38jFN5oCnUTGWw7AjL4QAYfomeI3n4hgw9k8IEMPpDBh+gZ2oO3vxG6mqMpNDZD3UR0xqo6ViMaMvhABh+iZ4jefiGDD2TwgQw+kMGH6Bk6gre/EQpvNIUmRNDYjMjjM8jgAxl8iJ4hevuFDD6QwQcy+EAGH6JnaAve/kboag4AAAAAQI4ovAEAAAAAyBGFNwAAAAAAOaLwBgAAAAAgRxTeAAAAAADkiFnNgeGkXDbr7jabMsVs7Fizri4tjtjqVgEAMH/4+wbAOQpvNEWWZVYqlaxcLtfW49NSAZGEz1AuWzZ5srVNnKgwWqvBskmTrG3ChFBvTsLvBzK4EL39QgYfyOAAf9/cIIMP0TNkwdvfCIU3mkIHT09PT227s7Mz3Np84TN0d7/9pkSyrHd7/HizceMsivD7gQwuRG+/kMEHMjjA3zc3yOBD9Ayl4O1vhDHeaAqdsapXqVQsmvAZ1P2u+qakStu6PZDw+4EMLkRvv5DBBzI4wN83N8jgQ/QM5eDtb4TCG02hbiKDbUcQPoPGvPXvpqNt3R5I+P1ABheit1/I4AMZHODvmxtk8CF6hvbg7W+EruZoCo3NUDcRnbGqjtWIJnyGrq7eMW/9x8BpAppAwu8HMrgQvf1CBh/I4AB/39wggw/RM3QEb38jFN5oCk2IoLEZkcdnhM/Q3t470YzGvL0162t6UxLsLGL4/UAGF6K3X8jgAxkc4O+bG2TwIXqGtuDtb4TCGxhO9CZEE80EmmwGAIDZ4u8bAOcY4w0AAAAAQI4ovAEAAAAAyBGFNwAAAAAAOaLwBgAAAAAgRxTeAAAAAADkiFnN0RRZllmpVLJyuVxbj09LBURCBh+KkKEIou+H6O0XMvhABh/I4AMZfIieIQve/kYovNEUOnh6enpq252dneHW5iODD0XIUATR90P09gsZfCCDD2TwgQw+RM9QCt7+RuhqjqbQGat6lUrFoiGDD0XIUATR90P09gsZfCCDD2TwgQw+RM9QDt7+Rii80RTqJjLYdgRk8KEIGYog+n6I3n4hgw9k8IEMPpDBh+gZ2oO3vxG6mqMpNDZD3UR0xqo6ViMaMvhQhAxFEH0/RG+/kMEHMvhABh/I4EP0DB3B298IhTeaQhMiaGxG5PEZZPChCBmKIPp+iN5+IYMPZPCBDD6QwYfoGdqCt78RupoDAAAAAJAjCm8AAAAAAHJE4Q0AAAAAQI4ovAEAAAAAGA6F9ymnnJIG0k+cOLHPGm4HHnigLbXUUrbIIovYrrvuai+88EKf73v66adthx12sIUXXtiWWWYZO/zww2369OktSAAAAAAAgNPC++6777Yf//jHtu666/a5/dBDD7Urr7zSLrvsMrv11lttypQptssuu9S+PmPGjFR0v/nmm3b77bfbRRddZBdeeKEdffTRLUgBAAAAAIDDwnvq1Km2xx572LnnnmtLLLFE7fZXX33Vzj//fPvud79rH/7wh2399de3Cy64IBXYd955Z7rPddddZw8//LD97Gc/s/XWW8+22247O+GEE+yss85KxTj8yLIs7euXXnopfdZ2NGTwgQw+RM8Qvf1CBh/I4AMZfCCDD9EzZMHb73Ydb3Ul11Xrrbbayr797W/Xbr/nnnts2rRp6faqNdZYw1ZaaSW74447bKONNkqf11lnHVt22WVr99l2221t//33t4ceesjGjRvX9DwYWKlUsp6entp2Z2dnuLX5yOADGXyIniF6+4UMPpDBBzL4QAYfomcoBW+/y8L7kksusXvvvTd1Ne/v+eeft1GjRtniiy/e53YV2fpa9T71RXf169WvNVKpVNJH1WuvvZY+q9DXR56qj5/383ijs1UzZ87sc0C1t7dbJGTwcSx4yjCvyNB63to/L8eDtwzzggw+eMvA8dCLDK3hKcNwfa80NVD752bftKzwfuaZZ+yQQw6x66+/vuk/yJNPPtmOO+64WW5X13VN0tYMyj2cPfnkkxYdGXwcCx4yzC8ytJ6X9s/P8eAlw/wggw9eMnA8kMEDDxmG+3ulJx23/4033vBfeKsr+Ysvvmjve9/7+kyWdtttt9kPf/hDu/baa9M47VdeeaXPVW/Nar7ccsul/+vzXXfd1edxq7OeV+8zkCOPPNIOO+ywPle8V1xxRdtmm21szJgxlvdZER08W2+9tY0cOdKGC43N0Nkq9TTQiRad4NAs9pGQwcex4CnDvCJD63lr/7wcD94yzAsy+OAtA8cDGVrJU4bh+l4pC9T+as9p14X3lltuaQ8++GCf2/bZZ580jvuII45IhbBeYDfeeGNaRkweeeSRtHzYxhtvnLb1+cQTT0wFvJYSE704VTyvueaaDZ979OjR6aM/PV+ziuFmPpcXGjoQHRl8HAveMswLMrSex/bP7fHgMcPcIoMPHjNwPMREhqE3HN8rjQrS/rnZLy0rvDVAfu211+5zW0dHR1qzu3r7vvvum65ML7nkkqmYPvjgg1OxrYnVRFeoVWDvueeedtppp6Vx3UcddVSasG2gwhoAAAAAgGE3q/lgzjzzTBsxYkS64q2uBpqx/Oyzz659fYEFFrCrrroqzWKuglyF+957723HH398S9sNAAAAAIDLwvuWW27ps60+/VqTWx+NaHr5q6++ugmtAwAAAABg7o2Yh+8BAAAAAABziMIbAAAAAIDh0tUcABBEuWzW3W02ZYrZ2LFmXV0aH2RhRG8/AAAIhcIbTV2Pr1wup7H7mgjP63p8jZDBhyJkCK9ctmzyZGubOFE7xKytzbJJk6xtwoQYxWv09hdIEY5nMmCoFGE/FCFDEUTfD1nw9jdC4Y2m0MHT09PTZ1I8LSkXCRl8KEKG8Lq73y5aJct6t8ePNxs3ztyL3v4CKcLxTAYMlSLshyJkKILo+6EUvP2NMMYbTaEzVvW0PFw0ZPChCBnCU/fsatFapW3dHkH09hdIEY5nMmCoFGE/FCFDEUTfD+Xg7W+EwhtNoW4ig21HQAYfipAhPI2J7t/lS9u6PYLo7S+QIhzPZMBQKcJ+KEKGIoi+H9qDt78RupqjKTQ2Q91EdMaqOlYjGjL4UIQM4XV19Y6J7j9GWhOURRC9/QVShOOZDBgqRdgPRchQBNH3Q0fw9jdC4Y2m0IQIGpsReXwGGXwoQobw2tt7JyLTmOi3ZgVPRWuUM9LR218gRTieyYChUoT9UIQMRRB9P7QFb38jFN4AgLmnIlUTkUWdjCx6+wEAQCiM8QYAAAAAIEcU3gAAAAAA5IjCGwAAAACAHFF4AwAAAACQIwpvAAAAAAByxKzmaIosy6xUKlm5XK6tx6elAiIhgw9k8CF6hujtFzL4QAYfyOADGXyIniEL3v5GKLzRFDp4enp6atudnZ3h1uYjgw9k8CF6hujtFzL4QAYfyOADGXyInqEUvP2N0NUcTaEzVvUqlYpFQwYfyOBD9AzR2y9k8IEMPpDBBzL4ED1DOXj7G6HwRlOom8hg2xGQwQcy+BA9Q/T2Cxl8IIMPZPCBDD5Ez9AevP2N0NUcTaGxGeomojNW1bEa0ZDBBzL4ED1D9PYLGXwggw9k8IEMPkTP0BG8/Y1QeKMpNCGCxmZEHp9BBh/I4EP0DNHbL2TwgQw+kMEHMvgQPUNb8PY3QldzAAAAAAByROENAAAAAECOKLwBAAAAAMgRhTcAAAAAADmi8AYAAAAAIEfMag4MJ+WyWXe32ZQpZmPHmnV1aXHEVrcKAAAU4W90ETKg9crFfB1xxRtNkWWZTZ061V566aX0WdvRhM9QLls2ebLZ+uubffSj6XPa1i+3QMLvh4KIvh+it1/I4AMZfAifoQh/o4uQoSBCHw/l4r6OuOKNpiiVStbT01Pb7uzsDLc2X/gM3d3WNnGifhv3bmdZ7/b48WbjxlkU4fdDQUTfD9HbL2TwgQw+hM9QhL/RRchQEKGPh+7ivo644o2mKPc7S1WpVCya8BnUXaf/GU9t6/ZAwu+Hgoi+H6K3X8jgAxl8CJ+hCH+ji5ChIEIfD1OK+zqi8EZTtPcbl9F/O4LwGTRGpq2t723a1u2BhN8PBRF9P0Rvv5DBBzL4ED5DEf5GFyFDQYQ+HsYW93VEV3M0RUdHR+rmojNuOvi1HU34DF1dlk2a9Hb3nba23m1NWBFI+P1QENH3Q/T2Cxl8IIMP4TMU4W90ETIUROjjoau4ryMKbzRFW1tbGlsSZnxJETO0t1vbhAm9Y2TemiUy/RKLdBa0CPuhIKLvh+jtFzL4QAYfwmcowt/oImQoiNDHQ3txX0cU3sBwol9ampgi+OQUAAAUThH+RhchA1qvvZivI8Z4AwAAAACQIwpvAAAAAAByROENAAAAAECOKLwBAAAAAMgRhTcAAAAAADliVnM0RZZlViqVrFwu19YT1FIHkZDBBzL4ED1D9PYLGXwggw9k8IEMPkTPkAVvfyMU3mgKHTw9PT217c7OznBrC5LBBzL4ED1D9PYLGXwggw9k8IEMPkTPUAre/kboao6m0BmrepVKxaIhgw9k8CF6hujtFzL4QAYfyOADGXyInqEcvP2NUHijKdRNZLDtCMjgAxl8iJ4hevuFDD6QwQcy+EAGH6JnaA/e/kboao6m0NgMdRPRGavqWI1oyOADGXyIniF6+4UMPpDBBzL4QAYfomfoCN7+Rii80RSaEEFjMyKPzyCDD2TwIXqG6O0XMvhABh/I4AMZfIieoS14+xuhqzkAAAAAADmi8AYAAAAAIEcU3gAAAAAA5IjCGwAAAACAHFF4AwAAAACQIwpvAAAAAAByxHJiAAAAACDlsll3t9mUKWZjx5p1dZm1t7e6VSgACm80RZZlViqVrFwuW3t7u3V0dKQ1+iIhgw9k8CF6hujtFzL4QAYfyOBD+AzlsmWTJ1vbxIkKowWlLZs0ydomTAhVfEffD1nw9jdC4Y2m0MHT09NT2+7s7LRFF13UIiGDD2TwIXqG6O0XMvhABh/I4EP4DN3dbxfdkmW92+PHm40bZ1FE3w+l4O1vhDHeaAqdsapXqVQsGjL4QAYfomeI3n4hgw9k8IEMPoTPoO7l1aK7Stu6PZDo+6EcvP2NUHijKdRNZLDtCMjgAxl8iJ4hevuFDD6QwQcy+BA+g8Z09+/SrG3dHkj0/dAevP2N0NUcTaGxGeomojNW1bEa0ZDBBzL4ED1D9PYLGXwggw9k8CF8hq6u3jHd/cd4a4K1QKLvh47g7W+EwhtNoQkRNDYj8vgMMvhABh+iZ4jefiGDD2TwgQw+hM/Q3t47kZrGdL81q3kquoNdcY2+H9qCt78RCm8AAAAAEBXZmkgt0GRqiIEx3gAAAAAA5IjCGwAAAACAHFF4AwAAAACQIwpvAAAAAAByxORqwHBSLpt1d9dm6rSAM3UCAAAA0VB4oymyLLNSqWTlcrm2Hp+WCogkfIZy2bLJk2ddm1LLZgQqvsPvBzK4EL39QgYfyOADGXwggw/RM2TB298IhTeaQgdPT09PbbuzszPc2nzhM3R3v110S5b1bmutykBLZoTfD2RwIXr7hQw+kMEHMvhABh+iZygFb38jjPFGU+iMVb1KpWLRhM+g7uXVortK27o9kPD7gQwuRG+/kMEHMvhABh/I4EP0DOXg7W+EwhtNoW4ig21HED6DxnT376ajbd0eSPj9QAYXordfyOADGXwggw9k8CF6hvbg7W+EruZoCo3NUDcRnbGqjtWIJnyGrq7eMd39x3hrgrVAwu8HMrgQvf1CBh/I4AMZfCCDD9EzdARvfyMU3mgKTYigsRmRx2eEz9De3juRmsZ0vzWreSq6g51FDL8fyOBC9PYLGXwggw9k8IEMPkTP0Ba8/Y1QeAPDiYpsTaQWaDI1AAAAIDrGeAMAAAAAkCMKbwAAAAAAckThDQAAAABAjii8AQAAAADIEYU3AAAAAAA5YlZzNEWWZVYqlaxcLtfW49NSAZGQwQcy+BA9Q/T2Cxl8IIMPZPCBDD5Ez5AFb38jFN5oCh08PT09te3Ozs5wa/ORwQcy+BA9Q/T2Cxl8IIMPZPCBDD5Ez1AK3v5G6GqOptAZq3qVSsWiIYMPZPAheobo7Rcy+EAGH8jgAxl8iJ6hHLz9jVB4oynUTWSw7QjI4AMZfIieIXr7hQw+kMEHMvhABh+iZ2gP3v5G6GqOptDYDHUT0Rmr6liNaMjgAxl8iJ4hevuFDD6QwQcy+EAGH6Jn6Aje/kYovNEUmhBBYzMij88ggw9k8CF6hujtFzL4QAYfyOADGXyInqEtePsboas5AAAAAAA5ovAGAAAAACBHFN4AAAAAAOSIwhsAAAAAgBxReAMAAAAAkCMKbwAAAAAAcsRyYmiKLMusVCpZuVyurcenpQIiIYMPZPAheobo7Rcy+EAGH8jgAxl8iJ4hC97+Rii80RQ6eHp6emrbnZ2d4dbmI4MPZPAheobo7Rcy+EAGH8jgAxl8iJ6hFLz9jdDVHE2hM1b1KpWKRUMGH8jgQ/QM0dsvZPCBDD6QwQcy+BA9Qzl4+xuh8EZTqJvIYNsRkMEHMvgQPUP09gsZfCCDD2TwgQw+RM/QHrz9jdDVHE2hsRnqJqIzVtWxGtGQwQcy+BA9Q/T2Cxl8IIMPZPCBDD5EzxC9/Y1QeKMpNCGCxmZEHp9BBh/I4EP0DNHbL2TwgQw+kMEHMvgQPUNb8PY3QldzAAAAAAByROENAAAAAECOKLwBAAAAAMgRhTcAAAAAADmi8AYAAAAAIEcU3gAAAAAA5IjlxNAUWZZZqVSycrlcW49PSwVEQgYfyOBD9AzR2y9k8IEMPpDBBzL4ED1DFrz9jVB4oyl08PT09NS2Ozs7w63NRwYfyOBD9AzR2y9k8IEMPpDBBzL4ED1DKXj7G6GrOZpCZ6zqVSoVi4YMPpDBh+gZordfyOADGXwggw9k8CF6hnLw9jdC4Y2mUDeRwbYjIIMPZPAheobo7Rcy+EAGH8jgAxl8iJ6hPXj7G6GrOZpCYzPUTURnrKpjNaIhgw9k8CF6hujtFzL4QAYfyOADGXyInqEjePsbofBGU2hCBI3NiDw+gww+kMGH6Bmit1/I4AMZfCCDD2TwIXqGtuDtb4Su5gAAAAAA5IjCGwAAAACAHFF4AwAAAACQIwpvAAAAAACKWnifc845tu6669qYMWPSx8Ybb2x/+MMfal/ffPPN0+D6+o8vfelLfR7j6aefth122MEWXnhhW2aZZezwww+36dOntyANAAAAAADOZjVfYYUV7JRTTrF3v/vdlmWZXXTRRbbTTjvZfffdZ2uttVa6z3777WfHH3987XtUYFfNmDEjFd3LLbec3X777fbcc8/ZXnvtZSNHjrSTTjqpJZkA5KxcNuvuNpsyxWzsWLOuLi3w2OpWAQAAAD4L74997GN9tk888cR0FfzOO++sFd4qtFVYD+S6666zhx9+2G644QZbdtllbb311rMTTjjBjjjiCDv22GNt1KhRTcmB2dOJlVKpZOVyubYen3owREIGB8plyyZPtraJExVG601YNmmStU2YQPHdZNFfS9HbL2TwgQwYKkXYD2TwIXqGLHj73a/jravXl112Wfohq8t51c9//nP72c9+lopvFerf+ta3ale977jjDltnnXVS0V217bbb2v77728PPfSQjRs3riVZMCvt156entp2Z2dnuLX5yOBAd/fbRbdkWe/2+PFmHO9NFf21FL39QgYfyIChUoT9QAYfomcoBW+/28L7wQcfTIW2zmgsssgidsUVV9iaa66Zvrb77runH/TYsWPtgQceSFeyH3nkEbv88svT159//vk+RbdUt/W1RiqVSvqoeu2119LnadOmpY88VR8/7+fxZurUqTZz5sw+B5TOYEVCBgfHwrPPDnxlW7evvfYQtg6RXktFaP+8HA/eMswLMvjgLQPvlXzsh3lBBh/HgqcM82JqoPbPzb5py3Qtv4XefPPNNEHaq6++ar/61a/svPPOs1tvvbVWfNe76aabbMstt7THHnvMVlttNZswYYI99dRTdu2119bu88Ybb6TuCFdffbVtt912Az6nuqEfd9xxs9x+8cUX9xlDDgAAAADAQFR76mKxallNFu668O5vq622SkX1j3/841m+prMduip+zTXXpC7lRx99tP3ud7+z+++/v3afJ5980lZddVW79957G3Y1H+iK94orrmgvvfTSbH9gQ3FW5Prrr7ett946TQI3XFTHaujnrjNWOsERbawGGRwcC5WKZRdcYG1f//rbY7xPOcXa9tnHbPTovJsMp6+lIrR/Xo4HbxnmBRl88JaB90o+9sO8IIOPY8FThnmRBWq/6sill156jgrvlnc170/dCuqL4nrVAnv55ZdPn9VFXROyvfjii2kpMdGLU6EHumJeNXr06PTRn17QzfoF38zn8qIIk92RocXHgu63335mH/oQs5o74O21VIT2z+3fBo8Z5hYZfPCYgfdKMZHBx7HgLcPcitL+udkvLS28jzzyyNQdfKWVVrLXX389dfW+5ZZbUtfxxx9/PG1vv/32ttRSS6Ux3oceeqhtuummae1v2WabbVKBveeee9ppp52WxnUfddRRduCBBw5YWAMoABXZ6s3CZGoAAAAIoqWFt65Ua91trb+92GKLpYJaRbe6UzzzzDNpmbBJkyalrgbqCr7rrrumwrpqgQUWsKuuuirNYq6r3xrbvffee/dZ9xsAAAAAgGFbeJ9//vkNv6ZCW5OszY5mPddEagAAAAAAeDSi1Q0AAAAAAKDIKLwBAAAAAMiRu1nNAeSoXDbr7mZGcAAAAKCJKLzR1PX4yuVyWo9PE+F5XY+vsMplyyZPtraJE99eA3vSJGubMCFU8V2E1xIZWi96+4UMPpDBBzL4QAYfomfIgre/EQpvNIUOnp6enj6T4i266KItbdOw0939dtEtWda7PX58qKW5ivBaIkPrRW+/kMEHMvhABh/I4EP0DKXg7W+EMd5oCp2xqlepVFrWlmFL3curRXeVtnV7IEV4LZGh9aK3X8jgAxl8IIMPZPAheoZy8PY3QuGNplA3kcG20QQa092/m462dXsgRXgtkaH1ordfyOADGXwggw9k8CF6hvbg7W+EruZoCo3NUDcRnbGqjtVAk3V19Y7p7j/GWxOsBVKE1xIZWi96+4UMPpDBBzL4QAYfomfoCN7+Rii80RSaEEFjM4owPiOs9vbeidQ0pvutWc1T0R3sLGIRXktkaL3o7Rcy+EAGH8jgAxl8iJ6hLXj7G6HwBoYTFdmaSC3QZGoAAABAdIzxBgAAAAAgRxTeAAAAAADkiMIbAAAAAIAcUXgDAAAAAJAjJlcDAACYV+WyWXd3bbUIC7haBAC4Ui7m71UKbzRFlmVWKpWsXC7X1uPTUgGRkMEHMvgQPUP09hclQ3jlsmWTJ1vbxInaIVoDx7JJk3qXbgz0JpHXkg9F2A9FyIAWKxfj9+pAKLzRFPol3NPTU9vu7OwMtzYfGXwggw/RM0Rvf1EyhNfd/fabQ8my3u3x40Mt28hryYci7IciZECLdRfj9+pAGOONptCZz3qVSsWiIYMPZPAheobo7S9KhvDUDbL65rBK27o9EF5LPhRhPxQhA1psSjF+rw6EwhtNoe5Gg21HQAYfyOBD9AzR21+UDOFp7GH/brTa1u2B8FryoQj7oQgZ0GJji/F7dSB0NUdTaIyPuhvpzGd1zE80ZPCBDD5EzxC9/UXJEF5XV+/Yw/5jETURUCC8lnwown4oQga0WFcxfq8OhMIbTaGJNTTGJ/I4HzL4QAYfomeI3v6iZAivvb13wh+NPXxr9t305jDYVT5eSz4UYT8UIQNarL0Yv1cHQuENAAAwr/RmUBP+BJ/0BwDcaC/m71XGeAMAAAAAkCMKbwAAAAAAckThDQAAAABAjii8AQAAAADIEYU3AAAAAAA5YlZzNEWWZVYqlaxcLtfWddSSE5GQwQcy+BA9Q/T2Cxl8IIMPZPCBDD5Ez5AFb38jFN5oCh08PT09te3Ozs5wazySwQcy+BA9Q/T2Cxl8IIMPZPCBDD5Ez1AK3v5G6GqOptAZq3qVSsWiIYMPZPAheobo7Rcy+EAGH8jgAxl8iJ6hHLz9jVB4oynUTWSw7QjI4AMZfIieIXr7hQw+kMEHMvhABh+iZ2gP3v5G6GqOptDYDHUT0Rmr6liNaMjgAxl8iJ4hevuFDD6QwQcy+EAGH6Jn6Aje/kYovNEUmhBBYzMij88ggw9k8CF6hujtFzL4QAYfyOADGXyInqEtePsboas5AAAAAAA5ovAGAAAAACBHFN4AAAAAAOSIwhsAAAAAgBxReAMAAAAAkCMKbwAAAAAAcsRyYmiKLMusVCpZuVyurcenpQIiIYMPZPAheobo7Rcy+EAGH8jgAxl8iJ4hC97+IS28p0+fbrfccos9/vjjtvvuu6c11qZMmWJjxoyxRRZZZOhbifB08PT09NS2Ozs7w63NRwYfyOBD9AzR2y9k8IEMPpDBBzL4ED1DKXj7h6yr+VNPPWXrrLOO7bTTTnbggQfav//973T7qaeeal/96lfzaCMKQGes6lUqFYuGDD6QwYfoGaK3X8jgAxl8IIMPZPAheoZy8PYPWeF9yCGH2AYbbGD//e9/baGFFqrd/vGPf9xuvPHGoW4fCkLdRAbbjoAMPpDBh+gZordfyOADGXwggw9k8CF6hvbg7R+yruZ//OMf7fbbb7dRo0b1uX3llVe2Z599dijbhgLR2Ax1E9EZq+pYjWjI4AMZfIieIXr7hQw+kMEHMvhABh+iZ+gI3v4hK7xnzpxpM2bMmOX2f/3rX4Xoe498aEIEvT4iv0bI4AMZfIieIXr7hQw+kMEHMvhABh+iZ2gL3v4h62q+zTbb2KRJk/r8YKZOnWrHHHOMbb/99kPdPgAAAAAAhtcV7zPOOMO23XZbW3PNNdPAd81q/s9//tOWXnpp+8UvfpFPKwEAAAAAGC6F9worrGB/+9vf7NJLL02fdbV73333tT322KPPZGsAAAAAAGAeCu/bbrvNPvjBD6ZCWx/1a3vra5tuuulQtxEAAAAAgOEzxnuLLbawl19+eZbbX3311fQ1AAAAAAAwH4V3lmVpQrX+/vOf/xRmqncAAAAAAJre1XyXXXZJn1V0f+5zn7PRo0fXvqblxR544IHUBR1odMKmVCqlCfmq6/ENdALHMzL4QAYfomeI3n4hgw9k8IEMPpDBh+gZsuDtn+/Ce7HFFqv9ILSmWv1EaqNGjbKNNtrI9ttvv3xaifB08PT09NS2Ozs7w63NRwYfyOBD9AzR2y9k8IEMPpDBBzL4ED1DKXj757vwvuCCC9LnlVde2b761a/SrRxzRWes6lUqlXAHEBl8IIMP0TNEb7+QwQcy+EAGH8jgQ/QM5eDtH7JZzY855ph8WoJCUzeRwbYjIIMPZPAheobo7Rcy+EAGH8jgAxl8iJ6hPXj7h6zwll/96lf2y1/+0p5++ml78803+3zt3nvvHaq2oUDUQ0LdRHTGqjpWIxoy+EAGH6JniN5+IYMPZPCBDD6QwYfoGTqCt3/ICu/vf//79s1vfjNNsPbb3/7W9tlnH3v88cft7rvvtgMPPDCfViI8TYigLiKRu4mQwQcy+BA9Q/T2Cxl8IIMPZPCBDD5Ez9AWvP1DtpzY2WefbZMnT7Yf/OAHaVK1r33ta3b99dfbl7/85bSWNwAAAAAAmI/CW93Lq8uGaWbz119/Pf1/zz33tF/84hdz+3AAAAAAABTaXBfeyy23nL388svp/yuttJLdeeed6f9PPvlkWmoMAAAAAADMR+H94Q9/2H73u9+l/2t896GHHmpbb7217bbbbvbxj398bh8OAAAAAIBCm+vJ1TS+e+bMmen/mkxtqaWWsttvv9123HFH++IXv5hHGwEAAAAAGD6F94gRI9JH1ac//en0AQAAALREuWzW3W02ZYrZ2LFmXV1a/LfVrQKA+VvH+5VXXrG77rrLXnzxxdrV76q99tprXh4SBafx/6VSycrlcm09Pi0VEAkZfCCDD9EzRG+/kMEHMjhQLls2ebK1TZyoMFqLyLJJk6xtwoRQxXf4/UAGN6JnyIK3f8gK7yuvvNL22GMPmzp1qo0ZM6bPD0H/p/DGQHTw9PT01LY7OzvDrc1HBh/I4EP0DNHbL2TwgQwOdHe/XXRLlvVujx9vNm6cRRF+P5DBjegZSsHbP2STq33lK1+xz3/+86nw1pXv//73v7WP6mznQH86Y1WvUqlYNGTwgQw+RM8Qvf1CBh/I4IC6l/dfWUfbuj2Q8PuBDG5Ez1AO3v4hK7yfffZZ+/KXv2wLL7xwPi1CIambyGDbEZDBBzL4ED1D9PYLGXwggwMa092/G6q2dXsg4fcDGdyInqE9ePuHrKv5tttua3/9619t1VVXzadFKCSNzVA3EZ2xqo7ViIYMPpDBh+gZordfyOADGRzo6uod091/jLcmWAsk/H4ggxvRM3QEb/+QFd477LCDHX744fbwww/bOuusYyNHjuzzdS0rBvSn8f8amxF5fAYZfCCDD9EzRG+/kMEHMjjQ3t47kZrGdL81q3kquoNdJQu/H8jgRvQMbcHbP2SF93777Zc+H3/88QP+kGbMmDE0LQMAAADmhIpsTaQWaDI1AMPLXBfe/ZcPAwAAAAAAQzi5GgAAAAAAGOIr3t///vdtwoQJaXC7/j8YzXgOAAAAAADmovA+88wzbY899kiFt/7fiMZ4U3gDAAAAADCXhfeTTz454P8BAACGtXLZrLu7Npu2BZxNuxAZioD9ABQaY7zRFFmW2dSpU+2ll15Kn7WN5ivCfiCDD9EzRG+/kMGBctmyyZPN1l/f7KMfTZ/TtgqoKIqQgdeSG+H3AxlcyIK3f76ueB922GFz/IDf/e5356c9KKhSqWQ9PT217c7OzsKtzRdBEfYDGXyIniF6+4UMDnR3W9vEiXqX2LudZb3bWk86yrJWRcjAa8mN8PuBDC6Ugrd/vgrv++67r8/2vffea9OnT7fVV189bT/66KO2wAIL2Po6SwcMoNzvjG2lUinEARRNEfYDGXyIniF6+4UMDqhLcP8rMdrW7VGKpSJk4LXkRvj9QAYXysHbP1+F980339zniraCX3TRRbbEEkuk2/773//aPvvsY+N1Vg4YgCbmG2wbzVGE/UAGH6JniN5+IYMDGofb1ta3YNK2bo+iCBl4LbkRfj+QwYX24O2fr8K73hlnnGHXXXddregW/f/b3/62bbPNNvaVr3xlqNuIAujo6EjdRHTGSgePttF8RdgPZPAheobo7RcyONDVZdmkSW93EW5r693WpFhRFCEDryU3wu8HMrjQEbz9Q1Z4v/baa/bvf/97ltt12+uvvz5U7ULBaKk59ZQoQjeRyIqwH8jgQ/QM0dsvZHCgvd3aJkzoHYf71kzUqVCKdHWmCBl4LbkRfj+QwYW24O0fssL74x//eOpWrivfG264YbrtL3/5ix1++OG2yy675NFGAAAAn1QYaQxuoHG4hcxQBOwHoNDmuvD+0Y9+ZF/96ldt9913t2nTpvU+yIIL2r777munn356Hm0EAAAAAGB4FN4zZsywv/71r3biiSemIvvxxx9Pt6+22mqF6XsPAAAAAEDLCm8tGaYJ1Lq7u22VVVaxddddd0gbAwAAAABA0YyY229Ye+217YknnsinNQAAAAAADPfCW8uGaYz3VVddZc8991ya5bz+AwAAAAAAzMfkattvv336vOOOO6ap3quyLEvbGgcOAAAAAADmsfC++eab5/ZbgHRiplQqWblctvb29jQZX/2JmwjI4AMZfIieIXr7hQw+kMEHMvhABh+iZ8iCt3/ICu/NNtssn5ag0HTw9PT01LY7Oztt0UUXtUjI4AMZfIieIXr7hQw+kMEHMvhABh+iZygFb/+QjfGWV155xc444wz7whe+kD7OPPNMe/XVV4e+dSgMnbGqV6lULBoy+EAGH6JniN5+IYMPZPCBDD6QwYfoGcrB2z9khbfW8da63Sq2X3755fTx3e9+N91277335tNKhKduIoNtR0AGH8jgQ/QM0dsvZPCBDD6QwQcy+BA9Q3vw9g9ZV/NDDz00Tax27rnn2oIL9n779OnT05XviRMn2m233ZZHOxGcxmaom4jOWFXHakRDBh/I4EP0DNHbL2TwgQw+kMEHMvgQPUNH8PYPWeGtK971RXd6kAUXtK997Wu2wQYbDHX7UBCaEEFjMyKPzyCDD2TwIXqG6O0XMvhABh/I4AMZfIieoS14+4esq/mYMWPs6aefnuX2Z555pnA/HAAAAAAAml5477bbbrbvvvvapZdemoptfVxyySWpq/lnPvOZ+W4QAAAAAABFMtddzb/zne+ky/977bVXGtstI0eOtP33399OOeWUPNoIAAAAAMDwKbxHjRpl3/ve9+zkk0+2xx9/PN2mGc0XXnjhPNoHAAAAAMDwKryrVGgvscQStf8DAAAAAIAhGOM9c+ZMO/74422xxRZL07zrY/HFF7cTTjghfQ0AAAAAAMzHFe9vfvObdv7556fx3Jtsskm67U9/+pMde+yxVi6X7cQTT5zbh8QwkGWZlUql9BqprsenuQIiIYMPZPAheobo7Rcy+EAGH8jgAxl8iJ4hC97+ISu8L7roIjvvvPNsxx13rN227rrr2jvf+U474IADKLwxIB08PT09tW31lIi2/BwZfCCDD9EzRG+/kMEHMvhABh/I4EP0DKXg7R+yruYvv/yyrbHGGrPcrtv0NWAgOmNVr1KpWDRk8IEMPkTPEL39QgYfyOADGXwggw/RM5SDt3/ICu/3vve99sMf/nCW23WbvgYMRN1EBtuOgAw+kMGH6Bmit1/I4AMZfCCDD2TwIXqG9uDtH7Ku5qeddprtsMMOdsMNN9jGG2+cbrvjjjvsmWeesauvvjqPNqIANDZD3UR0xqo6ViMaMvhABh+iZ4jefiGDD2TwgQw+kMGH6Bk6grd/yArvzTbbzB599FE766yz7B//+Ee6bZdddknju8eOHZtHG1EAmhBBYzMij88ggw9k8CF6hujtFzL4QAYfyOADGXyInqEtePuHdB1vFdhMogYAAAAAwBCO8f7nP/9pn/nMZ+y1116b5Wuvvvqq7b777vbEE0/M6cMBAAAAADAszHHhffrpp9uKK65oY8aMmeVriy22WPqa7gMAAAAAAOah8L711lvtk5/8ZMOvf+pTn7KbbrppTh8OAAAAAIBhYY4L76efftqWWWaZhl9feuml08zmc+Occ86xddddN11F14dmSf/DH/7QZw23Aw880JZaailbZJFFbNddd7UXXnhhlnZplvWFF144te/www+36dOnz1U7AAAAAABoeeGt7uSPP/54w68/9thjA3ZDH8wKK6xgp5xyit1zzz3217/+1T784Q/bTjvtZA899FD6+qGHHmpXXnmlXXbZZemK+5QpU9IM6lUzZsxIRfebb75pt99+u1100UV24YUX2tFHHz1X7QAAAAAAoOWzmm+66ab2gx/8IBXHA/n+979v48ePn6sn/9jHPtZnWzOl6yr4nXfemYry888/3y6++OLac15wwQXW1dWVvr7RRhvZddddZw8//HBaU3zZZZe19dZbz0444QQ74ogj7Nhjj7VRo0bNVXuQnyzLrFQqpV4M1fX4tFRAJGTwgQw+RM8Qvf1CBh/I4AMZfCCDD9EzZMHbP9+F95FHHpm6gn/iE5+wr33ta7b66qun27WW92mnnWbXXnttuuo8r3T1Wle29UPW8+gq+LRp02yrrbaq3WeNNdawlVZaye64445UeOvzOuusk4ruqm233db233//dNV83Lhx89weDC3t156entp2Z2dnuLX5yOADGXyIniF6+4UMPpDBBzL4QAYfomcoBW//fBfeKmJ/9atf2ec//3m74oor+nxNY7B/+ctf2vve9765bsCDDz6YCm2d0dA4bj32mmuuaffff3+6Yr344ov3ub+K7Oeffz79X5/ri+7q16tfa6RSqaSPquoSaSr09ZGn6uPn/TzeTJ061WbOnNnngNIZrEjI4ONY8JRhXpGh9by1f16OB28Z5gUZfPCWgeOhFxlaw1OG4fpeaWqg9s/NvmnLdC1/Lvzvf/+za665Jo3p1re+5z3vsW222SZNbjYvND5bE6RpLXAV9uedd14az63Ce5999ulTIMuGG25oW2yxhZ166qk2YcIEe+qpp9LV9qo33ngjdUe4+uqrbbvtthvwOdUN/bjjjpvldnVrn9ccAAAAAIDh44033rDdd9891bKzm+9sjq94Vy200EL28Y9/3IaKrmq/613vSv9ff/317e6777bvfe97tttuu6Wi/JVXXulz1Vuzmi+33HLp//p811139Xm86qzn1fs06jZ/2GGH9bnirXXIdQJhbieIm5ezItdff71tvfXWNnLkSBsuqmM1dCJFZ6x0giPaWA0y+DgWPGWYV2RoPW/tn5fjwVuGeUEGH7xl4HggQyt5yjBc3ytlgdpf7Tk9J+a68M6buhXoh6wiXC+wG2+8MS0jJo888ki6Oq6u6aLPmpDtxRdfrC11phenimd1V29k9OjR6aM/PV+ziuFmPpcXRZjsjgw+jgVvGeYFGVrPY/vn9njwmGFukcEHjxk4HmIiw9Abju+VRgVp/9zsl5YW3rryrO7gmjDt9ddfT129b7nlltR1XMuX7bvvvunK9JJLLpmK6YMPPjgV25pYTXSFWgX2nnvumSZ407juo446Kq39PVBhDQAAAABAs81x4a01tMeOHTukT64r1XvttZc999xzqdBed911U9Gt7hRy5pln2ogRI9IVb10F14zlZ599du37F1hgAbvqqqvSLOYqyDW2e++997bjjz9+SNsJAAAAAEDuhfdaa61lZ511Vho8PlS0Tvdg1Kdfz6mPRjS9vCZSAwAAAADAoxFzekeNpf7iF79on/zkJ+3ll1/Ot1UAAAAAAAy3wvuAAw6wBx54wP7zn/+kcdVXXnllvi0DAAAAAKAA5mpytVVWWcVuuukm++EPf2i77LKLdXV12YIL9n2Ie++9d6jbCADwplw26+7WBCBmmv+jq0vjg1rdKgAAAJfmelbzp556yi6//HJbYoklbKeddpql8AYGW4+vXC6nsfuaCM/renyNkMEHMjhQLls2ebK1TZyoMGZtbZZNmmRtEyaEKb7D7wMyuEEGH8jgAxl8iJ4hC97+Ruaqaj733HPtK1/5im211Vb20EMP2Tve8Y78WoZC0cHT09PTZ1K8RRdd1CIhgw9kcKC7++2iW7Ksd3v8eLNx4yyC8PuADG6QwQcy+EAGH6JnKAVv/3yP8f7IRz5iRxxxROpmriveFN2YGzpjVU/Lw0VDBh/I4IC6l1eL7ipt6/Ygwu8DMrhBBh/I4AMZfIieoRy8/fN9xXvGjBlpcrUVVlgh3xahkNRNZLDtCMjgAxkc0JhudfmqL761rduDCL8PyOAGGXwggw9k8CF6hvbg7Z/vwvv666/PtyUoNI3NUDcRnbGqjtWIhgw+kMGBrq7eMd39x3hrgrUgwu8DMrhBBh/I4AMZfIieoSN4+xthZjQ0hSZE0NiMyOMzyOADGRxob++dSE1jut+a1TwV3YHOSIffB2Rwgww+kMEHMvgQPUNb8PY3QuENAJh7KrI1kVqQydQAAABCTK4GAAAAAADmHoU3AAAAAAA5ovAGAAAAACBHFN4AAAAAAOSIwhsAAAAAgBwxqzmaIssyK5VKVi6Xa+vxaamASMjgAxl8iJ4hevuFDD6QwQcy+EAGH6JnyIK3vxEKbzSFDp6enp7admdnZ7i1+cjgAxl8iJ4hevuFDD6QwQcy+EAGH6JnKAVvfyN0NUdT6IxVvUqlYtGQwQcy+BA9Q/T2Cxl8IIMPZPCBDD5Ez1AO3v5GKLzRFOomMth2BGTwgQw+RM8Qvf1CBh/I4AMZfCCDD9EztAdvfyN0NUdTaGyGuonojFV1rEY0ZPCBDD5EzxC9/UIGH8jgAxl8IIMP0TN0BG9/IxTeaApNiKCxGZHHZ5DBBzL4ED1D9PYLGXwggw9k8IEMPkTP0Ba8/Y3Q1RwAAAAAgBxReAMAAAAAkCMKbwAAAAAAckThDQAAAABAjii8AQAAAADIEYU3AAAAAAA5YjkxNEWWZVYqlaxcLtfW49NSAZGQwQcy+BA9Q/T2Cxl8IIMPZPCBDD5Ez5AFb38jFN5oCh08PT09te3Ozs5wa/ORwQcy+BA9Q/T2Cxl8IIMPZPCBDD5Ez1AK3v5G6GqOptAZq3qVSsWiIYMPZPAheobo7Rcy+EAGH8jgAxl8iJ6hHLz9jVB4oynUTWSw7QjI4AMZfIieIXr7hQw+kMEHMvhABh+iZ2gP3v5G6GqOptDYDHUT0Rmr6liNaMjgAxl8iJ4hevuFDD6QwQcy+EAGH6Jn6Aje/kYovNEUmhBBYzMij88ggw9k8CF6hujtFzL4QAYfyOADGXyIniF6+xuhqzkAAAAAADmi8AYAAAAAIEcU3gAAAAAA5IjCGwAAAACAHFF4AwAAAACQIwpvAAAAAAByxHJiaIosy6xUKlm5XK6tx6elAiIhgw9kwFAowj4oQoYiKMJ+IIMPZPCBDK2XBW9/IxTeaAodPD09PbXtzs7OcGvzkcEHMmAoFGEfFCFDERRhP5DBBzL4QIbWKwVvfyN0NUdT6IxVvUqlYtGQwQcyYCgUYR8UIUMRFGE/kMEHMvhAhtYrB29/IxTeaAp1ExlsOwIy+EAGDIUi7IMiZCiCIuwHMvhABh/I0HrtwdvfCF3N0RQam6FuIjpjVR2rEQ0ZfCADhkIR9kERMhRBEfYDGXwggw9kaL2O4O1vhMIbTaEJETQ2I/L4DDL4QAYMhSLsgyJkKIIi7Acy+EAGH8jQem3B298IXc0BAAAAAMgRhTcAAAAAADmi8AYAAAAAIEcU3gAAAAAA5IjCGwAAAACAHFF4AwAAAACQI5YTQ1NkWWalUsnK5XJtPT4tFRAJGXwggw/RM0Rvv5DBBzL4QAYfyOBD9AxZ8PY3QuGNptDB09PTU9vu7OwMtzYfGXwggw/RM0Rvv5DBBzL4QAYfyOBD9Ayl4O1vhK7maAqdsapXqVQsGjL4QAYfomeI3n4hgw9k8IEMPpDBh+gZysHb3wiFN5pC3UQG246ADD6QwYfoGaK3X8jgAxl8IIMPZPAheob24O1vhK7maAqNzVA3EZ2xqo7ViIYMPpDBh+gZordfyOADGXwggw9k8CF6ho7g7W+EwhtNoQkRNDYj8vgMMvhABh+iZ4jefiGDD2TwgQw+kMGH6Bnagre/EbqaAwAAAACQIwpvAAAAAAByROENAAAAAECOKLwBAAAAAMgRhTcAAAAAADliVnMAAAAA869cNuvuNpsyxWzsWLOuLi3C3OpWIZpyMV9HFN5oiizLrFQqWblcrq3Hp6UCIiGDD2TwIXqG6O0vSoYiKMJ+IIMP4TOUy5ZNnmxtEycqjNaEsmzSJGubMCFU0RR+P0RXLsbraCAU3mgK/QLr6empbXd2doZbm48MPpDBh+gZore/KBmKoAj7gQw+hM/Q3f12sSRZ1rs9frzZuHEWRfj9EF13MV5HA2GMN5pCZw3rVSoVi4YMPpDBh+gZore/KBmKoAj7gQw+hM+gbsHVYqlK27o9kPD7IbopxXgdDYTCG02hrjqDbUdABh/I4EP0DNHbX5QMRVCE/UAGH8Jn0Fjc/l2yta3bAwm/H6IbW4zX0UDoao6m0PgYddXRWcPqeJloyOADGXyIniF6+4uSoQiKsB/I4EP4DF1dvWNx+4/N1cRYgYTfD9F1FeN1NBAKbzSFJqXQ+JjIY2TI4AMZfIieIXr7i5KhCIqwH8jgQ/gM7e29E2BpLO5bs1GnYinYFePw+yG69mK8jgZC4Q0AAABg/qk40gRYwSfBQou1F/N1xBhvAAAAAAByROENAAAAAECOKLwBAAAAAMgRhTcAAAAAADmi8AYAAAAAIEfMao6myLLMSqWSlcvl2pqIWq4hEjL4QAYfomeI3n4hgw9k8IEMPpDBh+gZsuDtb4TCG02hg6enp6e23dnZGW59RDL4QAYfomeI3n4hgw9k8IEMPpDBh+gZSsHb3whdzdEUOmNVr1KpWDRk8IEMPkTPEL39QgYfyOADGXwggw/RM5SDt78RCm80hbqJDLYdARl8IIMP0TNEb7+QwQcy+EAGH8jgQ/QM7cHb3whdzdEUGpuhbiI6Y1UdqxENGXwggw/RM0Rvv5DBBzL4QAYfyOBD9AwdwdvfCIU3mkITImhsRuTxGWTwgQw+RM8Qvf1CBh/I4AMZfCCDD9EztAVvfyN0NQcAAAAAIEcU3gAAAAAA5IjCGwAAAACAHFF4AwAAAACQIwpvAAAAAAByxKzmaI5y2ay722zKFLOxY826urQoX6tbNfywHwAAAICmo/BG/splyyZPtraJE82yTGsEWDZpkrVNmBCq6MuyzEqlkpXL5dqaglruIAz2gxtkaL3o7Rcy+EAGH8jgAxl8iJ4hC97+Rii8kb/u7reLPcmy3u3x483GjbMo9Augp6entt3Z2RlrfUH2gxtkaL3o7Rcy+EAGH8jgAxl8iJ6hFLz9jTDGG/lTt+ZqsVelbd0eiM661atUKhYK+8ENMrRe9PYLGXwggw9k8IEMPkTPUA7e/kYovJE/jSXu3z1E27o9EHV1GWzbPfaDG2RovejtFzL4QAYfyOADGXyInqE9ePsboas58tfV1TuWuP/YYk3sFYjGl6iri866VcebhMJ+cIMMrRe9/UIGH8jgAxl8IIMP0TN0BG9/IxTeyF97e+8EXhpL/NZs2qnYC3b2SpM6aHxJ2DEm7Ac3yNB60dsvZPCBDD6QwQcy+BA9Q1vw9jdC4Y3mUHGnCbwCTeJVSOwHAAAAoOkY4w0AAAAAQI4ovAEAAAAAyBGFNwAAAAAAOaLwBgAAAAAgRxTeAAAAAADkiFnN0RRZllmpVLJyuVxbj09LBURCBh/I4EP0DNHbL2TwgQw+kMEHMvgQPUMWvP2NUHijKXTw9PT01LY7OzvDrc1HBh/I4EP0DNHbL2TwgQw+kMEHMvgQPUMpePsboas5mkJnrOpVKhWLhgw+kMGH6Bmit1/I4AMZfCCDD2TwIXqGcvD2N0LhjaZQN5HBtiMggw9k8CF6hujtFzL4QAYfyOADGXyInqE9ePsboas5mkJjM9RNRGesqmM1oiGDD2TwIXqG6O0XMvhABh/I4AMZfIieoSN4+xuh8EZTaEIEjc2IPD6DDD6QwYfoGaK3X8jgAxl8IIMPZPAheoa24O1vhK7mAAAAAADkiMIbAAAAAIAcUXgDAAAAAJAjCm8AAAAAAHJE4Q0AAAAAQFEL75NPPtne//73pxnrlllmGdt5553tkUce6XOfzTffPM1sV//xpS99qc99nn76adthhx1s4YUXTo9z+OGH2/Tp05ucBgAAAAAAZ8uJ3XrrrXbggQem4luF8je+8Q3bZptt7OGHH+6zXtt+++1nxx9/fG1bBXbVjBkzUtG93HLL2e23327PPfec7bXXXjZy5Eg76aSTmp4JBVYum3V3m02ZYjZ2rFlXl1l7e6tbBQAAAMC5lhbe11xzTZ/tCy+8MF2xvueee2zTTTftU2irsB7Iddddlwr1G264wZZddllbb7317IQTTrAjjjjCjj32WBs1alTuOTB7WZZZqVSycrls7e3t6cSKei+EUS5bNnmytU2cqDBaYNCySZOsbcKEUMV3+P1ABjeiZ4jefiGDD2TwgQw+kMGH6Bmy4O13WXj39+qrr6bPSy65ZJ/bf/7zn9vPfvazVHx/7GMfs29961u1q9533HGHrbPOOqnortp2221t//33t4ceesjGjRs3y/NUKpX0UfXaa6+lz9OmTUsfeao+ft7P440OHg0JqFpxxRVtkUUWsTAeesjsyCP7Ftna3mQTs3XXtSg87Yd5PRY8ZZhXZGg9b+2fl+PBW4Z5QQYfvGXgeOhFhtbwlGG4vlcqBWr/3OybtkynFByYOXOm7bjjjvbKK6/Yn/70p9rtkydPts7OThs7dqw98MAD6Ur2hhtuaJdffnn6+oQJE+ypp56ya6+9tvY9b7zxRjozcvXVV9t22203y3PpSvhxxx03y+0XX3xxn27sAAAAAAAMRHXn7rvvni4gjxkzxkJc8dZY77///e99iu5qYV2lK9vLL7+8bbnllvb444/baqutNk/PdeSRR9phhx3W54q3zqRofPnsfmBDcVbk+uuvt6233jqNQx8u+p+5WmmllfqM43fvgQfMNPyh/jyVurzcdlvoK96t3A/zeix4yjCvyNB63to/L8eDtwzzggw+eMvA8dCLDK3hKcNwfa9UCtT+as/pOeGi8D7ooIPsqquusttuu81WWGGFQe/7gQ98IH1+7LHHUuGt7ud33XVXn/u88MIL6XOjceGjR49OH/3pBd2sYriZz+XBYostZqusskrq4h9yrMZaa1l28smzjvFeay3tTIvC436Y22PBY4a5RYbW89r+uTkevGaYG2TwwWsGjgcytILHDMPtvdJigdo/N/ulpYW3erkffPDBdsUVV9gtt9ySfsCzc//996fPuvItG2+8sZ144on24osvponZRGeGdOV6zTXXzDkB5pQOFi0bp4+Q2tt7J1IbP742q3lbwFnNw+8HMrgRPUP09gsZfCCDD2TwgQw+RM/QFrz9LgtvdS/XuOrf/va36Qf7/PPP185yLLTQQqk7ub6+/fbb21JLLZXGeB966KFpxvN13+req+7hKrD33HNPO+2009JjHHXUUemxB7qqDcwzFdmarG+ACfsAAAAAoJER1kLnnHNOGoi++eabpyvY1Y9LL700fV1LgWmZMBXXa6yxhn3lK1+xXXfd1a688sraYyywwAKpm7o+6+r3Zz/72bSOd/263wAAAAAAtErLu5oPRhOe3XrrrbN9HM16rhnMAQAAAADwpqVXvAEAAAAAKDoKbwAAAAAAckThDQAAAABAjlys443i03j+Uqlk5XLZ/Xp8jZDBBzL4ED1D9PYLGXwggw9k8IEMPkTPkAVvfyMU3mgKHTw9PT19JsSLtjYfGXwggw/RM0Rvv5DBBzL4QAYfyOBD9Ayl4O1vhK7maAqdsapXqVQsGjL4QAYfomeI3n4hgw9k8IEMPpDBh+gZysHb3wiFN5pC3UQG246ADD6QwYfoGaK3X8jgAxl8IIMPZPAheob24O1vhK7maAqNzVA3EZ2xqo7ViIYMPpDBh+gZordfyOADGXwggw9k8CF6ho7g7W+EwhtNoQkRNDYj8vgMMvhABh+iZ4jefiGDD2TwgQw+kMGH6Bnagre/EbqaAwAAAACQIwpvAAAAAAByROENAAAAAECOKLwBAAAAAMgRhTcAAAAAADliVnMAAACg1cpls+5usylTzMaONevq0gLGrW4VgCFC4Y2myLLMSqWSlcvl2np8WiogEjL4QAYfomeI3n4hgw9k8CF8hnLZssmTrW3iRIXRekqWTZpkbRMmhCq+w+8HMriQBW9/IxTeaAodPD09PbXtzs7OcGvzkcEHMvgQPUP09gsZfCCDD+EzdHe/XXRLlvVujx9vNm6cRRF+P5DBhVLw9jfCGG80hc5Y1atUKhYNGXwggw/RM0Rvv5DBBzL4ED6DupdXi+4qbev2QMLvBzK4UA7e/kYovNEU6iYy2HYEZPCBDD5EzxC9/UIGH8jgQ/gMGtPdvyuttnV7IOH3AxlcaA/e/kboao6m0NgMdRPRGavqWI1oyOADGXyIniF6+4UMPpDBh/AZurp6x3T3H+OtCdYCCb8fyOBCR/D2N0LhjabQhAgamxF5fAYZfCCDD9EzRG+/kMEHMvgQPkN7e+9EahrT/das5qnoDnalL/x+IIMLbcHb3wiFNwAAANBqKrI1kVqgydQAzDnGeAMAAAAAkCMKbwAAAAAAckThDQAAAABAjii8AQAAAADIEZOrAYilXDbr7q7N+moBZ30FAADA8ELhjabIssxKpZKVy+XaenxaKiASMjhQLls2efKs65xqCZZAxXf4/VAARdgHZPCBDD6QwQcyYChkBd0HFN5oCh08PT09te3Ozs5wa/ORwYHu7reLbsmy3m2texpo+ZXw+6EAirAPyOADGXwggw9kwFAoFXQfMMYbTaEzVvUqlYpFQwYH1L28WnRXaVu3BxJ+PxRAEfYBGXwggw9k8IEMGArlgu4DCm80hbqJDLYdARkc0Jju/l2NtK3bAwm/HwqgCPuADD6QwQcy+EAGDIX2gu4DupqjKTQ2Q91EdMaqOlYjGjI40NXVO6a7/xhvTbAWSPj9UABF2Adk8IEMPpDBBzJgKHQUdB9QeKMpNCGCxmZEHp9BBgfa23snUtOY7rdmNU9Fd7AzoeH3QwEUYR+QwQcy+EAGH8iAodBW0H1A4Q0gFhXZmkgt0GRqAAAAGN4Y4w0AAAAAQI4ovAEAAAAAyBGFNwAAAAAAOaLwBgAAAAAgRxTeAAAAAADkiFnN0RRZllmpVLJyuVxbj09LBURCBh/I4EP0DNHbL2TwgQw+kMEHMvgQPUMWvP2NUHijKXTw9PT01LY7OzvDrc1HBh/I4EP0DNHbL2TwgQw+kMEHMvgQPUMpePsboas5mkJnrOpVKhWLhgw+kMGH6Bmit1/I4AMZfCCDD2TwIXqGcvD2N0LhjaZQN5HBtiMggw9k8CF6hujtFzL4QAYfyOADGXyInqE9ePsboas5mkJjM9RNRGesqmM1oiGDD2TwIXqG6O0XMvhABh/I4AMZfIieoSN4+xuh8EZTaEIEjc2IPD6DDD6QwYfoGaK3X8jgAxl8IIMPZPAheoa24O1vhK7mAAAAAADkiMIbAAAAAIAcUXgDAAAAAJAjCm8AAAAAAHJE4Q0AAAAAQI6Y1RwAgIjKZbPubrMpU8zGjjXr6tJip61uFSLitYShwmsJaIgr3miKLMts6tSp9tJLL6XP2o6GDD6QAUMh/D4oly2bPNls/fXNPvrR9Dlt600vmorXkg/h90MR8FrCEMkKug+44o2mKJVK1tPTU9vu7OwMtzYfGXwgA4ZC+H3Q3W1tEyfq3Unvdpb1bo8fbzZuXKtbN6zwWvIh/H4oAl5LGCKlgu4DrnijKcr9znZWKhWLhgw+kAFDIfw+UDfO/lcAtK3b0VS8lnwIvx+KgNcShki5oPuAwhtN0d5vfE//7QjI4AMZMBTC7wONnWxr63ubtnU7morXkg/h90MR8FrCEGkv6D6gqzmaoqOjI3UT0RkrHTzajoYMPpABQyH8PujqsmzSpLe7dba19W5rIiM0Fa8lH8LvhyLgtYQh0lHQfUDhjaZoa2tLYzMij88ggw9kwFAIvw/a261twoTesZNvzR6c3twW5KpAJLyWfAi/H4qA1xKGSFtB9wGFNwAAEenNrCYsCjRpEZzitYShwmsJaIgx3gAAAAAA5IjCGwAAAACAHFF4AwAAAACQIwpvAAAAAAByxORqaI5y2ay7uzbLpQWc5RIAAAAA5gWFN/JXLls2efKs6zpqyYlAxXeWZVYqlaxcLtfWFNRyB5GQwQcytF709gsZfCCDD2TwgQw+RM+QBW9/IxTeyF9399tFt2RZ77bWeQy03IR+AfT09NS2Ozs7w60vSAYfyNB60dsvZPCBDD6QwQcy+BA9Qyl4+xthjDfyp+7l1aK7Stu6PRCddatXqVQsGjL4QIbWi95+IYMPZPCBDD6QwYfoGcrB298IhTfypzHd/buHaFu3B6KuLoNtR0AGH8jQetHbL2TwgQw+kMEHMvgQPUN78PY3Qldz5K+rq3dMd/8x3ppgLRCNL1FXF511q443iYYMPpCh9aK3X8jgAxl8IIMPZPAheoaO4O1vhMIb+Wtv751ITWO635rVPBXdwc5eaVIHjS+JPMaEDD6QofWit1/I4AMZfCCDD2TwIXqGtuDtb4TCG82hIlsTqQWaTA0AAAAAhgJjvAEAAAAAyBGFNwAAAAAAOaLwBgAAAAAgRxTeAAAAAADkiMIbAAAAAIAcMat5AFmWWalUsnK5XFvLTtPsR0IGH8jgAxlaL3r7hQw+kMEHMvhABgyFrKD7gMI7AL3wenp6attaUD7aunZk8IEMPpCh9aK3X8jgAxl8IIMPZMBQKBV0H9DVPACd7alXqVQsGjL4QAYfyNB60dsvZPCBDD6QwQcyYCiUC7oPKLwDUBeLwbYjIIMPZPCBDK0Xvf1CBh/I4AMZfCADhkJ7QfcBXc0D0LgGdbHQ2Z7qOIdoyOADGXwgQ+tFb7+QwQcy+EAGH8iAodBR0H1A4R2AJhPQuIbIYxvI4AMZfCBD60Vvv5DBBzL4QAYfyICh0FbQfUBXcwAAAAAAckThDQAAAABAjii8AQAAAADIEYU3AAAAAAA5ovAGAAAAACBHzGoOAABao1w26+42mzLFbOxYs64uLdja6lYBADDkKLzRFFmWWalUsnK5XFuPT0sFREIGH8jgQ/QM0dtfiAzlsmWTJ1vbxIkKo/VjLJs0ydomTAhVfIffD2Rwgww+kKH1suDtb4TCG02hg6enp6e23dnZGW5tPjL4QAYfomeI3v5CZOjufrvolizr3R4/3mzcOIsi/H4ggxtk8IEMrVcK3v5GGOONptAZq3qVSsWiIYMPZPAheobo7S9EBnUvrxbdVdrW7YGE3w9kcIMMPpCh9crB298IhTeaQt1EBtuOgAw+kMGH6Bmit78QGTSmu3/XQW3r9kDC7wcyuEEGH8jQeu3B298IXc3RFBqboW4iOmNVHasRDRl8IIMP0TNEb38hMnR19Y7p7j/GWxOsBRJ+P5DBDTL4QIbW6wje/kYovNEUmhBBYzMij88ggw9k8CF6hujtL0SG9vbeidQ0pvutWc1T0R3sykb4/UAGN8jgAxlary14+xuh8AYAAK2hIlsTqQWaTA0AgHnBGG8AAAAAAHJE4Q0AAAAAQI4ovAEAAAAAyBGFNwAAAAAAOaLwBgAAAAAgR8xqjqbIssxKpZKVy+XaenxaKiASMvhABh+iZ4jefiGDD2TwgQw+kMGH6Bmy4O1vhMIbTaGDp6enp7bd2dkZbm0+MvhABh+iZ4jefiGDD2TwgQw+kMGH6BlKwdvfCF3N0RQ6Y1WvUqlYNGTwgQw+RM8Qvf1CBh/I4AMZfCCDD9EzlIO3vxEKbzSFuokMth0BGXwggw/RM0Rvv5DBBzL4QAYfyOBD9AztwdvfCF3N0RQam6FuIjpjVR2rEQ0ZfCCDD9EzRG+/kMEHMvhABh/I4EP0DB3B298IhTeaQhMiaGxG5PEZZPCBDD5EzxC9/UIGH8jgAxl8IIMP0TO0BW9/I3Q1BwAAAAAgRxTeAAAAAADkiMIbAAAAAIAcUXgDAAAAAJAjCm8AAAAAAIpaeJ988sn2/ve/P81Yt8wyy9jOO+9sjzzyyCwLqB944IG21FJL2SKLLGK77rqrvfDCC33u8/TTT9sOO+xgCy+8cHqcww8/3KZPn97kNEAA5bLZffeZ/f73vZ+1DQAAAKC4hfett96aiuo777zTrr/+eps2bZpts802ViqVavc59NBD7corr7TLLrss3X/KlCm2yy671L4+Y8aMVHS/+eabdvvtt9tFF11kF154oR199NFWFFmW2dSpU+2ll15Kn7UdDRkcKJctmzzZbP31zT760fQ5bQcrvsPvBzK4EL39QgYfyOADGXwggw/RM2TB2+9yHe9rrrmmz7YKZl2xvueee2zTTTe1V1991c4//3y7+OKL7cMf/nC6zwUXXGBdXV2pWN9oo43suuuus4cffthuuOEGW3bZZW299dazE044wY444gg79thjbdSoURadTkT09PTUtrWgfLR17cjgQHe3tU2cqN9mvdtZ1rs9frzZuHEWRfj9QAYXordfyOADGXwggw9k8CF6hlLw9rssvPtToS1LLrlk+qwCXFfBt9pqq9p91lhjDVtppZXsjjvuSIW3Pq+zzjqp6K7adtttbf/997eHHnrIxg1QUFQqlfRR9dprr6XPei595Kn6+HPzPDrTM3PmzD4vxvb2douEDA48+6zZQO3V7Wuv3fTmzMuxUIj9QAYXvLWfvw29yNAa3jJwPPQiQ2t4yjBc3ytNDdT+udk3bZmTa/f64e644472yiuv2J/+9Kd0m65077PPPn2KZNlwww1tiy22sFNPPdUmTJhgTz31lF177bW1r7/xxhvW0dFhV199tW233XazPJeuhB933HGz3K7n0zhxAAAAAAAGo7pz9913TxeQx4wZE+OKt8Z6//3vf68V3Xk68sgj7bDDDutzxXvFFVdM48tn9wMbirMiGs++9dZb28iRI+foe3RuRGd6dAJCZ3t0cqCtrc0iIYMDlYplF1xgbV//em9387Y2y045xdr22cds9OimN2dejoVC7AcyuOCt/fxtIEMrecvA8UCGVvKUYbi+V8oCtb/ac3pOuCi8DzroILvqqqvstttusxVWWKF2+3LLLZcmTdNV8MUXX7x2u2Y119eq97nrrrv6PF511vPqffobPXp0+uhPL+i5eVHPj7l9riKMVSdDi+n1tt9+Zh/6kNmUKWZjx5p1dQ3c/bypzZr74y70fngLGVrPY/v52xATGfLB8RATGYbecHyvNCpI++dmv4xo9dkMFd1XXHGF3XTTTbbKKqv0+fr666+fwtx4442127TcmJYP23jjjdO2Pj/44IP24osv1u6jM0O6cr3mmms2MQ0QgIpszXuwww69n52OlwEAAACKZMFWdy/XuOrf/va3aaa6559/Pt2+2GKL2UILLZQ+77vvvqlbuCZcUzF98MEHp2JbE6uJuoerwN5zzz3ttNNOS49x1FFHpcce6Ko2AAAAAADDpvA+55xz0ufNN9+8z+1aMuxzn/tc+v+ZZ55pI0aMsF133TX189eM5WeffXbtvgsssEDqpq5ZzFWQa1K1vffe244//vgmpwEAAAAAwFnhPScTqmtA/VlnnZU+GtHabprBHAAAAAAAb1o6xhsAAAAAgKJzMas5ACCYctmsu9vVDPkAAABeUXijqevxlcvlNHxAY/G9rsfXCBl8IIMD5bJlkydb28SJb68JP2mStU2YEKb4Dr8PyOAGGXwggw9k8CF6hix4+xuh8EZT6ODp6enpMy5fM9lHQgYfyOBAd/fbRbdkWe/2+PG9y9QFEH4fkMENMvhABh/I4EP0DKXg7W+EMd5oCp2xqqcZ6qMhgw9kcEDdy/tPjqlt3R5E+H1ABjfI4AMZfCCDD9EzlIO3vxEKbzSFuokMth0BGXwggwMa092/y5e2dXsQ4fcBGdwggw9k8IEMPkTP0B68/Y3Q1RxNobEZ6iaiM1bVsRrRkMEHMjjQ1dU7prv/GG9NsBZE+H1ABjfI4AMZfCCDD9EzdARvfyMU3mgKTYigsRmRx2eQwQcyONDe3juRmsZ0vzWreSq6A52RDr8PyOAGGXwggw9k8CF6hrbg7W+EwhsAMPdUZGsitSCTqQEAALQSY7wBAAAAAMgRhTcAAAAAADmi8AYAAAAAIEcU3gAAAAAA5IjCGwAAAACAHFF4AwAAAACQI5YTQ1NkWWalUsnK5bK1t7dbR0dHWqMvEjL4QAYfomeI3n4hgw9k8IEMPpDBh+gZsuDtb4TCG02hg6enp6e23dnZaYsuuqhFQgYfyOBD9AzR2y9k8IEMPpDBBzL4ED1DKXj7G6GrOZpCZ6zqVSoVi4YMPpDBh+gZordfyOADGXwggw9k8CF6hnLw9jdC4Y2mUDeRwbYjIIMPZPAheobo7Rcy+EAGH8jgAxl8iJ6hPXj7G6GrOZpCYzPUTURnrKpjNaIhgw9k8CF6hujtFzL4QAYfyOADGXyInqEjePsbofBGU2hCBI3NiDw+gww+kMGH6Bmit1/I4AMZfCCDD2TwIXqGtuDtb4Su5gAAAAAA5IjCGwAAAACAHFF4AwAAAACQIwpvAAAAAAByROENAAAAAECOmNUcAABgXpXLZt3dZlOmmI0da9bVpUVnW90qAIAzFN4BZFlmpVLJyuVybS07TbMfCRl8IIMPZGi96O0XMjhQLls2ebK1TZyoMFoDx7JJk6xtwoRQxXf4/UAGN8jgQ/QMWfD2N0LhHYBeeD09PbVtLSgfbV07MvhABh/I0HrR2y9kcKC7++2iW7Ksd3v8eLNx4yyK8PuBDG6QwYfoGUrB298IY7wD0NmeepVKxaIhgw9k8IEMrRe9/UIGB9S9vFp0V2lbtwcSfj+QwQ0y+BA9Qzl4+xuh8A5AXSwG246ADD6QwQcytF709gsZHNCY7v7dH7Wt2wMJvx/I4AYZfIieoT14+xuhq3kAGtegLhY621Md5xANGXwggw9kaL3o7RcyONDV1Tumu/8Yb02wFkj4/UAGN8jgQ/QMHcHb3wiFdwCaTEDjGiKPbSCDD2TwgQytF739QgYH2tt7J1LTmO63ZjVPRXewqzPh9wMZ3CCDD9EztAVvfyMU3gAAAPNKRbYmUgs0mRoAoPkY4w0AAAAAQI4ovAEAAAAAyBGFNwAAAAAAOaLwBgAAAAAgRxTeAAAAAADkiFnNAQAAhrEsy6xUKlm5XK6tmavlfIDh+FoqQobosoLuAwpvAACAYUxvcHt6emrbnZ2dhVs/F81RhNdSETJEVyroPqCrOQAAwDCmq0r1KpVKy9qC2IrwWipChujKBd0HFN4AAADDmLpyDrYNDKfXUhEyRNde0H1AV3MAAIBhTOMn1ZVTV5Wq4ymB4fpaKkKG6DoKug8ovAEAAIYxTVqk8ZNFGEOJ1irCa6kIGaJrK+g+oKs5AAAAAAA5ovAGAAAAACBHFN4AAAAAAOSIwhsAAAAAgBxReAMAAAAAkCMKbwAAAAAAckThDQAAAABAjii8AQAAAADIEYU3AAAAAAA5ovAGAAAAACBHFN4AAAAAAOSIwhsAAAAAgBxReAMAAAAAkCMKbwAAAAAAckThDQAAAABAjii8AQAAAADIEYU3AAAAAAA5ovAGAAAAACBHFN4AAAAAAOSIwhsAAAAAgBxReAMAAAAAkCMKbwAAAAAAckThDQAAAABAjii8AQAAAADIEYU3AAAAAAA5ovAGAAAAACBHFN4AAAAAAORowTwfPIosy9Ln1157LffnmjZtmr3xxhvpuUaOHJn78wFecSwAb+N4AN7G8QD04ljwr1o/VuvJwVB4m9nrr7+ePq+44oqtbgoAAAAAIFg9udhiiw16n7ZsTsrzgps5c6ZNmTLFFl10UWtra8v9rIgK/GeeecbGjBmT63MBnnEsAG/jeADexvEA9OJY8E+ltIrusWPH2ogRg4/i5oq3BrqPGGErrLBCU59TBw8HEMCxANTjeADexvEA9OJY8G12V7qrmFwNAAAAAIAcUXgDAAAAAJAjCu8mGz16tB1zzDHpMzCccSwAb+N4AN7G8QD04lgoFiZXAwAAAAAgR1zxBgAAAAAgRxTeAAAAAADkiMIbAAAAAIAcUXj3c84559i6665bWy9v4403tj/84Q+1rz///PO255572nLLLWcdHR32vve9z37961/3eYyVV17Z2tra+nyccsopta8fe+yxs3xdH3q8qnPPPdfGjx9vSyyxRPrYaqut7K677urzPBqef/TRR9vyyy9vCy20ULrPP//5z1x/Phg+vBwL9S655JL09Z133rnP7RwLGE7HwyuvvGIHHnhger1rwp33vOc9dvXVV/e5z1lnnZWer7293T7wgQ/M8vcDKMKxMGnSJFt99dXT7/0VV1zRDj30UCuXy33uw7GA6MeDXHvttbbRRhvZoosuau94xzts1113tZ6enj73ueWWW9Lj6+/Cu971LrvwwgtnaS/HQ4tpcjW87Xe/+132+9//Pnv00UezRx55JPvGN76RjRw5Mvv73/+evr711ltn73//+7O//OUv2eOPP56dcMIJ2YgRI7J777239hidnZ3Z8ccfnz333HO1j6lTp9a+/vrrr/f5mj7WXHPNbO+9967dZ/fdd8/OOuus7L777su6u7uzz33uc9liiy2W/etf/6rd55RTTkm3/eY3v8n+9re/ZTvuuGO2yiqrZP/73/+a9vNCcXk5FqqefPLJ7J3vfGc2fvz4bKeddurzNY4FDJfjoVKpZBtssEG2/fbbZ3/605/ScXHLLbdk999/f+0+l1xySTZq1KjsJz/5SfbQQw9l++23X7b44otnL7zwQtN+XiguL8fCz3/+82z06NHps46Da6+9Nlt++eWzQw89tHYfjgUU4Xh44okn0mv9yCOPzB577LHsnnvuyTbddNNs3Lhxfe6z8MILZ4cddlj28MMPZz/4wQ+yBRZYILvmmmtq9+F4aD0K7zmwxBJLZOedd176f0dHR/bTn/60z9eXXHLJ7Nxzz+1zAJ155plz/Ph6w6RzILfddlvD+0yfPj1bdNFFs4suuihtz5w5M1tuueWy008/vXafV155JR2Yv/jFL+YqH+D9WNDr/4Mf/GB6br3xqi+8ORYwnI6Hc845J1t11VWzN998s+H3bbjhhtmBBx5Y254xY0Y2duzY7OSTT57j5wa8Hwt6jX/4wx/ucz8VHZtsskltm2MBRTgeLrvssmzBBRdMr9/6gr+tra32t+BrX/tattZaa/X5vt122y3bdttta9scD61HV/NBzJgxI3VtLZVKqeuIfPCDH7RLL73UXn75ZZs5c2b6uro1bb755n2+V11EllpqKRs3bpydfvrpNn369IbPc95556Wugupa3sgbb7xh06ZNsyWXXDJtP/nkk6n7irrUVi222GKp28gdd9wxBOkBP8fC8ccfb8sss4ztu+++s3wPxwKG0/Hwu9/9Lj2nupovu+yytvbaa9tJJ52U2iRvvvmm3XPPPX2OhxEjRqRtjgcU6VjQ8+i1Xu0q+8QTT6QhF9tvv33a5lhAUY6H9ddfP712L7jggvQcr776qv3f//1fei2PHDky3Uev6frXumy77ba11zrHgxOtrvw9euCBB9IZKnXRUPdVdSGp+u9//5tts8026cyrzj6NGTMmdW+qd8YZZ2Q333xz6vKqqxPqxlHf9ameusLqzNipp546aJv233//dJWj2nX2z3/+c2rDlClT+tzvk5/8ZPapT31qPtIDvo6FP/7xj6mL+b///e+03f+KN8cChtPxsPrqq6feHJ///Oezv/71r6nroK6eHHvssenrzz77bGrD7bff3uf7Dj/88HS1AyjKsSDf+973UrdePY+e70tf+lLtaxwLKNLxoCFFyyyzTHoOPdbGG2+cHrvq3e9+d3bSSSf1+R61Q/d94403OB6coPAegMbQ/fOf/0xvar7+9a9nSy+9dBoLIQcddFB6gd5www2p65Pe7Ogg00HXyPnnn58OtnK5PMvXLr744vS1559/vuH3qwuI/ujogKyi2MBwOBZee+21bOWVV86uvvrq2m0U3hiux0P1zdWKK66Yhl/Uv2nTcAvhzRWGy7GgQmXZZZdNXXb12Jdffnk6NjRWVjgWUJTjQWO+9btfr12NDb/11luzzTbbLNtyyy3TcDuh8I6BwnsO6IU9YcKENKGBXrTVCRPqv/7FL36x4ffr/vq+f/zjH7N8TeOTdt5554bfq3GrOkDvvvvuPrdrggY9piZfq6fJFr785S/PRTrA77Gg17furzO81Q+NadKH/q92cCxgOP1t0Otaj1tPJ6b0OHrzpw8dG1dccUWf++y1115p0kGgKMfChz70oeyrX/1qn9v+7//+L1tooYXS2FWOBRTleDjqqKPSpJr1nnnmmXSfO+64I21r4tlDDjmkz300iZqusAvHgw+M8Z4DGpNRqVTSOOvqmIh6CyywQLpPI/fff3/6Ho1R7T829eabbx5w3KqcdtppdsIJJ9g111xjG2ywQZ+vrbLKKmlpghtvvLF222uvvWZ/+ctfauNKgOjHwhprrGEPPvhg+r7qx4477mhbbLFF+r+Wj+FYwHD627DJJpvYY4891udxH3300bS02KhRo9KHxgPWHw+6r7Y5HlCkY0HPNdDziC4scSygKMfDYK/16uPoNV3/Wpfrr7++9lrneHCi1ZW/N+oioi4cWppC3UC0ratr1113XZo58F3velc6q6RlAXQm6zvf+U76enU8h7pwaGZCdSfRlbif/exn2Tve8Y50Rqk/ncHSbIL1XQbrl0fSlP+/+tWv+iwvoCU26u+jcSC//e1vU1vV/ZYllFC0Y6G//l3NhWMBw+V4ePrpp9MKF+q+qKVrrrrqqjTu79vf/nbtPhr3rXHgF154YVpWRldedHwMNqQJiHYsHHPMMelY0OoVWkpJz7/aaqv1GWLEsYAiHA833nhj+p7jjjsuLVum5cQ0W7lmQ1c38vrlxNR1XMsQa0nigZYT43hoLQrvfjRhjV7IKnr1wld3EB08VXrB77LLLumNjl7g6667bp9lAnQwfOADH0jdw9vb27Ourq405qL/uCV1g1phhRXSen8DURt0XqT/h/7QVGlcx7e+9a00xkkHktqqN2JAkY6FOSm8ORYwnI4HvVHTY+m1rkk3TzzxxFkKE63hutJKK6X2avzenXfeOaQ/DwxfXo6FadOmpfGyKrb1OBrffcABB/SZcEo4FlCE40EnmLRutyZx0/Ooe7gK7P7zHqy33nqpLfrbcMEFF8zSXo6H1mrTP62+6g4AAAAAQFExxhsAAAAAgBxReAMAAAAAkCMKbwAAAAAAckThDQAAAABAjii8AQAAAADIEYU3AAAAAAA5ovAGAAAAACBHFN4AAAAAAOSIwhsAAMyzz33uc7bzzjvXtjfffHObOHFiS9sEAIA3FN4AADTRjBkz7IMf/KDtsssufW5/9dVXbcUVV7RvfvObg37/Y489Zvvss4+tsMIKNnr0aFtllVXsM5/5jP31r381Dy6//HI74YQThvQxjz32WFtvvfWG9DEBAGgmCm8AAJpogQUWsAsvvNCuueYa+/nPf167/eCDD7Yll1zSjjnmmIbfq+J6/fXXt0cffdR+/OMf28MPP2xXXHGFrbHGGvaVr3wl13ZPmzZtju6nDIsuumiubQEAIBoKbwAAmuw973mPnXLKKanYfu655+y3v/2tXXLJJfbTn/7URo0aNeD3ZFmWunW/+93vtj/+8Y+2ww472GqrrZauBKtY12NUPfjgg/bhD3/YFlpoIVtqqaVswoQJNnXq1NrXZ86caccff3ztqrkeQycCqnp6eqytrc0uvfRS22yzzay9vT2dJNDV+sMOO8wWX3zx9Lhf+9rXUrvq9e9qvvLKK9tJJ51kn//851NBvtJKK9nkyZP7fM8RRxyRfiYLL7ywrbrqqvatb32rVujrJMVxxx1nf/vb31Kb9KHb5JVXXrEvfOEL9o53vMPGjBmTMut+AAB4Q+ENAEALqOh+73vfa3vuuWcqjI8++ui03cj9999vDz30ULqyPWLErH++VQxLqVSybbfd1pZYYgm7++677bLLLrMbbrjBDjrooNp9v/e979kZZ5xh3/nOd+yBBx5I999xxx3tn//8Z5/H/PrXv26HHHKIdXd3p/voe1T0/uQnP7E//elP9vLLL6cr7rOj79tggw3svvvuswMOOMD2339/e+SRR2pfV0Gux9UVfLXt3HPPtTPPPDN9bbfddkuZ11prrXSSQh+6TT75yU/aiy++aH/4wx/snnvusfe973225ZZbpnYBAOBKBgAAWqK7u1uXi7N11lknmzZt2qD3vfTSS9N977333kHvN3ny5GyJJZbIpk6dWrvt97//fTZixIjs+eefT9tjx47NTjzxxD7f9/73vz874IAD0v+ffPLJ9FyTJk3qc5/ll18+O+2002rbavMKK6yQ7bTTTrXbNttss+yQQw6pbXd2dmaf/exna9szZ87Mlllmmeycc85pmOH000/P1l9//dr2Mccck733ve/tc58//vGP2ZgxY7Jyudzn9tVWWy378Y9/POjPCACAZluw1YU/AADDla4cq3v1k08+af/6179St+xG+nfpbkRXp3XlvKOjo3bbJptskrqX6yqzup9PmTIl3VZP2/27aesqdf3kb7ra/IEPfKB224ILLpjuM7u2rbvuurX/q6v4csstl65UV6lL+/e//317/PHHU5f46dOnp67jg1FbdV91ea/3v//9Lz0OAACe0NUcAIAWuP3221N36quuuso23HBD23fffQctYDUGWv7xj380rY31xfv8GDlyZJ9tFd86ESB33HGH7bHHHrb99tunn4W6o2tm9zfffHPQx1TRvfzyy6cu+PUfOrlw+OGHD0m7AQAYKhTeAAA02RtvvJEmStNY5y222MLOP/98u+uuu+xHP/pRw+/RBGhrrrlmGi9dLVrraaIx6erqSleDNda76s9//nMaF7766qunK8ljx45Nt9XTth6/kcUWWywVun/5y19qt+nKtMZWz+8JiM7OzlRs6+q5Jo976qmn+txHE85pYrd6Gs/9/PPPp6vu73rXu/p8LL300vPVJgAAhhqFNwAATXbkkUemq9ua2VzUxVwTnWmWcM0oPhBdJb7gggvSUmLjx4+3q6++2p544ok0OdqJJ55oO+20U7qfrh5rFvK9997b/v73v9vNN9+cJnLTJG7LLrtsuo+uCJ966qmpi7euEGsSNV0t1kRqg9HX1ebf/OY36cq7JkqrFvzzSoX2008/nWZ1VxdxdTnvP2Gbfj7qjq82vvTSS1apVGyrrbayjTfe2HbeeWe77rrr0s9NRbwKeC9rmgMAUEXhDQBAE91666121llnpSJa47urvvjFL9oHP/jBQbucq0u6ikpd1d1vv/3S1W3NRq7ZzidNmpTuo8e89tpr08ze73//++0Tn/hEmun7hz/8Ye1xvvzlL6dlwTRb+DrrrJOWEvvd736XiuDB6P4q4FXUq+jVbOQf//jH5+vnofYfeuihadZ1XdVX8azlxOrtuuuu9pGPfCT1DtDSYb/4xS/SiQidfNh0001tn332SV3xP/3pT6er5dUTDAAAeNGmGdZa3QgAAAAAAIqKK94AAAAAAOSIwhsAAAAAgBxReAMAAAAAkCMKbwAAAAAAckThDQAAAABAjii8AQAAAADIEYU3AAAAAAA5ovAGAAAAACBHFN4AAAAAAOSIwhsAAAAAgBxReAMAAAAAkCMKbwAAAAAALD//DyFCfAoby60fAAAAAElFTkSuQmCC", "text/plain": ["<Figure size 1000x800 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "\n", "# Scatter plot of all points\n", "plt.figure(figsize=(10, 8))\n", "sc = plt.scatter(results_df['x'], results_df['y'], c=results_df['pile_probability'],\n", "                 cmap='coolwarm', s=10, alpha=0.7)\n", "plt.colorbar(sc, label=\"Pile Probability\")\n", "plt.title(\"Pile Probability Distribution (All Points)\")\n", "plt.xlabel(\"X Coordinate\")\n", "plt.ylabel(\"Y Coordinate\")\n", "plt.grid(True)\n", "plt.tight_layout()\n", "plt.show()\n", "\n", "# Hotspot overlay\n", "plt.figure(figsize=(10, 8))\n", "sns.scatterplot(data=results_df, x='x', y='y', color='lightgray', s=10, label='All Points')\n", "sns.scatterplot(data=hotspots, x='x', y='y', color='red', s=20, label='Hotspots')\n", "plt.title(\"Hotspots (Top 5% Pile Probabilities)\")\n", "plt.xlabel(\"X Coordinate\")\n", "plt.ylabel(\"Y Coordinate\")\n", "plt.legend()\n", "plt.grid(True)\n", "plt.tight_layout()\n", "plt.show()\n"]}, {"cell_type": "markdown", "id": "dc043dbd", "metadata": {}, "source": ["## Step 4: Zone Analysis"]}, {"cell_type": "code", "execution_count": 55, "id": "239f5d34", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Zone Comparison:\n", "NE:  201 points | Avg: 0.04055 | Max: 0.04174 | Std: 0.000370\n", "NW:  225 points | Avg: 0.04054 | Max: 0.04155 | Std: 0.000374\n", "SE:  215 points | Avg: 0.04055 | Max: 0.04139 | Std: 0.000324\n", "SW:  239 points | Avg: 0.04047 | Max: 0.04173 | Std: 0.000415\n", "\n", "Highest average pile probability zone: SE\n"]}], "source": ["# Divide site into quadrants\n", "x_coords = results_df['x']\n", "y_coords = results_df['y']\n", "x_median = x_coords.median()\n", "y_median = y_coords.median()\n", "\n", "# Create zones\n", "zones = {\n", "    'NE': results_df[(x_coords > x_median) & (y_coords > y_median)],\n", "    'NW': results_df[(x_coords <= x_median) & (y_coords > y_median)],\n", "    'SE': results_df[(x_coords > x_median) & (y_coords <= y_median)],\n", "    'SW': results_df[(x_coords <= x_median) & (y_coords <= y_median)]\n", "}\n", "\n", "# Zone-wise summary\n", "print(\"Zone Comparison:\")\n", "for zone_name, zone_data in zones.items():\n", "    if not zone_data.empty:\n", "        avg_prob = zone_data['pile_probability'].mean(skipna=True)\n", "        max_prob = zone_data['pile_probability'].max(skipna=True)\n", "        std_prob = zone_data['pile_probability'].std(skipna=True)\n", "        \n", "        print(f\"{zone_name}: {len(zone_data):4d} points | \"\n", "              f\"Avg: {avg_prob:.5f} | Max: {max_prob:.5f} | Std: {std_prob:.6f}\")\n", "    else:\n", "        print(f\"{zone_name}:    0 points | No data available.\")\n", "\n", "# Find best zone by average pile probability\n", "best_zone = max(zones.keys(), key=lambda z: zones[z]['pile_probability'].mean(skipna=True) if not zones[z].empty else 0)\n", "print(f\"\\nHighest average pile probability zone: {best_zone}\")\n"]}, {"cell_type": "markdown", "id": "2d55c585", "metadata": {}, "source": ["## Step 5: Hotspot Identification"]}, {"cell_type": "code", "execution_count": 56, "id": "cc404ddf", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<PERSON><PERSON><PERSON><PERSON> for top 5%: 0.04115\n", "Number of hotspot points: 44\n", "\n", "Top 10 hotspot points (x, y, probability):\n", "          x            y  pile_probability\n", "385791.4523 3529385.3306          0.041739\n", "385761.4523 3529225.3306          0.041732\n", "385781.4523 3529410.3306          0.041719\n", "385786.4523 3529385.3306          0.041629\n", "385736.4523 3529385.3306          0.041546\n", "385741.4523 3529440.3306          0.041452\n", "385736.4523 3529260.3306          0.041409\n", "385771.4523 3529185.3306          0.041393\n", "385776.4523 3529435.3306          0.041354\n", "385731.4523 3529435.3306          0.041316\n", "\n", "Hotspot spread: X range = 80.0 m, Y range = 255.0 m\n", "Hotspots are spread across the site.\n"]}], "source": ["# Identify top 5% high-probability locations\n", "threshold = np.percentile(results_df['pile_probability'], 95)\n", "hotspots = results_df[results_df['pile_probability'] > threshold].copy()\n", "\n", "print(f\"Threshold for top 5%: {threshold:.5f}\")\n", "print(f\"Number of hotspot points: {len(hotspots)}\")\n", "\n", "if not hotspots.empty:\n", "    # Show top 10 points with highest probability\n", "    print(\"\\nTop 10 hotspot points (x, y, probability):\")\n", "    print(hotspots.nlargest(10, 'pile_probability')[['x', 'y', 'pile_probability']].to_string(index=False))\n", "    \n", "    # Check spatial distribution\n", "    if len(hotspots) > 1:\n", "        x_range = hotspots['x'].max() - hotspots['x'].min()\n", "        y_range = hotspots['y'].max() - hotspots['y'].min()\n", "        \n", "        print(f\"\\nHotspot spread: X range = {x_range:.1f} m, Y range = {y_range:.1f} m\")\n", "        \n", "        if x_range < 50 and y_range < 50:\n", "            print(\"Hotspots are close together.\")\n", "        else:\n", "            print(\"Hotspots are spread across the site.\")"]}, {"cell_type": "markdown", "id": "4501e2a8", "metadata": {}, "source": ["## Step 6: Domain Analysis\n", "\n", "### Training vs Inference Comparison\n", "\n", "**Training Data:**\n", "- Biased toward pile-containing areas (80% positive)\n", "- Normalized coordinates\n", "- Specific construction types\n", "\n", "**Current Site:**\n", "- Very high point density\n", "- Large coordinate values (UTM system)\n", "- Uniform low confidence across site\n", "- Subtle but consistent spatial patterns\n", "\n"]}, {"cell_type": "code", "execution_count": 57, "id": "7df7a588", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["## Computed Metrics\n", "- Point cloud density: 1571 points/m²\n", "- Site area: 22633 m²\n", "- Total points: 35,565,352\n", "\n", "## Site Dimensions\n", "- X Range: 85 m\n", "- Y Range: 265 m\n"]}], "source": ["# Basic stats\n", "total_points = 35_565_352\n", "area = 22_633  # m²\n", "density = total_points / area\n", "\n", "print(\"## Computed Metrics\")\n", "print(f\"- Point cloud density: {density:.0f} points/m²\")\n", "print(f\"- Site area: {area:.0f} m²\")\n", "print(f\"- Total points: {total_points:,}\")\n", "\n", "# Coordinate range\n", "x_range = results_df['x'].max() - results_df['x'].min()\n", "y_range = results_df['y'].max() - results_df['y'].min()\n", "\n", "print(\"\\n## Site Dimensions\")\n", "print(f\"- X Range: {x_range:.0f} m\")\n", "print(f\"- Y Range: {y_range:.0f} m\")"]}, {"cell_type": "markdown", "id": "77d29d0e", "metadata": {}, "source": ["## Final Conclusion: \n", "\n", "DGCNN model is likely not generalizing due to biased training data , need to investigate further  "]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 5}