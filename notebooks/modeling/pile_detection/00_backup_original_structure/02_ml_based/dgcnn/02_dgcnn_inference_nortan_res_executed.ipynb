{"cells": [{"cell_type": "markdown", "id": "4e86c2a3", "metadata": {"papermill": {"duration": 0.010954, "end_time": "2025-08-01T11:05:09.517599", "exception": false, "start_time": "2025-08-01T11:05:09.506645", "status": "completed"}, "tags": []}, "source": ["# DGCNN Site Inference Pipeline\n", "This notebook applies the trained PointNet++ model to a new construction site\n", "with different data sources (DWG files instead of KML regions).\n", "\n", "**Workflow:**\n", "1. Simple exploratory pipeline to test DGCNN on multiple sites.\n", "2. Tests our trained DGCNN model on 3 different construction sites.\n", "\n", "**Author**: <PERSON><PERSON><PERSON>  \n", "**Date**: July 2025  \n", "**Project**: As-Built Foundation Analysis - Site Transfer"]}, {"cell_type": "code", "execution_count": 1, "id": "parameters", "metadata": {"execution": {"iopub.execute_input": "2025-08-01T11:05:09.525489Z", "iopub.status.busy": "2025-08-01T11:05:09.525213Z", "iopub.status.idle": "2025-08-01T11:05:09.533064Z", "shell.execute_reply": "2025-08-01T11:05:09.532698Z"}, "papermill": {"duration": 0.013152, "end_time": "2025-08-01T11:05:09.534287", "exception": false, "start_time": "2025-08-01T11:05:09.521135", "status": "completed"}, "tags": ["parameters"]}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["DGCNN Multi-Site Inference Pipeline\n", "Site Name: northan_res\n"]}], "source": ["# Model and processing parameters\n", "SITE_NAME = \"northan_res\"  # Will be overridden by papermill\n", "POINT_CLOUD_PATH = \"../../../../../data/raw/nortan_res/pointcloud/Block_11_2m.las\"  # Will be overridden\n", "\n", "RUN_NAME = f\"inference_{SITE_NAME}\"\n", "OUTPUT_DIR = \"output_runs/dgcnn_inference\"\n", "\n", "MODEL_PATH = \"best_dgcnn.pth\"\n", "CONFIDENCE_THRESHOLD = 0.95\n", "BATCH_SIZE = 16\n", "GRID_SPACING = 5.0\n", "PATCH_SIZE = 3.0\n", "NUM_POINTS = 256\n", "K_NEIGHBORS = 20\n", "\n", "EXPERIMENT_NAME = \"dgcnn_inference\"\n", "\n", "# MLflow configuration\n", "\n", "print(\"DGCNN Multi-Site Inference Pipeline\")\n", "print(f\"Site Name: {SITE_NAME}\")"]}, {"cell_type": "code", "execution_count": 2, "id": "a263b073", "metadata": {"execution": {"iopub.execute_input": "2025-08-01T11:05:09.564290Z", "iopub.status.busy": "2025-08-01T11:05:09.564131Z", "iopub.status.idle": "2025-08-01T11:05:09.566063Z", "shell.execute_reply": "2025-08-01T11:05:09.565825Z"}, "papermill": {"duration": 0.005581, "end_time": "2025-08-01T11:05:09.566915", "exception": false, "start_time": "2025-08-01T11:05:09.561334", "status": "completed"}, "tags": ["injected-parameters"]}, "outputs": [], "source": ["# Parameters\n", "SITE_NAME = \"nortan_res\"\n", "POINT_CLOUD_PATH = \"../../../../../data/raw/nortan_res/pointcloud/Block_11_2m.las\"\n", "OUTPUT_DIR = \"output_runs/dgcnn_inference/nortan_res\"\n", "RUN_NAME = \"inference_nortan_res\"\n"]}, {"cell_type": "code", "execution_count": 3, "id": "6b291543", "metadata": {"execution": {"iopub.execute_input": "2025-08-01T11:05:09.571062Z", "iopub.status.busy": "2025-08-01T11:05:09.570955Z", "iopub.status.idle": "2025-08-01T11:05:12.803464Z", "shell.execute_reply": "2025-08-01T11:05:12.802360Z"}, "papermill": {"duration": 3.237245, "end_time": "2025-08-01T11:05:12.805998", "exception": false, "start_time": "2025-08-01T11:05:09.568753", "status": "completed"}, "tags": []}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages/torchvision/io/image.py:14: UserWarning: Failed to load image Python extension: 'dlopen(/Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages/torchvision/image.so, 0x0006): Library not loaded: @rpath/libjpeg.9.dylib\n", "  Referenced from: <EB3FF92A-5EB1-3EE8-AF8B-5923C1265422> /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages/torchvision/image.so\n", "  Reason: tried: '/Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages/torchvision/../../../libjpeg.9.dylib' (no such file), '/Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages/torchvision/../../../libjpeg.9.dylib' (no such file), '/Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/lib-dynload/../../libjpeg.9.dylib' (no such file), '/Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/bin/../lib/libjpeg.9.dylib' (no such file)'If you don't plan on using image functionality from `torchvision.io`, you can ignore this warning. Otherwise, there might be something wrong with your environment. Did you have `libjpeg` or `libpng` installed before building `torchvision` from source?\n", "  warn(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Using device: cpu\n"]}], "source": ["import torch\n", "import torch.nn as nn\n", "import torch.nn.functional as F\n", "import numpy as np\n", "import pandas as pd\n", "from pathlib import Path\n", "import matplotlib.pyplot as plt\n", "import mlflow\n", "import mlflow.pytorch\n", "import os\n", "from datetime import datetime\n", "import warnings\n", "warnings.filterwarnings('ignore')\n", "\n", "device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')\n", "print(f\"Using device: {device}\")"]}, {"cell_type": "code", "execution_count": 4, "id": "setup_output_dir", "metadata": {"execution": {"iopub.execute_input": "2025-08-01T11:05:12.813879Z", "iopub.status.busy": "2025-08-01T11:05:12.812364Z", "iopub.status.idle": "2025-08-01T11:05:12.908225Z", "shell.execute_reply": "2025-08-01T11:05:12.907671Z"}, "papermill": {"duration": 0.100952, "end_time": "2025-08-01T11:05:12.909279", "exception": false, "start_time": "2025-08-01T11:05:12.808327", "status": "completed"}, "tags": []}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Output directory created: output_runs/dgcnn_inference/nortan_res\n", "MLflow experiment initialized\n"]}], "source": ["# Setup output directory and initialize MLflow\n", "os.makedirs(OUTPUT_DIR, exist_ok=True)\n", "print(f\"Output directory created: {OUTPUT_DIR}\")\n", "\n", "if mlflow.active_run() is not None:\n", "    print(f\"Ending previous active run: {mlflow.active_run().info.run_id}\")\n", "    mlflow.end_run()\n", "\n", "mlflow.set_experiment(EXPERIMENT_NAME)\n", "mlflow.start_run(run_name=RUN_NAME)\n", "\n", "# Log parameters\n", "mlflow.log_param(\"site_name\", SITE_NAME)\n", "mlflow.log_param(\"model_type\", \"DGCNN\")\n", "mlflow.log_param(\"patch_size\", PATCH_SIZE)\n", "mlflow.log_param(\"num_points\", NUM_POINTS)\n", "mlflow.log_param(\"k_neighbors\", K_NEIGHBORS)\n", "mlflow.log_param(\"confidence_threshold\", CONFIDENCE_THRESHOLD)\n", "mlflow.log_param(\"batch_size\", BATCH_SIZE)\n", "mlflow.log_param(\"grid_spacing\", GRID_SPACING)\n", "mlflow.log_param(\"model_path\", MODEL_PATH)\n", "\n", "print(\"MLflow experiment initialized\")"]}, {"cell_type": "markdown", "id": "085ade32", "metadata": {"papermill": {"duration": 0.003285, "end_time": "2025-08-01T11:05:12.914793", "exception": false, "start_time": "2025-08-01T11:05:12.911508", "status": "completed"}, "tags": []}, "source": ["## DGCNN Architecture (same as training)"]}, {"cell_type": "code", "execution_count": 5, "id": "f547b445", "metadata": {"execution": {"iopub.execute_input": "2025-08-01T11:05:12.920189Z", "iopub.status.busy": "2025-08-01T11:05:12.919968Z", "iopub.status.idle": "2025-08-01T11:05:12.931440Z", "shell.execute_reply": "2025-08-01T11:05:12.930433Z"}, "papermill": {"duration": 0.016918, "end_time": "2025-08-01T11:05:12.934052", "exception": false, "start_time": "2025-08-01T11:05:12.917134", "status": "completed"}, "tags": []}, "outputs": [], "source": ["def knn(x, k):\n", "    \"\"\"Find k nearest neighbors\"\"\"\n", "    inner = -2 * torch.matmul(x.transpose(2, 1), x)\n", "    xx = torch.sum(x**2, dim=1, keepdim=True)\n", "    pairwise_distance = -xx - inner - xx.transpose(2, 1)\n", "    idx = pairwise_distance.topk(k=k, dim=-1)[1]\n", "    return idx\n", "\n", "def get_graph_feature(x, k=20, idx=None):\n", "    \"\"\"Create graph features from k-NN\"\"\"\n", "    batch_size = x.size(0)\n", "    num_points = x.size(2)\n", "    x = x.view(batch_size, -1, num_points)\n", "    \n", "    if idx is None:\n", "        idx = knn(x, k=k)\n", "    \n", "    device = x.device\n", "    idx_base = torch.arange(0, batch_size, device=device).view(-1, 1, 1) * num_points\n", "    idx = idx + idx_base\n", "    idx = idx.view(-1)\n", "    \n", "    _, num_dims, _ = x.size()\n", "    x = x.transpose(2, 1).contiguous()\n", "    feature = x.view(batch_size * num_points, -1)[idx, :]\n", "    feature = feature.view(batch_size, num_points, k, num_dims)\n", "    x = x.view(batch_size, num_points, 1, num_dims).repeat(1, 1, k, 1)\n", "    \n", "    feature = torch.cat((feature - x, x), dim=3).permute(0, 3, 1, 2).contiguous()\n", "    return feature"]}, {"cell_type": "code", "execution_count": 6, "id": "bf8dd055", "metadata": {"execution": {"iopub.execute_input": "2025-08-01T11:05:12.944274Z", "iopub.status.busy": "2025-08-01T11:05:12.944104Z", "iopub.status.idle": "2025-08-01T11:05:12.950258Z", "shell.execute_reply": "2025-08-01T11:05:12.949957Z"}, "papermill": {"duration": 0.010056, "end_time": "2025-08-01T11:05:12.951121", "exception": false, "start_time": "2025-08-01T11:05:12.941065", "status": "completed"}, "tags": []}, "outputs": [], "source": ["class EdgeConv(nn.Module):\n", "    def __init__(self, in_channels, out_channels, k=20):\n", "        super(<PERSON><PERSON><PERSON><PERSON>, self).__init__()\n", "        self.k = k\n", "        self.conv = nn.Sequential(\n", "            nn.Conv2d(in_channels * 2, out_channels, kernel_size=1, bias=False),\n", "            nn.BatchNorm2d(out_channels),\n", "            nn.LeakyReLU(negative_slope=0.2)\n", "        )\n", "    \n", "    def forward(self, x):\n", "        x = get_graph_feature(x, k=self.k)\n", "        x = self.conv(x)\n", "        x = x.max(dim=-1, keepdim=False)[0]\n", "        return x"]}, {"cell_type": "code", "execution_count": 7, "id": "b2b14a92", "metadata": {"execution": {"iopub.execute_input": "2025-08-01T11:05:12.955679Z", "iopub.status.busy": "2025-08-01T11:05:12.955516Z", "iopub.status.idle": "2025-08-01T11:05:12.959501Z", "shell.execute_reply": "2025-08-01T11:05:12.959227Z"}, "papermill": {"duration": 0.007202, "end_time": "2025-08-01T11:05:12.960376", "exception": false, "start_time": "2025-08-01T11:05:12.953174", "status": "completed"}, "tags": []}, "outputs": [], "source": ["class DGCNN(nn.Module):\n", "    \"\"\"Dynamic Graph CNN - exact copy from training\"\"\"\n", "    def __init__(self, num_classes=2, k=20):\n", "        super(DGC<PERSON><PERSON>, self).__init__()\n", "        self.k = k\n", "        \n", "        # EdgeConv layers - getting progressively more abstract\n", "        self.conv1 = EdgeConv(3, 64, k)     # 3D coords -> 64 features\n", "        self.conv2 = EdgeConv(64, 64, k)    # 64 -> 64\n", "        self.conv3 = EdgeConv(64, 128, k)   # 64 -> 128\n", "        self.conv4 = EdgeConv(128, 256, k)  # 128 -> 256\n", "        \n", "        # Global feature aggregation\n", "        self.conv5 = nn.Sequential(\n", "            nn.Conv1d(512, 1024, kernel_size=1, bias=False),  # 64+64+128+256 = 512\n", "            nn.<PERSON>ch<PERSON>orm1d(1024),\n", "            nn.LeakyReLU(negative_slope=0.2)\n", "        )\n", "        \n", "        # Classification head\n", "        self.classifier = nn.Sequential(\n", "            nn.<PERSON>(1024, 512),\n", "            nn.BatchNorm1d(512),\n", "            nn.LeakyReLU(negative_slope=0.2),\n", "            nn.Dropout(0.4),\n", "            nn.<PERSON><PERSON>(512, 256),\n", "            nn.BatchNorm1d(256),\n", "            nn.LeakyReLU(negative_slope=0.2),\n", "            nn.Dropout(0.4),\n", "            nn.Linear(256, num_classes)\n", "        )\n", "    \n", "    def forward(self, x):\n", "        batch_size = x.size(0)\n", "        x = x.transpose(2, 1)  # (B, 3, N)\n", "        \n", "        # Multi-scale EdgeConv features\n", "        x1 = self.conv1(x)   # (B, 64, N)\n", "        x2 = self.conv2(x1)  # (B, 64, N)\n", "        x3 = self.conv3(x2)  # (B, 128, N)\n", "        x4 = self.conv4(x3)  # (B, 256, N)\n", "        \n", "        # Concatenate all features - multi-scale representation!\n", "        x = torch.cat((x1, x2, x3, x4), dim=1)  # (B, 512, N)\n", "        \n", "        # Global feature extraction\n", "        x = self.conv5(x)  # (B, 1024, N)\n", "        x = F.adaptive_max_pool1d(x, 1).view(batch_size, -1)  # (B, 1024)\n", "        \n", "        # Final classification\n", "        x = self.classifier(x)\n", "        \n", "        return x"]}, {"cell_type": "markdown", "id": "6e12b384", "metadata": {"papermill": {"duration": 0.001648, "end_time": "2025-08-01T11:05:12.963783", "exception": false, "start_time": "2025-08-01T11:05:12.962135", "status": "completed"}, "tags": []}, "source": ["## Load Data and DGCNN Model\n"]}, {"cell_type": "code", "execution_count": 8, "id": "1035c7b5", "metadata": {"execution": {"iopub.execute_input": "2025-08-01T11:05:12.967748Z", "iopub.status.busy": "2025-08-01T11:05:12.967630Z", "iopub.status.idle": "2025-08-01T11:05:12.970902Z", "shell.execute_reply": "2025-08-01T11:05:12.970546Z"}, "papermill": {"duration": 0.006183, "end_time": "2025-08-01T11:05:12.971787", "exception": false, "start_time": "2025-08-01T11:05:12.965604", "status": "completed"}, "tags": []}, "outputs": [], "source": ["def load_point_cloud(file_path):\n", "    \"\"\"Load point cloud from LAS or PLY files\"\"\"\n", "    file_path = Path(file_path)\n", "    \n", "    if not file_path.exists():\n", "        print(f\"File not found: {file_path}\")\n", "        return None\n", "    \n", "    try:\n", "        if file_path.suffix.lower() == '.las':\n", "            import laspy\n", "            las_file = laspy.read(file_path)\n", "            points = np.vstack([las_file.x, las_file.y, las_file.z]).T\n", "            print(f\"Loaded {len(points):,} points from LAS\")\n", "            return points\n", "        elif file_path.suffix.lower() == '.ply':\n", "            import open3d as o3d\n", "            pcd = o3d.io.read_point_cloud(str(file_path))\n", "            points = np.asarray(pcd.points)\n", "            print(f\"Loaded {len(points):,} points from PLY\")\n", "            return points\n", "        else:\n", "            print(f\"Unsupported format: {file_path.suffix}\")\n", "            return None\n", "    except Exception as e:\n", "        print(f\"Error loading {file_path}: {e}\")\n", "        return None"]}, {"cell_type": "code", "execution_count": 9, "id": "0767778f", "metadata": {"execution": {"iopub.execute_input": "2025-08-01T11:05:12.975629Z", "iopub.status.busy": "2025-08-01T11:05:12.975533Z", "iopub.status.idle": "2025-08-01T11:05:14.788163Z", "shell.execute_reply": "2025-08-01T11:05:14.787339Z"}, "papermill": {"duration": 1.817944, "end_time": "2025-08-01T11:05:14.791480", "exception": false, "start_time": "2025-08-01T11:05:12.973536", "status": "completed"}, "tags": []}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Loaded 35,565,352 points from LAS\n", "Point cloud bounds:\n", "  X: 385723.95 to 385809.63\n", "  Y: 3529182.83 to 3529447.01\n", "  Z: 553.38 to 556.27\n"]}], "source": ["# Load point cloud for current site\n", "point_cloud = load_point_cloud(POINT_CLOUD_PATH)\n", "\n", "if point_cloud is None:\n", "    print(f\"Generating synthetic data for {SITE_NAME}\")\n", "    np.random.seed(hash(SITE_NAME) % 2**32)\n", "    point_cloud = np.random.randn(5000, 3) * 20\n", "\n", "print(f\"Point cloud bounds:\")\n", "print(f\"  X: {point_cloud[:, 0].min():.2f} to {point_cloud[:, 0].max():.2f}\")\n", "print(f\"  Y: {point_cloud[:, 1].min():.2f} to {point_cloud[:, 1].max():.2f}\")\n", "print(f\"  Z: {point_cloud[:, 2].min():.2f} to {point_cloud[:, 2].max():.2f}\")\n", "\n", "# Log point cloud metrics to MLflow\n", "mlflow.log_metric(\"point_cloud_size\", len(point_cloud))\n", "mlflow.log_metric(\"x_range\", point_cloud[:, 0].max() - point_cloud[:, 0].min())\n", "mlflow.log_metric(\"y_range\", point_cloud[:, 1].max() - point_cloud[:, 1].min())\n", "mlflow.log_metric(\"z_range\", point_cloud[:, 2].max() - point_cloud[:, 2].min())\n"]}, {"cell_type": "code", "execution_count": 10, "id": "61fee98e", "metadata": {"execution": {"iopub.execute_input": "2025-08-01T11:05:14.797781Z", "iopub.status.busy": "2025-08-01T11:05:14.797601Z", "iopub.status.idle": "2025-08-01T11:05:14.801649Z", "shell.execute_reply": "2025-08-01T11:05:14.801234Z"}, "papermill": {"duration": 0.00814, "end_time": "2025-08-01T11:05:14.802683", "exception": false, "start_time": "2025-08-01T11:05:14.794543", "status": "completed"}, "tags": []}, "outputs": [], "source": ["def create_analysis_grid(point_cloud, grid_spacing=5.0):\n", "    \"\"\"Create regular grid of analysis points\"\"\"\n", "    x_min, x_max = point_cloud[:, 0].min(), point_cloud[:, 0].max()\n", "    y_min, y_max = point_cloud[:, 1].min(), point_cloud[:, 1].max()\n", "    \n", "    padding = grid_spacing * 0.5\n", "    x_coords = np.arange(x_min - padding, x_max + padding, grid_spacing)\n", "    y_coords = np.arange(y_min - padding, y_max + padding, grid_spacing)\n", "    \n", "    return np.array([[x, y] for x in x_coords for y in y_coords])"]}, {"cell_type": "code", "execution_count": 11, "id": "6de0bd17", "metadata": {"execution": {"iopub.execute_input": "2025-08-01T11:05:14.807215Z", "iopub.status.busy": "2025-08-01T11:05:14.807073Z", "iopub.status.idle": "2025-08-01T11:05:14.835353Z", "shell.execute_reply": "2025-08-01T11:05:14.835043Z"}, "papermill": {"duration": 0.031519, "end_time": "2025-08-01T11:05:14.836320", "exception": false, "start_time": "2025-08-01T11:05:14.804801", "status": "completed"}, "tags": []}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Limiting to 1000 grid points for testing\n", "Created 1000 analysis locations\n"]}], "source": ["# Create analysis grid\n", "grid_points = create_analysis_grid(point_cloud, GRID_SPACING)\n", "\n", "# Limit grid size for testing\n", "MAX_GRID_POINTS = 1000\n", "if len(grid_points) > MAX_GRID_POINTS:\n", "    print(f\"Limiting to {MAX_GRID_POINTS} grid points for testing\")\n", "    grid_points = grid_points[:MAX_GRID_POINTS]\n", "\n", "print(f\"Created {len(grid_points)} analysis locations\")\n", "\n", "# Log grid metrics to MLflow\n", "mlflow.log_metric(\"analysis_grid_points\", len(grid_points))"]}, {"cell_type": "code", "execution_count": 12, "id": "3fb10d9f", "metadata": {"execution": {"iopub.execute_input": "2025-08-01T11:05:14.841129Z", "iopub.status.busy": "2025-08-01T11:05:14.840993Z", "iopub.status.idle": "2025-08-01T11:05:14.845090Z", "shell.execute_reply": "2025-08-01T11:05:14.844854Z"}, "papermill": {"duration": 0.007485, "end_time": "2025-08-01T11:05:14.845889", "exception": false, "start_time": "2025-08-01T11:05:14.838404", "status": "completed"}, "tags": []}, "outputs": [], "source": ["def extract_patch_around_point(point_cloud, center_xy, radius=3.0, num_points=256):\n", "    \"\"\"Extract patch using EXACT same method as training - preserving Z variation\"\"\"\n", "    center_x, center_y = center_xy\n", "    \n", "    # Calculate distances to center (XY only)\n", "    distances = np.sqrt((point_cloud[:, 0] - center_x)**2 + \n", "                       (point_cloud[:, 1] - center_y)**2)\n", "    \n", "    # Select points within radius\n", "    mask = distances <= radius\n", "    patch_points = point_cloud[mask]\n", "    \n", "    if len(patch_points) < 10:\n", "        return None\n", "    \n", "    # Center the patch (XY only, preserve Z)\n", "    patch_points = patch_points.copy()\n", "    patch_points[:, 0] -= center_x\n", "    patch_points[:, 1] -= center_y\n", "    # DON'T center Z - keep absolute Z values for height variation\n", "    \n", "    # Convert to float32\n", "    patch_points = np.array(patch_points, dtype=np.float32)\n", "    n = len(patch_points)\n", "    \n", "    # Sample to fixed size\n", "    if n >= num_points:\n", "        indices = np.random.choice(n, num_points, replace=False)\n", "        patch_fixed = patch_points[indices]\n", "    else:\n", "        # Pad with jittered copies\n", "        extra = np.stack([\n", "            patch_points[np.random.randint(n)] + np.random.normal(0, 0.01, 3)\n", "            for _ in range(num_points - n)\n", "        ])\n", "        patch_fixed = np.vstack([patch_points, extra])\n", "    \n", "    # Normalize XY only, preserve Z variation\n", "    xy_max = np.max(np.linalg.norm(patch_fixed[:, :2], axis=1))\n", "    if xy_max > 0:\n", "        patch_fixed[:, :2] /= xy_max\n", "    \n", "    # Normalize Z separately to preserve relative height differences\n", "    z_mean = patch_fixed[:, 2].mean()\n", "    z_std = patch_fixed[:, 2].std()\n", "    if z_std > 0:\n", "        patch_fixed[:, 2] = (patch_fixed[:, 2] - z_mean) / z_std\n", "    \n", "    return patch_fixed.astype(np.float32)"]}, {"cell_type": "code", "execution_count": 13, "id": "model_validation", "metadata": {"execution": {"iopub.execute_input": "2025-08-01T11:05:14.850192Z", "iopub.status.busy": "2025-08-01T11:05:14.850096Z", "iopub.status.idle": "2025-08-01T11:05:14.882019Z", "shell.execute_reply": "2025-08-01T11:05:14.881739Z"}, "papermill": {"duration": 0.035051, "end_time": "2025-08-01T11:05:14.882837", "exception": false, "start_time": "2025-08-01T11:05:14.847786", "status": "completed"}, "tags": []}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Loaded DGCNN model from best_dgcnn.pth\n", "Model has 1,276,034 parameters\n", "Testing model with dummy data...\n", "Dummy test - Output shape: torch.Size([1, 2])\n", "Dummy test - Probabilities: [0.10651635 0.89348364]\n", "Model producing reasonable probability ranges\n"]}], "source": ["# Load trained DGCNN model with validation\n", "try:\n", "    model = DGCNN(num_classes=2, k=K_NEIGHBORS).to(device)\n", "    model.load_state_dict(torch.load(MODEL_PATH, map_location=device))\n", "    model.eval()\n", "    print(f\"Loaded DGCNN model from {MODEL_PATH}\")\n", "    \n", "    # Log model info\n", "    param_count = sum(p.numel() for p in model.parameters())\n", "    print(f\"Model has {param_count:,} parameters\")\n", "    \n", "    # Test model with dummy data to verify it works\n", "    print(\"Testing model with dummy data...\")\n", "    dummy_input = torch.randn(1, 256, 3).to(device)\n", "    with torch.no_grad():\n", "        dummy_output = model(dummy_input)\n", "        dummy_probs = torch.softmax(dummy_output, dim=1)\n", "        print(f\"Dummy test - Output shape: {dummy_output.shape}\")\n", "        print(f\"Dummy test - Probabilities: {dummy_probs[0].cpu().numpy()}\")\n", "        \n", "        # Check if probabilities are reasonable\n", "        pile_prob = dummy_probs[0, 1].item()\n", "        if pile_prob > 0.1 and pile_prob < 0.9:\n", "            print(\"Model producing reasonable probability ranges\")\n", "        else:\n", "            print(f\"Model output might be problematic: pile_prob={pile_prob:.4f}\")\n", "    \n", "    MODEL_LOADED = True\n", "    \n", "except Exception as e:\n", "    print(f\"Error loading model: {e}\")\n", "    print(\"Creating untrained model for testing...\")\n", "    model = DGCNN(num_classes=2, k=K_NEIGHBORS).to(device)\n", "    model.eval()\n", "    MODEL_LOADED = False"]}, {"cell_type": "markdown", "id": "6b0a249e", "metadata": {"papermill": {"duration": 0.001764, "end_time": "2025-08-01T11:05:14.886682", "exception": false, "start_time": "2025-08-01T11:05:14.884918", "status": "completed"}, "tags": []}, "source": ["## Process site with DGCNN\n"]}, {"cell_type": "code", "execution_count": 14, "id": "a5f0d927", "metadata": {"execution": {"iopub.execute_input": "2025-08-01T11:05:14.890958Z", "iopub.status.busy": "2025-08-01T11:05:14.890843Z", "iopub.status.idle": "2025-08-01T11:05:14.894271Z", "shell.execute_reply": "2025-08-01T11:05:14.894035Z"}, "papermill": {"duration": 0.00653, "end_time": "2025-08-01T11:05:14.895006", "exception": false, "start_time": "2025-08-01T11:05:14.888476", "status": "completed"}, "tags": []}, "outputs": [], "source": ["def process_site_dgcnn(point_cloud, grid_points, model, device, batch_size=16):\n", "    \"\"\"Process site with DGCNN model\"\"\"\n", "    results = []\n", "    \n", "    for i in range(0, len(grid_points), batch_size):\n", "        batch_centers = grid_points[i:i+batch_size]\n", "        batch_patches = []\n", "        valid_indices = []\n", "        \n", "        # Extract valid patches\n", "        for j, center in enumerate(batch_centers):\n", "            patch = extract_patch_around_point(point_cloud, center, radius=PATCH_SIZE, num_points=NUM_POINTS)\n", "\n", "            if patch is not None:\n", "                batch_patches.append(patch)\n", "                valid_indices.append(i + j)\n", "        \n", "        if len(batch_patches) == 0:\n", "            continue\n", "        \n", "        # Run DGCNN inference\n", "        batch_tensor = torch.FloatTensor(np.array(batch_patches)).to(device)\n", "        \n", "        with torch.no_grad():\n", "            outputs = model(batch_tensor)\n", "            probabilities = torch.softmax(outputs, dim=1)\n", "            pile_probs = probabilities[:, 1].cpu().numpy()\n", "        \n", "        # Store results\n", "        for j, (idx, prob) in enumerate(zip(valid_indices, pile_probs)):\n", "            center = grid_points[idx]\n", "            results.append({\n", "                'x': center[0],\n", "                'y': center[1],\n", "                'pile_probability': prob,\n", "                'prediction': 'PILE' if prob > CONFIDENCE_THRESHOLD else 'NON-PILE'\n", "            })\n", "    \n", "    return results\n", "\n"]}, {"cell_type": "code", "execution_count": 15, "id": "8444b060", "metadata": {"execution": {"iopub.execute_input": "2025-08-01T11:05:14.899479Z", "iopub.status.busy": "2025-08-01T11:05:14.899302Z", "iopub.status.idle": "2025-08-01T11:09:22.103907Z", "shell.execute_reply": "2025-08-01T11:09:22.103332Z"}, "papermill": {"duration": 247.2123, "end_time": "2025-08-01T11:09:22.109319", "exception": false, "start_time": "2025-08-01T11:05:14.897019", "status": "completed"}, "tags": []}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Running DGCNN inference on 1000 locations...\n"]}, {"name": "stdout", "output_type": "stream", "text": ["DGCNN Results for nortan_res:\n", "  Total locations analyzed: 880\n", "  Pile detections: 134 (15.2%)\n", "  Average confidence: 0.217\n", "  High confidence (>0.9): 147\n"]}], "source": ["# Process site with DGCNN\n", "print(f\"Running DGCNN inference on {len(grid_points)} locations...\")\n", "\n", "results = process_site_dgcnn(point_cloud, grid_points, model, device, BATCH_SIZE)\n", "\n", "if results:\n", "    results_df = pd.DataFrame(results)\n", "    pile_count = len(results_df[results_df['prediction'] == 'PILE'])\n", "    avg_confidence = results_df['pile_probability'].mean()\n", "    \n", "    print(f\"DGCNN Results for {SITE_NAME}:\")\n", "    print(f\"  Total locations analyzed: {len(results_df)}\")\n", "    print(f\"  Pile detections: {pile_count} ({pile_count/len(results_df)*100:.1f}%)\")\n", "    print(f\"  Average confidence: {avg_confidence:.3f}\")\n", "    print(f\"  High confidence (>0.9): {sum(results_df['pile_probability'] > 0.9)}\")\n", "    \n", "    # Log results to MLflow\n", "    mlflow.log_metric(\"total_locations\", len(results_df))\n", "    mlflow.log_metric(\"pile_detections\", pile_count)\n", "    mlflow.log_metric(\"detection_rate\", pile_count/len(results_df))\n", "    mlflow.log_metric(\"avg_confidence\", avg_confidence)\n", "    mlflow.log_metric(\"high_confidence_count\", sum(results_df['pile_probability'] > 0.9))\n", "else:\n", "    print(f\"No valid results for {SITE_NAME}\")\n", "    results_df = pd.DataFrame()\n", "    pile_count = 0\n", "    avg_confidence = 0.0\n"]}, {"cell_type": "markdown", "id": "c4f86d3f", "metadata": {"papermill": {"duration": 0.001828, "end_time": "2025-08-01T11:09:22.113289", "exception": false, "start_time": "2025-08-01T11:09:22.111461", "status": "completed"}, "tags": []}, "source": ["## Visualization\n"]}, {"cell_type": "code", "execution_count": 16, "id": "a9c7014e", "metadata": {"execution": {"iopub.execute_input": "2025-08-01T11:09:22.117847Z", "iopub.status.busy": "2025-08-01T11:09:22.117710Z", "iopub.status.idle": "2025-08-01T11:09:22.484386Z", "shell.execute_reply": "2025-08-01T11:09:22.484102Z"}, "papermill": {"duration": 0.371142, "end_time": "2025-08-01T11:09:22.486217", "exception": false, "start_time": "2025-08-01T11:09:22.115075", "status": "completed"}, "tags": []}, "outputs": [{"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAA6oAAAMWCAYAAAD4dHsQAAAAOnRFWHRTb2Z0d2FyZQBNYXRwbG90bGliIHZlcnNpb24zLjEwLjMsIGh0dHBzOi8vbWF0cGxvdGxpYi5vcmcvZiW1igAAAAlwSFlzAAAPYQAAD2EBqD+naQABAABJREFUeJzsnQV4G9b19l9ZsszMbIeZmZuUU6YVVtrWrrCtsHVbh9+2/7BdmZlXSDlt06ZhRocTO3bMzGzLlvQ977HlyrIcaGNfpbm/51HiyIp1fHXvuffcQwa73W6HRqPRaDQajUaj0Wg0HoKXagE0Go1Go9FoNBqNRqNxRhuqGo1Go9FoNBqNRqPxKLShqtFoNBqNRqPRaDQaj0IbqhqNRqPRaDQajUaj8Si0oarRaDQajUaj0Wg0Go9CG6oajUaj0Wg0Go1Go/EotKGq0Wg0Go1Go9FoNBqPQhuqGo1Go9FoNBqNRqPxKLShqtFoNBqNRqPRaDQaj0IbqhqNRnOKkZubC4PBgFdeeaX7uf/3//6fPKc5PlJTU3HTTTepFkOj0Wg0Gk0faENVo9EohcYWDSzHw9fXF/Hx8TjnnHPw2GOPoaGhoc//u2fPHtx8881IS0uT/xcYGIgJEybg17/+NY4cOeL2/6xevRqXXXYZYmNjYTabER0djQsvvBAffPBBL0OQj/fff7/Xz3AYhZWVld3P0ejhc+PGjYPdbu/1f/i9n/3sZ8dlQDmPB+WbO3cuPvzwQ5wqn8uxOHDggIwhx7k/2bhxo7xPbW1tv77P94Wnnnqqx+WHRqPRaDQq0YaqRqPxCP7617/i9ddfx9NPP42f//zn8tzdd9+NsWPHikHqyvPPP49Jkybhiy++EMPz8ccfxwMPPIDZs2fjtddew4gRI2C1Wnv8nz//+c8444wzsG/fPvz0pz/FM888g/vuuw+NjY24/PLL8dZbb7mVy53h2Rd79+7tYfR+G2hscyz4+NWvfoXi4mL5HSkvSUlJQUtLC66//np42udyvIbqX/7ylwExVPk+7gzVjIwMmUOab9CGqkaj0Wg8CZNqATQajYacd955mDJlSve/77//fqxcuRIXXHABLrroIhw8eBB+fn7dBsjtt98uRunSpUsRFBTU42f997//xd///vcezy1ZskSMriuuuEIMUm9v7+7v0Vj98ssv0d7e3stg3LVrl3gzaSgeC8qXlJQk78PXf9tQ3ISEBPzwhz/s/vcNN9yAIUOG4OGHH8Ztt93W7eH0tM/lVMLHx0e1CB5Dc3Mz/P394ek0NTUhICBAtRgajUajGSC0R1Wj0XgsCxcuxB//+Efk5eXhjTfe6H6eXjIaa2+++WYvI5XQiPvb3/4Go9HY/Rx/Tnh4OF566aUeRqoDhrTS+HLm6quvxrBhw47bq+rl5YU//OEP4mk8maG6DFMeOXIkcnJy+sxR7QuO2+TJk8WY5O/P36mgoKBfPhdy6NAhuQzge/FzoJH7ySefdH+fMl955ZXyNb3bjtBihmQ7oJec4c40Svj5Ll68GPv37+8lB9/rqquuQlRUlPx+w4cPx+9//3v5HkN+eQFBGBrueB+HF9ddjirDxSkbZafhNmPGDHz22Wc9XkM5+XPeffdduQxJTEyU33PRokXIyspCf4Rfb9iwAffee6/8nhyTSy+9FBUVFW49oqNHjxYjnGHad955Zy9v8oIFCzBmzBjs2LED8+bNk9/zd7/7nYwHx3jNmjXdY8XXkurqavHs04vO8Prg4GC5wNi9e3e/jY3jd6c8d9xxh4TA8+edyBwpLS2V1AD+P45JXFwcLr744n735Gs0Go3m5KANVY1G49E4wlu/+uqrbu8PPXo8RDsfXI/G4cOHxai55JJL3Bq2fUFDl4YnD+THa3hee+21GDp06AmHDB8NenppXEZERJzQ/6OxQG8s5XnooYckZHfFihVioHzXvE3Xz4XQUKBxRy/rb3/7W/Fs05DguDvGj+/9i1/8Qr6mgeQIcaYhTvg1jQ4aRP/+97/FIGao8Jw5c3oYGLwMmD59usyFW265BY8++qi8z6effirfp0f7mmuuka/piXa8D409d5SVlWHWrFniWadhxLFrbW0Vr7G7z/5f//qXPE8Djl7mzZs347rrrkN/wJBrzkGGrjOSgL+ja74zDXMapjRQOe4MZX/22Wdx9tln94oUqKqqEkOTEQOPPPKIXBjwb64nhsw7xsph9NOA/+ijj+Qih/OIFwAMcZ8/f76Epffn2PCz4Of/pz/9SebUicwRjgHloLFKI57zjrnV+fn530oWjUaj0Qwwdo1Go1HIyy+/TGvOvm3btj5fExISYp84caJ8vXv3bnn93Xff3et1VVVV9oqKiu5HW1ubPP/xxx/L/3n44YePS6acnBx5/QMPPGDv6OiwDx061D5+/Hi7zWaT7//5z3+W7/M9HNx44432gIAA+frVV1+V73/wwQfd3+e/77zzzmO+d0pKiv3ss8/u/h34+1599dXy/3/+85/3kI9j58Ahk4Pc3Fy70Wi0//3vf+/x8/fu3Ws3mUy9nv+unwtZtGiRfezYsfbW1tbu5zhms2bNkjF08N5778nPXrVqVY+f19DQYA8NDbXfcsstPZ4vLS2V93J+ft68efagoCB7Xl5ej9c6PiPCz4/vw/FyN878zBxwPvG169at6yFPWlqaPTU11W61WuU5yszXjRw5snt+kUcffVSe5/ieLByfwZlnntnj97rnnnvks62trZV/l5eX281ms8wbh5zkiSeekP//0ksvdT83f/58ee6ZZ57p9X6jR4+W77vCz9P55xKOqY+Pj/2vf/1r93Mnc2wcv/ucOXNkDZ7oHKmpqelewxqNRqM5NdEeVY1G4/HQc+KoMltfX9/9nCuDBg0Sj5nj4Qg5dfyfE/GmuvOq0qt0PNB79F28qvRSOn6H8ePH47333hMPJr1HxwsLOtlsNgmNZXVix4NhxJRt1apVOJmfC8ND6d3k+/E5x/vRe8ewanq1i4qKjvrzli9fLp5eekKdZeZnQO+pQ2aGva5duxY/+tGPkJyc3ONnfNu84M8//xzTpk0Tr5zz73frrbeKl44eO2fopWPVaAcMQyV9VZv+LlAG59+L78VCYQy9Jl9//TUsFot4zBl+7oCeZobpuoYvMwyW8h8vfL3j5/J9+ZlybBhqvXPnzl6vP5ljw9/BOYT/eOcIQ8EpA8ORa2pqTvh9NRqNRqMeXUxJo9F4PKzKyxw1Z2OTz7ny8ccfS5gjjUqGHTrgYZ1825YqNDyZ80rDk+Glx2vc3njjjWLcMqfwROCB+//+7//EOGEOIcNiQ0NDT+hn0DCkkUyj1B3u8nS/y+fCHES+H8Mw+XBHeXm5FIo6msyOHFh3OD5Hh8HDXMuTBY0+jrsrjpBkft/5/VwN5LCwMPn7aEYRjUka9M7wMsLZEHPHsd7LYbDScHSGhhovbxzfd8DPwNmQPBa88GBoNcNnmSftXE3bXTj6txmbvmB+8beZIzSuebHzy1/+EjExMRKSztBlhsLzskaj0Wg0no82VDUajUdTWFiIuro6qXpL+LfJZJIWM64wZ47w+84w744wr+7b4DA8WXyHxnB/GLfOREZG4swzz8R3gcYFDV0WnXFnCLnzSH+Xz4XvR3hBQA+qOxyvPZrMjhxEd8aE6+eqkr6My6N50FmtmvmgztDwYyGjk/1eR+NEqzT/4x//kMsHerA5p1lsih5WenAdn1l/yesq64nMEcrHHsm8LGLuMX+Hf/7zn+L5nzhx4gnLotFoNJqBxXN2fY1Go3EDD6TEYfywOA8LKbEaKENJj+ahc8DKvfQ20cikZ+jbGGlsF0MvJysOs8BOfxi3J5PBgweLYUCPFH///v5c6LlzeGqPZWT3FZ5LmQm9tEf7GY73cndZcTzv4w72pmVvVVdYhMvx/e8Kw7gZuurMyfDuOWSj/I6xcXhwaQgf76VHX+PF1k40sF988cUezzMEl5cqA8nxzhHn19Oryge9sSwgxWJTrtWqNRqNRuN56BxVjUbjsdDzQQ8OjS3nqqGsAMrwQxqP7kKA3XluaGAyt+4nP/kJOjo63OaFsifrsQxP9lV1brdyNCgfvYh874GGVW8pM9/bdTz4b47FyfxcaDjwAoGVZktKSnr9H+d2Ko5emK6Vh2n0MnSTHjzXSrXOP4PhsqwezFZDrhVcnX/Xvt7HHeeffz62bt2KTZs29ejb+dxzz4nHc9SoUfiuMASWxpXz42T0w+XPYSjvY4891uP3p2FJrzcr5B4PHC93Y8V55DqHmDd9rJzj/uB45wirg7Nqs6vRytSBtra2AZNXo9FoNN8e7VHVaDQeAUNU6b2iEclWITSG6H2it4iGofOBnsVZnnjiCWnbwRxMGksM76UHKTMzU/qr8uDu7K36wQ9+IKG/bDuSnp4uxVj4s2mwLVu2TNq2vPXWW8cVzktj9XjgAZ8tPk6kcM3JgodyeoDZHoTFgByteehhY8sOFuhxzuM9GZ/Lk08+KcWI2G+TRXDo3eP/ofHHUGFH3016tTg2zCGkIcV8QuYc0th9+umnpXDUpEmTpOcrjVIaoywINHv2bPncCY0yvhdfx9+FRjN/T77O8fmwfyzhZ8CfRW8vQ0EdBqwzbH3yv//9T9q2sI0Jw1tfffVVGa/333+/R5EiT4NjxM+ZlxLnnnuuePzpXWVO6dSpU+XC5HjgeHH8OW94wcLPg58LczsZws55zBY+XEdcY87e24GCRurxzBHqAfZvZXEvXjIwJJjznvOR/0ej0Wg0pwCqyw5rNJrTG0cbCseDbTZiY2PtZ511lrS1qK+v7/P/pqen22+44QZ7cnKy/D+2hxk3bpz9l7/8pT0rK8vt/1mxYoX94osvtkdHR0ublqioKPuFF14oLWzctac5mrx9tadxpr293T548OATak+zePHio77meNrTOHj//felxQdl42PEiBEiR0ZGRr98LtnZ2fKZ8LXe3t72hIQE+wUXXGBfsmRJj9c9//zz9kGDBkmbFddWNfz6nHPOkXYjvr6+Mn433XSTffv27T1+xr59++yXXnqptCvh64YPH27/4x//2OM1f/vb30QGLy+vHq1qXNvTOGS/4oorun/etGnT7EuXLu3xGkcLFrbYOdZn8l3pq0WQQwbX9j5sR8PPl+MeExNjv/3226VNizNsP8M2NO5gixfOPbb94c93tKphexquqbi4OLufn5999uzZ9k2bNsn3ndvZnMyxOVZ7pGPNkcrKSpnnHA/Oe75u+vTp9nffffe4ZdBoNBqNWgz8Q7WxrNFoNBqNRqPRaDQajQPPjWXSaDQajUaj0Wg0Gs1pic5R1Wg0Go1GMyC0tLRIXvLRYH7wifR51Wg0Gs33E22oajQajUajGRDeeeedYxYXW7VqlVSQ1mg0Gs3pjc5R1Wg0Go1GMyCwddH+/fuPWX2YrXw0Go1Gc3qjDVWNRqPRaDQajUaj0XgUupiSRqPRaDQajUaj0Wg8Cm2oajQajeY7k5qaiptuuqn736tXr4bBYJC/VfP//t//E1lOR3Jzc+V3f+WVV1SLotFoNBrNCaENVY1Go/EAGhsb8ec//xnnnnuuVD09mnHx/PPPY/78+YiJiYGPjw/S0tKkQA2NkqOxfv16+bl8VFZW9tNv8v3irbfewiOPPNLv7/PUU09pY1Kj0Wg0Gid01V+NRqPxAGg4/vWvf0VycjLGjx9/VE9kenq6GKcXXXSRFJ3JyckR43Xp0qXYvXs34uPje/0fm82Gn//85wgICEBTU9NJlz8jIwNeXl7fS0N13759uPvuu/vdUI2MjOzhldZoNBqN5nRGG6oajUbjAcTFxUlF1NjYWGzfvh1Tp049qlHjyiWXXIIpU6bgtddew29/+9te33/uuedQUFCAn/zkJ3j00UdPuvz07Go0Go1Go9GcLL5/198ajUZzCkJDj0bqd8kRJbW1tb2+V11djT/84Q/isQ0NDT3h3M5Dhw7hqquuQnBwMCIiInDXXXehtbW11/sfjzdwy5YtEt4cEhICf39/CWHesGFDj9c0NDSIB5M/k+MSHR2Ns846Czt37jzmz2d4M418X19fDB48GM8++2yfr33jjTekFYqfn5+EW1999dVizDtgL8/PPvsMeXl53SHTjnEmbW1tEq49ZMgQkTMpKQm//vWv5Xl37zVt2jT5nekFnzdvHr766qvusWPLljVr1nS/j3MfUX6mHA/+fL4P3+/f//63eMmd4ev4GXBs+TnfeOONbueDRqPRaDSnAtqjqtFoNKcoVVVVsFqtyM/PFyOULFq0qNfr/vjHP4oR/NOf/hR/+9vfTvh9aKTSmPrnP/+JzZs347HHHkNNTY14b0+ElStX4rzzzhPjkAYeQ4VffvllLFy4EOvWrRNDjtx2221YsmQJfvazn2HUqFHye9IAPXjwICZNmtTnz9+7dy/OPvtsREVFiZHd0dEh78NcXlf+/ve/y7jwd6OXuaKiAo8//rgYkAytpqH3+9//HnV1dSgsLMTDDz8s/y8wMFD+ppHI0GvKdeutt2LkyJHy/nxdZmYmPvroo+73+stf/iLyzJo1Sz4ns9ksBjvHg/IyB5Zh2fzZfE/ikLm5uVmM+aKiIvn8GBq+ceNG3H///eKBd+TPstPcxRdfLPJw/CjPhx9+KMaqRqPRaDSnJOyjqtFoNBrPYdu2bexvbX/55ZeP+jofHx95HR8RERH2xx57rNdrdu/ebTcajfYvv/xS/v3nP/9ZXl9RUXFMORyvveiii3o8f8cdd8jz/NkOUlJS7DfeeGP3v1etWiWv4d/EZrPZhw4daj/nnHPkawfNzc32tLQ0+1lnndX9XEhIiP3OO++0nyiXXHKJ3dfX156Xl9f93IEDB+T3d97ucnNz5bm///3vPf7/3r177SaTqcfzixcvlt/Nlddff93u5eVlX7duXY/nn3nmGXmvDRs2yL8PHz4sr7v00kvtVqu1x2udx2H06NH2+fPn93qfv/3tb/aAgAB7ZmZmj+d/+9vfyu+Qn58v//7oo4/kff/zn/90v6ajo8M+d+7c45pLGo1Go9F4Gjr0V6PRaE5RvvjiC3z++ef473//K542d0WSfvGLX4gXk567b8udd97Z49/0/hG+9/Gya9cuHD58GNdee614SFk8ig/KTC/w2rVru0NZ6c2kx7G4uPi4fz49y19++aXk6nIsHNCzeM455/R47QcffCDvRW+qQw4+6HUeOnQoVq1adcz3e++99+RnjxgxosfPoHeYOH4GPat8rz/96U+9ik0dT8scvs/cuXMlXNj5fc4880z5nTlujs/CZDLh9ttv7/6/RqOx+7PSaDQajeZUQxuq3xKGfPEwxDwoFkG5/vrrj3moYs6RI//I8WCIlgNW67zmmmskD4k5UzwEuSt68uSTT8r3+Jrhw4efcPidA+ZeTZ8+XX4OD0E84Gk0mlOHM844Q4zQe++9Vwwahpg+8cQT3d9/5513JEyUhux3gcabM8z9pNF1rHY4ztBIJQxFZWiu8+OFF16QvE6G2ZL//Oc/UmmXupDhwAybPXLkyFF/PkN3W1paeslKqCddZWGoLF/rKgvDi8vLy4/r92Feqev/HzZsmHzf8TOys7NlrBjC/G3g+yxbtqzX+9BQdX4f5tFyL3KEJvf1u2s0Go1Gc6qgc1SPYViyMIW7AiE8IP7ud7+TgwFzh371q1/hiiuukEPh0bjlllu6c8kIC2s42LFjhxQNYdENHtD4s5j7xFtx5mqRp59+WnKT2IqCBUO2bt0qP5OG5oUXXnjcv9v7778v/+8f//iHeACYy8WDoUajOTWh8Thx4kS8+eab3frivvvuw5VXXik5kQ6j0lFch0WDLBaL21Y2x+J4PIGuOLylDzzwACZMmOD2NQ4ji55OehGZY8mCQ/w/LB5ETygN8+8KZeHvQI809WtfchzrZ4wdOxYPPfSQ2+9Th58M+D4sJMUiTe5wGMYajUaj0Xzf0Ibqt+See+7p/jolJUXaQdAj2d7eDm9v7z7/Hw3Tvip7/uhHP+rx70GDBmHTpk1yOHMcPF9//XUpqPGDH/yg+zXbtm2TQ5yzoUoPBb0o7K/IIigM/7vjjjvkezRKWbWTh78f//jH3f/n2974azQaz4AeReeKszRG2QeUD1dYlIj9WhmSezxePfZtdZCVlSUGlHMF3OMxpAkrBzu8gUeDl4DUWXzQa0h5WQCpL0OVXkZGhzg8t649Xl1loUeVv9OxDL2+jHL+DEbBMGz5aIY7X8exOnDgQJ8G+rHep7Gx8Zhjxn1oxYoV8lpnQ9v1d9doNBqN5lRBh/6eBNj6gV4MVnQ8mpFK+Do2dR8zZox4RlnR8WgwFI5tExzwEMpwY2d4OKNnlUay4z2YD8VDHcPY6DVldctXX31Vvs8WD/QCMxyNHhgeCHn40x5Vjcbz4UUTK+66Qh3AqrPspeqAHknXh+OSiykDjkq2x4LpBs6wOi45Ee8mK/3S6HrwwQfFmHIXukuYd+kIAXbASBN6ft21fXFAzyhzUZkTyirIDqgDmbvqzGWXXSavZ6g0DVZn+G/m0DoICAjoJY/D60s9yugWdxcGjnxhXmBS1zKSxrWdjPN7833ctZLh+/DC0vV3IHw95wM5//zz5WtG3TjgWDo+K41Go9FoTjW0R/U78Jvf/EbywWhszpgxA0uXLj3q61lEhLfePHDt2bNH/j9vu+kxdQdDf5ljxlxSBzyI0VvKww89DAwX5r9ppLLABo1OtmOgN5WHMUKvAW/z2U+Q+WGOXC/mfTFsjV4Rvp6hzmyr4GwYazSagYP6hMaHI9/9008/ldYohEVx2B+TRh7DSmlwjh49WgwcGqhs88Lv81LKgbu8c4cHlUYmL82OB0ZmMC+f/U9pNDE9gfqMHtnjhcYadRXfl3LffPPNSEhIEGOPhYfoaeXvyx6qiYmJkkrBn0/v4Ndffy2RI8fKtaXhyXxOhg3TE0vDjYYa34861wEN5v/7v/+Ty0KGRHOcgoKC5PekMc+UC6ZzOAxs6mHmATPdgvIweoV1Cd59912pM0D5Z8+eLYYhe87yeRqWvDRgz1O2nGFbIMpFvcxeqPx9uBew5Y/jfWhkUi7+HxrnTMtg+PYnn3yCCy64QNJQ+DoawfzM2cKH8vNzpEyUgdE9fI4RMtxb3BnZGo1Go9GcEqguO+xJsCUB2wA4HmwpwPYPzs85tz1ge4eMjAz7V199ZZ89e7b9/PPP79Fu4FisWLFC2gZkZWX1+h7bJERGRkprAmfYyuHmm2+WFgpsTRAfH2//9a9/LT+ntLTU3tjYKF/7+fn1kJu/R3R0tPyMN998U17z7LPPdv/c1tZWeT+2VtBoNGpgGxRHuxnXR05Ojrymra3Nftddd9nHjRtnDw4Otnt7e8v/+/GPf9z9mqPxbdrTsMXLFVdcYQ8KCrKHhYXZf/azn9lbWlp6yX609jQO0tPT7Zdddpm006Fe4v+76qqrRB86fr/77rvPPn78eHk/6i9+/dRTTx3XGK5Zs8Y+efJku9lstg8aNEh0muP3cOX999+3z5kzp1tPjhgxQtriUK87oE699tpr7aGhofIznFvVWCwW+7///W9pLcPfhWPD9/7LX/5ir6ur6/FeL730kn3ixIndr2MrmuXLl3d/n/qbrXD4O/N9nFvVNDQ02O+//377kCFD5Peirp41a5b9wQcfFBkcVFVV2a+//nqZF2zxw6853ro9jUaj0WhORQz8Q7Wx7EkhvHw4uO6663D55Zd3eyYJvY9sAeAKvR6OAkgzZ848rvfjrThv5+kBcG6fQO8nizWxCT3Dd91BD2pZWZl4UJ977jnxztITw/A55sDS48GKvs4w1I3eVd7+86Z+3bp1mDNnTvf3+XrmQfX1nhqN5vSCURf0UlKvHK/3VaPRaDQajeZkoEN/nWDIq3PYK3M/GX7FMKxj4cg9OloOVV8heDQ2HbDdAY1IhugezWBkLizD48jbb78tYWEMrYuJiZFwMob30tB2B0PHGHrGsGOHoUrDl+FiDE3WaDQajUaj0Wg0GpVoQ/VbwEb0zC+ikce2MOyTx7ww5j05vKnMu2I1SBYsYR9AvoaVN1nwIiIiQvKlWDl43rx5GDdunPwfFjOikUrvKvOhSktLuz2hrGhJmEPKoin0frKgCnNM+f8chZIIPSCs8st8NeaU0Xjevn27vJ4/l7lgzKtiLiu9wDROWQGYsJWFRqPRaDQajUaj0ahEG6rfAraYYZEKGnoM36VHlAbhH/7wB/FUOjyU9Fg6qvqyjyELgjzyyCPyf2ggMqyY/8cBC2MwxI5hu3w4oCHp6IHIYh0sKMKfTa8qQ4QZbuzcJoIhw5SRxicLcbDYCvv93X333d2v4fcYwsyCIKxQScN35cqVYnhrNBqNRqPRaDQajUp0jqpGo9FoNBqNRqPRfE9Yu3atOKXYHaSkpEQq2rvrBODM6tWrJfKSaYh0qNGZxmrzKtF9VDUajUaj0Wg0Go3me0JTU5O0eHPtg94XbM+2ePFiidRkDR1GYTJC010P74FEe1Q1Go1Go9FoNBqN5nuIwWA4pkeV3UM+++wzqXvj4Oqrr5aOIuxOogqdo9pVsbe4uFgavvPD1Gg0Go1Go9FoNCcH+sUaGhqkMwW7VJxKtLa2wmKxeMQYGlzsFNbGcdTH+S5s2rRJWlQ6w+KuzvVtVKANVUCMVMZiazQajUaj0Wg0mv6hoKCgu73iqWKkJoRGoLqtsziqSgIDA9HY2NjjORZ2Zc/z7wo7jbDFpTP8d319vRRdZctOFWhDFRBPqmPxsHXLyfLSsoIv28qcajdHmlMTPec0A4meb5qBRs85zUCi59vJhQYPnUKOM/epAj2pNFLfXXQd/E1mZXI0d1hw1Yo3e9kqJ8Ob6sloQ7Urdpvwgz+ZhipvYfjztILTDAR6zmkGEj3fNAONnnOagUTPt/7hVE2xo5Ea4K3OUHVwMm0VZ2JjY1FWVtbjOf6b76XKm0q0oarRaDQajUaj0Wg0feBl6HyofP/+ZObMmfj88897PLd8+XJ5XiX6ikij0Wg0Go1Go9Fovic0NjZKmxk+HO1n+HV+fr78+/7778cNN9zQ/frbbrsNR44cwa9//WscOnQITz31FN59913cc889UIk2VDUajUaj0Wg0Go3me8L27dsxceJEeZB7771Xvv7Tn/4k/y4pKek2WklaWpq0p6EXlf1X//vf/+KFF16Qyr8q0aG/Go1Go9FoNBqNRtMHBq/Oh8r3PxEWLFgg7Wz64pVXXnH7f9LT0+FJaI+qRqPRaDQajUaj0Wg8Cu1R1Wg0Go1Go9FoNJrTtJiSp6I9qhqNRqPRaDQajUaj8Si0oarRaDQajUaj0Wg0Go9Ch/5qNBqNRqPRaDQazfekmNL3hdP019ZoNBqNRqPRaDQajaeiDVWNRqPRaDQajUaj0XgUOvRXo9FoNBqNRqPRaPqARXdVFt414PREe1Q1Go1Go9FoNBqNRuNReIyh+q9//QsGgwF3331393MLFiyQ55wft912W4//l5+fj8WLF8Pf3x/R0dG477770NHRoeA30Gg0Go1Go9FoNN83vLzUP05HPCL0d9u2bXj22Wcxbty4Xt+75ZZb8Ne//rX73zRIHVitVjFSY2NjsXHjRpSUlOCGG26At7c3/vGPfwyY/BqNRqPRaDQajUajOXkot88bGxtx3XXX4fnnn0dYWFiv79MwpSHqeAQHB3d/76uvvsKBAwfwxhtvYMKECTjvvPPwt7/9DU8++SQsFssA/yYajUaj0Wg0Go1Go/leGKp33nmneEXPPPNMt99/8803ERkZiTFjxuD+++9Hc3Nz9/c2bdqEsWPHIiYmpvu5c845B/X19di/f/+AyK/RaDQajUaj0Wi+xxgAg8IHTtNqSkpDf99++23s3LlTQn/dce211yIlJQXx8fHYs2cPfvOb3yAjIwMffPCBfL+0tLSHkUoc/+b3+qKtrU0eDmjYEpvNJo+TAX+O3W4/aT9PozkWes5pBhI93zQDjZ5zmoFEz7eTix5HzSllqBYUFOCuu+7C8uXL4evr6/Y1t956a/fX9JzGxcVh0aJFyM7OxuDBg7/1e//zn//EX/7yl17PV1RUoLW1FSdrQdbV1YmS8zpdM6A1A4qec5qBRM83zUCj55xmINHz7eTS0NCgWgTNKYgyQ3XHjh0oLy/HpEmTehRHWrt2LZ544gnxeBqNxh7/Z/r06fJ3VlaWGKrMWd26dWuP15SVlcnf/F5fMIT43nvv7eFRTUpKQlRUVI8c2O+q4FilmD9TKzjNQKDnnGYg0fNNM9DoOacZSPR8O7n05ZQ6VfAydD5Uvv/piDJDlZ7RvXv39nju5ptvxogRIyTE19VIJbt27ZK/6VklM2fOxN///ncxeNmahtBDS2Nz1KhRfb63j4+PPFyhIjqZyogK7mT/TI3maOg5pxlI9HzTDDR6zmkGEj3fTh56DDWnlKEaFBQkBZKcCQgIQEREhDzP8N633noL559/vjzHHNV77rkH8+bN625jc/bZZ4tBev311+M///mP5KX+4Q9/kAJN7gxRjUaj0Wg0Go1GozkRDF6dD5XvfzriEX1U3WE2m/H111/jkUceQVNTk4TmXn755WKIOqDXdenSpbj99tvFu0pD98Ybb+zRd1Wj0Wg0Go1Go9FoNKcWHmWorl69uvtrGqZr1qw55v9hVeDPP/+8nyU7vbF1dMDg5SUPT8PaVUXO6IGy2WysFmiHyeR5srE4hNXqubJ1WG0wGb0k7MrTZLNzPZhMHicbsbW3e6xsHR02GI0Gj5XNy4shfp4nm9XapeOMHrhWWRXVZoOXyaOOEt/oOOpfo+fqOG9T7xQn1Xi6/vVU2Txdx3nyOU6jORqet7toPIaqTTtRuORz1KbvZ3IBouZPR9JVixE0/NtXXD5ZZOXXYMXGPOzJqJTNa/TQSJw5MwXDB4WrFg35BXX48qvD2Lq9UDauYUMjcfaZgzFpYrzyDayioglfLj+M9Rvz0dbWgeSkEJy5cDDmzE5RLltdXSuWr8jCmrW5aGyyIDo6AIvOGIwz5qfB21vtga6juQX5S75EwQdfoa2iGj5R4Ui8eBFSrjoPpgB/5cZp4ccrUPD+V2gqKIF3UAASL1qI5KvOg09EmFLZuDY3bM7HytVHkJdfC7PZhNkzkmU9REcFQjU704vx1ddZyMisFENw6pQEnHv2UKQkh6oWDYcyKmSt7t5TKmtz3NgYnHv2MAwfFqlaNNRn5iDv7c9QtnIzbB1WhE0YiZQfnI/ouVNUi4ayiiZ8vTobG7cUos1iRUpSCBbNS8PMaYnKdVwtddzX1HE5aGpuR2xMoOi4BfPTlF8atrS0y1pYvTYHtbWtCAvzw8IFaVi0cDD8fL2VytbebsWqNTmiR8rLmxAQaMb8Oak4+8whCA5Wm+bFy+j1G/Pw9YpsFBTWwcfHhLmzqeOGIioqQLn+LV+7DfnvfoGa3Yfg5W1CzKKZSL16MYKGpCiV7VSku5+pwvc/HTHYOZNPc1j1NyQkRMqQn8yqv44iT6diAnnRB8uQ8eBz6GhqgXdIoNyad9Q3wic6EmP/9RuET+nME1ZB+sFyPPv2LtQ3WBDg7y09kBtb2hHo740fXzEW08fHK5Mt83AlHnx4A8orGhHgb5bb1cZGi2xeP7x2PBafN7zf3vtYc664pAH/fmAt8grq4O9ngsnbC81N7eJFumDxCFx/7XhlBzkaqf9+cB0OHiqXsTL7GNHS3CEb7bw5qbjjtunKDnIdLa3Yee8/Ub56Kwze3jAF+Inhare0I2ruFEx++H5lxipvyff+5QkUfrBcdjFTkD+srRZYm1vEeJjyxJ/gGxWuZL7xs3t7yV58vPSQeAW5Hnhx09zSjqSEENx3zxwkJpwcfftt4GXSq2+ko7W1A4GBZjlw8oIkKjIAv7x7NkYMj1Im25ZthXjy6S1oaGhFYGDnQZx6hIdyroXpUxOVyVadfgA77/kHWkoqYAoOFA8N9wajvy9G3vcTOQT3F8eac0XF9Xjg8U0oLK6Dn583vE1eaGxqh8lkwEXnDccPLh2tTMfV1LTgXw+uRWZmFXx8jTCbv9FxCxcMwq0/mapMx9FIffCRDUhPL4a3txd8fU1oae2Q9TplUgLuvWu2PKcCyvDks1uwfn2e5Ojxc7VYrHLRyjX6m1/ORUiIrzId9/pbu7H0s0OiP/wDvNHRbkNzc7tcdv321/MQFxsEVeS8/hEOPfyK7Alcq/YOKzoaGuGfEINJj/wOYeNGnPJn7YGUe80VNyPQ26xMjsZ2C+YvefmUG7/vyqlnQWn6ndbyKmQ98SrsVisCh6TAJyoCvjFRCBicgrbKahx+6AU5IKuAN+RvfXpAbqMTYwMRHuKLsBBfJMYEyvf+t/SQHIRVIJvWm7vEa5mUGILISH+5lU5KCpGbsHeX7ENZWSNU8d77e8WrlZgQJDe9YaF+SEgIlo1/2ZeZ4lVSxdIvMsRIjYsLQkxMoMgWHx+E0FBfrNuQJwd3VRR+9DXK12yDb3w0AlLi4RMZhoDkePglxKBi3XbxsqqCclE+c0QoAtIS4RMZDv/EWASkJqJm10EcefVDZbJlH6nGZ8sy4etjQmJ8iHymNAIT44PF8/DuBz2rvg8kFZVNePu9veA1Ldcn12lEhL9EGFRWNePVN3bJwVOV0fDq6+nyN2ULD/eTR1JSMJqaLZ3fa1Wk42w2HHzgBbSUVsre4BcbBd/oCPnabrMj8/HX0VJSDlW8+9EBFBTXISEuCFER/gjl3hAfJHPw86+ykJ1To0y2Tz87hIyMSsTFBSIm+hsdRyNr1doc7EgvVibbilVHxEhlFEtsLPWunxhYXK/bdxaJB1gVW7cViseSe0F8XLCMG8eP8h08VIHPl2Uqk+1QRiWWLcuUPZR7qei4qAAkJgbLXss9XxWMrsl86i2pwBM4OFkuLP3iomStNheX4+CDL8mZRaPxdLShqulF5dotaKuqhW9cZ8sfB7w553MNh3NRt+eQEtn2H65EaUUTIsP8etyM82s+V1Xbgt2HKpTIdiSnBlnZ1YiI9OuV68ZDcH1Dm4QDq6C6uhk7dpbIocg11y0kxEcOxVu2FigL61q3LrfTA+IS4hsQYJY85A0b86CKwk9WyNw3+fv1eN7o5wuDtwmFH69UJlvxF2vlltw7pOetvZfZG6agQBR/thrW1jYlsm3ZXiieBRoLznD+8dC5e08ZKiublMi2bXuRePF5meSM6JFIf+TkVCMrq0qJbAz1LS9vRFSUfy8dFx0VIJddu3eXKpGtdm8m6g5kwy8uuleuG5+zVNWidOVmJbLxgmH3/jKEBrvRccE+coG5TZExSO8fL9yoz1x1HL351g4bNm7KhypoiNKby2gWZ+hFNXoZJBxYFes35cFmtcvYOcNx9Pf3xtr1ubKHqGDrtgK5NOIe6gznH/daphbQk64ChuVbaurhGxvZ+xwXG4W6fZmoP5itRLZTFao81Y/TkdP019YcDUt1LRhPa3DTy5aHc5vFAktNnRLZ6pssoKPD7CZnkQUWeEHY0GRRIhsPvtwweXvvCg1XQ9drVEAj2ULZ3IRvdfaJM6C6Ro1sNJJ5iOwrtIyfdVVVM1TRWlYlYY3u4HpoLe/Mk1ZBa2kFvMzuQ5EoM0P32xvUGIPMc/MyuC8swjXC+Vhbr8aIdqxDd8WTHLLVqZKtvhU2u91tXjafs8Mur1G1N9jaLG7Xg4HGocEAS7WavYFj0m6h/u09bpyDnIY1tWqMhqYmi4SY+/q6z7Xn56rq0oa6q6q6GT596F8ar1XVasaNUPczTLov2bh3tLSoifCqqmmRkGC3Os7XKFFeqtYqnQ2c9O6KJ3HfsrZZ0Maznkbj4WhDVdMLhvoSFslwxdrUAqOvT/drBpqwYB+54eUG4Eo75TUAYS63mwMFw/Mk78jNpukIIwwPV5PLyJAkHsBpFLqvjglEuXiXBgp/f7Pk4bmTjTAfKSZWXeEdhtJ2NLk3lJkL6p8QqyzvzT8pDjaLe4PK2tQs+eXewWrGLiLcXwwud0Y88998zEaEh/X0Ug/kWnWuqOsM56GP2dT9moEmPMxfDGjOe1f4HL/H16iARcSMfj7oaOy9HmxWK/hJ+8Yo2htC/eDD3PbWDvfVuu12REaoGTfqN9ZT6Mug4gUnQ25VIJ766EC09iEbDWwWfVIFx8XdWiCtLe0ICvQRz6oKoiICZG93q+NaOuDnY5J5qQLf6HAWoYHdjY7j3sBzHMP2NcePwamgkpIHTk+0oarpRdSCGfCNiURLUUkPBUyF11pajuBRQxEytv+KAh2NUUMikBATiIqalp6y2e2oqG5BbEQAxikqgsLiCSzuwDBb5wOwVN4rb5JwR1VFUBiGNH1aIurr23qFSVVXtyAwwBszZyQrkY0hZ6zs29raWSDD1RPM78+dlQpVsLov/eGunkke1pmzl3jpmcpki188H14+5l434wz3pXGdeNEiGH3UFH+YMT1JwhpdvTEsjkKPJou0qDJUp01NlLzU8oqmHnqEh056cIYNjcCgNDUVk8ePi0V8bLDoDFcdx+cS4oLlNSoIGT0UoeNGoLWsUmoYOMvWUlgmeXAxC2cokY1zafKEePGEc465er4CA8yYPlmN/uUF5vx5aXIJ4qrjuBboUZ0zS10VVupffoYM1XeGOdFk/jx1+pfjwj2Ae4EzHMfWNisWzFNXMXnmjCQpoMQ91Bnusdxrp01L7LdCT8ci9sxZMEeGoaWorKeOs1pl/YZPHoOgoeo+V43meNHtaTS9MIeFYPivbsWBvz2Opqw8GAP9eYIT75FfUjyG3/dTZR4k9p274ZLReOqtXSgsa4Sv2SiytLR1IDTIR75Hb4gKKMdN10+Uw29RUWeZei+jQW5WeUi64bqJkquqiisvHyO5d4ezq6WyIytiUjbKeeXlozFYYWuf888dhoMHy7F7b5lUduRn2NraLrk+55w1FJMnqavknHDhGajashvFn69BW0UVjH5+sLbwYGJA3NlzkHixOkM1avZkpFxzAfLe/BSNNXUw+vvD1tYmeauRMyci7YZLlMmWmhyKKy8djbeX7JPiSX5+JjEgGFY7eHA4fnDFGGWy0cvBtfr8S9tRUFAvstFI5eE3Li4YN984SZmOo1Hzkx9NxqNPbBLZfP1MoKuSnsKIcD/8+ObJfYZC9jcck1G/vQU77vo7GrPzO/O0jUZ0NDbBHBqMUb+9Fb6R6vTIVZeMQn5hHbJza2D29pJ0EI4bo0muuGyUtKpRxQWLh0vLob37y6Tvt0TftLaLHj7vnGGYMF7N5QM5Y/4g7N1Xhs1bCiSnknsC1wKXAFuXsfK6KiZPjMdZi4ZIWx8a9axlQNlofE0cH4vzzhmqTDbumZdfMgrvvb8fBQWdOq69wyaVf9lG6qrL1ek4Fjobdd+Pse/vz6AxK08q0/NyydrSKsWVRt73Y+XtmjSa40G3p9Htafqkbl8Gij7+CjVb90j/ragzZiD+orMl3FA1xWWNWL2tALsOdlaYHDM0EgumJSE5Xn3Jblb9Zb+3LdsKYLHYMGpEFBaeMajf210cz5zjRs9+dBs350veFDdaHlJ4SFK9adHbsHZdLtZuyJPDEluXsL/gjGlJbnMJBxKrxYKSL9ai8JOVaC4slYq/iRcuFI+msY8c0YGCXt3SFZuklyoPJKwAnLB4ARIuWgjvwACl843by559ZVLVlFWA/f3ouU/CgrlpvYosqSAzsxIrVmdj/4EKubyZPi0JC+enSSikavLza7Fi9RHs2lUi/54wIQ6LFgxCsgf0eG0uKkPBh8tR+vVG2CztiJg6FkmXnY2w8f3b7uJ45hx7la7ZkIdNLHTT0oFBqWE4Y24qxo6KVq7jqHNZ/IeFlaiL2aaJOo4eftU6TorabcgTHczL1pjoAMyfm4bZs5KV97FmhNKWrYVYvS4HRUX1Eg0xt8uApuGqWsel7yrBqjVHpKAiiz7Nnpksnl5V3lTXdlKsTF+9Y79E19DTmnTZWVL8bKA51dvTbPjBzQhUuN83WiyY/c7p155GG6raUNV8T9BzTjOQ6PmmGWj0nNMMJHq+nVy0ofrdaDxNDVW98jQajUaj0Wg0Go1G41HoHFXNUWlobJMQNOYKpqWG9eqzphIWi6k/dES+Dhqe1q9hjt8mVLT+QDZs7e2SD+ITrj5cz7mYAvundTS3IiAlHn4xPfusadzD4JOi4nrU1LRKYSyGJqsOJXSG/TUZshcQ4C1r1ZNk82QaGtqQX1ALk8ko46Yq/7OvcPjcvM5CWakpof0a5nii2G0dQEs5q+wBvpEwmNQUxnKH1WZHeUMr2q12hPl7I8jXg8bNbkfDoSNor2+UFAJWFfck2QqL6qW1VFiYLxLiPUvHlZY1Sr9c1nxISQ7xKNnY4qekpEHarFGPuPbyVQnDzAuL6kTHsUic6lDuUxVH9V2V73864jlWh8ajYL7K+x/ux9crj0gfMK+uZvMXXjACZy0arHSDYE5ezusfI/fNT9BSWimFRliKPeXqxRh082XwMpnUGjOfrED2C0vQlF8ssrI4FfMFh915HUx+anNWylZvQeaTb6LhcJ4U3DEF+SPu7NkYcc/NUgxF4x4WA3rtjV04cKgclrYOmM0mjBgRheuvHY/UFDXVYR3w4Pb6m7ukuTwLUPEwwtzja68eh1EjBz4P6VSBBVnee3+f5GyzQidzBJmXd/GFIyWnXKWOY07ep59nYNlXhyVfm7BdzrlnDcUF5w9XegiWbKHq/bBXbAcsdXwCMPnDHj4KhpjpMHipNQozyxqw8UgVqhrbpOe2j8kLI2KDMHdoFPwUH9Crt+9F5uOvov5AFmyWDpgC/BA5dwqG3/MjKX6jkty8Grz+1m4cOlQBi6UDZh8TRo2Ixg0/nICkRHVFqAgv4N54Zw927SlFa1uH5JMPSQvHtVeNxfChai9aqTveemc3Nm8pRHOLRdZmclIIrrp8rNIigI7WQu8u2Ys163K7dVxsbCAuvWiUVHL2JENfo+kLz7ny0XgMPIi8+MoOvLtkn9zos/ccb1fLKxrxwkvb5fCkkqzn3sHBB15Aa0WNtNHxjY2EpaYehx55BRmPv65UtsKPvsbe//cEGnOLYI4Mh298tLQKyX7hPez9f4+77bc2UJSv345dv3kQdfsPi/HM23yS9/bnSP/Vv6UBuKY35eWN+PeD67Bte6H0/mShHR9fI3bsLMJ//rsepWUNymRrbLTggYfWYc3aHBiNBkRFB4hH9cDBcjz48HoczqpSJpsnw3X4wsvb8cFHB+TgGxnVqePosWEl4OUrspXKt+TD/Xjzf7tRX9cqlX75qKttxRv/2y3fU0r1XtiLVgBttYApAPAOYggJULYV9sJVSnVcVnkjPt9XgrL6VvibTQjx85berjvza/DZnmLxtKqids8h7PrVP1GzYx9MwUHwS4yBwWRE8acrsevev4uHVRXUYdRl1Gm+vl06zmwUnUfdxwKBqmBbmv8+thHrN+ZJhWRemAf4eWPfgXL897FNyMmtUSYb+7s+8sRGfLk8C3bYERUZgOAgHykc99iTm5C+u7MQmgq4Dp99YSs+/PigXMpFRflLJFBxSQOeeWGbXNBpTgymKat+nI6cpr+25mjk5ddi3fo8BAf7IDLSX0LhGPLLxtu8kfv4k4NiwKqgtbJavKlevr4SMsWm1XzQ6DIFBiD/nS+kIqUKOg3SdyW0NiA1ASZ/X6kIS2PaJyoCpV9tQO3uQ8o2razn34WlvgEBg5JhCvSHl9kbPpHh8EuMQ8WmXShfu02JbJ7O1yuzpd1QYmIwgoJ8pGcfm8zTy1BcUo+vFRo1Gzbm4fDhKsTHB0mFSbZvYtVJykpP3NLPM5TJ5snwILlhYz5CQn3lIs7s3anj4uKCZK189PEB8Uao8pB/8eVhCSGkwUDvPR/8ms8t+/KwhBmqwG61wF6+TaJY4BMG0HvqZQLMQeJVRW1GZziwAmx2OzblVKGtw4aIADPMJi8YvQwI9KHBasaRqibkVKozuHJf/wit5VUIGJwC76AAeHl7wxweioDURDFiS5atVSbb8q+zUVxcLzotMLBLxwX5iB6h7qMOVMX6TfnIyqmWMGSeSWisUsclJQajsqoJn32p7uJ8R3qxtPWJiQmUllcMqWV4PmVtaLTgo08OKLu4yTxchU1bChEW7itt8Sgb9Ud8XBBsVhs+/PiAGNoajaejDVVNL3bvKUVzS7tsCq4w/IwHqQOHKpTIVrVlDyzVdRLq64pPZBgsdQ2o3JSuRLbavRloKigVw9QV75BAdLS0omLjTiWyNecXo35/NnyjInqF+9CgZn+18nXblcjm6bC3IDd413BLXtrwULJxc4Ey2bbtKILBy9Ar54ifMdfv7t0l4nXV9GTP3jIxRIMCe1dwDA/3R0VlMzIy1ei4ffvLJG+WB19X+Bw9TDwcK6G5BLA0AN5u2vcYfQGbBWjIUyEZqpssqGhoFcPUVcfRaLXZgJwqNV5LekurNqeLYeoqGy8MDUYvlK/erEQ2GlJsV+bv792rRQ51no+vSXSgSh1HOWg899JxQT7YubtE2aXSrt0lsFrt0qfXVbbwMD9kH6lBaWmjsnNcW2tnD3d35zjWNDicValENo3mRNA5qppetLfbwO3KXf4CNwubzY6OdjU3cezZJzeU7mIgDAaRm69RAUNn7VabhHP1Fs0gD6Wy2aww9JW/S9la2wZarFMC3jr3lRNoMnp1N59Xke/DAxpDft1BmbmW2zv0rbkrFuovKYzRe+w4ngwR5dgpka3Ly+FuOvE5qj9lnhCblUUCAIN7/SuDyuJKCuiw2WVsWE+hz9dY1Xi3bJauvcHsPn/XYDTB2twKT9ZxqqCOMx1Fx3GtdnSoW6t9TTfqEZ6VVOlf1hkx9KnjOs9xqnTcqYoupqQG7VHV9CI5OURuVt1tTkzIl7CbJDVVbIOGpsDk74eOht4hXNamFnj5mOU1SmQbkgJzSKDky/aSzWIRLRM0LE2JbP5JcZ0e55q6Xt/jAYonvJBRQ5TI5ukMGxaJ5mb3FwxNTRYMHxaprCgF37vdYnUbXkbPW1xsEEKC1Ted9zRSkkLEoHFn8DU0WBAY4C0FUZTIlhwq6RaManGF89DHxyivUYJfBGD0ATo6Czz1gFWAuQ581RS3YXVff7MRLW4uURkWTBs6WtFa8A4LgX9yPNpre+8NXLtMGwkdP0KJbNRd1CPUZe7gnBs+XF2hp2FDI9Da1oeOa2yTUFbm5auA1X0pF40+VxgVwZzQ6Cg30QcDAPUXP1sarO5kCww0Ky+SpdEcD9pQ1fRi4vg4KWHO0BBnJce8VJY5nzI5QTYHFYSMHoqIaePQWloBa8s3N9Dc6FuKyxA2YSTCJ49RIhurNsaeMwfttXVodzKk6UVtzisWAzpmwTQlsrHacPIV54rXlMaqY9O3dVjRlFsIv/hoxJ03T4lsns6iMwZJ6C8rTzrGjX9XVDZJXuOihYOVyTZ/bipCQvxQUtrYfViibDW1nYbE2WcO6RXOpwEmTYwXY6+0tKGHjuOhnFXOZ0xPkpxQVQfzkSOiUFnZ3OOykF8z7YLf42tUYDCHACFDAWsLwzR6elottZ1GanCqEtl8TEaMTQiBxWpDi9PlDT1uDAsO9fXG8Bg1+5aX0YikK8+TC8G2yupv9IjViua8IvhEhCL+goVQBXUYdRl1mrOOo86j7mMVbFUsmJMqIb6lZU09dVxNi+i2sxeq60Iwa2YyIiMDpFYBK3U7ZOMlYVu7FWcuHCzjpwKe05KSQlBS0thLx9FQnTUjWXJXNRpPR4f+anrBfLef3zkTjzy2UXr4OTYHloSfNCkBN98wUZls3JDG/fUX2PmrFtTs3C+GljxvNCJs0iiM//s9MCgsjTby3pvRVlGDinXbxZgW2bwMCB6Wign//pV4g1WRdtOlaCosRfHSVSKjxEnT25oYK+Om+6m6Z/y4ONxw3UT87909KCisl2HjimB+41U/GItJE+KUyZacHIrbbpkqlWqLijq9NTxnMt/s4gs626xoesND+S9+NhOPPL4R+fmdFzf8TFmoZfqURFx/3QSlOu72W6fhkcc3IfNwZWelWntnKCHbDfF7KttKGOLnwd7R3JmL2t5V8ZqhwH5RMCSfp7Q9zYy0CNS3dOBgaT0au4x8DlWonzfOHxMn+auqSLz0bKkVkP/2Z2jKyuuK47NLob1Rv78DQUPVGPiEOuy6a8ZLK5PCwi49AoiBeM1V4zB+rLper/Ra/uTGSXj5jXQUFnd5pLt03KUXjMD8OWoiqAgLsf389ul46tmtUk3XYeT7+nrjrIWDceH5w5XJxvoJv7hzBh59YpN8pp3nOLsUZpsxLUk+b82JwTtflfe+XqfpnbPBrrKWvIdQX1+PkJAQ1NXVITj45PSStNlsKC8vR3R0NLxO0ZrSLa3t2LGjWCpk8pA0elQMxo6J6VXUQAUMpa3csBPVOw/IyZxhU9HzpsLo07twwEDDqr/V2/aicvMu8aYGjxiEmIUz4B0Y0L/vexxzjsu9dk+GVPi1NrcgIC0RcWfNlnY1mqNTUtqALVsLUVvbIhV2p09NRHy8Z/Sera5uxqYtBdJGgqH5vE1PTelduOVk8r3QcS3t2Lq9CLm5NTCavDB2dLToOU/QcfSC7EwvQcbhzoInI4ZFYuKEuF6Fs1RgZx5qYyHQWNCZs+oXBQQPhsFo9ggdV1TbIhV+2212qQA8LDoIfmZPGDc7GjKOoHz1FrQ3NMI/PgYxZ82Bb7QaD7krrPy7ZVuhRE6Fhvph+rRESR/wBBhNsGV7oRQ6Y9jqFEZFdIW3qtZxHC+OG41VelBp+A8d0rtwoQroQWWboZy8WrmI4xlu9KhoJb2Y++OsPZByb/vhzQg0qztjNlosmPrGy6fc+H1XtKGqDVXN9wQ95zQDiZ5vmoFGzznNQKLn28nlVDdUt9+g3lCd8trpZ6jqlafRaDQajUaj0Wg0Go9C56hq+oTFAQ7m1SC3tEGKFoxIDkNaXJBHhLMwEODgoQocyqiUr4cNjZRwFk8oHEN5juTUSK/Djg4rUlPCMH58LLzdtK1RQWFRHdJ3dfaeY+jq5Inxygo+uMLQVfbNYwXKqMgACWFlmJcnUFvXiu3bi6RQEUN/p05JcNvrUgUcr+07iqT4iYT+TopXVgzIFRYB2pFejKLieuk3OGF8nMdUm7S1t6Ny0y7UHcyGl8mIiOnjpWCbJ+g45pQdOFiOjMyu0N/hkRg5wnN0HFNC2PO1o8OOtLQwyWP0hJBpUlBYJz0uqeMSEjp1HPOSPYHy8kZs31ksazY6qlPHcc16Aq2V1ShbuRmWqlr4RIUj5ozp8IkIgyfAftDUcSz4xPGi/uUe4QmwR3r5mq1oyimE0d8P0fOmIDAtCZ5Ae4cN+7KrUFDWAG+jF0akhSPVQ85xGs3x4BmaW+NxVNa24IXPDiK7qL6zmAeT8L2NmDg0EjecMxx+Cjf9+vpWPPnMVuzeU9LdXoJ5WzRUf37nDKXGAw/lz7+4XRqoO5qQM7930KBw3HXnTKU5jbx4ePPtPfhqeZYckhw91hITg/Gz22dITo3Kg+/SzzOw5IP9UpGQcvE5Glu3/niKVGlVydr1uXjtjXRU17SA3Xop2zvv+eH6aydgwXw1LYcccB08+/w2lJV3VprulM0Hl148EhdfOFLpgYTGzBPPbEZBQb3kkrMuZsD7+6SS8g+vmaDUsGkqKMGu3zyI2r0ZsHd0Vok1Bfgh9sxZGPvnnyktfMact8ef3oy9e8u6K3ZSx40dG4Of3z5DLkpU1i547oVtkq/9jY7zEv3xi5/NQKyiyrqE/TTfeGsXvl6ZLbl5Dh3Hi5Gf3zFD9LAqOL8+/vQgPvz4YLeOIzHRAfjpLVOlaJtKCj76GoceehltFdXdDXuZOzvy1z9BwuIFSmXjRRf31YqKRunV26nj9uKKy0bjgvOHK9VxNXsOYffvHkbjEeZrs5evHZlPvomUq8/H8LtulIrPqiitbMJzH+5DbkkDbFabFMjyNZswdXQ0blg8Us50muNH91FVg2dcf2o8Cho0NFIP5dciNNAHCZEBiI8IgL/ZhM37y/De6mxlsnETeOGlHdi6rUA8bTSyWII9OMQHO3cV46lntrjttzZQvP3eXqxcfQQ+vqZu2VgCPjOzSqp4uuvbOFB88eVhfPLpQTDVJimpU7aYmAAUFNTh4cc2isdQFSwE9Ob/dktzdHpAOHZxcUFye/7E05vFC6wKeu55SGLLAbZlomzx8UFyw//Cy9ux/0CZMtlYvOPxp7aIkRobGyiycfx4YP/fO3uxfkOeMtl4GH/kiU3Iy6sTz1FiYgiSEoJh9DJg6eeZ+HxZptJibOm//g+qd+yDb0wkAoekyIPekKKPVyDj0VeVyUb99czzW8V7HxzsI+tUdFywjzz37AvblOq4t97eg9VrciUKw6HjwsP9cOBQOR59fJOsYVXwsuvTzzPkcpCycc5FRQVI9XpWeOacVAXXItck16ZDx3HNcu1yDXMtq6Jy6x7s//vTaK9rlAJ7gYOT4Z+aiLaaeuz965OoTj+g1Dv+5NObUVlJHdepfzl+nGfcMzZvKVAmW1tVDdLvewCNWfnwS4rt1CODk8FNNvul95H31qfKZLO0W/HMB/uQVVCHsGAfxEcHIj4qAGazF9alF+PDVerOcRrNiaANVU0vGO5LT2pUiB98uyol8sYywM8bQX7e2HawHJWKjJr8gjq5XQ0L85PwH8dNqr+ft5SK37e/HIcPVymRjSGha9bkSPNxlvV3yMaQs9jYABzJrcbO9GIlstFA/nL5YalsyrFzyEYvDY0u9pPcuClfiWw8dC/78jDa220SyuUIbaS3LSE+SPrlrVmbA1XQO0OjNDYmsLtSIv+mkU+vzfIV6jb8tetyUFXVhISEoG7vJMcvMtJfLpyWLT+szKjh5QOriNK4NzvpEVYTZaurL7/O6vbIDTSsGF63NxP+SXEw+vl2y2YODYYpJAhFn65Ca7kaPcK0gd17SuWCiy04HPDriAg/CWnNyalRVl163fo8BAWZEeSk42i0cn1kZVdh9+5SJbJxLn21/LDMLc4xh2yce9RxhUX1MidVwDXIi0KuSa7NHjouIUjWMKM2VJH/3hfoaGgSY4ut3ghD4f2T49Be14D8JcuUyUbdzz2An6GzjuNewT1j2VfqdFzxsnXSB9c/LRHGriI7bI/nGxUOL6MJee98LpdiKthzuBJ5JfWIDveDT5fnlGsiyN8Mf18TNuwuQV2juosbjeZ40Yaqphd5ZY2yoTqMVGeCAsxoam1HflmDslBCtpRwl7fIgxxDb3nQU0FeXq143YKDe4flsXeZzdqZ16UCGqKVlc0ICfbp9T2H8XU4S83BvLHJIh6PoODen6kYD2Yj9h0ohyr27y+TueUaXsZ/83nm6ak6KO0/WC6XDe5C34KCfcRbXlev5jBCo0XCad2E93Ie0uhhyx8V1B3IlnBfh5HqjDk8FJbaBslbVcGRnGq0tHbIhZcrvJyjQZado0aP5OTWiFeS3l1XeCFnVajjikvqJTTfnY7jHLR3zUkVMFqlsLBO1qQrXLtcwwcU6Tiu0eqte2EKDnSr40yBAajasgeqoI7j/ulWxwWZZe/gHqICtnqzGwxi1LtiDg9BS3E5mvLUXE5LuK+tM2XL3TmuodmCgjKGUmuOF7aL9lL4MJymFttp+mtrjoaXBOJ3bmCu2G122TAYvqcCh1F1NLvAy6hONo5NZ2Pt3lBmejRVwDGhojuqbIrGzWT0Etk4t9zBp/kaVfCQa+tjwvF5lXmWLNDVl2zda1XheuhrndpsnYdgVYWBDJSt65DuVjgvg7xGmR7p43sOcVX0QCRs0cHtwZ0ecYylKtm+0b99vEDhWqX+MngZjqLj7DB5q9MjBpMRdmsfA2ezwctbXU0Kjt3R9C/no6r9wctkYo9Ht9+zix7xcmvEDgRHO6Nxrao8x2k0J4I2VDW9GJ4cKqEiTW7C8mobLQgNNGNIgpqqnaNGRknImbt8yrq6Nvj7mzFmVLQS2YYMDpewLoYpudLUbBHPoCrZ4uOYSxaKmtpWt2HBNBjGjY1VIpufnzfGjI5Bfb2ll+FAz35Huw2TJyVAFVOnJqK1pb3X4Zz/bm5qx7QpicqKeUyaGCdeLOa9OcNx5HqQ9RLY24szEIwdHSPGA6Mc3IXJJyYEI0FRcbGIaeNg9PWRcEdXGPLrFxOJsPEjlMjGzywwwNyHjmuVaJLRI9XokWFDIxARTh3XW7ampnaYfYwYM1qNbJxLnFPu9C/nIOci9YwKuGeNHBEla9JVx3Htcg1PmqCmYBx1V8zCGehobOo0rpzgvzuamhGzaCZUQd3fOUa9dVxDvQVjx8TIHqKCiBnjxRi1tvX26LIoVfCwVASkqNm7RqSGwdvkJdEZrtQ2WBAR4ou0hNOnF+fJLKak8nE6og1VTS/YgmbC0EjUNrZJDgMP5B1WG6rqWmGxWrFoUqLkq6qAeSlnLhyE5iYLqqqaZfPio7q6RULS5s9NUVZZl5vlhYtHyHixBQE3V37NA2dVZQsmTojDKEUHTBqil1w4Ej5moxTtoHEqG31DG0pKGjB8WKSU+1cFKzeGhPigqKhBDpWUjZWJi4rqkZwUgvlzU5XJdtbCwYiJDkRhYT2aW9pFNoafM+ctOjoAZy4aoky2ubNTkZoSiuLiBhkvysbxKypukMMxx1UVkyfFy+G8tKxRQuIpGwt8MNyXoY4XXTBCmYcrbOIoxCyYhtbSCrRV1siB3NbegZaiMtjbO5B2wyXwDlLT3odVcxcsSENjQ08dx68Z4njG/EGIiVEjG0OPF58/XNpusRVSt46rbZVQ7imTEkSXqIBziVWuObc4xzjXOOc490pLG2Uusk2NKi5cPFzWJNems47j2uUanjs7RZlsKT84H37x0WjMzhfDlLJ1NDbLv/0TY5F8xTnKZKPuT04Mkb2gh44rapDq14vPU6fjYhfNRNiEkZKnaqmtFz1Co7Upt0jSCgbdfLnkrKpgeEoYxg6JlHNbfaOl+xxXWdMiX58zI1kqAGs0no7BrrJ8oIdQX1+PkJAQ1NXVITj45Bg5NpsN5eXliI5m37tT7z6gpa0D7685gq0Hy9DYwjL/BvGk0kg9Z3qy0pARVvt77/39+HpFVucNNewICfKVw901V43rLtyiAi6nz77IwKdLM1BV3SwbAj0g06cl4cYfTuzXnqDHM+fWrMvFe0v2SqVJHn79/L2l/+GPb5osxVtUwkJTbC1BA5C939hzk4feH988WXnfzczDlXj5tXTk5FSjzWIVgz8lJRQ33zAJI4ZHKZWNBYteeHkHDmVUyO05b9HpXbr26vH9evlwPPON3q2XXt2BXXtKxftsNBkQHRWIyy4ZhTPmpyltK9He0IiDD76Iki83SMEYhvuyHUfa9Rdj0E2XKTtgimzt1s4K4quOdOcYM/dy4RmDcPWVY8UYUwV12qefHRI9xwtCh46bNTNZ2jX1Z0/QY8056t9Va3Lw/gf7UV7BWgt2ySNn717qOBaSU8m27UV46+3d0lOYOs7P1yT64yc3T1bauoywsu+Bfz2H+owcWFvbJOIgZNQQjPrtLQgbpya6wLny74sv75Cewq1tnTqO3vPrr5soF8AqdVxLaQX2/+NZVG5KlwgNg7dJjPuhd1yLxAsXQiVNLe14+6tM7DhYLl/zwjo82BfnzEzGWdOTB1z/9sdZeyDlTv/RzQjqKpqlggaLBRNfevmUG7/vijZUtaF6VFjdl4WTaJgy3FeVJ9Ud9FSywi8N1SGDwhEertbQcoY3v4cyK9HRbhWDZiB6Cx7vnONtNFuusCgLK7ImJ4fCU+AlREZGpVTZZRj14EHhHtOYnAdyFpyi8cWbfIZBqsrHc4VqnJVg6eWioTBiRKTkr/YnJ6LjeNCk54ihoSOHR0mVWE+huagM9YeOSJ5e+MRR8A5W4610B0OkHUXO2KdUZY9oV7hGMzIr0GG1Iy0lVHoe9zfHO+eo26jjLJaOzlYwilJV+rqEOJRRKXsE2zalpYV5jo6zWlG7+xDaqmqlcm3ouOFKL2xcdRwLdbEoIC9GaOD3d0TGiei4+sO54kk1+fsifPIYMfQ9hfLqZimcxPEamhQCf18157hT3VDd/WP1hur4F7WhelqiDVXN9wE95zQDiZ5vmoFGzznNQKLn28lFG6rfjYbT1FDVK0+j0Wg0Go1Go9FoNB6F58RfaTwO5tGsXpsj/d0YMsLqe3PnpHhE+FlFZRPWrsvF7j2lUqKeeZbz5qQqKzLijN3eAau9HjY7K4raYIAfjF4h8DKoDwViEaANWwqwdXuRhJ6lpYZh3uwUDBsSoVo0lpgEUAXYGOrYzvJUgBcLs4QoL3fHsN8dO4uxfmOeFMqKig7A7JkpmDIpXnn4L4Ni9u0vx7r1uSgoqkNoiC9mTE/GzOlJSvO1HWTlVGPt5gLk5NXA388bUyfGY+aURAT4q08jYEjy2nU5OHCwQvI+WQBq7pxUGUPVcJ6tXZ8r+b1kwrg4KSwTFRXgEWkXnG8700skXJ+Fiqh/VeeSE+q1DZvysW1boVRbH5QWjgXz0zBksHodxyJ2m7YUYPOWfBnDpIQQmW+slKw6/Jc1C7bvLMaGjXmoqGiSUO45s1JkTahqI+Ws4/bsLcX6DXkoLK5HWIif5ERPn5aoNF/bQWZmpdR/yMmtltSL6VOTMHtWsrJqxM5UNbXhUGkDiutb4W00IDU8AMOig+DvAXvDqYbqyrsGz8gQGHB06K8O/XXLrt0lePypzaiqboHZ7CX93zo67EhJDsGv7p2jNOeHeSr/fWSDVKtl7zmu3fZ2G2KiA3D3L2YpLW5js1vQbiuC3d5ZAEUa0krHRiO8vWJh9Oq/XNVjzbm6+lY8/MRm7DtQ3tl7zuQlBydurNdfPU6q2yrDbgVsWZ2GqowX5afhagQM8YAhSZmW5gHuxVd24OsV2VLllMYfx43jd8aCQbjlR1OUVa+l+n7/w/344MMDUkjJx8coa4HTbtrkBPzi5zPh10/5SMej41atz8Vr7+6VarUcN44ld5yRQyNxz23TEBKsziDcsbMITz6zRXKORTabXYrvMN/yV/fOlfxtlcW7Hn50o1RM9u7qr8nPNTYmEPfcNQvDhqqprEuKS+rx4MMbkJtXKz16Wb+grd2K8FA/3HnbdEzqx8q6x5pzNP4efGg9Dhwql37gvETiWmVO4803TsLCBYOgipbWdjz2+CZs28H9AfK5trVZpaDSZZeOwuWXjlZmrFKvPf/SdqxafaSXjjtr0RD86KZJyi7kqOPeeW8vPvr0ECxtHSIb83w5VixS+Is7Z8DHx6RMx331dRZeeyO9uz2TtAqz26UV0i/vno1ghTouu7IRKzMr0MzP0ssgl/qce1GBPjh/dCxCBtiQPtVDf/f8RH3o77gXdOivRiM30s++sE1aDiQnBSMuNkgqEiYkBCEvvxYvvLSjVy+4gYKbwHMvbENpaQMSE4PlMBkXFyRfs5DMs89vk01MFR228i4j1QyDwQcGA5UaH1Z02MrE26qKDz45iD37y8SgZ8VEHnqTEoPFeHjznT3IL6hTJhvs9BpVdo0Vi2L5dv1tAOxFNLOViUbvDI1UHnaTkkLEa8+/g4LMUpV13YZcZbLtP1CODz46AKO3F5KTO2XjWogI98OWbYX4/ItMZbIVlzbgjSX7ZM0mcb5FByIhLlgKyOzLqMB7nx5UJltDYxuee3E76uvb5LOMjQ2SSsnUJzm5NXj5VXU6jh5K6rGy8kb5LOOcdByfo27ma1TAMZHq17k1MlYcM44d24ewDcxzL22XsVUFDZp9ouMCpYhSbCzXarDsCa++ni4VslXBtcg1GR7uJ58l1yrXLNcu1/CBg+XKZKPnnrqMOs1Zx1HnLV+RhY2b8pXJRk8qjVRemDtkS0wMQVi4HzZtyceyrw4rky0/v1Yq1fOSi/OMeyr3VrYto9xLPtivTLZmSwfWHK7svETy9xajNMzfLH+XN7ZhfTb3W82JoLqHquE09ahqQ1XTi+07ilBe3iSbvPMNL29U2cKEYS5HcmqUyMbNnIckhr85hyPx65iYAAnl27W7M1RuoLHZ22CzN0tEvfO4dX5thh0MCW5QVqFzw6YCBAWYe4SDUraoSH/ps7ZxS766kF97eZc6cg1HouFqA2wVamQDsHpNjoT+sgeiM4GBPp3tMFbnKJNt3YY8tLR0INyl7QZDzni7Tw+JqoubTduLxHjh/HJeD2ZvI4IDzdiyo1i8/CrYuq1Q0gdcdRw9SDQkqGd4KaeCffvK5dKIh11XHcfn8vPr5DUq4JhwbCLC/XtEEXAMaRxyTNmCRQX0pm7aXIDgYJ9eOo7jVl/fio2b1eg4rkEaglyTruGgXLuMhli7Pg+qoJ6gLqNOc4Y6j1EGbPmjCobU0pPqmnLENAJWNue4ihdT0SVmgzsdZzZJpJJ8X9HFTXZlExrbOhDs593zHOdlkLDfgtoWVDdblMim0ZwI2lDV9IKeSW5a7sIZ2ZOOmypzWFTA9+Wm5K69BTcHOkF4WFKBXfIquWH2HjfHRkFjVQWV1c1obmmXz8+dbNy8SsvVjBu9zQA3zL5yZjieLVBFYVFdn+1U2IeWudyqvG+FhXV95qEyB5Q9OHlJoYKyiiYYvAxuwxl5yGQoZGW1ms9V9Je98/Ktl2z+5i4dx0ungUf6f9psbsMZ+Ry/x9eooKKyWVq/uNMjsl/YO/cPFVRWNkkOfl86jo+SUjXjRmOGlzZ95WXz8oZrWQXUXdRh1GXu8PMzSW9rVRQV1YuB7w7/AG9p4dSsyOAqKWuQkGB3Oi4gwBtNze2oqlKjR+pbO88aDIF3xcfkhXarDQ1dr9FoPBltqGp6wXAfHrvpRXKFeSvMrenPpu7Hko2bgjsvEUNYeVJSJZtBDC1uCr1vdx2GjEHRkgv0N3fmRFnce9f4WQe7eAwHDmPXo69bcY6duqIULKzDee8OS5tVPDiqcsvoZejoIwyU+W80Yn391NTMC6IesTEnyo0eaWf+mxGBAWo+V4eOcCubpUM8NTxoqoBeLQMMbr1EfI7fc/V8DZhsAZ16hGPkTodwOPkaJbIF9q3j+DnzERKsZtxoBHItck26g2s4zCUqYqCg7mKuOHWZO6j7QkPUFQIMDfVFu8X93kCZeXmjqmhRSJCv5LH2qX9NXsrWqq/JS3ZOdzqOvY95OU2DVXP8ME1Z9eN05DT9tTVHY8qkBDFaqqp73gRS4fFGnTkYI0aoKeYxbmys5LhRDlcFzEbg4eH+mDQhTolsBvh2VfbtcLM58GDnBS+DmqrEkZH+GDs6RvKOXS8geNvPQxSL7yjB4AUYIrrGyHXT7zo8SfVfNbAqJw0EV2OV/+aFCSuxqmLmjCQ5aNJb7nppQ08qv99fxZSOxZQJcfD1MaHBxaPL+Vdd24rRwyMRE6VmPUyZnCCGTbWLR5frlnokOSlEWcGiCeNiERnhLx5CZz3SqX+bZC3zNSoYOiQCSYmhqKzqrX9ZlIo5jhxbFTD0ePTIaNRUt7jRcRb4+nork41rkFW4uSY7L1S/gWuXa3jmjGSoYt7cVLk86q3jOkT38fuqmDUjWXLzGIHhDOViVWdWJlZV+XfqlASJ5OIe6gznH/facV1rWQVpkQFirDa5fKZctwwJjgzwQbSyy2mN5vjRhqqmF8z/ZAVCa4ddwm5YcIRKt6CgXm7Lr7tmvHgcVMCb02uvHgcfM0Ol6lFX1yp5bgybYg7XD64Y2yuXcKDgYcPkxYrDRtjRJoWTOh/cxOwwGsKVtqi54pJRkpNXUFiP6poW2VxLShtQ19CGBXNTMWqEumrJUtkXNFqYs8jxanf6OgyAutYSZ8wfhNGjo1Fa2ihhjRw3ho6yoNfIkVFYpLBa8rSpiVL5sqqyGWVljSIbQ824NlJTQ3HR4hHKZBsxJAIL5qSIoVpC2RrbJEyvoLgeMVEBuOLCkcpkY4G4iy8cKQdzVx0XFOQrOk5VJWd6e6/5wTjxOHfruDrquHrRu9dcNU5Z1AjH5LqrxyEo0EfqAXDMGNLKMaShc8mFI6WgjCr9+4Orxsr7FxTUieHM9VBc0iBzb9EZg6SNjiouumCErEl+jlyjlI1rlmuXa3jaFEUXhQDOXDhY9D/3A+o2ykZdV1raJLqPOlAVNOB5wVBZ0VPHcc6x9dD55w1TJhsr+7L1EfUHuxBQNl5+cW2wANqVl49RJlu4vxkTEkOl0FNNswUt7VYpsFTdZJEc1ZlpPI+cptV5NKcUuj2Nbk/jFk6LjZsLsOyrTOTl1cohgEp58XnDMGpktEe0z/nsiwxkHq6ScLMhg8Nx3jnD5IZTNTZ7CzpsNV19VO1inBoNofAyBPdriOjxzDnmGi1dloltO4vkRjoqMgBnLhiEM88YpOxg3g0NensxYGeLGt4CewOGaMAQBxjU9nzjQfezzzOwem0umrnR+3tj7txUXHDecIQo7rnZ1taBL748jJWrssVwYD4XvRAXnD9ceiGqnG8dVpu0qFmxNhfllU2SEzp5fCzOP3MIkhW2uHLoOPZlXLb8sBg2PLSNGRODCxePwPBh6jz4DnakF0ul2KxsrgdgyJAILD53WL+2fzleDmVUiP7du79cwrvpgT77rKGYMytZuY5jJdaln2dIUSfm89LTeuaiwVh0xmDlOo69cSkbizoxbDU0zE9a5px3ztB+a7FyvPAyZOkXGVi3LhfNze3wDzBjwbxULD5/uFxMqITe1C+WHZaiT5TTx9ckfVSp47iHKdVxHTapjMzK8Ix44BybOiURFy4errSNn0PHZZQ3Ym9RnRROoo5LCvMTAzZWQducU709zYGf/ghBPgrb07RZMOrZl0658fuuaENVG6pHhdODt4Q8YKq6xe8LCWFp6gwrpKdXdcN0V+zsDdrVQ3UgZDuROceCKG2WDhk3Vf3x+kTGjQ9TZ1iwB8FQXxbIYFEUT2g03ysUrokhjqYBOfSeyHyTUOTmdpi9vZSFIveF1nHfHs43VoVlyK+n6TgWVnL0UPU0HcfLJepgzjfVxvOppuO4Hvw8Vcc1WjorO3ugjmtptynPS9WG6nej4TQ1VNVe4Wk8Hh4+VDasPpZsqm96j4ZBsRfwaNCY6auSrXJk3Dxz7HhwCw3xTNl44FXt3e0LGgohHpoPpXXct8fTDHvXNBFVRXaOBY0s1R7UU1XHsbidx+q4EM/VIwz31Xw3pIq9U8swFe9/OuJZV3kaj4NFAVhUibllnuZ8pzzV1c3y8ETZ2NePRUdU9Xg7GpJnWdmkrMfm0WhvaERLSTmsrWr6zx0rBI3jRm+Np0EvDWWjl8vTYFVTFgdyLTriCdisVrSUVqC1stoj9Ygn6zjuC9RxrgWCPAFLbb3oEVu7561VrlGuVa5ZT4O6TXScS/EiT8CTdRz3Usqmqm/q0bB1dIiOa6uqUS2KRnPCeOaVnsYjDiFstL3sq8PSY425DUOHRuDC80dgvKKKk84w/2jp54dwJKdT8aYkh+K8c4dhVlcVVJUcOFiOjz89hIOHysXQZ+GWc84aioVnDJKCTyrJya3Bx58eRPquEjlcskoyC2kwR0p1mFdTQQmyX1qC0uUbYWuzwDs0CEmXnIW0Gy6Gd5CaAi0OeOnwyacHsW5Dnhzk6KmZPTNZCvKoaivhgPIw723l6iNiCPJzZJElyhYfF6TcQF22PAsrVmVLz1ST0Uv0x0XnD5dCKKp1XOGHy5H7v6VoyiuW2+qwiaMw6KbLEDl9vHLZtmwtlDzQ3LxaeY6FeJgTzcI7qtm7rwyfLD2EjMwKsDsH59k5Zw+RfEvV+rfuQJbokYoNO2HvsMIvNgpJV56L1GsWw8tbrYeVhZ0+/vQAtm4rEsOGhf84Zsy1VO39ZfGpjz49iA0b8yQsmfLMnZ2Ciy4cqdyLScP0088OYfXanM7wWm8jpk9PwsUXjkBsjFodx8/x82WZWLEyW4oU0rM6aWKc6N/UFBYCVIfdZkPB+18i750v0JRfBIOXEeFTxmDQTZciYspYpbJpNMeLzlHVOapuef/D/Xj3/X1SKCMo2Ef+rq1rk3yfO386HdOnJSqTbdWaI3jhpe1obbUipKu/W1195wH9ph9OxLnnDFUmGw3ARx7fKFUAKRs3LX5thx2XXjwK113dfwfgY8257CPV+PeD66SgB2XjeHHTZ7VOVnW846fTlOVyNReVYettf0JDRi68w4Jh9PVBR0MTOppbEDN/GiY/+nt5TgU0/v75n7Vy8eDvb4afn0kOchy74cMj8btfz1cW8sUcvP8+sgFbtxdKKDfzythHsqG+TQrc3P+b+XJRomK+8ZLmuZe2YcWqIxKyx36CjC5glW62bLjv7jnS7kQVGU+8gaxn35ZibObwEMBqE4+DOTQYE/71S8QsmK5MNhZmeemVHfJZduu4ujb4+BjxoxsnS3EgVWzfUYTHntwk1ZzZX5OXb9S/vMxktfgfXNl/B+BjzbmaPYew4xf/h5bicpgjwuBl9hbPqr29A0lXnINxf/mFMkOaFXX/+e81yC+okz2VleuZC0pdMm1KIn5592xpE6YCFij6x3/WICOjUvZ46pKWlg40N1swckQ07v/NPGUh6ByfBx5ej53pxZKbykJ27FFKvZySEorf/2Z+vxWNO9Z842XvU89ulTMJjWeOHQ1XrlUW8frNr+Zi0CB1F3KHHnkV2S+8K7UezGHBEj1iqaqFT3gIJvznPkTPmTKg8pzqOaqH7vyx8hzVEU++eMqN33fl1LOgNP1OaVkjPvnskCje+Phg2aCYw5WUGCzem/+9u0dZyChvVt95d68ceBMTg+VGmg/2djXAjvc+2Cebrgoo01tv70ZjYxuSkoLFeOHGFR8fBH8/b3yxLFMOKapY8sE+MVKTkkIQGuon+WUxMYEIC/WT6qf79pcpky3ntY/ESA0YnAzf6Ah4BwfCLyEGfgmxKF+/HSVfrlMmGw2tg4cqEBsXJD0sOW4REf7yuWZmVsn3VbF5awG27yxCVJS/9BembOFhfrI2ONfohVDFgUPlWLs+T9YBvR4sBkTPTHJiiISLLvlwn7Jw1sbcQuS8/jGMfn4ITEuEOSRIjNXAISlor29AxqOvSbicChg6+M6SvbDZ7T11XGKwtJrg91SFF7Z3WEXH0cDifsB9gRcQCfHBknNJz35xcb0S2TiXMp94Ey0lFQgcmgqfyDDRIwHJ8TBHhKLok5Wo3r4XqqAHmmuSnyPXKNcq1yzX7o6dRdiytUCZbF+vzBZdRp1G3UbZqOuo8w5mVGClQh23aUs+du0q6RqrLh0X3qnj2JGAc04V3DO5d3IP5V5K2bi3co8tK2/Ekg/3K5Ot/nAuct/8BMbAAASkJsA7JAg+4aGi43h5k/Ho62K4ajSejjZUNb3YuatYbstdQxp5E01PSHFpg7QnUBVyVlHZjEg3Jem5wbI9B72aKqDHkv3TIiL9e93ah4b6yuGOBxIVsDfevv3l8pm6hh/TmKZnjuHUKrC2WVCybB1MIYHwcunPa/L3lcLJxcvUGao8iJjNXnJx4ww90vRwrV2Xo0y2zZsLZHxcq0zSM07jZvOWAmX5tDt2FqO1raOXJ4ZrIzzUDwcyKqUvogrKV29Fe10DfKLCesnmGxeDxiMFqNl1UIlsu3eXSi9G6jNXqH/5Pb5GBYcPV0mLK8rhquPCwnwlyoBtdVTQXFCCmvQD8ImOgMHF+0UvubWlFWWrtiqRjWuQhijXpGvUCtcu72s2bVFnqK5dnyu6zDX9gzrP29tLdKAqNm7KBwydBQCd4Thy75JWPxY1Bhf3TAur5wf29LJxj+V66DyvsE3dwFO+ZhvaG5rkwqaXjouNRsPhXNTtO6xENo3mRNCGqsat15JHEHf5lAxN6mi3idGlSjaG0bor6e84ADS3qJONXlVXg8axOXA0lY1bswXtlK2P0DKe63g5oQJrcwusbW0w+rgPLfPyMcNS3Zmnp4L6htY+83e9zUYZN1WeQYbR9tXegsZ1e7tNQudUwDYSfTWUFz3CdjqK1gMPcLIm3YTzGX3NsFnaJfRc1VrtS8fxOX5PnY5rl9647vSIjKdBnY5rr2+UwknGvkLzvLzkckKVocq1yDXpDn6uTBFR2Z6Juswd3M/qFRZB47jQWHYHZaaRqqooVX1jW5+pXVwj3HPZk1bVeuCCdBfqzj2Va4WFCzUnAMdS9eM0RBuqml4wt4LrgWFe7g4qzNHja5TIFhMIb5PRrZeImxX3jJjo/msAfjRiYwLl1pdj5IqjKqYq2SScy9/bbbVEHlSYT6iq8I4pOFDCffvaNOkJCRqWBlUkJ4VKvpY7mpsYAhmiLO8tOTlUDmruDGXOQ3ryA4PU5NQwN5ZyuZWNfRr9vCW8UAX+iZ0F4WztHW4PeMYAP/h1vUaFHmHRKXcXDHyO31OlR/i+9AC60yOO6uaqZPOLj4YpIADt9U1ui8qw6lNASrwS2ZiTKlE1bvYGrg+uYYaLqoC6izqspQ+DirqP31cFc+2Zk+oOzsOIcH/JW1VBQhzD8W196t/AAG+RTwUBSdRfdtjcnOM6GhphCvDr1oMajSejDVVNL6ZMSpCcsrKyJjFgnA8i1TXNGDkiCqkpoUpk43sPSgtDRUXPlgiUk/LSqBg3Vo3yZT7vhPFxUj3ROYeXm1hpaaPk10yfqqYIFcMvWcGRm6fz7TNlKy9vklyz2bOSlcjmZTQi6fJzYJcb3qYesrWWVcmGmnDBGVDFGfPTYDSyYEzP3Gd6GRh1cMaCQcpk42fKQxrDQZ0PS7zF5+F30RmD5GJHBbOmJ0l+KnO1nGXj/GOO5cwZSQhR1L80dtFM+CXGoDm/uNOI6YKe1LbyKkTOmICgISlKZBs9Kkb0K8P1nXUcv+Zz/B5fowIaU2NGx8h8c77IdOg4XiROnaxGxzH/Lv78eWivq4e1pa2HbM2FpRICGXfuXCWycQ1yLXJNOnvYOtsPtcga5lpWxcIzBovB6uo5pc6j7lOp4+bNTYOfj6mXjnNEMC1aOEhZEUDumdRh3ENddRz32jmzUnqFBQ8UMYtmwj8uGs35Rb11XEU1omZPRmCquqKYp3IfVZWP0xHdnkbTC26at90yFY89tRmFRXUwmYxiCFIRp6WG4cc3TVbmQeKG9NNbpuKhRzagoKgeRi5cAw9xdsTFBeG2W6cpbbNy4/UT5TB5OKtKlAoNmY52q7SB+elPpopBqIrLLxstxTx27y2VvEYeQCztNgQHmXHTDRORmKDu1jzlmsWSL1P8xVq0lVXC4O0NW1sbTIEBGPLTqxExbZwy2WbOSJbPk62aWG2PYWgMf+c8O+esIZijyMB3XNxcdcUYvLtkHwoK6iQUjrLxs501MxnnnTNMmWyxsUFShful13ZK7jbHi2Gj1Bzjx8biqsvGKJONRXZYAXb37x5CY1aeVIflYc5utSF03HCMvv+nynQcw0Cpx1jNuai4QT5LrlfquISEYPleX+He/Q3H5Ec3TpK+rszJ5xAx9JFtiFg3gPuGqoM5GXbndWjIykfVlt3iTTKYTNLqioWy+JmysJIquBazsqslV7Wqqhkmby+0W6xSSZxrmGtZFdRhhw9XYvmKbNTVtops1CMMXz3/3GGYOV1dS6Qxo6Nl72IngoKCenibO2XjWWDenFRp/aYK7pncO19+dSfyC+ph5rh12GSdsEXNFQp1HC9uxlLH/eHhLh1nhp3Fk+x2hE0YiVG//oky2TSaE0G3p9HtafqEFWJZtZPVO3nInDg+TnpHsiCEathongUedu8p7d7M5s5OdVuAZKBhQREWeNi5s1havwwfFom5c1L7PbT2eOYcb3o3by3Etu2FciPNiwfKxr9VwyqrLABR8tV6ufENSEtEwuIF0ttSdW9Gqsk9e0uxYWM+yiuapFLnrBkp0hNUdW9ckpFZiXXrc6XnMW/4Z0xPwuTJ8f3qTT1eHZebXytrNSevRsJGp05JwPQpCfB1KQClqndv0acrUb3zgOQ2Rs+bKl43VgFWDY1B9u1lQRYydkyMeN146aUaesRZ5IYtQ5h7SSNr7pyUfu9peTxzji2tSr/eiNIVm9DR2IKQUYORcMECBA9X5xV0QC/0jh3FUuSM3kpWS6b+5R6hGl5Gcz/duClPChayyi49hoxQ8gT9y8rr6zfmobi4QQoVzZiejMkT4/v10uZ4ddyRnGqsW5+H3LwaqfzLPtaMnmIlbNU05RWhaOlqVKcfkBZv0fOmIP7ceXJZN9Cc6u1pMn5xi/L2NMMfe/6UG7/vijZUtaGq+Z6g55xmINHzTTPQ6DmnGUj0fDu5nOqGaubd6g3VYY+cfoaqXnkajUaj0Wg0Go1Go/Eo1MclaDwaNm8/klMjeVIjRkRJY2tPobKqWUIeydDB4YhWVInYHXV1rRKqxHyV1NRQpfmfrrC1xYFDFVJBNC42EINSw5SHdjmw2uwobWyDpcOGALMRUQFmj5GNocnVO/ZLWDILs4RPGQMvk2eoUAbGZB6uksJFDD0bMyraI8LOHLIx/Jf5luzVOGpEFAL81d1Ku8Jw6ZycGgkhZAgrC0B5Ci0l5ajZfUi+Dhs/An5x0fBEHZeWFiZhrJ4C0xr2HyxHW2uH5PUytcFT9AjTL/YdKBcZWT1/2NAIj5GNockHD1agtq5V9nquB1X50O5CkzMPV0pYMvOgR4+M7rPdmgodx3MSz0tMaRg9KlpZJWJ3sPcxw5JFxw33LB2n0RwLzzjJaDwObqIvv7YTW7YWSj9EbqOhIb44+6yhuOKy0Uo3L1ZOfOvt3Vi1Jkf6vzF4PSjILLk+1187oVdj8IHeTD/65AA++yJTqv8yrp5tYSZNisdPbpqstJgSN9OVa3Pw/icHUVHVDJvVLq2GRg2Pwk9umCQ5SSopqG1BenEdGto6wGLTnGLh/mZMSwpFmJ9aw6Z6xz7s/+ezaMjMhdXSLsV3ggYnY9T9P0WkwkJPJD+/Fs+/tF0KPrGNAy+VWMjouqvHSSEo1ZdJz7+6E/sPlcvFCA/kURH+uOSCETjrjEFKD+jMs3zx5R3YvqNIKnRSFLYQYdGbyy4ZpaySKLG2tuHQwy+j8OOVsNTWy3PmsGAkXrQQI+65WXLNlMlmteGDjw7giy8zUVvbKvpX8vKmJODmmyZJhXGVOu6r5Vn44OMDqKxsFn1MHccqybf+ZAqiItXquE2b8/Hm23tQWtogxbF4cTN0SARu+dEUaTWlkn37y/DSqztRWFgneccsGpeSHIqbb5yEUSPVXpDwouuFl3cgO6da9n/qOLa/uu4H4zBtSqLyWh7PvrgdBw+WSysf1iyIivSXQkqsSKxSx7EH7Yuv7pBccoeO4wXE4vOG4eILR3pEfYVTCo6XyjHzOj0/L8+4KtN4FNzsn3p2K1aszJYNISkxGPHxQVIYaMkH+/DBR/uVyvfG/3bhk6WHYLN3VsFMTAyWyr+fL8sU41oln352CP97Zy9aWttlzDh2LES1bl2uVFF2bjcx0GzcUoCX3tiFmrpWxEYHimx+ft7YsasEDz+12W1v2oGirKENm/KrxUilJzXE1wgfkxfKGy1Yl1ONZov7PnoDQUNWHnb+8l+o3XcY5qgIBA1Nlb6v9YeOYNd9/0F9xhGlRcUefHg99u0vlyJnSUnB0gaprKxR1rBUeFZEa1uHzKvt6UUyzxITgqVHaG19K159axfWbsxXJhsNmCef3oLVa3OkwinHjeuVhv477+3Fx58ehEoOPPACjrz6kVx0BQ5OlgcNwiOvfSzfUwnHhmPEsRIdlxQMk7cBK9cckTF1bmk20KxZmyt7AL29sbGBIhvn3vYdhXj40Y1ue9MOFLv3lMia5NrkGqVsXLNcu1zDXMuqoLft4cc2Ii+vBuHhfkhODkFYmJ9UdubzrNqt8rLroUc34OChcoQE+SA5MQRREQEoLmnAU89tFc+5Krhn/vfRDdixs0g8qPxMY2ID5LN88ZUdUnxPFTxrPP70ZqxdlytnEDnHxQXJ2eStd/bgsy8ylMmm0ZwI2lDV9IIhhDvSi6W6JENEeCNI7wIr6jKc8MvlWah36Sk5kLeXq9fkSOhPeJif3AjywVvCkGAfbNiYp2xTZX+8L5ZlwmTijWqAjBnHLjjYR1o37N1bJiFfqjatT5dlSmgXjVR6xClbYIBZwn+zjlRj644iqOJQRSPaOmwI8jFKyyHK5m30QrCvEXWt7cipaVYmW/57y9BSUiHGgsm/0yNu9PNFwKAktJRWIO+9Zcpk4yGkoLAeCQlBclDiuDEcLi4uUCIhln6W4bYZ/UCwfWcxDmdXSSVYzjPKxnnHcMcOqx1Ll2VIuxoV8NC7a3cJIiP9pUqyQ8fx3xy/ZV8elurdKmjMLUTRp6tgDguBb1Q4DF5e8uDX5tBgqeDJSp6qvNBffHlYxohj5dBxHMPICH8Z00MZFUpkYwjyJ0sPiq5jP9duHRdolgiDjMOV4j1XAdfg0s8zZE1ybXL8KBvXLNcu1zCrxqqCe3pVdbNc/DpSBhiZxEtghtp+vTJbmWzrNnRWM2doOS8dCMcvIS4IDY0W8eyrghFnWVnVYgAyqkD2LZNR5ht7qfPiWtXlNC9AWKmelyLBQT7dOo5nE2+TFz5blqn0cvpUxMtgUP44HdGGqqYXBw6Ww0LPVkDvHAuWhWe4V0ZmlRLZmBPFA6S7HAsahDQW+RoVZGVXoaq6BaFhvfN4eSDhxsXwIBWUlDXKZs/wbVd428qD1D5FslmsNpQ3tokH1TVMSpSzlwFF9eq8DeVrtsIU6C/GgjP8tykoEOWrtigzBmkYGE2dB5Aesonx4CM5XaoMrgMZFeJdc5dHxhDbktJGyZ1SItvBCgkh9O86+PaQLcwX1TUtEkqtgpqdB9De0CS9P13hc+31jZIrrQIeypnSwDFyp+M4ptw/VMCe35xToW7qKHAOMtVhv6KLQqaocM/kmnTVcVy7XMPpu4qVyEbdxVZqzBt3lU2MaT+TMgOf7NxVInuUOx1HA2z/wQrZ91Wwd3+Z6Dh3vdup43hpXlLaoES2gxkVcuZwr+P8UF3dIhfUGo2now1VTS8kdMvQuRG4wue4sTHsVolsXe97tIslHkhUwHHh42i3XqrC4vi+fOc+ZTOok42C8Z2PIpqEParCxibpBveq0uBlgF1hODeLT/X1mYpsdru8RuWccwdl5vdUGfiOue5Ox4ls9m90zUBjt9kgo+PuczUY5Ht8jQo4JhwWd3POMZaKROuab/a+lmrXa1SNG/+kbO7XKsdTZcg0dUSf+peyKdpTie2osnFUFZ5HjjFuXMYq10Nf+bGdOs6u9HPVaI4XbahqejFkcDhMRi+3YSHM/WFezeBB4UpkGzwoQm7uGxp6e4lYLIDhSoMHq5EtNSVMPL0cI3eVHpnvO3hwhBLZ4mICER0ZgLr6tl7fY/glt7PhQ9U0nfc2GhDu5422jt6bpsPQilFYoCVyxgR0NDT1Mqr4b3q3ImeMV1YwY8yoGLk1d3fIra9rQ0pKqHhxVMBK3DwQMSTTlbr6VgkVZcicCoYMiRAPjbucxbq6NonOGJQWpkS2kDFDYfT3R3tdb08MnzMG+MtrVMAK4RwbjpErHEujyUuZ/mVoaGS4v8x7VzgHuUSHDlGj47gGWZjInWxcuyxexIJPKqDuGjsmGk3N7W51HL2VY8eqkY2MGR2NNovV7aUWPdWDU8OlYKEKRgzrnE/uwnup46KjAyS1RgVDBoVLNBLPHr1kq2uVcH1Ww9acoMXkKKik5IHTktP019YcjTGjY6QsfXl5k7QyIdwkWEGOOSHz5qbKIVMFyUkhmDI5QcKPGdLo8GKySnF1dTPGj4sVQ1sFNFIXLhgkGzs3AsfGyuIFpaWNGDI4ApMmxCmRjaFJ5ywaLIcihjU6DBuG6pWUNCAhLhgzpiYqOygNjwqU3NRGqfjbKRsN1Po2K/zNJgwKVzPfSPKV50rF1ea8ItjaO7pb1TTnFcM7JAjJV52nTLZ5c1MkB4ltEWiwimw2O8ormqRqJyvYqjKip09OlAJKnF+OwxLXBENHaTics3AwfMxqKnSPHxuL4cMipbCN40KOsvFwSV2yYF6aslZcwcMHIXr+VLSVV8lFiEPH8Ws+Fz1vqrxGBSyww7HhGHGsunVcS7uMJQ/uHFsVMLfy3HOGydyijnPIxrlXXNwgBYJmTE9SIhvXIGXjmuTadOhfrlmu3agof1nLqjhr0RDJI2fotONiiX/z37yYOHPhYGWyzZ/Ted4oKvlGx9EwLK9ohNlswjlnDVGm4zifqOM4vyyWrr3BZpd8X/5N/esuLHggmDghTipKl1LHtTrpuLpWOdctPGOQfLYajadjsKuKvfIg6uvrERISgrq6OgQHn5xecAwxKi8vR3R0NLxccttOBaqqmvH4U5sl35MFeBjCwkIGs2Yk4cc3T1HaAoYG6jPPb5W8GlYWJWYfkxyQ7vjpNKU9wmj4vfL6Tily48ibYVEPHop/dvsMKfLRXxxrznHjXPLxAXyx/LBcOHBv5+uSE4Nxx0+mIi1F3e0q1dDhqibsKalHK+eb+HghxZWmJ4UhNkht37eS5Rtw4F/PSVGlLonhGxOJUb/+CeLPm69Utv0HyvDMc9skF4ranGPJNcA2Uuef23+G6vHouLyCWjz1wjbk5td1h12y6Mi5iwbjyktGK20BU1HRJDqOvZi7dZy/N+bMSsGPbpyktA+tpa4Be/74CCrW74C1pdMLZ/TzQdScKRj3t7tgDlHjiXYYfmxjsn5jHlqo4wyQAjIjhkfiZ3fM6NcWMMeaczRgWJF42VddxbAMkAswRrtQNno1VcF1ycr0Sz7YL8YC1yWXJtus3HbrVGUeVQcsRPjq6+lSZ8EBC2bdfMMkZQa+g737yvDsS9tRVs4og86wVXoEr7p8DM5eNFipjjuSUy3VrvPyazsvWe1AYKCPtIC58vIxSlvAsK/2E09vkVoFjgsIRqTNnZOCm6+fNOBGdH+ctQdS7qz7foogH3XGfUNbG4Y88OwpN37fFW2oakO1T2jYsDpm9pEaUbYMweFG7wnNyTltWTqfFSY5g4cNjfSoxun5BXXSl443wKmpYRgzKrrfD+XHO+d4o79rb6mE6jH0cvyYGGW3vq60tFtRVN8KS4dN2tTEB/tK9V9PgB6tsjVb0VZRDZ+IUEQvmK7UYHCGn+XOXcUSBUHPyKSJcVK12xPmG9fAnv1lKCppgK+PCePGxEjlaU+Ahg0LK+Xk1kho/tjRMcr7WTrruNo9GajZ1dkqJ2ziSISOHe45Oi6/VorJsB8oQwhHjYzyGB1XWtaA3btL5SKTHq9x42LFmPYEGPmzM71EKgAzNHTShHilF7+uVZ137ChGbV2reM8nT4yXqsmeAL326btLpAoxZZo8Ic5t4SxVOm7X7lIpWOjnaxJvZrQH6TgWEcvNq5VWXGNHxSApqXehtoHgVDdUs3+j3lAd/G9tqJ6WaENV831AzznNQKLnm2ag0XNOM5Do+XZy0Ybqd6PhNDVUPeMaT6PRaDQajUaj0Wg8EIOBLfTUXVgYFL63SrShqumTpmYLtm4rQnZOtYTFjR4ZjYnj4zwiTJR5Utt3FneF/tol9Hfq5ITuhuAqYb7bnj2lklfDao6pqaGS4xOksHKtczg3+xzuTC9GS2uHVMpk3nF/h4keFxLc0QjYqwE7c998AUNk598eEs69ZWuBFGphP1p+pipz3pwpL2/Exs35KOsK/eVaGOohofCtldUo+XI9GrMLJM8yeu4UhE8dCy+j0SNCHbduLcSRnBrJJWchuQnjYz1CxzGcm/0rMw5Xyr9ZlZuF5DwhTNQR6sj0Bua+sULy9GlJHhEmKuHcGRXYtadUqsWyUNzMaYlue0grycU/XIVtO4ok9DcmOgCzZiR7TJgo8yw3bymQ0N/wMD/5TFnA0BNgOPemzQWSuhIUaMbUKYlSONETdBzDuTduLpDQX/YtZejvqJH0who8QsfxM83NrZViXmPHxHhUKLxGcyx06K8O/XVLbn4tHn1iEwqKWASlc4qwZQ3zkO6+c2a/54YcqwjKQ49uQObhqu4CLRxjHpbuuWu2spYXjk3hkcc3iaHaIUWBOis+Uqa7fj5LqvD1F8eaczTuWYRq46YCKfrUKRukouJtt07DpInxUIY0rswF7KU8ajp9wxvwSgO8ohWKZsenn2Xgvff3dRZo6ez6KkWBLrtkJC69eJTSw9La9bl4+dWdUgnbIRvzpBYtGowbfzhRDLD+4Hh0XMXGndjzh0fQXFze1TDXAKOvGXFnz5GiQEZfdZc3R45U45HHN6KwqL6rQqwBJhNbdcTirp/NVFqUjYfyhx/diKzs6h46bsiQcNz7i9n9WpTtWLAQEPeGvfuo46Qbrsz/xIQQ3P2LmRiU1n9V148155iT+uzL27F5W1GnjmM/cBYFivDH7T+eKvn4qqBB/+ob6VixIlsuCR1rNTTUFzffOAnz5qQqk43z/8OPD+CDjw5KRefO7qSQy9WrrhiDC85Xmxu9as0RvPpaeld7tS4d58eKv0Nx/XUT+s0gPB4dx0vfp5/bKgUoHadps9mIWTOTcfut0+RrVRzOqsKjj29EcQkL7XWd40xGMVTv/vnMAb88P9VDf4/89nblob+D/vX0KTd+35VTz4LS9Dvc4J94ZjPyC2oRFxOElKRQeURE+GPPvjK88ka6MtmobGlssRoxC1Gw8AkfMTEBOJxdhaee2eK2p9lA8cabu7BjZxHCwn27ZYuLC0JRcQMef3Kz2960A8XHnx7EmrW54vVgqwY+EhKCxUP41LNbUFHZpEw22MsBe0mXSvIDDPTw8jLECtiOAPbePSUHivRdJfjfO3vkwiYpKVg+U/7NA9O7S/Zh+45iZbKxCNALL+1Ac7MFiYnfyGb2MeKLZZlYsSpbmWyt5VXY/YdH0FJaicBBSQgamoagYanwDg1G4acrkfXcO+pka+3AY09tRkFhvazPzrUaIpEFPHjSoFAF59nTz24VT6qzjuPXrFDMtaryfpljk76rWMaqU4906riCwjrRce560w4UHy09iHUb88XjRk9gcmKIXBJWV7fg6Re3ia5TxYqV2bImuTYdeoRrlmuXa5hrWRX08FKXUac5ZOP4cS6+9fYe8Z6rNLZeemWnXEI4619GPSz9PEOMWJWX5lyPbLnFvdSxr3KPXbM2R/ZcVbDrANcjjdRvdFyonE14Rnnjrd3KZDtV4V0No2+VPQw4LdGGqqYXrKyXn1+H2JigHt4YVuxkb8Gdu0okxEXVpsUqnSyb73xTyU0rOiqg+/sqoKG3aUuhNHf38/0mBJljGBcXKGO2dXuREtloIK9cdQQ+vqYe4Xm8ieYmVlXVIu0JlCAl/XkQsgMG72+0sfzN28sOwEaPnBpWrj4i3mjOOYdXgX/z4oah3SqNQR6G2PSeHjaHV4Gy0Rto8DJg+YpsZRc3xV+sRUtxOQJSE2BwCvP1Dg6EKcAfhR99jfaGRiWy8aBWUFAn67KHjvM1iYdr2/Yi8WqqgOkMNEjZ5sVZx/FrPsfv8aJOBRwTjg3nl3MIMscwNi5QwuN3pBcrS1VZvT5P0j8Y7eCAlYip4yqrmrFpa4ES2bgGv1qRLWtS1maXHuGa5drlGuZaVsWKlUdgabeKTnPWcdR51H2qdVxjY5tc1DjLxnVKvvo6S9nFzYZNeeJJ5fxy9upyj+Veu2LVke4epiouH4qK62RdOus4nk14RmEYtdLLaY3mONGGqqYXjnBfdyErvKnmgYDhckpkK6yTjZO9wFzhAYW5U3yNCgoL6+V2nD3UXKEhzc20UJFszOth2FRQUO8cMm6wvK1j+Xo1dAB2ejrc5N7JwYRqSo1BQ3j54W6+ET7P76s6KDE01GxmgQeD27VaVtaI+obOPpwDTX1m58Hb2Uh14B0WjLbqOjQXqPHUFHSF+7rLRQ0K8kFTc7usZxVQtzKqhaGNrvA5fk+Z/i2oF/3PMXLFrFjHlXbNdXd5sg4jIl/RuNXXt0keOdekK1y7XMNcyyrgZ5aVXYUAf3OfOi4rqwqqoH5lT2N3Oo6fdUlJo+T7qiAnt1bmlrvQY37WDJOvKFdjDPIcxG3JXS4qzyg8qxQpWg8azYmgDVVNL3zMRskCcXf4Zp6N0ctLXqNGts7DmyNv1hnHc6pk42GDG5Y7DxbHktKpylfh+3oZDbB2Nf12xW7r9JirU0Pc6Psy9vi8ujwfjoujWborHVabeJZU5W/RcOmw2vuUjUXQaECowOTn11Ugqzf29g4YTEZlOaqi4+xH03EGdWuVn5fB/dA5nlOm43yMov/drQfHWJoV7g383PrUcXauZXX6l7JxTbqDa7ivy7CBgIZgn7J12MQ7qAo/f++j6l/mlasqDETd35VC3rf+VTTnjnaO4xmFZxWV+bOnJLyQUP04DdGGqqYX48fGyqbZWbigJ1XVLRJ+NmJ4pBLZWLGOIT/MOXKFeSK86WehABWw8nBcbGcYrSsNjRb4+ZgwYXycEtliYwIxdHAEampae21cDE3ihjpRVTElAw/m4V2eVXtvC1pCgvuvCNWxYFEMevFdLyD475bmdvm+KqZNTZTP01EcywGfq69rk+qTzmGQA0nU3MnwMnujo7G5l2yt5ZUIGTUEAWmJSmQbP446zuTW28xQPlZjHT5MjY4bNzYGIUHudRyfY+jouLFqdNyIYZESgllV3fMzdXgNeXHCsVVBQnwQ0lLDJA+1l45raYe3yQvjx6iRjWtw4sQ4WZOusnHt8rlpU9SsBV6yzZ6ZJLrMnY6j7ps9MwWqmD41US6hGS3lDJ9rqG/DlEnqKmFPmhAHo8nQK7yXn2dtTSuGDI5AjKKKzlyHPHPw7OEKzyg8q/RncUeN5mShDVVNL5KTQnHG3DSpYFte2SQbFRUxk/LpOLr0opHwdcrBHEh4SLvogpFyw1pa2iCFO/hgiCP/Pv/cYWJIq4BhhJddOlpKwDOkhocjjh3zQOpqWzFndoqU01d1GLnsklFiyDOkkZUdLZYOOfiWlzdJxV9uusrwopFM71oLYKfBagPs3GBbAUNwZ5saRSxaOBgpKaEybnX1rXKwrK9vlfBLFhw5a+FgZbKxtcXI4VEoKW1ATW2LyMbKxAzRjIoKwIWLRyiTLWr2JETPn4aW4jK0llXC2tqG9oYmNGXlwRwSjCG3XqXME02DZt7cNDGuWBBFdFxLu+SRM6eR65heJhWwSNEFi4ejzdIh4awOHVda2ihrlhVYw8LUVF3nmFx+6WjxqnKsunVcRZMY/RxTjq0yHXfhSAm5LCxmGka7rAca1RVVzaLfxims+su1yDXJtck1Stm4ZktKGmQNz5yRpEy2sxYNEV1WVNQga4KyUddR51H3LTxjkDLZuG+y1VZxcYNUNqdsPJswxJxG4OLzhyuTjfvmxPHxqChvlr2U65N7K8ctONhH1ooqHUcjlGPHswfPIFynzdRxRfVyRrn8Mp5VtEf1hDAY5PNU9cBpWk1Jt6fR7WncwtYqn3yWgeWrslFb0yKLhDdwFy4ejvlzUpWWqueU/Wp5FpZ+kSEHJM7gqEh/nHvOMJx3zlA5aKqERYk++uRgZx6czY6QEB8sOmOwtDHpz1Cb45lzu/eU4N339yEnpwZWqx0BAd6YPSsFV185VpnnrRtW9rXld1X45c1+l6fVKxkwqO1BW1xcj7fe2YPd7M3Y1gGz2YTxY2NwzdXjpC2HSnjY/d/be7BlW2Gn58jbiOFDI3D1D8aJl1/lfGtvbELmk2+i+LPVaK9rlHDf4OFpGHrHtYieMwUqoYeGLTlY8IQHYBa6SYgLwsUXjcTc2SlKdRy9Rcu+OozPl2WKjiMsFnfeucNw7tlDlfZnpP5dtyEPH39yEEVse2GzS5TLmQsH45KLRvbr4fd45lz6nhIs+fgg8gpqRcexr/CcGUm48tLR0uNSJZmZlXj73T3IOFwl8491FegxpB5hoUKVFBbViR7ZvbdMDC5eSrCn8DU/GK+05ZujTymrD7MYYWtrO7zNRowYHoVrrhorXkuV842XDu8s2Sv7flNTZ3QSWzRdeflojB+n8PK3y1vfqeOyUVfXJjouKSEYl1w8CrMVRAKd6u1pcv5wJ4IVtlSrb21D2v89ecqN33dFG6raUD0qPPjSc8SDUUpyiPTg8hSohNlChzOYt8GqPCDuoMeXsnW026Rs/UAYgcc757jkWZ2TXhqGBKvsF9kLUUcMeWSLCx/lBqorLIhSU9sqlw+siu1J8DBXUdkslw8J8cH9bmidiI6z1NShKb9EeqgGDU2FwYN0Ij0g9A6yTzTbN/RX39lvAy9FuFaJR+q4/FrJxeNlzUDkWB7vnKOhn19YJ3sEdVywm+JPqqD+5XyjUcMLVnrQPQlWdqZRExbqi2hFYat9wcrNDM1nESUaz56k41g4iREQDEPmWlV50eVWxxXVw+TtJRFzqnScNlS/oxytp6eh6jm7nsYj4Y2vp+Yx0DvZn7ep3wVuBP3Z+P67wA00JTkUHols7p51cHOGBzdPO7w54IHX0w69DsxhIfLwRHiJ1J+e5+8CDVNP1b+i4wZ5po7jxWqqh+o46l/VURhHgxdwnnYJ5yAywl8enggvfD3q0tdVxynKuf8+4ehnqvL9T0dO019bo9FoNBqNRqPRaDSeivaoavqkesc+5L+3DFXb9kpuWeyimUi+4lwEDlJX9MFBTm4Nvl6ZjZ27SiRclBXumCPlCR5Wm92CDlsdbOz9abfDy+AHo1cIjAb1N8GsjMxxW78hTwor0OvLQhnTpiQoD1VqbuvA9qxK7DxShcbWDkQF+2LykAhMTIuQ1g4qsbZZUPjx1yj8eAVaisrgFxeNhIsXIemSM5W1WOmWzWrD+o35WL3mSGcRjxAfzJ2dikULByHITU/fgQ5z3L6zGCtXZSM7p0ZyBFkAirJFeID3t2rbHuQv+RLV2/fBy9uEGOq4K89FYKqaCqzOFNc0Y/uRahwu6ex1ODQuGFMGRSBeUSElZ0qrmrBmZzF2ZFTI/BueEop5ExMwIkVNISVnGBpKHbdxU76krtDryxoBUybHK9dxLAK0YuURrNuQKxWAExODsWD+IMyZlay8tgLDzFevycHqtTmorGyW6s7z56Vhwbw05W1MpFK41YpmqxVWmw1eBgP8jEb4mUzytWrZtm4rxMrVOTiSUy0h8HNmpch5RFXRM2eqtuxGwQfLULNjv6RexCyahcTLz0VAsqIq/xrNCaJzVHWOqluKlq7C3r8+ifb6RpiCAvgLSZsJ/4QYTHr4dwgbr66a6J69pXj0yU3ShoAHXx4+WOExJNgHd9w2HVMnJyiTzWZvhcVaAjtYsdbxuXOJGeDtFQ2TV/+FfB1rzjG/8l8PrpNCSj4+RslX4bixBx0rAl91xRhlBzkaqa+tykJWaQNMXuyL54W2dptEAk8dEonLZqQoKyBDI3XXbx5AyfINMBi8YPT3Q0dzi6yJmIUzMOnB3ygzVpmL9+LLO7B8RZZ8zfYgbRYr2tttGD0yGvfdO6ffwtGOR8e9/+F+LPlwv+QK8gDHvEYeiFNTwvDrX86V/EFVFHy4HPv/8YxUIhYdZ7Who6kZ/klxmPzI7xA6Zpgy2TJL6vH+1ny5sPH17hzb1nYbgnxNuHx6MobGqstPyimux5NL9qK8phm+ZhP40be0WuHva8J15w7DnPH9dwA+1pxjfuW/H1yH3NzaTh1n6tRxNLSuvHyMVGFVBXMYH3hoPfYfLJeqq+xz2dLSIXqNVXd/fPNkZTqO9QoeeXwjtm0vks+TVf35HI+H06cl4a6fzVRmrEqrrfZ2NHd0dIdOd/fsNRoRZjb3m7F6rPlGOd5dsg8ffHQA7R1dOq6dOs6KtLQw3H/fPKn0rIqCJV/g0AMvoKOpCabAANi7dFxASgImPvx7BI8Y2Ir1p3qOat6ff6Y8RzXlL0+ccuP3XTn1LChNv9NWVYMDD7wAa6sFgUNS4BcbBb/4GPm6pbgC+//5LOx9dbnuZ3jgffm1nbLpJyeGSCsa5qwkJQajscmCV15Pl1t0FXDTardVdBmpZhgMNKJZYKSzyEiHrRJ2uxrZyHsf7MORI9WIjw9CTEygeLSSElmgxYhPlh7C4awqZbKtP1guRmpEoA8ig30R4m9GdIgvAnxM4mU9UFirTDZ6Uku+2gDf6Ejp++kbE4FA/h0bhbKVm8XgUUX6rmIxUllchEW7mKPK6tw0APcfKJPK2CqjHj785KBcOnCecb6xnUR8XLB8j5UyVdFaXoWD/30JtvaOb3RcQqeOay4sxYH/vNCr3+VA0d5hw+e7itDSZkV0sI+sBVkPwT5oarPis/QieY0KOCZvLz+MipoWxFP3hvoiPNgX8VH+aLfa8O7XWahx05t2oHj73b1yEdet47g3JIWIYcjqp/R4qWLp54ew70CZrE2uUa5VrlmuXXqAuZZVsWrNETFSIyL9EB9PPcK/gxAW7ofNWwuwZl2OMtnabDYxUmmMmry8YHT622K1oqnLgFUB98xPlh6UPbRbx8UEythxr33v/X3KZGNbsMxHX4XNakXA4BTZr0THDU5GU34RDv33RWU6TqM5EbShqulF2aotaCuvhl9iTA8PGyt1+sZFof7QEdTsPqREtn37y6QKMVs19JDNYJDn2E81neHACrCjTTyqjKh3lY3P2WGF1d6orIXJ1m1FEhbqWvGPrSXoddi8pUCJbKwauiO7EmaTlxg1zvj7mGC12ZGu8IBZ9MlKKetvCuwZqmoK8IPB6IXCj75WJtv6jXnipWR/XGfo/fDz98a6dbniwVTBpi0FaG6y9Ap/4/yjl3dnerFU8VRB6crNsFTVwi8+ureOi41C3d5M1O3LVCJbVlkDqhstCA3w7imbwYCwAG/5Hl+jypt6pLgO4cE+Pbx/lC0yxBd1TRakZ1QokY1ziXOK+sxVx3EOsvLplq2FSmTjGly7Lk88bq6eSa5dtqrhWlYFQ369jAb4ufRH74xYAtaszVUmW0uXIerqNZXekl3fV2Vwcc9sbu6QOecM5x/3WrYM496rgrKVm6Taei8dZzTCNyYKtbsPoiHjiBLZNJoTQRuqml60VVRL9VUvY+9QH2OAH6ytbWirrFEiG5U+QxzZx9IVR/++2noaiwMPDdHOMN/ey6pzo7DDLm1XBh56oBkS6noQccjGqKaqajUbalu7FS0WK3z66L/ItiEqvTTNRWUS7usOPt9SUq7soFRe3tRn30o/XxOamtvlEkJVuxwa+O7CySmbpc2KWkWHONFxXYc2V0yKdVxDa7vMJ877XrIZvWCz2+U1KqAhamm3SdiqKzRc+UnXNqpZq5xLDLlkexBXxKgxGJRdjNBIZk0Ad/qXcA1zLauAc62isknWpDt8fUwo7+rlqwKr3S7zyh0SBty166qA84kGfl86jnsu914VtFV06i93rcC4b9kU6rhTFdnPFD9OR7ShqumFT2SYFAFiyIgr1uYWSciX1yggNMRPDkQMAXaFt9Lc0UKD1ZSIN4CHNyqS3mF5nYYMD3Jq6pfRg8XDJZulu5ONkdwR4WoKP9BA9TMbYeHn14fHNUxhH0SGvVub3R82+DwLK6nK7WX+k8x7N7S0dogHZyD6W7ojPMxPjCp3Rjzz3+hZCg1VNOe69JfdjY7raKKO81Gm44JozBgMMu97yWbtLCQjr1FAcIAZZm/mj/ceN14g8pMOVVTAi3OJIZicW65wDvKhqrUJ24Ow1VtLHxcMXMMsXqQC8YZHBIi+cAeNf0YrqYIhvn0ZovxMqXlVHd8ZWm6z2tzqOI4n91xVLWsc5zh3aVo8x3kp1HEazYmgDVVNL6LPmA6fqHCpbuqsgKnwWosrJAFfVTGlsWNikBAfjPKKxp6y2e1y6xsdGYCJE+KUyGaAD7wM3JTchSJ1wAAveBnUFI8JC/XD1CkJ0sidoaLO1Na2wt/fhBnT1VRzppdo0qAItHXYJM/NtcgSLyYmpKrr15h48ULY7Z3FxFwNGho6CRctVCbb7JkpEmbW4OJxptHf0tyOeXNSpBenCliEJcDfjJqankY+q8TW1rVi0sR4ZYYDK5ibI0LRUlzeW8eVViBkzFCEKCqmNDgmCBEBZtQ0tfeUzW5HbVM7wgPMGKKoz+Wg+GCkxQWjuq5VDFNn2SrrWhESYMbE4VFKZONcmjghXvQZ55gznIP+AWZMn6qmmjPXINci16TrhRzXLtcw17IqFsxPg81q72VIs94DP9t5c1OVycbKvoSXXr0uH7q+r+qicOa0JLmA4Jxzhnssqzpzz+Xeq4LohTNhDg9Ba0l5j+dZUKm1rAKhY4cjaPggJbKdstCjqfpxGqINVU0vfCPDMfJXP4LRx4zGrDy0llbKgY5f+8VHYfRvb3UbTjIQ0Atz0/UTERLsi4LCOgm94SO/sE5urW+6YaJsHCrgZuntFQUDzDQVpHCS3U6jtdOIMHlFwkuKK6nhqsvHYFBaGIqLG1BW3ojq6hYUFtbJjfmFi0dg6BB1rX3mjIzB4NggVDW0obKhFXXNFpTXtaKprQNThkRiVFKoMtkSL1qE2DNnobWsEk25hVKIR/4urUDMGdORdNnZymSbNDEOixYORkODBUVF9fKZlpY2oKy0EaNGRuOC89VV5+Zcu/jCEeIt4lqlbKw8zRzz1JRQqTKtCt/oCIy892ZpSdNEHVf2jY5jZfNRv/6JssMvc7XPn5gAfx8jyuvbZC3Ieqhvg5+PUb7nmss9UHBMrj5rKCJD/VBc2YSqulbU1LehuKJZLpyuXDREafTD1VeNRWpqqKwFzjXOOc49zsFLLhwprWpUQR07ckS0rE2uUcpGObl2uYZ5caOKhQsGYcrkBFRVtsj+QNn4d1VVixj38xUaqj5eXtKKhoZqh80mocCOv81eXgjoMmRVMHRoBC66YITsodxLOW6sk1FUXC9zjXuuKqjHhv3iRjmrNWbldum4MjRm58E/KR4jfnWL8nZNGs3xoNvT6PY0fVK1fW93H1UvkxExCzt7DAYNTlYtmlQNXb6CfVSLYbd19lFliX+VxlavPqpSOMnRRzUYRkP/hk8dz5xj3uCKVUekj2pTswVpqWHSY3D6tETlmxa9p9vYRzW7SnLwokP8MMVT+qi2tkkPVRZOYs4qC1QkXrQQiZeeBZOfmtAu59v7DZvysWr1ETmUs03TvLlp/d5H9XjmG7cXVhNdueYIsrOr5RKJPQbZu1eVN9WZyi27kb9kGaq37YOXj3dnr2j2UU1T3yu6qJp9VKtwuLSzcNLQ2CDpo5rgAf1nSyqbsHpnEXZmVEg4Mvunso/qyNT+DSU8njnHi8sV7BW9MU88gkMGRWDhwkHStsxT+qiuXZeDuvo2qRR7xgLP6KPKkGlW/12zNgcV7KMaFSCeVvZRVRWV4axHWqxWedBIZTiwr9EI/37uo3q8Oo5FulasypZzCaNI5sxOwaIzBkllZ9VUbkpH4QdfomYndZwZsWfNQeJl50iLmoHmVG9Pk/+3XyhvT5P8x8dOufH7rmhDVRuqmu8Jes5pBhI93zQDjZ5zmoFEz7eTy6luqBb8n3pDNekPp5+hqleeRqPRaDQajUaj0Wg8CrXxHBqPh6FTzLdgQZvkpNBe/elUwryj/II6Cb1hCJXq8CTXcEyGYfJvFn9SVXnVHRyvhqw8WFtaEZAUB3NYiGqRThkqKpqkRVJoiC+io9UUxuqLmpoWaTMRGGBGXFyQ8jBH14IxJaUN0uoiKSnEo2SjjmPerNHoeTqO1c2pRwh1nGsPTpVQt+UX1MJqtSMxIVhZbYC+dFxBQR1a2zoQFxvUq8+watlKShrQ2GRBVGRArz7DqmFuL4udsQgQq4p7EtU1Laiqbpbw2rjYQI/SI/X1rSgta4SvrzeSEoM9Sja2KOM5jrqNesSTdJxGcyw852Sv8Sh4CPn0s0P4cnmWHIDZvyk+NhAXXzhSKgCqVMLc6JmfuvTzDNlUCfPdzj1nGM4/d1iPRvQqYL7gRx8fkMMvK2PSqGG+4KUXj+qz5+VAUbEpHYef/h/q9h+GvcMK7+BAxF+wAMPuuBbeQZ5leHkSNLL+984epO8qEeOBBgPzoq/5wTi5iFAJDee3392LLVsL0NLSAZO3F0YMi8IPrhyDYUMjlR+QlnywD2vW5aKx0SLGYFpaGK64dIyy6tzOF10ff3oQX6/IRk1tq+gNfpaXXDQSs2clK9Vx1BtfLj+Mz5dlyuUIodGw+NzhOOfsIcr1L3PcP/70kBx+KWtYqC/OXDRY9gfVOo5rdMmH+5CTUyNGdGCgWYoBXXn5GOXGdObhSrzz3j4cyqxAR7sNfn4mqY7NIlCqqsM64GdJHbd7T2m3juMapY6jsa/aQH3n/X3Ysr0IbW0dYmiNGh6Fqy4fjcFp6gpkOXrkvrtkL9ax7kNTu+i4wYPCZb6NGxurXMd9+PEByYvm5QN1HC+VLrl4FGbPVF9r5FRDdS9Tw2la9Vcbqhq3vPpGOj7/IlM2q7AwXzmM8Gb/6ee3or3dJocSVXyy9BDe/N9uthpEaCgL2XQ2cn/19Z2yafzgyrHKZFu3PhdPP7dVNnrelHPTqm9oE0OisrIZt/90mrJDJgvHpP/yX7DU1MEnOhJeZm+01zXgyMvvoym3CJMf/T2MZlYs1jhTVdWMfz+4Frl5tXLpEB7uJ/1oeVjPz6/FH393hjLPAw3BBx9ej/0HyhEc7IPwcF9pMr8zvVi8Xff/eh4GKTrI8bLr8Sc3YfPWQgQEeIse4XMHD1bg4YKNuPeuWZgwPk6ZsfXSqzvFGKSX16HjcvNq8NQzW0ROFpJRBQ+X1Bk8WHbqOHq6mvDiKzukCNrll45WJtuatbl49oVtcgjmWqCMLAz01jt7xKC45UdTlOm4XbtL8PBjG9HY2Cay0aDhBcmHHx8Ub9e9d81W5k06klONBx5aL/tAWLgvgoPMaGpux5dfHUZhUR1+/5sFyiJveBny7wfWSoQS10JgoJ9EGqxdnyt65I/3nyE9Q1XAz+/BRzfiUGYlQkJ8pD9zm6UD26jjCutw/y/nIjlJTVQQ18Ajj2/C9h2FciFC/cvzEfVxQeFG/PLu2dJST5WOe/7F7fh6ZTZ8/Tp1HC9uWPDpqWc2SxFKFn3SaDwd7f/X9IKHb1YQpeJlE3KG1PImOj4+WJTb+x/t77NxeX9TV9cqhioPG7GxQRJm4+trQkxMoMgpHojKTg+Eik1ryQf75e+EhM5wX8rE8K6QUF+pQpl9pFrZppX17Dtoq65DwOAU8aQafX3gGxMJv/hYVKzbjor1O5TI5ulwo8/Lq5WbaDZv5+VNcLAvEhOD5WC3fEWWMtk2bsrHwUMV4vGgR8ZsNkml36SkYDl8Lv0sQ5lsu/eWYvvOYkRG+sshl2uBLaQ4bjQk3v9wv5t+wwMDD2usvBoc5COXDA4dx3XLKraUjZ4bFbAyNz83zrPY2EDRb3zwaz7HSBJGuaiAY0IPOfuUcqw4Zhw7VojlWNKI5YWOCjiX+LlxbnGOca5RNs49zsFtO4pkTqqCEUpck1ybXKNcq1yzDNPn5Q3Xsiq+WpEluozjRt3GeUZdR52Xl1eHlauPKJNt/eZ8ZGZVIj4uUC4KKZvouIRgabP2+VeZymTbuasE6buKERUdIBV++Zk6dBzPKip13OGsKrlM5dmDZxCuBZ5JuG55kf7eB/vkrKI5cY+qysfpiDZUNb3gZk5PDW8vXeGmz8320KFKJbLt3VeG2toWuS13hR5M5sLt2VOqbGNgiKi7m+egQDNaWjskLE0FzQUlqN2bAZ/oiF7eDlOAH2ztVpSv3aZENk+HB0hu8q7tI/hvHtQZ6q2KrdsL5fN0zV3kc8EhPtK+iVEGKti1q1g8k65eIsrG9Xskp0bCDVXA8Mbm5g7xQrsSEeEn3reMTHU6rq6htQ8d5ysH4D2KDC6OSVl5k1sdx7GkF45eTRVwLtFryXFz1XGcgwy1VSUb1yB1P9ekq2xcuzyAci2rYsPGfPj5e7vVcT4+RrlkVcXW7UXitXcNKedzzD3etrNYcpFVkJ5eLF5KP9/eOo5e86zsKjGmVem4lrYOOXu4wvXLPGmeWTQaT0cbqppeODwJ7sK36Mm02uywWNRsDAz54QWluzxUx3MMfVQBm34zfNBdaBnHktLxJlNVH1DmpHr11RzdywBrc+tAi3VKwENQX+GCfJ79B1XdmvNCyWR0f8tqMnrJIcqi6Nac+bJ9RYDyAEyvnKVN0Vq1WMFuE+51nLFLx6mTDXbKBo/UcVabrU8dRywqZbO617+CAWhrVbNvcUyOJhvXMNeyCqi7JO+zjz6ulFnVuBFefhxN/3JceQmhAl4+99W1plv/KtRxXJHudBz1L88qqtaqRnMiaENV04vEhBA5ELlTYg0NFgR0hY+oICkhBGYfk9tNneHIvHVluJIK+L68uecYudLeYZUNQ5Vs/gkxMIeHoL2utwfLbrOxeguChqnLyfNkhgyO6PMQyef5fVU5eUMGh8s6dWcoNzRaEBMdKCGZKkhJCZVLJR6IXGF4Jj1wDNlXAcMGabm4C31jVAZ1HMP3VEAdQS8bDX1X+By/p0qPMGyVFVc5Rq5wLFXquNiYzrnuTjaZg3bKryaXkXOd4dHu9gauXYvFJmtZBfzMWPyHuc996bjBgyOgCo5La1vfOo6fO3PgVZCaEipzy52Oo2wMn1ZVv4A6jp8tzx7u8n79/c3KznGnLNznVT9OQ7ShqunFpInxSE4ORWlpo4TuOeCtKyuMTpoQL8asCoYOjcCo4ZFS4MbZkOYhqaK8WTa10aOilcjGDYkVHFmm3jmHl56j0pJGxMcHYdrURCWymQL8kXTZ2ehoakF7/TehSHarDU25hfCLjUL8+fOUyObpLDxjkIT+smCX47DEvzkHvb29pKKzKliBOyjIF2VljT1kq6tvlXzysxYN7hXON1DMmpGMqMjOEDPngxzDIJtbOrBgXprkc6lg8qQEaSHhquPoHWdo7dQpCYiNUVPpdMTwKKnWzFx7Z284v+Zzw4dFYuSIKCWycUymTk5AbW2rjJUDjmFJSaMYghxbFXAusQAW55ZzuDvnHucg56KqSqdcg2efOUTWpKzNrrVK2bh2GcLKtayKRQsHw+xtlNYvznqEOo+6b9EZ6nTc/NmpEr5aXtHUU8fVdUYAnXXGIGUXhZxPjjBaZx1HQ5Be6IUL0pRVmp46JRHxcUFy9uAZxAHPJjyjzJyeKLmrGo2no6v+anrBG/uf3zEDjzy2EQVFdaKAGbhqNBkwfmwsbr5pkjLZuCHddus0/PfRDcjKroZNFLBBPMCDB4fjztumKzuYkxuumyCVL/fsKUFFR3OXzJANg2Oqsj3C4B9fgaa8IpR8uR6tpRWdsXAGwC8uCuP+8gv4x6upTujpTJoQJ+0jWCiroIAeaXv3wZgtCHhwVwUr+v745sl45bWd3bJROuZMnXv2UDmAqoIHuDtum46nnt0irZrEvSr6xYQ5s1Jw2SXqKteyONHP75wpOq6opLPFCo+61B2sRHzjDycqk4267I7bpuGhRzYg+0hN1wHYDi8vLwwfGqm0cji58fqJcmHJXFoegCmdo+0FdRzHVhWcUyWljdi6rVAukgSDQYxU7g0seKMKrkVWzl+x6gjya+tkvlEBs6rzTTdMUladm0ybkiC6jNWm2X+2E4MUVLzqKrWtpHj5fNN1E/Da/3ajoLAzIohzzt/PG+efPRQLFBr47KV9+0+n45lnt6JQZLOLmqNxP39eqrRrUgWju35+5ww89sQmFBc3ONQvTCYDJk1KwA+vm6BMNo3mRDDYVSVXeRD19fUICQlBXV0dgoNPTiiEzWZDeXk5oqOj5YBxKsJbQfZmzDpSLbkgY0ZFi7dVdZ88wtv87TuKpOIpGTY0QryVqvvkEYba7NpVir37StHeYUNaahhmTk/q96bzxzPnGOZbtW2vFE6idzVwUBLiz50L32h1oV2nUjXsTVsK5CKChbtmTEtEakoYPIHSsgZs2lwghTsCA33EeOaa6E+D5nh1HCvUsuAUi93wAMdDL3WJygslBw2Nbdi8pUAKO3lTx42JwcTxcR6h4+j52La9CBkZnUWdhg+PFE+va+EWFTCCJX13CfbtKxMdx16W06cnSjXW/uR45hyN530HyqV4EaOA2BuXni+uWdXwuJV5uEoqEDc1tomhM3NGkjLvvStsz8R2UlyzbAPDfYvRVZ4ACxVu2looUQWcZ1Mnx2PIoHCP0HG8FNm4OR/FxfXSiYDnJEZ2qe7pThgKz32Llc6p48aOicWECbHwNhm/F2ftgZS75D/3ItjPR50cLW2I+/VDp9z4fVe0oaoNVc33BD3nNAOJnm+agUbPOc1AoufbyUUbqt9RjpbT01DVob8ajUaj0Wg0Go1G0xe8q1B5X+GF0xJtqGr6hLlRhzIqkH2kWsL0GMqS4iFhQAwEyM6pQcbhzrC4oYMjMHRw/4YBnQjMyWPoL4uMpKaGYdSIKI8IdSTl5Y3YtadUwqeZOzt+XKxHhDoS5r4xZI9h55GR/lK4S2XOmzNsWVJn6UC7zQ5vLwOCzSaYPCC0y9F+qHzddjQXlsI7OBDR86fCN1JdzptrKPyevWUoKmLor1FCaxny6AlIKPz2fag/mA2DyYjI6eMRNCQFnqLjsnJqcPhIZ6/DYYMjMDg1zGN0XH5BHfbt78xTHZQWhpEjPCPUkTQXlaFi/Q5YW1olvSFy5gR4easPmSYMq92ZXozGps6q3AyFZ0i8J2Cpa5C0kLbKGvhGhYse8Q7yjLVqbWlB4/btsJSVwxQcjKCpU2AK84zUC4bCs29pcUmD7FfUcaqq/brC9bn/QDny8mslhWvsmBhlxTA1mm+DZ2hHAP/6179w//3346677sIjjzwiz7W2tuKXv/wl3n77bbS1teGcc87BU089hZiYb4q+5Ofn4/bbb8eqVasQGBiIG2+8Ef/85z9h6qtfpOa4qK5uxhNPbcaBQxXd1XWZ/8lcnx/dNFmp8cAy+s+9vAPb04u7K0/6+pgwbkwMbv/JVGXtOBwb1qtvpGPNulw0NbdLwQxWhmUVz5/dNl3pAZ0XD0s+2Ieln2dKexCed3mwZJ7lnbdPV55v+dXXWXhnyV6pKNpZ58kghvQtP56CsaPVFnqqbetAUWMrLNZvMiXMRgPiA3wQpjhnsHrHPuz58+NozCnorDJit8McEYqht1+D1GsvVGrYMK/3iac3Iye3VsLomGjCAi3nnzsMV10xVqlh01JSjl2/exg1O/fDammXcfMOCkD8BQsw+rc/hdFHTUViQiPmuVd3IH1vWXdfax8egMfE4NYbJyNQUbVkQnlefm0n1m/Ic9JxRqlETD2ispKozWrF4afeQu6bn3a14jLIBUTwiEEY/497ETw0VenFwxdfHsZ77++Tyr/Ub1JoLz4Yt90yFaNGqqlW76B42VocfOBFtBSXi/7lH37x0Rj921sRe+YspbI17tqN4ieeRFtRUWdRNjtgCg9DzPU/RPj55ymVjbmfTz69BXn5nYXPKB7rUSw+b5gUqFKpf3kp/fjTm5GZWdXdiotFllhxnUXRPOWCWtO/PPnkk3jggQdQWlqK8ePH4/HHH8e0adP6fD1tsKefflrsq8jISFxxxRViV/n6+kIFHuHi2bZtG5599lmMGzeux/P33HMPPv30U7z33ntYs2YNiouLcdlll3V/32q1YvHixbBYLNi4cSNeffVVvPLKK/jTn/6k4Lf4/sAbuMee3Iydu0pE4bKfIB9msxeWr8zGW+/sVirfS6+nY/3GfCkqwl5hfLDv4ZbtRXj2pe1u+60NFO99sF8OI/SeimyJIQgJ9pUKmY8+sclt38aBYvnXWXJIosHA/mmJiSFSmZXVk1lh1F3/wYGCxUVeeT1devbROGW/3JjoALmhfvzJzSgu6d3/daBoarciv6HTSPUxGuBr8pK/LTY7Chpb0ajwM6UHNf2+/6AxOx9+CbEIHJyMgLREdDS34tCDL6F0+QZlstEr/tBjG3A4qwoREX4y3zjvuDxZQfmLLzOVyWbr6ED6bx5E5cadMIeHihc1cEgKDN7eyH/7c2Q8/roy2ai/XnwjHZu2FcLfz4TE+CB5+Pua5LmX3khXquPefm8vvlqeJd4ZtvhhS5rgEB/s2lMiFUad2/0MNHn/W4qsZ9+WNjABg5LlM/WNiUTtnkyk//JfPVpzDTQs2vXam7vQ2tYZycI9NSo6AIWFdbI3sE2NKqp37sfePz+O1vJq+KcmIHBwCvyT49FaWok9f3oUtXszlMnWVliIggceRFthEbxjYuCTnAxzYgKsTU0oefZ51G/erEw2tnl5+FFW566WvdSh43iGeu/9/XL5qgqeNR55fJOcPUJCfGSdcs4ZTV74fFmmnFU0J4bBy6D8caK88847uPfee/HnP/8ZO3fuFEOVTj/mXrvjrbfewm9/+1t5/cGDB/Hiiy/Kz/jd734HVSg3VBsbG3Hdddfh+eefR5hTGAeThTlADz30EBYuXIjJkyfj5ZdfFoN0c5di+uqrr3DgwAG88cYbmDBhAs477zz87W9/k9sDGq+abwcrJrKabnR0gNy+8UaQj+BgX+lntnZd7jel/xWE1G7dXoTQkE5ZHLKxVUhEmB927S2VkGAVsK/bylVHpGw+5XPcpNITzabkmVlVUilT1ab1+ZeZoui4oTo8WQw5Y39XjuvGTfnqPA3LMsVTw1A4R4g0b3sT4oNQWdWENWtzoYqq1nZ02DqNVMdnyr99vAzgmbyqRZ2uKfp0pYQ5BgxK6vYAGoxG+CfEwNpmQe5bnyozangwz8+vE4+RI7SR8y483E8+42VfHe72Fg40lZt2oSb9APwSY2EK9P/mM40IhSkoAIUffo3WymolsuUX1mHnnhKEhfqK59Sh4/g1n9uxp0Reoyo0f/WaHAQEeCPEScdR53HtZmRWYs/eUmXh77lvLYXBZIJvTAQMXcVvjH6+cnnTcDgPJYoubrgGaRy0W6yIjgro1nHsXUrDpry8CWvXq9Nx+e9+IWG//inx8OqKRvPyNonR2lZVi/z3limTrWbFSrSXV4hx6mX+RseZY2Nha2tF1SfqdBwrrXPv5B7qrOO4xxq8gGVfHlZ2Oc2zxuGsSjl7OLohcL3ybOLn742Vq7LF0NZ8v3nooYdwyy234Oabb8aoUaPwzDPPwN/fHy+99JLb19PGmj17Nq699lqkpqbi7LPPxjXXXIOtW7fitDVU77zzTvGKnnnmmT2e37FjB9rb23s8P2LECCQnJ2PTpk3yb/49duzYHqHAvClgha79+/Vt0bclK6szTMRdqxceTurr26RljRLZjlSjuaUdQUG9Q994eGIosCrZcvNqJaSL3gVXuIlZrXbxXqqADckrypvEu+sKPSPkUGZnvu9Aw/BBtgcJDu49btxYeZjbd6AMqmiwdMDIUGSXEC7+m883tFuVHZQqt+yWw5vjUO6MOTwE9QePwFKjxqjhIYnD4phfzoSE+qCislla1qigdk8G7B1WmPx7tyxh2DTHrP5AthLZmJfa0trhNryXz7W0dMhrVMB1Sv3PfcAVpoPQm/r/2fsO8DirK+13ukYajXrvknvvvYHpnRAgIYE0Qno2dbPZzZ9/y7/Z3TRCQggkIYRO6B1s4957L7Jk9d7bzGj6/7xnPEaWRsYsSHcUf+8+s5Zmhvj4fveee+p7VOm4vso6uBpbJUM+GHS6gsEAOg+fUiJbd49begQj6Tg6NkaTXvp9VYC6q23XIelFjaTjGLhhYEcVHIcOQ2+JrOMMdjtcZeUI9DmUyBa+MyPqOHuMZMk511eVHedjFVCE/mdmWNliw7JlDWMPPT09573YGhkJTNjRlxroR5G9mr+H/ajBWLJkifw3Yce0oqICb7/9Nq677jqogtJGTvaeMhXN0t/BYC212WxGYuL5lw6dUn4W/s5AJzX8efiz4cCHOvDB8kETLInk6+NAqB8r+LH9740meB/IfRUMROivCMjFynfV/NuCkGRgMBiSMcJnqmTT6YKhtUNQXkM+14fWdqRku9Ce0+mD8vdDF1k2YiRluyD4LM/e88PLplN3loLsOwr1Vw79KAAEQ7Ip6UUyGvjXIxjh75bV0uvl85FYuw/ScaIndBd4puL8K9pz3E/DrJu0+RpGbt0+CKE1C0syVDr5XJVsZ/WEyDFIPu6FUN/7yN17F9pzfF58rvL8Ij1XvR46g16Rjgvrh2HuBt5divSvBNkMBtEXkc+DTvSMKv3LsxjgCg1zVrlwAUqpRMeFz+swZ1U+//jsyg8Dyhayh4bbb6FKjdGUbSzawwMRrm5R+fcTeXl5GAiW6f7rv/4rBqOtrU1aJCP5SadORQ7aMZPK/27ZsmWy930+H7761a8qLf1V5qjW1tYKcdK6detGvUGXTcH/9m//NuT91tZWIXD6uA4ky5f5oMfa/K28HCMKCsywxviGRON6ejyYMD4GKcmh+WKjjcw0oLiQ5XAe2OLOz/g6nD4U5ZuQnalTIps93oeJ46xwujxDMg4kpCrINaIgzzRisl1ozxn0AUybEofmZgeSk89/puynyQsaMWlCrJJ1I+bNThAG56SkUKl5GCSn8AeA2bPsymQLujxwuP3wiNHxvmxcZ28AsFsMaPWpKYWPWTkXgfZ2eDOSpRxuoGweYxCJ0yags98FnfvjL/H6IB1XUmJB3kkj7Hb/0IxDhwcFuXEwm/rVPNdpxdBPK0a/LQ5G66Cz2tEFU9ZkeLJT1ei4VKAozwSD3o3YWPMQIrnifJN8R4VsSYl+TBhvhdvtGZIdZBk39Vt+rhodF7BZYF4yQ7Kq7Es97zOvFzpjIQxzJytZN8o7Z7YdZ850IDn5/Gca0nF6zJqhTsfZblyOlk174M1OPV/HBQLw6f2wXbFEmWy+BQvg7OmBJyUZOv35Os4XDCJ2yhS0OxwAX6Os4yZOsKK83IiEBH8EVn8vigttMOhdaGkZff6HgnwT8vOMiIvzwWw+nzSpu9srsifYfaP6XHt7e0ft7/p7Rm1t7XlzVC2Wj49AdNOmTfjZz34mxLULFy5EeXm5+Gpsq/w//+f/4JJyVJla5gGZM2fOuffo+W/ZsgUPPvgg1qxZI2nrrq6u87Kqzc3NyMzMlJ/55+C6aX4e/mw4kF2YzcUDM6qMUKSlpX1sQ3TDGRb+b441R5Uyb9zcjJ2766REz24zS0yus7NfStJu/8QEFBXlKpEtPZ2XQyfeWVsOmy2IhLMMvz19HvT0unH5iiJMnVIEVZgzpxfP/O0ILC0uJCXFQK/TodfhRVenC7NnZ2He3PEjNqbmg/bckiVT8PCf9qK51SnkNga9Dk6XD21tTowfl4IliydFLPceDaxYDuzZvx1NzQ6kpcZKuS/3WmubQ4hHli+dgpTkUC/haCPe50dFt0vG0pDpl880QEfQH0SMXofiBCusRjXsifarV6Hrb2vRu/kArFnpMMRZEfB40d/YApM1BpO+vxKpg6Kpo7XfFtuT8N76Jhw91iY9W3GxRhnx097hYoIJN984GVlZw+vpkURaaiq6nnsXTRt2QZ+aDFOiXYxyd2sHAq5+jP/W3cgpKlCm40pKOvDe5krExwF2W1jHudHb58UVq4owaaK6ETpzZnfjueePwhITRFKiJaTj+rzo6nJh/rxczJkzfsTYnD9oz/lWL8PRf38QnspGxKSnCuOvr9cBd3MrkmZPwfjLlgwJTIwWVi4PYt/+HWhq7kNaWiyMBj1cbj/aWh1CSrV82RQkJg4tRR8NWK5ehZ5XNsFdTj2SAYPVIqN9+huaYU1PwcSrVyGeG1MBEleuQNWatXDvPwhTehoMsbEIeDzwtrTAGhuLvFUrYRsh2T7wTl1EHdeII8c6kJoSK+Rn1HFt7S757z5xy+QL2qIjieTkVLy3sRkHDzYgMcmK+DiT3Fu04xg4/8ynpiAvL3tUZVLFGvuxgXpN5Qgufejvpp9yMb4KGXsNBsM5vyiSHzUYdEbvvvtu3HvvvfI72ysdDgfuu+8+/Mu//IsSf0aZo7p69WocPXr0vPfY7Ms+1B/96EfiOJpMJqxfvx633XabfF5aWip0yYsXL5bf+ed//ud/isObflZRMUPLB8im4eHA6EOkCAQfwMf5EKioPu7/zdHC176yCJaY/diztx61daEoGLOEn7hlIm67dZrSf9Nn75wJvU6PTduqUNvQe6536+rV43D3p2Yqle3G6ydJLyqJi+ob+qRclIRUS5cU4EufnwuTaWSP3IX23IplhXC7/XjpleNobHRIJJ99ZXNm5+C+L81DXJy6sT7Tp2Xia/ctwNPPHhZDjrKRTGnihDR8+YvzkJaqbqxPnFmPogQd6vrc6PcHxMniOltNBuTaLIgzq+ugiE1Pwdxf/xOO/fvv0XWsDP2NzZJ1sOVlYuJ3Pof0pXOV7be4WAu+/51leOTRvThxogXt7a6zRCNWfOLmqVi1skhdGZVej1n/+T0c+88/SCbJUVYV6odOTcS4z38C4754m1I9cs+dM2E0GLB1Vw3qzva4Ucdds3oc7vqkWv17841TRMeRDKu+vk+CmGRdX7G8SHSccYSDNhfac3k3X4GAy40zf3oerup6CT4Y46zIXLEA0/7vN2COUxPsImbNzMZX71uAZ547LL2L1HHMdE2enI77vjQfycnqxvokTR6HOT//IY7/1yPoO1OLgM8rc2cTJhZj6j9/BQkKx/rEZGSg4Mf/hIbfPwRXeTn8zADqDYjJyUbGFz4P+6yZyvabzUYdtxyP/HmvEFC2tYV0HIOtn7h1KpYvK1Sm4zil4dtfX4xHH9uP/Yca0NnRL2XKSUlW3HrzVNxw/aRR1yNj0RYeyzCbzUJESz/qlltuORd84e/f/OY3I/43TqdzaEXe2WotVVwcuqBKnvtBWLVqlbD3hueocj4qm3g5cobO57e+9a1zrFThDCy/n52djZ///OfSlxqOBDB1fbFgRjUhIUFKPD7OjGrYgR7Lh5NkJxUVHdAb9JgyOQ1JiiK+kcBM4KmyEJnB+JJkYZ2MFpAB+PjJFiEXKSpIEmr4kcbF7jmHwyPMziSeIqtuSXGy0r6LweWDHE7O0SYcmD5xQqrSWZsDQVXJUTTMrBr1OthMBskmRQNokHceOikMwCa7DakLZ8IQY4mK/cZ1IwkP2TEZGJk2JV1YuqMFfRW16D5RDp3JiJR502BJUTtPeCBa2hwoO0sON744GekKZ5QORld3P06c1XHFRUnIzYkeHeft7UP7nqPwOV2IL8mHfXJJVOk4Eif1ObzCABxNOo4l0u17jwrTb0xaMpLnTTvHAhwNOs554gS8zS0wxMcjbuYM6D/GksePquM4oqa+oVd0HOd+M0AdLaip7UZVdSdMRtpx6RHJ0EYDI2Frj6bczQ/8EHaruoB+j8uNjH/4xYdaP46W+dznPicjQDk7lf7V888/Lz2q7FW95557kJOTIy2RBHtdyRT8xz/+8VzpL30xOrz831KB6NBAw+D+++8X5cCMKsmPyOjLuumBXv6bb74pi8jsalxcnDyQf//3f1cq998TcrLt8opGpKbGYllqPqIRvAiWLIpO2egkLJyvpnT7g8Ce6DmzR7cc6WJBQzdeYfb0QiAjZvKcqfKKxnVjMISvaIStOE9e0Qg6ptHknA5EYhTrODLYZq4OVV5Fo46bOycH0QhmUdOWvN+OFW06Lm7aNGAaolLHjStJkVc0Ij8vQV4aPiIYq1CZd9J/+P/kzjvvFP6dn/70p5LMY3Lv3XffPUewxCrVgUGYn/zkJ7Kf+Wd9fb2Uvd94441SvaoKUZVRVQUto6rh7wHantMwmtD2m4bRhrbnNIwmtP328WLMZ1R/FwUZ1W99uIzq3wOiMz2gISrQ0tKHrdurpfeCrJ2zZ2ZhyZJ8xJ8l91AJT3sH2jdsQc/+wzIixD5rBlKvWAVzmvqIJktrOQj8wMEGuL0+TByfhuXLCpCVGR8VZWd79tZh7756kbOwMElkKyyInnLHaATjeQ5fAD0en5T+mvQ62M1GxBn1UVFSeLqsDdt21kipfoLdgoXz8zB3dtaI9wuOdbAfetvOapSebpN+wVnTM7F4UX7EGaajjc5OF7bvqjk3X3Pa1AwsW5yvjHBn8OzjXYcbcPRUKzxePyYUJWPJnBykp6jrAR0L8Pr8OHCgAbv21KK7242cHDuWLynAhAnnsxSr0nGHjzRh564atLQ6kJ5uw5LF+ZgxLSMqdFw0o6qmC9t2VMufcbFmzJ+bg/lzsiPOMB1tNDb1Yuu2apSWtcJiClUsLV6UF1XtFxo0XAhaRlXLqEbE8RPNeODBXWhtdcBg1CEY4CxJoKgoCT/83jJkZqhzupxnKlH20/+Cq6pG5r/JcD+fHzG52Rj3r/8E2+QJymQjS+0v79+GsvJ2cNose498Pr8w1n7ja4swe1bWiP3dH7Tn2Pd5/+924NChRjFKDEY9fF4/4uNj8MXPzRFyGw1DwbVqdnrR6faF5vWdnd/H55tsMSAj1qzUkHvznVL87cVjMhbJZDTA6wvAaNBh0cI8fP3LC0bMWBrrOu7IsSY8+PButLc7JRAXOKvjWKL8/X9YgvQ0dT3vlVWduP/BHdLzRnZugmyi7Cn/7jeXoKhQXWCptcOJB/66HxW1XSGiGZ0OPn8AKYlWfPWuWZg2gk7XWN5zDBL+/uHdEsTkSDDuOa/Xj9g4Mz51+3Qh4lMFyvPXJw5i7XvlIlNItgAsFgOuuXo87vnM7Kjpox1NXMx+27S1Cn996hB6+9wwmvRCNMbrgEGvf/j6IqVBr4OHGvH7P+xCewd1HGfhBsH/I8v/D7+3XJiKRxNjPqP6+x+pz6h+43/G3Pp9VIwtTa9hVOByefHQI3vQ3u5Abm6oRzU3NwFZWfFCrPTnx/YrY/8K+v2o/OWD4qTGFOTByld+nvzcX9eIip8/IIQQqvD4kwclO5OZYZO1y86OFyIlEo88/Kc96O0d/XlqYZDtl9F89vZSJo594Z9ujw+PPXFAsnEahqLH60eH2yfGh8Wgh9mglz9pt/H9Ho9fmWynSlvx3Ish9vS8nATJ2ufnJiAxwYrtO2vw7rpyZbJFM/ocHjz8573o7OoXEqDsLLv8yXNbdqYdjz15UJlsJCeibPX1PcjJij/HE8Cf+d7Dj+6V76gA9f6Trx7HmZouZKTGISfDhqz0OORk2tDV048/P38YTpc6/RvNeGdNmWTdEhNjRO/yPg2T7HHcDyuXVGHbjhqsWVcmBEBh2fLzE4QY6O13TmPn7lplskUz6hp68NenD8Hj9cmIoezMeOTl2MUBPHCoEa+8flKZbD09IZujs7s/dN9nx4tNQh1HG+XxJw8ok02Dhg8DzVHVMAR799ejqakPGZm286KojLImJ8fKqImq6i4lsvUeOY6+0jKYMzOgO0uZTfBnc3YmXBXV6N53SIlsDQ09OHS4UZiROVrlnGw6HTIzbZKd3r2nTolszKZu3V6FuDiTGB8DZctIj5NLbfuOGiWyRTu63D6JQpPpdyDCv/NzVdiyvUoCSynJ1vOyujQ4OYt2w+YKyehrOB/79tdLeWNm+vk6juc2OdEq5ba1dd1KZCNbOEsIWXo5cOYyf2aWl7qX31GBuqZeHC9rR5LdIgyiYTCrmp4ai9Z2F/YfO39mn4ZQye/6jWfkTA5kg+WZ5dmlc79lW5Uy+TZuqpBsm91uGUIKyAzhps0VymSLZuzYVSvz28nePFD/xliMctdu3VEjQTEV2L23TmyOrEzbebJRx9FGOXioSWwWDR8Cuih4XYLQHFUNQ8AZb4ycs4xwMKh8Xf0++Y4K9Nc3Iuj1whA7tE+L4ziCAT/cDY3K+t048oVrNEQ2Q6iXsalFzbq1tTtkHEKkvpTQnDidllEdBm5/YNgxNHxfZqsqqjCgM2U2GSOWHtMg7upyobdXjaEUzQifQwbfBoNnRHScorPKv5elmDR2B4NBJn5G/gAVoCNKHRdrHarjjHSqdUBLu0OJbNGM3h43urr6I94NMr/XpEdtrZrACHVXTW0XrMOMU7FajTLeRMNQNDSGSvMj618z+pweKbtVgbCNNjDYFQb3YX+/F80t2lnVEP3QHFUNQxCO+DLCOhjh/hVVM8IMNo5q0CHg9UUsC5bvKBrqTgOXa0NykUjGQCAYRJyidSPBAzMgnmHKVPms420auUIkGHS6YR1RPtPhDJXRgN0eI9maSGCPGfuSBmbQNYTAc8hnGum5ehTruPDfy77PwQi/N5xTMdKwxhhD/YsR9lx4PSM5sZc6rFYTTCbq38gl2z5fEPZ4i7qxWzYLvMPcDbzz4xXJFu2w2UwR7aTwuvHOjbOquVfpKBMX0nGq7BENGj4MNEdVwxDMnZMNm80srJMDQYXX2uaUPozJk9KUyJYwbxYsGWnwNA8tffO0tMKUkozEhfOUyEYSlrzcBLS3OYdcDhJNjzVhrqIZoWlpcZg6JV0ybINlY1mw2WzE/HnROdtPNRLMRiFPolM6xDDnPEmFs1UXzssVQ5PR8YFg1o0laZyXSyNZw/mYMytbSE7Yozr4mba1OaQndMI4NQziJGFJTrIKydNg8D1+xu+owPjCJGSnx6Gts3+ojutxI85qwqzJ6Upki2bwDC6YnyscBTybAyFnVwcsWqRulu/yZYVC9kTnaiD4O53rZUsKlMkWzZg3J0dKaQeX99J5pf00dXK6cEKokS37bFXNUB1HG4W2SklJdM62jlboGJRW/LoUoTmqGoaAjL433TBJsm8NDb3iyPT0uFFX1wNrjAmfvnPGeT2YowmjzYbcL34WOqMBrqpq+Lp74Ovphau6RqhYc+6+E6akRDWyGfWyNjabRcq42PfJtWN5EHuQrrlqPPLz1chG3H7bNKSmxkkZFy8vjqdheVBnlwvLlhbI+AsNQ5FoMSKWWSR/UEbTkH2Vf3oCQVgNevlcFeiIcgxNS5sTLW0OeaZ8nnX1PULuceN1E5XJFs2gI3rd1RPESeD5pKHJ88pSalZGfPr26cpG+1B/3H7rVCHvqq3vFjZROjgkbmFQgp/xO6p03O3XTpKsaV1zH3r6PDKqprHVAbfHj2tWFCErXR1bcjTjphsmCykh71E6MTyr7JNuaXFi3uxsOcuqcOXqEkwYnyr3PYMhlK2t3Sm/Myi9+rJiZbJFM6ZPSZeRUZ2d/VKyz3UjcSL1SFpqHG67ebIy2Whr0OZwuXwhHdfnQTd1XG236I+7PjUzYlmwBg3RBm08jTaeJiK4LTZursS7a8rQ0BgykHiR3Xj9RMyaOXIjVi4WHVt3oun5V+AoC5E8xBYXIPO2m5B8+QrlM9842ufV10+h9HSrRFYzMuJx9ZXjcMXlJSNK8X8xe66isgOvvXFKSJ9YRsjszOrLSnD9tROUBR/GAnyBINpcHmEAZqUXV9duNiCFJX2KzzfJlN54uxSbtlZKPypLDDnH7+YbJgmb7Ujh70HHrd9UgbXrz8isQfYbTxyfghuvm4Tp0zKUy0YylLfWnEb1WeK6woJEXHfNBCyary7zFsax0214e3MFyqo6ZXRZZlocVi8pwKqFeSOqf8f6niN5zauvnxTCQpZfsuT2spVFEhhWXfnQ0eEU2XbsrIHL7UNsjAlLl+Tj5psmC/nOpYiL2W/MOr+9tgzrN1VKkJDO36wZmbj5uolKx0gRtD/e23AGa9aWiSNN+2PihDTcctMkTJ0y+jpurI+naXn4n5SPp0n/6n+PufX7qNAcVc1RvSBYptTR6ZI+vKSk85lFVYNb19vWLn+a01KjTjZGVn3egKxbJNKWjxsfZs8xe0RSFMqmOagXD38wlFHleWDvajSBpXuMmDPbNRoZt78XHcdRL11nDUyODok2PdLeEWrBGMzsHBU6TkpZg0hKsMAwCnvg72XPMUvucnqFVXek5hx/lMAXM/jsS1XtPKvGh9lvdFjZSkBOAFX9xhfScczic85rYoI6HTfmHdU/0lGNUSeHqx/p9116jmp0aUgNUQcabyxhiUYIW2LayA2X/6iyRXMUmiQ8l5Ce+9hA59RgiB5nYSBo8HJ8iYYPBwaRWBIfrXqEMxmjVsfZ1RltYxnMpPIVjaBzeqk7qP8bMODLMTXRquPIU6FBw1jE2A1Jahi1qDmjv06nNypl8/X1wdcbGqcTbQhHpqNRNmZTmVUdjrFQJYI+L4KuXhk1FG0g22l3d/8Q0pFoiZpTNmZWow3cZyR36o9C2Xg+Pd298PY5olI29pbxFY16hPcC74dolC3odSPopB6JzLSrvPqBFTe+6JONuo2VGdEoW1jHDcder1zHna1Uikod19UDn0PNqJy/G+jOek2qXjpcktAyqhqGVWw7d9dKjyoHzLO1ctq0DFx/7URljL8D0b33AJpeegN9x04IiVLc5AnSo5q4eL5q0VBW3o433y7F4SONoI2Ul2fH1VeOx/KlBcpL9+rqu/HGW6XYu69OLn1m4EiksfryklEpT74Qgn2d8B1ej0D5AcDrhi42HvrJS2CYthI6k9rsA43xt985jU1bKoUwg2yKK5cXSW8vs9Oqjd5315Zhw8YK6ZEymw1YuqQAN1w7UXkUnX3Qm7ZX470tlUIcwz02d2YWrrtiHPKy7cp1XMM7W1D93FvoPV0ljIqpS2aj6LM3I2mWOhKUMA4easTb75bidFm7/D5hfAquu2YiZs9SzxFQeroNb71TiiNHm0THFeQnCHHLksX5ynVcoK0Bvv3vIXDmMOD3AYlpMM5YAcO0pdAZ1LY5tLY6pJ98x85qcbZYdXP5ZcWydqpLgBlIeuvd09i6o1oIssiKvXJZIa67arxMAVAJOn/vrDmNDZsqxFHlWpGJ+PrrJiqvOOA9un7DGaxbfwYtrX2i48jwfMN1E5Gbk6Bcx9W/uRE1f3sbveU10On1SFs2B0V334zE6RrRnoaxAa1HVetRjYg33jqFp589LLMY4+1mIczo6fHAbrfg299crGzMCtG+fgsqf/lb+PscMCaGLgJfVw/0sVYUfvsrSLv+KmWyHTvejF8/sF36QeLt7NvSCcENSQzIusvXSOGD9lx1TRf++xdb0NjYK/1H7FeRTE0giCuvGIf7vjRPmZFJJ9X71kMIttYAlljAaAI8/YDPC33xLBiv/CJ0fE8B6Jj+/FdbxSiPYVlcjBGufh/6XT5Mm5qOf/zBcmVlfMx+PPDgTuzcVSMstRzkTvZVR59HiDx+/I8rkD5CLKwftN94tTz23BGs3VghDLac0+v1BdDb5xHyne9/fRGKFLJgl//5BZz+3ZMI+HwwJyZIBt/T0Q1LahJm/+IfkbZkjjLZNm+twh8f3St9jNS5BJnXOT/1vi/Nx8rlhcpkYwDuN7/dKT34vBvCOo5tIp++czpuuWnKiP3dH7TnAs018Lz2EIJdrYDVBhiMgKuPuxGGWZfBdPmnlOm4lpY+/OznW1BV1Yk4mxkWswEOhxc+nx+LF+XjH765WBlfAO+B//nNNhw/2YpYqxExlrM6rt+H6VMz8MPvLJFZ3CpAh/7+3+7A7j21EoRjkNDt9sPh9KCkKBk//tHKEXNWL0bH/fHRfVj3XrkEuujQk5eClVTZ2Xb80w+WK2X6P/2HZ1H2h2dkxrzoOL8fns4eWNKTMPfXP0bK/BmjKs+Y71H90z/BHquwR9XZj/QvX3o9qmPPg9Iw4mhtc+DFl49Db9AhN9eOBHsMEhOtkhnkhfb0M4ciDnwfDfidTtT+6a8I9LsRU5gvo2j4shblI+jzoe7RJ2VcjarSn6eePSyjX/LyEiRazmxbTo4dZosBr795CvUNPVCFF146Jk4qZUtOtgrhQ3ZWvBjCGzdV4PiJobNpRwu+IxtCTmpiBnS2JOhibNDZUwFbEgKVhxE4c1CZbBs3V+DosWZkZtqkB4lOPv/k78eOt2D9hhDztArs2VePXbtrkZwcK/JQNhpt3HOVVZ2SPVeFU+Xt2LitShxUzl6mM59MPZJtR3OrAy++cVKZbI7aRpz58wvQmU2wFefDnJwAS2oybOMLxVk9df9fxYFVIpvDg2efY5DQH9K/CTHy4s98j5/xO6qyR089c1iyb7wPBuo4ZpJefvUkmprV6F/Cu+ONkJOamg2dLRE6qw265Ewgxgb/0W0I1pcrk436n04q14pnlGeVZ5Znd9eeWjnLqvDepgqcONWKrAybcFKc03EZNhw70YJNW6uUycbKLlYAcR5pRsZZHZcaKyOmzlR04O131Ok46n/enbxD5S6Nt8jdyjuWDM/Pv3RMmWx9FbWo+OvLMMRY3tdxadRxBfC0deHkrx6LyrL4aAaDXKpflyI0R1XDEOzbXy8RwZTk86OUPCRpabEyo7G0tE2JbN37DsHd0AxzZvqQQ2vJSIOntR1du/Yqka38TDuqqjuRkho7RDaOgeGsxr2KjBHOxGNGkKymg0fk8OJn+eievXXKelIDZfslk6pjBmQAdGZGL4MInDkAVdiytUqMcLP5fNkY3Teb9diytVKZbMykMkDCLMNAUF5G9/m5q19Nf/m+Q42SkaGjOhDcf0mJMThe2iYOqwo0b9wtfakx6Snnvc9zG5OVLqXAXUfUGMCHjjShtc0pDsNAPSL6NzVOPuN3VOB0WZvMYU5JHcpATAOdJfL79jcokS3Q1YpAbSlAB1U3yLRhdtXTD/+ZI0pk4xmkw8UzObjFgmc34A/KWVWFLdurYTZRn52f0eXvRqMOW3eok41l0qz7G0zwxHXkzOOt26uVcQbwzuTdyTt0sI7jXcs7l3evCjRv3AVvdy8skXRcZhp6SivRfbxMiWwaNHwYaI6qhiFg1pSINPOTFxfLgcPfGW1ItjQYhN40tAxUZzRSC8PXq8b45Zqw7IclXUNkYzRMvuNWJJsbHso2TB8Un3V3jxrZ2I8qr+FKew0mBJ3dUAWWOA424MIwW4yybqo6KFhiPly5oMVikDJgliirAEt8WQ4XKQpMo5gljwzeqAANOErFnq3BMFgtCHi88h1VZzWIYMSecb7Hz1TpEZaqsprGMihoE9YhKnUc+h2A3xtRj8ge1OkRdKqpaGEJN0tYeSYjgWeYZ1kFqLtIAjSsjjMbZHyTKrBCyWSObKqyUomOIokLVaC7t3/Y1i4+a965qs6Dp7tP7KFI+lcfQx3nkWCdBg3RDs1R1TAE4XE0kVj/yPLIOWEsvVEBCzOpRiP8rqEXZ8DtBlmfmFlVARLXWGKMERmSOY9WvqNoDAaz4+w9iiQbDRVm5TLSFRHvWKzQxdpDPakREPR5oEvKhCqwxGw4JkcaoCz5UlWSk5sTKgeNBBKisBRNFRFKRlqc9D9HcuKdLq/0waUmqxnhZM0K6YhI5b0MdBliY2DNTlcgWUhHcCZpJPZmvsfPVOkRVtTwuUXSI+H7QpVsuvgk6EwxgHvo3SB7MBiALlHN3cCMG8/icOz5dGJ5llWAuisrK17OZCSwV5X9lir1r8cdWcc5HV4pP49V1D+bmW6TPtZIOo76l3duiiKyJ9FfvNv9Q9fOR36PWCtiszOUyDZmwaCE6tcliEvzX63hgpg3N0cIWJqbzx/7Qmervd2JiRNSUVKcrES2+FnTETuuCJ7GFiEGCIO9FiwJthbkIWG+GhIUGhoknujodJ3n5HMNm1sc4iwuXJinzFBasihfSroHUvtTNpYS8nOyKKqATm+AfvLi0FiaQc4qMyA6oxmG8erYnFetLBZjjms3OINO//SyVcXKZFu2tFCy5IOzMSw1pHFHRlFVBC2L5uaIcd7a7jxPj3i8fvT0eeRz9r+rQOYVS8RZddU2nicbHdf+5jakzJuO+AlFSmSbPi1DetxaWhznjY/iz3wvPy8B06erMTAL8hMxeXI6Ojoi6bg+cVLJeKoCurgE6CfMEfIk6pKBsqGnXVjEDRPnKZGNZ5B6giRAg0vxeXZJ0LZ8mTqCrMtXFImOG1zhwFJuvU6HyxSSd3FdImV1mUVlkI7rqoqxnuzq7L3nHXqejvP40dfrwdLFoc9VIPOKxbBmpg7VcV4f3C3tSFk4E7ZiNfaIBg0fBtp4Gg1DwAzMV+6djwf/sAu1td1ySdBeYllrfl4ivvSFucoySHqjEYXf/wbK//W/4aquhd4ciqSyjMWSlYmi730Deoua6CrX5PP3zJYxHCSyMRp0Uhbk8fiQkGDFvV+ci8QEdYxxZBwm8+/xky1ifPByZ2loXKwJn/nUTKXshBxBE2yuQqDiEIKOYKjcl5lUOqmzroQuT924kKWL83GqtFVGELAMjeeBhgjX77KVxVih0MAk6/CtN0/GK6+dPHdWabxxL86fl4vrrpmgTLacrHjcdds0PPXCUdQ29Ei5qN8XgD8YxJQJqfjkjeqeqTnRjmn/5+s4/JMH0FdWBUNMjLD+0oizTyrGlB/fp0zH0an56r3zhT28rq7nXNmj1xNARkac6GaTUU3wgWvypc/NQXu7A9XV3XIGWPJLHUfCvS9/ad6Qfr3RhGnpTQi2NyBQV4Yg+1TZ8049YomFccVt0JNYSRE4yoqjy/YfqEcw6JTnHCoHNsoZnjpFTQafWL6kAKdOt2HTtipxnKlHeDfw+a5eVYwligKsxKyZmbjphkl4/a1TA3RcQIKEixfm4dqrxyuTjYGbz3x6Jp585hBq66jj2NIQQCAYlIDTJz8xVZlsManJmPaTr+PI//3tOR3H7CpJJxOmjMOUH31ZmWxjFVQpg9vfR/vvvxShjafRxtNccObm5i2VOH6iNTT/cE62GOVJSWrK9QbC3dyC9rUb0bV7H+hF2+fPRupVlyMmR/2MQc55I8EDmQqZPZo8MQ0rVxTJpTaSuJg9x9KzHTtrhNiDzKFFRUlYtaJIsuSqIaRKFQeFOCno6JZyX8OEBdDlTlLOdsdqgv0HG7Bte7VUGqSnx0m0nNUHyufPBoPCSrx1W5UQ3ZDEY8niAixakDuisxkvVseVVXRg6+5anOFYDqsJC+ZkY/G8XAmQqEZPWRXqX9+Ajn3HoI8xI2PVQmRfv1KMPNXgOBOOqTl8ljhp5oxMGUszUuOGPgw4r5dngcR71HFTJ6djxfIiyfaOJC5mzwX7nfCX7oO/7ICUAesyC2GcshD6LHWVDwNLt3ftqROCIAa9uF7MGNKpUa3j6GDtO9iAHbtqJENIHbdsUT7mzMqS0UOqdRzPwZZtVaiv7xEbhDN76aiOZMXIxeo4zhXmjO3Kyk4heKJclG8wyZ0K9JyuRN3rG9C5/7j0pmZeTh23Cpbk0Q9Mj/XxNG1//Rfl42lSP/+fY279Pio0R1VzVDX8nUDbcxpGE9p+0zDa0PachtGEtt8+XmiO6keUw3lpOqpa6a8GDRo0aNCgQYMGDRo0DAdOwogwDWNU//5LEJqjquGCcPn86HGz5w1IshhhUlwCNBDdPf0oP9MhZUHjipOlRypawBJblgNxlENhfmJUlOsNLD9jPxKZbHOy45GbM7Llen8vIJnNmcoOdHb1IyEhBuOLkyOOcFIBnoGq6i60tjqk9GzChBRlfYyR4KiqhaOyTobPJ86eKn9GC7hmXDuDQScl8Fy/aAFHI5WXt8vP48alKO1xHwy2DlDH+f1BFBYmKmP7jQS3x4fS8g75MyczHtmZ8YgW8E44fboNfQ4v0tPiUFjAua+6qNFx7KPlvuOs43ElKVGj46IdnC/f0NAjUxGoR0ay7eLDgoR2NQ090qYyvjAJsYNm0mrQEM2InpOkIargCwRR2uFAI1liA0GZj2cx6lFgj0Fx4tBh76Mqm8+Pl149gXUbzkg/KGvXyS562coi3Hnb9GHnwY2Ww/D2u6fx+punhCGZFz/JqRYvzMc9n52l3Ahmj88LLx1DU1Of9F2yh2bWzCx88XNzkJyshkZ/LKC8sgOPP30IFVWdQjLCPVZUkIR7Pj0TE8edP1B9tNHQ2IO/PH4AJ0+1SvCBxggZqEmQNWd2tlLZ3G0dKP3FI2jbtg++Pif0RgOsuZkouvdTyL7xCuXBpCefPoTtO2vQ5wixm5KZ+4brJ+L6aycqNdBJiPX8i8ewfiN1XIhtOiHBgtWXleCOT05TxuRMUKe99U4p3njrlLD/kjjGFmcR0rG7PzNLaV8e9e/WXbV46e1TYpyLjrOaMGtqBj53xwzljj773J9+5pA4NewJpVMzeVI6vvT5OUpHwBAMOvz1iQNCBOg5q+PI7v+Fz80Rh1VDZHR0OPHo4wekh5ZMxOznzcywCXnh8qVqmPTDcLi8eOb1E9hzqFHG5XCudUpiDK6/rARXLS+MmgCJBg0XQvSkxzREDXjZH2vtQ/VZJ9Bm0iPWpIfPH8DpDicqFA7/Jp578RhefOW4UP1nnY2Wk030tTdP4clnDymV7Z01ZXj8yYMyQJ1kFDk5drkM1r5Xjgf/sPu8cROjjV27a/HIn/aiqblP5uDm5trFGCEpyq9+s33YWaGXOhqaevHrB3fi5Ok22OIt4gQyMFJa1ob7f79TjE5VYKDml/dvx/4DDbBaTSJbcpJV2J1/+9AuHD/Rokw2f78bR37032h8Z5MwcccV58GSlQ5XQzNO/uz3aFqzWamOe+iR3Xh3bRmZGmRWY0aGDT29bjzx1CFxxFTiqWcO46VXj4vDkJVlkxd/5nv8TCW4Nlyj3l6PrBnXjmvIteSaqqS92Lm/Ho8+ewhtHU6kJFvlbmDgZtveOjzw572SYVWF4yea8cDvdqK6phvJyVbRvzyzBw7W45f3b5OzrAq1dd341W+2ibNqt1tENrI3nzjZil/+eptkCjUMBUcN/eqBHUJQSMZf6t/UlFi5Yx/50x7s3lOnTDbaGo88fQjrt1dL0C07w4b0lFgZDfbUayewbluVMtnGfOmvytclCM1R1TAE3W4fmh0eWAw6xBj14mgx22A1GWDQAdU9/fD435+hN5poa3Ng/cYKGfDNC4HRS76YDeS8ss3bqiTDpAKMpjLLwEuBpb7MevBnsrCmpFhx4GADTp5qUXZpvfbGSfS7fcjOihcHlc+VxgiHvZ8qbROWYg1DsWFzhcyIpEHO7AzXjQYmf29tc2DdpjPKZNu2o1rKVimLLc4ssrHkjL8zWMLsviq0btmNrgPHEJuXDXNSAnR6PQwWM2LzcxBwe1D1+MvnzUIeTdAg33egXs5lUqJVzikdGgaXjEYd3ny7VDKuKtDU3IuNmyukEiNlgI7jz3zG/IzM06rKfbk2XKPQWoXG03AN6XztO9CA02WhUuXRBgOpb6wtExbizHQbzGf1L4NKGWlxOFXehkPHmqEKb7xVKrOYc3Li5YzyrPIZM5NaWd0lTPGqsHZduczoZWCVuo2yMTNOh7WppU/uXA1DsXdfPU6dbpWAOe9SrhvvVt6xvGtfe/OksuD0qTPtOHyqBalJViTEW+QsmKjjqFP0wNubKkRGDRqiHZqjqmEIOvp9MuvQFCF6w/Jfty+ALkXZtxOnWuWyj1TClWC3wOnwKssisV+2rc0ZcXwPS36ZETl2XI1sjU29qKntkr6jwZA5uYEgjhwLjcHQcD72nM1WDi4F5e8MmNA4V4UDhxqlt3LwiBwaTAn2GDkLvX2h0tHRRufewwj6AxH7US1pyXBU1sBRWatENp5Dj9sfsUyV55dl+6fL2pTIxixWX59Hnt9gsDean3EWsgrQCeXaRNZxJul/P3a8WVnlQ31Tb8S7gdku9tIeL21VIhvvLJbms3x7cLklz65Br8OBQ2r0CDPge/bVITYuso6j7tuzVwtiRgLHggUDwYjtRgze1NR0SeBJBU6Ut8PjDZW+D0aiPQbtnS5U1HQpkW3MQhcFr0sQmqOqYQjCpVuR+hf4Dj8Nyv8ffdDYEDmGObDBAd8ZbbAfims3XG8bZWZfkgqwNDoQCBke0SZbtIPrMlwvD9fT6ws9d1W9jMM9U77P/kFVzzXg8w9/sXLUQyAQ+o6is0pEeq5cNz7O8HfUyTb0M74X0nGKnmmAez2yHuFa8l1lOs4fFKfhQr3FPKuqsr0Xkk1v0MlZVgXup2Fl01HHqZMtmsHneqG7gclUZedBMrnBC8oW+o4GDdENzVHVMAR2i1FK4UmoNBgefyjTGm9Ww8NVVJSEGKtRsgqDwVI9i8Ug31GBgoJEKf/p6RmawWI2lZcDySlUIDMzXkqlI8kWNnrHj0tVIFn0Y8qkNCnrHuyM8neWQvJzVaQUkyelyd6K5Ciz3zIvN0EZgUzClAkS2gr4hlZfeDu7YUlPQVxBjhLZiouSJRPNDOBg8IzwHBcVqtEj/HtJsuNwDC095nvWGKPIrwKFBUmIjzdH1CNcS66pKtmyMmySxeK+j+xQACWKninPYG5uQsR149n1evyYMildiWzUXVOnZEg1UiQdx3uVn2sYivElKcMGtbp73HLnZmaoYZwuzE2AQa+XUvjBYJ9qvM2MfMUEXho0XAw0R1XDEKRYTUiwmOD0Bs45q3KZ+gPCAJxlsyBWEetkQV4C5szMQkeXSy7Q8MVKR6Kt3YnpU9IxQRELK42kVSuK0OfwSKlXWDYacGTZLS5KUsbCytKkq68aL9Hdzi7XOdkYxW9o6BVHdsnifCWyRTtWryiS0u3mFsc5g4R/Nrc6pHT0ylXFymRbubxQehcbGnvPRe5Zxs2zwMDI1VeOU+ZEp1+xFHGFuXBW1SPgCQWWuO/c7Z3So5r7yetgsKpxomfNzERJcYr0eoadVcrGMmmSBC1fVqCMBZvBrBnTM6XEdmCfLH/mezNnZIouUQHutWVLC2SNuFYDdRzXkuywXFsViLEYceWKIni9AXT19J+n40huQ2KlhYr0L8/gNVePlzPJsxnuW+SZra/vFbZpnmVVuOLyEtFl1HFh2ajjuG7so+XnGoZi8aI8ZGXaRP+GM+Lcd7xjuX7Uv6qmEMyanI5C9hi3OoSpPixbr8MjDMDL5+VI76qGDwHVREr6S7P2VxtPoyFiqc/MdBsOt/Shq997rnjEoNOJkzoxRd0YE174935+nijeI8ea0dbhkKyN2aSXMStfvXeBUsr1O2+fLhH9nbtq0NUVYnFklmHC+BT8wzcXKx2dc81V48XQXbe+HLV1PWd1nk6ybt/42kLloxuiFZMnpuFLd8/GU387Ir2+XDOWvrPP5zN3TMfUyWoyIUR2lh3f/OpCPPLovrOyhYq9GC2/7dbpSscjkEBp2s9+iOM//TUcFTUhxyEIGG2xyP/MLfJSBRKd/cO3FuO3D+6U3vJwAIIkNytXFOKuT81QJhv111fvWyBlqseONcuZDcs8d24OvvJltTqOY49Y0UJG067OsI7TY9LENFlTlaNzrl1dIkHMTTuqUd8YOqtcqvycBHz1njlCuKcKPIt8lq+8dhL1ZAo/20dD5uSv3Dtf6Xia6dMy8MXPz8VTzxw6Tzb2It/z2dlSuaEhcnD6O99aggf/sAv1Db2i46jm2K990w2T5M5VBfZlf+PuOXjoqYOorOtGgKXxCEpAZ+WCXHzy2knKZNOg4cNAF1TJJR8l6OnpQUJCArq7u2G32z+2Xp6Wlhakp6dDz36sMQj2t7W5vOhx++TeSraakHiWrVA1GPU9Wdoq7J3cwePHJYvDQINJNXikzlR0CNECI+Ysl2OWYaQNuIvdcxxFcPBQo4yjITvh3LnZsMZoA8A/CF3d/dh7oB6dXf3i1M+fky2GSjSAWfx9++vR0upAXKwJc+fkyCy/aNhvflc/WrfugbOqDvoYC1KXzIVtnLrs0UCw9+7w4SaZHUlSm2lTMzCuJDkqdBydZxIrUccREyekYuoUrrUuKnRcWXm7kHVRxzHDO2NGJkzG6NBxNfU9OHKiWQKaHMsxe3qmGOjRAJLr7NvfIBny9LQ4zJ+Xo3y+dhidnS4hVmKQlU4qZYsWHacCF7vfOKaGI8KYWWXZ/uyZWRIAjgYw03uktFXOhIE6bnwqivISlOi4kbC1R1Putmd/CnusuoB+j7MfqZ/+9zG3fh8VmqOqOaoa/k6g7TkNowltv2kYbWh7TsNoQttvHy/GvKP6t/+r3lG989/G3Pp9VGgnT4MGDRo0aNCgQYMGDRo0RBWioxZGQ1SCpaH7D9RLGStLall2xtK4wTMbVYCFAJ6AX16ESW+ARW+ImrJklsQdPdYkxB6FhUlYMC9H5tFFS8neudLf7HgsWpAnLKfRAM6d27OvXkpZ01JjsXhhnjJSm0gle+zLC5f+LlyQK4Peo6Vkb+fuWin9tcWZMG9uDgryE6PiPPBZ7jnYgIbmPumbmjklHeOLo6O8liV7+/bVS+kvddy0aRmYNiU6WghYssc5uWVn57lOmJAqJYUqe0DDCAYDQFcV0FUJBP2ALQtImQidwRwVOq6+y4WKVocQAKbEmTEx0w6rQn6AgbJV13RJmX6fI1T6Sx0XaS6tCrDPXXqPu/tl5jZ1nCrW2sFo73Bi9946tLY5pNd43pxs5OclIhpA8sRde2qFmFBKf2dlYfy4lOjQcS6v3KlVVZ0wmfSYPi0zaloIxhp0ep28VP79lyK00l+t9Dci6uq7cf9vd6Cqqkt6VQmTUS+Mk9/+xmKljo0/GEC3p1+MEJIDhI6uDia9HgnmGKFkVwWOK3no4d3Yd6BBaOFlRrNOh/y8BCEaYb/qSOGD9hwN3z8/th9btlaJkxq+Q9PTbfjm1xZKEEIVqIaef/EY3njrlKwh14y7LjnJKiQfSxUzEq9ZV4Znnjsi4yVklmUQsNstQp513TUcw6IOu/fU4k9/2YeODpf8Ttli40y47urx+PSdM0fMILkYHXeqrA0PPX5AHGghGjnLzrp0fi6++OmZSp0uBkV+8+BOVFd3nWU6DcJsNgop27e+vkjYTlWBzLC/+e0OnCptPTcX2mjUYeKENHzn20tk7IUqBL0uBEtfBTrLOSw3xLqj0wNxmdBNuhW6uJHTIx+05ziGZs2xJhxv7IGH84/5pg5IijXjxpnZyFMY9OIee+a5w3hnbZmMggnr3+RkK778xXlYuCBPmWw8m2+/e1p08GAdR2Kxq69URwpE7NhVg788cUCChGF5bXFmXH/tBNzxiWkj5hBejI4jF8XvH96Nlpa+s7JBRkitWFGIL31h7oj3bV8IVdWdeOB3O1FT231O/5pNBnHyv/G1RcL0PJoY66W/7S/8q/LS35Tb/3XMrd9HxdjzoDSMOOjQ/PbBXaio6EB6epw4WXwlJsZIJPixJw4ok43KtsfjhsfvFxZiZlKNeoP87AkE0ON9f2SCCtAQ4aUabzfLmuXlJSAjIw5V1V1yYdBBVIU33y4Vxl9GfPPy7CIbM6qtrQ787qFd6OgIsYuqwOYtVXjpleMSMaRcfOXm2IVB+Y9/3isZL1U4fLQJTzx1SOaV5uaG1o3rx3NClkxmp1U6Ww//ca/M7MvJeV82g16HV18/hY2bK5TJxqzM7/+6Hy1tDjkDfJ652fGy/zZsr8bra8uUycZxKg88uBOVlR0iW35+AvLzE8Uw37O3Dk8+c0iZbNRff3hkD46daEZqauxZ2RJkNMyx483ymUodF6xcB7SfBEyxQFw6QMc0Jgnoa0Tw1CsI+ofOfx0t7DzTjkN1XbAY9EizmZEWb0FyrBmdTg9eP1SPvghzc0cLGzdV4LU3TgkLfFj/8szy7D78p71yllWBOuzpZw+LTgvLRl1HnUfdd+RokzLZqPv/+Ng+YZrOzbYjPzdBXnROyaC8dXu1MtnI4kzGX96hvEvD+tcSY8Ta98rx5lulSqtFaHPQ9qCOo2y0STgHmTbK088dViabBg0fBpqjqmEIDgkLJg0423njVFi6mpAQg7376s+Nwhht+IIBeAN0UvXnRVH5szirfj+8gaHDt0fr0tq+s0ayzXGx72djmDXKyrKhpq5bHH0VoIO87r1yKb2kMR5eO5Y48oJtaXGI7CpAo3vNe+WScWAGNSwbM4GcUccI/6YtlVCFDRsrpHyKQZtwdpIypqXFybq+t75cmWybt1ahs9sl6zRQtnAp4dp1oXVVgZ1kIW5zCPuw8WwpLWXj6ByLxYCN26vEmFJlmLMEk/ODB2Z1mWHg+eB4qXCGZLRxuqwdx0+2SNaU43LC4M90XE+cbJHvqECwvxNoPQGY4gDjgMyC3hhyVh3NQIeaAISbDM61XTAb9FLme07H6XVS/tvl9OJUY48S2XgG16wrlwogsugO1nFdXS5s2ValRDZi/YYzosuo0wbKRp1H3bd+o7qA1+atlXIHUI8M1HG8K/yBINauL1cWuOGdybuTd2i4XYCyUYfwruWdG57TPNrYv79BMqm0PQbqODJM00bZvqNGaXB6TIJnQ/XrEoTmqGoYAhpwLDcbaCSFQQXH0kxV0V9fICClyJGOq26AM6sCvBQY9Y1UFs2LIhgI9SepQHNLn5RNRZKNlz8r99iLrKqHkbP7IsnGS99sMaC0NNSnpwKlpa3Dlkjx0j8lI5LUGEocXcJgUqTSN0bOOS6huydULjfaqK7rllIzOgpDZLOZJZPU1MI5yKOPqppQS0OkucZcN4fDK+dZBagjPG5fxD3H92j4qtIjcLQAvv5QNnUwDKZQ3SO/owCdDg8cHh+sEcrJORucaFZ0FngGGdy1D6PjTGaD6BEVoO7i3z3ciBzuOepAVSgta5d2gUg6zm4zo66+R2wSFaio7JC7M1J7Be8z3rnNzWoCXtQRfLaR2isoW1+fW5mO06Dhw0BzVDUMARvuaW9EMr79gYAoZaOi3jLp+Yzopp7/HRUwm/WyNuxrGYzwIHBVPXnsL6Zs4X63waBskYz20QDJudj+w7mRkcD3WS6qClyXC8nGgI4q0gyuCwe5RwKfNZ+5qh4ps0kv7YsXkk0VMRvPA+NZEXWcyBbSgyrAHjIiUuwj/F74O6MOZk6lgTEwjHBBQK9GNnIT0CENcyoMBt81KiIjMX6A/uUZtlqiV8eZFcpG/TqsbAG1eoTrNlxc/JyOU3XnMzg+nB3nD9lxqnScBg0fBtou1TAE06dlSBSVTHaDQcIW9kpNnpimRDaTwRAyRiJYwHyPNpRZkaE0riRFiInaz5LaDAQzNOxbIRmVCmRlxaO4KEnYYQdfXMzQMOtFRlEVsMaYhCWR+22wbD5fQIw4stiqwsKFeXD1+4YYSyznc7q8wtqpCvPn5ogc7C0b0svd0y/7TRUp0PTJIfbc/kGlb5Stq6sf+TkJyFHEmjxjeiasVqNUQETScWmpccKyqwJTp6ZLxoPloIPR2eWSskKyEyuBPQ+wJALuCCW0PhdA1t+kYhWSIcVmRjozRW7fED1CYiW2hhSl2pTIRpbaGdMz5EwOlo1nl2dYlY5jkI06jLpscJsAdR5136IFuVCp43z+oNwFQ3RcrxtzZmUjJkYNo/7MGVnSczy4vJey8a4tLk6S0m5VdhxtDtoeg9HR6UJGuk1sFg0fAjQw9QpfOq30V4MGQVFhEpYtLUB3t1vYJ3mRUhE3NfWJ03DzjZNHnS0uDPamWg1GiRQyuxvKVAbP/gxYDSZlrL+M/N5602RxpBsbe2XNuHak1efFwMt+wvgUZcbIrTdPkfKu+oZe6TvixU9jmM+Vl9rcOeqcwRuumyRjaGpre+BwekQ2GnUNDT0oLk7GimUFymS78vIS5OYkSHlyb59bZKODU1fXjewsO65YXaJMtiVL8mUMQrjEl7I5nV7U1fVIn+oN101UJtusqRmYMSUdrW1OIVaibOyDa2xyiJN4yzUTlI1IGFeSjMWL8qU0r32Ajgv33t9y8xQJoKgAneSrrxoPl8snbMkktOGLP/e7fMLAqor1l+NndHlLQ7+4OgASJ5H5l46rpw9InQrYspXIRr27ZFwqzEYDOhwecU6ZcaPj2uXyoigtDsVpcVCFG6+fJGeSZ5NnlOeBZ5Znl2eYZ1kVrrxinOgy6jTqNspGXUedR913xeXqdNyyJfkSZK1v7BHHlLLxjqit70FKcqywm6t0osmWzzuUdyll491aX98rd+0nbp6qrNpm0sRUsTkYeKMNck7HNfbKWbnl5skR27s0aIg2aONptPE0EUHD6MWXj2HDpkp0d/dLIIcz33jZXnXlOKXzwbhlnT4vXD4v/Ge3LxWv1WhEnNGsXLaNmyvx6usnpTeF+4DZkZXLi2SUyUiWsF7MniMR1vMvHkVtXbdEy61WMxYvzMVnPj0Tdrs62nWCJDEcAcNeWZ/PD4vFhFkzM3HPZ2ZJplol2JP9xDOHcPJkKzwen4wxoSHw2btmSWBHJThX8MmnQ+zD/f1eGI0Gkekzn56BqVPUjQoJ9x8/+8px7D7YAIfTC6NBh+zMeHzi+klYOFuNQxMGjTaehU1bqoSsJUwec/MNk7H68mKleoQGL0c1rVlbJkEuguQxdGCpg1XOshaToekAgnU7gP6uUMkve1YzZkFXsBI69qqOEC5qJFJjD7aXt6Gtzw12YcSY9JiUaceqSenKZ6keP9GMp589Iky21HExZ6tJ7v7MLAlQqARlIov5qdK2czpu8uQ03HPXLGHEVomW1j48+exhHD7SJBUabGdgtvKu22dg8qQ0pfuN9hEZk3ftqYPL5ZEqkrzcBNzxyemYP09d8JdgYPBvLxwVQipWLPHfQFKqm2+ajMtWFo26jhvr42k6Xvl32OMUjqdx9CP51p+OufX7qNAcVc1RvSCo3EhvzvKWkuLkqIrAsReJ5EqE8Wx/UjQ5+iGHKyCU8GRLHmlc7J6jTCSB4CXGkmDVBtJAUB3RYOrt84hc2VnRMWw+LFttXY+UXyYlxAjdfzQMdA+jqblXGCg5X7CoKGnEZfswOo4R/cYWhzBhFhUknmMBjgbQ0CSpSDTqOJLEcBYiUVSYrKySJRJkDE1fQ6hfNTYdOvPI65GL3XPMpDZ1u+D1B5EcZ4bdGj3rxvJa6jg+WwZGMjOiTMfVdqOzu1/YifNy7VGl41jxwMAcS6kLCxKjSsdRLmYrGYwuLkpWGkwaDFa08LlSJuo4VXwUmqP6EeVwXJqOavTcyBqiEswGsiw0GkHH1GxQGyEfDrwIRjLS+1HAy2rCeDX9dx8EGh685KNVtvBM4WgEDd5oMnoHgiV6fEUjGESaPgqBpP8NWD44klnxjwLJnCaoK8m/ENhzn5MUnfuNmXs6C1Gr4/IToa4I+cLIyoyXVzSCgdVoCvoORGJCjLw0aBiL0BxVDRo0aNCgQYMGDRo0aBgOzKqrrJDUR0+WfjShOaoahkX5mXYZ9H3iRItk4chKePmqYmRkqO0XJOrqu7FxUyUOHGwQ/l+ym65eVay8lyZcArRpcyV276mTXh9mVi+/rAQTFbGIDgQJitiTt2NnjZSekaiIvSpcP9UlXiSh2LqjBtt21QgrbE52PFYuLcSCeTnKSHfCCHi9aHhnC+rf2AhnXROsORnIueEyZF+3AgazGlbdgeV6e/bWYfOWKtTUdUuGcPmSAqxcUQir4pJHynastBVbdtbiTE0nYmNMWDgnGysW5SMhwkzJUZevpw7BxoNAd7WMVdGlTgIyZ0MXo16PsBx5w8Yz0pdHzJqZhcsvK5b+N9VobXVgw6YK7NlXB683gCmT0+RuiIZKDZZyb9pSiZ27a4W0qKQkGZetKBKmZ9WgjuM53bqjWuTMz02Qc7pgfq5y/ev3eNDw9hbUv7kRrvpmxOZmIufGy5B97QroTYr1SCCApvd2oO71DeirqIUlNQk5168S+YyxVrWyBYM4fKwZm7ZVobK6C3GxJixekIeVSwsizs1VMWubeuTkqVbpO164IBerVhZFbfZXg4bB0HpUtR7ViNi1uxZ/+OMecWxirCYEA0EhH2HZzQ++u0wcHJWkO/f/dgfa2hxnyYl00m+ZnGzFt76+SAw6lcblL3+9VXoZzRaDlKCRvZMjQu794jysXF44Yn/3B+05spv+/Ndbcfp0u8xPM5r0wiLKnjwSPd1y02SoAlkc739wJw4dbRanlL2MfKYkprhqdQk+/5lZypxVGnBH/s8DaHhrk5wDQ6wVfpdL5vlmXbMcM//re8qcVarvJ546hLffPS29xzExBrg9fpFz+rRMfP+7S6WfayRwMTru7Q1n8LfXT8iztMYYRUavL4CSgiR87ysLkKqwHDjYfBTBsjcB79mxKrwKA14gNgW6qXdCZ1Pn2Bw91owHHtwpOo4MyQTXMCUlDv/wzcVK2zHYM/uLX28TRm7qDp5LjjCJt5nxlXvnY+mSkSsH/qA9xyAhZSs/0xHScUa96F/uvU/fOUMpCzZZdH91/3YcPdYE3Tkd5xcZr7tmAu757Cxlzqrf7cHhf/41Gt/dKoFfgzUGfqdL5My+fhVm/Mc/qNNxgQBO/M8fUfXsWwj6fKJ/A/0eBAN+pC2bhzm/+hFM8TZlOu7Vt07hhVdPSFCa5Fhenx8+bwATxqXgB99aIiRoqrB5axX+/Jd9wuRMPcLebY/bL73HP/je8lFvYxnzPaqv/T/1Pao3/2TMrd9HxdjzoDSMOOic/uXx/RL9JWEM2X6ZRc3NTUBjUx8efXx/xCHSowFSrP/5sf3idIls6TYhpMjLs0sWjp/RoFMBrsnjTx4URt2cHPYL2pCWFpKNl9jjTx5AR4cTqvD8S8dQWtqGrCzbORIlriFJZMjwXFHRoUy2d9aV4+DhJqSnxiInK17Gb+Tm2BEXZ8K6DWdw4HCjMtka3twkL3NKEmwl+bBmpcFWnA9zejIa3t2CulffUybbocNNeGfNaTFCcnPtSE2NQ062XfYdM3Fvv3NamWw19d148c1T0EOHvGy7OKWZ6TZkpdtQXtUhn6lC0NOH4Jk1jEIAsWkAM6jWJCA2FXC2I1j+rjIdRyI2Gpecw5ifH9ZxNjmrfO/Pj+2T76gA1+Sxxw+Ik8r7ICOs43Ltonf5GYlbVIEMp2Xl7ULCxqAqdRyNcTpc1H/VNV3KZHvr7VIcPtIo68UzyrPKM8uzyzPMs6wKda+9JxUj1Gm24ryQjivJF53HAF3DW5uVydayeQ+qn30LJrsNtpICWLPSEVeUC2t2Blq27EXVM28qk+1MZQdefuOk3KEc48N7K0u4AmwoLWvDi6+fUCYbbSTaHLQ9aINw31Eu2ia0UWiraHmq/4XHpPp1CeIS/WdruBD27q+X+al0AAdGeBk5T02NFTZbGgMqcOx4i7DXDZaNP2dkxKGpqReHFDk1NIJOlbbIPFBmAgfKRkOTjjQp7FWAZWZ79tTBbrfAZDqfgIqZ6D6HFzt31yiRjVk2Rn6ZHR/MuMrSKQYntu9UIxtR99p6BBEUQ2kgTDbuQb1SR3Xr9ipxWgazSnMdY6xGGZXE9VOB3Qc4ksaD5KTzZWMGic9135FGdHSFRq+MOtpOAe5eICbp/CHqOj1gsQM9dUCfGj3CAANnRkbScenpseIkHjmqxqmpqOzE6bJ2pKTEnlfhEJItTkbpsAxdBRgE3Lu/AQl2yxDG1ZRkq2Q02Y6hAjyjLEdmddJgHcezy8+376iGKtS98h50er3otIGgzqMzo1LH1b+1GQGvD+ak87N/zPryVffKOgR8aoLTu/bWyditwVlT3rGsZNm9t15m5arA7r11YnPQ9hioR2ib0EahrUJbSoOGaIfmqGoYAg6IZv3PQGcrDEZ/3f3+0HeUyOaU+Z+RRkjwcmCAMDx3cPRlc8Ht9p8r1RuIsFGnKqPKkSouty9izyIvMT7qllY1sjldXhmDxPK84RiUG5v7oAqO2kYY4yKXqBrirNKzqioyzVm9gwMPYcRajdKHzLIvFWjvdIkPGKmckc+aJcocg6EE7p73HdPBYBkwR6/QkVWAjk6njDCJNEKCPWYs3+O4H2U6zhNZj4TvC1U6jnrffQEdR5b4ljY1eqSvzw2HwytnMhJ4hjl6Rdlc8rom0WWRQN3nrFVX0eKsbYA+JnL7gjHOCk9XL3y9DnaKqJgAAQAASURBVKhAa7tT7vaIOs5qkpmvnV1qdFz4HEZqmRE7zu1HuyI7bswidKGpfV2C0BxVDUPArBtBY2kwaAjQgAp/Z7Rht8dAb9BFLH1jZo7nWBWBQThbyQtgMLiWXM3RmKcaUbZ4C8wmg1yckQwVyjc48zVaoNMSutQjZ/48noBkRFQhJiMFfldkY4Pvx6SnKustS0mNHTZjyv43ZqlVzd5kZosqJJITzzPC/Wgfof7ZD4SJmSMKF5rDfB7opBqMgFlN/2yCPUb2U6TnyvfYG83vqNJxfG68B4bTcdTRKsA14d10IR2XlGBVNmaIZ5FnMhL4XFkKrALca5b0ZPhd7oif+539iMlQR5LFvzvgHkY2lxtGW+ywTvZIIykxJrTvI+g47kOzSS96UAV4DqXtPhBZNtoqquw4DRo+DDRHVcMQzJ+bIzO3yOw4EFTGra1O6flRxe44Y3qGzIqkbAMvB/7c0uKQ0uTZs9SQKXE2Hl9tbaGMyEAwA0Jncf68HCWysdRn7pwsdHf1S0Z6ILp7mM00YcH8PCWy8cJctiRfMquDjXNmBBmYWLpI3WS/3BsvR9AfgM/pGuKkBr0+5Nx0uTLZli4ukEzW4Kwp15Frt2xpQcTqg9HA/JlZiI0xorv3fCOTGcGuHjemT0pDWooiMiWy+9JZDWdWw6BOcXcDtiwgXs1ZJQN3RrpNdO1gHcf32BfK76jA+HEpKCxMEh032DjnezTKVek49uDNnJ4h5Y6DdRz7ZhmwIbuuCvAMLltSIGdysI7j2eUZXrJYoY67+QoEvd4hATnqPJIZqdRx2detlLJk76CsKUnufH0OYV9XRfS0YE4OYixG9AzWcf6AlPzOnpmFpEQ1TvSC+TniiA6uvqBt0t7mwriSZBQXJSmRTYOGDwPNUdUQ0an59KdmyOXJHgYSeLAxnz8nJVnxubtnD+kBGs0L/3OfnS0R6traHilFY8lXXV2PRKzvvmuWfKYqMn3PZ2YhLS1W1oprxrUjcQETN7ffNk2cbFW4/RPTkJ+XiLr6HnH0WQ5cX98jIxyuuXo8Jk1UFzW//uoJmDQ+Vci6mlscYlg2NPZKaeiyRXlYMFeN8Uvk3nIF0lfOR39DCxzVDXC3dcJR0yAjHNKWzUX+bVcpk23u7GxhkmYPckNDrxjpLS19aGzslXFIN10/SZlsJYVJuOayEiHZqW/qRVdPP9o6nNJ/ScKs229UxzKti0mAruiy0C+OVsDTG3JanS2AJQG6kquk/1gFWF1APcLyPOoO0XEdIT3C9z73WX6mJkvOMkLKlpIcKwzn53RcbbdUs3zq9hlKx17cefsMIWETHdcW0nF1DT2yB6+/ZoIY56pw4/UTJcDLs8kzyrPKM8uzyxE1PMuqQB2WtnSO6DTqNtFx1Q2i86j7cm9erUy2zNWLhXnY3doOR1WdyMZSZWd1A5LmTEHR3Tcpk23ShFRcvbpE7tD6hh65t7jv+DNHD91+8xRlstHWuP22qWJ7UHfwnJJ7hGeVNsrdn1HHMj1mYdCpf12C0MbTaONphsXBQ41Y+145TpW2ioHCOapXXzkOxUXqLvuBs8HWrC3DoSONkgThuAbKNnWKurENYdTUdOHddWVCKuL3B8U4uuqK8Zg3N3tEL4aL2XM0kNasK8e2HdVCU0/WyStWj8PypQXKZ5UyAr1uQwU2b6sSgor01DhctrIQl68oGrYPc7TgczhR8+Ia1L68VgwlS0qiOLD5d1w7hIBktMFxCJwpvH7jGbQ0O2CNNYnzetWV40Y0mn8x+43Xy8799Vi/rQq19T1Smrl4bg6uWF6EjDT1c/yC7acRbNgXIk+iY5oyEbqc+UpH0wwcw8WzynEmBOeAXn3leJnLrBocUbNmbbnMUWWGhkGRK68YN+LO1sXsuaZm6rgy7NxVK/20BfmJuOLyEixdnK/cMKfjvHZduZDHuajjMuKw+rISXLaqCCajWh3n7XOg5vl3hDjJ3d4ls0rzPnEV8j959bA9+qMFZk9rX16H2pfWwtXQLCRPuTddjvw7r4MlOVHpfuP+5+zv9ZsrUFffK2OHlizKw1WXlcjEBJWg/t23vwFr3yuTkU1kJ2ZVwTVXjlcyc37Mj6d552fqx9Nc+89jbv0+KjRHVXNUPxDh3s9I5ErRIBuhKsN7IfAC8wcCo2aAfJg9x2PPtVPtAF5INj5T1YZlxF43rxd6kykqZRvNdfuwOo4lj9H4TAnOZKSSU5VFHas6jiWOgWBQ03EfEpqOGxuy/T3pOAY0DXq90oC05qh+RDkcl6ajqqZ5ScOYQjQaSGNBNl4Ien30GUkEL9JoNODGgmyq+qHG8roR0SybLkrPabTrOAYvo3Xlovk8RLts0azjolU2IlqfKaE6Y/93Afr4KmMQOlyS0BxVDR9YcsNeFb3RgLiCHCE1iBYwIs0yL0ZZMzNtUaWIKRPHDVBGEqOoIrSJhDDxFJn/2E+mihV2OLCPps/hQWpyLGy26DJK2IPU1eUS9mZVJBnDgXMi2TPIHm2VfYKR4HJ50dLqgMVikPMQTRkHMohz/jIdwszMeOUl8ANB/REeW5KVGR9VTisrRrhulJHrFmmcjkod19zSJ+zSLL9U1dM7HNjHSGIlzqPlvM1oAsuT2TebmGgVUsVoQsDRi2BPJ3TWOOgTUxBNYJ8qnyvJlQbPQFYNsnRzlBn1R1ZWfFTJpkHDByF6rGcNUYWA34/qZ99E9XNvwdXQCuh1sI8vRPEXb0PWlUuVGyFbtlXhrXdOC2kBZyLQUb326glYfVmxciW8/0ADXn39BCqrOxEIkJzKiqtWj8N110xQbmiePNWKF185jtLSVumfJRPxqpVFuOXmycL8qxIkZ3nx1RM4fKwJXl8AsTEm6fW57ebJysZxhEED5IUXj8kQdc7/ZB8S2U3vuG2aDFRXCc6gffHlY9i2o0aMJZNRj+nTM4VIo7AgSbmB9OrrJ7FhUwV6etzSI8V+xttunYopk9OVykYH6501p6VnkMEROqjsZ+RZWDBPDTvsQB23cXMl3nq79DxH9frrJuKylUXKdRzPwWuvn0R1TZc4rKkpsbj6qnG45ir1Oo69vS9Rx51uC+k4uwWXryrGLTdNVh4wZG/vCy8dx9HjzbL/Yq0mLF2Sj0/eOhXxisaqhUFH5vmXjmHf/nrRcXS4FszLwe2fnKY88BXo60H/+lfgPbIbQbeLZQYwjZ+BmNW3wJCphq0+DFe/F6+8egKbtlSitzfE4DxpUqo800kT1faTc4+99U4p1r1XLjNTqeOKCpNw682TMXeOOoLCsQodZ+YqDGTqoiiIOprQelS1HtWIOHn/Yzjz6IvQG4wwJycIRb27tQMGqwXT//VbMrJDFd5ecxpPPHVI+qPCWS1muoR18o7puPUmdUx7u/bU4vcP7xaHgQzJBr1OqOu93gCuuWo8vvT5OSNmZH7QnqOT+otfbxX2P8pmNIXGmrhcPixZlI/vfnuJMiOTzJz/9cutaGjqldl0FrNR1rC3z4NpU9LxT99bpizzywzD//uvTThzpgP2BIs49DROerrdQu//kx+vEqZsFaAc//OLrUIqZoszIy7OJEYmGUUzM2z4539cOWKkGR+033g+H3hwJ7ZtrxZGblu8GT5vAJ2dzNbE4AffXaqU/OyvTxzAm2+Xyp6nM0OHi7LFWI34+n0LsHRJgTLZ3njrFJ58+pAQxXGtCD5TGpqfvWsmblTI5rx9RzUe+uMe9Lt8SEqKEZkYhKBRfMN1E/H5e+aM2N/9QXvu+Ilm/PL+7bJWlE10XK9HWH+XLyvEt7+xSBnXQk1tF/7r51ukCojPlMEuksZRB3Pc0I9+sFxZsJCVGP/vvzehsrITCQkWxAzQcSUlyaLjVFWQBPud6Pvrr+A7cwK6WBt0FiuCXg+CvV3Qp2XB9oUfwpCerWS/cc/f/9sd2LGzBtZYo1QAhXUc998/fn+5MmeVpv2jj+3HO2vKYDLrJSjNwA3v/9hYM7719YVYuGB0nfyx3qPaufa/lPeoJl314zG3fh8VY8+D0jDi6KuoRfWzb8Foi0NsfpYM1CbLnq0kHwGPD2UPPQN/f+QB3KNR3vjKayeY4EVOtl2cF76ys+Ilk/TGm6XoGDQ3bDTJChiRplHEEQl0HFhyxlLHeJsZmzZXoKq6S9mlxUwqL6m8vASJ3tMoYqSc2RCydx4+GmIXVYG315RJ5oiU/sye0rFhJjor04YTpa3YsbtWmWzrN1SgoqITOTl2MdYoG//k75VVnVi3/owy2chsevRYszilLCOkgcn1y8tNQFNTnzhiqkC5du2ulefIGZfcb9x3eXl2cSSYWVIVJyUz97oNZ6RMmnNJeU75c05OvGSBn3/x2JB5l6MFBt2o4+hAZ2fHv6/jsuNlpjA/Y2mmqjJprg3XiIzhcWEdl2GTIMl7G87I2qoA9xJl497iHjun49LiZA/u3FWDY8eboQo8i3RS887pOJOM+eHZPXa8Bbt21ymTjc+tqiqk4xIH6biKig5s2FihTDbP4Z3wVZyCPiUDensSdJYY6G126DNyEWhrgnv7u8pkO3S4Udj9Ob+dd+lAHUdn9YWX1ek4Bh02bq5AfLxZbBCeUzrSPLf9/V787YWjYrNo0BDt0BxVDUPQsm0/vD19MoJjMGKy0uCsbULH/mNKZDt6tFmcrUgZLL7HESeHjqhxuMrLO9DQ0CPGx+CsKTM2zFweONigRDb2pLLcl5nUwbLRCGYUmCVfKkCjd8++erngB/cHsu+N7+zaq85R3b6zGmaLYUi2mb9bYowy6kcVdu8JrcvgkkauI/fc3n31kplWgf0HG8TZGzzXmPuPjkNZebvMylWBA4caZV2YPRosGwM3DJqcKm1TItvhI03irHKNBiMl2SqOmCodx3Jarg3XaDDYt80MIddWBbiXOIKD6zZYx3EPerx+2ZMqwH7UvfvrJas1WMeFzm4QuxXquK1nqx4i6TizWa2O8x7bJ61HOtMgPaLXS68qPw963MrafFg5Mrjah/uPGVXeubx71ek4n9wDg2VjULO+oVdsFg0fAmSFV/26BHFp/qs1XBB+p0uUWSTiJL3ZhIDPB59TTUSfBEAMULLXbYhsZw0AfkcF+Pf6fEGYTEPXTQwnHSTbqgIs4/L5I8tG8FGrcmiYpfH5QrT+kUCZHQ41shFOR6jvMxJI4MV1UxU157oYjZFLyVn26PMHZJakCnBdhitz5zOlgafqPDCjIASOEeQjcyfPiirZGLghIpWoht8Lf2e0wTXhnorEbsq1pAruVymbjKMZRsfpdHA5fcp0nN8XlDMZCUaDOh1H3UWyM+MwjLVcT1V3AxF09kFnGKa32GhC0O+TUmAVcLg80nIUCXzWLLVVaY+I2RFRx6nVvxo0fBhojqqGIYgrzJGZgmT8HQxvdy9MtrjQdxSAjHXMsjE7ORhUulIulxmvRrbMUJkee46Gm4VI+VUgPc0Ge7w5omwymy4A5OclKJGN2Q4SEpHpN5JsZO4kAYQqFBUlRdxvBA04yqaK3IZ/N43gSI4ynzVL0lSxirLEkWKx9zOSbDabRdgxVSA72y7PLFLpW1+fG7FWI3Ky1ZzVUBsDddxQ54Dv0Unkd1SAa0ICoEh6JFQqrVMmG/cSyxwjycY9yL3IUlYVYLUIs1iOYfSv2+tHUcHI9JJ/EHgOSLrGgNyFdJwqGHKKJGMaSccFXX3QJ6dLZlUFCvISz+6tyDqOGXRVRFThcxi2PQbLxnOs6qxq0PBhoDmqGoYgfeUCxJfkw1XdiMAAQ87v9sDd0o7UJbOEAVgFJk1IxYTxKWhtd5zXQ0ZlzPEXvFCnTVVD0MJeo3lzstHV039exoMXGcvlOMJh4Xw1bKJ0oFetKJJSoIHRcRmj09gnZUpkn1QBZsJXrypCMBBisB0oG9lYKfvKZeqIbS5bVSwBkI5O1zmDhH+SZInvk2laFVYsLxSHr7XVcZ6xxF5uZnCuuLxEGUHW0sX5UobJXtmBziqdLWaPKLsqJ3r+3Bwx0poa+ySzEIbH40NnVz/mzMoWZ1YFJk9Kw7iSZHmmA41M/sz3+JkqxmSuyexZWbL3uVZhcA35nLmmXFsV4F5asaxQ9tZAJz80RqdP9qIqHcczeMXlxZKp59kMg2eWzzQ+ziLnQRVWXx7ScXyuA3UcdR4Dw5cr1HHmOcugs8Yi2NV+no7jqBpdMAjL/FXKxuZxP7GXt7EpNCYvDN6xzN6vXFGojASQzOW0OThCaqD+pW3S1e3GvHk50luu4UOA24xlI8peuCShjafRMARGawxm/uy7OPCDn8NRWSvZVV4IrA9NnjsNU//5q8pkY/T3q/cuwK8e2C5ENrwcwtmsgvwEfP0rC5SOR7j7M7PEuTpxslUuB4rGpctIj8M3v7pwSL/eaOLWm6egvr5H+kHJ8kjZmEkly+59X5qPzAx10dXVK4tRXdONTVsr0dndL2V6/kBQSKjuumM6Jo5PVSYbRzTcdusUvPbGKdTW9ohjTTZIklPcfOMkLFqobjzC+HEp+NxnZ+OJpw8OkC0oxiUd6KuuHKdMNpLYfPXLC/Dwn/agro6yhfYbS5UXLciV8Q2qwGf3za8vwm9+twMNDb2hQerBkH6ZNiUDn79ntjLZWN77ta8swK9+s13Gv1AumpkUkYE4fqZy1usX7pkjRDFkERfj/KyOY6COa6pyZuknPzENjY292HegHq2tznN7jk4q96LKMSs8i3yeW7ZVy/rxGfoDAcm63X3XLIwrUTcXdPHCPCFTIuFTbW23MNxSj9DJ+sQtU5QFHwhj4QRYr/0UXO8+j0BzXahPhfe+yQzzgsthXrBKmWy8M++7dz7++Oe9Z/VvWMfpZbTarbeom0BA4qRvfm0hfvv7nair7wnZIoFQYHjGtAzc81l1Ok6Dhg8DbTyNNp5mWLjbO9Hw7lZ0HT4FndGI1MWzkLl6MYyxamjqB5NTcJbf8RMtYiyRAn7xoryoGJ7OiCUNJZKisCyzpDj5bHZpZEeYXMyeY1aGTIUkTnL1+6Tcl1FhlU5qGDSMTpxqxZ799UKKlZluk8ue5VWqwT1G8h+y2DIQQVKbRQvzJbuveqYlQabVHbtqZLxPQnyMzHhlZcFIOjQXq+M4n3H7zhox0q0xRpnfN2tWppS3qgYzSNt31KD8TLsYl7NmZGHe3BwhllENludxv3EuKMEsKoMiNEBVg20W1CEci0SdQicrnF0aSVzMnmM596FDTdh/IKTjOBt32ZJ85fOOwzqOI3QYLOTYMraLLF2UL0zs0aDjTpdRx9XIzE0SZnG/MRgWDTrO31gDz5HdwvSrsyXANHUujCVTRlS2i9VxTc292La9BrV13bBajeLYz5yRpXymMMEpCNS/JBrjSCSOQqKOUzFTeMyPp1n/c9ht6uzfnj4Xklb/45hbv48KzVHVHFUNfyfQ9pyG0YS23zSMNrQ9p2E0oe23jxeao/oR5ei7NB1V7eRp0KBBgwYNGjRo0KBBg4aogvr6Jg1RCybbz1R0oKKyEwa9DlOmpEupUrSAZTalpW0IIijlSSzxioYSJSLo7gZ6a4CgH7CmAXHZUSMbyx2PHGmS8j2So0yZnBZxFIYKOPt9OF7TCafbh5R4CybmJg47Gma0wfU6fLQJXZ0umRk5c2amDHiPBrDckTOGSczCPmjKFg1l8GGinZOlrTK3z2IxYOa0TJnnGy06rutoKXpOVUBvNCJlwQzE5mYiWsCS7tKy0DxX9mnn56svgx9Y7sjWC47gKCxIjJoSUaK/tQNtOw7A53LDVpyHlHnTlBHuDAbJlA4fbpL2FfZxT5+eERVl8OExZpStu7sfiUlWzJyeGRVl8ATJE4+fbkNbpxOxVjNmTk5DXKz6MviwjiMvBeeoc71mzMgc8TL4D1vSXVXdKYzh06akR0UZ/JhEmNRI5d9/CSI6NJCGqAMHzj/8xz04cjTk0BA0gFetLMI9n5kVcY7eaIGMjn/5637s3F17jsGWBB7su7jvS/OUEhYFg34E67cBbYcB/9lZs3oTYMsDCq6BzmxTemG9/uYpvPr6SekBJUMLnyN7aL/+1QXIzVHbJ7XrZAte31WNzl63kMfQrsxKjsVnLhuH4iy1ZS4HDjbInmtq7kOARB7QCWMiiWXYD6oSp8va8Mif9qKmtvscuyPJYz51x3SsvqxEqWwNjT34w5/2Sg+o9yyDLcljbrxuIm65cbJSx4bOzJH/8wDa9xyG3xXac2a7DbmfuBKTvvt5GMzq9Aj12p8f2yd9+K6wjos1CWv4vV+Yp4xJNOwwPPXMYWzcXHFuFAz73aZPy8DX7lugNAghwdU/v4CKv74MT0e3PFOD2YTE6RMw4/99B7ZCNazrYazfeAbPPX8UHR0u+Z095OQJ+MqX52OCQsI4Yu++ejz2xAHpKWfwl4R2JMj64ufnYs7sbKWyna7swKPPHUY9GWxFxQWRkmjFHTdMxvIF6sjsiLr6bjz08B4J6ocnESQmxuCWm6bgxusnKtVxJE186JE9OH6yBZ6zkwjY437l6hJ8+s6ZUdFDq0HDB0HbpRqGgMbu7x7aJUQedABzc+3y0umBt94uxbN/O6JUvr8+cRDvbaiQgdph2cwWA7ZsrcIjj+6NONNstBBs3A007wn9YkkJvQwWoPsMglVviiOrChs2VuDpZw8L2RPHSJDAIyHRgpOnWoRhNNL8wdHCsapOPLf5DHocHqQmxIiDmhRnQX2bA4++W4q27rNOvwJUVHTIeaCTylmNnA+akRGHlpY+/P7hXUKypAqtbQ7c/9sdwoCdkmKVs5CZaUNPjxuPPrZfjE+Vztb9v9spRlKCPQZ5OQnIybKLMffci8ewbsMZZbIFfD4c+tEv0bxhF4zxNsSV5EvmLajTofLxV1H+8HNQCTqpGzdVwmwyvK/jTAZ5j5+pxAsvHcMbb5+SnzmXlLLFxZmwZ28dHnhwZ8S5jaOF6r+9jdLfPiGj1OKKchE/rgDm5ES07z2KA9/7b3j7HMpk41nkmeTZ5BnluvHMsmKJZ5hnWWWwi7qMOo26jTouLT1OdB51X0VlhzLZmtscePCv+1HT0IOUJCtyM23ITI1Dd68bf33hCA6fJRtTlR3n3ck7lHcp79Ts7HgJ7j/1zCFs2FShtMrmNw/uEFIxW1zIjuN5pXX06uun8MprJ5TJpkHDh4HmqGoYArJMHjvWLGVJjL4xIsgXS1lokGzcXInOzlBEeLTB0hqymyYkWMT4DcvGLA1nge7f3yBGuwoEfa5QJpUZVJNNxvrIyxADmBOAvnqgp1qJbDQe33qnVC4pPtdwqS9LV1n+W13dJYEJFWBgYdORBrg8PnFSjWdlo2GekRSLtp5+7C5VZ4zQoWK5NA2QcCUB/+Tv3d1urHuvXJlsDM5wRiQNkDCLI6PkNIQZkJBnrihws2dfHSqrO5GdGX9uZAkzSKkpcTJq5Z01ZefNQh5NtO85go59R2HNzYTJbgvpEb0eMWnJMMRZUfPiGrg7upSV+zKTyqyM3W55X8fZLUhMtIhDyOy5CrAk9L0NZxBrNUnmlM+TsrGKhUEc3h1ktVUBv8eDqqdeByOq1qx06Ayhs2q0xSKuMFfKu5vX71QiG88gz6Lb7ZezGc5k8czSgeCsS55lVXhv/RnRZQN1HPUvf+/qcmHdenVBpW1769DS7kROhk1YawmuX0ZqrLSKrN1coUzH8c4kmznv0HAbCO9W3rEU6a13TisL3LC959SpNqn84fnkOeV5TU6ywhJjwNr3ys+b6avhIhC26VS+LkFojqqGIWA/Aw3ISOVl7M1j2WjZmXZlsrG3h0bbYNCpZllwWZmiDJezGfA5AGOEWX0Gc6hf1dGgQjI0NvVKdDwxIWbIZ7z0ec0zKqwCLo8flU29iI8xDSmTYvkZHdcTnCepCIcPN8pZGCxbyEA34dCRJmWGEkvzWVkQaQwNzwgzIczgqMCp06FZwpHaBLgPm1v7UN/Qo0Q2jtwKeL0wxg0tU7WkJMloru5jZcp0HMt94+OHlh7Hx1skU336dKhvdbRRXhHaT7wHBoPBCBrllF8F+ipq4apvhiV1aB+v3mxC0O9HxwE1WSSuGc+i3T70mfLsGk0GOcsqQN3FkWXUZZF0HEvOqQNV4eipFpgj6DgJ3tjMKKvqFIdVBU6dapUWmkgltAw0MQDBu1cFeA7ZOxupx5j6lwEIZvM1aIh2aD2qGiIiePYCi9xfcWlGdS4el/zEp0sGwagXInrPavRKpmFM40LnIVozEsGQ46UMOt0HqJEoXbcoQFTcAcNAGz75MYP9b3yp/PsvQVya/2oNF8TECSlSYuN0hUg8BqKrqx+JdoswPKrAhAmpUsbCMqXB6O3zSFR/4kRFpBSxmaGSX59z6Gd+D6AzCPuvCpCtmS8+v0i9LDRDpkxOVyKb1WxAcWY8+vq9QzKTzMj5/AFMVch2OmtWlmSxBsvG350OL2bPylJmZM6amQWfz3+ORGmgbMzijB+XHLH6YDQwaWKasIVHKu8lWVtGug052WpIshJnTYbebIavb+hZdbd1wpKaLAQ8KkD9xQx+T+9QHcdnys8mTVKj48aXJEs2NZIe4X3B7PnECWpkY49xbF6mZMMjlQWzFDh5zhQlsvEMkrQuUnWD6DhfADNnqGGbpu6aPStTdFkkHedy+jB7ZhZUYebkdNEhEXVcnwcTipMRq4iZePKkdLk7eYcOBjOW2Vl2ZZMSaCux2qa/P7Idx9L9kqJkJbJp0PBhoDmqGiIq3+nTMtHW6pQeBl4IvCTYl0pj5PLLipVRr5MEaNmSAjHi2C9F2UJGeb+MDSEDK8fUqIDOGAOkziRTC+DtA4KBUEjT1w94ugFbLmAvUCIbS5Ouv26ilE+1tDqkJIhgqXRjQx8KC5OwaGGeMkPpspnZsJqNaO3uF8eUcHv9aO5yIi0hBgsmqXGiiStXj5O+nvr6Xng8IYOEfzY09IrRTgZFVVi+rBBZWXbU1/ecY+em0csy75gYE667ZoIyJ3rhvFwUFyWjoalX9AbPKfddmDTmuqsnKGMP5xgavlwNzfB294b0SCCA/pZ2+J0u5N9+NcxJaliwSWTDs9jddb6O488M0C1elK+Modtu534fJ3uto9Ml9wJlIxFba4sD06amY+oUNWeVLM2Fn71JHAdXQwsCfr/IxmCEs6oeCVPHIXP1YiWy8QzecN1EOZMsBw33LXId6+p6kJUVL2dZFfhMqcs4QsrjPV/HkfvhyivU6bhl8/OQnhKH+uY+uD0DdFyrA7ExJly9okiZjuM55d3JO5R3KUEdxztWr+edO0EZsy5HC02ZlIbmFoecz7Ad19HhlGd79ZXjpV1Kg4Zohy6okiI1StDT04OEhAR0d3fDbv94IvyBQAAtLS1IT08XhTXWQKPoj4/uw6HDDXC53qc1v2xlMT5710y142n6vXj8yYPYvqNG+lUJZhkWzM/Flz4/V/14moYdQNshgORKhN4MxOdDV3AVdMy4jhA+aM+FCT1eee2kRFT5O5/jhPEpMlaChBAqsae0Ba/trEYHx9MEIdm47JRY3HXZOBQpnt97+EgjHn3sABqaehDwB8XhZ6T885+bg7mKRzdwLMIjf9qDquouMeBotKWlxuJTd8yQcVIjhYvRcTTK//DnvcKMLE6+LtQfddP1k2REjdLxNG0dOPqvD6Jt50FxTmU8TYId+bdfg4nfvht6kymqRnDFxpqxeFEevvi5OefIqVSA2aNnnzsizOt9Z8lYZHbk9EwZszKSQcyL0XEcTVPxl5ckM87CTL3FgsSZEzHzP76DuHy1Z3XT5ko89/wRtLY5RVY6MZxB+5UvL5CMq0rsP9iAvz5+QHoq6dDoDTpkZ9rxpS/MwcwZ6jKqRHlVJx7922HUNfaKI0i9kZpkxZ03TsaSeblKdRz77P/wyB7Rccz8Ujb2p9568xRcf626QCHB5MIf/rgHx443nwtkss/96ivH4Y5PTh91J3okbO3RlLtz6/2w29SN3+rpcyFp+XfH3Pp9VGiOquaoDgtuDTLoVlR0wmDUYerk6BoUTQbgU6fbRM7x41JlHl20IOjpAXprQllVazoQmzHiF9bF7jkGIWQ+7tkxNcygRyLjUQGX24eTtV1w9PuQHG/BxNyEcyzAqkEW3SNHm4UBmIYII9Zhpl3VoINKtlVG8unQzJqROeIBm4vdbzR6S0+3ob6xBxZzaN5mJFIvFZBM5Yly9Jw8A73JiOT50xGbnYFoAWc0cu0IltSqnnU8EK2tDhw/0SJ7r7goCUVFSVGj41j+27bzEPz9btiKcpE0Z6raHtABYHCVBGxOpwfpaXGYOiUjauZZUscdPtoUKg1NtGLG9Izo0nFlbWjrcMIWZ8b0iWnCPh0tOo5khA2NvYixhII2kQjHlM0WruhAVVWX7LOpU9ORlhqB8HEUoDmqH1GOPs1RvWShOaoa/h6g7TkNowltv2kYbWh7TsNoQttvHy80R/UjytF3aTqq0REq06BBgwYNGjRo0KBBg4ZohMb6qwSao6phWPS3dqDhnS3oPHRS+rVSF89C1pVLYIyLVS2akDzt3FUrpWfE5IlpWLI4T8g+VIO9ILv31uHIkUa4PX6MK0nBsqUFSE1Rv27soTlwqBH7D9RLCVpBfhKWLclX3p9KkMwmUHUS/tIDCDp6oEvOgHHKfOgz1RBQnSdbMIjTZzqwa08tWtudSE2OxcJ5OZg0ITUqSgqrqjuxfUe1kKEk2GOkX5tlexw+rxqOmgY0vLUJPaWVMNpikb5yAdJXzhfyG9UgIdD2XTXSX8ZZljOnZWDB3BylPaBh9Pa6sWNnDU5wVqOwclPH5SPepobFeXAP7Z59dTh8pAleX0BY4JctzkdycnTouP0HGrD/YL0w1hYUJIr+VcW+OhCBYBA1HU6caXPA5fUjyWrChIx4pEXBM6WOO3mqFbv21KGtzYG0tDgsWZQvHAbRoONYnt+0dguc1fUwpyQi/fIlSFk4C7ooyHT2VdWh4e3N6D1dBWN8HDJWLUT6inlK+9zD6Pf50eTwoNvjg0GnQ3KMCRmxZuGA0KBhLEAr/dVKfyOi8/ApHPzh/8BR0ygXQTBIAgO9jGyY+5t/hjVLHQsryQt+df82IY85N4wxCOnf+t53lqCwIEkpecGvH9guxmUwwDm0NE4gvUjf+voiTJs6cv1vH7TnaFz+9sGd2HegHj5fEAaDTogpEhOtuPeL87B0cT5UIej3w/vuk/Ad3QH4PIDeQPpE6KxxMK28Fcb5V6iTLRjE868cx5vvnoar3yckI35/ENYYI669cjzu+uQ0pYbcmrVlePrZwxK8oWPqDwRhMuqxYnkhvnLv/BEjPrsoMqX3duDIv/4O7tYO6Ix8pkHojHqkLZ+H2T//IUzx6nreT5a24ncP7xZWTPZoh+dG0zD/3jcXI0Wh01VT241fP7ANNTXd5+m4/PwEfP87y4QZWBXaO5z41W+243QZ+QFC4lHHZaTH4dvfWIzJk9JG7O/+oD3H4NtvfrcTBw83COmZTq+TP5MSY/CV+xZg4fyRI975IPgCAWwobcWp5l45o1QZXL8Ykx5Li1MxQ2H/Mfc+dchb75yWcSaiR6jjrEbcdMMk3Hn7dKU6rvqZ11D+4JPw9vRCbzQKo7PBYkb2Tasx+Z+/Ie+p0nEN727Bsf94SMi7dEaj3GV6owHpqxZi9v/8QGlgv6Pfi2NtfXAJyV5ov/EpJpH8LC0ellEOZI750t8dv1Vf+rvk22Nu/T4qxp4HpWHE4XO6cPhf7oeztklm09lK8hE/rhDW3EzJrh77z4eHzFsbLZC04OE/7kFlVZdQ+tNg44s/19Z146GH95yj/leBx586iKPHm5GeFou8vATk5iYgN8eOtjYnHnp4t9DEq8LLrx6XaDnnp9Hgzcmxi4w07v78l33C9qgK/oOb4Du0BbqYWOjTcqFPyYIuLYcPHN5NL8Nfc1qZbHsONODVt0phMOrlWeZk2ZGXYxcH8M13S7Fzb50y2egsPPnMIXj9AXmWfKYkFSOz48ZNlVizrkyZbM76Zhz5twdl/IttXAFsxfmwjS+AJT0VzRt2oezh59TJ5vTi93/ai5ZWpzxTvqhHGFA6WdqGvz59SJlsDB5Rx1VXdw/RcdXVXcIwGh4vpQJ/feKAZN7S02wiV1jHkcjr94/sPsdSrAIvvHQM+/bXITnZGtK/ouPs6HN48Mif96KlpU+ZbEfre3CiqUcc0xSbGclxfJngDwax7UwbmnqGzqYdLezYVYvX3zwFs9lwTo9w3Ui+Q5b4PfvqlcnWeeAYyn73uFTc2MYVIq4oT+wRkz0edS+vQd1L7yqTzVFdj2P/8Qd4ex1ndVwe4scXwpKWIkG68j+9oEw23gnH2/rQ7wsgzqRHnMkAm9mAGKMeHf0+nO6IMO9dg4YohOaoahiCls170FdRC2t+lgxJD4MRTBqZbbsOobe8Wolsp0pbpUwvNS32PKZE/pyeHiclkEePNSuRram5V0rOyGhqNr8f4WW2JjPThuaWPimXUwE6o5u2VMkYn4FljYySUzayPHLcjwrQAKGTyiyqzvp+hk0i+PZkBN1O+I/vhCps2lopwQ+yYIazCjKCICFGsg4bt1Qqk23L1io4HF4ZRzMw48FRUkajTkaIqArcNL67Be7WdsTmZ59XnmeMs4qRWf/mRni61QRH9h1sQHNzH7IybecxXtNQZ/aNrKys3FCBEydbhaUzLX2ojmM5Jj/jd1Sgrr5H1obBLq7VYB3HcUT79qtxalhRsHV7tbBdW2PO13EZGTZ0drjEIVMBZlCPNnbLOsUMqHCgbPEWI9y+AE4qDBRu2HhGgsBkMx+o4/icOY5o48YKZbI1vLleZuGyimugjjMlxEOnN4ijyjtEiWzvbIG7rQOxeVnn6zhbrLzqXnsP3l41wZEWl0cyqVaT/rx1Y8mv2aBDq8sD59mZuRo0RDM0R1XDEDiqG6RGJFIPmSnBJpeGo0qNMcKsH+cxsuxyMDjLj5kGVZnBxsbQ0O9IQ7RDBqdO6OtVoLXNgd4+D2y2oT0zvMR4xzIjrQT9TgS72yWbOhhywZosCDSpcaKJqpouKYGLBI5HqKrpVlZhwPJ3s/l8QySMOJsZ7e1OMeBVoPdMrZA/ROohox7xdvfBWdekRLaGpl4EEZpjORgcfcHZ0Y1NagxM6i86BwOdrTAYZGIPpjId19QrGVOu0WCYWNodDH1HBVpaHFKxEkn/0kHkEamvVxN86Pf60ef2STZrMHh22YbRpuichsfQWWNNw+q4ymrOpFWD3lMVMMRE5p6gHnE1tMDbo+as9pbXSDA/oo6zx8Pb1QtXo5qgktMbkNnQ+gh3g1Gvgy8QhNOnOaofClxL1a9LEJqjqmEImPHg5RUpShnweGXeIL+jAjExJjmrzGQNkS0QDPX8RHBiR0c2o/T2eL1D143rSfkiOdijARq9RoMuomwEH3XcMIbKiMNkBgxGwB8aSD4Efh90MWrmvhGcS0rCmEjg+8xSq+rfosPAfuNI8HkD4ohxdqkKmOLjQs2LEaBaj/AccsRxpAADHUHOjVapR2hhRirvDb+nSjauG0vguUaDIXeGYtm434fTcXzUPKsqQN1LIhtmViOBd4NF0SxV6i5moSM9U4LrqexuOJs5DXgjt8wE3F7ozUYYYtSQUZnsccNmc6njdKLj1PSohsmSIuk4bkNeWcZL1PHRMLagOaoahoBEJ7wcSA4wEFR4/Y0tUuaSPGeqEtnIZEpmSRJ6DAbf45Dt2TOzlMg2blwycrLjJYs1+HLo7nGLkTRndrYS2VgWTZKTzs7+IbIxQ2Iy6TF3bo4S2XQmMwwT5yLo6hty6QdpoDC7P2keVGHpwjzJ4g8uoeXvZHherpCEauHCEDmM2+0bYvj29LixYF6OMuNcmH1jzEOyHaJHmtuROGMi4grU7Lk5M7NkXbq63UNka2t3IjszHhPHpyiRbeb0TCm5bO9wDfmsvd0ln/E7KjBxQqqsDddosB7p6u5XquPYw0v24Y4O1xDZ2PrAUuU5c9TIZjEaUJwaB5c3IMy/g3sJiXHp6ojFli0pgLt/qI5jZp+6b9nSQmWyZVy5TO4Af797KAFfdy8yrliqzFEluy8DbuxRPU+2YFAI5GgnWbPVEE+mWk2SOfUOCo6IbP4AbCYD7BZt8IeG6IfmqGoYAlthLgrvugF+h1PKgKmEeSE4Kmqgt5gx4RufUXYxcDTDJ2+dKhkH9pDRAOGroaFXMki33DRZDDkVYOnbnXfMkPK8uroe9PW5xQlsau6Do8+Dyy8rRmFBorKo+Sc/MU1IRsgkSieGZcqtrQ4xOsmGqcr4JYwLroQuJRPBtnoZTRN09yPY04FgZzMMBZNgmDJfmWyXryjC+KJkNDT2CKsz162zy4X6xh4UFyZi9coiZbItXpgvwZvm5tBzpGzd3f2orQ0R8dxw/SRlsqUsnImsq5ehv6VdiJXYMuDp7EZfWTUsaUmY8PW7lGWiSQJ05eXFcDg9cj55Tlk2WlffC4vFiDs+MXXE2JI/CAy23XrzFGGrHajj+DMDEPyM31EBrskdn5wmWVP2q3LNQjouVBJ81RXjlDEScy9RNvZZkjW5pzek40jyROd18aI8TJuijq1+Tl4SEq0mdDi8cHr88PgC6O33ocflQ35SLManqXNUr1xdgqKiJCmNDus4jm5qqO/F+HHJcnepQubVK5A8fyactQ3ob26Dz+GEu70TfWdqEFeYi4K7blYmW+qS2ci8Ygn6m1rhahio46oQk5mC8V/9lDIdF28yIMdmgdcflF5UlvoyKOJgpY1Oh5LE2IhlwRouANVlv7pL83lp42m08TQRwcxW9fPvoPqZN8TIZA+GfWIRir9wGzJXL1YrG1kSt1fjzbdLxVii15qVZcd110zAZSuLlM98O3CwAa++fhIVlR1iWKakxOLqK8fhmqsmROyJ+7hwMXuu9HQbXn71BE6cbJEyQs6d5ZrRwaeBrhKB1gZ4d7yJQPlRwO8FLFYYpi6EackN0MWqM+KItg4nXn7jJHbvrYfb45Ny2gVzs/GJGycjLVVdWTLBHtRXXj2BrduqxaExmvSYPSsbt90yBfn5iUr3G7MgFX99GbUvr4W7vUv6uZLnTsW4L9+hrCrjnGz+ANasP4N168/IbFyqjaKCJNx8w0TMnaUm8zZQx5Eoizou3NeezcDDdRNl7JBqHccRV6+9cUp6Gykrz8BVV47D1VeMG9HZvRez58hITIbzU6Vt8ozp1K++rBg3Xj9JuY5jH+qe6k5UdzilDJjlvpMy4jG/IOk8kiUVYNDyxZePYQ91nNsHS4wRixbk4bZPTFU+A5zOX8Wjz6PpnU3iDOrNJqQum4fiL39amHZV7jefqx9nHn0Rda+sg6erR0bUpMybhnH33YmkmeoChQSz97W9/ajrdaPfH5DRNPFmI4oSYpBqHf051mN+PM2uB9WPp1n0zTG3fh8VmqOqOaoXhN/jgauuWWYgCrNdFEV0aISQuZNIT7eNqBP4YcFjRZZfllJlpNtGJTtzsXsuXN7IstXU1NiIpC0qEeztQrDfAZ0tUeaoRhOYpenu6Yc93oIEu5qs1nCgk8osCPvJWB4/0vgwOo4jr/qb2qCPMQ9h71QN9uY1tzpgNOhlFmg0yUb9ER6pEp06znFWx8VFn45rc0pQKTUlTlnf7HAgsRKZfm0Wg5QFRxNYkcFWlcQEiwQyowlsI3C3tEtrkiUtObp0nMMJV1MbjLExSufMRwKDIi5/QEoorcbI5HujgTHvqO55SL2juuDrY279PiqiS3triDqQ+XckI5YfBYzcZ2dH52GVsS8Z8YhW2VRnAS8EXXyivKIRdFD5ikaQEIWvaIQx1hq1eoQOVm6U6hE6ptGt49RWOlxQx6VFr46zWYywRacakQy0qtLyD4LJbpNXNIKkSfEl6vgKPohYyaaProCIBg0Xi+gJz2qIWpBMgaQK0QhmQ4ZjK1QNZhoGk9xEm2zRWFDBcun+KJVNiID6o1u2SGyxqiEEHm6fspmuHwRNx/3v4PMHhmXEjgbZmFGNxrPKMxrteiRaZfO7+pXNTR3LOi7Q70bQF532iAYNF4KWUdUwLPYfbMC7a8pQXt7OcYjC5njNVeMxrkQNG+ZAsMdyzbpyHDkamsM4ZXK6yDZ9WoZq0VBV3Yl31pTJ4HsaJMVFydKjumB+rvKyQpKe8Jnu2FUjxnl2lh1XXlGClcuLZNagSpA1dN2WSmzdXQOny4fUZCtWLSnA6mWFyohtwiC5yJp1Zdi4qVJK4+x2C1atLJbnqjqLSSdm/YYKrN94RublcoTTimUFuPbqCcqIxQYGHbZuq5I+0Lr6bnmOixfm4dprJiArM165Ybl3fz3WvlcuOo6lffPmZOOaq8fLmVWN4yeaRY8cP9Eiv0+bGtJxU6eo13F1bQ5sPd6E49Vd0gdXkGbD0ikZmFqQqFzHNbb0Ye2mCuw6UC9OQ05mPC5fVohlC/KU6zgSFb2z5jS2bKtGf79XKltWX1aC1ZcXK9dxbB3gnbppc4WQ7TGretmqIlx95XghCFQJv9uDxlffRdMba+FuaoUhLhYZ161GzievhzklSa1s/gA2b63Ce+vPCOGe2WzEkkV5on8zFFccUMd1b9+JtjfehvN0OeckIWHxQqTdfANiS9QRZI1ZqCY00kVPW8poQutR1XpUI4JG+eNPHpTIqi3eLDMHSdqSnGTFd7+9VKlDuHtvHX7/h93o7XUjPj7kJPT2esRh+Mq987B8mToqfZIV/fL+bWhrc4g8LE8m+y+NkLs+NRM33TBy5AoftOfIGvpfP9+CurpukY2kOzROdNDh+msn4HN3z1ZmZNJJ/cUfdqGsogMxFqOMknC6vOLoLJmfi69/bq6y/jw6qb/8zXYcPNggz9FqNcq5cHv8wpT8j99frmwEDA3xhx7eLYaS3qCT/lTK1e/ySkDpx/+4YsT6VT9ov/FqefKZw3jjrVPyHG02szBzc8/l5Njxox+sQH6eGoZY4q13SvHUM4fh8fhENsrY2+dBSnIsvv+dpRL8UgUGkv7wyB5h1R2o4+JtZnztKwuweJG6EsPyxh48/l4ZuhwexFmMojOcbh9MRj1uWpiPFdNGjj38g/ZcbUMPfvmHXWho7kMcx3MY9ehzeMRBveGKcfj0rVOV6biODqfo3/Iz7YixmmAxG+BwhnTcymWF+PpXFyrTcTyTv/j1Nhw+0gSLxSA9vS6XT4Jgs2dn4wffWarMWQ14vTj1r79Gy9rNQsZmsMVJdtDvdMI+YzKm/+qnMKckK9Nxjz1xAO+8WwZOEea96vP65bnm5ybgRz9cgRyFpfvNL7yMxr88ITNdDfE2Genj7+2DOSMNRT/9Z9imTh5VecZ8j+reP6jvUZ3/tTG3fh8VY8+D0jAqF+pzzx8VJZyXl4CkRKuMNaFR2dXVjyeeOqisvIUOAv9+p8uDvDy7ZIz44s80OJ969rBcuirA9Xri6YNCVMR1I9svRyXk5iaIw/riy8clo6kKL7x0TJzU3Fy7kCglJsTIJRoXZ5JIOtkyVeGdjWdwuqJDet7SUmORYLcgK8OGpMQY7NxXjz2HGpTJtnFzJQ4dapBZtJmZNsk0MFJOAhlm9JnJVAVm7bdur0ZSUoywwlK29LQ46WssK2/H62+eUiZbWVk73l1zGrFWE3Jz7LLfuO94NjgG4/kXjyplOH3+xWMSoOb5TBQdFys6jlmvx586KA6EqsDIk08fEl03WMe5qP+ePiTfUUXK8vquGnQ7PMhKsiIhzgx7rAmZSVZhFH13Xx06es+fdzmaePGNk2ho6kVuVjxSkq2iR3Ky4iW4tGZTBcqrzp8NPprgWeSZ5NnkGeVZ5Zmljtu6oxr7D9Qrk23DpgrRZRkZcaLbKBt1HXXeoUON2LSlUplsbRt3oGXdFpjTUmDNz4E5OREx2Rmw5uei58hJ1D33ujLZeGeue++M3KG8S0M6Lk70HUck8c5VBXd9A5qe/hvJPBBTkAdTchLMaamIKcyHp6UNDX96LCrLuzVoGAzNUdUwBHv31Ut5IxVuJIIKKuDTZW1KZDtyrEmYfnnRD4yMh2Wjk3jwUKMS2c5UdKCiolOo/AdH7VNSrJKRJvW/quADx+bQcR48PoJlrCxD27O3TolsjNpv212LWKtRsjIDQSeHlymdVVXYsrVSoumDR1vwd2almc1UhR07a6T0bHD5MTMzcTYztu+sUdYnvWdfnWRlEhLOZ41hdouONbM3YUZbFbKxIoPBpIHguaUzXV3dJU6FChw63IiWFgfS0mMj6LhYmQvKtVOBmtY+1Lc7kGSzDNFxiTazsNkeq1bjDLa0OXD0VKs4C4NLfBPiLeLk71N0NzDowLPIMzk4a8qz6/cFsH1XDVRh0+ZK0WWRdBzXkqOSVIFOajAYgNF2vj3CETXMrja/sxEBj5rg9K49dfJseYcOBO/YhMQYHDjYKHevCnRt3wlfdw9Mqee3anHUoDk9Fc7TZXCVlSuRbayCOk/161KE5qhqiDiCg4jUz8OyIPY2sodFBXp73JLpiNTPIwZA8H35RxtcEzpdXKPBECUj3+lXI1uvGx5vIOIcQcrGZ93ZpUY2GpC87DmbNBJMJr2MXVEF/t2RninB9WQGTlVkur3DOWxvW4zFIOvqdKrJvnE/hVp6IukRo5wVVWeVTurwOs4Ij1edjuOasIzQFGFsCZ+19J0p0iMOlw8+fxBm01DTQX9Wx/UpyvaybNtzIf2rA7oUrZvT6ZGzyDMZCXyu7W1qHBrup84u6rjI+pfr2d6hTv+6W9qgN0emSDbEWOB3uuBzqJGvq9slOiSSjuOz9ijUcXRSZd9HKFnWx8SIc8/vaNAQ7dAcVQ1DIFkGXaj/bTCYIeEgcJYCq5KN0cpIWSI60LwvWPKlAsyk8rKPVJYXLiMcnMEZLbB82xpjlL7PSIYK5UtPUyMb+zvjJeMR2cDlc2UZsCqQ9IfOdCT0u3xSrqwq0snSQRpDkUBCKvY0sv9SBVj1QP89khPPfcizwn5QFQj37UZiSOb5ZZ80qyBU6RE6fWxlGAy+x1ET/I4KMGtqNurR7/FHLAtmoDBR0dwV8ifEmA1yRw0G9yC3YZqidaN+Y+aUZzISGLThWVY5So26LBLoYGdlqtO/1rwcBNyRnT06qKYkO4zxakYRZaTFSR9rZB3nkzuX+1IFWObLTc++1MHwO5zQW2KGZFs1fADIKqr6dQni0vxXa7gg5s/LkSHpLD8bqIDpzHCI+viSZIwfp0bBTZ2aLj1ulG1gDxnl5Hu87GfNzFIiG+WaPDlNos8DDWDK1tzSJ31wC+fnKpGNPUeLFuZJRnrwqIv2dhfi4izKCFqMBj1WLc4Xh5RjaQaC0WhmlpYuULNuxMoVRWDibXDvs8PpkWd72Up17InLlhYIKQvJqAaCgRwamGQmVsUmSnZfOsosxx8IyaR2u+UsqGIl5t9NJ7l5kI7jueV5mDA+FUWFathEZ0zPlD7ywfo3rONychLkOyqQkxKLwox4IVIarH/be91IsJkxXdG6JSXEYN6sbPT0eoaMzGnvdMEWZ8aC2TlKZOMZvGxlUYiEbZCO49k1WwxyllWB7L58htRpAyFkezpg1coiZbJlXLNKynw9nV3nvc9MasDdj8wbroTeqGaABe9MBiAGZ5yp41i1wTvXblczkzZh2RIYU5LhaWo+X4/4/fC2t8M2cxpiCtXtOQ0aLhbaeBoNQxBvs+BLX5grrJO1tT2IiTGAY8sYzec4ky9+fq6yDBKdli9/cR5+87sdqK3rCZVS6QB3v0+ylV/64rxhS5hGGlyTz989G60tDunjNZv10tvI/k9G1L9wz+wRY2C9GNz+iamorOoUZmI6hyypZaaQUd9P3TFd6UiOqy8rxqnydhw63iwsxFy7frdfyrmvXlWEWVPVsUwvX1YgI0LYp9XZ2S+lcG53KHtP43LlCnUs03RYbrhuIt54qxS1Pd3C1skMK52I2TOzhM1ZFfLzE3HnHdPxzHNHUFPTjRirUao02I83cUIq7rh9ujLZGLj5wufn4JE/7ZWzyjPANSNjcl5uAr74uTnqdJwprON2imzM7hIM4nCcyZe/OFdZ8IFr8oklBfjLOjeaOl0wG/TQ6XVwewOItxrls3iFo0xuv2ESauq7caaqUypvqD+4brExJtxx02Tk56hjyuQ5pe49dKRRykXNplBpPp/ljddPVBZ8IFatKBIdt217NTo73tdxlHPF8iKlTnTy0vnI+eQNqH/hDTg6u2GwWsVBZfY+ZdlC5NxxozLZeGfe8cnpeO75I+f0iNcbkIDX5IlpuOO2acpkM6emIPdrX0btbx9Cf2U1DLFWcVJZ8mstLETuV++9ZHseNYwtaONptPE0w6KiskPmRh473ixOzfx5uXKhkbRINRoaerBxS6UQBBEcE3LZqmIxMlWjvd0pLIkkJ6LhO3lSGi5fVTziWeiL2XMkdNq8pQo7d9VItJwXLaPlHDek+tKiQbljb528Onv6Zf7h8oV5mDczS7lsdLB27a7Flm1Vkh1nWeuypYVYsjgvYi/haIIqfN/+BmzeWimsznTCli4pwPKlBSM6UuJidRz1B8laOJYjNtaMxYvysHJ5obJMw2ACtA0bK2QuMx2GBfNzJINOh1A1OE6KstGxIRh4oI5TOe4ijE4Sw51uw7GqDulZLcmOx8IJachLG9kS0YvZc6zC2Lq7FrsP1Ev5ZUlhIlYsysfUiWlQDZaVk6V7+45qISwk4zRnWM+bm61cxzELuGNXLbZtqxJiqox0G1YsK5SsoKqxOWEEAwG0b9mNprfXw1lVC3NqMtKvWomMq1fBYI1Rut+of48eC+k42kzMsDLTygAmg/6q4ThVivZ330Pf0WPQWyxIXLYYyVddIY7saGOsj6fpOvBH2OPVJRt6ep1InHPfmFu/jwrNUdUcVQ1/J9D2nIbRhLbfNIw2tD2nYTSh7bePF5qj+hHl6L00HVXt5GnQoEGDBg0aNGjQoEGDhqiC1qOq4YLodXpQ1+aQXpWC9HhhVYwWkPihsqpLRjkU5ScpYzeNBBJmsNSRJaP5eYnKSGMiwefzo7yiU3pnST6VPsLlen8vYPFJTV23jF3hrMaCvATl5XoD0dDYI+RAJI0pKU6OOHpFFZwNzXBU1kmZXuL0CdCb1PUxDgZHc9TUdEl5Y0lxivT5Rgv6HB5UVobmkhYVJcmzjRawv/JMxVkdl58ozOIaPhihIjYSjJHUzgydTn0J/EDZqmu7heApGnVcXUOPkLPxHBQXJkWXjqtvhqOqTnpBRccpIniKhB6nR3rKDQYd8lJtwtyt4X+B0Lw1tX//JQilJ+kPf/iDvKqqQsOkp06dip/+9Ke49tpr5fdVq1Zh8+bN5/03X/nKV/Dwww+f+72mpgZf+9rXsHHjRthsNnzuc5/Df/3Xf8EYRUpiLIKELG/uqcWuk83o6/cKwU1yvAWXz8rGqhlqewZJVPDGu6exZn25kNvw2k9MsOCKVcW49YbJSvtpeNGv31CBV18/gZbWEDMx+1SWLy/Ap++YMaI9gxeD3Xvr8PzLx6THl71lsVYT5s/Nwd13zURCFPQMRiuqa7vw5N+OoLS8XdiJSYQyYVwKPnvHdBQVqGE5DaOlpQ9PPH0Ih482yYgJo0mPwoJE3HXnDEyfpo6ghfB0duPkrx5D03s74O11QG80IK4wB+O/fheyr16u3NEiCcrmrVXC0Emjl72pN904CVddMU6pjqPz99pbJ7H2vTPo6goxipI1/KorSnDz9ep13Np15Xj9zVNobQvpOHu8BSuWF+JTd8yIKkc/2hAM9iIA8irwmfLm4uxvO/Rgj6rafsbK6k488dwRlJ0J6TiyiZP9+u47Z6AwP1GpbE3Ucc8exrGTLecIqCjTXbdPx9RJanuP3e2dOPnLv6B5wy54+5whHVeUi4nf/Cwyr1iiVLZ+rx/vHqjHgYp2ON0+GXuVbDPjsulZWDA+NaqCEBo0DAelN0pubi7++7//G+PHj5fL7/HHH8fNN9+MgwcPitNKfPnLX8a///u/n/tvYmPfrw/3+/24/vrrkZmZiR07dqCxsRH33HMPTCYTfvaznyn5N/09gM/ib5srsO1EE2LNRqTEx8gMuq4+D17cVik/02FVhZffOIkXXj0Bs9mA1NTQfujuceP5V04Ii+09n5qpTDY6qX/6yz4xQjhrlsyTJPd4481SdHX147vfXqLscth/sAG/f2S3rFFqcqw4NI4+DzZsrkBbuwM//sEKZYzJ0QySJ/3ywZ1obOqVmXjJiTFiLB0+1iyf/csPliMrQ80MRJJj/fI321FW3i5Ze2ZBSOB1uqwd9/92J370g+XCsKsCfo8HB3/4c7Rs3QdzciJi87MQ8PjQW16DIz/5jWQcMlcvVqbjHvnzHmzaUgVbnAkZGTbpR2O25tHH9osPcfVV46EKz790DK+8cVKchTCxE7Ncz71wDO5+vwQhVGHN2nL85fH9osc4a5a9ez09/XjtzVPo6XHjW99YpBnAERAMOhAAg/IcT8OApf5sVrUTAbihDzI4okb/NjT14pe/24mm5j6ZQ049x3vi0JEmCYT95B9XIENR5U13Tz9++dsdqKzuQlJSzDkdV1rWht88tAs/+u5SjFPEWO/vd+PA9/8HbTsPwpySdFbHedFbVoVD//xrzDGbkL5ivjId9+L2KnFSYy0GpMabwal5JEJ7eWe1fIcEaBo+BJjBV5nF11+aelVp/v/GG2/EddddJ47qhAkT8J//+Z+SFd21a9d5jikd0fBrYAPx2rVrceLECTz11FOYNWuWZGL/4z/+A7///e/h8Zw/D0zDxYOlvvvK2mC3mmV4u4wyMeqRmhADg06H9w7WS3ROBTo6XXj3vXKJ2tOAY2SVr9SUWMTFmbBhS6VEX1WV+zKTSis3MzNenD5mPsS5SbZiz746nCptU3ZpvfrmSThdXuRkxcv68bmSITYz3YbjJ1vFkdUwFOu3VIqTmpttl5IzBh/I7JibHS97bf3mSmWycZwEmWuzs+Mlq0XZmCXPzbFLSeub75Qqk611yz607ToEa24WLKlJ4pgaY2Mk2+BzOHHmzy8Im6cK0LHftbtODF+OjOI5NZuNyMy0SWb11TdOCkOrCrS1ObB2fbk8x4E6jj/zvbUbyuU7KsA14dpwjbhWXDPRccmxSEqMwc7dtSg/06FEtmhHAC3k1gXAzClbaGh0Gs/+7kQQoRJvFVi3sUKcVOoN6jbqEeo6/t7Q1IcNKnXczhpU1XRJmwqrkwbqONoDb68tUyZb86bdaN9zBLF5WbCkJJ7VcVbEFeXB1+tA+Z+eP2+G6WiisqUPx2o6kRBrQkKsGQa9Xkp+U6VyKoiNRxvhGTRvWIOGaETUFKozO/rcc8/B4XBg8eL3o+xPP/00UlNTMW3aNPz4xz+G0/n+8PidO3di+vTpyMh4f8bi1VdfLQxdx48fH/V/w98LTtZ2we31IS5CCVdCnFkichWNPUpkO36qRTKUkfqhWLrKnq7jJ2kQqDF+We5Lp3Qw6ER73H4cOdqkRDY6WlVVnUhOtA7JdtChDvgDUjqqYSj27K8Xx35wPxR/Zyn3rn11ymTbf6BB5Bg8IofPmOeBYxOYdVWB1p2HEPD6xTkdLJslPRU9pyvRe6ZGiWxcF45DitTzmZRsFUfwdJmaoNKxEy2ix5g5Ggy+19frke+oANekvT2yjiNHACsNVOm4aEYwyMBu71nHVBfBDNMhiG5FsgWxZ38drNbIOo66b9e+eqjC3oMNMBhDM3EH65H4eLPcW6qCSq3bD0iwbfCIHNFxacnoPnkGjmo1a1fe0COOqDUCrwgd144+D2rb1AT1NWj4MFBe53f06FFxTPv7+yWb+sorr2DKlCny2V133YWCggJkZ2fjyJEj+NGPfoTS0lK8/PLL8nlTU9N5TioR/p2fDQe32y2vMOjYEiz94uvjAP93eAF8XP97o90fxesq5M+cHw0UhvZgEF7fx7dWH3bWm8ihCw697vmeDtJfo0I2/r185iQsoNkRqQefREYjJduF9hzXTWQzhkyiwaBB4vH4xuR+HWn46GwZZOMP+cxo0MkzZaBNRbmj2+OLuN9ENqNOzqnXy+f68fdGf5CO87vdCBp0CEZaF6MBgWAQfo9XyZ7jM6MuC9nl568dfX4mQdTpEZ/otuF0HN9UdVZlTYKAwTBUj/Ax6w3qdFw0Ixj0IyDrxSc6XIYtAJ1u9P9dXE+vzy+O4HB6xCc6RM2ac69Tzw4nW8AflPvNYjEo0HEeBA36YXScUf5bnyId5/VzPwWHteMCwYA899GUbayd2yHQ6UMvlX//JQjljurEiRNx6NAhmQv04osvChkSCZTorN53333nvsfMaVZWFlavXo0zZ86gpKTkf/13kmzp3/7t34a839raKg7zx3Ug+W+iohpr87eSLW6kx3oRC+dZA/19uNw+5NqDiNU50NIy+uW/yQkBFOYZYTZ7YR2U8WXfSkGOEalJodlno414mxclxRYEA17EDWIg9vkDyA0akZWpHzHZLrTn9Do/xpUwG+NGYsL56yYOQ0CP4kKzknWLdsycZsPxU61IThjqjAb8Pkwanyi6QwWmTo5DVxf7U2mUDDaWvMjMiEe/qwctbmZzPl58kI7TTSsGjufDm5kMHT2YAfC0d8JYlAlnnBluBXsuM0OHvGwj7PaQgT4QDocXJUUW2O0+JechNSWIgnwjYiw+xAzqGWcWuDDfKN9RIZs93odxRRZA55US0cEBzjwYZW1V6LhoBuUNStkvA+SDs/jBcyXBep0a/Tt7WjxKy9qRnHi+IyFOmt+PqZOSld0NUyfFweHoQlIE/cu7li0ZDkcnnE7dqO83/czxQNmZiDrO3dYJ8/gcOGOM6FewdkkmF1JMHlgCARgHZcqdXh+yrDqY/H1oaRm9ipve3o//HtLw9w/ljqrZbMa4cePk57lz52Lv3r144IEH8Mgjjwz57sKFC+XP8vJycVTZs7pnz57zvtPc3Cx/8rPhwBLi733ve+dlVPPy8pCWlvaxDdGlgqNS5f/mWLpQiaTkVGwudeBMYw9SE8zSn0q4PD509vqxdHImxhflKZGN65m2qQl79jcgPTX2HMMk+0ObW52YMS0Dc2arYexMTwdKSlrwzrtlSElhH41R5GDUsrGxD4WFSVi8aNKIERZ90J6bNaMET//tCDq7PbDbOBaB2cAAGlv6ZC2XLJ6sjZiIgEULdNi+pxPtnW6kJluh0+sQDATR3ukCdAYsWjBRBsKrwJLFFqxb34TjJx3ISI+TPiQaVZ3dbvT3B3HD9ROQmXl+1clo7beEK5aj7bHX4NxyELH5OdCbQhkGb1cPAh1dKP6HzyErX40eSUxMwZtv1+HY8Y5Qr6UpZGQ6nF60t/fj6ivHY/y4fCWycT3fXdeA/YcakZEWd85Z7T+r4+bOysLMGSXKdFxRcbOw/lLHxcWazrHENzX1yVikhQsmCdHdSGAs36vBoAkB1J4t9Q2XANNJpaMQAz0KlY2qWbQgiJ17d6K9qx8pSSTI0gmbc1tHPwx6AxYvnIT0dDXEO0sWWrBhawta2lxyT7FHNazjPG7g1psmDKmsG639ZqeO++vrcG09iNi893Uc2c6DXd0o/t4XkZmbAxVITErBzko3qjqcSLOHuEYom8vjR7dTh+WTM1CUN7qyxcRokwU0jEFHNZJiGFiWOxDMvBLMrBIsGSYBEyN9YUNx3bp14myGy4cjwWKxyGswqIg+zsuPCu7j/t8cDVjMenz+yon4y5pS1Lb2ScaN16nJoMfM4lTctqJY6b/pvs/Pg6t/N06WtoqjRfDymjIxHV//0gIYWJemCJ/99Cx0d7uxb3892tpcZ0t+dSgsTMZ3vrkYVuvIzkG80J674dqJaG1zyjiOmm6Wu+tEvox0G77xlQVISQ6xi2o4H9OnZOCzd87Asy8eQ11j3znzksQed35iKmZND+kjFcjPS8JXvjwff35sP+obQv1GLFtlT/QtN03G5auKR9ShudB+s6YmY/Z//wCHf/wrOKrrRC7+P2OcFQWfvAbFn79VmR6JidHj299cgt/8bgeqq7vEKKd8dLAWL8zHZ++apVTHffXLC/HA73fiFHWcP1S2x/LHaVMy5DOVOu7uu2aju9uDAwcbzuk4OjZFRcn4h28tQUzMyI7gGqv3ajCYAh08CILVFwNtHDP0yINO9/5Eg9HG7BlZ+MwdM/C3l4+jrqFXninPA/vc77p9muw7VeD4r/s+NxePPnkQ9U1n9W8Q0l9+642TsXxxgTIdF5uRitk/+y4O/+Q3cFbVhQpsz+q4wjuvR/E9t6jTcRY97lpZgqc3V8gMVdpxXCUmHmYVpeDaeXmjLttYO7NDoM1RVQJdUBUl2dnMJpl68/PzpSTgmWeewf/8z/9gzZo1KC4ult/JCpySkiI9qt/97ndlpE14tir7wsj2yx7Wn//859KXevfdd+Pee+/9UONpmFFNSEiQEo+PM6MadqDH6uFklPxYdSdqWvrEEJmYk4BxOQkwRAFFNktpjx5vxqnT7aAbPaEkBbOmZyqdLxgGj9TJU604cqxZ+hsLC5Iwb27OiM8XvJg9R9kqKjtx8HCjZGhysuyYPy8nIqmMhvPBmZF7DjTIXEuyJS+Yk4P0tOhw7jm6ZPeeWglE0EmdNzsHeXkJI/p3XqyO8/b2oWn9LvRV1MJotSBt+TwkTB0fFSNMWImx70ADKqs6xRGcPi0Dkyfx36NeNvZ6Hj7aLGM4iEkTUjFjWnToODr2J0+1CCkVHemiwiTMm5M94uOt/h7u1WDQhSAYKCTfghk6JECnUztfOwySAZI8rqunH0nUcXNzzo1Higodt68Ore1Oua/mzc6Wst+o0HE9fTIruq+qXnRc+soFsE9WU/UQyY47XtuFho5QK9e4LDuKM2xKZBsJW3s05e46+hjs8eoCSj29TiRO/8KYW78x7ah+6Utfwvr162X+KTfBjBkzhDDpyiuvRG1tLT772c/i2LFjwgTM0txbb70VP/nJT857QNXV1fja176GTZs2IS4uTnpcOZvVaLz4C1NzVDX8PUDbcxpGE9p+0zDa0PachtGEtt8+Xox5R/X4X9U7qlM/P+bWb0yX/j766KPDfkbHNJw5vRDICvz2229/zJJp0KBBgwYNGjRo0KBBgwZViLoeVQ3RA7/Hg7btB9B94gx0Bj1S5k9H0pypUVHOEs1gkcKZmi6cKG+X8SD52fGYOSn9HGGLatlqartx8FCDzDzMyrJjwbwcmQcaDWhq7sXeffXo6/NIydmC+Tmwy4By9ejocIpsHDKfmBiDBfNykZKiLro6EJyVumdvHVpaHDLPcu6cHGRnxSMa4HM40bxpj5T+ct5g+vK5iJ9QFBV6hGMtDhxqlBnD7HNn6e+E8SlRIRvbG06UtuL0mQ75fUJJMqZOTBM5o0GPnD7TjuOn2uAPkIk9EbOmZcAUJTrOUVaBjp0H4Hf1I7YwDynLF8AYFx1ntaGhB/sPNoiOS0+njsuVfvdoQFs7dVwdurr6kZxklbaQ5OToWLeg24Fg43HA1QmYrNBlTYUuLgXRAM5x3bOvHo2NPdLiM3tWNgryExEN4Egp9pNX1XRJf+r0qRkYHyU6ToOGi4HmqGqICEdtIw7+4y/QfbQUQZ9fSAIMMey9mI+Z/+87MMXbVIsYlWDf52MvHcPeo00y45Jgv1thTgK+9ulZyM5Qt24knnry6YN4b2MFnE7v2Vm5OnFovvn1RZg4IVWpcfnq6yfxymsnxekKX6HPvxSHL39xnhhMKrFpcyWeePqgGHBhNqXnXzyGz3xqJq5Y/b8flfVxgEbII3/eKz20lIvNHC+8dAw33TAJt92qNrDUeeQUDv/z/eKkUjA+57KHn0XerVdiyj/eC73JpNRheODBnThT0SE9l4TZYpTAzdfuW6A0eMN+vAcf3YcTp9uENZwwGQ2YMiEV3/zSPCQmxCg1yh95/AD2H26QkWBhHVdckIRv3jsf2ZnqAiQBrxflv/kzmt9YJwESko9w/9NZnfR/vwv7tEnKZOPef+mV43j9zVPo7fWEeFF0IT3ylXvnY87sbKjEe+vP4OnnDqO7+30d97cXj+Kez8zGqpVFSmULNJ5A4MgrgLPrfbLk0vXQT7gMunErleq40tNt+N1Du9DY2BsaQ0T9+/JxXHl5iZCyqewpr6vvxm8f3ImKqk6ZN0vZ6EgvWpiL++6dD+sIE5/93UGbo6oEl+a/WsMHXvaHfvRLdB44jpjMNNjGF8I2rgCmhHg0vrsVJ38xfMn2pY4X15zGtv11sMYYkJNhQ25mPFKTrJJhfeiZg+cMOxV4Z81pvPXOaSFUyMu1C9lORkYc6ht6hP20s8ulTLbtO2rw3PNHJTsTli07Ox4dnU489Mhu1NR0KZPtxMkWYdXlfM2cHDvychPkTxrsjz1xAEeONimTjc/uwT/sQnu7E1mZ8bJueXl2cbxoAG/ZWqVMNndHlwS7+sprEJuXJTqEL73ZjKqnX0fF468qzaTSST1d1o7U1FhZt9xcu4xb2bqtGk89e1iZbDR2//jkQRw+3ozEBAvycxLkxZ/5Hj9TSC2BZ14+hu17ahBrNSE3Kx552XYZ21RW0Y7fP7pX1lYV6p55FQ3PvwGd2YzY4gLEFRcgJicLjsoanPzpL+DpUKdHeBZ5Jnk2eUa553hmeXZ///BuOcuqQB1GXUadNlDHUedR91EHqkKwpwmBgy8Arh7AlgZdfCYQn8EhqgicXINg/RFlsvHO5N3JoFdGZtw5/cs79s23S+XOVUkU98DvdqKsvB1pZ3UcZbPGGrFpcxWe+9tRZbJp0PBhoDmqGoagbechdB0thTUvS0r1CEYsTXYbzMmJaFy7Dc760LxaDe+js6cf2/fXI85qgi02NKeUYMlvRmosqht6cEjRhc9La83aMonusmw1LBtL9egQNjX3ibOoAjS616wrk1LH1JTYc7KxxJHZXrLscqSOykyDw+kRpz7MBss/WbZHw+69DWeUybZ5SyU6OvvlGYYj91w/liRzHMG768qVOTWNa7fDWdWA2KJc6M2mc7JZUhKht1hQ8/w78DnVBEfIes1MKp9pmKmWsrFsOj7ejG3bq6UMUgUqqrtw9GSLlF4OzHjwZ7537GSLfEcF2juc2LGnVkpVybwaPqsWs1FmvjJzc/iYmrvB73Sh4eW3obeY5Z4Ky8a9F5ufC2d1A1o3bFciG53Td9eWyZnk2QzLxjPLs8t15VlWhXXry0WXUacN1HE8Hw6HR6mOC9QeANx9ISdVHyot5/rprIlAwI9g5U5lOo53Ju9OPkNWPIRl4x3LZ8t5wyy9VQGWl5PNPDMzHmbz+zpOzq7NjC3bqpQGpzVouFhojqqGIeg5VYGAzwfjWSd1IMzJCfB298l3NJyP2sZe9Dk9iLcNHfVCZ5XGSnVDtxLZmpv70N7hgt0+tBeKDiFNkzMV7Upk63N4UF3TDXv80HXjxcpL9sQpdRF9ZhOYPRpcXiYlhXEm+VyVocRZwmazPmLpmz3egvq6bikjVYHu42UyOkp/1oAbrEf6W9rhqG5QIltlZaecx0jjVHhG2D9YXd2pRDb2krndfsnuDgbf63f75TsqUF3XLWtjj6Dj6Kz6A0FU1qqRzVnbAE9bB0yJQ8cy6WQPBtF7qlyJbN09/air75EzGVnHGeQsqwB114mTraLLIuq42JCOU4a2CsAwVDaBOQ7BnkbAq8bh4p1JsSL1jVOPMNjV1NSrTMfxPHJvRZKtt9ettFJpTIJBHNWvSxCao6oh8qUuvW5Dje+gPyCHJXTxaxgIg4GDwXXn+t0GIryWLAlSAYNRP6xsYajqpeGakPmfl2okUOZwtFoFjGeDDJHA941Gg7IeKWbE2XsUCVxPnV6n7LlK/+kw2416hARtqvRI2LCMpOP4TLkfVZEWvS/b0M9C7wWVyqa7gI6jfKp0nM5gYBoQwUAg8heCoeyqChjP6t9hdZw/qJRsT/TIhfSvSpIsvVHKfCOC7yvsGxTdOoyOC+kRdfo3LNvwOk4XFcRsGjR8ELRdqmEIUhbMgDHOCm/30Eigu6UdMekpSJ49RYls0YySvESkJFrR2eMe8pmz3yeGyJRxalgK2QuVn5+Izs7+IRcXy4LpaM2cnqVENpLWzJieiZ5u9xDZSADF17y56siUFs7PlbK4wYYcf3c6fVi4IFeZbHNnZ4scXKOB4DpyPadNSVfGKJq6aKY4omReHQx3azvi2bNanKdENrL7kjiJPXiDQVbnlORYZeRiUyamwhZnQncEPcLMHEtu+R0VGF+cjJQkKzpJKjYIDqcXMRYDpk5KUyJbXFEe4ory4WkbWhnCPci9mDRvphLZeAanTkkfVsfxDJOpWwWo+8k8TIK9SDrO1e8VHagKuszJoRLfwPkltLKOHgd0aeOhM6khF5sxLVPWz3OWOHGgbNQjZP7NUsS+Pm1qOiwWgzzXwejocEmbzbiS6GBNHjNgQFr16xKE5qhqGIKEqeORdeVSuFs7pDwv4PMj4PFKX2rA40HhZ2+WflUN5yPGYsR1K4vlkmppdwpbJ8mBunrd6Ojqx6zJ6ZhQmKxENkZPb7lpsjD+NTT2inNKI6Snpx9NTX2YMjlNKbPuDddORFKSFfX1vWIYUTaWGDY09KKwMBHLlxUok2315SXi6NfV9UivashB9crvmelxuFIh6++yJQUoLEwSMg+yJVM2jh2qr+eAcguuv26iMtnSVy5AyoLpcNY2CrFS0O+Hv98NR1W9MIgXf/E26JkFUwCOoCG7Lw22zk4X/P6AkAA1t/RJduuG6ycpY/1NT43DZcsKZa+1dTjPBWv4M53By5cXyndUgCXw1181XjKDLa0OWTOuXWd3Pzq6XJg/O1ucWVUZ1by7b5P+Z2dNvew17jlPZxf66xuROGc6kpfOhyrccN1EOZM8mzyjPKs8syRR4hleujhfmWxXXVGCzHSb6LSww8r9x9+zMuJFB6qCLm8OdPZMoK8FQY8TwWAAQZ8b6G0BLPHQlyxVJtv8eblydzY29sldynXj3co7lvrjlpsnK6u2mTwpXQKZJOtiLyrPqYc6rrlPbJSbbwzZAxo0RDs0R1XDEFCxTvu/30Tx52+V/jJndT2c9U2wJNkx6QdfQskXPqFaxKjF6sX5uPvmKcLQ2dbZj6bWECHLFUsL8OU7Z5wjqlABRsW//pUFyMmyi4Fe39ALrzeIFSsK8b1/WBqxX2+0MGFCKr7z7SUoLkpCd1fIeGMWc+7cbPzwu8uQlGhVJhsJnX74vWUSoXb0eUW23j4PpkxJw/e+sxS5OUN74kYLCQkx+MfvL8P/Z+8rwOO6ju/PMq+YWbbMzBDHsQMOO5wmabhpqJQ0+ZfbXynlpOGGsWnD5LCZUWZZtphZWi3z/r+ZlRzBKrEda+/Kfef7XrXaVb2T9+6de+fOzDmzZmbD4w6wbSShU5CfiB9+bwEmjE8VZptCo8aMv/8E2cvPQsjnh6O6Hu7mdhhy0zH5/76PzGWLhPo4kqA5/7wxPCdpo9nW5kC8WYsbr5+OC84bA5G45tKJuPzCcXz41dJm54te03vfumSiUNvOO3M0vn3VZO5za+twoqnFDrlMhvPOLMJt188QKhWSevbpGPvz70OXnQ5vazucNfU89tIvOAsT/vgTHpOiQHOR5iTNTZqjNFdpzpIeM81hmsuiQD6MfBn5NPJtZBv5ukmTUtn30UGdKMi0Jsjn3gBZ2niAA9Q2JleSJWRBMetayBLFHWJSoEdrJ62htJbSmkprK62xd90+l5+tKJBfu/vOeTj3nDGQIezj2tscfCB8y40zcc7Zo4XZJiG6ePzxx5Gfnw+tVou5c+di+/btX/n3FosFd999NzIyMqDRaDBmzBh8/PHHUbN3IGQhkTz3MQKrlbIPceju7obZbD4p/2YwGERraytSU1Mhp4anEQrKqBJxEvWTxU8ZK+mnHiNcbj/Ka7vgD4SQ0yNRM9w41jFHWRDSfqNTfQrCMjNPzpg/GaDMUXlFB+sMpqTouXQqVoTJyVUSUyyVTxOr4+hRiTFlW11dN2e5iPxkTFHysPdGHY+Pc9Q2wl5Vz5nUhGnjhQYMA0EZByInUipkfN9E6qcOhNXmQUUPqdOovISIZDyiQAdJZZWdnKnJpbaHGPJxAY8X1n2HEPR4oM/Lhi5HrEbpQB93pKydM5epKWFJk1jyI+UVnRxIJyRoMaowtnwcrE0IOS2QqfVAQs5RFuBY8HFU1UKZVApeqW1AaF/vAJDGNhEn0Zog0scNx147mnZbjvwbZpNenB02J+LHXHdc9+/111/HDTfcgH/9618cpP7zn//Em2++icOHD/O4Hgiv14uFCxfyZz//+c+RlZWFmpoaxMfHY+pUMa0TUqAqBaoSThFIY05CNCGNNwnRhjTmJEQT0ng7uZAC1egHqnPnzsXs2bPx2GOPHR3TOTk5+P73v4+f/vSng/6eAtq//e1vKC0thYoIEWMA0syTIEGCBAkSJEiQIEGChFMEXq8Xu3btwllnnXX0PTpwod+3bNkS8f/zwQcfYP78+Vz6m5aWhkmTJuGBBx5AICBGD5ggdVJLGBJUKrJ2fRUOlLRCpZQzK+HiRfksWC4ara12rFlXhT17m7gkiFhjzzi9ICbKWIm4YN36auzc1cBsgOPGpuCMxQUoLBBDMtIXPpsdDR+tQ9PnG+G3OmCeMAo5y89E4sxJok1jgpZmhxfNDg88gRAMKjkyjRokR9AwjTZIV7hlzTY0rFgDV30LdFmpyDx/MdLPWgC5UqwbpfG/r6QVG7fXo77JijizBvNmZmH+zGxoImjoRRv+oAe+oAOBkBcymRwqmR4quZ5fSxgajrom1L+3Em2bivn3lNNmInv5mTDkiGHn7gsioFq3oQo7djbA5wsyoQz5uPy8BNGmxTSIaGfTllps3lLL+sY52WacfloBpk0Ns8eKRDAUQpvLhxaHB+5AEDqlAml6Nftf6kGWMDJR02zDpv3NKKu3QKNSYOroZCyYlIY4QWzwIxqUVReZWZfLj2Z4+4L6SOkaiPb2dg4wKeDsC/qdMqaRUFlZidWrV+O6667jvtTy8nLcdddd8Pl8+M1vfgMRkAJVCRGxq7gBj/9rG29IiGSH2OxI9Hvtukrcd89pLHUiCtTf8+DDm5i9LixmLUPpkXYOXH/0/fmYOKH/pIwmqE/lbw9tRHVNuB9EIZehrLwD6zZU447bZmP+PHHMju72ThT/6AF07jrIDo80BS0HjqDp4/UYe8+NKLjuYmG2+YMh7Guzoc3pY1k6hQwgdY4Wpxf5Zi2KEiiwkQkLUvf/7nHUv7sSoYCfWUW7S8o5cM26aCmm/Pb7Yc1QQUHqWytK8eEXZfB6Ayy5Ul3fjb0lrdi+uxHfv2UWdFpx5TvegB2eoKVHkkPGQqABhANXnTIZcpn4QDoW0bm7BLvv+wuc9c2Qa8MbkK7dJah/9wtM//tPhMqD1Td042//2Ija+m4+wCTSliPk49ZX447b5wiVMollUE/qQ49sRvHuRp4KapUclZWd2LKtDpdcNB5XXzlZnI8LhVDa6eCDQpqpxPln8wbQ5vIi06DBmAS9FKyOQOw41IqXPzsMm8PLh5Z0GHy4tgubDzThe5dNRlqi+KSDhONHTk5/WTcKIP/v//7vpPzbVBpMpe5PP/00FAoFZs6ciYaGBi4HlgJVCTEDosx/5vldsFo9/YgeiDSjrt6K517chf/71VIhiyoRUTzz3E60tjqQnR13lEWXAmkKEp9+dif++qdlQhhsaTP+4iu7UV3dxZndXjIber+p2c73bdy4FGEMtuVPvY6OHfuhz8vqR2bjamzF4UdeQdKsSTCPLRRiW63VjVbSYVTIoezDjOwJBFFtdSNRq0KyXgwBT+PH61D/zhdQJ8ZBFfcl+6XPakfDB6v4vuVcdo4Q2w4ebseKlWWs0dtXssTt8WPXvmZ8sqYSl50nRqImEPLBE+ym2BQyKI/6C5oPlF31BLqhU4qvMog1BLxe7P/tY3A2tsI4Og+ynlP0UDAIe2UdDvz2MSx8459QqKM/H+jZPf9iMWrqLMjOMkOh6OPjmux47oWdGD82GWazOAbbWMVHnxzmKpvUVEM/WRCqwHn/w1I+YCV9XxEIV7J4oVLIoOqTMfIFg2i0exCvUSLdIGXgRhK67R68tvIIXB4/slIM/fdxLXb8d1U5fnCFuMORkQm6VyLvl4z/t66url+PaqRsKiE5OZmDzZaWln7v0+/p6ekR/z/E9Eu9qfT/68X48ePR3NzMpcRqAeuOVHslYRB27WpEW5sdaWnGfk6MNiVJSTocKetg9lMROFjSgppaC1JSDP2kXuh1aqoRDU1W7NnXLMQ2CuIPlrQiIVHXj3GV7mFaqoFp67dvrxdim7erG42frIcq3jyIcVWbkQKfxYrGTzcIO81vsHvYGfUNUgkahRykQd9o90IU6t5bydp9fYNUAmsJh4C6d78QZtumHXXweAKIHyBtQVImdFizfkstszyLgD9ImocByKDo50fCr+Xwh1wIhsT1vcQqOrbuhb28Fvrs9KNBKoFe67PSYSuv5b8RAaoUKT3chqRE/dEg9aiPSzOgvd2J7TsahNgWy6A5uGZtJbRaxSDtSjq4JAb2DRurhdnX5PDwz75Bau/voT6fSxg5KD7SDovNi9R43aB9XLxJw5nVpo6wfJ6EkQWz2dzvGipQpaCSMqKrVq3qlzGl36kPNRKI8ZfKfenvenHkyBEOYEUEqQQpUJUQkcqcsiCR5C1I+oIWVdqQiAB9L2VVIwlVUxlwKBiWmxBjm4N7kPQRqN97N3WkOyhKZijgcEFpHFzqQ4uYTKHgMkNRZb/eQHBQkNoLunMOv7iAxlHdAKUhcomUwqiHo6ahp7Q1+mhqtQ8pg6DXKmG1e2F3+iAC4SBUFvHEnnT96J5RICuhP1xNbQgFAizlMxAKnYY/czW3CfO/lK2ndWAoH9feKW1+B8Jm87A+aaT71rt2kbSJCNA8dPoD3KYSCUqZDE7fl5tWCSMDHd1ueroRtdt1GiU83gA6rPQ3Ek5l3HvvvXjmmWfw0ksv4dChQ7jzzjvhcDhw88038+ckXfOzn/3s6N/T552dnfjhD3/IAepHH33EZEpEriQKUumvhEEw9TTZUzntQCdH2RuVSg6TSczJitGkYZvohHrgBp0CWP4boyDbjGooVXK+R3p9/yCfN+X0NwYxtlEmlXpSA24PlHrdYNsCAWiSxPQdU4CqkMngo9RpBNBT1VDTqiBokxPQ3RbWshwIup/6zFRh5VPxZi18QwTxtBGhzKouwqFONNBLlkTja/D9ofe+/BsJX0IVZ6Qbg6DPD7mq/7ML+nz82cDsfjR9nEqpgMfrH9T7TOuFSB8Xy9DpVdwjSEG+MYIUOc1h0i0VAZqbarl8yMPAQCgEfZ/suYSRAYNOxfMxkv/1+gKciDDGkGb0iACtVyLXLNnxf/fVV1+NtrY2/PrXv+by3WnTpuHTTz89SrBUW1vbT3qJ+l8/++wz3HPPPZgyZQrrqFLQ+pOf/ASiIHkfCYMwc2Ym4szaQZlJcniUNaTe0LFjUoTYNmVyGpf4trVRWWFokG2JiTpMnyaGFXP0qCQU5CXwfRuYYaOyX5NRjTmzsoTYpktLRvLCGfC0dXFQ2hdU9qvQ65B+ZuRSkOEGkXRkGNScWaUy4L6g92iJFdkflXXREoT8fgRcnkFBasjr5c9Fgdh9aZFxuvpnTf2BIBxOL+bNzORgVQSUMjoQoacXjHBoE4BCpoVcJp2VDkTKwhnQZabC1dgyyMe5Glr5M/obESganYS83Di0D/C/hM5OJ8wmDWbPFOPjYhkU1C+Ynwu73Xv0QLUvyRIdvook2ks3qLmKioLSviDyHXpH6k8deZhelAyjVoUuW/91i+Ztp9WN3DQT8tLEHHhJiC6+973voaamBh6PB9u2bWNt1V6sXbsWL774Yr+/p7LgrVu3wu12o6KiAj//+c/79axGG1KgKmEQUpINuOLyidwbWF9vRXe3m9l/6+q6YTJpcf210yKWBUdrwf/2tVO59LeuzgqLxQWLxc12KpUKXPetqUczwtEGbTauv24aEhJ0qK3t5ntG945sIwmHSy6eIFQ+Z8xd18JYkA17RS3cLe3wdnbDUV0Pr8WKnMvPQeKsycJsy4vTIU6jgMMXgMsf5FJgpy/AMgmpejVvpEQh+9KzkTx/Opz1TVweTf2+9NNZ24jEOVOQe8W5wmybNSUD82dmoqvbjeY2O6x2D5deNjbbUJAbjwvOHC3MNoVMDbXcgBCCCIZ8XOZL5cAh+CCXqaBRiJeSikWoTEaMu+cm7iV3lNfC097FF71W6NT8mcr4JXFWNEF+//rrpiM+Xovauv4+LhAI4fJLJzK3gYTBuOjCcSgsTERDo5UPVYmssKnZxoeYC+fnYbagQ0wCyYARYZLbH4TLH2D/Sz/J/yZqlciQAtURh/QkPc6blwt/IIRGGm8OLyw2DxraHIg3anDFGaMilgVLkBBrkIVENVfFEEiTKC4uDt3d3f2YtL4JqBG5tbWVaZ77ptVHCmhYbN9Rj08+K0NVdRdnvaZMScf5547B2DHJos3D/gMt+OSzIyyZQyN4TFEy2yYqm9oXRDT18adHWIaAyuHycuOx7OzRfKI+nCWixzLm7NX1qHr1AzR/vpFLC/U5Gci78lwOxkTrgdImqcbqQjNlHUIhJlLKNmmQY9IO2T8VLRDDb/VrH7KupbfbxkRKpGmZf93FUAsqw+xbxrVyQzXWbKpBV7eLSwwXzs7BsjMKkZSgEzreyI/4Qg74Ag4E4T+aaVUrTFDIpLKzr0Lb5mJUv7YCXcUH+feEGRORf+2FSFkgJpvaFyS59fGnh7FnTzNXQeTnJ+Dcs0dj3twc4T4u1vkfPv2sDBs2VXOLSGKCDmcuLcQ5ZxX1SK2Jgy8QRL3dw8RJVMmikss4k0o+eCDJ0v8KRvp4I/9LEjVrdzegrtXBskOTRyfhrJnZyM8wnxJ77Wjabal6E2aTOEkfq82J+IIrR9z9+6aQAlUpUP16ogWnj4kyIhEYiYarp+RRF4O9FkSsRGVeRKARjR7G4xlzAY8XQY8XStOXtPWxAtr4UskZ9a7GnG1+P/xESmXQCQ/sB4IORVxuH0vVDEWwJGq8hZeZsJZqrD3TWIffEW7BGIrQSySIWI/kLmLRx8UyiGMhzGWgirmsVq//pcPB/3Xt1FNlvHHbgCcApULG64MoSIHqN7TD9r8ZqMbWTktCzIE2H4YYJseIxQC1FyQPMgRruHBQWeFAmZpYAW2O5ALJk74KFJyKzqAOBdrwGgRpzX4dwkFMbD7TWEcsBqi9iMXDy5EAVZQOk041/yvhxP0vscBL+KYQvY7J8L+IkXtEJCEqoIxgS4u9R7ImtpLvZI/D64fd649J21wt7XDUNXEWLtbgbu9k2yizGotSDtS71ZstjyU4HF62jX7GGiibSrZZY1BywOsNoLnFhi6LC7Hq49ojkKDFQpa8tdXOF72OOR/X3Mb92pKPOz7QHGUf55Z83PGA1gSyjdaIWEMs+zhiC6e5QHuSWPNxEiR8HaQjFgkRQc5s1epKfPp5GZpabKx7OHpUIhNCzJyeKdy2eqsbR9odsPQs9GaNEmOSDMgdIG4tAu1b96Di+bfRtecQQsEg9NnpyLv6fORedR7kApnTCJYDR1Dx3Fto37KHN5ea5ATkXr4MBTdcIjzD2thoxbsflGDHjgb4/EEYDCosXpSP5RdNECY51AsijXnnvRJs2kLMeX7Ols+fm4vLLpmApCSxGS/aUL7/4SGs21DNrKJEeDNrZhYuXT4e2VlxwkscP/rkMFauqkCXxQ2FQobJE9Ow/OLx3FcuEhT4fbGqHJ99XoYW6t+Sy1A0KhEXXzQe06ZmCPdxm7fUYsUnR1BXZ+H3cnLiceF5Y4a91/1Y0LZpF/s4y/4jVCsKXXYa988SsZhMcIlk175S9nEd2/Yi6A9Am5KI3CuWIf/65VAIEqzvRX1DN959/xB27mrgAxLya2EfN1545RKx1ZOP27KtNuzjtCosnBf2cUQQKBLk197/sIR9nMPhg0opx+zZ5OMmIjNDbIUL3asVHx/G6jWVsHSTj5OzOsGlyydgVGGiUNuCgQBq3/gENf/9CM6GFtZLT5g6DqNuuRzJ86YJtU2ChGOF1KMq9ahGxBtv7ceb7xzktrK4OA1v6ohdl3pq7rpjLhYIpNKv6HRgd2M3Ez5olQouhiAiHmr1mZphxphkcayTLeu2Y89P/s5MuhQE0sLg7bICgQAKb7kc4398y7B999eNOcv+w9j5/d/D1dgGdXIC66r6um0Iuj3Iungppj5wr7BAmk6iH/jzOmYSNZs1HAiSvIrL6cP0aZm4/8enDdJtjBaI1fSBv67D4cPtvLGkcnO32webzYsxRUn42U8WIyFeJ2yT9LeHNmLXrkbo9Eou/SWNS2u3B1lZZvziJ4uHjWn668Yb+YwnntqG1WsruS+K7h1tzru7PUhK1OH+Hy8SSsz22n/34p33S7jUkcZcr4+jgOF7d83D3NnZwmyj4PmFV3bD7wsgLk57dBwqVQrcfP10LDunSJhtzau2YM/PH4Tfaoc6iXycnBnEEQpi1G1XYdwPbxy27/66MUeHgzt/+Ae4m9vDPk6lYvkt6sfPvuxsTP39j4QF0nQQ94c/r+Of5jgNNOoeH+fyY9aMTNx3z2ns90SAsoB/+ss6HCnrgMmk4ZJuyl5SgDh2bDL7EbNZjM4rZZ3/+vcN2LO3ifcfevJxHj+zJufmxOHnP12M9GGSWfm68Ub92Y89uZUDaCKxM5CP8wXZj6SkGPCT+xaxbJ0I0Na+9MEXUPn824BCAXWCmaXpiEFcHW/G9L/ej9TTZ0fVppHfo/oWzGaDQDsciC+4YsTdv2+KkRdBSRh2UGkNnRBqNApkZpp440aLV3a2mQXLX39jP5e5iADR5h9ssTHTb5xWBY1SDrVSDrNWCUoylLTa4faJsY0ylIcfeQU+qw3G0XlQJ8QxO6whLxNKs4FPNa1l1cIWrbIn/wNXUxuMRXnQJMVDZTJwtleTmoSmT9ajY+seiMKKjw5zkEpjjE7vaUNCMkkkdbF3XzO2bK0TZhsFWrSBo7lA2VOyLTFRj8wsE8rKO7nyQBS2bq/H7j1NSE018P0i2yhozsmJQ0ODFR98VCrMtoMlrdiwsYbtoedIfoSCrpwcMzo6XXjjrQPCytAos/Xxp2XQ61TIyOjv42iD/vob++DzB4SVhfIhIUJ82EABPl30mt57692DwkofA14vDj/yMvx2Bwyjcnnzyz4uPwsKgx7V//4Q9koxc5XG0pHH/w13cweMRfnQJPb4uJwMaFIS0fjRWnTs2A9ReH9FKQepNDdpTvT6uNQUA89hmsuisGp1Bfsy8mmkRU62ka8jn0e+b/XaKmG2UWXBvv0t7EOSe31cgo7nKq0ZH318RJhtZNemzbXM3kz67nRQSD4uNzcObW0OvMXzWAxsR6p4z0F7D9qD0DylPQntTWiPQnuVWCzZlyBhIKRAVcIg7CpuhM3uZefbF1RulpKsR2OTDYcOtwmxrcXmYX1NfQQqf51KAbc/gCa7mE0clcHZK2qgTU8dVJpHmQefzYHWtduF2OZqbEXHzgO8YRuYUaAFjPq4mldvFWIbnY5v2VbHm3Eqm+oLzjDIgC1bayEKGzfVQK2SDyI/USkVfJizYaOYwwfC1m21tEMfRGpDZawUeJHElKheXypv9Hr9g8q2aW4kJGpx+EgbmprtwnwcZbNID3SgbcnJetQ3WHHkSLsQ2+hghkrNkyOUlCcl6ll3k/5GBCx7S+GoqocuY7CPowoSknFqXb9DiG2OmgbOqNLB20DbVHEmBFxetIjyvy4fduyo5zk5kOWX5i6d12zbJu4wbj1lBDUK9ml9QT6PfB/J6YjCVjqklPWsBX1AawX5ls1bw6XKIrBjZz1rpA8s22Yfl6DjwzoKWEWgdd0O3nPQ3mOgbbRHsZVXo/tAmRDbJEg4HkiBqoRBoLJG5jaL0AdFC5efxMCdYja/vh5CkUi0+b3vkR6cKBkJ0iaVR+j1pHtJl9/uFGOb3YHQELYRqESZNpkiQNl56mVUDaEjSP1IVOYlClT+NhRDJ9lsd3iFZQap/Jh6UiNBpZbzJoqqIESA7stQvZRUCkxlwKKCaPrer/NxTqeY++Zy+xFCaNChDYGeNUuGCbpv5L+G9HFyOV8+UT7O4WLSGIVmiBYBuQx+m12YjA/13dOcjASlUgaroCw5jSeaq0P6X5WCfaAo0H2hNSASyGZaO0RVeNmdXgxVSU7PmnycsLnqcJGDi+jjaP7SPPbZxQTRIxZ0L0Vf/4OQAlUJg0D9HjK5jBeAgSAiA71OifR0MX2gRrUCCpksYjDqDwbZKRvVYvp8DDkZLCVBvVsDwSU2MkCfky7ENl1mKlRxxojBKBE+Ue+KqTBHiG1UykXZo6FYJj3eAPLz4iEKVMY1VEBFBzbUJyWK3IZs8/qCEQNlh93HVREmoxiNpKxMM2eKItlGG+Nw6aNenI+jgyP/YD9C45D6kEX5OCpxVCrkHNxEOkSkYJX+RgT0uZlQGvUR/QhtfAkGQT5OTz7ONLSPo8FoyBfTd2w0qXku0vo5yLZQiAMtmssiQPMgNyd+yMNn8n3k40QhLzd+yECU5iqtHeRLRCAnKw7hoRXZ/xoN6oiVEdEA7TVoVYpU3kt7FNqrGHLFEmNKkHAskAJVCYMwc0YmMtJNaG7uL4lAgWtHpxMTxqfy4iECKQY1EvUqOHwBFibvBb12eAOI1yqRJmhjbsjLQuqimfC0d/aTRKBNkrOmEbqsNKSfvVCIbbSBy7poKS9QfueX9Pmcnalv5p7VjPNOF2IbZY6WLhmFgD/UL1gl24iJkg5GTl+UD1FYckYhlEoFMzr2BZHbUBnfmUtGCbNt8aIC3qTRfeq7WaL7SEHY0iVkuxg3v2B+DpfWkh/paxuV6VGG5rQFecIIWmbPykJaqiGij6PS2qmT05GTLWZzPnF8KgoKEtDa6mCyll7Q69ZWJ39GfyMCplG5zBbqae1A0Ovr7+NqG3lznLZ0nhDbqP8u8/zFTBAXcLn7+7i6Ji5Nzjx3kRDbqKSW5iIR7UT0cQZi/y2AKJy5pJAPp7sHSFuRzyPft/SMQmG2LT49n8ujI/m4YCCEM5eOilh9EA0QAzcRsZG8VV/b6JDJ6fTyuiWKzTn9rAXMxk17Dz6o6QHtTWiPQkRKUqB6nJDJxV8xDIdjeDL0kjyNhEGgbMJdt8/BI49tQX29lSUlODOCEIpGJeE7N88SlkGi752VFY/NtV3o7pGm4VPDHoma2dnxUAzoAYomxv+/2+BqakfX7pLwGwoFQj4fdBkpmPLbH0AdJ45Kf/R3r4KtvCbcQxYMcbkvlcqpE+Mw8ed3wCgo20BYdvZoVFV3cb8nBQq08aBAS29Q4eorp/DhiCjMn5uDiopOfPzpESbvUPbYRj1d558blgsRBWLNveaqKfjvG/tQV2floJQCGrp/i07Lw3nLxgizjbKW37llFp55bmePbTIEAiEO7kni6sorJgmzjTaPd94+F48+8aWP6z34IpbTm2+cIcw2eoZ3fncOHvznJtQ1WMM9jT2Z6ZxsM38m6vCBMOFn34W7tQOWfYfDb8jlCAX80GemYcrvf8SHYqJQdNe1sFfVoW1TcR8fRzJc8Zj0yzuZPE4UaC6SjyPynS99XIDH4jVXTxHKgL1wQR4qKjtZjq7b0s3ji8rftRolLrpgLObNFVNtQyDff9UVk/HmOwdQR/5XqWAfRzYuPr0A55w1WphtVDVyy00z8fyLu3pskx/1cbNmZrO0jygQsy/tOfb+/EHYy2sgU6lYfYA2TIkzJ2H8/bcKs03CqYm0tDRcddVVuOWWW3DaaaedtH9XkqeR5GmGRFu7A5s21+BQaRv3qUybksELlmhNSwKRJtVZ3GhxeI5mWnPjdEyoJBrU99H8xWa0btjJ0i8J08Yj84IzoM9KG9bvPZYxR6epLWu2omXNNi6RM48tROYFi2EuEpex7AUFf3v3NWHb9nqWSyB9PNpAidbbJJCbLDnUik1bapkcg9gnKYCdPClNuKYlobyig0mfGhttLH0xd3YOpk/LGNaA5lh9HDHsEvsvbdIp+ztrRhZnNEVJcfQFPcsNm2qY2Il83PSpYR8nWtOyN5tFz5QIWWiETZyYyvMhvkeuRiR8NjuaPt+Eto27OLOaMH0CMs8/nYNV4T7O7WFiuNa127hfNm5cIbIuXAKjoNaGgT6OGH637ahjCSli1T1tYZ4wCZOBPm7/gRYmtmtvd7C8ysL5uRwoxoKPO1LWzvsRInMk1uS5c7IxdUps+LjaWgs2bK5BTbUFBqOKg9TZM7OgHqLvN5og/VRivCaiMblWg9RFs5B+9gKojNGXWRnx8jQ174qXp8m7NGbv33vvvYcXX3wRH3/8MfLz8zlgveGGG5CZ+c0y91KgKgWqEk4RSGNOQjQhjTcJ0YY05iREE9J4O7kY8YFq7XviA9XcS2L+/rW1teGVV17hoPXQoUNYtmwZB60XX3wxlMrjP6CWZp4ECRIkSJAgQYIECRIkSPhGSElJwb333ot9+/bhwQcfxMqVK3HFFVdwZvXXv/41nM7jY4YXX3slIaZBPXlVVZ3cc0ElQCQCHito73ahrMHKrwszTEhLiB3bqHT14MFWLvXKz49n8qlYKJ8iEJHN/gPNLFuSlWFGUdFg3UFRYAbMYJgoi9idVXJFzNhGRDsHDrbC0u1CnFnLZb9DydZEG8FAAF3FB+FsaIXKbEDy3KnM6hgrz7SsvIPLktUaBd83UUzEkWyj3rLKqi4uIZwwPgWJibFx3wiOuiYu2SNQCwExi8cKOjudKDnUxj6usCABuYII9iLBZvdwGavXE+Dy2qLRsePjSMasfdte+KwO6LNSkTBjIuQKRcz4OLpvRKoUH6fDpImpMePjqC+Vxlt7hwNGg4b9yED9aJF+hHq27VX1UBp07H9JnzxWbKuptaC62sI+jloIqHRagoThQktLC1566SXOqNbU1HCQeuutt6K+vh5/+ctfsHXrVnz++efH/O/FxiyXEHOghf7Z53dhV3EDnD209cRuRwx737pq8iBh8GjC6wvgrfWV2HKwBc4eQiW9VolZY1Nw1ZLR0AmSp+ldTN9+9yA++ayMWWGprl6vU2Ha1Azcdussof1ltGB99nk53n7vIJN50O9qjRLjxiTjju/OZvIbkXD7/bD7PAiEiLYrBBkoUJXDpNZwwCoStHl7/qVdTLwTJiuSISvLjJtvmMF9UiLRXVqB/b99DNaSCgS8PtaypH7osT+6EVnnLxZqW2urHf96Zgf3uRPbL8UKiQl6XLJ8PBNRiQweaH4++8JOFO9uOio/RCzEROx15eWThBIW+V1ulPz1GTR9vJ57yXmuxpm4n3z8/d+BUifOj1Bg+sbbB/D5F+Ww9rDEEgHfjOkZuO2WWcKYnAl0n4j07L33D6Gzi1hiwb3Q48el4I7bZiM1VWzw0PDxOhz+50vcN0hMrAq1CuYJozD5N99D3Dhx7OEE4gd44eViNDSQjwvr+GZnm3HrTTMxaeLw9h5/HYjo6enndnCwRcE0HZyTRNN110zFgnniyOwIjtpG7PvNo7DsOQS/28M+TpuWjNG3fwt5V58v1MdRnzuR2e3Z28R6rsTLRvPzvGVFuPzSicLYkkcu6H6JvGdyxDLeeecdvPDCC/jss88wYcIE3HXXXfj2t7+N+PgvDzEXLFiA8ePHn0L/1RKELfaPP7kN6zZU8WkqLVa0KafFizYAb719UKh9b66rwMpd9bwgpCfpkZGk54Vr3d4m/GdlmVDbPlhRijfeOsCLKd0zYunUaBVMAkEsypF0G6OF9Rtr8OIrxXwIQRqR9FyNRhVvUP7x0KajBxIi4A34YfW5OUjtzaTKZTLOrnZ73Aj0odePNug0+p+PbmbCjKQkHXJy4pCcrGcm24cf24LKqk5hthH7avG9f0ZXcQnUSQkwjc5jZlNXUxv2/+YRtG7cKcw2Cv7+8fAmFO9phMGg4vGWnm6C3eHBy6/uwZp1VcJsI0max57cyiRPxN7c6+OIhZUOmt77oIe1WxBK/vwUav7zETPqGgpzYByVy6+rX1uBkr88LdS2d98vwTvvHuR7RfeM7h3dQ7qXjz6xtZ/cT7Sxem0ljy0aYzTWyDYaezQGaSy6eg42RYDmIs1Jmps0R2mu0pzt2n2I5zDNZVEgH0a+jHwa+TbyceTravv4PpGkjv/45yYcOdKB+HgN25aaamD5pief2s6HiCJJxejZtW8uhjLOxPNUn5sFr8WGkj8/jYYVa4TZRnuNRx7dzHsP2oPQXiQjw8R7E9qjfPhRqTDbJJyauPnmm7m8d9OmTdizZw++973v9QtSCfT5L37xi+EPVP1+P9ccP/XUU7DZbPxeY2Mj7PbBQtsSRh4o+0EncCRUTVlUOhGkQDAxUQedTokvVpVzNkIEWi0ubC1phVGnQpxBzcEM2WfWq/naVdaOhjYx45B03T79rAwqtYJLpOmekW1U5kgL64GDLThY0iJs0VrxUSlLDqSlGjlbRLYZ9GpevKj0kZgoRcHp9x0t9+09gaZnq5TJ4Q+F4A4MFi2PFmi8U5kjbcp7mWrVaiWyskzo6nLhi1UVwmyjjZC9sg6GUblcckaQq1XQ52XCZ3Og+pUPIorRRwPbdzagvLyTNZmJRZeeK407ymrRs6bx6PMHhNhGTLr79rcws6nJ9KWPo3lL5cmffl7OBzoiQPIqjZ9sgDoxnrU/KUNOF72m9xo/Xg97db0Q22w2Dz77opzvUT8fZ9LwvaR7SuzYIkBjacVHh3lspfb1cQY1j0Eaizt2NgixjeZg1cvvw2938tykOUqgOUsHETSHRQY1X6ysYF9GPo18G4F8Hfk80i9duVqcj1u3vhpNTTY+dNBqw/eNDtAzMozcxvLxpz0ySQLQvHILug+WwZCfDZXJEPYjKiUfRJAsUtXL7yHoF7N2UQB/oISIoAy8ByHbKINK85b2KFT11VfTV8IxQBYDVwyjqamJ48LZs2cP+Tc6nQ6/+c1vhjdQpXrjyZMnY/ny5bj77ruZ3YlAdcf33Xff8f5zEmIQh4+0w+MNsJTEQMTHa9Hd7cGRMjGnv+X13Vzua4pgm1Gn5L7L3r7VqNtW2cnlZgnxg0vfqDSOgkXqsRGB5hYbGhqtEXtTaNGnLAj1X4oAbSwpc6qQhTeWfUG/0zsegYFqcXEjP79Itun1av5cVDBI8iC0MRrY40a2qZPi0bW3FD6LmPlwqLSVx1UkiQbyI03Ndi4zFIHSw22cWYjk42iOWCwulAnycZ3FJfDbHKxvPBDqBDOXAnfuElPVQr3GdG8i+RG6l3RP6aBTBGgsNbfYI/pfGoM0Fg8J8r/erm7uYVQlxg3yIzR35Uolz2URIN+1a3cj+7JIPo58387iRogCHZqr1PKwnvAA2+ggndZUUQFXx459fP96Dx76QpOSyAcQztomcT7OH+TnNxA0R6j9h0qqJUg4WTCZTMySPRAdHR1QfIM+/OMOVH/4wx9i1qxZ6Orq4si4F5deeilWrVp1woZIiB3Qgs6HNxF6K+g96h+k4EIEQj3X0AdLYfuEgHorv9o4YQENV85+xVfTow4JLNmLZdBtGarNiO+bqPGG3mcW2bjw/KUxKWiufsV4ClsW9jUi8HW3hD4X9lTZuCG+vXcgivS/x3DvRIDGEs9F2VfLjQgBD6jQ0P2KtK4KVAqk7x6ylZJ8nEDbevcjMTgdEAp81TPt+RtBY+7rn5m4tUHCqYnQEOPJ4/FArT5xbfLjZp3ZsGEDNm/ePOhLSdy1oUFMWY2EkwtiSKQsG/WYDTyNo+Z8s0mLolGJQmwjdl+9Rgm7i7Kq/ceg0+2HRi1HQboYfanCgsRwxtni4T6fviAiGSq7KRqdLMQ2KpMi8omGJuugLFJv3+y4cSlCbKP1nHpSvYEAl/sOdnwhqBXiCLKmTknH51+U9WzmvrSPfqeT/Hlzc4QRZiTPm4r2Lbt5M0TloX3h7exG0twpUCcMzsxFA2PGJHNZNI2vgcRE1DqQlmZCdlacMB9HNrndvqPlhH1ti4vTYFShGB8XP3kMFAY9fBYbZ1D7grLjKqMB8VPGCrGN2H3jzBq+RwMZ4Kn/k+4p3VsRoLGUkhzuXdSlD/ZxNEXHjhXj46hk2zy+EB3b9w2ajzR3g14vM8WKAPmuKZPTsXpNJRITB/s4l9OH0xfmQxSI3be3OmNgVtVq9WD6tEzuQxaBxBkTUP/BSi7zpcqWvvB2WGAsyIYhL1OIbbTXUCpkvPfobVnpBVXFxcXpeM8i4Tggk4cvkd8fg3jkkUf4J/mOZ599Fkbjl6R1gUAA69evx7hx40743z/u/2o6kaQvHgiiHaa0r4SRj4kTUlmmgRZ82ojTYkUXbU4cdi8WL8oXJuGQkWTAjKJkWJ0+DlZ7bXO4feiyezG5MAl5aWKYHalP68wlo3jDRvI0vbYRSVFzsx1jxyRj2rR0IbYRS/N5547hk+f2DufRTBYtYo1NViaomD8vR4htXEKrpNJawB8MHj2Vo59EriSXyaEVGKiefeYoXtQbm2xHeypp49vUZGcGxbPPHC3MtqyLlnI/FJWYBTzh8regPwBnfTMUWjUKrrtYWBA9b04O8vLi0dhohdsdLt2mcUc9b4FgiJknI5UFR2vzO25sClpaHDw/e+eqxeLm35ecUSiMods0pgBpS+bC09bBpCy9tnktVnjau5C6ZC5MRWICByr5XbKkkO8R3au+Pq61xcHsunRvRYDG0nnLxjDxWkcfH0djj8YgjcW5s7OF2EZzMP+6i6HQanhu0hwlBNwenrs0h2kui8I5Z42GyaRmn9Z7cEm+jnwe+b6zzhTHSHzG4gIkJxv4GXq9PfctEERLi53lac4/t0iYj8tYdhqTYjmq6hBwhXk7QoEgE2ZRcj//uosgV4kJoqdNTceYomTee/T1cbQ3oT3KWUtH8Z5FgoRvioceeogvGl//+te/jv5OF/1Ouqn080QhCx1n7v/qq69GXFwcnn76aQ5MSdCVxF2pZzU3N5epiUcarFYr/zd1d3fDbD452TgK6KlWOzU1FfIBmY6RACJWePypbdy36PWEN5mUiTttYR5Lcgw8oYsmXB4/Xl1Zhj1lbXD3LFwatQKTChJx4zljYYjQkxEtUI/Wy//eg7Xrqo72zdAGijbFd985l0/8hwtfN+Zoqr/zXgk+/OhwWFaCyRVkKMhLwPfunCtcB9Hl98Hu8/aUlYdLWhVyOcwqDdSCdQaJhIXkaejwhiEDP8ubrp/OGVWR6NxdwmyitOENl9uGuD+q6M5rh1Ue4Vh8XF19N7PrVlV1wR8Ilz/S5uiC88bgissmDcqQRBMUzDz+5FaUlLbxBpjuHJGLLT49Hzd+e7qwIJpAfaj7f/soWtZuh98Z3gAr9VqknTGXpUxEajTSvXrp1d1McuNwerkigu7VhHHk4+YNq9b21405Ck7feucAPvrkCBM/kY+jrFJBAfm4ecjJFpPBP6pn+frHKHvyNXjaqDdQBplcBmNhDib/9gdInD4BIrF1Wx1efGU3s+z2Vp4TEc8tN87E7FlZQm0jEsKnnt3JwWrvjjUhQYtvXTllWIPoY/Fx1iNV2PuLh2AtrUSo5wCCKiEKbrwUo797tVB5GnqWpOBA/aq9QT6Ri1Hwf8N106KukTsce+1o2m2pXwGzefj2cF9vhwPx2RfG7P1bsmQJS9QkJCSc1H/3uANVypwuW7YsLOJeVsb9qvQzOTmZ07s0oUcapEA1MugZE7ESNdwrFXJMmJAqdKEfaFtdmwPlDd38ujDTjPw0U8yIutOCuv9gC3y+IAry4zF+HI2D4bXtWMccZVT37Gli4qmsTDNnQERqRvYFBalEnMQEFcR0KlfEzDOlUvji3Y18Ih0fp8P0aRm86McCKJtKZCyuxhYOYlIWzYImMT4mxhtlaIjxur7BCq1GialT04f1wOZ4QOOMyH+qqrvYx02alMZzIlZssx6q4IMIQuKMiTCPK4yZ+UDkbAcOtDCTeEF+AmdTh9u2Yx1ztEHfu7eZfVx2lpl1QGPFx3k6LWhdvxN+mx26zDSknDYTCk1s+BE6XN29pwmW7jBh1ozpmRHJeESAqn+IWImerdGgYd3e4dbsPdbxRsy+7Vv2wFFdD4VOy/5XlyamzWcgmESstBVV1RaoVHJMnpiGTEE+bsQHqg0fiQ9Usy4Ycfcv6oFqrzzN66+/jr1797IkzYwZM3Ddddf1I1caSZACVQmnAqQxJyGakMabhGhDGnMSoglpvJ1cSIHqqReo3nvvvfj9738Pg8HAr78KDz744Al9x3HXb1LWdMGCBRyY0tU3eKXPTj/99BMyRIIECRIkSJAgQYIECRJiDTKW0BN3YCGLQTKl3bt3w+fzHX09FL5JtY3yRGqQSdR1YIkvRfj0WSSiJQkjE75gEJ0uH2y+ALNumTVKJGhVUMRA6Znf4UTz6q1hPcFQCAnTxiP9rPlQmcT1bvUi4PWibf1OtG3Zw2yOceNHIePcRcNeinksCAYC6Ni6J1x65nByf1TmeadDlyG+ZJ/bCeos2F3aBpvTi5QEPWZPSENmSmyUiVZWdWLb9nounU5K1GHunBxmQY2FUkwqNd+8tQ5NzTaYTRruKYtGKeaxIGjpgHffNgRa6iHTaKEaOxXK0RMhE0iQ1Ytuqxtbd9SjsqqLy+ImjU/DjGkZQvtTe0EEKNt31HN/GYH63OfOyY6JUkzqd9tV3MAcBkS6M6ogkcnYhrsU81hApcj7S1qx90AzXG4/crLMmD8rG0mCCAD7ggvY/O2AtwkIegCFEVBnQ6Y0xYRtNA+2ba9DR6cLyUl6Hm+xwgzrqqlD59qNcDc2QRUfh4SF82CcPCEmfFxbmwObttSylq9Or8SMaZncUkNM/6JBJJibt9YyTwD1pE6ZnMYl3dHuT5VwamLNmjURXwst/aXyh5aWFiZQ6osjR45wvyqlyEcapNLfwXD4AjjS5YSrhxyAQMuBSa3EmAQ91AIdMLEm7rr3T+g+UBbWKCPZEIUc5rGFmPGPn3LwJQok7F5831/QsW0vgr5A+KbJZDDkZmL6X+7jgHq48HVjjhgm9/7yITR/vinMEEt/EwpBm5aMKf/3PaQtmQdRIBbY/352GOuKG+D2BML6pCHAZFDhqrPGYPFMcWQe5CLffb+Eiaioh4s2RtT3YzCqsfzCcbjy8klCN0tE3PXCy8VMkMU6x6EQNFoVliwuwK03zRy23rxj8XG+w3vhfPtZBLs6esRTQ5ApVVBNnAn9lbdz4CoK5RWdePjJrWhsth0lj6GNJZEC/ejuecJYfwnEtvrgw5t4c9mr/Uk97hQ03PujhchIFxfYkETZQw9vwsGSVmZfJdC4y8ww4Uc/WIDRo4ZPnubrxhwx/D7x/A7s3NPEAbSsR1c7OVGP7944E9Mni2FdJ4RCQcC+B/BUAyEiKOzxGXINYJgKmTZPoG0hvPn2Aby/opSZ/Wms0XvUg3/ZJRNw6XKxAWHrik9R9+QL8Fm6mYCKSOMUeh1SLjoXeXd/B7JhIts7Fh9HRHv/emY7OjtdvG4Rz4JapcD8ebm46/Y5QokniWPk4ce2oLk5zB7OBIUKGQfR9/xgQdQPlkZ66W934yfCS3/jMs8bcffvm+KYZ9Bll13GP8lZ3XTTTdBovqS1piwqsf9SSbCEkQ+SBCmzhINUrUJ+VNuS3rd6/ajqdmFsopjJSs52368fgWVvKfR5WUdJKIJeH7pLyrH3l//E/Ff+CrkglthDf3+eSW102RnM0sm2+QNMsrDnZ//AaW8+zDqIIlD+7JtoXLEWmrRk6HtYQynQd9Y0YN9vHsXCMQXQZ4mRltiwuwGrttcxY3NSnPZowNVuceO/nx9BdpoRowQReRGB0htvH2T2UJLx6bWtq8uNt98tQV5egjDZi8rKTmYjpgxXX9tsNi++WFmOvJx4nLusSFgm1fnWswjauiBPzTyq8xpyOznDKk/OgO7cq4TYRgHNY09v5yA1K8N0NPNBhC37Drbgpdf24Id3ijm4oUOQJ5/ahoqKDmRkmI5mPohRvLyiA0/8axt+++szhTEmv/hyMfbua0Z6uvHoJpwC1oYGGx59fCv+/MA50A3Qpo0W3v2oFFt2NiA5UQd9T+aZ7mdTix1Pv7QLf/j5EnGZVXdl+JKrAbkeR0/jgk7AsQchpRky5cllyzxWUOaefBlVEuTmfulHKPgi30c+buZ0MXqg9pLDqH38WYT8fugK847a5rd0o+WdD6EvzEPqhecKsa211c5BKum5Zmebj85JOtDcsLGaD2+uvnKyENvIhkef2MpBambmlz6OfB8RZr3y2l7cfcdcIbaNWNCcFZnBl4mvHhgqNjwWECPwieCYj9rpNIEuchAkS9P7O13p6en47ne/i1dfffWEjJAQW7C4/XD5+gepBCr5VcllsHj8cPbJtEbVtr2l6NpdAm1Gaj+mRLlaBV12OroPHGFRdRFwNrSgeeVmFnfvDVLZNqWCRb8dNY1oWbVFiG1U5lv/7hdQGPT9pC0oeKCA393ajsZP1guxjTaS63bVc4LBZFAfPbmnn8nxWtbI3by3EaKwanUlSzSR7EZf2xITdfD7A1i1ukKYbes2VLEMB8lI9LXNbNbwpumLVeVHdRGjDQpGKViVJ6UfDVLZPq0eMo0O3l3rEXT1yP1EGTt3NzJrbUaasV95HgVe8XEaFO9pQnOLXYhtxNB5pKwDKSmGfuV59Do5xYCy8o6j5cAiMr27ihsRH6/tlymie5iRYWRm5127xMxVu8OLdVtqoNcpjwapBJoH9JypnHXrzgZx2VR3VXijSRnU3nWVf9eHy4DdNRCFlasr2JeRT+vrR8jnke9bLdDHtX26En6rHer0tH62qRLi+f61fvBpuLJKADZuruVgng5t+h4cUSaaNF7XrK1ktngRoExvU5OND7v6+jiyKy5OyyXeVLIsQcI3Qd9Y8OuuYc+o9uqj5ufn47777mOGJwmnJiiTSkUifYPUXihlMriCQbj8QeiV0c9aklYkCWtrI2T+lHod3D4//03K/OnRt62qDj67A/rcwSfPLPodCsFeVQ9RQbS3sxuqhMHOgoIImVwB25EqMba5fWjtdEXUv6UNiUalQFWjDaJQUdXJGsKRoNerWb6JDvBElMZVVHZxFiTSdxtNarS2OWCze1hqItoINNfx4UPfILUXMr0RIXs3gp2tkGcVRN02ClLJx0Xq0zIZNWhosqG+0Yr0tOj3vDc02jh7GqkXVadVoqPdyQHhhPHR7yunHjzSTo0k4UP3kuYB3VsRoLFut3sRb/6y2qsXvUFEnSDbEPIAAQcgUw+RpVEAfovQ3lTyZZFAvo98nCg4Sssg14WrbAZCaTLC09CEgN0BpTn65fA1tRbuoIlU3UD+t9vq4WBQhEY5+S96tpFaP0wmNZqb7TxX6UBMgoQTRW9sOJw47ual3/zmN1KQeoqjN0CN1L7MXQ4yQFQbvkIXPo0O9fRG9UXve0qdmN4y+l65UomQj/qP+oPuJV0K7eBNVDRA2m4ypQLBCLb13jtFnyxwNKFSKvjE1x8IDUmOoteKI36gMsahspKUhaCAQlT/lsGgGvq++YLcj0T9UiIg0+rCpY2REPADpJGrEqMfSdnA3jk5EPSsFXIZa76Kso3Q2/85sPqg799EG5SN4bkaoaKG7yfbJma80ffSeCd/EQlkm1aQbbxiMmPnEJk/GocyMeXS5LvouQ7t44LQCiTwUhoNCPWwig4ErbW0rsnUYvwIHRwNlcw96n8FjTka6zSshvRxCnlMkMaNLMhi4PrfwwmxbLz11lu46qqrMG/ePNZQ7XtJGPmI1yi5zNfXsynqC28gBI1czqRKIpA0dyo0KYnwtLQP+szd1gF1ghnJC8WMw7jJY7jE19U82DZflxVKg47F3UVAn52OhKnj4GntGLRw+e1OyFVKpC2eI8Q2jVqB6WNT4HD6jm7Ee+HzB/m9mePF9M4SFi7I5d7FgRs5CiSo32fh/Fxhts2emc3Pk3pU+4LuGfVNzZyRxWVoIqAaM4WJk6gntS/I3qC1C4rsAshTxPS9TZuSDr1WBavNM+gzYnVOSzNizOjhIwX6KhArJ5XmdXW5Bn3W2eXislv6GxEYU5TEWeb2jsG20XijkttpUzOE2JaZbkJhXgI6u9yDfJzT5YNKKcc0QWRKMupLVaeHS3wHBg4hmrshQCNmLhDIh7ndvkGHI+TzPJ6AUB+XcPoCYihiBv2+oHJfv9WG+NPmCTsAnjEjE0qlbFB5by+HwZiiZKSlilEioHmo0ymZryCSj6OS4CJBPk7CqYMZM2agq6uLX0+fPn1QTHgy4sPjDlQfeeQR3HzzzUhLS2PNnDlz5iApKQmVlZU477zzTtgQCbEDvUqBNL2ayZPcgSAzsvqDoTADsAzINmk54yACJPEy6tYrEPT74ahp4CDL73DBUduIoMuD/OsvgS4tWYhtCrUaRXdeA6VWA3tFLXw2B/xON1xcdmtB1oVLEDexSNip+ejbr4YmOR728hr4um1cQu1qboOrqRWpZ8wRFkQTzpmXi9REHRqpVNXphdcXgMXmQUuHE0W58Zg7SVygeuaSQuTnJ3DZIzGeUtBKlP/19Vbk5cbjrDNHCQ2iJ45P5V4kCmLINupZravrRmqqERddMFaYbcqiSVBNmIFgdwf3qoa8HoRcDgRbGyA3mKBdeomwTHReThzOOD0fVpuXS0bpwIHkYKhcjkpYr1g+QVi2gcq0L75wHHy+IMsN0SaYLnpNWRr6TEQpd28m94rLJnHQR+XHdM/o3hGpDI07YpqmOSECNJYuu3AcTCYN6httXKJM86G904n2ThdmTsvAJAHl0kehGwMoDEDA2hOw+oGgCwjYAVUKy9SIwtlnjUZuTjz7NPJtdN/I15HPy8+PZx8oCklnLWYZGnddI7wdncxe7+u2wlVVC21WBtKvWC7MtunTMvgwsK3NiY4OJ983Kj+n+0gHSpdfKo4teVRhIhadls8SXL0+juYEzVuNWsls9ZJEzQmSKYm8YgzLly8/Sq57ySWX8O9DXVGTpxk3bhyX/15zzTVMqrR3714UFhbi17/+NTo7O/HYY49hpEGSpxkMolhvdnjR7PRwFpWmh04pR6ZRgyStuFJHAg3Zunc+R9XL78FZ28Tv6bJSkX/dxci7+vyIPXHRBBEqlT/zBmzlNVxSq0lJQN6V56Hgpks5mB0uHMuYa9++D2VPvgbL/iMI+QNQx5mQdfFSFN3xLSgNYnUGa5qseG9tJUqru7i0kDbFsyak4tIzRiHOKObEvBfNLTa8/uZ+JpKhDAOVGU6flsmMjsTsKBK0sXz9rf3YsrWOAwfSAx0/LhXfumoyb1ZEjreQ2wX3mg/gLd6AkNPO5b6USaUgVVU0CSJB2aIVnx7BF6sr0GVxc2VmTmYcll84FvPniJO46vVxX6yqwEcfH0ZLD+FJWqoBF5w3FmefOUq4diRpRr7/wSHUN3Rz9j4hQYdzzhqNCy8Yy6X8Isfc/pIWvLOiFJU1XQgEQjAa1Vg8Pw+XnD9WuAZtyNcJOEsAH1XdBAGZMhygGiZAJherQUtEWeTjdu/50sfNnJGJb105hSsMRMLb0YX6519B1/rNCDhdTJ5onj4FWTd/G4aiQqHjjXzuW+8exPoNVRykUknt6NFJuOryiZg4QdwBK4F63T9YUcq+xGJxcS8tscNftnwC64BHGyNenqb5c/HyNOnnjLj7901x3IGqXq/HoUOHkJeXx5P3iy++wNSpU1FWVsalwB0dHRhpkALVocFZ1Z6SRwpUIxEsiULQ52PiJBrCpJ06nEHg8SIYCIRt8/m5HDgaQeCxjjm6XySXQ9leKgmmYDWW0NblhN3pQ2KcVniAOhB0at5lcXFpZkpybPXqU8BKJV3Ut0rlZsMdzByPjyN2XyJOop5UKvcVHWj1BWUaKFtJG8ysPjIOsQDaaFIGhJCdZY6pDEhYksbKPaFURhgNSZrj8XEkPeTxBpCabIBRUPl7JPCWK+igBYzIA4QHqAPR1u5gX0JZe2L9jSVQwOpta4PSbIY2c/jLuI/HxxFpXWurg/t96fAylnwcV2Q02ZhYKTs7Tpi0lRSo/m8Eqjt37uQ4kTBhwgTMnPnNqvWOu9GQpGgoc0qBam5uLrZu3cqBalVVVcSmbQkjG9SraoihzdFAJl3zWHElSV8F0nE1F+UjFkELqLFAbMboq5CSoEeKGDnBrwVt3GJt89YLCp7pikXIdQYh7L7HAtpYFuTH5oCjwDRWbaOAXgSb6bH6uKyM2NzIcQCjMIpjJPwa0AFcrB3C9UKdlMBXLILYwumKRVAlQeEwVtf8z0B0+a0sdg4/IqG+vp6rbTdt2oT4+PDaYLFYsGDBAvz3v/9FdvaJtTcc99Hx0qVL8cEHH/Br6lW95557cPbZZ+Pqq6/GpZdeekJGSIhtDMWOGQuQbDv1bCNItp2atsWqfZJtJwbJthOHZNupaVus2hfLtkk4NfCd73wHPp+Ps6mU0KSLXlNlAn0WtYzq008/zV9KuPvuu5lIafPmzbj44otx++23n7AhEmIPdl8AXW4fHP4g96ia1AokalTQRtDlijbKKzrwxcpyFO9pYuc7dUoG90iNHSOGSKkvSJvs85Xl2Lqtjvvgxo1JxllnjmbiBdEg8qTaNz5B48fr4LM7ETdhFHIuOwcZyxYJL1XydttQ++anaPhgFTyd3TDkZyHn0rORvfxMlv0RXR66em0l1q6rQnuHA0mJeiw+vQBnLi2MSsnj1/WTV3Q4UNZmR7fbz/NzVJIBRSlG6ARXQ9DcbFuzGU3vfgZbaTkUeh1SzzkdWZefB226QGKbHuw72IKV1Bdd1sHsnbOnZ+LsJaOQHUEnNNro2lfKc7Vt4y7+PWXRLOReeS4SpowTbRpsFbWoef1jtKzawi0YiTMnsW0pC8Qz/7d3ubBmRx227W+G2xNAfqYZi2dnc7+7aB9HJbUrV1dg/cZqJp/KzDBj6ZJCnH5afkS9y2iXh67ZWI31m2rQ0eViH3fGwjycsShfmFRTL3z+ANavr2YfTGX6ZrMGixcVMJGd6Cwm+bjNW+uwek0Fqqq7OHt52oI8JqhKjoHqm917mrByVTlKj7RzhcbcOdm8V4qkhSxBwjfBunXrOB4cO/ZLEkd6/eijj2LRokXR61E9FSH1qA5Gl8eHJoeXN8G9fanUr6qSy5Fj1AgtBy7e3YhHn9gKi8XN/XgEh8PHbI933T4H8+bmCA2g//7gRjS3OqDXK6GQy+FweLm88IbrpuPcZcPH+vt1Y85R14Sdd/8W1sNVHDAQIYXf7uAgsPCWyzH2BzcI28h5u7qx43u/R+euA1BoNJDrNCziTsi+5CxM/r/vCwtWicnxoUc2Y8fOBsgVMtbOo8CVevRmTM/CvT9aICxYpfm5pboTZe121olUK+RHWbpTjRosLUphFm8R442Wlqqn/43aF97gYEZpMrKOb8DhhGF0Pqb841fQ54ljOl25rhIv/2cfXG4fDHoVkwKRRFJqigH33DUPY0aJk25oWbMVe3/xEB/YqExhIhufzQ5NYhym/vEepC2ZJ8y2rr2lKL7nATgbWqA0GSCTK9g2kt+a+NPvIveKc4ftu79uzDW1O/Dwq7tR32zj4Ip0LJ1uP1QKOS46oxCXLBVHREW97X/9+waUHm5noiJilSYiHrKHgtXv3jpLWH80+bN//msrivc2sQ1079jHBUN8ePODO+YwU6wI0GHvv57ejrXrq1h1gCSQiOyJmOGJ8fz//fg0mM1aYT7utf/uwwcrDjFxl16v4r5yund5eQn46f2LkJ4mjgPi08/K8PK/d7M9JFMWCAbhdPqRnmrA/T9eNKxke6dkj2rrKvE9qqlnxuz9GzNmDF599VVWg+mL7du349prr0V5efkJ/bsn5BWp5vjzzz9ng15++eV+l4SRD18wiBanj52wWi6DqufSyElbNYhmp1dYCQnpRb74cjFr9uXkmI/2DNJrOhF+8ZXdgzTNogW6Jy+/ugctrQ7kZJu5zycxUccse3S3/vPGPpZxEIWyJ16DtbQShsIcJlHSpibBWJgLhV7LDMqWvaXCbKt8+T107twPfW4m9LkZ0KYkwlCQw/1I9e+vQsvqrcJsoywqBalJyTomySCGUyKPSU42oHh3A1avqRRmW73FhfIOB7RKBeK1Kg5KTRolzBolWu0elDSHiXhEwFZShrpX34Fcq4GhMA+alCToMtNgKMiFo7wKVU/9W5htJFny37cP8kY0J8uMxAQdZz/oNRFSvfL6PmE+zu904eCfn+EKA+PoPGjTk/mi1/Tewb88A7/LLcQ20q4s+cszcDW2sj26jFRo05JgGp3HDOel/3wJrgga19HCuyvLUddsQ0aKAckJOiSYtchKNTIT9icbq1HVQ0wlAis+OozSw23IyDAyiy75kawsM0xmNdasrUTx7jCDvQis2VDFQWpyoh4ZZFu8FhnpRiQl6rBjdyNnWUVhx856rNtQzXIvlAWk+5aebmQ934MlrfjokyPCbDtS1oGPPjkMjVbJz5JsI1mwzEwzqqu78ObbB4XZRnsN2nOQF6M9CO1FaE9CexPao7z0ym6pFFjCScXf/vY3fP/732cypV7Q6x/+8If4+9//fsL/7nEHqh9++CGTKJ177rn43ve+xwb0Xj/60Y9O2BAJsQObN8ABKQWnfU+f6TW9RyzAzh4m4Ghj3/5mptFPTTUMsi0lVc+MhVQOLAJVVV0oK+9AUpJuEKsebYKtNg+276wXYpu7rRMta7dx4DcwM0nvkR5t0xebhNgW8HrR8OEaKI16KDT92TlVZiOC/gCXKosCbZLoeQ7MmlKWXK6QYx2d9AtCVaeTM4GaAWWDpHOsUshQ2eGAv6dVI9poW7uZx5U6uf+pvUypgDopER2bd8Ld3CrEtp27G1lfcGBpHj1n2pxTCV95ZacQ29o374azrgm67PRBPo7eo8/aNxULsc1y4Ai6D1VAm5E6SAZMl5HCrKxUDiwCHRYX9pW1M1P4wMxkvEkDp9uHXSUtwqoyqNyXMm4DmZupdJWyhpu2iAsG122q4eof8ml9QdUjNCfWb6kVZtumzbWcCaSMYF9QRlqrU7J/ptJgEdi6rZYPxuMHkNhRGbc5ToOduxo4ky4C23bU854joo9L0qG8vIP9nITjALljoTqqiDkkJCQgMTGRL+It2rNnD+bOncvaqnTR6+LiYtxyyy0n/B3HXcvx4x//mL/wgQceYKkaCaceqGwwPB8Hzwpa/n0IwS/oJI56fGhjTovUQLB+Xyj8NyJAAulU9qPV6gZ9RosD3U0qVxYBb6cFQbcXqsS4QZ/Rc5YpFHC3ipGWCjhcXIJM5ciRoNBq4GoSE9AQqCdVo41cPksbu45OF59MiygptHv9Q0oNUJm+LxiC1x+CUoA6h7etMzy2ItwXhU4blprosAjpVSUfQXZFundU9tjZ6UK31QMR8HRY+GckuS1+Lxg6+jfRBj2voNvDVRgDQT6EdlKibOu2e7kcNM40uGeRxyFksAh6ptT+QeWX2iFaBCh4bevRy402yHdRT6p2CB9HZcpUZSAKrW2OiOt9r/91Or1wkX60OfrtSHTf6LAyko+jIL/b6j0q9SPEx/XsPSLdN9qLdHeLmQ8STh3885//HPbvOO5AtaGhAT/4wQ+kIPUUhrLHsUXafFNuRg4ZVIL6fOITdNx3RCfUmgEEDxQkkllUtiQCVPZDC2pvP0hfUHDd+zcioElOgIL6Ph0uKAcEhMwGGAhAl54ixDbKpKrMJrjbu6COH9x3QRtjfY44IqrUFCNKj7RF/Mzt8jPxjqi+NyrzbbVF3mz4AkFoVQqoBZG0aFKTEQoFI/qRgNPFJcEDs63RAm0cyS6alwM3cjR/1RqlMD+iIW0mmQwBj3dQhQG9J6MewlQx902TksgHR36HCypT/14t8iEEaikQAcqaqlUKuGltGJC1ZB+HEBIFPVMj8TroVZzhMhoHH0DQ2kXlwCLA1UhJelTWdCGS8AuttfkCZYioxJe4HyLB7fIhIUEPnV4MR0BKkgHBQGQf53L5+dArXkCQSuj93iF9nFqBhITYlDOTMHJw4403Dvt3HPcOZtmyZf3qjyWcejCrlVDK5fAG+9OZ02vK0OiUcr5EYMqkNGRlxaGtzTnINhLbTks1Ypogdt38vHiMKUpGR0e4HLOvbXRaThqXc2aLIY/RJCUg7cz5TFpEhDZ94WntZMKWjGWnCdPDzb7kTA5eAu7+QZfXYoVMpUTm+YshCmecXsCZeiI+6Qsq+aLnzJ8LQmGigTch7gGlb1QVQXO1MEl/9OAp2kg5cyGPK8+ATD2NP8rwJy+aA22aGJbu2TMyuVyvtd3Rz4/Q82zvdGF0QQIKBemXEnOuMT8LrvrmQT7O1dAMQ14WkudPF2Jb3MQixE0eA3dTK/ek9rWNyJUokE07UwzRU2KcFtPGpsBq98LfxzZCl9UDg1aFWRPThNhGQcHpi/I5eCGehb4gvgXKqC5ckAdROOO0fFCHwEB+B6eLuCqA0+eLs23hwjwupSWW5IEBNLE6L1lcEK6mEgAibtTr1ejq6l8pRaXc9Fznzs4aVBYcLRC7L+05aO8x0MfRHoUUEvJiVAc5ZiGTi79GCNxuN5NQ9b2illG94IILcP/996OkpASTJ0+GStX/JItkaiSMbNDGNl2vRpPDA08wBIUsvBGhpV8tlyPdoBaWQaIF/dabZuKfj25GbW039/xQfQuV/tAJ4i03zxTGwEr35KYbpuOv/3Cgrq4bOh3198h5safT9G9fO1WokPqYu65Fd0kFug8egUKjhUytRMDuhFyjRtEd3+JNqCgUfHs5OnYcQMfWPZAplT1ZGydnj0j2InVxfxa5aGLx6fnYu78JW7bWoYtL5JSctQkFgXlzsnHGYnGBalacFuNSjShtscPt83FfKjF10jlJulmLienimAFNY0ch75ZvMfOvo6IGCqMeIWL9dblhGjcaBXdcL8w2yqhef/UUPP/qbtQ12JihmzZwbneAyWRu+NZUYT6Oxv7EX9yB3T/5G+xl1VAawz6D+n0pk0qf0d+IAN0TYvbd9aM/wk7PVK/jkl8q3ad+8vH33wqtoCw54fKzi5hMqabJytlVpUIOl8fPry87czTyMsTNh4suGIdDpW1MAESBFwWvFLhShdCyc4owfaq4qpHFp+Vhf0kLtu1qhLzbzQy/5OMI82Zl4/QFucJsmzUjE2cuHYVVqyu4VJXWVY83gIA/iKmT03HesjHCbBs9KhGXXDweb797kNf8XtZfrzeIotFJuOLyScJso70G7TleeKkYdfVWZkumXl/KphIp1Y3XTxcu1yTh1ILD4cBPfvITvPHGG+joGFwFEeipuhl2eZqvklqhQX+ihoiEJE8TGU5fAJ0eHxy+ICgpY1IpkKBVQSOIQr8vamstrEdHUjW0KZ86JR1nnzkKhQXiNkm9aGmxY9WaCmzbXs8L6oRxKaz3NmH88PbiHcuYc7d3ov7dlWEdVZvjqI4qBYKiFy2SuCCG34YVa+Fp74KxIBvZl57NmV45978JtM0XwPoN1Vi3oYoz98T4SwEs6R8OLEGPNsiFV3c6UdbuQLfbF9ZRTTZgdJJxWMt+j2W8kW2dm3ai6cMvYC05AqVej9Rli5Fx0dmcfRMN0hZctb4Kh0hjUCnnTOuZpxdwZYZoWA9Xovatz9C6IVzBlEo6qlcsg3lsoWjTWOqKbGtZuTmsozp7MnIvX4bEGROH9XuPZcxRH+r64gZs3dfEwVZBVhxOn5mFKWOShfs4u93LMitErGTtdiM7O44zgvPn5Q7Zax4teLx+bNhSG9ZR7XQhJVmP0xfkYdH83EEEUNEGSYFt3lLL966hwcoMwItOy8eSxYXhw2rBPm7nrkZmbq6s7uQM6yLSn11cIKQ3dSBKDrVi5aoKlJS2QaMO66ieuWSUkFLzES9P074GZrO4tcFqtSMueUnM3r+7774ba9aswe9//3tcf/31ePzxx7ld9KmnnsKf//xnXHfddSf070o6qlKgKuEUgTTmJEQT0niTEG1IY05CNCGNt5OLER+odqwVH6gmnRGz948UYUim9IwzzmD7iO139OjReOWVV/Cf//wHH3/88Qn9u9LMkyBBggQJEiRIkCBBggQJJ4TOzk4UFoYrfihQpd8Jp512GtavXz+8PaqPPPIIvvvd70Kr1fLrrwIxAks4dUDlmLbyWu4VNI8rjCiZIGEwQj4fPNVV/FOdkwuFyYRYQSgYRKCpFiGPG4rkdMjNsUOoQAUe1M/ot9mhTU+BNkMM+clQCNnbAY8N0BghM4phSR4KnTYPuuwe6DRKZCTohJc5DpRuamqycX9vbk7cIJ1L0eWY9Q3dbBMRookuc+yLkNeDQHNYw1KRnguZWkxv6lDl8DX13QgEQsjKMME4gOlcJIKhENrtXma+TtCroFeLLc8f6ONs5TXwW+3QZaZClxF9eaavso3mKc1XYqjPSI+ddYvgbGyBu6kNqjgTjKNyY8rHkV5qr4/Lz0sQXsrdFza7B/X1VqhUcuSRjxNEPjXi0atnKvL7YxgUpFZVVXFmddy4cdyrOmfOHHz44YeIjz/xfeYxee+HHnqIa4spUKXXQ4GchhSonhoIeL2oePYt1L31KfcLUpMqSYSMuvly7huMpQUilkALvX39WnS98xZ8jQ0cFCrMcTCdeTYSr7waco3YjaavdC+cn7+NQGMtEPBDptVDPX0BdOdeAblebF+e9UApKp94mX+GvD7W2kxaNAcFd90oRGuzL0K2VgRLPkWorQwI+ACFCrLkUZBPOBcyc7pQ2ywOLz7cVoMD1V3w+AJMIJOXasQFc3JQKJBMieBwevH6WwewcXMNHE4fE8fk5cTjiksnYub0TKG2Efvqu++XcK87aQ7SxpI25ssvGs/9xyJ9HPkN75bP4d36BYLdXfyePC4B6vnnQD3vbMgEliGSj1u/tRYfflaG5lY7s8XGmTVYeloelp87dkjNy2ihqt2BrTWd6LB7OWDVKOUYm2bC/IJEaARv0Dt3l+Dwwy/Dsv8wgl4/lAYd0pbMxbh7bhIm69OL2rpuvPbfvThwsIXnBj3HKZPTcd01U5l8RyRIR7v0ny+hde12+J1uyNUqJEwdh7E/ugEJU8YJDwL/+/o+bNpSx5quvQdeV10xGdMFKRD0ZUYmoqfVa6tgtYZ9XGaGCZcsn8B9tNI+TsLJxM0334y9e/di8eLF+OlPf4qLLroIjz32GHw+Hx588MET/nelHlWpR3UQaEgc+OOTqHntQyh0OqgT43jj5GnrhEwuw8Sf3YG8q88XbWZMwrpmFdqefIwJRpSJSYBCjqDViqDTycFq2g/uGbbF4evGnO/wfthfeQQhpx3yuERAqULIZUfQYYN6wgwYb74XMqUYxmT7kQrs+9H/wdPcBnVqUpj11+6Er7ML5injMeXh3/MpugiEnF0Ibn4OIVszoDEDSg0JWgIuK2TmVMjnfwcygxhiIGI0ffLjQ6hsssGoU3I21ecPwmL3It6oxnfPG4fcFKOQ8eb3B/D3hzdh+84GmIwazriRbEhHpxMGvRo/+t58YUyn5OP+9cwOfLGyHFqdkgMtYv3t7HRDoZTjtltmMuGIKLhXvwfP6nchUyghM4bXpJDdilDAD83SS6Fdeokw21ZvrMYLr+3lZ5kYr+PNL+mD0lg8a1E+bhtGNtGvG3NVHQ58WtIMjz8Io4ZY1wG3LwiPL8gEYxdMyoBCUKbLcuAIdtz9W7hbOliWSa5Vw291sFRT4sxJmPPUb1nOSRQB4O//tBb19d1ITNRxVtDl9jPLeW52HH71iyXCGOu93TZsu+2XsOwthTopAUqTHkGXh++jLjMFc/7122EjGPvaNdUXwJ//vgG7ihtgNmlgMKrh9wVZ/oW0c398z0JmJhbl4x57chuTPOl0KpjNGmb97ex0Qa1U4PbbZmNxlKXVRnyPauc68T2qiYtHzP2rrq4+2qc6ZcqUE/53RlYEJSEqsJVVo+H9VVDFx3FpEgUNSr2O9ftIC6b82TdYOkRCfwS9XnS99TpCfj/UWdmQ63SQqzVQJqdAkZAAx6YN8JQdEWIbazCueh8hpw3ytCzIdHrIVCrIzQlQJKbCd3gffIf2QBTq//M+PE2t0BfmQmU2Qa5WQ50YD11uNqwHDqP1s7XCbAvW7AgHqcZUyDSGcPCgNgDmtHCmtXqbMNuKKzpQ1WxDSrwWZr0aKoUceir9TdShixhG9zUJs23Pvmbs3tOE1GQDEhN0nKEhiYTsTDPsDi/e+aCkn75fNFFV1YUNG6tZZ5A24Gq1ElqtCpmZJt6cvvteCWcjRCBo7eJsKpX5yhNT+Gff1/QZ/Y0IEIvue58c4UxlZrqJAxp6rslJesSZNNi4vR7Vdd1CbKOxtKOmC25/uNyXGK9JD5wCVpNWiapOJ2o7xa1blS+9C3dzO5eskpQPtdFokhOgz89C1+4SNH5y4j1c3xRfrCpHQ0M3srPNMJk0XP5OgVd2lhl19d1YvaZSmG0NK9bAsu8wDPnZ0CTF833j0t/RuXA1tqLqlfeF2baruBF79zUxSziVSpMMErEQ03202z145z1xPq6srIOZkokhOTlZz/OUpPsoO04l8W+/V8KBtoTjgSwGrpGD/Px8XHbZZd8oSD3m0t977733mP/Bb5LelRAbaN+8G36bE4bRg7XT6CTYWd+MzuISlkuQ8CU8Rw7D19wMRVLyoM/kRhN8XV1w7i6GdszYqNsWbG9GoL4SMnPCoGyHTKNFKBDgQFU9eXbUbQs4XejYuB3KePOgkkYq8aL32tduRtZVF0EIGvcBCg1k8v5lgzKZHCGlFiH6fOJ5QkzbX9XJbSsUoPa3TQaTTolDtRbOdFGmNdrYu7+Zhe/pNH+gbUkJOg4WGxqtyM6KExJEk4YlbeAGIilRj5ZWB0vXiMiG+CsOIuSwQZ4yONssM8Uj2N7Ef6OeflrUbSur6ERbu4MD04EwGdWwNFqx72ArCnKj3/fe6fShze6BUa0Y5OMoaLW5/ajudKBAQGbQZ7WjfVMxVycN9HHM+yCXoWX1VuRdFf1KJQqkNm+tg1anGtQ7Tr9rtEoOeK6+cjJEoHnVFtbWprWgL+g+quLNaFmzDX6niw/To43de5sQ8If4wKafbTIZEhK0KK/oQHOLXUivL/lft9uHlJRIPk6H5mYbyso7hl02T8L/FlatWsUtoocOHeLfx48fjx/96Ec466yzTvjfPKbdy+7du/v9Tqlcv9+PsWPDG+4jR45AoVBg5syZJ2yIhNhBwOPlg5tI5Vsy6vEJBBCkv5EwKKNKAZ8sguYn30sZkaOIuW9E6hQKBiBXRJ7yZF/I54EIBH1+hAJByFVD2KZUIuByQxRCfh8JSEf+UCYH/F7e7Ino96GeVMUQtlGJoy8Q4tNzEWp+Hk9gSPIHKq+lUlvqhRMBry/Qw4sx2D6lUo5AMASfINvg6/ERke4dvRfq8zcC7hs9N+qDHmyaDHKZjP9GBKiskTK9Q7bayAA/iW4L4nwIko8bEGz19XHUeykKVD0Q6ZkS6H1R1QWEgMMF+RC9xbRmhPwBXkNEwOP2D7k0UJAfDPiF+RHyrTQnI/k4tk2g/5VwauKJJ57AD3/4Q1xxxRX8k7B161acf/75HLySzuqwlf6SgGvvRc2x1ChbX1/PAStddXV1WLJkCS644IITMkJCbME0Oo9ZfgPuwYGLz2KD0mzkshsJ/aHOyYHCYETAZosYKFJQo84Vc98USamQG+O4H3WQbcSGEgpBkZkvxDYaT7rcLPi7rYNtC4UQdHtgniSOMEOWkA34hthE+tyQJYpjn8xLM8LrD0QsL3O4/Ugxa2HQiuk7zuvJqtGGaCBsNg/3TKWniek7zsmO42cWKaiifkujQcV/IwLytGxApaZd8OAPPS7+jP9GAKhsm0obiUBmIPheyoAcQcQ7cToVdCoF96QOBAWwFOAnG8SQ2akT4mDIyeD1cygfFz8l+pU2BJoHRaOT4HBEPvxwOn0oKhJH9BQ/dSwCLk9EH+ezWGEsyOZSahHIz09gMrHIPs7LZbcpqWJ6e7N7fFyk8l6avwaDmv9GwnGADqZFXzGMBx54gANS0kwlYl26XnvtNX6PPjtRHPd/9T/+8Q/86U9/QkJCwtH36PUf/vAH/kzCyEfKaTNhGlsAZ20Tgl7f0fepvMbT3onUxbNhzBezUYplqFJSYVh4GoLWbgRdX/ZCUc+qr6mRA1nD7LlCbKPyXs28pYDXg6DTfnTRpyxrsK0J8oQkqKfNE2ObTIbMy89HCDJ4Ozr72BaEu74JqngT0s5fClGQ581hAiUiVTpqWyiEkMsSZv/Ni365dC9mj0mBSadGu9Ud3oz3yl84fXT2gAUT0oSRx8yfm4OUZD0am60IBIJfsmI7vEzUsmRxAQc9IjBrRibL5DQ32bk8uRculw/WbjfmzM5GWpqYza8itwjKvCIELe0I9cmc0uugpYM/o78RgbQUA2ZPy0A3kSe5v8xi0T2kEsfcTDOmTxEjKUWMvhPTzfD6g3D32ZzTvLA4fTBrlRiTKuaZyhUK5F51Ljk1eDr6+JFgEM7aRu69zL5oCUThrKWjoNYo0d7h7OfjqMybylpFEotlLz8L6ngTnHVN4UPVHttoL0IVBrlXnSfsoHDhglxuHyBZmr4+jgJBykLTfaO+UBGYMyuLe4ybmwf7OJvVi/nzciKW8EuQcKKwWCw499xzB71/zjnnMAFU1AJVYr9qa2sb9D69Z4uQSZIw8qDQqDHtz/fBPK6AFwfbkWq+PK0dHMRO/Nntok2MWSRffxMMc+ch0GWBt6Ya3tqaniA1F2k//DHkenELg3bx+VDPXoyQy4lAcz38TXUItDYyA7Dh6jugSBjcWxstpF9wJnK+fRmXcDkrquEor4KzsgZKkwFF998J03gxG3NG6hiWoQH1qNqaEbI2AbYW3iTJx58DWdp4YaZlJRlw1aIC6NRKtHS50NjhQFOni8t9F0/JwLxx4vqPiEDpe7fPRUqSAY1NNtTWW1DX0M2Zm8Wn5eGSC8XdN41GiR98bz5yc+N4I1dba0FtbTe6LG7MmJGFG789XZhttOnWXXorB6MUmAZa6nm+0mtF3hj+TKSsxPVXTsaMyemwdLtQ29DNF8nUEDvs3bfOgkagZumsvASMTzcx6y/J09DV5aAgVYVzxqfBIKBXuxc5ly9D/vXLuW3GXkZrahXsFbWcDZz0q7uGjbn2WDBzRia+deVkLt2uq7PyfKir6+ay32u/NRXTBLFzE+InFmHiL++Eymjg+8X3raya14rCWy5H9vIzhdlGRGx33zEXiYl6NDba2IfQfaMs9NIlhVh+kbhKIOIGIB8XDlbDttFztVjcmD0rC9++Zpow20a8jqrIK4Zx8cUX49133x30/vvvv48LL7wwevI0N9xwAzZs2MDZUxJyJWzbtg33338/Fi1ahJdeegkjDZI8TWQQsy8RGXQfLOfe1KQ5U5CyYDrkKjEnhCMFlEF17tsL1949YTmJgkIY5s3nsuDhxLGMOZru/qrDrKca8rihSMmAesocyM3RJz+JZJv9cAU61m+Dr9sKbUYqkpeeBl2mmAzNQBDDb6jpAOC2ARojZBmTIDPHhm2dNg/2Vnagw+aBTqPApLxE5KYYhjWgOVYf1211Y+v2eiZO0mgUmDYlA+PHprCsiWhQ0Lx9Zz0TO1Hf1qRJaUygRH2qokE94/7SPQjUVfDvitzRUI6dCplKrBYzgaRp9pe04kBpGwKBEAry4jBraiYMw5whP5YxRxnUxm43ajoc8AVDSNSrMTrFAL3AALqvj+s+WMYEQESwpM9OR8Y5C6HLiA1CG2L43ba9ng8hEuJ1mDc3R7iGai+cDS1o+nwjM/2q482sP2sePyomfFyXxYWt2+r4QE6rUbJ+6vhxKTGhU0o+btuOelRXd7FfmzwpHZMnpQnxcSNensaySbw8TfzCmLp/jzzySL/79Pe//x0LFy7E/Pnzj/aobtq0CT/+8Y/xy1/+MjqBqtPpxH333Yfnn3+eRVwJSqUSt956K/72t7/BYBBTj/9NIAWqEk4FSGNOQjQhjTcJ0YY05iREE9J4O7mQAtVTL1AtKDg2LV46tKmsPDGZq+M6YgwEAti5cyf++Mc/clBaURE+6R01atSIDFAlSJAgQYIECRIkSJAg4asQInUEgVnyUAxk6Aeiqqpq2L/juAJVkqChpljSx6Eo+puKuEqIbRA5wMFDrais7mJClonjU1GQP1iHUwSoEKCsshOHKzr596KCBIwdnRQztrVaPahqtzMbYFqcFvnJRmGkNgPha26Ce+8uBN1uqDKyoJs2EzLS8osBdHY6sbO4EXa7l0kqZs3IEka4E4mpdseuBu7xiYvTsG1xcVrESpl+y7odcDW0cM8bEZ7p0lMQC/D5A9hX2oamNgfUKgUmj01GRoq4U+m+IDIxtFcgZGngHmRZymjAnBEzfqT0cDsOH2nn38eNTcbYMckxY5u1tBId2/dxr2DchFHcGiJXii+vJTQ221hH0uMNIDPDhOmT06FSRZY4iTaIsGhXcQMcDh9SUwzcHzpQa1gUurvd2FncgO5uDzPWzp6ZBZNJfKk5wWd3oHXtdria2qCKMyLtjLnQpopjI+4LknnZvacRDY02Jp+i0l8R2qmRQERK+w+0oLqmt/Q3jRnZY8GPSDh1Eeop1j0Z4+y4V5VJkyZx+vZY070SRiba2x147KltLHpPjo7GnE6rxNzZ2fjOTTOFLqzEGvrUK7ux92ALPF5inpRBrZJj0rgU3HHDDMSZxQUPRGLz2f4mHGzoZvZJAsnTZcbrsXxGNhIM4gJC0ni1vPUf2D//CEGH/Whzvio7F0l3/BCawtHibAuF8MlnZXjz7QO8WWLfJpMhLdWI278zC1OniCPzIGzaUouXXi5Ge6cTMsjY3sREHW749nScfpoYWZ9etG/bi/2/eQSO2qbwG6EQ1AlmjL79Wyi44RKhG5KaRiuefn0v6pptYabkEGDQqbB0Xi4uXzZmSO3GaCDk6ESw+HWEOmupsTysT6rUQJY1BfKpl0CmEDdXrVY3Hv/Xduzd33RU61CtDvf33nX7HJgF+ji/y42Df3gSjZ9uYI1L1txWKZEwbTym/enH0GelCe2dff2dg/hiTQUcTh/7ESrZzM0y467vzEZ+j2SSCND4X/HxYbz9bgk/3/C0lCE93Yg7bpvDAYRIrN9YjZdf3Y3OTldYVxshJCfqceMNM7Bwvlg5urbNxdj/28fgrGsOvxEK4XDiyyi661rkX3uRUB9XUdmJx57Yyv29vT7OaFDj3GVF+NZVU4T24je32PDIY1tRXtFxlPmX9m6nLczDrTfNZJ8iQcLJxMsvv8wVt2VlZfz7mDFjmMPo+uuvP+F/87h3CSRDQz2qK1asQFNTE9du970kjHyQQ3v0X9uw70AL4uO0yMmKQ06WmUkC1m6oxr9f3yfUvhf+uxfbihug1yuRlWFCVoYRRqMaO/c24+lX90TUW4sW1h9uxe6aTqjkMiQb1UgxaWDUqFDb6cC7u+p4IyUK9pWfwPr+m/xamZUDVVYOlMmp8NVUoePRvyPwDejDvymIwOPlf++B2+NHVpaZ9d3S0gxoabHj0ce3MhGPKFBG66lntqOr283ZmexsMzIzTay3+cxzO1FyqFWYbY7aRuz5yd84SNXnZMA4KheGghwEPD6UPvQCmj5dL8w2u9OLx1/bjepGK5ITdMhKMyErzcgb9I/WVeKLTTXCbAsF/Qju+g9C7RWA1gSZKR0wpQEKNUI1OxAs+UycbaEQnnp2J7Ztr+MNL7F20kWv6b2nn9sp1McdfuRl1L79GRQ6DQyjc2EcnQdNSiI6tu3F7v/3NwR7uCtE4NOV5fjgk8McuGRnmlj3NTlRh8paCx7+1zau1BCFzVtq8epre+H19fdxxDr9yONbOKgQBfJh5MvIp5FvYx+XYWKfR77vSE9WXwTslXXY87N/cJCqz+31cdmsrVr69+fRsmqLMNvoUPWhRzajptaClBQDP1O6dzQ7332/BJ99Ed6siwAdcJFth0pbkZCgQ05O2Da1Wo4vVpbj9bf2C7NtpCIUkgu/YhkPPvgg7rzzTpx//vl44403+CK5mjvuuIO1VE8Ux/1fTQbs3buXaYizs7NZQ5Wu+Pj4ftqqEkYuDpS08OacypJ6NcBo4acSIJNRjY2bazjjKgK1DVbs2teM+DgNDHo120WXXqdCUqIW+0tbUV7dJcQ2h8ePvbUW1vPTa5RHT3nVSjkS9Go0WlyoaLULsS3k88H2+ceQKRRQJCQetY1KfpWZ2fA1NcC5bZO4bOrnZfB5Azzmek+gVUoFsrJMaO90Ye364e+DGAorV5dz2W9GupGZYQn0Mz3NyNn9L1aVC7Ot4cM1XApHGze5umeuKuTQZaYi6PWj+rUVwoKaHfub0dhiR2aKgUt+2TaZDPFmLWdSV22t4dJMIWgtQ6irDtAnQaYMlzayL9EYAJUOobpihNxiDkeqqru4jDAxSQeD4UsfR68TEnX8Gf2NCLhbO9DwwWqo4kxQJ8Qd9SNKvQ66nAxY9pWifcseIbZRdc3nqyu4vDEhXvul/1UrkJlmREOTFdt2NQitGKGDSpI0OerjVD0+rt2BdeurIQrkw8iXkU/r6+PI59nsXqxcHeYjEYG691fB3dwe9nE9igO0jumy0hDweIX6uM1ba9HYaOXgvjc7SeOOqm3oGX/6WRl8fTR9ownyE5TtJT1oKkfutY2qMaidZu3aKg60JUg4WXj00Ufx5JNP4i9/+QvHiHT99a9/xRNPPNGPHXjYS3/XrFlzwl8mYWSgsqqLs6qRhKqprJayW9S3mpwcfQKtypouFppPTBjc40bBapfFg6oaC4oKEqNuW3O3C06vn4PSgVAp6DQsxMHq2Izos7X5mhvhb2+D3Bw36DNa9AmeslKYzjk/6rbRBomo802mwfeNFlaNWo6DB1sgCtTfo+85FBloG8lxHDjQys9WRPkZ9QjKNWrIIjBSqhPjWP/Y22mBJin6h4gVtRbOLPRufPvCbFSj0+JGU5sd+VmDx+RwI2SpB4IByJQRyns1RsDRDlgagfToz1XaXLrdfu7RHgjKqlq63Kis7EKhAB/XfagC3i4r9HlZgz5T6rRw+wOwHDiC1NNnR902kgbp6HIhLkJPJQWvwSBQVtmBMxdHv23J0u1GbZ0FZvNg28hvUMBKfBAiQL6LfBz5skg+jtZVqq4Shc7t+6DQaiP6OFWCGd2HyuG3Obg3P9o4UhbONEf0cWYtWtscaGq2Izcn+j6uvKITwUCINaMHgrgVqFqJ+lZFt9WMJIQg50vk98cyqMp2wYIFg96n9+izqAWqixcvPuEvkzAyQCeBtMGMtPkmjbpw34+Yvove76UD1KHiArlCkG2c+ehtIh9sQ6jnb0SAF3n66uAQJ8/0XHsC1mhDIZezaUMdipPJkTYC0bSPxn0k0LNWKMX1IJG+cWiIZ8rvy2Xinis9s6GHG/sWYQRjsvB4inzA0DN/Bc3VY/GtonwcZev5vlDUh/7jKtyfJ86P0H2jZznUXCWI6okOj3PZkHOVbBbZr01zdaisZNg2gT5OpUSIx1sEBHvmr7D9CN23yJ/R/aRlVyHo3tH3Drmm9tw3SXJHwsnE6NGjudz35z//eb/3X3/9dRQVFZ3wv3tCo9RiseAf//gHvvOd7/BFtcek6yPh1MCEcSnQahRMRjEQXV1hIfCxRclCbBtflAyjQYVum2fQZ1abF3qdEuNHi7EtM14Hs1YFu4cInvrD4wtAKZcjL2lwliQaUKZnQp2Vi0D34JLBkNdLKy60E6cKsY3KkCZOTIPV6hm0WSLmab8vyMyYojB7VhZcTh8v7n1Btjqdfmb/FUXmQZmrkN+PoN8/yDZfZzcSZ06COl6M3tq4wkQOHrwRSt8sVjcyUw3ISBXD/itLLgQUKsAfofTNbQN0cUCiGAKZCeNTucyXmFcjZeYoqzphvBhG54Qp46BNTYSnLcy23heU1VJoNUiaNUmIbVmZZmSlm5iVO1K/HjPXjxNz36hthlibu4fwcYFAiJliRYB8F/kwWu8H2kY+z+3yYdbMwRn0aPq4oNeLYKC/HyFbvV3dSJo7FSqjGHnEKZPSOBjtJTzrCxqHOTnxwth/yY9QObIz0j7O4kJSkh6jR0W/KkPCqYvf/va3+PWvf819qb///e/5otf0/u9+97voBaqko0q6qRScdnZ28kUNtPRecXHxCRsiIXZQNDqJAwOSCqGNES1WtJgSrT71lC07Z7QwyvqUJD0WL8hj59tpcbFtdBHpg83uwcLZ2UywJAIalQJzCpMQCIZgcXr5Jy2m1Lva7fJhVKoRuQLKpQmU5TBdeClkKjX8zU3cs0q2Be02/l0zeix0s+ZAFC44bwyXxRG9v8fj7wkCfWhosCEn2yyUWfesM0chNdWAhgYrXO7wfXO7faivtyI5SY9zzhLHlpx5wWKYx+TDUVUPn83BtgXcHjir6lnCoeD65cJsmzEhDUX5CWhpdzKxEgfP/iBaOpxQKOU47/QCcVmkxHzI0sYCLgtCHns4GxgMIOTsAoI+yAoWQKbSCTGNNraLT8/nvmhiYO31cfTabvNi8ekFSE8T4+OovDL/28s5cHA1tvIBCWW7PB0WuJvbOKhImDFRiG00li5cVsRlvs2tdpZFoudKPZb0+9iiJMwQGAxedME45nigEmWvN+zjHA4v+5W8nDhmYhWFZWePRkqSgX0alZ2TbeTryDYiCSIfKArZFy+FaXQuHBV1/Xyco7IOmoQ4FHz7YmG2zZmdjaKiZDQ12WC3hw8hqCe1qdnGQSI9c1HVZxMnpGLqlHTuf6ZeVLKNWrqoHDngD+HC88fGjCzSiEFIJv6KYVx++eXYvn07kpOT8d577/FFr+m9Sy+99IT/XVnoOLvQFy1axOndZ555BsoezTS/38+ZVZKtWb9eHMvkiYLYiuPi4jgrbDafnOxDMBhEa2srUlNTR2R5BQUJL7+2B1u21XEPISWMSFft3LOKcMlF43kzIAq0ELz54SGs2VzDLIU0gs0mDU6bm4NrLpkAjVqclh+VSW0tb8f2qg7YXH6m+NeqFBibbsY5k9KhG0bbvm7M8cZo3Sp0v/s6/G2tdJQPuU4PzcTJSLz5DiiTxepu7tjZgH//dw8Hq5RF1WiVKBqViNtumYVcgbISBCIXe/7FXaiusfDpOW1CqO/o5htn8Mm1SBAr5v7fPQ7L3lL4nW7I1UoY8rIw7p6bkH7m/GH73mPxcZ3dbrz07gEcLO+Ay+PnrFZyvA4Xnzkai2dnC5WVCHmdCB5YgVDTAcBLMisyQGuGrHAh5KMXR+yJixZojL32371Yu76apUx6e96WLC7ANVdPESorQZmtiufeQs2/P4S7rYPLWYlcKeOs+Zjwk+8Oa6/gsfi41eur8c6KQ3ywSplK6rGcNCEFt357OpISxVS09GLbjnr8+7U93LdIQQOR3IwpSsJtt85CtoBe7YHMvy+8VIzauu6jPi4/Lx633DST9XtFwlpWjQO/fwLd+48c9XHGgmyMu/dm1lMV6ePa2h3MmHywpIUrbKjklkgBr7h8EpaeUQiRoMP7F14sZv1vYrymoDkxQYcLzh+Liy+MfhA9HHvtaNrd2bUDZgG90L2wWu1ITJgdk/fP5/Ph9ttvx69+9auTLl963IGqTqfD7t27MW7cuH7vl5SUYNasWXA6nRhpkALVodHcYmeGSXK+tFiJ1CgdCMqollWFS1lH5cWz5luswOX1o5Y2SsEQ0uK0SDIOfwb6WMdc0OWEp7QEQbcbqoxMqPIKYkb8mw4haMNEiyqxYxYVJcWMbVRVQAErlb/Toc3YMSlCD2z6gty4Zf9hznKpTAYkzpoMhUYdE+ONbKtrsqGpzQGNWoGxBYmsyRwrCNnbEepu5PJ3WVIBZGoxVQ+R0NHhRFl5x9FKFyrXixV4LVZ07jrIWdW48aNgyB3+8vxjHXMulw+HjrRzdUZmhhm52eaY8SMUBJKPo1LblGQ9P9dYsY2C58NH2rhslSRNaM0XyQ8wyMftIx/XwgcjibMmQaGOHR9Hh5iULafDByrNj0RGKQrETEwkkyqVHOPHpcAUhf1IJEiB6qkbqBLoHu3Zs0d8oJqWloZXXnkF55xzTr/3P/vsM9xwww1oaRHHDneikAJVCacCpDEnIZqQxpuEaEMacxKiCWm8nVyM9EC1o2uX8EA1KWFmzN6/G2+8EdOmTcM999xzUv/d4z7Wvvrqq3Hrrbfi73//+1Ea4k2bNuH+++/HNddcc1KNkyBBggQJEiRIkCBBggQJsQti9iXSJIoJZ86cCYOhf3XSD37wg+gEqhSgUpkKZU+pN5WgUqlw55134s9//vMJGSEhNkGEAOs3VHOZklKpYFbCRQvzWINLNFh4fsUatG3ezVoXyXOnIuuiJdBliO0XJARsNnSvWwfb9u0IebzQTZiA+KVLoMnJEW0aE2Rs3VaH7Tvqufe4IC+ByVlGFYpn/yPyE+pT3bK1Fl1dbmRlEYlSHveAii6NIzKbPXubsHFzDVpbHdyDtGB+LmZMzxRGltELKoo5VNqGDZtqUFffjfg4LebPy8WcWVmszyga1DqwfnMN6zMTw/OcmVmYNzs7Jog87NX1aPhwNbqKD3KpdMqi2cg8fzHUCWL7BQnUY7lhYzX27W/m36dMTsei0/KZwEs0qG924+ZaFO9uZFbn8WNTsHhRPjIzxZ/yU9nvtl0N2L67kV+Tj1s0PxcFgvvcCdQKUmdxobrLCZc/gHitCoWJBqQaB+s0i/Bx9Dw3b6llwh0ikDttQR6mTc2ICR93sKSVfRwRPCUkhH0cMbKrlOJ9XHlFB++Vqmq6mJV77uwczJ2bHRPlv/UN3Wxb6ZF2JnycOSMLCxfmCiv/HckIhUjyRyCvQig2WgSGwnPPPYf4+Hjs2rWLr74g/3aigepxl/72gnpRKyoq+DUx/ur14hfPE4VU+jsYtDl69ImtzBinVIW1GmmRJXKF++85TeiGpLu0AsX3/An2qnrIidCLZP18fhhyMjD9Hz9hCQVR8La0oO6PD8BVVhbeeCgULP+iSkpC5o9+CNOc4WPW/boxRyyi//jnJhZ3J61I6q/0eoMwGtW46frpOHOpOGZH6iV77MmtTN5FGyYV2eYLci/jpcsn4IrLJgrbyJE9L7xcjM+/KOceWurz8fmCfP/onn3n5pnC+rjIfb/7fgneeucgXC4/1OqwbbSxnDs7G9+/ez73TA0HjsXHrd1QjRdf3c3Mqyq1gkmyaL5OGp+Ke743T2jPe9vGndj3y4fgbmmHXK1m9tpQIADzhNGY8dAv2J+I3Pg++PBmZhPt7YOm/sGMDBN+/KOFQg+Wmlts+Os/NqK6uovHGV00LxIT9fjenfOGVWbl68Ycyb/886mtKCltY91SpTIsj0Sb8hu/NZUZ40XBHwhiQ3UHai0uJgBUyGQIhEJQyWWYkmHGpHRxfbQ0tp57cRdWra7g170+jg66zjl7NG6+YYawYJV83JtvH8B77x+C2+MP2+YP+7gF83J5zA0Xudix+Di6Zy++spt5Fcj/0v2jsTdlchru/dFCoQHhzl0NeOKpbejsckOtkoeVCIIhlqW5/95FzOgcTYz00t/2zmLhpb/JiTNG3P37pjjh3RUFpgkJCXyN5CBVwmAQZf6/nt7Ocgg5OXHIzDBxdot+Vldb8OwLu4YUB48G4+T+3zzKTKeGwhwYCrJhyM+GsTAXzrpm7P/1IwiQLqggND/zDFyHD0OTmQFNbg40WZnQ5OfB392Nxsceh99iEWbbW+8cwJ59TUhJ0SM7Ow7p6Sbk5Jg5k/nyv/dwNk4UPvuiHJs217JGb06PbcSqSzIm77xX0hNciwFlGD79vIyzgTQfwvctjgP8lasqsHFTjTDbKJNKQapcIeNn2WtbYqKOg/5PPjsizDYiFnnptT0saUUSQxlpRv6ZlmLAvgMtePPdEmG2ebttOPC7x+Bp74JxVB6zJBsLcmDIy0b3gTIc+vNTwnwcbXSfemYHmpttyM42IzPTxBe9psCVfHN4Mxx90D157oViVFZ1ctBM6wL9pDFHEhhPPbuDmUZF4a0PSrC/pBWpyQZkZ5qQnmpETqaZ5WBefn0fmlpswmw71GZHTZcLepUC8ToVTFol4rRKDk73NVnRIvC+kQ8jX0Y+ra+PI5/32edl7ANFgXzFu+8fglIl/9K27DheK2jN+OyLMmG2EUPyS6/u4TW01//S2kprLFXgvP3OQWG20Xx8+rmdrIyQmxOep9lZZqRnGJmgjYJrCRJOFrZu3Ypf/OIX3Ab66aefQmigSidMVINMpwt5eXl8UaqXhF3pMwkjHzuLG9HS6kB6urHfCS+d7Ccl6VB6uI3L+USgc+cBWA9VQJeVDrniy1NUmUIOXU46bOU1aKdyYAHw1NXBsXsPlImJkKm+LPmhe6jOzICvrQ3WzVuE2EabRyrVo7IkjUbZzzYqY+22uoVtRmjTvWpNBZ+U08aoL4hKn07RqQRSFNasq0QwEGKd174gLWGWw1hbKcy2dRuq4XL7WXaj71ylslrKMpBttIkSgc3b62C1epCWauhnG9llMqk5kKZxJwKtq7fC2dACfW5mPxkauUoJbVoyOrbvg71czAEEHcpU11q49LJvFote03s1tRYcONgibGNOrSB0ENKX8Zqeb1qaEa1tdi7fFwHKpm7dWQ+zUd0vw8Y+LtnAG/atgmyjTFZZu53lmVR9qi/INh1lCIMhVHWKU0wgP0G+bKA+Ovk8qighHygKGzZUc8UNsRD3Ba0VlDGnjCYxsovA5q21PK5oDe3r42iNNRjUXKos6uCGpJCoIi49rf8+jkql4xN02LuvidmAJRw7QpAhBLnAS4ZYxFtvvYWFCxfi4YcfxrPPPosLLriA20SFBaoUMT/22GPcj0oyNXQ98MADePTRR1k/R8LIR2urnRetSNIbtDjQxpj69ETAWd/MZb5KQ/9Fi6DQarishTagosp+gy4XFMbB5TSynqDa19oiTOaC5BBo8RwIWsRoA0UZMBFwOr0s+RLJNgJJmtAGWRQo06zTRS6f1elVqG+wCsu+1dd3Qz1EH6rBoGKZCbtNTIVBS4udpUkjlTMa2I/4uA9TBJwN4b5Pbh0YAJL3CThd7GtEgII92nj3PVDqBZVx02fUQyjEtlYHPzeDfvBcpfWCnrQo29o7HHC6/aybOhA0BmkYNrfahdjm8Qfg9gWhjtAi0Ot/LS6fENvId1EfI/mySKAxRz5OpP9VayL7OL1Bjc4uF+u+i0Bjk5WfXUQfZ1CxXVSZJmofR4jUlkL+1+32c0JCgoRvij/96U+47bbbuCS5q6sLf/jDHzguFBaovvTSSxwxE3nSlClT+LrrrrvwzDPP4MUXXzxphkkQh96AgU5SI2nAUf/gUEHFcIPF5Kkn1Tt4YQpy5ijEG00RUBiNnEkNegafoHIgEwpBMYAFLVqg58V9n94wAdpA0LM2DzhNjxa02nD2j8ZWJFD/G+mWioLZrOU+t0jwegJ830T1lhGxmX+IjCmV3NJ9Ha4e1a+DyajmXrxIQTw9awpsIgU80YDKZOT5SH2pAxFwe3ges68RAKp6kEEWsby3t/+NNpoiQJtvyshEmqv0nGnJEGUb3bdwb3tk20LB8N+IAGVRKaChzOpQ/lcriPiMfBf5sKH8L91PUWtDr4/zeYND+l+NWhnxUCcaiDNpI+6Tev0v+biBVULRXPN53H+V/zWIJ3uSMPJx+PBh3HfffVD0JGR+/OMfw2azcX+3kEC1s7MT48YNJquh9+gzCSMfs2ZkcgnQwJNAcnjt7U7ulxo3NlmIbcnzp0GXmQZXU9sgB+xuboMmJRGpi2YJsU1XVARNfj58be2DbPN3dnKm1TR3rhDbUpINmDIpDV0W96CFlUqT1GolMyiKAAVTC+bnMRnFwM05nfpSKmT+3FyIAjFdEwnQwI0cBdC0iTt9Ub4w24hMhDaaxG7aF5R1o0zqvDk5wth1Z07P5A0kPde+oPFH5B7EFEtlwSKQsng2VHEmeNr6r1k0b8mPmEbnIn6qGFK2KVPSubSWfO1A0HtJSXpMnSKG6GlMUTL3pZIdA30crRd0OEGsoiJA5ZfjipLRaXEPso0YzrVaBWYNI9HT1wWqeQk6zqwGB9jmo7JVGZA/oLQ1mjh9UQH7N/JpfUHvke8T7+N61oI+oLWC+DQWLsgdNjKlr8OsmZn83fYB5b3k4yxdbl5zae0VAZqHBoOG1/y+oLnR0eFCXm58TLD9jySILfuV8xWLIHLdvuROarUaWq0WdvvJqWA57mOoqVOncunvI4880u99eo8+kzDykZpqxCUXj8d/39jPVPC9fSrUb0ZkC9d+a6ow2QuV0YCxP7geB373BBwVtVAnxvMi7+20cOnvmLuvEyYtQeW9aTfegPq//hWemhoo4+OpsQwBa7ikNvnqq6HJFRdwXX7ZRFRUdqKujhjjNNwTGg4OQzhzSSEmTUwTZttF54/Fvv1NTNZlNKn5lJxKgt2eAGbPzML8eeKkfZYuKeS+uwMlLXw6TpIDbrePy7rGjUvleycKc+dk8/2hfiQqj6MMJW0uieE5NyceF104VphtE8al4IxF+fhidQXsDh8HMcTWSdImtHm78lJxTM7G/GwU3HApyv/1H9ir6thnhAJB9iPqxDiM+cGNEcuCowFiCf3WVZPx3Au7UF8f9r8E8r8ajQJXXzmJ/bAIUBbmumum4pHHtqCujpgwNdw7S316cpkMV14+iUn3RIDG0pUXj0dNnQW1DVbEmTRsLwWpgUAIZy0u4EBWFCammdFs9aDL5YVGqeAMqzcQ5CxrbrwOufHiSCnPWko+rp7J2SgTR1UYrh4fN2lCGs5YXCDMNvL9m7bUMoMtHTbo9Wp4vH4+iMvPT8AF54nzcZMnpbMs06o1lbyW0rwktmSaD9QbesXlk4TZRmSEF5w3hskIG1025gWgsWbtdiMuTodrvzVFGFu9hFMPzz77LIzGL6uQSL6UqmyTk7/0uVGTp1m3bh03yubm5mL+/Pn83pYttGjV4eOPP8aiRYsw0iDJ0wwGDQsiAvj0szLuEaFNwPhxKbjw/LGYPElcQNOL1vU7UPXK+7DsP0zHXDCPL0TBt5cj/awFok2D8+BBtL/3Hpx793FpoSY7CwkXXID4s84a1o35sYw5ImL5cEUpdhY38OaNsjNnLR2FZeeMFq5HRz01H6woZZIdD5XUmjVYekYBb0REa24Sg+KKj0uxfmMNHA4f96yeflo+LrxgLLNPigRtKD/+5AjWrK2EpZuy43LMm5uDiy8ch/Q0k9DxRlkPClRXrq1EW7sTSoUc06em46LzxrLUlWgf1/DBKtS+/hHslfWQyWVInD0ZBddfgsRZkyEa23fW83Mlhl1CYUEizwVRlQ99QYRKH350mH/SISaxiS47uwiLT88X7uOIiGrFF0ewZ18LVxaQ7uyZpxfgrDMKefyJhM3jx8EWK7P/UtCgVckxOsmI8anGfiRLItBlcWHFR4exfmM1S11RWSjpWF94/jjh2ulUMbLi48Ps46w2Lx/YzO/xcXSwLnK8URaaWOtXrq5gLgiFQoZZM7Nw8QXjkJsr3setWVfFzM3EQUGHSnQgTfs42s9FGyNdnqalcx/MZpNAO2xIS5wSc/cvP//r/T59XllZGT0d1cbGRjz++OMoLS3l38ePH899qpmZmRiJkALVoUHDw9Lt5tNf6tWLNdsoA0I/NUkJwgXTB8JvtSLk93NmtS+z6HDheMYclUwRkyI900ikWaI3JXRR+bmozP1QoHtG946yDqL6ooYCbZgok0pBfTQC++MZbxSwUpaBNpii+lKHAh0meToszPirjo+dxZ9Avo3YbAlxZnG90EOBxhs9WwpkoqGzeVw+zuk9euAlOkAdCCr3pas3sxpLkHzcN/BxVvdRxt9YAh0m0WErrfUDmZ2jCSlQPTUD1eHGCXkhCkj/+Mc/nnxrJMQcaGMkOmP0VbZRgBqrUMawI6GFNNYW015EaxNyIqBNSKxt3npBQX1iYmxqWtMGiaSGYhF0iKRNSYxZHxcvOJv1VRC56f060IGIITanA2dPRWdQh4Lk476Bj4tR2+gQaaC8jwQJIwXH7CnLyspwzTXX8MnCQFB0f+21155wWldC7IJO9P3B0JBshbGQbaBLlDzIV8HbbYO7vTMis6ho0Ik5ScJEYhYVDSpl7ex0DiL2iAVQbxTJIdDPWAPdL7pvA4mVYgE0zmi80biLNdD8DHR3IegQI8/0tT6u281XLPo4ImKjktGhmE9FIqZ9nCt2fRwRxHXZPEMynceEj3PHno8L+v1wt3XCZxMjgfRVoPlJ81SUpuuppaMq9vpfxDEfm/3tb39DTk5OxHQzpcTpM/qbJ5988mTbKEEAaFNkowXL44c3EN6E6JVyJGiU0MdAOebufU34eGU5Kqq7uEe1IC8e5545GrOniy8/79xdgqqX3kX7tr2s62rMz0Lety5A9iXD26N6LKitteCDj0qZmIJ6VLl/KwZ7VIkQiCQRlsRIjyqVra749Ag2bqnlkkLSalw4PxcXLhsjPONFbJgffRLu36JDGyLJoh7V5ReOR1qaGImVXlCQ8PnKcqxcVc49qtS/NWN6JveW5eclCPdxri1r4VzzGfxN9Ux8ph4zEcZzLoJ6zASIBhF40XPt26NKvWXU/yYaRLrz4UelKDnUxiy2xAR87tmjuW9btI+rruliP7J7T1PYx6WE+/DPOXO08DYH0hZ+f8UhbN1Wx6Q7VM695IxC9nGiZKR60W334rOtNdh6sAUujx96jRLzJ6dj2bxcmASX61NgT3Nh9doqLv0lpl3qUV1+0XikpIhh1e1F0OdDzX8/Qu1bn7EaAZGwpS6Zg8KbLoO5SBxb8lf1qJL/HTtGHLGYBAnHA/nxkChdeeWVQ35+1VVXYfXq1cf15RTUkg4rBb90ETnTJ598cvRzt9uNu+++G0lJScwmdfnll6OlpaXfv1FbW8vkTnq9nvsI7r//fmabkvDNQAFqM7Gu+oNHz3AocG1weGEXfNK6YUstHn5qO/YdbOH+Htr8HihtxSNPb8fKdWKz+m2bi7Hze79D4ycbWFZFrlah+2A59v36YRx5/N9CbaMN7wN/WcekD3TCSqQ7JFj+wkvFeObZnUKzIm1tDjzw1/VMmEE9UrQR6exy4rX/7sM/H9nM74kCsTn+/dHNePuDQ7A5vFCT5IrTh3c/LMXfHtnMQazIDMPDj23Bv/+zF+2dTr5v9B6R8NCzbm6xCd0kPffiLjz/4i7UN1qhUss5qFm9phIP/GU9yis6IBL2FW+h+8Un4KsqA1Rqnq/uXVvQ+dhf4N5fLNS21Wsr8dAjm7D/QAszc9JFr+k9+kwkdu9pxF/+sQFbt9exbAhpl5aXd+CJp7bj7fdKhNpGY+pPf1vPm3MaazTmiLn++ReL8dxLu4RmpWku0pykuUlzlOYqzVmau488vkVodtXm9OLRN/dixaZqOF0+qIkt2eXDBxuq8Nib+/m1KJDvf+iRzfj3f/ehqyvs4+g9Wise+Os6XjtEVmPs/93jOPinp2GvboBcSwoJQdS9+Rl23Pl/6C6tgEi8/uZ+PPn0dlRWdfEBJm3mNm+pwZ//uh579zcLtU2ChJMeqFJASIHgUCAKYmL+PR5kZ2fjz3/+M3bt2oWdO3di6dKlWL58OQ4ePMif33PPPfjwww/x5ptvcqBMJE6XXXbZ0f9/IBDgINXr9WLz5s146aWXmA7517/+9XHZIaE/iOShgwMDGdQ9QuVKuQxquYwX/zaXb5AWXLRAi+jr7x3ksqSsDBNn3ejKzjDzJuStDw4JCxyCgQAOP/wyvJ3dMBblcf8skbMYCrKh0OtQ9fJ7sFce3xw5mXj7nYNobrUjJzuO+1WMRg3S002Ii9dg3YZqHDjY/xAomljxyWFUV3dxZoaYiInmn9gcU1L02Lm7kTMQorBmYzVKStuQnm7kDLTRoOafGelGHC5rx+r1VcJs27a9nmUlkpP1SEs18n2jPqnsbDMzPBOLpygQI+yadZUwmTXISDex7Ar1u+fkxPHm8o23DggLHPwtjXB8/gGg1kCRkQ250QS5OR6KrFwuAba982+EAn5hByP/eX0fZwPpORIZEF30mjLUJBs2UJs2WqDvp4CBvp/8CJEoUZ9qVqaZAwhiFG9qFnM4QmPpjbcPoLXVEfZx8ToeczT26P6tXVeF0sPtEAXK8tKcpOdIc5TmKs1Zmrvbd9TzXBaFdbsbUVbXjbREPRLjtDDoVEiK0/LvpTVd2LS3SZhtW7bWoXh3I+vk0ppA943WCForqqq6ONMqClQ11fDBaqiTEmDIzYQ6zsT97rT+O2sbUf6v/wqzrbauGys+PsKZ+sxME89Tqv4h/9ttdeO1/+xlVmwJx45QSC78+l/EMf9XU3lvRcXQp0Pl5eXHzUJ10UUX4fzzz0dRURHGjBnDBE2UOd26dSv3vT733HN48MEHOYCdOXMmXnjhBQ5I6XPC559/jpKSErz66quYNm0azjvvPPz+979nRmIKXiWcGOz+sLabckAFF5V0UcDqCwY50yoC+0ta0dbhREqSflCJWXKiDl3dLuw5IOaksPtAGayHq6DNSBlkmyYlET6rHc2rw2M32mhrd2DfgRYkxA9m56TNnNfr51JDEaAy382ba2EwqgeV5mm1KlodsHlrLURh45YaJvFQDyh5p/dUagU2bK4RZhvdF9qgDyyNpgwc6dFSGbWofi4qL/e4A6yfOpjYQ8uBbEurmH4u996dCDrskMf3J1GieStPTIG/oQ6+iiNCbNu7r4n7oCmAGYjkZANLYNDfiEBZeQfqG7r5oGagjyOyLJvdy89dBFpaHVySTHYM9nFquD3k4+qFla7SYRtpWQ7UrqS5S3OY5qoobNnfzJlxuvqCflcqZNiyX2Cguo18HAaVRtNaQYSAmzbX8hoiAi1rtiHg9kIdbxpE0KZOSUTblj1wNbUKsW1XcQP3adOa3882mYx9Cx2akK66BAknExQn/vKXv2ROI2LMJlClbG8C8kRwzE0Rp59+Oh599FEOGiPhkUce+UYaqpQdpcypw+HgEmDKsvp8Ppx11llH/2bcONKlymXd1nnz5vHPyZMnIy3tS13PZcuW4c477+SbMn369Ijf5fF4+OpFL0EUlWzQdTJA/w4tPifr34sm6FkgFAT4BGdAxiMUQiAU4qxrUBH9XiS7w8Mt5UqOovvbRiXAtHci5yzivhN5UiDgh0yrRWhgnxb9rpDDZ3MMm21fNebsdg/8gSAMBg3fv4Gge2e1uYXcN6eTiDv80GoUEW2jTI2FCVvEzCW6L1QmHck2rVrBBBU0Z0T05nV3uzhgjmibRsEbOJfTC4365Pcff52Po8oGUnQIxwz97SOZGtq8U2YumBL95xpwOhCSUSmcfPCdU2sQDPjhd9ihFDDmwoQnIagi+Dh6TyYLwS7Ix5FtlFXVRJgPPS4OTufw2fZVY85mc7NtJqNqCNtkbL8oH8f3bQgfR3OY5rII2+h+2p0eaCKMN4JGKefSYFH+l3y/WhPZ/2q0Cl47XC4vlEpN1H2cp7ObKJwHr/d0IKfTwtfVDY/VDk1a9PtBHY6v8r9yzqay/43icx2J++H+oIMckVlNOWIZVPlKCcOFCxdi/fr1nHykSty9e/dy4vGtt94a3kD1Zz/7GQeQV1xxBf7f//t/GDt2LL9PWqp//etf8dlnn3G283ixf/9+/nepH5Wyqe+++y4mTJiAPXv2QK1WIz6+v2AyBaXNzeGMGf3sG6T2ft772VD405/+hN/+9reD3m9ra2M7TtaEpKwwObmRpqPq9AVg9/g5ozpw800lv9TK2O1SwSWAXt+o8yI7Qw6j3jdIY5PZHVPlMBl8R09yogmXWQv55NHwqlRQGvtnQ0IU/CtD8OUkD5ttXznmQj4U5Kng8/tgMg5+boGAHNmZCiH3jRbMUYUaWLrciE8YvBmhta1otE6IbYRxRTpU11iQGD94kQ0F/VxmSL5DBEYVamG1AYmJg++bXB5AnFkLp6sbXu/JL8f8Oh+XmS5HZoY8om12ux/xZgooHGhtjX7G12NOgiMtC3JTPJMo9UXI7UIouxAhlRZKAWPOZKQxpYTZGICS+sr6wO8L8mdGQT5Oq3GzH1GrfeFqhz6gUuVgUIGE+LD25HDgq8acXOZFfq4KwWAARmP/tYH+PhCUIyNNLuS+0dpUmK+G1e5BfNzg+UAVmOQDRfm4onQFGlrdiNcMDrhCGi9y09XibButg8vVHdnHyfzcxmK3d8Hlkkfdx4XG5SJUngVfZvKgvZLXYoU8Oxk2pQxuAfcuKRHIylQgPj44qMLA5fIjP08FjcYd1edqs8Ues7qEk4ef/vSn+MMf/oB7770XJtOXVQaU4HzsscdO+N895kCVspMUDd9yyy0cTPYFkR298cYbmDFjxnEbQAEvBaXkDOjfv/HGGzkqH05Q0E03sm9GlViLU1JSTpqILjk4clz0b460QJUypiGrGz4io5DRCX7YyZGz9oZCMCgVyDKohWSQkpKS8cHnDSgp70RWuvHovSV23YYWF3KzzJg9o2hQEBsVpKaiPSUFTZ9vhDovm4mU2LZQCM6aeuhSklC0ZCE0CXHD8vVfN+aKRrfgg48OIz1NdTTDRra1tjuhVqkwd+5YpKYOj21fh6lTRuPlV3fD7lRBr/vSLXVZ3PD5ZJg1a8xX9sgPJ2ZOK8LWnTtgc/pgNn55am9zeGC3A8svGC3MtrlzZFi1ppnlS6jEq3dOutx+tLf78O1rJyAzI13IeJs3V4sPP65He4cTqSlflopSf3lTkxvnnzsGBQXZEIGgYS7aP30HgSP7oUjP/tLH+f0ItDRAO3U24sdPFOLjEhKT8O4HNTh4iHq2TZD32ECHhA0NDhQWJGD2rDFCGGzpWSck1mPnrkZkZSqh7DmsJD/S2OxAcpKO/Qi1E4gYc4WFLfjkszJkpFOpvvJLH9fmgkZLPo78iBh966lTR+HV1/bCkayCTvulbeTjAgEZ5swm/5sixLZpE4LYXVkCZ1DG/am9sDu9cLoUuHhCoTAfN2tWCKvXtjCjed8yVqfLj87OAM44YzQyBPk43eL5aH/uPfjq2qDLTDvqLwIuDwL1jci76TJkFeRBBObNNeO9D2tx8JADmenGo7b5AgE0NbqYGX78uOiyEmu1sasJLeGbgxKPr7322qD3yXe0t584P8Bx8aFfeOGFqKmpwaeffso9qeRkqbf0nHPOYdbdEwFlTUePHs2vqQ91x44dePjhh3H11Vdzn6nFYumXVSXW3/T0sFOin9u3b+/37/WyAvf+TSRoNBq+BoIc0ckMKrnf6ST/m9EAWZtu1DLrry8YguzoQaYMOqUC6YbBfTZRs00ux3eun4l//msrGprsR8t9ff4Q0lIMuO2GmdBoxEmZTPx/t8Hd2Mr9qpSpkSkUCHo80KQmYvKv7oIuaXglOb5qzF1x2WTU1FqZPZRA5dNeb5DJKW64bhpyc8TJhZx7ThEzdlKfVnt7iBkKqWyV+rcuuXg8pk5OFyZ7MX9ODo5UdOKLNZWwWEj+RQG/P8CBwtLTC7BoQZ6wOT5hfCouv3Qi3nrnIGrrbFyiTNkbulfz5uTgvGVjh9W2rxpvWVnxuOn6GXj+pV2orbNyCbffH+J1Y9KkNFx1xWRh901uikP8Dbej+/lHEWioodrLcOo+GIQ6twBx19wChUKMXBOV1d5x2xw8+PBm1NVZoeyRjaLnmpFhwu23zYFaLU7K5JYbZ6GjYwOqqi3sfylbQ4y1RBB0261zEGfWDev3f9WYu+ryyairt+JgSSuvCxRIe3qkrm789nRkZfav0IomaC6Wl3di2456ngPkP8j/6nRKXHHZRJ7Lwnzc5HRUNFqxYXcjLHYfVNSmEgjyzyWzcjB3YvqgrFy0QL6f1oD33j/E6xf5OJ8/nCWcPy8Xy84uEubj4oryMeHHN6Pkb8/BcaQacq0aIR9ViMiQOn8GxtxxjTAfl5Cgx223zMITT21DTc/aQFUPdKg/qjCJfXO0bRtpe+GBCIVkfIn8/lgGxWpNTU0oKCjo9/7u3buRlXXismqyUIypiFOKmPpQKVilU6z//Oc/LEtDOHz4MPep9vaoUoMuBc90Y3pP+55++mmWqKFyhkjBaCRQRpXIoiirezIzqmQD2TVSJ6c3EITVF4DTH4AMMhhVcphUSiZUEg0iG9m4rQ77DjYz0cKk8ak4bV4OUpLEaqr1lvw0frIeLWu3Iej2IGH6RGRfvBTGwpxh/d5jGXNErEOkHtu21XOfG2VnTl+Uj9GjkiAatNndvrMBW7fVoqvLzayOixbmYeIEcRu4XpB0z579zdi8vY5ZRYmMYsGcHEyfki7s0KYX5MKJRGb9xmrU11sRH69ljcE5s7OHtbLgWH1cZWUn20bEHXq9iu1aMC9XuDYuwd/UAOeWdfCVH2KJGu2UmdDNOQ1yk5is20ACtA0ba7CvR0Zi6pR0LDotn4mMRMNq9TDJWHFxI8/bceNSsHhRPjIzzOJ9nMvHB17bd9bD6fChsDCB9V1JhzYmfNyOemaytXS7mQGYbBs/bjABX7RBBIr7ytuxo6QF7RY3UhJ0mDMhDZNHJQkLUvv6uAMHW7Fxcw3LDREZ27y5uZgzO2tY9b+P1cdZDhxBw4dr0F1SDlWcCelnLUDGOQuh1A/voc2xgMjPiNW/9HAbNGolZs3IwsIFucwCHG0Mx147mnY3tpfCbDYJtMOGzORxMXv/7rvvPmzbto35hiiJWVxczMnDG264ga/f/OY3Iy9QpRJcarylwJRq1yll/Je//IX7Xc8++2wmRfr4449ZcoYeyve//33+//X2whKBCbH9ZmZmcp8s9aVef/31+M53voMHHnjgmO2QAlUJpwKkMSchmpDGm4RoQxpzEqIJabydXEiB6qkdqHq9Xtx9990cs1F8plQq+ee1117L751olZK4+iGAHQBF2ZQRpUEwZcqUo0Eq4aGHHmLnQBlVYuklRt8nnnji6P+f/qNXrFjBAS0RMhkMBu5x/d3vfifwv0qCBAkSJEiQIEGCBAmnCkIglnhxBxahGGf9pVbOZ555Br/61a9w4MAB2O125jciCdJvgmMOVBsbGzlzeTJBdMVf13hNmqh0DYW8vDzOukoYHrS02FFZ1Qm5Qo5xY5JZ4D1W0NXtRllVJzOvj8pPQFKC+DKbvlI1XcUHEfD6EDeuEIa8E6/PP9mg8t+SQ8Rw7UdWpgl5ufHCy876lsaVlLbB4fQhJVmP0YWJMWMbMTf7Ko8g2N0FuTkOqlFjIVMIPes7CiqMKa/oRGubA0aDivvdhBCKDWEbic/XN1hZC3HCuJSYKPvtRXOzDdW11G8Z9nEiSuKGQmenE0fKOvj1mKIk7gONFVCLA/m4oD9uIiClAAEAAElEQVQA8/hRMORkIJZ8XGl5R9jHZZiQk2mOLR93qBV2hw+pKQaMHhU7Po76oA8faYOlO0xcNHZMsvDWhr5+hHR829qdzKtAfiSWfJztSBUcVfVQ6HVInDUpJsp+e9HYaGUfR2XS48YlDxvZmQQJBKqSpetk4Zh3WRMnTuSAkVK4Ek59OJ0+vPTqbmzZWgubzUtyg0iI1+G8ZUW45OIJQhgn+y70b60oxZrNNUz3T8XrRJZx2pwcfGv5hGHRjDxWhIJBVL70Lqpeehfu1k7+XWU2Iv3M+Zjw0+9CHWcSupiuWVeFt94+wAENESvo9UpMnpSOW2+eiZRksf29O3c34t+v70Njk61Hc1CJMaOTcMv105GbI4aNuBfeisOwvfEi/PXVCHm9kKnUUGblwnTVTVAXjRfeg/Ts87tw+Eg7b8xpbmZmmHDtNVMxd7YYVt1edHQ48dwLu7B3fzP7FCLfSU424IpLJ+LMpYVCN+jUo/3yv3czuQ3pCcrkMiQm6HDeOUVYfuF4oX15RCT2n9f38Xy1WsOSaWazFkvOKMA1V01hYipRIJ9W8fxbqPn3B/C0djA5C/flnb0Q4+//Dvs7oT5uUw3e/eQws01T36Vep8Lk8am46eopwg8zaay99p+9R30cHdxQMPidW2YiO0usj6Pg+cWXi1FTS3JWAR5j+fnxuOXGmWyjSNTWWvDMCztRVt4JD/k4lZwPWa+/dhpmzhB7COxqasXBPz6Jjm174bc7IVMqoMtKQ9Ed1yL7krOE2ka6wS+8VIwdOxvYx5FPS0zU4cLzx+LiC8fFzAHJSEGI1XwFkikh9p5XXwWVr8ODDz44vIEqCbfefvvtLE3z1FNPITFRPDGBhOFb7J98ehs2bqqF2axhwgem0e9y4b9v7GfnRkyjovD6B4fw0cpyZkvMTAsHft02Dz5eVc5B7HeunSbMtup/f4DSB1+AXKWCPi8TMrmcMw9173wOX7cNsx77Nb8nApu31uHZ53fCHwgiNdXAAY3D4cW27XUsb/LrXywRluk6dLgNjz+9nRdTyqTSJom03vYdbME/Ht2M3/zsDA4iRMDfVI/uZx5CoLMN8sQUyDVahLweeGvKYXn2IST84JdQZZ2808PjQZfFhb8/tBE1NRYkJemRkqLnOdDQaMXjT26FXrcIkyf115qOFjwePx58eBNvgEnrMClJx5tzCl6ffXEX1BoFE8kI83HPbGfSnXizlrVwKaghH/efN/fzho6CVVGgAPrjT47AYFAzqRjBYnHj/Q8O8T289aaZwmyrfPEdHHnkJSjUGujzssI+rsuKurc+hc9qx4x//kLYBnjj9nq8+Po+lvJJSTFAqZBx5nJbcQOsNg9+/sMFTCgjAsS2TnOSDmxonlI2kF7v3dfEc/g3v1zKh8GiAsGHHt6Mjk4n3zeNRgG3x4+ysg7845+b8OtfnCEskKaqAro/xOZMJHYkdUWBNP3+yONb8ZP7FnEFiQj4HU7s/vGf0FlcAm1aMrTpKQj6fHA3t+PgHx6HQqdBxrJFQmwjffLHHt+KbTvrER+nRU6OmQ+niYiSZJIoU04BqwQJ3wTE6Hss+CZrwjHvmO+66y7s27cPHR0dmDBhAj788MMT/lIJsQ1anEgnj07eiEGUNm3k1CgTQptL0qmz2TxCbGvrcGLt5hpmD02M17FtdCXEaTmrunlnPRqabcIWrcqX34NMqeQTVblSyZs4TWI8tBmpaNtUjI4d+4Wx1n6w4hAv8BnpJt4kkeMwGjVITzdxeSEx7orCR5+VMZMonZJTJpVso2ecnWniktH1m2qE2ebcuAr+jlbW25RrdWG5Ao0WyowcBDvb4drwhTDbNm6qQU1NNwczFNSQbSRdkplp4kqIjz45LMy2nbsaUHq4HenpRi6nJdto3NF48/sC+HBFKQddIkB27d7ThOREPbczkG0kZUJVBVQe98nnZXA4vUJsa2q2Yd36aphMavbBvT6OXhtNaqxbV4XmFjE+zmezo+bf70OhVkOXmfqlj0uK541624YdXA4sAnQA99HKMvj8AZYqUynl/FxNRjX/friiA8X7wgzKIg5GVnxUCpvdy3OT5ijZRnM2M9OM2tpubNoszsd9vrIcbR0OPpSmLC/ZptOq2K+0tdmxak2lMNuIsZaCUtIUpjWBbKM1gtYKWjM+/vSIMNtaVm2BZd9hbu2haimyjeaGITcTAbcHVS+9wxUIIkASTXv2NbNPO+rjlHIuN1coZVjx8WFmyJYg4ZtgzZo1x3StXr36hL/juFI7pI1DX/bLX/4Sl112GZMfzZgxo98lYeTj4KFWLiE0GAZn1+jEl7IOVGYoAqXl7bA7fYiL0EdmNqrhdPlwqKenK9qw7D/CJ6malMHVBiqTAUGPV1ig2thkRV1dNx88DARlLymQ3X9AzCaOsgo05ih7P/DUjQ5IaHEt3tMEUfDs3REOUAdkwslWmU4Pz75dvBEVgeLdjbzpGNhHRrbFxWlYtkbUoRLJSdC4iqT5SRlWkpmgzK8IHCxtZX1N2vgOsi1ei85OFx/YiQBloKnSIRIfAGVGqGSZesxFwLK3lFsaNMmDfZzSZODNeefOA0Jsa2iyobHZzvdoKB9XImjdojlYeqQdcRF8HPk3uUImzMeR79qxqwGGniCwL+iARKtTYceOeojCrt2NXOobyceZzGocONgiLOBq37aHA1GFRj3oM5ojtrIaOGoahPkR75A+TseVLcRrIOH4yZREXv+LOO4amJqaGrzzzjtISEjA8uXLmX5YwqkFWtBpvYqUqqeFixY2KpMTAfpe+v6hqwhkTCkvimyH+rWGLO2VyRDy+yECQRL6DoWfXyTQ/aSyIFHj7atso/cpWyIMNJ6oSTsSZHKEAmKeKYHui3yIyUA9l0F/UNxcpWc2xDylKRJEKPw3guZDrPo4mg9fB1H3LUTfG56sgz4L30sZ+0FxfiT0lb3FovwIr1vBEOSqyH6E5rDfJ87H0XgaqjSPbQuItW0oH0fv/3/23gM+zupKH36mz2jUe6/uvfeCwfQaIEBCCMmSkEay6dnd/7bs7rebHkiAkBB6Sei9GWzj3ovcZFu99zYz0oymfr/nyGNUxsYEpDsO75PfRNKMQIf73nvuqc8JhdQ9VzkPZ1BytAPoxIb8as4D7/Mz2UlhHafKVtLw94Prr7/+9AhRfn820Hf8W/CRvEzSDv/gBz/A2rVrcfToUaSlpf1Nf1RDdKOkOFmivGROZPnPUPQ6PFLGV1yUpEa2giQhx2BWYSRzHbOpJFIqzk9UIlv8lGKYk+Ph7eqRMrihYKaBxl3ijElKZMvKipPeI7I4j+xDDRu9ZBVVAWbuCwsSpVSJGYeh4GXKzNf0qep0jXnyDCn/1UuARDdMtpC7D5bZC5T15M2YloEjR1pPBW+Gy8CyODJjjlzT8cKECSl4b0OllPeOJF/r7R1AakoMcrLjlek4g14nfbQsIxwmm2NAdFxRoSIdV5ws5ZfMqrI0fyio9/gZf0cFyO5rSowb1HHpw/WF3+2B3mRAwvSPN4rgb0V2Zqz0sXf1uGEd8UzDAZ0JhWrWTXoE8xOk5JwtKkPBs8u+8unT1PRZUm/MmJ4hJbbJyaN1HO/VZUvzoAozpqXj+PH2iDqO7Q3swY+1j85ojgcSZ01B46vrhflabxxOcDbQ1QN7fpZwVagA2aSZqfd6/aOqWshJkRBvRWGBGh2n4e8HCQkJp88lndWxsIXOOY982WWX4Sc/+Qnuvfde8Yo1J/XvF7y0yPLX1tonzupp+nXnAJwOL1YsL1DGEMsxA/NmZKC7d0B6yMRZOHWZdna7MWNKmoyqUQFLShLyrrsYPmcfvF29p8tB/f0e9Nc1IXHGRKStWqBENvYGXnbJRHFKSaYQlo2lQU1NTmGJXbZEDSEQFdtlayeIjG0dfaczSnRwGpucSEm2YbUi0h3CtuIi6O2xCLQ1n84W8WugvQW6GDtiVqhjdly1slB6xxsbnWLsEly/9o4+6bnkM1flRC9dkicELNxf3GdEmJSNz/bSSyaOchLHC7NnZogj3dLmkjaHsGwk3KEzSJInVeRddJDnzslGZ6dbnNWwjuP33V1u+UyVE03nNOfqi+BzOOHtHqrj3Oivb0bCzMlIXTZXiWwkSbpkdbFkJnt6PcN0XHOLS3Tc4nlqnAaewcsvmSRnsn2IjhPis0annGGeZVW4+KIS2GPMaGl1nQ5c8ivXjT2+ay+coEy21auKZCwT9Ui4p53rR+Z6k9mgVMdlXbYS9sJc9FXXIzAw2NPOLKpH2LADyL/5SulZVQHqCQa0WlpG6DiHR9ptLrigKKrGDZ4PCIX0yl/RhkceeQRxcYOkpsys8uczvf5WnLOVEAgEhEwpN1ftuAMNYw9mP75z11L87r4dOHmyEx2+fnmfkfwLVhfiC5+brVS+L98yG15/EKVHW9Hd45HKG7PJgPmzsnDnrXOUUq5P+tatMkO16c1N8FbWyXskV0qePwNzfvYDZZcWcenFE9DT4xYyrIYGx+nSR85RveubS5ReWosX5OALt8zC8y8fE+eUz5RPkezEX/3SfOQqyrwR5pLJiL/1TjiffxyBtqbT7+sTkhF3wxdgnjRNmWwkxvrOt5bigQd3i0ESNkY4yuT6z07DsqVqgg8EKx6++52luO8Pu1Bb3yOlj/QdSCBzzdVTcfmlaqoLCKPRgO98YwnufWCXzGakAUzXwWY14sLVRbjlszOVycYz+bWvLBAn5tCRFumXJUhkt2hhnnymVMd954vC7tuybisGqON0EFKllEWzMPv/fiiM56pw6Zpi9Dg8WL+lRnpWhfhMp0NRfiK+/sV5yjJvxPJl+RIkfJE6rtFx+hmSbOzrX10kZ1kVpk/LwFfvWIAnnjqA5uZTegQhpCTH4Eu3zVU6nobBrm9/cwn+9Oc9w3Qc76sbr5+OxYvU2aQkSpzzix/j0L/+Fi6ehVPl5xzRVPKVm1DwuauUyca+7O9+exl+f99OVFR2ws/WnlBIKqoYmLjphhnKZNPw94VgMIhf/vKXePXVV+H1enHRRRfhP/7jP2CzfTLBXl1IFQtIFMHhcEj6ure3V1LXn9SDa2trQ3p6OvSKxpF8XDCiSqKCqupu6WlgppUlv9Ewe0uGf1d3C5Mjd/DEoiRMmZASNbJx+HfHzlIEfX7ETy5C6tI5YsyNJc51z3GG34GDTRgYCEiWYe6cLGWZrZHgeIR9B5olq8XS0AVzsyOSQahA0OnAwOF9CPR2Qx+XAOvsBfI1GkAyEbLsMstAY3zevOwxr3o41/3GjNaB0mYxzmk8zZuTJUyn0QA6qNRxNXU9EqCbPjVdytCjRY+QDIuM3ASdhSmTU6NGNsfxKnTtPjSo46YWI2Xx7OjRca1OlB4dJMzKyYzF7OkZEsyMBjCjun9/k+g4MrAumJ+jbCzYSLAklHqEGWkSni2cnyNl8NEAZgFJ+tTR0YfYWLPIxkxrNOw3tva0b9mLvppGGGKsSFu5QJh/o0XHHTrcgtpTOo6l0gxOq9AjY2Frj6fcde3VSuV2OBzITyuKuvX77//+b/znf/6ntIXSOX3nnXfwuc99Dg8//PAn8u/XHFXNUdXwdwJtz2kYT2j7TcN4Q9tzGsYT2n77ZKE5qn+fjurEiRPxwx/+EF/72tfk5/feew9XXnkl3G73J3JutJOnQYMGDRo0aNCgQYMGDRo+Eurq6nDFFVec/pmZVWbsm5o+aJX6OIiOej8NUQmWE+7e0zCk9Dcds2ZlwjSC3U4FhCkxNIBAcJDAwKA3waQbHGodDaU2h4+0Skkh+8wKC5OweGGu9OZFSznhgYPNwnianRWPxYtzZa5atJQTtm7YCV+vE7bsdGRevBwxORmIBriq69Hy3g4MtHOOZBIy1y5DbLE6NsyR5YQ7d9VL6S/32YJ5g0Qa0XAe+r1+lLe50N3vg9GgQ0FyDHISbWccOTGe8Dldst96y6qEtZN9liQDGusS1nMBzydnSJ48NfuTpb/z5mZHRZk+dVzp4RYcPdoqYzBI7rRoYW5UlOmTaKfseBsOlrbIGpJZesnivKggjqGOq6zqwt79TUKOxdJfyqaKnHAkGlud2HuoBT3OASTFW7FgZiayM2IRDWhrc2Hn7gbRdWxvWLggR/ZdNOi47h43du1qkHnlPJ9sp5k6JS0qZOM+27WnATU13UJYyBYulv+OZGLX8OEIhTjWR90zDSn822eD3++H1Tpcv5pMJvh8n8x8Y630Vyv9jYi6uh789vfbUVfXe5qhMNzf8I93LVV66QdDAfT5e8RJDW9e3SlnNcaYCINOnSHncnnx+/t3Sg9omIWVlxUJIb777aUoHsOxEh+259gr+ODDe7BlWy0GhAVQJ4RKHFvzza8twuxZWVAFMiUev+cx1Dz1GvzO/tOj6egQTv+nO5Fz1Rp1soVCqHnqVZy89ylhOg3DnBiPid/4HIpu/4xSg2Tbjjo89MjeQWIxORCDhBmXXDwBt31+DgyGsdE/56Lj6rr6se54Kxxun/SSEyaDHhPTY7F2Srp8rwq9xytx8Me/hLOi7tQ8REBvNiJt2TzM+fkP5fmqNMp/+7vtQvQUnm9sMOgwaWKq6JH0dHXOA9nf77lvB0oPtZya/cnZpXrk5cULectYjrz4sD1Hx/SPD+7B9p110oNP8Ghyvb719cVyf6nkfHji6YNY926FBIHDjHFJiVbc8eUFWK6Q+Iw67s33q/DSuvJTbPqD75Px98bLJuPSVUVQCY7OefTx/dI7G770GRS5/NKJ+Pwts886O3esdVzpoWbc/8fdaG/vO7VuIVisRqxcXoA771gozqEqVFV14e7f70BD4ymGbp0OJqNeGIFJUMVe3/HE+V76W9tWo7z0tyC9MOrWj2fj8ssvh8XyQT/7a6+9hgsvvBB2u/1jz1E9/zwoDWMOOli/u2+nROAyMuzIz0+QV3KyFfsPNOGRx/crk01G0fh74Q96odMZYNAb5cXv/UGffKYy9vLkXw5KFjoh0Yr8/ER5cYZpfUOvGHeDBooavPr6cazfUCUXfF7e4DPNzo5DR0c/7v3DLolUq0LDK+tR9fAL0JuMiJ1YgLiJhYgtyZdRP0f+5w/oPVahTLaO7ftx/LePCmlM7IQCxE0qQuzEQgQDQZy453G0bd6jTLaa2m5hw2SAJDcnXp4pny2NozfePIEN71cpk8014Me6slY43T4kxZiRGmtBit0Mi1GPYy0O7KntUiYbyU9K/+nXcJyoRkx+FuImFcrLkpaM1vd3oeyXf1YmG/UXDd+yE+1IS4s5rX/5PTOF/EyljnvsyQPYt69RHKxB2RKRkWlHbW0v7rl3hziLqvDyq2V4f3O1VBWE1406jk7EvX/YKaORVGH9xio5kxypEta/PLM8uw8+tEfOsiocONaG5948IczcuZlxyM+OR15WnARJ/vp6GQ4db1MmGzPQDMT19w/VcfEwGHV45bXj8rxVgXcm787Ozn7ZZ2H9y1nvvGt556oCbQ2eR9oetEHEHslLENuENspTfylVJpuGvy/cfvvtEsyhMx9+feELX0B2dvaw9/5WqK8h0hB12H+wWS5N0uYPjQZarSYkJVmxd18TmpocSpg7AyEf/CEv9Dr9sCyWjCGA/vTnJp1FyaW1Y2c94uLNclGFwUx0VlasjIQhayFnNI43OA93/cZKifRybEgYzLbxgqVs27bX4bprpo67bDS6a595U6jzLakfZJx1ej1i8rLgKq8VRzZhmppZfnUvrJNZuHETCj6QTaeDLSsNropa1D//NjJWL1Ii2+YtNTIXj8ZR+Dzwa2KiVVgymb258ILiMcuqng0n25xweAadVP0Q2awmA7yBII42OzE/PwkWBa0ErZt2w3GyBjEFOcPGqRjtMTCnJEqJ98RvtCAmN3PcZTtxsgPH6aSm2mE2f3BF83vO2zx+sl1+Z8rk8Z9l3trqwu7dDYhPsA5jqmU7CO+L+vpeKVdWMZOZJY4bNlbBajMOyxSd1nGNDqk+uOqKyUqyqe++x2CbDolDqpGYCQyvG8/yWGajz4aNO+rg9QXESQ2DZzU1yYaGZife31WPWVPSlci2aXM1nE6vOKdDdRzbVfr7HbKua1YXKalq4Z3JQG9ubvzprC7lIFOy2+3HexsqceXlk2W033iDtgb3PG2PoWW+tE1oo7Dq4Ibrpwu7voZzQwh6ean8+9GIjzMj9VwQnf/VGpSCl2YwFBpmJIVBBczIJqN0KhAI+U9lE0ZfSjodlUgIwZCaiD6dPRpLQx3BMOjwU26urQq0tfWhp8eDuLjRpT68YHX6weycCnAmI2n9TRFKLXnpG2wWdJeqi0z3lB6HMTbyZW6Mi0V36QllGS6OLuHIl0hGGp91S6sLDueAEtnanYOl+ZF6UW0mA9zeAHr61VQYOMtr5do3WEafB5b8snfVWcHfGX/U1fdKmb7NNlr/8j3vQEB+RwWo9139PsRHGFnCfRhUqOPoRPc6PBH1b9iJYEuLCvAM8ixG0r88u1y7kxWDY4iU9M3WdQ8Lro7ccxU16rK9J8o7YLGcQcfFWmTuNkf9qADvTFYERyo95rPmncsyfhXgOZSZrhFKj7lutFUaFNlxGjR8FGgZVQ2jwEuTFiaV3MjLgSQaVMqq+i50ERzUMMRZEH9BTb+g2ayXtWH0fCRRQdiRkbVVAD4vvUGHgD+yQxUKQhlJC8t9dQY9gv7BnrKRYMmtMUZdTzTn4oW6Il/oIb8fBtsHkf7xhs1qPN3DOBL+QFD6GlXNjzQZdKf7yUYiEKRugZArqQAdVJY5RtJx3Ic6gwH6CE7seCD8vE61lA1DOEan7Jma2W6hk3tgpC4L619lOs5sOK1/IyJEHadINuPZZeMZ5llWBYvZCLfHc0bZVBJ42awm0WWRwPeNBr0yYiCuy5lilHzWvHO5L1WA55BnMpKOE9n0gwESDeeOUEgvL5V//9OIT+d/tYazYtbMDCnrcjhGZ2K6utxSfkZGOxUw6llGyMzp6IuL7zGratKrGVA+cUIqMjNi0dk1ug+KfUi81GbPGv9SQiIrMxYTipOlR2tk9s/j8Uu/z7w5asiUjDE2pK9eCF93r5AqDUXQ65P3Mi5aClXIung5Av3uUY50KBCAv68fWZetVCbbwoW5QnYWJu4axqDsGMCc2VnK2KbJ7kunxusPjpKtz+tHepwFyTFqZCOzr9Fug6/HMeozT0uHME0nz52mTP/Gx1sj9lPyvfg4q/yOCkyemCrka11d/aM+Y3kmSxxnz1Sj49i/SBZY3lEjdRz79ejMqCKMYykyz2KvY2CUbDy7PMMLF+QqkY1OzOLZWXB7/BJAGunQeAb8WKLobiDIJh0MRNZxTscAFszPFmdWBebOzhJnlHfoSNm6uz2YUJIiNoEKzJqZKe1atD1GgjZKZmYcJk5IUSKbBg0fBZqjqmEUCvITsXpVoZQrse/S6/WLIm5udkr08DPXTB3WnzSe0OsMMOvtklpgiW84YkgmYOlx1HPshboI5vXXTZcIb2OTQ/pCWcLHHhaWAC1bmq/sYqAxcv1108RgYt8K+xfZk0TDt7XNJQYcR1+oQtEXrzvV81knpcABrxcDnT3oq2lA4sxJSll/8z97mfSn9lXVCesvZfN2O+CqrJPxNAWfvUyZbGQK5diSpian7DEacyzpYtlXanKMkn68MApT7ChMiZE+VRIr+YNBDPiD6Or3Sl/qogJ143PY75x95QXwdvbA3dwm5Er+freUoDO7X/LVm2Cwqgl4JSfH4IrLJorOZTkryYn4Cn/Pz/g7KkBH9Pprpw3OyGt2wnNax/Wht9eDlSsKUVSUpE7HfWa6BGYaG52ndRwdV5IpcWTI7NlqnGji6isny5nk2eQZ5VnlmeU68gyrZP29cFmBjKFpbHHCSdn8Afna2OpCblYcVi9WJxvZc+nwUcdxj3Hd6HzV1zskaHLFZep03Px52XJ38g7lXcr9xn3HNqDYWAuuv3aqMh03aWKK2BzcY7RBeE5pk9A2oY3Cc6ySkViDhnOFNp5GG08TEbwMXnrlmJABUNFR2TIyeM1VU3DhmmKl4zi4Zb3BfgwE+hHEqREE0MNiiIFFb1cu2+attXjl1WNigDBSzuzImguKcONnpo9pCdW57DmyNj/7whHU1vZItDwmxoxlS/PwuZtnReztGk+wD/X43Y+h59BxyaRKpvWCRZjy3dthy1JD5BGGq6pemH87dh4Up4ZOTMrCmZj8vS8hfuL4k2MNBbNbT/6lFHv3NUpWhOMHGBC55aZZY1r5cC77bcAXwLaqTpygk+UPSL9qaqwZS4qSUZyqdj5jYMCLij89g/oX3sFAV6+Qd9nzs1Byx43IueYipXqEZ/PNt0/irbdPnq7QSEm24YrLJ4lhPlbjOM5Vx5Fp9ZVXy6TvkjqO48ouWlMsgbqxLCc8lz3Hc/DcC0ekj5frSMd1xbIC3HLTTOWzrDnD+i/PHEJFZSd8/qCU+zKTeusts5QFH8Kob3bgmdePo6yyU5waPsfpE1Nx81VTkJPxAcmSCjBgTpZa3l/UcWYTdVwqPn/LLBnZpHK/OV0D8ky376gX/g6SdxUUJOKmG2YoDf4SDGw9/+JRbNxULaR71BvZWXG49pppWLWiYNx13Pk+nqa6tRFxCuV2Ohwoysg579bv40JzVDVH9axg5JcXPnvdWFYVTRE4bl2y/BKcncqy32gBI9I1NT3i8JORdTycwHPdczQsSQLBjE1WZhySkmyIFkg5V0UtfL1OcU5ZghlN6KtthKe9S+a72gtyomKg+1BjjpkjGc0xhAV4rPBRdFzfgB89LL/U65AWZ4lIsKQK3l6nZMd1RgPipxTDYFbrzAwFMyCcZU1w9IWqEsdIoG6rrumWHsa83IRxmcl4rnuODiqDcSxb5WgOMsRGk47jncq7lRlBsjtHk2wt7X3ocQ4gKd6CzDS1waSRIDFRR2c/Yu3mYUzn0aDjmFFtbnFK1QHZm1UGkyI508zk034rLEyUnmkV0BzVjwen5qh+eqE5qhr+HqDtOQ3jCW2/aRhvaHtOw3hC22+fLDRH9ePB+Sl1VDXWXw0aNGjQoEGDBg0aNGg4IxisUBmw0OPTCM1R1XBGuHwBtPd74fQF5HgkWIxIs5lhVUQFPxTNXf3YcbwNZXU9MgFjcm4Clk5JR24UlFG1d7uxeX8D9h1rE8bTyQWJWDUvF5ML1ZCMDEVPrwcbNlZh245a9PX5UFyUJL1l7KVRXcZKEoqNm6qwZWuNyJmTHY81q4uxdEme9P2oLnPcsq1Whs+3tfdJuR6JY1avLFRO8c9S7p276qVvkHMuE+KtWLmiABesKlLek8eCnQMHm7Hh/SpUVHYhJsYkpDEXXlAcFSXnZSfa5TwcLWuTkS8LF+TgojUlypg6h4Ll+e+tr8TB0mb5ec6cLKy9sETKClWjpdWJ9RsqsXtPo5yNadPSZd1UscEPRSjQD/RVAH3VQMgLmFMB+0TAqr5Un+W+7BekjuPMV5ZMX0AdtzhPeako+xk3b63B5i010kKQnh4rpIqrVhQqb/lhKff2HXWi40gGlJhgEx3H+4E6RbWO27e/SXRcVXU37HaT9ESvuaAYiQnqxqqFcaysDes3VslX6rjFi3JFj/D5atBwPkAr/dVKfyOiy+NDrcMDfzAE+gjcJJwcYjHqUZJgg13hxXWysRdPbqhEd98ArKfkIGlLnM2EW9eUYFq+OkOursWJe58pRXNHH6yn5vqR/CHGasKtV0zGyrk5Y/a3P2zPsYfxF7/aIg4DZ77S+KBzSEfrszfMEFZgVYYcWRx/9dutOHS4BQajHhazAW63X9bv4rUluONL85U5qzTE7/3DLmzfWSc/Wy1G6X2j5lyyKBff/uYSZXMG6aQ++sQBvLOuXEZLkJxlwBsQw276tHT86HsrEBc3Nv3R56LjXn71GJ557ogYwbYYE/y+oBC1kBn2n364UqmxRIP8z4/sg6vPixibUdbSzZ7GzDhZN/bkq0LpoWbcc+8OGXFhsw3uLZ6H5GQbvvOtJcrGrBBV1V345W+2ornFBZt1UMf19/ulR/Ur/7BAgjdjhQ/bcyG/E2jfAPg6AWF/50xwL6AzAglzoYufCVVwOgfwi99swdGjbcN0HPkfLr1kIr78xXnKnFWez9/duwM79zTI7F7RcadGrpA5ljpOlbNKXfbQo/skaCPzZm2ndJw/KCNYfvi9FWPWH/2h+y0UwgsvHRXCIuo1Os28L7zeICaUJOPHP1yptAeZzvPDj+5DX78PMTYTAmRe9/iRnR2PH/9gpUx4GE+c76W/la2tykt/SzIyzrv1+7g4/zwoDWMOXzCIeqcHgVAIVqMOZgMZdfXyPcdL1Dk9o2bBjZtsgSBe3F6D3n4vspJsSOYsxjgLMpNsQtjy4rZacVpVgGvy13dOipOaQ5KMJBtSEqzISbfLYPJn15WjqzfyUPXxwHPPH0F5RSeys+NkhlpKSowQUtAAefGVY6is6lImGxlOSw+1ID3dLqyElC03Nx5xcWYxUPYdaFIm26YtNdi2o04IWZjlpWzyNdmGnbsbJMqvCsy2rXuvXDKnnCNJ2bh+GemxOHK0Fa+/eUKZbCTaoQFHQ5z7LDUlBpmZsbL/qqq68MzzR5TJRuKTx586IKO38nLjZTY0nea8nAS0tDjx6BP7lek4Og0PPbIPvb0DyMuLHyTcSbPL92Rgf+iR/fI7KsA1eeTx/Whpccm6cc24dpSNa/n4kwfQ3TN6/uu4oecA4O0EjAmAMR4w2gETAw56wFGKkFedjnvtjeM4cqQNGRmxw3Qcz+66dytOZ85V4P1N1aLLqNNO67iceKl6YCaTQR1V2Lu/Se4AOqNcr7CO411RergFb71zUplsDPq+9EqZ3KHUcZSNdyt1HO/a519Qp+MYmH7iqYPw+4OndFyM3Au5uQloanLg0cfV6TgNGj4KNEdVwyj0DPjhDYZgMeiGZdj4vdmgQz9nNSpyBssbHWjt9ohzOlK2lDgLOpwelNX3KJGtvtWFivoeJMdbhkXGKVtqohW9fQPYV9amRDaW0u7e24j4BIsMvh+KpCQr+vu82LGzXhlD8vubqmC1GkZlJpkN5EW7dVstVIHlvtxqI0vMOEuYQfb3N6sz4uhAM3ofHz88a8osOeVjKR8j/SrAcmSWOjILOBTcfxxnsmdvo4zWUYHdexvQ3eMRR2uoHuG5pbFZXtGlLHBD45ujrWiIj9RxfK+pxYFDh1uVyMY1qajoQkqqbZSOozPNAMDuPQ1KZAv5+wBPPWCwAiMZ4A0xQNALuNWcVZ5BOnvMBo5sFeDZZRZu63Z1Oo4tF3yeI+eji87TQWkwbtu2WrkDRlaG8K6wWgxSSs07RJWO40ga3qEjdRyf6649jXL3qsCu3Q0yd5bncqSO4yikEyc7UFunxlbSoOGjQHNUNYyCLzAYZYtUBkrbJBhi1lVNJM7R70UwFII5Qp8sh1gzQOjoHxxZM94gpT8HfrNsaiR4OXA1u51qLq2eHreUq0Yab8HnTPlI+68C7n6flF+ONJLCoGHX2uqCKrAnlSMHIsFqNUk/l6rINAfNn6lHlmXA7EOms6gC3E867vsIeoQGOzNwXd1qsm/8u+F9P0o2q0kylnRkVYDOHrdTpOfK91ii3NWt5qyyFHnAG1mPhEvzKb8SBNz0VgFdBD0ie1AH0JlVAJdrQMovz6TjmJFTpeOou6jjwiXmI8H7rLVNzboRnNVrtkTWcVabSdaWd4gKtHf2QW/Qn1HH8c7l3asC4b8bUceJ/g3IedZw7giFdMpffwvuu+8+FBYWwmq1YvHixdi9e/c5/XN//etfZW9fd911UAnNUdUwCiYqttDgBTYS9E/5MechqkCszSQHh8PSR4LltbpTv6MC8XYzTEa9GHIjQeOSyxlvH/t5qpFAgh32RIX7jkbNow2GkDwiKjxeoPHGyH0k2Qg6/yr7fJhhO5NsfJ8ZQ1W9vakpdsnGnEk2W4xRGdlIcpINoWBkPULZ6HQxs6oC/LuUi+fyTLKpIkIJ/91Iz5XvcQatKtkSEixCyBLpPITXUtUzHcykGged1ZGQPRgazKwqAMt7Gew6kx7xKdRxUo2UfGYdNzAQkLJ9VWBG8ExVIZSZ+o397yqQkmRDMBA8o46zKNdxH5zLkbLxHPM8a/j7xjPPPIPvf//7+I//+A/s378fs2fPxqWXXiq912dDTU0NfvjDH2LlypVQDc1R1TAKiRYjTAYdvAE6Vx8oOX7P92wmA2IVEStMyolHeoIVXa6BUbJ1ubxIirNgal6CEtkKsuJQnJOArt6BUZdDl8ODOLsZ86emK5GNvUYL5nP+lkfIKYaCpUkkWliyOF+JbMwmkFmy3+0bZZAwG2jQ67F8mRrZCJLDcKu5Pb5Rlz3Xkuy6qrBsaZ4QszCrMNLwZQaH7JOqiJ7ILkkjkn2VQ8E143tz52QrM84XLciV4E1Hx/BMEc9tR2cfiouSUVKcrES2WbMypY+RWa6ROq6trU/6zFSRKU0oSZG16ejoH6Xj2BNH45hrqwI6Y6ww+yLYD4mQDEXQDejMgK1AiWw8g9Rx1GcjAxA8uywVXb5MjWzE6lVFci5HOqvUeXzOZP9VhRXL8uUOGFkZwrvC4/Zj9coimIxq7BHemQy08l4dCpYqs8ecdy65DVRg8cJcKT8eWSnF59nZ6caECSlKCeM0jA9+85vf4Ktf/Sq+/OUvY9q0aXjggQcQExODhx9++Iz/TCAQwK233oqf/vSnKC4uhmpojqqGUTAZ9MiNZQ8o4PHTOQ3Ki9+zRzVPPlOTQTIbDbhuaQHsFiOau93ocXnR0+dFS7dbGID5mc2sxjDnmtxy6SQhUWps60NnjwfdjgE0SEmXDp9ZUyKfqcJnb5iOwsJENDY6xOBliR6/JwvglZdPxqSJKcpk49+fPjVdyrz4EtmaHOJE01FcqMj4JeiILpyfjc7OfukdJFlMc7NTDPP587Kx5gJ1jur8udm4cHURHM4BWS/KxnLg5hYnpkxOw1VXTFYmGx29a66aIhmZhgaHPFM6X/w+Py8RN984Q5lszBDdctNMKYvjSJ+uLrc4rfyezvPtX5ijjIGVZbVkgLXHmFFf3yv7jq/6Bodk5r70xXlnLEUfa3BNvnTbXKSlxYhsXDOuHb+nM/H5W2ZJBYIyJMwdJE/y9QJ+F8BRNb6ewSxr3HTArE7HXXXlZBnf09zskjLfsP6lQ8NRJvPmqmNy5oiyuXOzRadRt1E26rrODreMbFIZjKPuX7WyUIJbouO63XJHcA2nTU3DlZdPUiYb78wrLpskDj6fpei4Npd8X1iYJHeuKrD/njqOWoznk+e0/ZSOy0i344tfmKN8XNP5O0dV5QvCQjz0NTAwPFAdhtfrxb59+7B27doP/gv0evl5x44dZ/yv/K//+i9hur7jjjsQDdDG02jjac4Ih9d/eo6q7lSmlXNUYxTPVCPq2lzYVtaKsvpeyTRwjuryaRkoyohTLRqa2l3YuHdwjipZiiflJ+KC+bmYOTF1TP/uuew5XlTvvleBrdvr4PH4ZCbjRReWYNmSPOWXFsc3vLehUsiL6HhlZsSJAbVqVaGyiHkYNEQ2bKoSdkxGqFkqd8GqQpkHeqa+s/ECo/ckTdr4fhUam52IizVLloFrN5ZlZ+ey33g2STjy3oYqVNd0Sb/b8uUFuOSiCVLSpxpkmn53fQWOn+iQrNaiBTm4eO0EmW+pGifLO7DuvQ/YYOfMzsIlaydg0hjrkXNBXX2v6BESJ7Hlgg7YxReVjHmm95z2HB1U1wmgn3NUfYApBYidJNlU1TqOmbewjmNffnZWPC6kjltROIrkbrzhdvtk3iZ1HHugqePoQFPHqQqMhEGypE2bamTcCmf4MlNIHbf2ohLExVqU6zgyI/O5kpyIvAUrlxeIHlFZMh0G51gP6rh2uUcXL87DJWtLkJsz/jrufB9PU9HSoXw8zYTM0fqfZb3/+Z//Oer9pqYm5OTkYPv27Vi6dOnp93/84x9j06ZN2LVr16h/ZuvWrbjllltw8OBBpKam4ktf+hJ6enrw8ssvQxU0R1VzVD8U4S2i+pI/H2WjeOOVmfkoe268ZfuoYHmSJlt0y/ZRdRxl4zGN1rNKaLJFt2wfVceNp2wfFZoe+dug6bi/DdEg2/nuqJ5sblfuqE7KSkN9ff2w9bNYLPL6uI6q0+nErFmzcP/99+Pyyy+X96LBUVUbJtNwXiAale75Ilu0ihfNshHRaiQRmmx/f7JFux6JVmiy/X2eB022vw2abBrGA/Hx8efk6DMjajCQUXz4ODP+nJmZOer3KysrhUTp6quvHhasIYxGI06cOIGSkhKMNzRHVcNZEfJ5EepqAfQG6JIzoTOoL/sNI+jzwVU1OPvTXpQLg9mMaIpeNjQ64PcHkJUVF3Gcgyow0zDQ0Iig2wNzViaMcbGIJvQ3tsLb44AtMxWWFI3s4aOUFbIsmX2M7EGKJkOdRCjsKbNYjcjOiosq2aIZQa8XnvrBuaTWvFzoo0jHkXynsckpX6njIo3lUqnj2GPJMUMkn+KZiCbZ2EfO0VEsDVXGknwGdPV60NvnRWKsGUnx0SUbOQvYr832BvZgRhPcvgAcbp9wfCTFDE4niBaQFIu9x0ajAbk58ZrT+imB2WzG/PnzsX79+tMjZuh48ue77rpr1O9PmTIFhw8fHvbev/7rv0qm9Z577kFeXh5UIHpuFg1RhVAggMC+9fAf3IiQs0fm0NFRNS2+DPopC5UqYV709S+uQ/UTr6C/rkmmDthy0lH4hWtRcPMVyi+IXXsa8NLLx1BX3yMOK1n/2EtDYhky3KqEs/QwWp54Gn3HTyLk98MYH4/ktWuQ+YVbYIhR20/Te7wSJ+99Cp27DyHo9cFotyHrkhWY+K3Pw5qqhoH1fHFQn33hCHbsrJc+M6NJj2lT04WsiAytqg2kF186ivc31cDpGpBZmyRZuvH66Zg1c3REV8MHOq7zjbfR9uIr8DYPRsPN2ZlIv/5apFxxqXIdt2N3PV554wTqG3uFYDcp0YqLLyzBVZdNUt5refhIC55/6SgqK7tk7FZsrFnIgK6/bprygGFlVReeee4wjpW1SW85+9uXLsnDzTfOVO6wtnb248UNFThc3gGvPwizSY/Zk9Jw/YUkAVR7N5Akjuu2c1eDcCvQ4ZoxPV3IgsizoBID/gB2VHWirMUJtzcAg16HrAQrlhalIC9Z7bqRYfrV14/j3fWVMleVDmp+fiI+c+00YQXW8NFwasiV0r//UcHRNLfffjsWLFiARYsW4e6770ZfX5+wABNf/OIXpTz4//7v/2TO6owZw0kOExMT5evI98cTmqOqISJ8m55HYO+7gMEIxMTJHLpQWx28bz4Ck98L48wVymSjg3r8Vw9Jj6UlNUmc6L76Fhz9//4AX48DE7/+OWWybdtRh/sf2CWDvpOSrMKE2ePw4Km/lgqR0de+os7Jp5Na/dP/ha+7B6bUZOhNJvidLrT85Vl4GhpR9G//JO8pka2iFnvv+m/01TXBmp4CU3wsfK5+1Dz9Ghwna7Dogf+EKcoyv9EAOqa/unsbjhxtRVycBYmJVhnbQJKbmtpu/POPVikz5GiI33v/LiEa4ZgayubzBUVWko784LvLNWf1DGh79kU0PfSo6DZj0qChwCqI+nvuR8DVh4ybb1Am2+ZttfjTI/tkXnRyok2MX2a6nnzmkDCLfvk2dWyihw634De/2y5kbJzjy7nWTpcXL7x0TDJK3/32MmWONM/jz3+1Be3tLhkXRgeaI6TefOuksLL+y09WKyNm6+xx4+6nDwhDfUKsGQlxZrnDthxoQn2LEz+4bZ6y7CqrMX75663i3JNEiXpkwBvAzt31sqb/8pMLkK9oJB0DIW8eaUF5mwsWow52i0Heq+nsQ5tzANfOylbmrDLY9dCj+4SQjeORGAgJBIMor+jE7+7dgW9+fTGWL1U39k3D+ODmm29Ge3s7/v3f/x0tLS2YM2cO3n77bWRkZMjndXV1Uc+jE93SaVCCYEcTAoe2AJYY6BLToDNbobPYoEvJkvl0/u2vI+SLTIc91vB296LyoeegMxphL8yBMTZGMm/2/GzorRZUP/4y3K0dyqKXz71wWAw4ltdwvATZEtPT7EiIt2DzlhpUVXcru7RanvqrOKnWwnwY4+Kgt1phTkuFOSMdvTt2wbFnH1Sh6vGXxUmNm1gIc1ICDDYrrGnJiCnIQdf+I2h6c7My2aIZ23fWiQGXlRkrhjkNEjqsZK3lCKLX3jyhTDY6DXv2NiA1NUZelI3GeW5uvDA8P//i0WFzQjUMwtfVjdZnnofOZIIlJ1sqHfji9zqTEa3PviC/owIspX3+lWOi63Kz4yUAEdZx8bFmbNxSjdr6XiWycS8xk0onNS8nHrF2s+w5ltfytWdfIw4fHd6rNZ547Y0TMrokNzdBzihl45nNyoqVM8yzrArv72tAQ5sL2Wkx8hwtJgMSYi3ISYtBXYtTHFZV2LqtVhhrWV5OB5/rFn9Kx3FMzRtvqdNx1R19qGx3Id5qRJzVJGW/HJOXYjejz+vHzuouZTqO2XvaHLQ9eD55TmmT0DaRc/zCEWFT1vD3j7vuugu1tbUyxoYESosXLz792fvvv49HH330jP8sP1NJpERojqqGUQhWHwE8/YA9QrN2XBJCvZ0I1perEA0dOw9ioK0LlozRFN3WtBR4ux3o2H5AiWyMVHJGXiRKehombrcfBw6qufC9zS3oKzsBU0ryqGwHjeCQPwDHrj1KZPO7PWjdsFMcVN2IyJ7BYpb3Wt7bpkS2aMfuvY1CimUeMTuYWS5mH/YfaJKMhAocKG0Wh4bOzFBw/9HgpCHFuYgahsO5/wD8Pb0wpY6e+cn3/D098jsqUF7ZJb3GkXQc91t/vw8HD7UokY39slVVXeL8jdRx3IOsNFClf3kG9+1vlDUa2R84eHZ1UgWhCruOtMJqNkhp/lDwZ5YA7zrSok62vQ3QG3Qwm4e3zXAdOZqGwTA6XipQ2eFCMMT57sPXjfsv1mJEU68bvW6fEtk41oo2B22PkeCsY/Zwl5d3KpHtfMXgtAS1r08jNEdVQ0QCJelJjVS+xVLgYABQlFENuAekDFk34kIlwu/5+92Ksg0BIRaJVFoWZtn1DKiJYAY9HsAfkIxMJOj0OgQUrVtwwCuOsv4MsrEc2efoG3e5zgf093lhjHAWCO7DQCAEr0/NnnP3+85YAmoyUbagnBkNwxH0DOrWiMR1DOSEPvgdFfOEuafOrON0ypwG/l3KxnLfSKBjQ8NdBegky7qZznRWdUKupAI0fj1e/5n1iEEPt0fNuhF9rrPrOL8/JOurApyTfqYqd4NOh2AoBF9AjXNBW+NM42g0/avhfILmqGoYBX1KpjiqIX+Ei9PdN1gSnJqtQjTEFudBb7PC7+of9RkdVDo7/B0VyMmOk8i90+mN2K/HC4PlcipgzsyAMTEeAYdz1GehYFBetuIiJbKxHzUmLwu+3giyhUIIeAaQMHOSEtmiHUVFydKvFSnS6nJ5kZYWI1kHFcjLS5A+chKKjQTPCCP9ZGTVMByWvFzozCYE+kfruKDbLZ9Z8nPV6jjXaEeZ2XPaxDmKdFxGRixi48zSkzoS3IPci6p6GbnX01Jj4IpwN/Ds0tEqKVFDfMZ7qTArHv1ncEbdAwEU5aibHcl1YRAiso4bQGaGOlbntFjLGTNdHlaTmA1IUNR3TFuDz5a2RyT9y3PMMmAN5w5eZapfn0ZojqqGUdAXzxp0RLvbhP13WKa1rweGounQs19VARJnT0Hy3GnwNLUh4P3g0idLrLuhFQkzJiFl0SwlspEuf9HCXDgcHsk8hMHIJYk82GPDz1WA5b3Jl12MYL9byFjCoIM60NgEc2oqktasUiIbS3vzP3vp4Nicrp7Tlz6/uhtbxZHNveZCJbJFOy5YVSj9Wm3tfcPWjX16NM7XrilRRh6zfFmB9KZy7w91VlkeyhdlZ8+qhuGInTkd9qlT4G1pkxFcYfB7vsfPYmdMVyJbVmYc5s/JQk/vwLDMqei4Vpc4qQvmKgpi2gfZfcP7KwzuveYWp5QrL12ihjyGZ/DitRNEFp7NoWeVveR0ZFevKIQqrJ6XI1nd7hGydTk8Uvq7cm6OMtkuWFkEu92C9hE6rtfhkeDDxWtLlI1bmZIZLyW+3f2+Yc4qnVRvIIQZ2QmjyoLHCwsX5IjNQf3L83laNo9fmOIXL8pDWppdiWwaNHwUaKy/GkZBZzLDfMUd8L7+IEIdzQiFSbH1eujzp8B00S3qZNPpMPOn38b+H/wMvUfKxdEKlwInTCvB7P/5LvQKZ73e9vk5MueNpB0B/yCZOWXOzIzDt7+5ZFS/3ngi4+Yb4alrQO/W7fC2d7AtSsoIzakpyPveXbBkDrLAqUDeDZfCWV6L+hfegauje7DEMRiEOTkBU3/wD0iaNUWZbNEMjp/50hfn4bEnD6C+oVf2GsvNONPy4osmyEsV6Bh8/c5FeOBPu9HQ4JBsG405jmhatiQP11+nxtmKdkjg5gffQc1//wz9FVUIsdVC3jfAPmWSfDayl3s8cfvn56C7x4Njx9tPG8Dcd3Ri77pzkZC2qML1105DU5MTe/Y3oqOzT+TinktNsePrX10Ysbd2vMCzWFffi42bqtHT7T4tW3y8FV/64lwUF6sbJTVvajquXlWMt7bVoKGtD/T76HfFWE249oIizJ40mhNivDBpUipuv20Onny6VNiRw+tmtZpw+aWTcOEFxcpkY7b0kmmZWHesFZ1Suj1oK5Htf2pmHBYVqnumzDLT5rjn3h1yJgYdaR0MBh3mzsnCbZ+frUw2DRo+CnShT2t37hA4HA4kJCSgt7cX8fGfTCkEh+q2tbUhPT096qmfz4SQ24XAiX0INlcDegMMBVOgL5ktjqxq+Pv60bJhJ7r2HhEFzCxr5tqlUTHChCVwJLEpPdwKnzeA4uIkieQnjvGcvHPZc5yd6jxwCL279iDQ3wdbQQESL1gByymqcpXgc+zef1SeK9md7XlZyLp0pbJS7vMJTc0ObN9Rj5ZWp2RnFs7PwdQpaWM6JuRcdRzHMnHGK+cKc/zG3NlZMpZG9bzNaEegrw89W3fAdeTY6Uxr4vIlMNjVZ0FYqrr/YDMOH2uV0sLioiQsWZiLhDEeYXIue47ycJYqybzYk8oS9KWL85CWao8KHVd2vF0IgBxOr7B1L1uSj2xF5dIjZatpcmBfWZtkVpPjrVgwLR0FWeplIxoae0WPkOmXpFTRpONImHSi1YlO14BkUIvTYpGfFCMzVVWDo6N27KxDVVU3TGYDZs/MwLy52Upmuo+FrT2ech9tbEWcQrmdDgem52Scd+v3caE5qpqjquHvBNqe0zCe0PabhvGGtuc0jCe0/fbJQnNUPx6cn1JHVTt5GjRo0KBBgwYNGjRo0KAhqqD1qGo4I5hsP1neKfMOWcIyfXo6cnPUsCZGLFOq7cGJEx3SQztxQgpKikfPCFUFzig7crRVyoCLCpMwZXKaMsKHkWAPrcxY8/iF/GTmjIyoKcMkEdX+A81w9ZGx1o45szJlwHs0wO324cChFnT3uKXEcd7sLKU9xyNLMQ8eahZiFpLKsAcpYYxLzc8VQb8fXbtK4aquh8FmReqyebBlpSNa9Mjxkx2iSzgzcsa0dGRnxSFaZKuu7saJ8g75efKkVNEl0aLj+Dw7dx+W55swtQRJc6dFjWydvW4cqejCgC+A7FQ7phYljZoRqgoksjlwcFDHpadTx2WNmhGqCj6nC21b9sHb2Q1LWjLSVy6A0a6ur3coSN7FGb0kVYqLNUv5aqQZoSrAM8AZ733VDTDE2JC2Yj5sEWa9qwB5PLr2HYHjRDX0ZhNSF8+GvUAdOdb5DNXMu8FPaf1rdFiAGqIO3d1u3PfHXThytA3eU+yONMpXLi8U4geVzgMZHf/00B7s2dsozgNBAg9eXF/76kJl4zgIOqZPPH0QG9+vliHvBI0QOqrf+sZipX1SNHxffPkYXnvjhDiEZLcxGnRi/H7r64uRn58IlVi/oRJ/fe4wuro4lmNw7mxOTjy+9pWFmDZVrWOzd38THnnqgDiCYfBZfvHzs6U3TyVOnOzAH0hY1NiLELnFEEJSog033TgDl6ydoNR5cFXW4fC//Ra9ZRUyK5cMLabEOBR+4TqU3HmLUlKgzq5+3P/nPSg73iGOPtfNHmPG6hUF+OLnZivp4QqDuuOPf96DvfsaTzOIU8ctWJCDr92xUNk4DoJs62W/+DMaXt0Av9NFujgYLGakLJyJWf/zXaUGOll1X9tchXW76tBH5l+dToKshVlxuOO6GchOU6t/171XgWefPyLBLh3/pwfychKEdIyBCJVofncbjv38Qbib2wY5gXRATE4mpv/znchYs0SpbMfK2vDAg3vQ1OQQkicKmJwcg1s+OxMXXViiVDbHyWqU/uvdcJRVCQcEYU6KR9GXrseEr96kVP/yWZb+v7vRtf+ozCsnjPGxyPvMWiEp5IxyDRqiHdERYtQQVSCT4+/v2yFGUqzdhNzceHkx6/bOu+X4yzOHlMr38GP7sGlzDcwWw2nZbDYjtm6vxR8f3BNxptl44YWXjuGNN0+KARKWjVFfZjDv+f0O+GisK8K76yvxzHOHxZmmA5iXG4+kJBtOlnfgN7/bFnE24niBe+2hR/fB6RwQSn2uGzOqZHm8+/fbZbyEKlRUdeH+B/dIJJ+zGjl7jrP7Orr68cBDe3GyolOZbG1tLvz2nm2oq+sRx5nrxoxgX78Xjz5+ALv3NirNzhz80c/QXXpMsjOxJfmwF+chFAii4g9Pof7ZN5XJRsKd3z+wGwdKWyQ7k5sTh7yceGHEfGtdOZ598agy2ai/Hnx4L7ZsrRXnNKxH+P2WLTXymUqcvPcp1Dz1GnQGA+zF+fJcTYnxaNu8Bwd/8ivJLqnCxr0NeGVTFQLBEDJT7eKYJsSZUd7Qiz88fwj9ngizwccJu/c0yJnk2cwO67hUO2rreuQM8yyrQvfBMhz6t3vgaemQmdZ8pjG5WTIerPT//RY9R04qk426n3dAY2Ov3AlcN94RHPNDW2DffnU6ztvrxIEf/hw9h07AkpGC2AkFsBflIuD14+Tvn0T9828rDSgd+PEv0b5tH8xJCbCf0r86vQ7Vj7+C8j/+VZls5ytUz1ANfkozqpqjqmEUjpW142hZu1wKjNwzIsgXSwntsWa8v7nmVNZr/MGs0c5d9UhIsMj8yLBssbEWJCfbhG2X5XIqQCfrvQ0VYlAyoxWWjZnozMxYyXyVlrYokY0O8ltvDxobnG0ZLkOmrGScrKsbZFRUZZi/9U65ZI7oCIZL9JiJpkPd3t6PTVtqoArr369Cj8MjxqXpVIk0gzbZmbHyzN/dUKlMts1ba9Da5pJ1CpcPcv0y0mMlS/jm2yeVBW5a39sOZ3k17IW5MMbY5D1mUK0ZqeLk1D79KgKnovzjjSPH2lB2sh0ZaXY5n+GzSmZu/rxxc42wZaoAR5iwWiQxySpBrrBs/D4xyYY9+xrFuVEBT0cX6l9aB2OcHZbUJHmelM0UZ4ctJ1MyNyyBVAGvL4B3d9WJbiNj7WkdZzYiMyUG9a1OYbRVAZ7BN946Ca/XL2dzpI5rbevD5q21UIXaZ94UpvWYwpzTWTaWicYU5WKgqxd1L7yjTLZNm6slSMh7aqiOY7CQrM5vvlOuTMc1v7MFjvKaQR1nG2y1oG6zZaVJRrr6qdeGzUIeT3Rs24/uA2USeDDGxgzqEb0eltRkKU+uf/4dcbQ1aIh2aI6qhlFgho1Ztxjb6LIQGnIOpwfllV1KZCuv6JSSLtLTjwR78+js8HdUoKq6S/qP6ESPBEulmanm2qpAc7NLHJpIfYvh/tTjx9sVlTn6UFHZGfGZ0tg0mfU4fFiNg0+UHmmVszCyhIs/2+0mHDraqsxQOnykFUaTIWL/M9ezuroLDoeaTHl3aZnMwo1UXmZOSYK7qQ19VWqCI+WVnQj4gxFnfvKM9Do8qFIU8Cov75SWBmZ6R4Lvuft98jsq4DhWCW9nL8wpo9sEjHYbQj6/ZJdUoLmjD529HsRHKIs2GvSSjaio71UiW69jADW13TIzdSR4do0mvYzUUQHqLgYXWBIaScexR5VOjypQx9FBPZOOq6BNIDNMxx9d+47JOHK9abQe4Rnpr29GX22TEtm6D52Q6gbyAoyEJSURAx3d6D1arkQ2DRo+CrQeVQ2jwAshdOoCG3lxhYdG6xX1XUhfD6IU57AmqvpVPqwVUJ6qIrKn8JKc0dULqVs3QncW6XgcVO5HWZczLBx7LsO9virA6D37FyPj1MIp23ODOi4iTvXnKVu3c/i7yo4D/zD/9pn2XIQ7Y7wwmHk+sx6R7aZqvw07k1Go4862cIO/MI7SnPtZDccHlZ1Vg+60DKMQlk1RH/7Z9tPpu0EbufORQP2msrUs9CmdJqrtUg2jQFIHi9kgpEUj0dPjQWKCBZMmpiiRbcqUVMmcMnM5Ek6nF7YYkwwBV4EJxclITopBd89o2ZghYeZSlWyZGXHC8EsSj5Fg9pyBh+nT1BAWsbyc6+J0DIxSxCRH8fmCwmKrCvPmZKHf7R8lG3/mGZk3J1uZkTlndqb0WzJbP1I2R+8AJk1KUcaMmTxvOvQGfcTyXkbzY/KzEVucp0S2KZNSYTYZ0H+KjG0oeH6TEmyYWKJGx02enCrlx5Ey4XwvJsaMKYr0SOLMSbCmJWOgfXRFjc/VJ6RKSfNnKJGN/ajpSTHodY3ebz5/UHTcJEWEccz8TZyQKmdypB7h2fUHgpgzW42Oo+5KW7lgkBhrpI4LBuF39SF99SKoAtfF5w3KXTBKxzkG5O5QRS6WsnCWrF/Q64uo42KL8hCTr+a5Js2bJuXb/r7Rd763vRvW9GQkTJ+gRDYNGj4KNEdVwyhQ8fNy6OjsF8ICXgi8JLq63NITcvFFE5SNvqDDtXJloTildLooF+Wj40onevHCXGXstbwsL71kAnzegIyACctGkiKyxXL0xYzpGUpko5N81RWTpQSOJcB0bigbiT2am50oLkrCkkVqnAbiissmyfpRFjrOhMfjQ2OjQ4gzVq0sVCbb2jXFSEm2oaHJKSMSCPZ/NjY5pRf54jXFymRbtaJQetwaG51wnyKK4fo1t7jE2bnyssnKnOj0NUsQP30i+msbhVhJ9IjfLwQtlInMv6pYJ6dNScPM6RnS/+wcouPIBDzg9eOSi4oRG6H0djzAEWDLlxVIuSh1WjiKz+/53vJl+RJ0UgFzYjzyb74SAY8HntYOhAIBkc3b44CnqQ2pS+ciZYEaR5W67ZKl+ZKk7+hxiwMowSSPD62dfSjKicdcRQ4+9zv1L88kz2ZYxzGAybPL57lqRQFUoeCWK2FJT5ZS/IBnMEAScA+gr7oetsw05H/2MmWyrV5ViKzMWDQ0OOROOK3jml1yRq+8fLIy2TLXLkX8tAnoq2mAz9k3qEd8fvQ3tEBvNKDo9uugN6opXExdMkeYuN2NLfD2OAf1SCAAd0u7EC0VfP5qmOJilcimQcNHgS70ac0lD4HD4UBCQgJ6e3sRH//JGADBYBBtbW1IT0+H/jwsr6Bz9eeHBxn1wplVRoVJBX/LTTNhMqob3UBn4fGnDmLL1hq4TkXP6eQsW5Ivo3NsEXprxws0jp5/8SjeXld+OutLeWbPysSdX1koPb5jhQ/bczzq76yrwAsvH5WgA382W4ySXfr6nQslCKAS23bU4am/lAr7JZ0GOtccncN144xclSD5ziNPHEBDkwOBQAh6gw45WXH40q1zMHtmplLZqmu68ac/75GeSsmO63VIT7PjczfPwsoVY+fgn4uO629sxdGf/g7dB46K4csaPUbyi758IwpuvVZpuSP7UP/82H6ZzcjMKpsKqOMuvrAYn71uutLZwgw6PP7EQWzbXivzNglWktCBvf22uRF7a8cLDDaU3//0IAFPV6/oEVOsHWmrFmDGv34DluREpTru3V31eGtbDbpPBSBYHTQxLxFfunoa0pIGSb1UgXcWWfPb2vtEx3EEEoOE1HHUdSrRvn0/jv3sT3BVNYhDozMahP13+j9/TWZvqgQ5DB58aK/oOgZZRcelx+LWz83G8qX5SnVcX10TDv3H79FzsAx+z4DoNGtGCibcebMEAFTquIHObhz+6X3ybAMut9Sgm5MTUPj5qzHh67dAbzCc97b2eMq9v64FcQrldjocmJefed6t38eF5qhqjupZwbEXNIBpmHOWZWpKdAz/JlpbXTh+skMaVSZOSBFWwGiaQ3v0WJuw7RYWJKGwIHHML6xz3XMMQpCggsRTjOSzjFvlZTrSQKdsDEBwdAOz+yodhqGgE0g2bGbyE+KtmDk9XemszZEBkrLj7WIAM2gza0bGmAdsznW/ScXD4RPoq26A3mpGypK5MCeoDYoMla22vhc1tT2yz5hpTVbszIwczXGSOu5USbDqYNJQeNo60bnnsMyOjJ9agvhJRWP+N891z7n6vThW3QWvL4isVDuKc+KjR8e5fTh0pFVm5TKgRB0XZgFWDWbaOneWYqCzB5a0JKQsmgWDWd3M3qGgg0od197RJ5nUWTMyxzxg81F0XE/pcbiqG2CIsUo2M5p0nONEFRzHq6UUmFlWlu+rgOaofjw4NUf10wvNUdXw9wBtz2kYT2j7TcN4Q9tzGsYT2n77ZHHeO6q1zYhVKLeLjmpB1nm3fh8X2snToEGDBg0aNGjQoEGDBg1RBW08jYYzgiU27JE6fqJDyuLmzMrCksV5ykhGhoL9n9t31kmZKJnWp01LF5IRkttEQ2nXjl31OFjaLP20kyamYuXyAumpUQ2SAO3d3ygvzqNlSfKKpQXIy02IivJV9gvu2lUvxDHZ2XFYtjRf1k81WHjStfcwmt7ZKmRAHOiedelKKY2LhpJC9nBt214n5FMJiRYsWpAnTMnRUDbtCwbR7wvCx9El7Nk26mE16JWNuDpf0EMdt6seR8va5OcZU9OxbEmelJ2rBstWd1LHHWoWVm4yxa9YVoC0NLtq0TDgDeBAWSsOHm8XIqWCrHgsnZON7CjQv+zv7dy2F20bd8Db2QN7UR4yLl2FhOmTokLHHavoxK7SZnT1uJGaZMPi2dmYUpIcHTquphs79zeimfPA4y1YNDsbM6OobDpawXaQrTvqcLKiExaLAXNmZmLJwlylXB4aNHwUaKW/WulvRBwra8Pv7tuB1rY+GDhXNTQ4e4sjWH74vRVKna66+l785p6tqKvrPTU/TYdgKCT9lt//x2UoLlJHvEO231/fvQ0nTnbIxU/SB5LvpKbG4K5vLMbsWWNHVf9he44Mv7+7fycOHGxGIBiUCz7gD8oQ+jtunyeGpsr+zwce3CNkI+xFomz8arebcNONM3H1lerYa/kcj9/9KGqeeAX+fo8MdyezIwepF9xyBab96CtK59G98dYJIWih82A0GmTdjCY9VizLxzfuXAyz2aCOTMkfQM+AH4HwvNlTj9Bq0CHZYtKc1TOgpq4Hv713h5B3hdeIOi6XOu7bS1GQp4bZPBzA/NVvt6K8vFOeJ+WjjmO/5XfuWoLp08aO2fzD9hx7U//wTCkOj9C/CbFmfPHa6Vg8hvr3w8AxTcf+6x60vrMJQd8gWVHI54MpPg4l37oN+Z+7VplsJHZ6+rVjeG9brQQzDcZB/Wu1GHHpykLcfOUUpfr3zQ2VePHtE+hz+2Ey6uD3h2Ay6rFsYS7uuGnWmHEFnO92XOnhFtz/4B6Z4GAw6BDkFDMdMHlCCn7wnWXj3o9/vpf+7qtRX/o7v1Ar/dWgQTKC9/9xt4xUyc2Jl/EXubnxyMyIxcnyTvz5kX3KBg/zQv3jg7tRW9sjY0tycxNEtpzsODQ1OfCHP+4WAiNVeOypA+Lkp6fbkZeXcHrtyLL7hz/tESIjVXjplTLs2dcoo1bycxORkxUvmVRS/j/8+H40NTuUybbuvQq8v6laZn5y3ZhNzcuLl7Hkzzx3SEg0VKHl3W2ofvQl6C0WxE0shL0wV76SNKPmydfQ9PYWZbIxIPL0M4fEiQmvW35+gmQcNm+pFfZpVfAHQ+KkcvyhUQcY9ToYdTq5dDz+EBzewVE/GoaDDsIfH9qLhkaHMEtTB4sezoqT9x748175HVV45LH9OHGiA5mZsaI/wjqOxvB9D+yWgIkqvLy+AqXH25CSaEVORhyy0mKRkxGLPo8fj796DG1d/cpka3j2dbS8sRGm5ETETigQPWKfUCizSivufRw9pWXKZNtV2oR1W2pgtRiQm8V1syMvKw5mkx5vbarG3sMtymQ7XtGJ5986Id/nZcUiKz0WedlxiIs1Y8uuemzYXqtMtmgGCQn/+PA+dHW7B/VHNu/7eGSk2VF2sh2PP12qWkQNGs4JmqOqYRT27W+SeZY0RBiRDoNRy5QUG44ea0VtXY8S2cqOt6GisktKzIaWNTIDR+eQ7J2HD7cqka2p2YkDB5qRlGQdlsXiGnIt29pd2L27QYlszKZu3lYDe4wZVusHJT+Mkmekx54qpa5XVvK7fkMlDEbdsLJyykaW6X63H5u21EAV6l98F0GfD5bU4eMjOIaDYxzqX1ynTLZNW6rR3+eVdRqa8SDzL88H11WVU8NsKp1Ug27wWYbBDBx/7A8EEdAKekbh2PF2VNf2SIZyaFkjv6fe42dlJ9QEbuobeiVLk5xsG5bFCus4MrHv3deoRDYny5FLm2G3mWExf9DVxL2XnhKDXucAdh9qVlby2/jSO9CZTcNmV1I2S2Ya/E4Xmt/cCFV4f2e9BIHjYy3D3k+Is8AfCGLTbjV3A7F1TwM8A36kJNmG67gYk2QJN+6okztEw3Ds3NuAto6+UXYcbZPEBBv2lzajpdWlVEYNGs4FmqOqYRRaWgeHQ0cqp6EBTMehpUWNgqNiZZlopP4Ki8UopTqqlC+NNGajuUYjQaeBcxqbFcnW3tEv8xhj7aPXjZc/LzKWGaoA5/QyGxNp3QjOQayp6YYqOE9WwxgbuffOGGeXz1VVGLD8nYZHpLI8e6xJoumqsvjMqHJVIsnGd+jEBvh/Goahtc01WHYZYfSGzWqUz1TpOOr9s+k4Noio0nEd3W7pSY2JcDeEgyPNHX1KZPN1OzDQ3glTfGxk/Wu1wFVerUQ26q76ZqfsrUiwWYyoa3JCFeqaHLCcobTXbjOhU567Vp0RSY/w2Roj9PDSDuAoOM1R/WgIRsHr0wjNUdUwCsy40XyMZHzTSaTis8WoacSng8qLPVKWiFFVShzJUBkf2YyS9eAajQTXki9VssXYjPLcOFcwEhhNj+TEjgfoaDEo4vNGLtn2sY82bnikfzxhSoiT/rJICA545XNV/VtxceYzZkx93iBMJr30manAYBD/zI4oP44GkpZogwThdIP6bCTkPd2p31GAmJhBPXImHce4gyodx30usp2h9YPXGR0bFWCbgN5kQtAbWY+EfH6YEtX0nPEMMjtJPRsJXM9YRfc9wRLfM8nm9QdEx5mjZJ51NMFGOy4U2Y6jHSB2nE3jU9UQ/dAcVQ2jMH9etlwO3d3uYe9T4TEzx97QqZPTlMjGId8sP2YGbiQ6u9xITLBizhw1hBkTSlKkZ6ujwz3qcmBpLZ17rq0KpKXaMX1qOrp7RsvGnjKT2YAFc3OUyMZM+NLFedJTM9I4J2syfZ2lS/OhCtlXXiAOadDrG/Y+y4EDngHkXLVGmWxLFuWJy+cZkVFg4MHpHMAiheyOVuNgppf9s0PB/ceSX7OBPatKRItqzJqRgeREm+izkejodMtns2eMHWHR2UAGbva6dXT0j9IjZOpma8G8uWr0b2ZqDCYUJKHH4Rmt4/p90m85Z0q6EtmM9hikrVkKb3evtAsMRcDtka8ZFy2HKiyblyNsySODXnQQ+eLnqrBoTraEuyjfUPCu6OvzYdGcLKm60TAc8+ZkSQCi1zG8ooZng/YTbZUShcST5yPCCQeVr08jNEdVwyhkZcbhyssnw+MJSN8lHRkavQ0NDola3/zZmWPGJPphYA/jTTfMlFIuEou4XAPi4DQ2ORAMhHD9ddPFWVUBlr7dctNMYarlWjmcA7J2zS1cQx8uXTsBBflq2DrpMFx/7TTpZSRrcq/DIyW37JtleejSRXmYqcj4Ja68YrIQY3Hd6ExTNrKLspx61swMLF1Ch0wN8q6/GEmzp6CvugGe1g74XH3yta+qAYkzJyHvhkuUycZxUQzMkPiM68V14/rV1/ciOzseV185RZlsFr0OMQa9OKosAw6eclD9oZAwicebjFpGNQJYPXD9tVMlG8JyfJbs80V9R/Azko6pACsfqOMY/OBZdYZ1XLMT/W4fLr9sInJz1Iy64l667qIJSIq3oqHFCYdrQEqB27v60eP0YMnsLExRaJgX3PYZ2Avz4Kqsw0BHF/zUI81tcDe0IGXZfKQrdFTXLMlDSX4imtr70N3rkXXr6vGgpb1PnP9Vi3KVybZkbjZmTEqVfksp73b7JBjR0OJCblY8Lr+gRJls0YyCvASsXVMstkdzi0vOKW2S+kaHZMhvvmF6VIwv06Dhw6CNp9HG00QEt8X6DVXCGkpHi0ZASUkyrrlqCubPzVYuG2f4vf7mCSF14gYmq90Vl03GqhUFyo1fko28+tpxnCzvkMwWSVEuuXgCLlk7YUxnvp3Lnquo6sIrr5fJ/FmSZNCpv3B1Ma66fLKy4EMYZG1+8ZVj2LO3EX5/QPrgVq8sxLVXT1M+u9fT3oWKB59F81ubZUSNwWZB1qUrMOHOm2HLUDvnlQbIK6+VCeEUAzccUbNgfg4+c+3UMXUazmW/8ay6fAH0+T8gTuIM1TiTAWZt/uEZwXXbtrMeb64rFwIjghmQKy+diGWL85TrOI644p6rrOoSHZeRbsell0zExRdNGEbcomLPVdb14PXNVSir7JQe6MQ4Cy5YmIdLlhfAZFSr4/qq61H98LPo2LJbKjTYNpB11YUo+OINw0iWVKCzx41X11dgN+d/ewMSlF4yJwtXn3L+VYKBmtfXV2Dr3gYJxnE0zbyZmbjm4onCAjxWON/tOGad391QhXUbKmWeKsmnJpWk4JorJ2Pm9PEPTJ/v42l2VzcpH0+zqCj7vFu/jwvNUdUc1bOCpUAdnVRw+lHMoqohpcjtg+QYqan2MTWQ/hbZWLpHhyslJWZcDKSPsud6etxSLpqUZJPS22iCw+GBq8+HpERr1A0l9zld8Hb1wpyUEJEYRSVIjsHyS/YIJoxDVcFH2W+D5b6DfanMpmo4N9AJDLc5UP9Gm46jbDSGU1OGs7BHhY5zeKQXLzHeEnU9jN6uHvhd/TCnJsEYM76zLD8MLJMmg3J8rFlZv/HZdBxLWWNiTKMYiscCf192XL+cUY6nU2XHne+O6k46qnEKHVWnA0s+hY5qdFmoGqIOVGyZGXGIRsjYgTGMpn4chEerRCsSE6PLOBqK+HirvKIRzHqoznycjTzDlhldhuXQ86D1o3500DFlRUa0PlP2vkcrEqNUhxDm5ER5RSPY18hX1Oq4IePVNHwUOy467y0NGj4M52+ISMO4Rc1ZajOSrCWaIqwclxCNIBEQyzKjsWiBrJ1RK5s/EJFYKVoi05RN1WzSD8u+UbYzsZ6qBPcZ95v3DMzOynWc2zeKrCVaQP0WrTou5Pci5B1N0BYN8PoC8lyjUTYSsfkcLoSC0adHolnH8U5gGTArlaINQnbj7kfI74ta/SvkhBo0nGfQMqoazqjYdu9txDvvnERldbeQF82alYkrL5uESZPU9uQRR4624q23T+JoWZv8PHliKi67dBLmKmL8HYqqqi688fZJ7D/QJM4DCZTYv7VsifreMvaBvvHWSezaXS9sjoyysq/sgtVFyokVurr68ebbJ7FlW+2psmQrLlpTIr29qsuTecm/9U45Nm6qEiOO/bMXrCoS8pi4cShBOxvo/K17rwLrN1aiq8sta7VyeQEuv2yS8qw+Dcv3N1fj3fWVQrjDPUYm4isvn6SMdGeYjittxntbalDb2Duo46am47LVRSguSIJqsNedOu74iXb5ecrkNHmms2dmqhYNwdYaBA5vRLCuDAgFoUvNhWHGKuiL5yrXcY2tLryzpRp7D7fI/stMs+PCJQVYsTAHBsXlm+6WdtQ89Rqa3ngffrcH1vQU5N94KfJvugIGi9o+fM5bfuvtcjmv1HfkBVizuhiXXzrxjDOuxwt0sNatr8CGTdVCFsfZriuWFeDySychOUltdVAo4Idv/xZ4925CsLMVOqMRxukLYV56MQypWcp13Pad9XhnXbnwebBCY97cbLHjios1xt+PCk4GH5wOru7vfxqh9ahqPaoRQYfhiacOihHMWY3hcRcJCTZ899tLMWe2OgW8Y2cd7v/jbrlMKRs73ygbx7989csLxOlShWNlbfj13dvQ2dmPuHiL9OM5nd7TjMCfuXbamP3tD9tzZPv92S83i7NKI4Tz59gLGgqGcNklE3HHl+crMzLppP7fLzajorJTniPHDTCTz6j+0iX5+Me7lgrjqApQjl/8eos4DhaLQUrPPB5WGQSEKfknP1ypzJBj9vTe+3dhy7Ya2WMs2WNmsL/PJ4bIP/941ZiVZ37YfuPV8tgTByQwQthjTfD7BrM1HHHFdStU6BC+saESz75eJtmZWPspHdfnFeKYu740H9MnqRnBRWzdVosHHtwtey/M8EsdFxNjxte/uhArlhcoky3YcAK+dx9CqN8BncXO+mTA4wIMJhiXXAvD7IvU6bgmB3778F5hq+VZMBp16OsfzCJduqoQt14zTZmOcze3Yc83f4reYxUwxtphsFrgc/ZJBo4jsOb87/dl1qoK8Ez+/NebceRIG6xWg8xSZ7XSwEBAAiM//sFK6QtVVf1zz/07sXN3vZAosW9WdJzbh4klKfjJD1aOmbN6LjrO8/oT8O5az1p46Gx2wO9DyN0HfWoWYm77Hgzp6kb7vPjyUTzz3BG5R2krkVzM6RgQ3owffHc5pk0d33FN53uP6o7qRuU9qkuLcs679fu4OP88KA1jDo65eO75I8J8kpsbL8QsJN3Jy0uA0+kRB1ZVWRAvzyeeLpWMG2Vjr2ViolW+9/sCePqvpRIZVgEaupSN82fz8xPk8uTaUTY6hS+9ckycRFV4/sUj8vf5HHlRsQ80OytODOH3NlaKk60KdGbKKzplpAodK8qWmRmH5JQYYXjmSxU2vF+FQ4dbhNk0Iz0W8fEW6Y3OzLRLZv+9DZXKZCND8rYdtUhJjpGxUlw3rl9OTrxk9l97/bgy2U6c7MA771bIuKbs7DgkxFtl33H/kUn8WeoYReCoi1fWnRQWzJzMOCTEWZCUYEVeVhx6nQP4yytlykrP6Zw++ZeDYowP6jjraR034PXLZ/wdFQgFA/DveAnod0KXlAWdPQE6W5x8D50e/r1vIeTohCq88M5JtHT0ISczFsmJViHcyUq3wx5jxIbtdaio7VEmW9WjL6H3aAXsRXmwZafDnJwAe0E2LOmpaH5rC1o37lImG3XY0aNtotPST+k46jrqPOo+VpKowq49DfKijiNfxmkdlxWP8spOCaqrQqDmuGRSdTGxMKRmQm+Pgz4hGfr0HAQ7mjGw8RVlsvGuf/nVMrE9wnYcbRLaJrRRaKvQZtFw7mBaT/Xr0wjNUdUwCvv3N8mczZFlg0IQlGqXcQknTg6Wo403Dh1uRVubC2lpwxmIw7KRaZejE1SAoxqqa7rFGB8ZtU9OtknUmuXUKsAM78HSZjF4RzKHMtI64Alg155GZRHzLVtrxKEZWX7M6DmV87YddVAFymYw6keVH5vNRjECNm+pUVpdwPnBI7MdXEdmzbfvrFPWX04nmplnGr1Dwf2XlGiTEUk8yyqw/0irZE+TR7Aj89ymJNlQ3+xApSKn5uChZrR39AuJ0kgdx/fa2/vld1Qg1FqDUFcjEJc0OjMZmwh4+hCsKVUiG+elHi3vEJbfUTrOboZ7wI99R1qUyObvd6Pp7c0yjkZvGq5HTHF2hAIBNL29BaqweUu16DLqtKGgzqPuU6njtu2k7g+NYiCmjuN7W7bXyh2iAr5j+wHfgDioQ6HT66GLTYC//BCCjm4lstHWYDUXbY9ROi4lRmyVquouJbJp0PBRoDmqGkaBQ6GJSKMQWPro8wVFAaqAyzkgUcBIZaBhJ4cOoQqwNI9ZXa7RSISNOhJBqACzzBzVEKnXk7Kxqqm316NENhLFeAb8Z+xDpQHFCLAqsCeKpciRYLYY0dPrUUbYwp7UM5VEcx+ydE8VEQ/XhXsrUqnloB4JwKnqrPZ5ZVRORB1nPiWbqrPqHJD9FKlnnO+xT0mVjgt5+oCAHzCOLnXX6Sgbf2dwnM54g3uJffeRRtEM7kPAoWjdOIom4B6A3ha5n11vNmOgXY3TwL3Gs0pdFgk8D/xcFaj7zzTejXcG+1dVBeNCrl6pJIgEncmMkM+HUL+aYBxnanPPn0n/0lZRZcdp0PBRoDmqGkYhNZUZwUH2v5FgyZnVapTfUSUbjbVIFxP7aXlnMFqoRLaUGLk42TszEuEywjRFsrHkx2Y1RiwZlBmXwZCUeakA++5YlnSmckY+15xsdf0Y2VnxZzSE6ASy5FZV3xvXheymkRDub2RmVQWY/QuSCTOCEz+oR0yjov3jhdRTPW2Rynv7PT45x+HfGW+wMoS97ZEYOvkeP1Ol43SxSYDRAng9EcuCT/+OArDU12Y2SntIREbWEJCWrGbdTIlxMCfGw+8cnPs9EoGBAdgL1fQyUndRh50poOV2++VzVWB7ypkYuSkzS4FVzdvWJ6Wx5yeijgsNuKGzWKGLVzOGiLONz6jj+k/pOEV23PkKVkqrfn0aoTmqGkZhwfwcMZZaW13DFDAVXmenGxMnpKBEEWPc9GkZwqLb3t43rL+CcrKMkH0rcxURPbH3bvq0dHR3uYc5+ZStta1PnMVFi3KVyMbLfNmSfMnWjBwRwkHgZK5dtjRfiWwMPFx4QbHINdLIlEi/2aCUPIbsvjTmRmaxSObFFNIaheRdXBerxShZ36GgY02yJ8qmioSK+yk+ziJlrEP1CLOVDscAFi/KlRJgFZg/M1NKfFtHyEYd19XjwZSSZOTnqAmOzJqRgdzcBLS1Dddx/J7v8TP+jgqQ3VeXWYyQq+e0Y0rIGjo6xEnVF81WNjd1wcwMOPp8w0Y0UbaObreU/y5SdDcYzGbkfWYtAm4P/P3DnfyBji4YbVbkXHkBVIH6l7pMdNoQUOex6kCljlu5vFDugJEVP7wreGdcqJCx3jRzMXQxdoR6h2fDw5lU04xF0MeocfKpX2lz0PYYquNomzBLPWN6BnIV6TgNGj4KtPE0GkaBTsuddyzAfQ/sQn19r0TemBnxCblHAr6ikB2WF9JX71iA39yzDQ0NvXKBkfWJFxajg3d+ZcGp98YfXJMv3TYXbe19qK3tgdGkP50ZoaP4D1+er8wwJ268YTpqartRdrxdjA+W1HoGAtLn87mbZyllYL3s0ok4fqID+/Y3IhRyw2zWS9kqn+VVV0xWOnaIzmDZ8bbToxFYCsf9xjVcvaoIq1YVKpONrMPXXDUFr7xWduqsGqTEm0bngvnZuPLyycpky8tNwK2fmy3ka/X1DmETZWkme2qnTknDLZ+dqUw2OtC33zgTD/7lIOqbXbCa9WDigUZcXlY8vvCZGcp0HAMLd96xEHf/fvspHcdrOiR7jkQ3X/vKQmXBB66JceVN8L/9J4S6mhDSGwdZf/1ewBYH48qbobPFQhVuuGwy6lucKK/pFt1rNOglG0cdd/OVU5CToU62wtuuRXfpcbRt3is/6y1mBD0e6C0WlNxxI1IWq3HwCeqwY8fbpVeVTgz1LtfNYGAQsQjLl6kLFM45NRaPhHsO5wc6jqdz4YIcXHbxBGWyGbLyYVl7AwbWPYdAawN0ZgtCfr9kWQ1FU2G58DplspEAkzbHHx/cc9qOY+UUdVxhYRJuv039KCkNGs4F2ngabTzNGVFX14ONm6tx9GirOIgL5udi9cpCZWVnQ8FsL+e9HTjYJH1Rs2ZmYs2qImGNVQ06M5s212DP3gYpy5w6OU1G5hQXjW0W+lz2HCPmHH2xY1e99MuWFCWJszXeNPWRQId+x856YbHt6nbLnM1VKwsxb06W8guVlzuf55attWhtd0lZ64rlhVi8MFf5/Fmq8IOlLdi8tRr1DQ4kJlglm8kMOsv0xwrnquM4B3TjpmphISbp05LFeeL8q54/S3B+6uZd9ThR2SmBm3kzMrFigbpM71CQGfn9TdUoPUWcxJFg1CNkPlWNUF8PAid3I1h9CAj4oMueCMPkJdCnjm3FyLnsOVe/F9v3N2HPoWb0e/wozk3AyoW5mDTG+vdcwNmpzW9vQdNbm+Ht7EHshHzkXL0GacvVBX9Py+YPCrvu1m01EmzNSIvFyhUFWLggOnTcgdJmbN5ag8Ymp8zYpn5buihvTGdsn6uO81cfh/fAVgSbamVEjWnGQphmLYXOpt5WImES9UjZiXbp3+bzXL2qUImOO9/H02ypaFA+nmblhNzzbv0+LjRHVXNUNfydQNtzGsYT2n7TMN7Q9pyG8YS23z5ZaI7qx4PrU+qoaidPgwYNGjRo0KBBgwYNGjREFbQeVQ1nRVdXP6rZb2nQYeKE1FHzGlWCI1cqKgZJDIqLkoQ5NloQ7O+H++QJ1lPBXFgIU2oaogUkszlZ3iGEO1lZ8cKqGC0gmU15ZZdQ63Ooe35egvKSuDBYfNJ7rELGSFhSk5AwfWJUyVbf6EB7Rx/sMWZMKEmW/rxoQUurC42NDukvmzQxVVkfeSR0drtRV98j/XgTi5OVMYhGgsPhQWXV4BxGPlOyOEcLyLhKPeIPhFBUkIhkRYy6kRAY8KKn9DgCHg9ii/IQk6euxz1SiW15Rae0YbCFgCR80aRHOA+8p8cjPY68V6NJtrr6XiFSJJM5SR15ZqMFTc1ONDezF98oOk5VH3kkkDCxtq5HSrgnTUiJKh13PoEUmUHFf//TCM1R1RARdGL+8swhbNpSI0yxrHqhIUJiGxK0RJo/OJ4X/UuvHMM771ag5xTbKZ3Ui9YU47M3zFB6QYgz89ab6H7pBfja24RPXB8bi7iVK5F6+5dhiFEzAiaMbTvq8Ozzh9Hc7BQDkyQj8+Zl4ctfnKe8L+/QkRY89ddDYoyQtZNsttOnpuH2L8yVETEq0VtWiaM/+yN6D5+UeYgGqwUJMydh2o/uQOJMdYRFRGubC4/+pRRHj7fD4/HBaDQIY+0tN8zAnJmZSmUja/NjTx7Avn1N6Hd7oTfokZkeixuvn45VK9SRUIUdrb+8eBTbd9fLzFSqtJTkGFx58URcdlGJUgOd+/+FF4/h3fUVp9lOExOtuPiiCbj+M9POOFdyvHTcm2+fxGtvnEBnJ5mJIQ70yuUF+Pwts5QbwU1vbETlH/+CvvpmhPwBGONikL56Mab88CuwpKgjjCMOHGzG038tPaXjgjIyjOyrX759rvLeYzrPjz1xABVVXfB6/bCYjZgwIVnuhrHmV/gwNDU58PDj+1FW1ga3JwCTUS/s/1/4/Gzhp1AJkk898sR+7N/fLKPpGNTPyorDTTfOxHJFTPpDx9A8+fRBuffDDM4knbzu6qm45OIJUROE0KDhbIiecJSGqAENkT89tAevvnFcMlxZWbFIS7NLdP/xJw/itTeOK5WPjtYzzx0WwzcjI1ZeJOJ5/qWjePypg0plo5Pa/uc/wt/VCWNaOkzZ2cLY2fPGG2j93d0R562NF3bvbcD9f9yFxiaHzK/MyY6DyaTD5i01+M3d2yLObRxPI+nue3egsroLCQkWGTNEI27P/ib8+p5tpwMSKtBX14R9//g/6NxZCmN8HOxFeTAmxKFz9yHs++7/wlVdr0w2h3MAv7p3B/bsa4TVbEB2ZhwS4y2oqu3G7/+0G8dPdiiTjSy1d/9uuxB5GAw6CTZwJAyzq3/68x4h9FIFnsMHn9iPt9ZXSB9aVrpdZhzTsX7iuUN4e30lVOIvfz2E5144IgHDsI7jPMtnnz8in6kEnVQ6NDyTvBd4P3ANX3/zBB54cI9SHdeybguO/tfv5cxaM1JlNqneaETjK++h9Ce/kEyrKhwra8M99+4QcpuERIvoX4vVgF176vHL32yV+1UVGhp78evfbsXRY22IizXLfGa73YQjR9pENhJ7qSQn/MVvtmIvdZzVJOvGO6KishO//d12yeqrAs8nJxDwDuVdStl4t7J65A9/3IU9exuVycZxVpzc8NY75QghJOeUTiod64ce3Yd171Uok+18BadfqH59GqE5qhpGgaU/O3fVIynRKkqX5TXMUnI0AkeuvPHWiVEzJccLnZ39WPdeJaw2o5SGspSFLzIRsxyIRrGqS5WjBphJ5bgcU2YW9GYzdAYDjEnJMKakoG/PHniOHVUjWzCEl18pg8ftl1JfMiUyusqxOZkZsThW1i6GgCq88c5JcRJys+mgmkS22Fgac/GoqevF1h11ymSre+5t9NU2IbakAKY4O3Q8D3F2xJbko7+hRT5Xhe276lFT1yPPlPuf68bSs5ysOPQ6PHjz3XJlsu0/2ISjZW2yv1jxQNm47yirZ8AvI3UiDaMfD5RXdWHPgSYkJ1qlkiCs4zLS7NDrdHjj3XLJuKoA50G/t6FSHAUalmEdx+9j7Ca8t6FKfkdVhua1149Db9CJ88w149qxTDQ52YrdexpQXt6pRLZgIICqh5+XWaX2wlypeqD+NScnSulv155DaN+yB6rwxpsnJDuek/OBjos7peOqq7uxdbs6Hffu+kq0yIzeeGnvoWx2u1l+bmlxYsNGdYEbMq1z3BvXKazjmLWnbAyWMHCiCrwzeXdSx/EuPa3jsuMku/ryq8eUBW44Uo3j3lJSbKd1HFsueG6ZSX31tePiaGvQEO3QHFUNo3DkaJsoMF4KI8EB0l1dbpw42a5GtmNtEnmOVKbKsRzs+zlytFWJbJ6TJ+Dr6IAhZXSZlN5uR3BgAP2HSpXIRmODPSqk9R9Z7iNzcoMhlB5qUSIbHYJDh1tktuVI2dhnyVKqvfvVOdEt63fAYI8RB3UodHo9jLF2NL+7TZls+0qbxTgaOT6C68j1PFLWriyodPhIqziikcZH0LHhbD9m91WAZdKc02uP0HNP57Wzqx8nqwb731XoOD6zSD331HHs3+bvqACzV1wb3gMjQceG98ZhRfq3r6oerqo6mNNSRn1msFkR9AfQuatUGZ8Cn1l8QgQdZ9TLGaZToQJ0pHbtbkBMzGDwcijCga+duxug0hkMB2uGguvIkvODpc3Kgkqlh5tl/UbqOMpGG6WmtkfabFTZcdRxkXhFGFQinwGz0ho0RDu0HlUNoxDOckTqX+DFRaeG/Y1qZTt7D6sKsB+KDVs6/ej+MVlLHX9HTQSTa8LndqbeYorHvjhVz5SyGc4gG8cCcMC7KgS9vlFOahh8P+T1i7Giot+HxFhnWjeW2/r9PKtq1o6ynQkGvV7KmAKK9AjXRI7kWXScqmzv2XQc3+OKqZSNPamR9AjXkvIp179nIdgJ+nxK9S9n9UYCM9Scua0KZ9Mj1L9nO8tjDa4L1ycS+H4wMHieVUD2+hnUPvWv2EqKzgP/7pl1nF6pjjtfweS4yurb0Kez8lfLqGoYjcLCJCkTiVQW4nAMSKaVLI9KZCtIhM1mhKtvdJaIpTZmixFFhWoIMyyFhTDExSHQ2zvqs6DXKxk4S3GxEtlY7sPyaD6/keBlRf1XUjw6EzEeYCaG7L7stxwJOoADXj+mTVbHmpw8fzr8zr5RJVz82edwIXnBDGWkFJMnpoghF6m8zOnySglwQrwaplgSsHBdIhlD4aqIzMxYJbIV5iVCp9dhwDvaAHe4vFJ2XpCboEa2giTJ0LDMdiT6+nzSu61Kx+XnJ4r+j6RH2ONOB7aoSI1sMQXZsKQmw9cdQf/6A7IXE6ZOUCJbQrwVuTnxZ9Rx7OeeqkjHcV2mTkmTvRVJNt6r06amQxWmTUmDd2AwGDgSLqcXBQUJiLWPrv4aD/DOpFiRddwAUlNi5O5VZcfxPHJvRZKN2WieZw0aoh2ao6phFGbNzBDqd/ZBhQl2eEmwrJYG5tLF+dKvqgI00GbPykJXp1su0DDIdtrR3o/pU9MxeVKqEtmMycmIW70GAZcLAafz9MUa9A7A19QES1Ex7AsWKZGNhu8laycI0yRHD4RlYxaVtPrssVm+LF+ZoUTZWNrFssKwbLz8m1qcUu64akUBVCH/xkthirfD3dCCUGDw0g8FgnA3tsIYG4P8z16mTLZVSwuQnGhDU4vrdFaB69fV7ZZ1vXiNOvbapYvzkJkZJ/srnJERVuxejziIF68tkV49FZgzIwNF+YlobXedNuQoGwNgLNNctihXGIBVgGNoZs7IQEdH/7CSRn7P8zFzegZKitWwsJIXgHrC6fRKeXL4rPKeaG3tE7nmzlYzCsYYY0PujZcj4BmAt6tniP71ob+mATH52ci4eLkS2egwXHrxROl/HqrjmPVqanJKKfWqlepYsC9eO0H6Pskgzkwbwa+tbRx3ZcLaC0uUyXbBqiIkJtrQ3Ow67RBy/ThyhQH1S9eqGxNGVl/enXyG4YokysY7ls+WzLqRWh/GAwvmZUvQi20/w3Sca0DOLlnXVTP9a9BwLtBKfzWMAkcffOeuJbjn9zukhyHAiysEacRftiQfX/zCHGWy8UK68ysLpfeCvah0TnlH0cmZPSsT3/zaIqWU66lfuA0BRy9cO7bD380eN51kUq0TJyHrBz+C3qJuDuIVl02SvpQNG6vQ0DDYG8ilIhvrt7+5ROmltXxJvszHe+X14zIPlFVotOVIIPOVL81Hfp66yG/KwlmY/i/fQNmvHkJfdcNgqVcoBHNyEqZ873akLlF3Hkgw8vU7FuDPj+8XNl2C68YMw43XTMUqRcEHgj2W371rKe79w040Ng0GbihbjM2Iyy+dJKOuVIEkQN/+6iLc++fdqKrtOW2cW8wGLF+Uj89fP0OZbNRf37hzkRiXZIqlw0pQx82ZlYWvK9ZxX/jcHBlZtmdfkzCIhh2xSRNT8I93LVU6Hqzo9s9goL0TjS+/i76KWlFwzJzHFudh5v98H5ZkdXpk9apC0b8ko6L+DZdx0/nnnZaboyaDT8yemSljaJ76a6kw1opsocGRSLfdOkdpRpVZv298bRH+/PBecQgpG48r2YlvvGmmsgBruNeee/7eP+ySXtRwAMIWY8KVl08SPacKdJC/+52lYsdV13R/oOMsRtmLt9w8U5ls5ytCp/6n8u9/GqELqeSSjxI4HA4kJCSgt7cX8fGfzLxG0vW3tbUhPT1d+gHORzALcrC0RUaGsH9l+rR0KRGKhtlbjKySSv/4iQ65HDhgm1mIkYQLKkB5PCdOoP/QQYR8vlOZ1IXCAjyWOJc9R9lIqsR5fiztzs6Ox6IFOcpnH4bB6O/eA00S8aWTumh+LuIVla6OhKetU4iVBtq7YE5JROZFS2HLVFeSPBTMAnKUDw1he4wZ8+ZkyaiascS56jhmAjk+h5lVi8UgzhZL+KNBj4iOO9IqzMnsKZsxNR0TiwdLllWDGRkG406cGjE0ZXIqpk+LHh1HuSgf+QpY6cI9N9ZO6rnqOOeJKnRs2z/IAFych/TVi2C0q8mQR5oJund/k1QopafZsWhhrpRhRgOYpSRzM6seGLhctDBH5qdHAygTx71Qx7H8fOGCnDGfPXuuOo5l+qLjmhxCPjVvbra0s0SDHmHAa/+BJtTUdsuM7VkzMjBxYooS2cbC1h5PudefrENsnDq5XU4HLpqUf96t38eF5qhqjqqGvxNoe07DeELbbxrGG9qe0zCe0PbbJ4vz3VF970Qd7Aod1T6nA2snf/ocVe3kadCgQYMGDRo0aNCgQYOGqILWo6rhQ0tGKqu6pCyOZWcs/z3TiJPxLovj7E2Wn7EoYOKEVMyZkyn9tapBecqOt8scSZIXkSF5/rwcKQmKBtmqqrtx4GDTkNLf3Igzc1WgobFXyrtYFsferUWLcqOG8KG/qRWt63fA094NC0t/1y5DTE4GogE9vR7s2l0vfb5kUV4wPwd5ilhrR4LPcvfeBul9Y3/U3DlZQroTDWVxJAFiGWZNTTcMRj1mTmd7Q7TouABKD7fK7FKCJHGzZmRGRekv+93Kjrfh8NE2BKjjipIwf262MuKYkTqOdxbbG/h8c3IGdRzPRTSgrr5XZqaKjkuzY/GiPCGMiwaQQHHXqdJfEhgtXpQrejga0N3jxu7dDUNKf3OlRz8aQBI2lkyzvcFqYelvlpTDR4OO4z3PWbRsbzBRx83IiJoWLg0azgVa6a9W+hsR7LW4+/fbxakJN+Gz/2jO7Czc9c3FiIu1KL2wSBBAohE6rJTOaNBh8qQ0/OO3lyq9WNmPd98Du+RiIOFTeI5ZQUGiEMuMJR38h+05shI++tgBbNxUBbfbf0o2jq6Jw13fWKyUMINq6PkXj+LV14+LARcGmVfv+PJ8LFmcB5Woe/5tHP/to/B29g7Ow+WQ96QETPrHL6LwliuVykbH/sGH9kh/GUGNTqP8qism4ebPzhwzg+RcdNzxE+34/f070dLiGiRTItGI1YiVKwrxlS/PV0q8w6DIb3+3HbWnyJRCpwjjyJb5za8vVurYkLX5nvt2yPqFZ1ZTx9HA/M43lwpLrCrwfN5PHbe/SYKZ3F507MkwSvKWsSQF+lAd5wvgz4/sw5atNXB7/MJ7xv3PMUjf+dZSZYzwBPf/M88dxutvnkRfv/f0+E2OMPnqHQul51Il3llXjqefOSQjdHRDCNFuvWU21l6kjvWX2LmrHn9+ZC+6ugbJuwiez2uumoIbr5+uVMfRDuGd/wGZXUg4H9asKsKXvjhPaWCprq4Hd9+7Q3RcWP+SMI6BzG99ffG4c1Oc76W/7x5XX/p78RSt9FeDBrnsaSSVV3RJxDcvL0FeJLXZtacejz9xQJlsVLZ/fHAPDh5qRmKSVeQiaQFnhB451or7/rAr4ry18cLTfz2EbdvrJOKblxcv8qVn2FFd3SUXRnjcjwq88eZJvL2uXIzxsGwcH9La6sTv7tuBrq5BR0cFNm+txXMvHBFeu9zcQdmys+PQ6/DggQf3CBGEKnTsKsWxnz8If78H9pI8xE4oQGxJPvwDXpT98s9o375fmWz1Db24/4+70N3jkfUaPKvx0OmBF18+ho2bqpVmee+5dweaW5ziKIhsufGSdXt3fYUEJVSBDhaDXVVVH+i4/PwExMWZsX1nHZ58ulSZbNRfDzy4G4ePtiI5KQb5uQny4veHjrTKZyp13BNPH5Q14lpxzbh2XMPKqk787t4dEec2jhdeea1M9hb3GPfaoI6LFUZW7kXuSVXgWXzh5WNyNsOy8czy7PIM8yyrwsHSZjz25AGZycx5r5SNmWi3x4dHntgvpFmqQN3PO4CzP8M6jncE3S7eGVu21SqTjXemBOJaXcjKjJMqFr54x779bgXeeOuE0kwqbQ7aHhkZp+y43HixTWijMCihQcP5AM1R1TAKpYdaxICjcqPCDSMmxiQlSrt2N6Cl1alEturqbin5ZaZt6AxGGibMpDIDwbJbVZcWL00acIz2hqO8ZpMBmVlxEtVkFkIF6CC/+14FTGa9RMnDsjHay/Jfzsuj8akCNLrXvVcuWS0yTIZl44w8Gpm9vW68r9Dhqn/hHficfbDlZMioIYJfWfZL57XuubeVybZ5SzV6etzIyoqV9RLZdDrJuNGXWfdexemKiPHG9h11aG11ITsr7nRWgbIx4DXorFYOmxM6nmDpe3VttwRqhuo4ntu4eAu27aiTEkMVqKjswtGyNqQmxwxrF+D3fI+f8XdUgKXl23fUyxoNzThzDXlWw20FKsC99N76Stlb3GPDdVycMIrvUKTjeAaZsWRKi2dzqI7j2eXcTZ5lVeBZpGNDFuJw2Tu/8mcy2q7fWKlMNup+Bhi4v4bqONoAHJ237t0KZYEb6gnenSxBHqrjEuKtMJn0sq6qgtMsL6fNQdsjXLlC2XhuY+PM2LK1VmlwWoOGc4XmqGoYhZraHgQCoYj9RqTRZz8GFaAS2ep65EK120eXrNhsRonmc/yKCtTW9cow7UijBuis8jJlL5wK0GFgOWF8BNlokNBuUmX8cj/V1zvEwR8JXqxms1HGEKlC176jMMbZI5aXmeLs6Np/VJmhVHaiAyazIaJsXE/2hTIrrSqoxHUJG5dDwX1IB5uZLlU6js7DUCd1qGx9KnVcbbe0DTAwOBJ8zzMQUFZhQN3K8xpJj/CcBqnjFK0b9xIdmkiycQ9yL3JPqgDPIPsXz6TjGEDkWVYBrgsDvJHuVMrGPVdWpib4S1D3c6xVJB0XH2eWnl/uSRVgYCZc+j5aNovcua1tgyXB4w2eQz5b2h6RZKOtwrXTcO5go5nq16cRmqOqYRQYGeRxiGR807ijUjYq6i0LRy0j+QXh91T1hDCCyrVhX8tIhNdSlWz8u4OyRVZ0oeBgVkQFjAbKBomOR0IgGBRDRRUMVjNC/sjljKFAEAbLB9nz8YaFzsGpHsaRYLBJzqqiPTe4n3RnnIOsUjbRcaHIOo77kPuR51kFJPshfdCjP+N7XFFVvb1cE8YdIukR6YELqtdx1BeRoVO2bqdlO8NZ5RnmWVYFrsuZZQtKMEwVqEfOruMG7xAVYIDhTDHK07JFoY6jjcL9qErHadDwUaDtUg2jwIHQJDxxOkdHKVkqwpKbKYpIKWZMS0d8vBXd3R+QKoRBpkL2X5DVTgUmTkiR8uOhhA9h9PX5YLYYMGtmphLZsrLihIWQEd6RFxdLk8jqPHtWlhLZSOhAki72II2UjWRZvPBVEo1krF2OQL8boUBglJPqd/Uh8+LlymRbMD9bnAYSZQ2TLRSS9eRZUEV8NmtmBgxGnVRAjJStu9sjxGK5ipiJuS5k54yUienuciM11Y5JE9XoODKrx9rNEfsp+R7L9qgHVYBrkpISWce5XF4pT1alf7mX2IfHvTVSj3APUsfNmqVG//IMcl0i6TieXZ7hhfPV6DgG2ZYsypXS6ZEBCP7s9gSUktlR9/upa/3B0TrOOSAs4uNNChTG7JmZ4vCNLO8VHdfjRlFhsvSuqgD3GwO8tD1GgueXZd0TSlKUyHa+goE41a9PIzRHVcMocNTAimUF4vh1dvbLBcGSWpaP0mm4+qrJyhgx2cN4+aUTxfBoa+8T4ie+Ojr6pFzvwjXFyMxQczGwVPq6a6dK5J79UF6vX9aOziEvBo5ImDI5VZkxct01UxFjM6OpySnrR9n4jMnISuOYbKeqcNUVk6V3q6HBIT1RzLg5nQNobHKIg71yeaEy2fJvvBT2why4Kuvgc7gQ9Afkq6uyFvb8bOR/9jJlsvGcctRLU6NTjDauGw1OrmNiohVXXzlFmWzz5mZj5vQM0RvsweN+477j/rPajPjMtdOUjYFhUGnJ4lxxang2B3WcX84CjfNrr5qqzPhlsOuSiyag3+2TPtmwjuP3fO+SCyeII60CXJPrrp4qa8S1Oq3jutxCCsQ15dqqAPfS9ddNF2d5qI7j3uMeZJBw3hw1wTiCZ5FnkmeTZ5RnlWe2uckpZ3j58nxlsl2ydoKwv1M23qOUjYGHhoZe6TG/aE2xMtlWrSiUO4B3Ae8EysY7grLyzrjy8snKZFswL0fuTpIp8S49reOanVIyzbOiqtqGDOEc4cOzSRuEstGhpm1CG+W6a6ZFxTgpDRo+DNp4Gm08TURQoT37/GFhKmQUWKfXCY0+L1s6iipncFHhvvp6Gd5ZVyEKmAwViQk2XLy2BJ+5bprSWao8Tu9tqMQrr5aJI02DjtH0FSsK8PmbZ42p8Xsue440/3yujU1OufB5mZKq/otfmKt8lt/RY63Ctlpd2wO/LwCL1SjZ/dtvm6ss+BCG40QVjv38z+guLUPAMwCD1YLEWZMx7UdfQcK0CcpnHz76xAEhGaORxJIvjkPiWImxzOCfy35zODx44ulSmfFK45K9gjR8b7xhBpYvVWeYE1yrvzxzCJu31ogBTEeHTiJHXlxy8QTlOu6VN8qw7r1KycwQnCd8ydoSXHvlVKUjL4T87N1B1mY6z9Rx7HlbtbIQt9w0a0znRZ/LniPBzfMvHBFnIazjOKv0ts/PlmocleAZfeqvpdL/zGfMteIZ/dJtc5GeHqtUNhIoPvbUQZSXd0hgmiW3HOdD/cvRQypB8sZHHz8gTNgD1HEmgzivX/jcLJnvrnK/scrhiacOykg60XFGPXKy4/DZG2ZgySK1Y9UYECG779attXC6BnUcM6nXXjMVay8sGXcdd76Pp3n7WK3y8TSXTSs479bv40JzVDVH9aygEq6t7Zb/hgkTkocx7aoGI78c7M4NXFyUpHS2ayRHv6KyEz5fEAX5iUgah7mH57rnWGpWUdElhjovVNUG0lDQ6OUzZTQ/LS1mTGcyflRQVTpPVsPT3gVLShLipxRH1dB0Zhza2vqEFIUZmkgkRp8kPoqOozNNx4HG78SJKUqDSSNBR5DzBrleLIUbS0fro4KlyVXVgyRnxUXJUhIcLaD+oI6jM8gybjrS0aTjyss7xeFiYCSadBzXizqOJZnp6XZhjI0WCOFUTTd6eweQlGSVuyuadBxnH7e390uLD3XcWFdkfFQd19jsFP0xsSRFaTBpJNgqRSI09qRSx6nKpJ7vjupbR2uUO6qXTy8879bv40JzVDVHVcPfCbQ9p2E8oe03DeMNbc9pGE9o++2Theaofjz0fUodVe3kadCgQYMGDRo0aNCgQYOGqEL01DhpiDqwBGjDxiocPdYmZSxk37tgVRHS0tQQeQxFf10jml97D13b90q5UvKSeci6+mLYi9T2hISZkdnbu2dvg8xDnDIlTcgoooFhL+hyom/7+3Dv2Y5gXx/MRSWIWb4G1mmzoqKUcOu2WmzdXiszNln2u3JlIRYtyFFefkbWyd2lzdi+rxFtnf1ITbZh2bwcLJmTrbzEi/t/774mbN5ajfr6XiQm2rBsaT5WriiIilJ99h5vfL8aFVVdsMeYhEF09cpC5f2CRCA4AF+wD4EQGXZ1MOpjYNLbodcZo6KUe+P7VThQ2iw/kwhozepiZEdBqWhHZz/e31SFPXsbhehp2tR0XHhBMYqLk1WLJn3Rm7bUSD9+X78PE4qTseaCojHtZTxXuD0+bNlai+076kTHkaV41YoiYe9WruP8Qew+0oIdh5rR2eNBWpINS2dnYcH0DGXjX4bquN17G7FlS42U/1LHkUhuxfIC5aX6lO3wkVa8v7la+nxJNLl0ST5WryqMinYkluev31iF48fbhQWYBEtrVhcJMaWGjwaSYp9hit64/f1PI5SW/v7f//0fXnzxRRw/fhw2mw3Lli3Dz3/+c0ye/AGL2wUXXIBNmzYN++e+9rWv4YEHHjj9c11dHb7xjW9g48aNiI2Nxe233y7/bqPx3BSYVvo7Grv3NuD+B3ajt9ctFwF7B73eILKz4/Cj769QSq7Qe6gMR//5Z3A3tsBgs5LSVsaHWDPTMO2/f4SkhbOVycZL9Je/3iqDtDljzaDneI6ADHq/846FcrGOFT5szwV6utD5+5/DW36cQ9YGXx4PdFYb4q//POIuuwYqSR9+c8927D/QJAPU2ctIx5Xz/S6/bCJu/8JcZYYcDbg/P3sIW/c2IBTkvEMDBrwBkWfZ/Bx89eZZyuYzUn0/9ZdSvPbGCXEYeFa9voDQ2M+ZnYnv/+PyMWPoPhcd9+bbJ0U+koyQ6ZdrydekiSn48fdXIiVFnbHkC/ZjINCJkHD+h/dWCHqdCVZjKgw6df2gx8ra8NvfbRc283A/Gfveyfb7ve8sE8dQFdjP+6vfbkV9gwNm8+B8UOq4+HgLvnHnojEdZfJhe44s9b/4zRacLO+UABJfHrdfCJVu/dxsXHHZJKjkVPjNPdtwsLQFOj1gNn2g466+crLIp0rH+fxBPPTSEew81CI6hfqX/b0kUVw+Owtfvna6soAc5XnsyQN4862TpwmoKBstVzKLf/8fl40ZSeG56LjX3jgupGxuj19k8/s4Ui2IKZPT8KMfrBiX3u0zgYHfPz20R0YNWq0GmRHt8wak9/hHP1g57v3R53vp7xtH1Jf+XjlDK/0dV9AB/da3voWdO3fi3Xffhc/nwyWXXIK+vr5hv/fVr34Vzc3Np1+/+MUvTn8WCARw5ZVXwuv1Yvv27Xjsscfw6KOP4t///d8V/Bf9fYDscA89vE8uVkZ8SUSRmRmH3Nx4of1/6JF9EYdIjweCPh9O/uw+uJvbYC8pgC03C7acTPl+oKMLJ352n7CyKrtQnzggTmpOTpzMT+Pa5eXFwzPgxyOP7484/3W84HjlWQycPAZDRhaMmdkwpqbDmJsvjr7j5b/CW1etTDY6NPv2NyI1NQY5OfGStefes9mMeOvtcuw/OJhVUoHt+xvFSU2ItSAnMw6pyTHyNTHegm37GrB1b6NSFtHX3zohBhLXi+tG44NEVFyzN946qdSh+euzh+Rc5OcnCNskiW2yMmNx4kQHnnnusDLZgqEABgJd4qTqYJQMKl/8PhjyYSDQrUzHMeDwp4f2SmUGn2lGRqy8OCeUjtiDD+2V31EBrsnDj+1HfcOgjsscouMYbOLdwDEdqsA9xb3FPSYkSml22XuUm3uRulkVeBZ5Jnk2B8/ooI7j2eUZ5llWhW0Hm7CjtBkJcWZkp8ciNdEmX+PtZmw72IydCmXjmvEOYLDhtI7LiZe7gnfGW++UK5ONRGfPPHdEAgz5ead0XDbPRawEm559/ogy2ag/eFZpe/B88pzSJuHa1db14tHH9yvTcecrgqGQ8tenEUod1bfffhtf+tKXMH36dMyePVscTGZH9+3bN+z3YmJikJmZefo1NJKwbt06HDt2DE8++STmzJmDyy+/HP/93/+N++67T5xXDR8dLOdq7+wTRsKhEV5Gznk5lFd2obyiU4ls3XsPwVVRA1t2BnRDIpz83padCXddI7p2DN8/4wWWXR4ra0dysnUY4yrXMCM9VpzUnbvrlcgWcPTCvWcH9LHx0JmGR5/1ySkI9rvQv2urEtkYJWeJo9liGFXGlZBgFaN8y9YaqMKWPfWSSWXZ6lDE2EzQQYfNip5pOGLO8nLOZxwKZuG4lizPVOXU7NhVL5F86oyhYAYpPsEiVRuqAjf+YD9CoYA4pkN1HL/XgZmHAQThUyIbHRbOr4ykfzMy7FK1ocqpYTvIyfIOyYSP1HGUt7OrX56rCnAv7drTIHtrZIUD96DD6cWOnXVKZOMZ3LipSs7kSMZVnl3vQEBaHlRhy/5GeYYxI1oF7JKpDGHrfnXBOJb7cv14FwwF15KZX94dvENUgPupr28AKSnDs6bcf3HxFik/Zym6CvAssLyctsdQPcJzSxuFtgptFg0aoh3qG3GGgOlsIjl5eJ/LU089JY4ondSrr74a//Zv/ybOK7Fjxw7MnDkTGRkf9J9ceumlUgp89OhRzJ07d9TfGRgYkNfQtH64zIOvTwL89zBa9Un9+8YTnZ19UghnMvL/h0dwYmwGdHb6JbIfLBn//zZPazuCgSD0MbYRkgE6q0Vq+D1t7UrWnTMFvV4/kpMt0I2QjjYd7wqu7VjJdrY95+vuQsDrgT42AaHTZY6noNMhZDDA39GmZN1cLo9k8ekIjlw3gn01zc0OZWeptb0PMVYavqNl4/ttnS6p7FBRttfU7JBS5EjrZo8xyngTp9MjPV2fND5Mx7W1u8BY0uAEieHy8Vn39HjQ0dmHhITx7+MKBP3SzkDZRmYV+GMILN/zQqc3KtEjIQyWmI9cN77HqPrg/NLxPw8dHf0Y8LIE2TZqzxkNOqU6jnuJ5dEcqzJSNspFHcw9qWLdHA63VCjxTEY6q3S4mprU6DiuZ3tXH2wWBh5Gy2Y1G9DapWa/Ec0tDliskXVcjN0Eh9Mjd8hY9Lx/mI5rbXeJ4xdZxxnR6/BK8IbjdJTYcaf2/Sg7LsYIh2NAes1ZKTdeOB/tYQ3qETWOKjfwd7/7XSxfvhwzZsw4/f7nP/95FBQUIDs7G4cOHcJPfvITnDhxQnpbiZaWlmFOKhH+mZ9FAvtXf/rTn456v729HR6P5xP776HjTSV3vvWo2mN8yMk2ICkpJIpuKDgXtDDfBIO+X3o3xhsuuxWByQXwJCdCZxwe/Q0FAggYgL7YGCWy6XV9yM8zwWoNwmIZ7bQEAgbExfrHTLaz7TmWTDuyBst89THDybD4+wF/AN7kdPgVrBuj4Vw3lg7Gx482Rug48HMVz5QoyNKjtSOApFj/qM90AT9SkmyiO1QgL8cIp5PBvdHrZjQFYTEb0dfXA6/X+Yn/7Q/TcakpIWRn6SPK5u4PIC7WiIDfiba20es61vAF3fAHuCaGUQGGwZ7VECwGA/R617jLZrUMIDfbiISEwKhZuOx9C4WM8jtKdJy+HwV5JsTEBMS5GnlOg0ED7HafEh0X8LuRn2eEQR+ALSZCj37AIHtSxbqxpzIvd7C3PT5u9Hmg/Z6Xa1Sm4/JSdehy+JBoi2Cr+HxIT1anf6n7aZZF1HHGoPSnulw98Hj0467j0lJ0yMqMrOP6+oNIiGPPKnXc+Ff30dbIzjJElG1gIAiziYFh17g+Vycvq/MYvBpYXaXy738aETWOKntVjxw5gq1bh5cf3nnnnae/Z+Y0KysLF110ESorK1FSUvI3/a1//ud/xve///1hGdW8vDykpaV9omRKNID47zzfHNVFC2Px/Es1Em1jz0UYVNYNjX0oKkrCnDkTlJArpCyLR+f9T8K9+zBiCnJPG5mUzV3fBHtqMoqWL4YpfvwHvKempsH6Ug3KTnQgLydumAHc0emGwWDEwoWTkJ4eNyZ//6x7Lj0d5sxM9O/YDGNWLnSGD4zMgLOXNxdSZs+DJV0NScvkSfl44aVjyM42wWT8QLZ+tx/dPUHceH2JEFqowPSp/dj/0hF4A8x8GIcZn+3dIaxaVqRMttmzvdiwqU2IPIaWJpOluLFxANdeXYScnKwx+dsfpuPmzjHg9Tcb4HR5kTgka8qMYH29W5iJJ00aO3KxsyEY8qPfP0gcM5ThV7In8MOgsyDGmK4kSx4Xl4Rnn69C2XE3sjI/KP+lbM2tbqSm2LBwwaQxI5A5G1JSUvHci5StC7kjdFx7Rz9MJiMWL5wspE/jved4BNNS67F9Zz3y8kzQD5Gtp3cAPp8O8+ZMRHq6Gvb1CRPy8eprx5GTYx7Goktm4t7eED538wR1Om6yG8+8fQIDIQMsQ8qm6Vh39uiwZlmhMtlmzfTg/c3UcQHE2D44qz5/AM3NA7jhMyXIzs5UouPmzdXhzXcapXIlIf4DHRcIBtHQ4MaqlYUoKVEziWDBAhtefKUW3T0e0RlhUI/UN/Zj6pRUzJpVMq46zmpVz/Su4fxDVDiqd911F15//XVs3rwZubm5Z/3dxYsXy9eKigpxVFkOvHv37mG/09raKl/5WSRYLBZ5jQQV0SfpVFIBfNL/zvFAamosbr5xpjDt1TU4EBdrloivyzWApCQbvnjr3GEG+3hCb4/BxO/cgbKf/gb9FdUwJSQIYaevxwlTXAxKvnU7LIlq2ND4mG+7dS5+ffc21NY5hOmXz97l8sJk0uOzN8xAdlbCmMpwtj2XcO3N8NdUwtdUB31MLGu7ERLiMh1iL74C1snTlLFOXnnFFBw+0ib9b1abSUocyRQbCISEKZlMoqrO0epF+Sg91o6DZW0wm/SwWoxS/jjgDWLm5DRcsCRfmWwcNbB8Wb6MvKAxTmeVDjTXbuKEFFx95dQxle1s+23ypFRceskkvPb6cTkDLH8ju2ifyysjVqhjVK2bHmZYkYiBQA/d+tN0DTpdEEadEVZDEgx6NUzOdrsFt35+Lh74027UNzhPlw1yDcngfOvn5srvqACfF/U/Gbrr6gdlY+8s7wbeCTdeP2PMAnHnsudu/uwsVNf2oq7eIWtlMg7qX/4zV181BZMmpSrTcddcORVHj7WjoqJTiIGYkaaTygwNR0nxLKs6DxcsyMWh8g4crewUNmKr2QiP1y8VVLMmpWHlvBxlslH379nXiG3b69Bj0Mna0YEmm/Okiam44vIpynQc2bfXXliCt98pl4BcrN0sa8Yyb5Kf3aRQx+VkJ+Az104XErGwHqED7XJ6hZDqts/PhWFIwHo8cL7ZwhqiA0rH0/BPf/vb38ZLL72E999/HxMnTvzQf2bbtm1YsWIFSktLMWvWLLz11lu46qqrhA04HPH705/+hB/96EdS0hDJIR0JbTxNZJBR7+11FSiv6BBlPX9uNi67dGJUzAPtOXAEjc+9ga7dB6XmLHHeDOTedLXS0TRh1NR2CxPh3n2NUqpXXJSMS9ZOwOJFH2SAxwLnsuf8bS1wrX8L7t3bEPJ6YczKhn31xTJLdSg5lQp097jlwt+8tQbufp8Qtlx0YQkuvqhE2fiXMPrdPqzfXivESQ6XF3F2M1YuzMXaFYWnCEfUgUQj6zdUYf3GSrS394mjv2pFAS67ZOKYzso7l/3GPtDNW2rw3oZKIQHic1y6OA+XXTpJWFlVQkreQ254gy4EQ4OleQadDWZDnNLRNEPnz1KPcI41MX1aOi6/dGJUzAMl2ynP6t79TbIPJkxIER23cP7Yzjw+lz3X1OzE2++cFDIvng3OY6YOWbmiUJxqlSAT69vrqONq4XH7xGG4aE0JLrqwODp03O56bD3QBGe/Vxh/6aCuWZiHGMWzShl8e3d9hcx1Jz+GLcYks5gvvWTimI5/OZf9xjuec3vfW18pnAEMQCxbki+2UmaGeh23a3cD1r1XIWeWrQQL5ueIHlExYvB8H0/zcmk17HHqnmmf04nrZhedd+t3Xjuq3/zmN/H000/jlVdeGTY7lRuCc1VZ3svPr7jiCqSkpEiP6ve+9z3JuoZnq5LEhGy/7GHl2Br2pd522234yle+gv/93/89Jzk0R/XsIEEFL3jVF2kkBL1e6Y0yWNQbliPB0qRgIDSK5XGs8FH2XCjgR8jvh85sUT5oPtLFz1mgzFxGm2xUl4zmM+ug2uiNKNtAQLL3I3sble+3EOcwD/ZcqprHeDaEZ6lG234jwqzN0ah/o1nHsfeduoSOQ7Q9V8rFzBuJ4qJNNpmZ7gsMkrRFmWzUIxy3Qv0bjTqO+jc8vzcq7TiDblhrzXhDc1Q/Hvo+pY6q0jDZH/7wB/l6wQUXDHv/kUcekbE1ZrMZ7733Hu6++26Zrco+0htuuAH/+q//evp3WbrAsmGy/C5duhR2ux233347/uu//mvc/3v+XjFeRsjfAr05+hzUMORCiNKl0xmM8opG0ACxjYMR8reAhhsd6KiVTXHm42yyRbMe0emic79Fq4N6Pui4aHUYwjpuPBytvwUMwEWzjrONGKETLYhm/UtEs/49X8AyfbVkSiF8GqF0535YMpeOaThzejaQFfjNN9/8BCXTMJQttr+xVch3YnIzoyrCyqh0W9vgKIf0tNioMkq4tykbSW1ISBVNxiZl6+33wesPIiHGNIw8IxrAEmD2+LCki31m0QRvjwPerl6Yk+JhThrbfuOPCq4Z184eY5Ze8miC3+2Bp7kdBpsF1sy06NIjXi/c1HFGY1TquNY29pEDGen2qHJuqEda2ziaKRSVOo5kgMwipabYo86B4MzXvv7o1HG9vYPjwhLirYiLU9ML/aE6zm4e05LfvwVuj0/KkukQpo0RmdjHqcpoa+8TEq+R85k1aIh2RJf21hA1CAWDqH32LdT+5XX0N7RI/2L8lGIUf/l6ZF64VK1soRC27ajD62+ekL43jggjOQt78tasLlKuhA8cbMbLrx5DZVWXlFGlpsTgkosninyqnem6jj5sOtaC6jbOEwzBbjVifnEKVk7JEOIRlahv6MULLx2V9ePFGhNjxopl+bjhM9OVG0zulnZUPvgMWtZtRcA9AIPVgoy1yzDhzpthy1bbM0ij8uVXyqQXlMav0WjA3DlZuOG6acjPT1QqW8AzgKrHXkL9C+9goLNHAl7J86fLuiXPnaZUtmAggNq/viEvd1PboI6bVoKSf7gRGasXKddxfJ7Ucey5JHKy43HlFZOl/1i1jiN/wcuvlaG6ulv0CHstL714Ii69eIJyZ7rseDtefPkYjp9oF0c/IcEqfaBXXzlZeVaprq4Hz790FAdLW+D3BySoRGbY666dirhYtTqO/e3Pv3QEu3c3ioNvsRqFyOjGz0wXvgCVcDoH5G7Yuq1WemkZFJk3N0vuBvYgq4TH48crr5Vh4/tVcDgHZP+zn/z666YJ2ZNKsPz97XUn8c67FeJEM1teUpyM666ZJneEBg3nA5T2qEYLtB7V0Th+z2OofPBZQG+AOTlBBr0NtHfBaI/BzP/6DnKuWK1MtnfeLcejTxwQJZyYMEh33uvwiPF26y2zcc1VU5TJtmtPA+69f6dEfhOTrDDo9XJ5BfxBXHHZJHz59nljZmR+2J6r63DhqS3V6O33Is5mglGvQ783IJnVWQWJuGlpEQyK+i457P5/frZJvtKwZO8WmWtJ+z97Zib+6UerlIzjIAY6u7HnG/+BnkPHYU5MgNFug7/fA193L+KnT8TCB/4L1rRkZUbSL369RZz7GLvpNOtvT48HWVlx+JefrEZeboKS/UZHsPRffoPGVzdAb7PCnBAnFRoD7d2wpCdhwd3/D8nzP5iZPd4o+/XDqHzkBXGeJTse1nFxMZj9P99D1iUrlMn2xlsn8PiTB4WlM6zjeno9ok+++IU5uPLyDzgdxhs7dtbhvgd2yflk5p7Gr8PhkczqNVdPxRdvnTNmf/vD9tyxsjb86rdb0d3tEf3LwCBZTul4rV5ViLu+sUSZI81A3P/+fBOam51ITLSeZv3t7/OJ0/DjH6xUlvklyRP1b1VVF+LjLSIHR145HQOYNDEF//JPF5zeh+MNztf+2S8349DhFsmkCuvvQEAyvzk58fh/P1ktgWoV+402yD33bsfW7XWwWY2DzOa+oGTM6dz/5AcrhWlaBWjaP/zYfrz19kkYjHrEx1lEn/R0e2Qdv/2tJVi04OxTNj5pnO89qi/sr1Teo3rDvJLzbv0+Ls4/D0rDmMNV04Cap1+HwR4De0E2THF2mBLiYC/JR2BgAOX3PSmZElXZI87bDGcYqHD5ys6KF6Pk5VfLpDRIBXhpPfPsIYn45ubGS4Scl2pmRizssWas31iF2jqOw1BzaW080gKH24uMBCvsFqOU/CbZzUiwmXC0vheVLQ6owmvMHDVxnnGCGHF0SnnRc+2OHG2V0QSqUP/COvQePoHYojxY01MkWEPH1F6UB8exctQ//7Yy2Xbuqkfp4RZkZNglc891o6PPdaRB/Pobx5XJ1rmrFM1vb4ElPQUxORkwxsaIQxg7sQADbV0o/8NfPrT9Y6zgKK+RTCp1mz1/hI7r9+DkvU9KSbAK0ABnRpDEJ0N1HL+nU8jP+DsqwEqHZ547LAES6jga5qLjMuMkULLu3XLU1fcqkY176dnnj4iTmpcXL4Z5jM0kpY7JyTZs31GPI6cYlFWAY5p4Jnk2eUZ5VnlmeXZ5hnmWVeG9DVWoru4Sx4/BB8qWnGRDdk4cyiu6JFuoCtT9R462ITMzVu4EysY7guvY2OjAG2+dVCYbnxtZdfkcWVVA2ejo5+cnoKvLjedePKpMx1XXdAtLMm0P3qM8p7RJeG5pozzz7GGxWTRoiHZojqqGUWjfshe+XicsqUmjyQKy0tFX14yu/UeVyHb4cCs6u/uREmHsBi9WZlZZVqUC5RWdaGxyyqU1MmvKYeCMDO/b36REtu4+L2o7+hBnNY2SzWo2wB8Mooxl1ArAbAcv+8G5s8Nlk1I9nU5GTahC81ubYLBaoTcNz+jqTUYYbDY0vfm+MtnEuA2NJsrgOsbFW7Bnb6PsOxVo27QHAY8XpvjY0cRKGanoLj2OvtpGZTrO7+yHOSWCjstMg6umET2lx5UZv5KRSR7dg5eSYpNsOX9HBU6c7JBS5NTU0TqOGTdmCPcfUKPj6ARSB9MpHSkbHX2v14/9ivQvs8+cBUonJqKO4xiRPep03NZtNVLqO7I1hWRZzPyy5FYVduyq4xUwanY715GBEt4NvENUYN++Rsmg0gkcCu4/ZvRZhs6SahVglQ33HW2PkbLRRmlodKCislOJbBo0fBRoPaoaRsHf5xZlFmmupt5sQtDnl99RAVLT0zA3GEaXqEpJl26Q1ECJbB6/9ERxPMhIyHrqdFJOpQIDvgACwRAsZ+hD1UMHz6kxGOMNlqqyX8t4BjIW9s66XGoy+ITP6ZJ9Hwl83+dwSdRcRd8gS6ONxsh/l/vQ5w3KmVFRNu1z9gFnKCU3mE3wO1zK9Ij8Xd3guYzEJB6ijnP1K9MjlC1SiSrfkxEdivSI2+0XgrhIxElcSz5u0dEqZPP4JUMUSf+G5aPhrgJ0pEQ2c2TZ6CA6nWoy+NxP1CNnIsPievJzVXC5qOMir5uJQVZ/QO4QFf3Hrn4vztTZxXXzuP3K7nz+Xd0ZdBxlo63C86zho50Vpay/oU9np6aWUdUwCiz3pYaLVPrm63VJCZ/8jgJkZcbJDLVIyp8GEg05/o4S2bJixSHgxToSNFLITpyVOTy7NF5ItJthMxsiOqOifMmcHK+mB4nZDrIk9kVYt/BsusLC8R9OHkb8lBL4XZGj4nyfJGOqyG0KCxLh9QYjXmBcT5bKqSJpiS3OIyubELNF0iPMtJJlVwXshTmi49gzO0o2h/OUjstRIhv1FzNZkQJuzI7ToVCp41hOG8lx4UxV+R1FsrHElxm2SPqXhE88Izk5amQjGRwzvWfScXS0eJZVgLqrsCAJ7jM48SwT5eeqQN3P2dWRwGfNu0MVc3J+bsLpvTUSfNZxsWbJXqpA2NaIVN7LdaOtwvOsQUO0Q3NUNYxC+upFYmT21zUjFPjggqDjOtDWgdQlcxA3sVCJbJMnpWLChBS0d/QNU8D8nuNgivITMXO6GhbWzIw4zJ+bLaV5LDMLgxdZS4sLmemxWLwwT4lsNrMRcwuT4fEFJbsaBi/YTpcXsVYTZhWoIQRiCdfai0ok48se5JEjJlhWtXqlmv1G5F53MXQmEwY6uk4bJOJAd3QLEU/eZy5RJhsZQ+12E9o7+ocZSzRE/P4Q1l5YrIxpOvvyVbCkpaC/rmmYs8psps/pRM7Va4RgSQUy1iyGPT8L/bVNw3XcgFcIldJWzB90tBVg2tQ0FBcno72tf5SOa2/vF9ZO/o4KkGF1zuwsdHe5xbkapuOaXdKrunC+GgefAZmVywuEyG6oky9jdFpdSEqOwbIl+Upk4xlce2GJnMmhjjRl49mlo8WzrAoXrSkWPcy7a6iOI98DgyYXrilWJht1P4MjHR19w3Qc7wpmty6+aMKocurxwrKlBUhMtMn9PlQ2BpT6+wcJvFQ50YsW5iIjI1Zk4/kMg7YJn/P8edlis2j4aAgpfH1aoZX+ahgFY4wNs//nuzjw41+gr6qenoQYmiwFTpozFdP/5WvKMki8kL7+1YX4zT3bUFM7nJgoLzce3/jaIqUjYG6/ba44V+xNCZeDBkODcwa/+fXFEvFXhQumZ6Ld4ZFe1GC/T8r0eIHF2ky4ZkEeUhSOgKGjWlPTjfc31wgZCtmHaZgzE3HLTTMxbWq6MtkyLlqK4jtuRM1jL8FVUSPzNkP+AAwxVhTd/hlkXrpSmWwcf3Db5+fgyb+Uor6+VyoKyL7K0q4LVhfJyBBVIIHSzH//Fg7/9F64KmoH1y0QgN5oQMaaJZj49VuUyWaKtWP2//c9HPjJr+CqqhfdNqjjdMJEPO2f7lQmG5/hN+5chN/csxV1db1SBiwIcWZ4Ar5+5yKlI2C+/MV5QhRz4iR1nCSmSZgspEDf+vriUf1644nP3jBDeAIOlDYhEOgXPcISR87c/NodC5Ceri6DxPFkvLO2bK2R9WP7CmWLjbXgC5+brXSUybKl+aiq7sabb59Efb3jtGzMul13zVQsWqAm+EBQ93/u5ln467OHUVfvgJGyBUNSWUUHm3eHKmRnxeEr/7AAf35475B1C0k7xpJFubj+uunKZGPghufx9/ftlH5UvU53yiYBZkzPEFtFg4bzAdp4Gm08zRnhae9C05ub0H2wTIhjUpfNQ+bapWLkqQajqTt21uNoWZso36mT0yRaTjZF1WA0f/eeRhwsbYbPG5AM8IrlBWNeAnQue479ZSebHShr6JUy4KwkG2blJyFVUdnvUNBpPnykBTt3N6D31HiV5cvyUVykJtM7FNxjPQfL0Lxui8zcJOEOx5ckzZuufKYlUVPbjW3ba8VIT4i3SjR91syMMXVozlXHkTCJesRxohoGuw0ZFyxG+uqFMJjVBW3CcLd2iGwkTtJbzEhbPg+ZFy0VZmfV4OzI7TvqRMcR06emi0OheqZwOGO0e08DDh7izOOgOFmceZwcgeRuvPccmYlJWrd3/yCRGMtWly8rEKdCNej8HTrcKmtH4r+cbOq4AqWltUN1HAOsvFc5czM1zY5lS/KkiikadFxVdZcwAJM0KyHRKo7gzBmZY5pNPVcdR8b6LdtqhdWfFS4L5udi3pysM/b9jicYOCcZVkVFp/T0siJi0cIc2KzjH1A638fTPL+vAjGx6vRIv8uJG+dPOO/W7+NCc1Q1R1XD3wm0PadhPKHtNw3jDW3PaRhPaPvtk8X57qg+u6dcuaN608KJ5936fVxoJ0+DBg0aNGjQoEGDBg0aNEQVtB5VDWcEk+0cGl1d3S0lhCTwUNnjMxItnf2oODX7szg7Htmp6kuSw3A3t6Fzz2EEvT5hhU2YPjEqyqeIga4edOwsRaDfLeynySxfjZJocaCvD/0HDyDgdMGUno6YWbOktzEaQPKYQ0dahIiCA+dJ2qViJEIksJ/36LFWtLX3CXnH7JmZykg8IpV0Hz/RjqYmJywWA2bOzJSZm1Gj46q7Rc9Rx02fni4sotGChsZenCwfnHXIEsyc7OiJopPQ7ujRNilnLSpMQlFRUtTouJ5eDw4fbhHG8OzsOEydkhY1sg34g2joccvXOKsROfFWZWRAIxHwDKBjxwEhirOkJSN16VwYLNGhR8gqzf3GfRdLHTcrS2k/9Eg94vQF4PEHpRc0wWyASWEf+UjZKqu6UFPTI9wF06dnKGMi1qDhb0F0WFkaog4Ohwd/emgvDhxsOj1ri0RAF15QjFs/N1tp7wWp6v+6vhy7y9rQL2NqQrBZjJg3KQ23XjJJvleFoN+Pk/c9hbpn3oS3q1eY2ox2mzAlz/zPb8Oalqz0wqp58hVUPPicMNiSCYV9eYmzpmDWf30HsYW5UAnHpvfR/thj8LW3iWxk1DUXFCLzW3fBNnmyUtlKD7fgkcf3o6nFiWAgBL1Bh8yMWHz5tnmYOztLqWw0Qv744G4haqHDSoM8LTUGt9w0SwiVVKKl1Yk//HE3TpzsOM0SSyf/umum4eorJyt1HujMcN1KD7UMzhw8NUaETMm33DxL2E5Vgb2VDz+2Hzt21p2e/RkTY8bSJXn4h9vnKZmLO9Rh+Oszh/Dehirpo6WOs1mNmD0rE1/76iKlQQjquNfeOIGXXz0mASXCbDaIk0+iPdUspyfanNhT1wPXgF9kpYOaEmPG6gmpSFM0RiqMts17cPRnf0JfbZOwY+kMetgLc4U8MW3ZPKWynSzvwB//vAd19b0IhHVcml1IqMj/oBJufwBVvW64fAEhFyNMej2yY83IjDEr1XHd3W488OAe4X4I67j4eAsuWTsBN904Uynx5PkIEmPypfLvfxqh7VINETMgv79/lzTgM2OUmxuPnJzBSP6rbxzHs88fUSrfMxsq8P6BRmF0zE6NkUyqyajH1kPNeOKdE0qHIlc+9Dwq/vQMgv4A7MX5iJ1QIMQsLe9ux4Ef/1IcWVVoeGU9yn71MHyufsQU5MJeUgBzUiI6d5Vi//f+Dz6nS5lsfQf2o+W+e+Hv7IA5MxOW/HwYU1IwUFmBpl/8DL7WVqVERb//w040NjuQlmJHXm6CfG1uccn7dBRVEmX8+u5tKK/oQlKSDXl5CcjMjEVXjxsPPrwX+w40KXW2fnP3Nhw63CIOIPUIs1sejx9PPn0Q6zdUKZONWcDf37dDiGNsNqMwhlPHUfe9/OpxvPjSMajEw4/uw3vrK8WQ5LrxRSZRvkcHViVeePGYrBHXimvGteMaci3JMMq1VYX3NlTK3uIe417junHvcQ9yL3JPqkJtVz+2VHWi3+tHvMWI5Bgz7CYD2l0DePdEmzivqtBz5CQO/vOvhfjMlpMh95Y1O0MYsQ/+06/gOKHurLa1ufCbe7ZL5UNK8qCOy8i0o7OrHw9IoKlZmWz+YAjl3W44vAGYdHrYjAZYybweCqHO6UFHhFnI4yabP4h77t2BXbup40yndRzP54svH8Mrr5Upk02Dho8CzVHVMAos02MELjUtRi55RgQZ+aUhzHlm6zdUynw1FWjt7seuY62IjTEj3j4YreQrLsaMhFgLDpR3oKGtT4lsPocLtX99AwarFdaMVIlIUzZTQhxsuZno2nsEHTsPKpGNDnL1Ey/LCA6ODeGIEMpmjI2RqDkNETrTKiDz+l57DcG+Ppiys2VmKaG3WmHOy4O3uRm9GzdAFd7bWIWubjdysuIlO0Pwa05WHHp7PXhvY6Uy2TjqornZIQa51TpYSUDnhtlej8eHN986qSxws2t3AyqruoW9mSV63G8sr2UmhHjjrRPC0KoCR4+14chRkqTYZTxIWMclJ9tkHd9dXyFVJarKfXfsqkdCokVYzMM6jt8nJFgky8rfUQGuCdeGa8S14ppRNq5hWrodR462ytqqAPfSG2+ekO+5x7jXKBv3Hvcg9yLZdlWAZ/BQswO+QEicVAZZCZaHJtpM6HH7UN6uLlBY++xbUu5rL8o7XerLr/biPHhau1D3/DvKZNu0pUbm4NLJCrdasNohKzNWqg3efLtcmY7r8vjQ7w+Icxp+ptxzFoNesqstfV5lWTAGZ46VtcnYKFbDfaDjYuT+eufdcpk5rOHcEQqqf30aoTmqGkbh+IkOKa+lUzoSLNvrcQygokJNFqmioVfKfeMj9KbE2ozwDARO962qiEoPtHfBkjp61ADLf0N+P7r3q8nUsJyrr6YR5pTRsunNJhmc3rn3sBLZgv19cJcdgyEhYVSZFHtn9WazZFxV4UBps5yFUbKJEWyWz1UZShyBZDQaRvW4UTaWeJVXdMLhGFAiW9nxNmHNjNQmQD3S0upCY5NDiWwsRaZjYzuTjuvxoKJSjY47frxDDPD4CGNo+Ez52YkTHUpkK6/skuAM12gkeEa4plxbFeCsyJa2voiycQ9yL/JuUwH2LrY7B2AzDjrPQ8GeRoNOJ32rKkDd1bF1H4zxsRF1HIOZLAtWBeo4k1kfUcfFxVsksB4ujx9vOLz+089wJFj+6/YHMaCowmBQxwVhjTCGhmeEZcEqq4E0aDhXaI6qhoig2j1zb0VI/qcOZ/vb0VvDr3QSVPhvn61dJpr7H4LRKRuPiPJlO2sLlGrhzi6ZqrXj3/2wvx2tK6dy3fAhf1v5WfgQRPM0PqU36nl6GKKDgiqKl053Xj5WDRqGQXNUNYzCpIkpEoGOFKXs7R1AQpwVE0tSlMhWkp2AGIsJzgiy9Xn8sJqNwgCsAmT2ZTZ1oLN71Gf+fjf0JiOS5kxVIhvZfWPysuDt6Bn1WdDnh44lQfNnKJFNH2OHdfIUBHodowwmliqHvF7Y586FKpBBl71to2QLhaR0as6sTGWEGbNmZsLvC0q/4Eg4HF4UFyVLFk4FJk9Ok9mDkcp7SWSUkW5H7qne93GXbRJ1nD5izyIzhiyznVCcrEz/2mJMcDpHl+WRvMgeY5LfUQGuCcuPuUYjwbXkmqqSjXspI80eUTbuQe7FyZNTlchmNeqRFmuG2zc6u8bSUPY05ibalMgmxETL58Hv7Iuo4/x9/UhboY5MiTrO5w2M0nGUjdUi3G+q2H/jzYOlyJHKe/3BoJQE86UCXBejQS/92pH0b2KCDcVFoyusNJwZQYSUvz6N0BxVDaMwbWo6ZszIQHt7H1wur1wIfLEcjob5BRcUSb+qCmSmxGDB1DQ4+rxw9n8gm8vtQ49zALMnpiA/Q80IHXNCHPJvugKBfg88bZ2DTlYoBJ+zD+6GFiTNm4bUZWocLr3RiKIvXCPfu5vaEAoMOg/+Pjf6qusRN6kIWZesUGYoJV11NfQxNviam6VEmggODMBb3wBTRibi11wIVVi7plgcF5IphZ0ufm1qdiI+3oq1a0qUybZqZaH0ozY2OjBwioyFJBosq+UomCsvV8esu2RRHgoLEtHc7DrtENLY7Ojol16bKy6bpIw9fPq0DNFzbW19otN4Tikbe+/73T5cdGGJPHMVyM9PxOKFuaJvHWTVPaXjaJT39Axg0cJc+R0V4JqsvbBE1ohrFZaNa8i15JpybVWAe4l7KhiE7LGwY8O9xz1YWMh1zVMiG8/gjKx4mAw69Hp8p2XzB4LSn5pgNWFi2v/P3luAx3ldW8NrmMTMkmWZmdmxHTsMbZK2aZI2pZRShtvb737/pe/eUtqkDTRJw9AwM5mZ2TLKYmaNhuF/1h6PIxglThPrjOt33WdqaWZutXvec/bZuLa60W9FX7wUlrRkuCprZaQaEfT55G6wpKeg6AuXKpPtgsUlyM6K6Dif70Mdx75Vlu5fdok6HZdmNcFu1Etpd/DUM+V5kHJfHZDjMMcsCx4OTJ6cg7FjMmSd+um4Dje8niBWLB+JRMVM0xo0nAl04XiuhRkmdHd3Izk5GV1dXUhK+mwi/OyHaW4mWUeWRHLPNTDidt/ftklDfjQix7mMvDS+euO006QyKuD2BvD0B8ew43Az3N6I42A1G8RJ/crFY+CI0ZMxXAj5/Tj8l8dR88I78Hc5I+NprBakzZqEyf/vx7Bln72I/sftOR71ikdexImHXoSvo1PqfpjlTZ44GlP+5ydIKFVjxEXRtXIlWp54DIHWyNxI6HXC/ptz6w9hG6cmEx0F+1AffnyXOIBcRxpGzAh+7aZpmDk9X/noBo4gqKnpOm0Ak+jm+i9MEofrbOFMdFx9fTfuuW+r9Hv6A8wmhZGcZJXRNJ+/erzy0Q1/vX+rkCpFnXySjiy9oBQ33aB2BBerWcjavG1HLdynqkeYZaUD+62vz1Q6P5JBmief2ovVayskkEmQ5GbihCx8/ztzzmoQ80x03MuvHpIRNV1ChqUTRviykWm49btzkKd4Dm15E8fTdKD31Kgm/i8g++/isnRkJ6qdLdy0egsO/u4BuGobIzXcOsBRlIcJ/+e7yFo0U6ls7EO9/4HtQiLGQASRnm7HDV86uyO4zkTHkUypotON3kDf8TQ65DosyD1F+KgKbW0u/PX+bThY3gxfHx3HYNMN108Z9vE0Z8PWHk65/77lCOwJ6kZcuZw9uHHumHNu/T4tNEdVc1Q/dlA0XwY9B0VnITdH7Ry6vqhrceJ4XaRclCXBBVmOuBnq7qprkrEvQZ8fyWNLkTJl7FmX7Uz3nKe1Ha0bdyHg9iKhJF+caL1BnVHeF0GnE707dyLo7IEpKwv2KVOFTCke4PEGZOYmM0kko5g6KScmUYWq2Zb79jehtaVXnJipU3PPerT8TPcbxyEcKm8Rp5UODedtqqrIGAjqDhJOnazskDI56jjVszb7oqq68zQ50djRGcoyqUPNyD14sFmygiNKUjGqLD1udFx7u0vOAwMQZIsdNzZTWIDjAR5/EDWdbvgCISRw/FuK7TRjrGqwRaVl4y5hAObM74wF02G0qXWg+wZIGDhvaXGJszV1Sq78Gw/7TSoefEF4giHGV5FsNsIcJ/uNsh091oZK6jiTHpMmZCMrS032/lx3VJ/cdFi5o3rT/LHn3Pp9WmiOquaoavgngbbnNAwntP2mYbih7TkNwwltv3220BzVTwfXeeqoRjrBNWjQoEGDBg0aNGjQoEHDP8QWf7b//vkIzVHVMCT8HR3oXLsBrsNHoDMakDhtKpLmz4XBpr5sj+QAW7fXykBrHt6xYzIxd05BXJADsNxsx6567N3XIOVKI0vTsGBecVyUO4YCASntalqzDQFnL5JGlSD30sVwFObGRYnSwUPN2La9Fl3dXukBXTi/OK7KHeMVNbVd2LS1Bg2NPTJ/c9b0fEwYzyyA+pLC5mYnNm6uljJWq9WIGdPyMXVqDkxG9eXmYX8v0HkE4d5GQG+ALrEYSBoJnUF9SbfbH8Thhm7UtvXK74XpDozJTYJNYe9sFOQt2LGzTkox2XvMHtD584qQqoi5diBPQMuGnWhaux1Bl1uI4vIuXQx7frZ62UJhHDjYhO076oQoKzc3EfPnFqGoMDk+yuCPtWHzthq0tbuQme7A3DmF8mzjoaWG+mPjpio0NfciOckipGLUcfEgWzyjx+NHeX03GrrcMOp1GJGZgFHZiTDFSWmyBg0fB630Vyv9jYnew0dQ9Zs/wFtXf/o9nV4P+7ixKPn//hXmzExlsjU09OBPf9kofWVRYhuCl/1PfzQfRYUpSkmo7vjLRiFooVGi00eiYHS6fnTrPGHFPFv4uD3HMQN7/s/taFq15dRIGr0wE3OkzuT//CFyls+HKpDF8aFHd2L1mgp4vRwlETHqEhMtuPH6Kbj4olHKZIt3fLD6BJ58eq8Yvux1I/ukxWzEBYuK8a2vzYDxLDmEZ6LjaJDf98A2tLe7Tz1TwGjUCQHVj34wT1g7VSHcW49w5ZuAtysyb5A3IXVJQgF0JZdDZ1LHwtra48XLu2rQ3O35cASyDshKsuKaGYVIVxiQIwnVn/68EeWHW06PM+F/kn36Jz+cjzGj1RHG+Z292POvf0Lzmm0SlIvqOGtWOib/1w+RvXQuVPaRP/jQDqxZVwmfL6LjSA7LwNJXb5yK5ReqYw/nc3z62X14460jwpLMteVa2+1mXH3lWHzh2olKHcJ33zuGvz+9Fz1OrwTfqEfIar50SSm+KTru7NhY57odV93Wi9d216HTTdKzyNBvshAXZTjw+ekFcFiGN1d1rpf+PrFRfenvVxacf6W/597J03DWEfR4UH3bHeKkWgsLYCsplpc5Jxu9Bw6i7p77lV6o9z24HSdOtothRKe0sCBZSJ6qqruE4Y5Ojyo8/uRuIdzJzLSjqChZZON8P476ueferZIJVoXjDz6Phnc3wJKRhsRRJUgYWSQvf1cP9v3nXeitaVAm28pVJ/D+B8eF2ZTrVlCQjMLCZMnWPPHUntOEMhr643hFu6wPM/dFsteSUVyYAofDhJWrK/DeyhPKZGtp7RUnlWNVCgqS5Jny2bKyYMu2Wjz/0kFlsoWDPoSr3ok4qdY0wJoB2DIAcxLQU41w7RplsjFA8+a+OjR1eZDqsCAzySov/tzY5cEbe+tizm0cLjzy2C4cONSErCy7nFG+8vMSZQzG3X/dEnM27XDh+P3PovGDTbBk9tdxvvZO0XHuhmZlsr33/nF8sKoCjoQPdVxRYRL8/gAee3I3jp84xXauAFu21uDV1w8L2Q6fJ88r/9UbdHjplUPYsfPDgLUKxl/qOJJ2RWSL6BHeFbwzVq5Wp+PiGazIeGNvPbrcfglsZSZGdEmS3YSTLU6sPNSoWsRzDpx3rPp1PkJzVDUMQvfW7fBW18CSlwtdHzZYsq+a0tPQs2s33Ccrlch2+GirjONgWVLf8RGMqGZlOiTLyvJRFaChtmNHnTDCkt00CkaAc3ISpSyT4yZUgJmGuldXwphgl1e/LHlRHrwt7Wh4e50yw/yDVSeg0+sku9BvEH2GHb0uP9asO6lEtnjHuvWVcPb6kJnRn/GaJfAGo16cVVWBm42bqiWTmpOT0K8EmVlUOtKUnRkSJeg6AXg7AEsqD8GH7+tNgMkBdJ9E2NOuRLSadhcaOj1Itpv6scHyZ77Hz2raXEpkI3Pzrj31UuJrNn+o48ioy+fM2cLbd9Ypkc3X1YO611bBmOgYrOOK8+FpakO9Ih3HM/jByhNSTdC3PUV0XKZDxvzwPKgCHWjKyOca1SP8Ny3VJtnfVWvUOYNr11eit9ePjAx7Px3Hu4J3Btc1OpZLw4c42tiNjl4vUgfMcmXJLzOpx5p60OlSFzjXoOFMoTmqGgbBW1snmctYY0EMiYkI9rrkO6rKfnlx2myDS1bY/8bLtq6hW4lsNNJcbn9MyvxoaVJdfY8CyQB3fTN8Hd0wJQ8uW6Ehx4BEz7EqJbIxy9zU7Iy5bjRMLGYDKirUOA3xjpNVHTCbDDHL8hIcZrS29cKpyBlk3yzFitUny2dNuZqbI/2Xww46qTJIOEZZtMEKhLynvjP8aOv1IhgKwRyjZNtiNEhpN7+jAtRfQ+m4SOAwLHpQBTj/09fVDfMQOo6b0VlRo0S2nh4vWttccAyh48xmPSpOqtlvvOsrqzpgd8Quw+e4K1WyEdT9LPMdSsexZ1VlpVK8ot3pk5L8WKOPrCYDPP4Q2k7NQdagIZ6hOaoaBsFgtUovA3t7BiLs9wuxkl4RoZLVYhTjNxgcHEGNRlX5HRXgZcrMgt8fY93CamUz2Kzy3EK+2GV5fNaGPlmI4YTZbBBHPhBj3QgGH872vLxzFTTUWB491LpxP/bNfA0naOAORYHAM0LZGFxSAr0x0lgZS75wMJJlZXZVAaIkJ7GyRJH3wsqIUPi8OG+WpeYDwWdN8VTpOKOdOs44pI6jcEaHOh1nMOg+QseF5SyrAB1AVjkEYjxTgvqFZ1kVqPuHqgoJBIKSpeb6augPM4PjwlI7WI8w2EUHVr6j4ROz/qp8nY/QdqmGQUicNQOGBAcCnZ2DPvO3tMCcm4uESROUyDZpYrb0uHGg+0C0d7iRlGTF1Mk5SmQbNSpderXImDjwcmCfHrPA06epYde1F+Qgddp4eJvbB8kWcLqgNxmRvWS2EtlYJk0GR2YdBhrnzJ6zH4/skxoGY/bMAvnX6wv0e5/ryD03a0a+MiNz+tQ8ybINzHZw/5GQZ1RZOvJyFRFTJJUCBjMQGKBHeDZ8PYAlDXDkKRGtNDMBDotJ2DoHgu/xM35HBUiURD4AZgcHoqvLK3tt+lQ1Os5RUoCUiaPhaW4bpOP83U4YLCZkXTBLjWwOM2bOyJczOVDHkSWemD07cpZVYOGCYmFyHugQ8nefNyjs66owZ3ah3AG8C/qC69jT45PP+7baaIigNCtBHFHXgHXj2aAeSU8wIy8OWLo1aPg4aI6qhkGwFhUi48rLIyW+9Q0IulwIOp3wVNdAZzIh58YvQ29RwzpJFtjPXz1eIvd19d1wufzyYkkwo/xXXTEGaWlqouYct/HF6yZJRqG2rlsMdBKLsKyVF+rSC0oxoiRVWdR81He+BEtWKpzHquDr7Eag1y0lwSQYyV42F5kLpkMVrrhsjDgtNTVd6OryiNHEYASf64RxWTLeR8NgzJ1dgEkTstHY5JQACdetu9sjZbckG7v8ktHKZJs8KVuMyLZ2t5CJuT1+6UmtqSGDohVfuHaCMiZRHYmT0ieROQ7wdgJBHxDgz22SbdXlzIWOWVcFYP/YvLIMMc7bnV54/UF5tTm98h4/G262ziiYuSIDLFmla2v76Lgmp/y8YtlIZeOkRMd9/wZY0lNO6bie0zrO09SKnIsWIH3OFKjCVZePlT5e6jieUZ7VtjaXrB0DsPPmqgvG8bmVlKSirq4bnZ1uka2j0y2/l5amYtnSUmWy0UnmHcC7gD3vlI13BNcxLy8Jl1+qTsfFM3KSrJhcmAq3Lyi9qN5AEJ5TesRs0GPR6KyYZcEahkY4FFb+Oh+hjafRxtPERDgYROtrb6L1tTfga26RHh9rSTGyvnANUhYvVCtbOCwEC2+9c1ScVe5gGgCXXTwaFy4tVT5XjfMFX37tECorOyXqm55mw/ILy8QZO1s0+me659p3HsCx+59Fx+5DCAeCMKcmIf/qC1F2yxdhtNuUz8l7/sUD2LevET6Wm9mM4qBed80EcWw0xAYz0S+8chAbt1RL0MZk1GPSxBxc97nxKClOVbrfaFS++no5Vq2pkGwSyx9Hl6Xj2msmYMJ4tXMtwyzxbdmDcOtewM++Sp0w/+qyZkOXUqZYtjD21XZie0Ub2k9lpNMcZswqTcfkghTlOo4zrF95rRzV1REdl5Fux0UrynDpxaOV67i27fuE/bdj7+GIjktLRsHnV6DsW1+Awap2zjbJ/l6gjjvQJCW1DrsJC+YVifPPIKxK0GF+7sUDcn95fUHhBpgzq0BkI+GTStAx5d2wiTrOHYDZpMfkSTn44rUTz2pg5Fy341jiu+1kG3ZXdUgWlaRK2UlWCXZxlupw41wfT/Pw2kPKx9N844Lx59z6fVpojqrmqH4kQl4fvPX1QrZjKciPkFLECViWRCZd9mHk5CZIRjNewGMlWd5ASDJbw1GadKZ7jrKReCTocsOWnw1TglojZCBYVkiinfQ0u3Lj7VwC16y13Y0EuwkZGWf/mX4SHcesW3NLr/RxZ2clKHe0+iIc8p+apaoHLHQC9XFlaLafIk5Kc1jiKgNCB5X6NxgIITtedZzbA3t+trLe1I8a3cQMdHq6vR8LcDyAmVQ6hgwOkgU43oJyrBxJSLBIcORs45/FjvMHQ+jo9Yn+YMBLlf7VHNVPB9d56qhqhf0aPhJ6ixm2ESWIRzByzzml8QheBCxLilfZHIVq+sjOBDRAhsMI+WcDjTe+4hEkaylWVBL6cdCRNImlwHEIGpaZifFZTUA253xNx/1D4DgpvuIRdE7jzUGNgoFLLXj5yUHytayk+NQj5xLYeqFyhnXoPM0rnrshIg3DmrkMBtXMYvw4MHIer0UB8S6bPxCb5TEuZPMH43Lt4l02snLHo2xEKI5lCwcCMVnO4wFxrX9DIYQC/Ym84gXca2SFjV/9G4rL8xDvOi7o88WlbATv1HiVjXpEmzer4VyEllHVMCR272nAex8cx+EjLRI9J2vhxSvKUDoiTbVo8ARC6PIF4D7FUmgz6pFkNsAWB+W/lK3N40e3L4gwwrAbDUizGpE4xLzL4URzsxPvvn8cGzdXwesJorAwGRcuG4lFC4pjzrscTrDcjPtt7bqTMuA9K8uBZUtKhcgjMqNRHVi6+sGqE1i9pgIdHR4kp1iwZHEpLlpepnR0Q9TJ8mxdA8/m1Qi2NkFns8M6axFsC1dAn6Q2i0mjrf7tdah+/m30HK2EwWJG7qWLUfLlK4SJWjW6t25H65tvw3WoHDAYkDxvLjKuvAy2kerIY6IoP9yCd98/hn37G+X3KZNzcPGKURg7JlO1aOg+UoHKZ95E86qtCAWDSJ06DsVfvBRZi9Ww6vYFyeve/eC49DOSKZY9jMuXlmLB3CLl+rer14e1e+qx+VAj3N4gMlOsWDgpFwsm5sjYH5Vgfzv175p1Fejq9CI11YqlS0qxfNlIqYhQCTqnNS+9j9qX35eSblNSAgquWoaiL10GS5paHUfnb/3GKqxcdUIInixWcisU4ZKLRinv7aX+3bGzXp7r8RNtMhJs9qx8XLJilDLSMw0aPim0HlWtRzUmPlh5Ao88vksMdM4xozJmTw0ZdX/24/lKiVB6/UG0uP0IhsOI+lYMFJIoINNmQoJCp8blD6Kqxwsf55LqdKRnAUe+Us4cuxkZZ/HC/7g9V1/fjd/dth7VtZ2w20wwmvRw9frFQb3i8rH4yg1TlBlynV0e/P62dRIU4UVPIg8aTtROixYW49bvzj2rJC0fBbLV3v7nTdi5q07WzGY1wePxyyzQadPy8IufLFBmyIWDAfQ88yC8W9cgrNNBb7Uj7Pch7PXAVDIKSd/+JQzJqUr2G6+WI3c+joqHXhBnhsZlyBdAoKcXCWVFmHX3vyOhVB3TKYni6v/2MIIeD4wJCZIdDPY4Yc7ORMn/92s4JoxXJtvWbTW4576twpKc4IiUOjp7vUhMsOLW780RkhtVaN91ELt+9lu4G1pgTEoQ/oJAVw8MdivG/fJbKLn+8rP2tz9uz5Ft/Q93rBdGYgaQqDN6XX4pob76yrG4/rpJ6nSc04u7Xz6AioZuWEwGIT2js0qShfkTcvDVi0aLI6ECvOf/eMcGCU6bTJxvbBK9x7mvM6bn42c/mS96T1Ulxt7/+2fUvbFaODKMCQ4EPV4E3W6kTZ+ImXf9X1jS1em4J/6+B6+/dUQYWe0OzqMNCeFTcWEyfvXLxepGcAF4460j+PvTe2V8Gef0Bk/ZcVmZCfjFTxdg9KjhbXk413tUH1h9UHmP6i1LJ5xz6/dpce55UBrOOjgW5Kln90q5GTNunFtK0gf+zPmHjz2xe8gB3MNRo89sJZ1Ukx4w6nXy4s99P1NGoOTywR8Kwcph2iKXDlaDTuYzNpEiXmEJ3/MvHRAntSA/SSK97EPKz08SJ+udd47iyNFWZbK9+fYRcVJzcxOFbCclxSY9vikpVmzYWCUso6qwdm0ldu2ulzXjDEnKlJOTKBnf3bvrsXJ1hTLZfAd3w7ttLXSJyTBm5UkG1ZCeBUNWLvyVR+Fe/aYy2Tr3H8HJx1+FwWFDQmkRLBlpsOVlwVFWBOeJahy5+0llsvmam9Hw2JNyLm3FRTClp8GcmQFrSRF8La2ou/8hZaXAdBoee3IP3O6A9OCnpdnkxZ9dbp/oXzoRKsA1OfSHB+FubEVCWTFsOZmwZqbJz1S7R+96QsZdqcKzL+4XZ1V0XEZExxXkJcFqNeLNd47iREW7Mtne216DE/XdyE61ISPZimSHGTlpNiTaTdh8qAn7FMq2cvUJcVKp03JO6TjqOuo86r516yqVyda4cjPq31wj+oOzci0ZqVKNYS/OFxb7k0++pky2w0da8fa7xyQowruU+41rVpCfeIrFfr8y2RqbeoQpmdxw1B2048j9wJ85LowOtpan0nAuQHNUNQzCjl316Oz0DCpbYSQ6M9OOquouHD2mxqlhqa8/FAaTa30j4/yZzmogFD5dDjzssgVD8rfpnA6M2vM9OtAsB1YVfNi5qwHJSdZBUfvkZIsYvsziqAD7odavrxSHeWCJr4NR4GAYGzdVQRXWrj8JBtNp7PYFWU6ZsWGpsip4d22WUVJ6e0K/93VGE3Q2B7w7NyLsi7DGDjeaVm5BwNkL84Bsh95ggDkjFa0bdylzaro2bUWgqwumrP5ltMzYmLMy4T5+Aq7DR5XItndfo5ToU9cO1HFZmQ4ZI7J3b6QceLjRuf8oug9XwJabNYgBnk6rr60Tjau2KGPSZZl0SvJgHcf33C4/tu2sUyKbzx/EtsPNsFNnDJDNYTVJUHj7EXUO/tp1laLLBjI3U+fxMa9RqOPq31qLcDAkFRl9YTCbJbta9/pqKQ1WgW3ba6S6JjmpP8ET9x9Zk3nnMrivAtt31KG7J8Ke3xesoErPsOH4iXacPNmhRLZzFXTsQwpf4fM0sKA5qhpi9goSsXoWeZGx74czEVUgmi1lme9ARI06lreoAP+ulCDH+CwqGx1pFeCFRWNpoLMVlY3Pur0j8txVZJBcbn9M2Qiz2YC2NhdUgeMQhhq9wTLl9g63sgsk2NEKnSl2SZ7ObEXY40HIrWbtPC3tVCIxSy2NNiuCHH3V1qlEtkAn/64u5rgtvdWKkM+HQJca2bq6PWKUxOrL5nvse+d3VMDX3ikjy1jmOxA6OmA6HXztXUpk6+r2wucLwRrjrHIP8qXKaej1BuDxB2FhNDUG6CS2K3qm1F2i44bQv9R9be1q1o3wNLVCP8T8W4PNgoDThWCvGvnaOtxSEhxLx1mtBrlzVZ1V2nG6Iew4nhEGiHlmNGiId2iOqoZBYJkZEYtpkuVonIWYlqqGvp5lvkPRdEedBVWzBo16vfSisid1KNmYWVUBliTxcqJTGJNJMcSRCWpGwtjtZpklGEs2goGR7Jz+0fThBOfgejyxmU097oBkuVT1vRkysoXpNxbCXjd0dgf0djWEHrbcTDZ5xXTiA71uMTKtWelKZDNl8O+GJRs9ECGXSwxjU7oa2dJS7WJcct8PBN/jZ/yOCliy0k87BwPBPmQ+aWu2qnWzyd0Uqyw6mgkZjvnCseCwGmG3mOAZoqKGrTSZikbCUHex3YK6LBao+6gDVcFemIeQJ7ZDRT1iTkmEMVHNc83KcAh/RywdR1vJZjEqG/VDPhFKFYvpl7IxABy19TRoiGdojqqGQZg1I196GZqbe/spYCo8ZrZGjkzDqFFqjBGbQQ+zQQdW9/aVTSj1QxFH0K6IdIe9qA6jAf4BJRr82SflynokW9QQPbEMac7sAsmEM5LaF+3tbiQ4TJg/t0iJbMwmLLlgBDzeILze/sYS5WUWadF8dbN8L1g0Qv4lCUVfRMiewsKMqQrWGQskoxrq6Z/FYrlv2OOGZfZi6ExmJbLlXrQApuREeBpbB5GjsEQ0e9lcZY5q8vx5MKWlw9fY1P+sBoPSo+oYNxa2UWVKZCO7b15u0iD9y5/5HmeX8jsqkDy+DCmTx8LT2NLPyads7tpG6Vflc1XlqM6cni9ZooEcCswIJiaYMVcRCZXZaMD8CVmSVWWWrS96XD6YjAbMGaeOoJD6l8+QOq0vojqPn6tC/uUXQG8ywtfRX8cF3R55FXx+BfRGNQMs5s4pFAIl3qF9wTuWd9fs2QVy96oACddSU6zSRtBXjzABwVagcWOzUFQYn3Po4xUqy35Dime4qoTmqGoYhKQkK75+83TpGSTdOhvv2RdVU9slkdVv3jxDWQaJfzfDapLMqi8E6VeNvCKZVLLq6hXKlusww2rQwxsKC3GSLxiCJ0R2Yh1y7WaYFDJAf/HaiRgzOgMNDU40NPZITxefL3tAr/38BJSWqhs7dNklozF9ai5aWlyor+9Ba6sLtbVdYihdtKIMM6bnKZNt8aISYR5m3zbZRClbXV23lBHOm1uIpQqNONO4KbAtukSc0kBDrZQCB5vrEWxvgXnsZNiXXKZMtqQxpRh9641k4IHzeCXc9c3orapHb2UdUiaPxpgf3KRMNlNaKvK/dwt0Vis8VVXwNTXD29AIT3UNLIUFyP/uLcp0HDMdt3xjphDaVNd0i3NKY5M/kxDlW1+fKd9RAa7JhF9/GwkjCoQQy1XTIM/VebxKyrnH/+u3Yc1Qp0euv24iykrTUN/Yg4amUzqutksMvC9cMxHFCkdyrJhRiPHFqWjt9qKx3SWlvvVtLmH+XTotDxNKzg5z7Zlg6QWlosuo00hGFdG/3aLzqPs4vkwVsi6YjaLrL4O/2wnniSphm+49WSv7LmvRTJTccIUy2cpGpuPaz42XO1RspdZeNDT0yB3Lu/ZL101UJhsJMG++aRrMZiNqarrFjmtscsrdVVCQjJu/Mk35uCYNGs4E2ngabTzNkODcLTKaHjzUDKNBh1kzC7B0yQjkZKuj546CDmCPLwAXh6azPMioR6LZCIviWXRR2Tq8ASFOooHkMBmQajHKv2cTZ7Ln2Leydn2lkBNxbENpaSqWLi7F1Ck5yi8tlv6uW1+JDZuq0NHpEbbOCxaXYM7sQuUzXjnIfdOmaqzbUImm5l4pk160sEQMONUzXiVjv287PNvWibOqT0yGdeZ8WGYugt5mV7rfKFvr5t2ofWUlOg8chTHBjrxLFqHg6gvP2kiJT4Le8iNoe+dd9O47AJ3ZjJQF85B60XJYctRlt6KorunCqtUnsGdvg/w+dUouli0dGRdZEFddE2pf+QCNH2xCyOdH2qxJKLxmBVInjz2rf/dM9lznKR23eWu1ZAjLRqZhyeIRmDQhW72O8waw5VATtpY3o9vlQ166A/MnZGPaqAzlsgmp3cYqrN9QiZZWF7KzHFi8sATz5xdJxlclyDbd8P5G1L22Cs6KGlgy05B32QUouHIpjA71Om7P3kasXleBiooOOOwmLJhfjCWLSyTorxpk86ceOXS4BRazUSqrmCEnK/Zw41wfT3Pfyv2wKRxP43b24LsXTjrn1u/TQnNUNUdVwz8JtD2nYTih7TcNww1tz2kYTmj77bPFue6o3vvBftgcCh3V3h58b/n556hqJ0+DBg0aNGjQoEGDBg0aNMQV1HSgazhn4O/qgfP4SRnjkDhuFAxD0MSrAEe9sPyXJQGJZoPS/s9YZVQcLk9SDw7YVkWoEAuU6WRlBzzegAx1J3FWvIAFHpStt9cvcyTjocz8XAEHvLPH1+EwYURJqvJSwr5gv7bLH5Q+8gSTQVkf+bkGMthW1kZIZEoKkmGzxh5FpAJkIK6o7BByFuq4pERLXOq4/NxEYUDVcGb6t6auW8aZcfYs1y6e9Ai5Fdg/G486Lp7hbe9Ez/FqIaVKnlAmM2g1/GPnQ2URavg8LYDVHFUNMcEZh5UPPo3G19+X2Xmcj2fLy0bhjdcg79pLlV4QPKzVPR5UdXvEACbYm1qQYEFJsk2pEUzZVq89iVdfKxfiAjIlJyaapUfqi9dNGnJW6HBh+846PP/iASEYoTHH0TDz5hTghi9NQdKAoeXDjfLDLXjqmb04caJdekItFhOmTc3BV26YiqwsdeMR4h0k8HjiqT3YvbtBhs8bjQbpPb7hS5MxYbzaXkt/KISTXR60uP0IhEKcXAqHSY/iJCsybJqxNBQCwRDeWnUc76+rRMepudapyVZctHgELl02EkaFvfjUcavWncRrbx9Fc0svwqLjLFiyqATXXT1O+uBUYuu2Wjz/8gEhjaETHdFxhbjh+skyBktDbFTVdOLvz+/H4aNt8Pk5vsSI8aMzcMMXJqGoQG1fNMnEHv/7HukF9Xr90jPL6QM3XD8F48ZmKpUtnhFwe3D0nr+j9tUP4O/skYSDvTAXI795HQo+t1xz9M8T3HPPPbjtttvQ2NiIKVOm4K677sLs2bNjfveBBx7A448/jgMHDsjvM2bMwG9+85shvz8ciJ8UlIa4AQ2RY7fdi6qHnpE5ZdbcLFiyMuBpaMbR2+5F3XOvK5WvosuNIx0u+EIhcVAtRr38fLzTjeOdg+f7DSdWranA3x7aLpHf1FSrZAU5juDl18px3wPblEbEdu+px11/3YKKk+2S/eDsPB3CePf947jjrk0x5zYOFyoq2vGnP2/EgYPNQvdPx9Rk0mH9hircdvsGdCsamh7v6HF68cfbNwgJlcGok3VLSDCjvLxF1vPosf6jYYYTJBIrb3eh3umlUhE2bLNeB6c/iMPtLrQOMTdXA/Dy20fwzGvl6HH6kJ5qkxd/fua1Q3j5nSNKZftgTQUeeny3OKkcf5GRYYfXF8DLr5fjwcd2K9VxDMTdfd8WVFV2SuAtM9Mh8rzz3jH85e7Ng0ZzaYiAQdU/3rUZu/Y2wGo1yHxQq9mAHXsa8Kd7NsuzVgXq/j/cvgEbNlbBZNKLjuMdceBQs+g43h0aBoP7fv9/3IkTDz6PkNcviQaSUPVW12P/f98jZGga/vnx7LPP4mc/+xn+4z/+A7t27RJH9eKLL5be61hYs2YNvvzlL2P16tXYvHkzCgsLcdFFF6Gurg6qoDmqGgbBebQCTe+shSktBdacTOjNZin5tRXmQWcwoPrxFxFwqrm4vIGQZFMNzPAaDVJKGP2ZI2tqe7xwB9QYI5wB+sqr5ZJFzc1NhMViFEbY9DQ70lKt2LKtFkePtSm7tOgsc9wL5zBy9BDnl6ak2JCdnYADB5uwc3c9VOGNt46gtbUXhYVJcDjMIhsZE/Pzk6SEmmyUGgaDTMTHjrchLzcRyUlWWTe73YSCgiSZ7ffm20eVydbuCaDD4484qAa9VDrwvHIWcuBUVcT5Wsr0UWhtd+H99SdhsxiRmW6H2WSQF3+2Wox4f91JtHX0n9s4XPB4Anj1zSMI6yCjyvrqOJaKbt5egxOVHUpko9596dVDwh6el5coZdLMvKVSx2U5sHdfI/bsa1QiW7zjg7UVMs6HTOsJp/QvA175eQmoq+/BqvUnlcm2bkOVOKO8Cxh8oGy8IwoLkuTOePMddTountGxpxyN72+SWdXW7AzozSYYbFY4ivMRDoZw/IHnEPR4VYt5TuFcnKN6++2345ZbbsHXv/51jB8/Hvfddx/sdjsefvjhmN//+9//ju9///uYOnUqxo4diwcffFBIxVauXAlV0BxVDYPQsW0PAr0umFIGs4pZstLhaWpF155DSmRr9/rhD4ZhMQwuWTEbdNK32qYoU8NxPk3NThk8PxC8WL2egBhLKsAMb8XJDjHaBpb70PjlHLjoGAwVfXi79zSIETJQNholeoMO23eoi+bFM5hB0ul1g0bkcB3ZF81n6nT6lMhGJ5X3Kp3TgbJZ9Hr0+oMyXkpDfxw61irZ05QYoy34Hj87eLRFiWxHjrehpc0VU8fRsaEju/9g7Ej92QZLfaurO2PrOKtJyoD37lOj4+IZDBZt2VEnjv3AMWAGvR5WiwFbFerfHTvr5A7gXdAXfMa8M3btqpc7REN/cCwYS3+NSYPbZqzZ6XDVNqJzv9rqDA3/OAtxd5+X1xs74ODz+bBz504sX7789Htkr+bvzJaeCVwuF/x+P9LS1M3H1hxVDYPAuXi8BGL1LzCjGg4FEfKruRhCYQh50sd9RwV8vpBE9Q0x+sci6xkhWVIBvz8qW+yeFMqmqvSXvbLBIdaN4Ps0gDUMBtdlqGfK9/nM2e+rAsGPiP6KxOFIhFrD4LNKxGof43vhPt8ZbgQCQdlTsXpko3eGMh13Srah9Ah3ncr2hngG12VoPaJXum50QofqyabMvDt4h2j4BHac0SBZVX5Hw7mHwsJCGZcTff32t7+N+b3W1lYEg0FkZ/fnquDv7Fc9E/zqV79CXl5eP2d3uKE5qhoGIaGsBDqDPmZZiL+zG6bEBDjKStTIZoqU+DJzOhDBMMCAMBmAVaCoMFkypz09MdbNH5QLo7goRYls7EdlaV4s2Wjc0V8oHZGqRDaWmrGsi/2WsaL9Pm8QY8ZkKJEt3jFmdAb8vmDMEtqeHt/pkmAVSCCpDh2rGLL5Q2GYDJGSfQ39UZiXBLPZAHeM4IzLE4DFbJDvKJEtnzrOJKywA0Fnhvq3uFAN8Q4ZzLnXY+kRZlO5F0tK1Oi4eAbvpdFlaeh1xXZaXG4/RpelQxXGjs6QtpqhdFxBfqRcWUN/JI4eIZGtoG9wRY2voxvm5AQkjlJjx52r4BZU/SJqampklmr09etf/xpnA7/73e/wzDPP4OWXX4bVqm5yheaoahiEtHnTkTB2JNw19Qh5P1RyLAf2tXcgY8k82AvzlMiWZDYgzWKENxiWSGoU/NkTCCHZYkKKRQ3rZHq6HQvnF4kz2Ovy9XNSGxqc4sjOnJGvRDayDa9YPhJef1CMzOilTwOuvqEbWZkOLJhXpMxQunh5mfQwtne4T8tGB7qx0SnlXRcs0i7UWOC6pCTbZJ24XgTXr6OTPYxhXMR1HVDON1zIspmkP9XFyH34Q9nIBMxsa47dLEEnDf0xakQqxpalo7XDLSRFUfBn9qaOK8uQ76gA9cTcmQWiQ+jA9NVxJOShjps6OUeJbOy7v3BpqVQZUAdH9QizbfUNPcjOdAj7r4bBuHBxqbSAtLa5+ulfMoqzJHjZ4hHKZLtg8QjhKxio43hXULddvKJMY6+Ngewls5E4qhiuyrp+mVN/Ty/8HV3IvXSx9K9qOPeQlJTU72WxxGYzz8jIgMFgQFNTU7/3+XtOzkfr6T/+8Y/iqL733nuYPHkyVEIbT6NhEEieNP6/f4lD//Z79BypQDgUKathM37GojkY9dNblMnGC2lcegICrU50ev0InbLjeE/RQZ2Y4VB6aZEuv6PTg5276tHWGiE84WVaXJyCH/9wntLxNFdcOgZNTU4hp+jsjLDocqmyshz4/nfmKJ01eMHiEpkDSlKlmpoueYY0Sdhv9s2vz0DpCHX9EfGMoqIUfOeWWXjw4R2oreuWklramWTFvPrKcVi6pFSZbCRQGpvmwJH2XriZ0ToVV2LPaq7DjCJFmd54B/f+LV+eirsf3YljJ9sjATn2+hp0GD8qHd/68hSlOu6mL01GV7cHe/Y3imOjO6XjRhSn4IffmaN0PM3nrhonztWGTdWngjWRlgsSP9363TmiTzQMxtRJ2fjydRPxwquHUFPXc7rsnON8rr9mAiaNz1ImG3X/t785Ew89urOPjgtL9dI1nxuPxVoQMyaMdhum/v4X2PMvt8kM1agdZ7CYkXvxQoz9yc2qRTznwEBJNFii6u9/EpjNZhkvQyKkz33uc/JelBjpBz/4wZD/f3/4wx/wv//7v3j33Xcxc+ZMqIYurNEuSjMy67yZQmd04rMANwPpn7OysqR5+VxE0O1B67qt6Ck/Bp3RiJQZk5A6awr0RvXxDWZoON6i0xvxVJPNRmTYTIOIW1SAWcpD5S3Yt79RovksN5s9M18i/mcTZ7LneNyPn2jHrt318HgDyMtNwtzZBTIHMR5AMpRtO+rgdHqRkeHA/LmFSh3ocwXt7S5hleYYiQSHSTL3LDM/mw7Nmeo4ZlB5Vl3+kJSGpllNUhmhZUE+GsxS7j3UjKMnI+M3Ro9Iw5TxWYOIs1TpuIOHW3CgvBnBQAgjilMxc1reWQ/EnamOI7v67r0N8FHH5SVhDnWcNkP1Y0Hm320769HZ5ZHRQ3Nm5EvbSDygrc2FzVtrhOk3IcEidyoDdWcT/wx2HCvhGldtQffhCuhNRmTMm4a0mROhNxj+KWzt4ZT7L2/vgc2RqEwOd28Pfnzp1E+0fhxPc/PNN+P++++XWah//vOf8dxzz+Hw4cPSq/rVr34V+fn5p/tcf//73+Pf//3f8dRTT2HBggWn/3sSEjj6To0u0BxVzVHV8E8Cbc9pGE5o+03DcEPbcxqGE9p++2yhOarD76gSd999N2677TYhUOLYmTvvvBNz5syRz5YsWYKSkhI8+uij8jt/rqoaPA6Qc1j/8z//EyqgPjWmQYMGDRo0aNCgQYMGDXGKMIknFZb+hv/Bv80y36FKfdesWdPv98rKSsQbNEdVw5Bgb9TJFifqO90wkLE2w4H81MEz6lTV6h8+3oajFZGyuLKSVIwblf4RowmGDyxS6Dp4DG1b9wqJQdLYUmQsmA6DOT6YCWvrumRuKUlHyAo7Y3q+0t7ZvmhudmLHrnqZ/ZmZacesGQUynzEewHK4HbvrpQc5JdmCWdPzhUk5HtDb65NZsyz9JSvrrBn5yIqTkj0ydvKZ1td3w2IxYuqUXCHdiQdwpMm+fY04WdkhumPihGyUjUyLGx13kKW/x1pPMzyPH8fMjnrZoi0EBw42SRnwiBFpmDwpG6Y4YXGurumSGcLeU6W/M6fnyd6LB7R0ubG/ol0YnNOTrZgyMh32OJGto8Mts5nJYZCaasWsmQVxo+N8XT1oWr0V7vommJITkb10Dux5/cduqByhw3muJO3iXTptai4K8uNDx4UDfoQqDyLUXAOdwQh90VjockriQsdp0HAmiA/tqCHu0Nnrw8s7a1DX4T7Nrmsy6jEmJxGXT82HVWGfVLfTi/sf34V9h1siM/vCkL4tOqrfu3kGUhVerBzps/+/7kbDexsQ6HVH5pgZ9EieMEqIDRJKCpTJxn7Zp57dh/fePyajCMiyS5De/9bvzcHoURlKDV8SKb3w0kFh7IzOi8zKTMAt35yJGdPUsExHsX5zFZ54Zt9pghbK+9zLh3DjFydhyUK1ZB579zXgvge2o7m5V36nbM8+fwCfv3qckMuoNEhOVLTj7r9uEceBcvGZOuwmLF82EjfdMBVGo7rAEsm7/nLXZnG46GyxCcZmNWLOnAJ8+1uzhO1UZVDkrr9uwYEDTafnklLHTZyYjR99fy6SFeo4GuV/e3A7tm6tlRE63F508keVpeNHP5iLnOxEpTruib/vwcrVJ0THcedz/zMw8oPvz8XIUnWkbNz/7+6oxTvba9B7at14IDKSrbhp+SiML1Y7Omf12go88dRedHbw3oqQsj3z3H7c/JVpWKxYxzWv2479/30P3HVNwgBIYqCjdz+JUd/9MkZ89XNKdRwDSffcu1WInj7Uv2ZctKIMN14/WWnwPNTeCP+bDyHUUMl0XOShmq0wjJ0J04qboDPFRxBYg4aPgvr0k4a4Ax3Tl3fVoKqtFwlWIzKTLMhINMNi1ONAbRdWHjyzQcFnA7wEHnlmL7bva0Qi52/mJKIgLwlJiWbsOdiMvz25O+a8teHCkbueQM3L78NgsyGhrFhe1pwsdOwpx+5f/gHBPuN+hhvvvHcMr71eDr1Bh8KCJBQUJCE724Ga2i78+a7NYhyrwqYtNfj703slw8WZqgUFyTIXkcQZ99y7RWRUhfIjLXjo8d3ocfpEJjr2zEQ7XT48/ORuHCxvViYbs5R33bNFnNScnAR5plw/Ol40MtdvHNxrMlxgwOHPd25CVXUnsrIdKCxMln1Hw+31t45IYEIV6Pz9+c7NOHykBenptohshUmw2oxYs7YSTz2zT5ls1F/3P7ANO3fWyWimiGzJ8vPOXXW474FtSnUczynXiGvFNaNsXMPyw83i+EcdaxXgnnrj7SOyx7jXKBv3Hvcg92KsOdLDhW2HW/DqpirJlOek2ZCbZkdmihVt3R488s4RNHW4lMnGzPhDj+xEr9N3Wv/m5SXKTNoHHtqB8sMtymTrOV6Fvf92B9wNzbCX5CNhZJG8Qr4ADt/xqASFVYGBS96dvJ94l1L/8sV2Vt61b797TJlsYb8PvtcfQKjuBJCUBl1GHsCX2YLgvg3wb3hVmWznKiTYqvh1PkJzVDUMAst969rdSLGbYT6V8WDE0mY2yOtgXRc6+8wJHU5U13Vj94EmpCZZYbeZTkdSmf1IT7Xh0NFWHDvZoUQ2T2s7al9ZCVNSAsypSadlM9gssBflobv8hESGVcDnC+Ld94/BYNTLiIaobMzS0CBpbOzBps3V6jIN7x2D3x9CZobjdGkjs22UjeVoa9edhCqsXHcSPb0+ZGc5TkfH+S/nMrpcfnywtkKZbGvXV6Kt3YX8/MTT2UmuX0aGXZxVrquqy23TlmrU1XcjNzcR5lMVGNx3KSlWmEx6vP/BcSk/V4E9extxoqIN2dkJp0tCKRvZrxMTzVi/oUqYlFVloffua5S5zHb7h1ld/pyeZpfPKhTpODKvbthYJWvEtYrqEa4h1/L4iTZZWxXgXmK1CPcW91hUNu497kHuRbLGqgDP4Jq99eKkpiZaTlezGKlHUm3ocHqxRWHA6/2VJ0SX0dmK6l/qOI71YRvGB6tOKJOt9tWV8DS3wTGi8PTEAZ1eD1telrTWVD39pjodt7la7k7eU1E2bu671FSbrB/vXN69KhCq2IdwUxWQmnk6cyoVXrYEwGpH6MAmhHsjWWANGuIZmqOqYRAauyLlvlEntS/sFgPcviAaFWXfTtZ0ylgVjuAYJJvNCK8viJPVnUpk6zlyEr7ObpjTBlPmG6wWhIMh6V1VAV6mra0uJCcNHtEQdb6OHW9TIBng7PWhsqoTSUmDy5B4sZoZHFFoxB063NIvKNJXNjoPB8tblBlKhw43wzTEqJfEJItE+ru61WSRTpxol1LfWOW93IdtHS7UN6gxlCo4nzQYjtm3yMwlM2/sW1UB/l06Xew1Hgi+x88ovwpUVnWgp8cnazQQXEuuqSrZuJfaO9xIjjGfl3uQe5GOtAp0u/yob3MhwTZ4v/Hsmgx6HKtVcxaouw4eapLZy0PquENNUIW2HfthsH4YeOgLU2oSuo9UwN/tVCKb7KdTpe8DkZRskTuXd68KhJqq2egOnTFGea89EWG3U/pWNZw5pHr6FKGSmhfOS2iOqoZBiFwIscsM+BY/VsXnEbkQ2CP4cd8ZfnDWrE4f6Z+JiXAYepOavjdmUlmONNTAaK6n0ajmoTKr8FGycTlVkrSIkTvEhuPb7N1W1SPFdRnymYbCIpfBoEY2OYfhoZ8ps0p89ioQ1RGxnivXM9p3qVa2wZ9F31MlG0d0cG1i7bnoWqrqO+aacL8PdR64eKr0COd7887k/O9Y4PtGRec0unZD6l+F60bwzhzyTqUjptNDr+q5io7DkPqX2Wllffj6j1iTqCGnYJaqBg2fFJqjqmEQitMdMBsN8PgHXw5Ojx+JViMK0+xKZBtXli7Z1K7uwRnd7h6vZFUnjFZDCpQycRRsuZnwNg+O2vt7eqG3mpE+e5IS2dhbWVSYIoy1A8HSJF6okyfmKJHNZjMJ22p3t2+Q48DyVRKkTFdIpjRrWh7cbv8gQ46/u1w+YRRVBa4Ls1hco0HM011eYYlNTBic/RoOkPiHhhyZVweC5dz5eZF+WhWYMD4LZotByh1jycayW1XkYuPHZQrTdSwd19Xlkc8mjMtSItvoUelSfsw1GojeXj/MFqOsrQpwP7F/PJZs3IPcixMmqJEtwWZCWX4ynK5ATB1HP2zSCDVET3TuZ88sgKs3to7zuP2YPUsdCWD2ktkI+f0IBfqX0HIdfe1dyJg3FUaHGntk0sQcWb9Y5b28a4uKUpCTo4ZcTF84GjCaEPbGaGFwdkKXlAZ9jlqSLA0azgSao6phEDiCZkxuEno8fjg9AYmoshS4y+VDIBTGrNJ02MxqCKMz0uxYtqBYqP3byEgcDMmrvdMtfYSL5hQiN1vNWA5eliNu/rxEf121jdI/w5+9rR3wNDQjc+FMpM2YqEQ2OqJXXzkOVotBKPR5sfKiZ4kjS5PGjsmQUQSqcMVlY4TJtK6uW0obKRtHrvD34qJkXLBI3YV64ZJSZGclSI+by+0X2ei48nf21K5YMlKZbIsWFKOkOAX19T3SS0bZaJTX1fdIeeYVl41WJhsd+HFjM9HY6ER3tzdiWPqCaGjskb5BMhKrygxyv8+cno+2Njc6O91ikNPZ53ifQCAs+7Fvf+hwgqy5Sy8olefJntCojuPPLJPnZ+wHVQGHw4wrLh/TZ63oZIVlDdvb3fLMOUZHBZi5oo7j3uIei+o47j3uQe5Flezhy6fnw2EzorHDDa8/IhvvMf5ekOnA7LGZymRbsbwMWVkO0bfUbSKbyy+/U/dduLRUmWz5Vy5DYlkxeitqpMSXsgXcHvRWVMOSnoIRX7lamWyzZ+XLfm9o6JG7NKrjZEyNxYirrxirbJyUvnAM9KWTgO4O6UUNh0MIBwMId7ZKJto4cwV05vgYPXSugLaw6tf5CF34fKWR6oPu7m4kJyejq6sLSUmfTYQ/FAqhubkZWVmce3fuxQN4ka481Cgsv+xJZZUIM6l0UueXZSqd5Ufj6KW3j2DVhkp0kcUxDCH3WDy3CF88ZaioAo9T5ZOvouLRl+FujFwIJFfKuWghxv/ym/Lz2cKZ7DmS77zw4gE0NjnF+LXZTZg6ORffuHm6ZJFUgrNdn3hqD2pru+QZ86IfPToD3/rGDOUz6Y5XtOORv++RHj32QbNvtqQoBTffMBVjytKVykbm34ce3SWsq3TyabAzs3TD9VMwc0a+0v3G7NbDj+2UZ0vDl44pDeLrrpmAJYtHKB0rwUAI99umzTVwOr2i09LSbLjisrHiqKrUcWTOffb5/Vi1pkIy40RysgXLlpTiS1+YdJq4RQXomL7+5mG8+fYRcU75e0KCBQvmFeGmG6aIM6tqzwlp0bqTMuaKTNjUcQw4cK7lNzi6LNUGldhX0YaXN1Sisd2FAHukzQaMyk/C9UtHIjtVrf49crQVjzy+C5WVHeJsUceVjkjD12+eLqOHVMJZUYMD/3OvsOcHXW7ozSYklBVh7M++jqyFM5XqOAaQHn5sF/bsa4D7lI4jCdV1105UGmAlwp5e+Fc/j9DRnQh7XGShgi4xFcZZF8Ew48Jh179nw9YeTrn/+OpO2Bzq5pO7e534xdUzzrn1+7TQHFXNUf1IMItK4iT2kxWk2ZRlUmOBTurxU8QdpcWpSuenDoS/x4mO3eUI+QNIGlsKe/7ZH0x+pnuOGTeO5fB4gzJmpbAgPgaTE3RQjxxtkWwSs5UjRqTGzWByGuR0WDu6PEhJtmJUaZpSZ6YvqMZJwtPS0iuOwpgxGWe9r+yT6Ljaui7J8jKjP3ZMZkwSI1VobnbiZFWnOPhjR2ecVUfrHxl/cfx4RMeVlaUJY3e8gI7+4aOtcmZLS1KRmemISx2Xn8eRUnGk44IhnGB1hjeA9CQrCjMdcaXjSKrHDDn3WllZelzpODLnu+ubYUpOQOq08adZgONBx5G4LpJJjT8dF+pqRbiphmUH0OeNhM6qJiiiOaqfDm7NUT1/oTmqGv4ZoO05DcMJbb9pGG5oe07DcELbb58tznVH9bZX1Duqv/zc+eeoaidPgwYNGjRo0KBBgwYNGjTEFeKnNuE8hTcYgtMXhDcUgh462E16OEwGGOKgFIh9ITWvrkT7jgPQm43IWjwL+VcuhTVDDTthX7S09mLd+krs3tsgPaqTJ+Vg8aJiISJRDTJzrt9YhR076+DzBaQMiP14ZABUDRJlbNxUja3baoSYhaW1SxaNkF5Q1WBf3patNdi0pRodHR7ps1y0sFiererSOPa67dxdjw2bqtDc1IusTAcWzC/CjOn56sYPnAKLYvYfaJI9V1PTJWXJ8+YVYd6cQukzUw2WEq5ddxInTrbDbjNjzuwC6WeMhxJbrtfaDSdRfrhFSqVnTM/DooUlsobxUJLMnvK9+xvl96mTc7B4YQmystRF9KPopI7bUCk6zu8PCVPxBYtHxEUbAUuSN26mjquFy+3DyBFpIpvqPkuCvZ9bd9Vh8446aV0pzE3EgjmFmDg2U7mOYwk3dRz1b3NrL7Kp4+YVY/rUXGWkZ3113L5TOo7zclNSrJg/pwhzZhUo7dc+F1Bd04m16ypRfqQFFrMRM2fkYeH8YiEv1PDJEA6G5aXy75+P0Ep/FZb+9viDaPf4wb0XvaL4MKwkHLGZYFTYG9K8YQf2/p874Glqk7EqJAVivyXZ92b85d+QOLJImWwcsn37nzeiodEJk0kvi+f3BcWA+8kP5wu7oyo0NvXgD3/aIP2CnF9p0OvFWU1JtuG735ktF+vZwsftOTrQf/zzBhw81ByZYWmMjA6hw/CVG6fi4hWjoAokAbrrns3Yur02MpvUxHULyriLz105VghkVBlyNOAeenQnVq46IT/T+aNsXL+lF4zALd+cpcxZpfp+/sUDePnVcnmWlI2OA5eKTvRPfjhPxv+cDZyJjvtg5Qk89uRu6Tm2WAynWWLHjcvEL366UGnP5Y5ddbj3/m1o73CLbBwRQvnIovyLny5AXq660qqjR1tx+50b0dTUC5M5srbUcdnZifjZj+crG50TJe+67Y4NqKyM9PXy0fM8kKjo1u/OkX13tvBxe449vX+8YwPKy1tOz7D0eoMy0udrX5mGC5epY+h2e/y468Ed2LWvQe55Bka4btx7V18yGtdeMVapjnvgkR1Ys75SgnIkJCRpHNfvwiUj8M2bZyhzVqnjnnlhP15/88hpkiefnwSPOsyZmY8ffm/uWesHPddLf7dtr8W9D2xHV5cbZrPxFIN4GCNKUvDLny0S0qfhxLle+vuHF3coL/39l2tnnnPr92lx7p28fxL4QyFxUjm2zKQDTHpd5KUDPMEQOrx+dbL1OLH/v+6Gt60DCaOK4SjKg6OkAI4Rheg5XoWD/3vfoFlww3mh/u3B7eKkFhQkITc3UWaEFhQkS5b1/ge3x5xpNhzgmjz6+G5UnGwXkiLO9cvJSUBhYbJkLx98aIdkIlSBTJj79zfJuAHOr+SIC8rG0UN/f3ovqqs7lcn2znvHsHlrjbCu8rlGZTOb9Xjl9cPYuy+SVVIBZmfocNHYpUxR2RITLcLKum5DpTLZDhxsxiuvlotjH5WN65eWbsP2HbV4652jymQjgRJZdf2BIAoLI880uu8OHmzGcy8cUCYbR0k88PAOmb1cVJgslRg8s3wxyPTI47uV6Tiu130PbhfWWtFxOR/qOGZZ739gu3xHBbgmDz22S9YoLy9RXpwTyb3HtfzbgztkbVWBTMncW333Gvce1+vxv++RkVKq8PbKE9ixpwHpqXYU5CZJxrIwPwkmox6vvnMUh462KpONmUqyJSclWlCYnyx3RFFBMhIdZqxcXYFNW2qUycaKAjqpdFCZsadslDE91YYt22rxzvvHlckWz2DQ5sFHdqK31yvrRqeUNgl1XMXJDjz2xC7VImrQcEbQHFVF6PWHJJNq1EUGbkfBn1nI4gqEZGapCjSt2gJ3XRPshXn9ZNMbDbBmZ6BjzyF0HVJzORwsb0ZlVaeMuOjLRsifs7McYhyrcmpq67qx/2AT0lJt/TJsXMPsbAfa2l1ScqsCNB43bq4S56pvOShly8ywn/q8WlnwYfWaCpjMhkHZP2bcfN6AUmdwzdoKyQJy7fqCjit9GX6uChs2VsLtCQwau2GzmmC2GGRdWVKtApu31MgMS5ZJ99Uj3H+c8bplS42ywM22HbVobXWJ8dZXNp7b9DQ7DpU3o0pR4IbBJJYkx9JxfK+6pgsHDjQrkY1rUl7eLGs0SMdlJaClrVfWVpVhvmVrreytgTqOe7C724PNW9ToOJ7BNZuqJXtqtRoH6TiPN4CNW9U5g6vWVUjQPGFAOT51Ht9fs/6kMtnWb6qKZOwHVF/wrmDZ7+q1FXKHaBicTaXNwXM5UMfRRtl/sFlsFg2fLFCncoZq+DwtgNUcVUWIOqGxSn34Fj9W5aiS/p21SXrT4HIaY6IDAZcn8h0FaD01ZH7gZU+wtCUcivSvqgBHg3g9AZnbNxDRsil+RwV4YbncsWXjHqQRzNmqKuBy+cTIdMSQjaDRycHzqsAsjM0Wu7SMc2g5dkXVBUJDY6g+VK5nV7dXym5VgPuJuiyWjuM+ZClkq6qz2uqSf2OVM1I2lqJHvzPcoP4KhkIxyxn5Hj9rblFzVltaXOJUxdIj4riGFeq4Npf04A+l4/hiJY4K9PT6JBhoH6IMn6W2tQ09UAHqroaGniFls1mNSjPRdR+h4/isGezijGYN/4iOU3NWNWj4JNAcVUVgjyARy8DlO/xUVYuqKTmRgiHMpq0BCHm8MmzblKSmTp9ZLBocsbJE0aiqKpIWykZjLVbpMZ8zn2tCgkWRbBaY2a/lC8T8nBnD5CQ1slmtJjHA2UsWC+y5JHmGKpBYZ6hycmZ7uW6qesuYSQ0MUQbK9aSBZx3CyT7bYBmh7PsYOi7S/2ZAoqrz4Ihkw2PLFpBMDc+zEtkSLNBBFzNLxPf4mSo94nCYTvVWBmLqkIiOU6d/WQLPvTUQ0X2oSsfZWeFgjvR9xoI/EFI2B5y6i2d1SB3nCyolF2MmdahS90iPr3HIQOL5jI/WcUE5KwMz6Bo0xCM0R1URyO7LxR9oilCpBMNhWI166VlVgawls2FOTYansXWQbO76JiSUFiJt+nglspEBluVvjBYOVMAs5WOP4/RpuUpkKxuZjpKSVJFjoGwkbOGlMGvm2SMa+ShkpNsxeXIOOjs8YlD2BSP9vOxnn0Wip48CDTiywJKtc6BxzgwJozbz5xVDFcgCS7kGGuc0kny+EBYvGqFMtnlzi8TQHJhRoLzMpM6fWyRlwCrAvc4gxMCeRe6/jnY3Jk7Ikh5CFZg5Ix+JiWa0t7v7vc9z29rmkr7VUSPVsMSS3Tcjwy7Zjr56hD/zPZbq8zsqQBIn9qO2trpj6rjEsHxsVgAAfQ5JREFUBLOsrQqwxHHC+GzZW7F0HPfirJlqdBwrgObMyBeugkCwv45zuf0SuJ6r6G4gFi8qEYKigc4qdR51yaIFJcpkI3s5LwFWYPQF5ep1+bBwfpHG/BsDPIcM3nR0DNZxrD4gadzIUvUTHM45hBW+zlNojqoiWPQ6JJoNUuLrD0WcU5b6+qVvVY9Ui1FZlsael42y716PcDAI54lq+Nq74G3tgPN4FcwpSRj3s69Db1Jj/LIv5cbrp8BiNqCmtluYbPmqre0Slt3rvzhJWZaG5bOUjdk/9pixnJU9eixd8nmDuOqKsUJmoApfuHaikE9RNhroJD+pb+gRGZcuKcWE8VnKZLvi8rEoLU2TtaKjT8OyqckpP8+eWXDKWFGDZUtKMWliNhobnWhu6RXZ+C9/55pduKxUmWyzZ+Zj7pxCKe2mPJSNRghL9Rg0ufLyMcpkI/v2hUtL0dPjQ319j+w3OjPcf+wN/eJ1E5XJxr//uavGSaaGfe3sX+R5Zf9nUqJVzrEqJmdWhNzwpcmSuaztp+O6xSD/8pcmK6sa4Zp85YYp0rtYU9N9Ssd55NwG/CF8/urxykaE8b780hcmyrMVHddxSsfV98ge5F4cO0YdW/IVK8pQUpgsOret3Y0epxeNzU6Rc870PMycoibASixfUorxYzPR0NQjwZDTOq7JKQGlJYvVOapzZxdi1vQ8uQuamiM6jsGkuoZulJak4tKLRiuTLZ7B8W5XXjZGsqe8D3jPR3UcM+TUcX174DVoiFdo42kUjqfh0jv9IXT7A6f7Ue1GPZLNRpjjYG5Zw7vrUfn0G+g5UilGQPrcKRhx01VIm6HOwIxi774GvPHWURw91no6m3npxaOURfP7gjK9+fZRkZGR/YL8ZFx80SgsXlh8VoMPZ7LnaJSTQZGMsIxIZ2YmYMWykVh+4Ujl80BpIL351hEhdeKoFc55o5N46cWjY/YkDydoVL719lGsWXtSovjs8eFMyysuG4OkJLXz6LhWb797TIiTaIgwQ81M6pWXj0VmpkPpfuMeW7n6hLAm08jkHps5PV/WTfVcYeo47rV33zuG6touyWpNnJiNKy4dgzFxMFd41+56vPnOURw/3ia/jxqVjssvGYNpU9U5NFEcOdqKN946LPN7qeOKi1JkvBVnC6vWcWQvf+OtIzJ+iPuPmdYVy8tElyjXcW0uvPn+MWyRGduRktolC4px8dJSmXGpEnRk3nz3CNZvqEKvyy9l3pyxfdnFo5SVmkfh8fiF3XfV2pMStOF9MH9uoZwHVgudLZzr42mo4/g83/3guATz6ZhOmZyDyy8do2Su8Lk+nua3z22F1a5uPI3H5cSvvzjnnFu/TwvNUVXoqPZnEouQKEV7V+MFlM3f1QOdQQ9TovpB8wNl6+31g51RLKtVPTB9IFiOyf5BZh+GQ7ZPsudIpEAHh6VBqoe5DwTlonzMGqk2LAeCvdF8rnRU463cTErhen1ixJ2tuYL/6H6jM+N0eiPMzopKkYeCBAydPjkHsYh4lMvWGyHDilcdx9mMUe6As41PsudYKsrZs3Sy4i1zxD5ojzcohGdGQxzqOJJSnWLVjSdEy32tlvjTcfEM6hFWFfA+VanjNEf108FznjqqWgd6HEBG0sTXPdpPNpb7xqtsqog7zgSRCyG+DN8o6MyozlIOBcswGSH/CGi4JSfHl/EWBY0QZqHjEXQUVGeeP0qPDBw9FFeyKc5mfRTizbHvCwZE4i0oEgWzp6ozqB+p4+LMQe2n4+JUj8QzhDBLEZGYBg2fFvGpKTXEDRgt7+j0SP8ny5TiKaLPKCGJAlgTQBKleJONtPmMAJO1MN4yg+zdYuaSzzTeoubMCvJFpyveHFaSO/U4fUIaM3Dmq2rwebIszm43x10AJ+jzSZ+7wWqBJU1tyW8sHcc+QZ7ReNRxUcKneNRx3rYOhP0BWLLSoTfElx7hWZDy2lSr9PvGE5jB51iueNRxgV4XfJ09MKckwug4e2W1n0bHsdpGVZ/2R2WiOzs9Mis33oJyPKtsL4smReJJj5xLGIrFfjj//vmI+NKQGuIGPBBrN1bh3ZUnhLSAJcmjytJwxcVjMGVitmrxZKj8G28ewcnKDnFUi4uTcdklY6RvRbUSPnioCa++fhiHj7RIySPJRS6+qAwXLh2pvASN6/XqW0ewZ1+jGOgcb0IijUtXlCl3WBubevDyq+UyqJwMlIkOM5ZcMEJ6LVUbJQw6vP7OUWzcWg23OyDZ6HmzCnD1ZWMGDaJX4Tyz73jVmopIea3JICRLV185DnkKybuIkN+PyqfeQPXzb8PT1Aad0YDM+dNR+o1rkTJhlHIdt2p1Bd557xgaGnvkbLI39aorxgl5lmrZtm6rxZtvH0FlVae8R5ZO9paRPEs1WrfuRcUjL6Fj9yEZY+Yozkfxly9H4TUXKde/Jyra8cprh7Bvf5PouPQ0u/TgX3LxKOUOa319N155rRzbd9aJY8OyZPbOkvRMdeDL09KOEw8+j/p31iHo8sDosCHv8iUY+Y1rYUlPVSobA5evvXEYa9edlJm0nDtLlvrPXz1OGXlXFHyO5KRgLz4D52whYB859e+IklT1AXNvAB3eAHx9eFDSrSY44ixArUHDUNB6VOOgRzUe8dLr5Xju5YPCiJ2UYEEoHJZIJvukvvetWUK1rwo0yB98eMdpwh2iq5sGuh433zRNyHdUYfeeBvz5rk1CTJGcYoFBr5fsJY/Z568ahxu/PPWs/e2P23MnTrbjtj9vQnOrU8qnTEa99L/5/CEsWViC731rprJ+VTL8/ub3a1FV3YnEJIuwOrP/ze0JYPq0PPzypwuVlSqTSOn3f96I8mOtSLCbRA6PJyi9UqPLMvCrH89XVo7GjNGf7tiAbTvqZF4qe974HvdfYUEyfv2rC5CXm6hkv9GB2fcfd6LmxfeEJdyUkoiQzw9feydsuZmYcef/RerksVCFZ5/fjxdeOii8/8yA8H9PZ5dXsuU/unWeUmK291cex8OP7pJnmZwcKdnr6vLKufj616ZjxYVlymRrWr0Fe379J8m60YEhfwGZ4TlGquw712PMD246a3/74/Yciexuu32DELOJjjPppTePXAHLl43Ed26ZrSxYSLbf3/5+LWpqaWdYhPSMpEUed0ACSz//6UJ5TwW87Z3Y/r3/RMeecpiSEmGwWxFwuRHodgp54qy//oeyFiDyFdx2xwYhF7NZjVJuTibbnm6vkHj9279egKysBCX7jYGQv96/DavXVojzzEoWzv2mrUQSr1/9YpGw2atCk8uHtlNjfQw6ndhzwVAYBr0OBQkWJAyzs3qu96j+5tktyntU/8+X5p5z6/dpce55UBrOOkiZ//rbR+TSzMtJFOXLgeCkOydBxbMvHpAooqrI6nPP75cLoqAgWXrLEk/JRvODhicvCRVgme9Tz+yVrFZhYZIYSlw7OgokpiAzKxkpVeGFVw6hubUXhfnJUuLILCUv09QUKzZuqcaBQ83KZCNLJ53UgoIkpKXaRDYy1mZlOrBnbwM2b61WJtvq9ZU4crwNedkJkp1x2M1IT7MhNycBR4+3YdW6SmWybdlWgx2765GVZZe14roxS851pEH8+puHlcnWtuMA6l5fDXN6CuxFuTAlJcCSkYqEsmK4G1pw7K9PKytl4rgG7jkGHZh1Fh2XZEVhQZI4DzzHA2f6Dmdg5LkXDsja8Dme1nEFSRIw5Gf8jqoM+eG/PA5/d688R3NaMkzJiXCMKIDBZsPJJ16Fs7JWiWxcr2dfOICWll6Zg8sxYTwPOTkJEtBcu74S5YfV6TieRZ5JPkeeUcrGM8uzyzO8dVuNMtlqXngXHXvL4SgpgDUnQ86qLScT9pJ8tO86iJqX31cm26Yt1dizp0HWincC1413BNeRdwbPsSqQ9XrDxiqpquFMaMrGfcdZw2Q5f14CYWrgCYQkk8pqOE6RoHNq1PNnnYxDbHb5zttS0n8UoZD61/kIzVHVMAi79zZKH97AkkYZUZNuR31jDw6fGguj4mJoaXUhI2Pw2A3KxvEczGqqKjmjIZKeYR9U/sbLiwbwzt31SmRjhuHQ4RZxSgdmFJglZ+aGxpIK8G9v3lIdk4FYsqhhYPMWdUbcxq01kpkZWBrNMkJmuNZvrlIm2xauS5jr1L9skOtI54bGL0uDVaBp9VYE3d5BmRidXi89je07D8BVo+as7txVL0Gv1NT+mXCeW467qKvvOT36arixd2+j9KVSnw0E3+Nn/I4KdOw9jN6KGljzsgbpOEtmqjDEN6/drkQ2zvw8cqQlZi8vz4LXG8SOnXVKZOMZ5FmkHIN1nEm5jmNAyWC1Qm8eoEfMZujNZtS/sUaZbLwbGIUeWFHDdeSdQUeWd4gKcD/5/BH2/L7gHUvdcuAg7ZVeJbI5/cFI9nRAAQHPhkmvgzcYhjt4nno+Gs4paD2qGgaBJY1UZrFKpFjewkwDyzKVyNbrk3E0sciJeHHpoBNqfVWycW24RgPB9eRq0llVIpvLLyVJzOzGAp81gxOqZuSxJ3Wosjc6iaqy5ATLaIfq3zWbWT7tl8i0it68rm7PkERdlM3nC0npnIr+NzotGKLM0mAxI9DTC3+PGiOORDZErGfGfegPBGX0lSr9O5SO43v8TJWO4zML+QPy/AaCAQiup6pnSv1LHTcUkRgftSodR0eVsvFMxgKfK1tEVIC6y9fZDX2MZ0oYrGb5XK3+jb1uHHdFJ5VtQCrKprudXuh1Q+lfA9wekmapOavMmtLoiKXj+A71yKm2VQ1niHAoLC+Vf/98hJZR1TAI2VkOUWSxSt/o8LBPhGU4SmTLTpBMVqwsES8rOlyUXwVyshMk6hvLwGWpMqFKNmaJEhwmMYJjGSqMvLLMWwVYLsWSWhqascBMCHuRVKGoIEkIlGLB5Q6gIC9JGYFMUVGKGGqxSri4D1limJCohogqoSSfm0t6VQeCpaPGRLv0qqrSI3xkMXVcr0/GmqjUI5ytyQDDQPA9fqZKNltBDgwOG/zdzkGf0YEl7AU5CiQDMjMc0qMdS/9G2DqBXEU6jn33UlUzhGwM1LFcVNmYt7IiBJyxAwwBpwuJZcVQBep+3gGxwLPKu0PVmKT83CTpYx1K/7JaifKpAMt9iViyBcOMIUYyqxo0xDs0R1XDIMycmieGHHssyFobBY06lp2NG5OJEkWOw7ixmUJOwHKaqPNHUM7m5khv0uRJagwl9rpNnZIr5cd9e3h5UTQ0OqW/Zs6sAiWy8cJcOK9Ysn906PvK1tzSi+REizAmqwAz4RcuK4Wfw9z7OKuR0Rwu2GxGLFpYAlUg0RTHM5Gwqy+YAeFlv3ShOiNu0YJiMdJ4LvsaJIzi0/glo6gqptPcSxbBkp4CV21jP9lYDuzv6kbepYuVjaqZNaNACFgaG539ZOO55agasv6qchwmjM8Whl/2WvbVcfyZOq6kOFW+owJ0WDLmToW3uU2IsaJgMMJVXS+ObM6Fc5XIxj7U+fOKhCNgoI4jWRsdxQXzi5TIxjN44dJSCSr1zbBFxw+x0mWxQh0nbM16PXwdXYNIlvRGAwo+v1yZbNT9DI4P1HG8K4KBMC5cNlIZCSD3E3vbeS77ysaAEh1V6mdVo8ISTQZxRP3MAvaRLXQqME3WX4uiddOg4ZNAK/3VMAgsE/zuN2bi7vu3ora+WyL4VG4sOygtScU3bpqmLIPEC+m7t8wSptOaum4hCGD6lxdWbm4ivved2UrHrNz8lWniRB873hYpn9bphHGSUdXv3jJb6Xy1a64ah+raLuzdH3EcDEY9Av4QEhMsuPnGqZIZVIWLV4zC8RPt2LipWgwSlsLRaeBM0OuumYCJE7KUyTZnZgEuOdGO91afQHedV2SLlnivWFaK+QrHhTBw86UvTBIG25qabimRo2w8JwvmFeHSi9WNgHEU5WHCr7+DA/97L5zHKqXXLRyMBHAyF8zA6O/fqEw2Go/UI3ffu1XWjc80quNGjUzHN26erkzHUZbvfns2/vTnjair65EgCREMhpGfn4TvfnuWsrnMXBM+U09TKzoPHIVOpxfWX2ZTbTkZmPzfP4IpUR0rJs9CbV23cBn01XFk2eUzVTnKhGz05DHYsrUWbW2uUzouJIEmys2zrAp5l12Azn1HZIwU5x2zV5WBCM49Lr7xSuRetFCZbNT9135+Al565SBqaruFrT6q4xYvKsbFK9QxYBfkJ+NrX52GRx7bhZqaLhhNBoQkuKTD9Gm5IrcqkDgpz25Bfa8XviBbUyLOavjUiJqcOJsDfi5Am6OqBtp4Gm08zZBgpm3DlmqUH20VZ3Xa5BzMm10gjo1qMGtJp2bf/kbJpk6ckI3FC4uRpqjMpi8Y6SUxxs7ddRJBHzM6E4sWFp/1srMz2XPMNGzfVS9zaFnGPaI4BQvnFyvLkPcFjQ+STW3dWiPPNz8vCQsXFMtsS9WzGakm9x9qxubttRKIyEizY+6sAkyekK18Ni5x9Ggr1m+sQm1dl2SO5s4uxPTpeWc1m3qmOq77WCXq3liDroPHYEp0IHvZXOQsnw+jTV3Qpu/s3vUbqmTmMQNcHIU0b25hfOi4Drc80/0HIsRJrBThWWU5t2r4unrQ8O56tKzfgaDHh7QZE2TmpqMw96z+3TPZc8xmbd1ei+07aiV7ObI0FYsWlEiZvGqw93nXrnph6+7s9Iijw6zb6NEZqkWTrHjLpl1oeHcD3PUtsBdkIfeSxZJBjwf9e/hIKzZuqhLGbhI9zplTiBnT8s5q0OZMdVxlVYfoEc4pdzhMmD2zQOa8Wizqc0G+YAhdzOQHglJCmWA2IslkiAT5hxnn+nia/35yk/LxNP9+0/xzbv0+LTRHVXNUNfyTQNtzGoYT2n7TMNzQ9pyG4YS23z5baI7qp4PnPHVU1Yd7NGjQoEGDBg0aNGjQoCFOwbSeytRe+DxNK2qOqoaPRH19NypOdkBv0EkPzcDZqirBEsyjx9rk9I4qSxdylHgBy206PAGEEBZSA5bbxAs42uLQkRZ4vEHkZiegtDhFeWlX39LkA4ea4XT6kJlhx9gxmXFRWhstTS4/3CJkOyyvHT8uUxlR0UCw/J1zP5uae6X/cuL4rLgoOzvNanryBPx1tdBbLLBOmAy9Qw1rbSywXLqyslN6QceNy0JKsvqS5ChIqBSd5zp6VIYQssULOrs8KC9vlt7ZkpIUKWONF5Dd/GB5i+gTthCMiCMdFw76gNYKhP0u6OxpQGqR9PrGA1iafKi8RcqS01Jtcuer6oeOVZrsO1qOQGsL9AkJsIybJPokXnTcicoONDT2wGoxYsK4rCFHwalAdU0Xqqo6pL2Bz5TEYxo0nCuID0tGQ9yBjsIjj+/C1m3sZfTJuBoqN5LekNxG5eXFvs+/P70Xa9adRM+p2XM0ztnrc9ONU2W0hMoL62SXG9U9JDCgmxohNci0mTA2zXGaMl6VbKvWV+Klt46gtc0l5DG8VMeNzsC3bpyKrAy1RjD7yp58ag8aG3sQCIRlDt3I0jTc8s2ZGFGSqlS2Q+XNeOjRnUKYQYeV+78gPwlfv3mGMMSqRHV1J+5/aDtOnGiXMQ50uMjafeOXp2D+XDUsp1HQqGx74B54Du5HyOOBTq+DIS0Dydd8EYkrLlXqPPQ4vXjokZ3YsbPuNBMrAxCXXDRKSFBUMYkSdLCefHov1q6rFBZbIiHBggsWl+CmL09RGoTg/iexzTvvHROHhiAh0MwZ+fjm12co7e+ljntv1Qm8/Fo52trdEsCx2oyYODYLt3xtBjIy1HIYhOr3IVz+DsKudnpegMEEXUoB9JM+D12SGrb6KEhA9chjO4WMKqrj2NdLEqrx49SR2RG+qgq0P3If/CdPIOz3kVURxuw8pFz/VdhnqmGZ7svl8cDjO1F+pBVuT0AIFBlkvfaqcVi2eIRSHcf547y3yP1AHUdJ2ON+2SWj8fmrx8dNEPhcAfVJ30kYKv7++Yj4CJVpiCvwsr/3/q1YtboCRqMOhQVJwjZJFtbnXzogRopKPPHUHrz+5mGRk3LxxbvgrXeP4eFHdymVrbLbg+Ndbhm2bTPp4TDpwbugodeLA639R2EMNzZuq8Wjz+xDV7cHOVkJKMxLkrmDu/c14o77t8HtUTOYnDhwsAn33LtFItIZGQ4UFSUjOdkiGcw/3rFBHGuVjuAdd27CyZMdSEuzydgS/ltV3Yk/37VJiDRUEu5wfQ4dapZZjYWFScjMcsi4hHvv34a9+xqUyRbyuNF8++/g2rENOpsdpoIiMS6D3d1of/QB9K5bpU62UBh337MFa9edlCxDQUES8vISxdF/7oUDeOW1cqjEY0/sxptvHSGLxGkdx5/5Hj9TiVdfL5c14lpxzbh2ZJvmWt79161Kjam1G6rw6JN7ZHQU59Hy7mJma/uuOtx+z2Z4+oytGW6EW44itPeFiJNqSwUSsgCTHeG2CoR2PImwp1uZbNRh1GXUaVEdR4emoqJddB8zcqoQaGtF61/+AN+RcuiTkmEqKIYxPQuBpnq0338nPOUHlFYncV/t2tMg+6wwP0n2XUeXBw8/uQebttYok43Bhjv/ugXrN1RK0JdngefV4/Hjmef2iQ2lQcO5AM1R1TAIR462YueeBqSn2ySLKmNW9Dqkp9slA/fu+8clUqcCnIdHgygh0SwXKeXiKyXFhuQkCzZvqVZ2qQZCIdT0eMQxtRrpoOpk7ZhFtRoNaPP40e5RYyhxBuOb7x+XWaXZmQkSLadsDrtZyn8rKjuwbXc9VOHNt4+ip8eHvNxEuVSjY5Ly8xOl/Hzd+kplsn2w6gRaW3vFII9msvgvnYe2dhfe++C4MtnWbaiUkQ2UhYaS7DeTAbm5CcI+/cZbR5UFR+ig+o4fhSknD4aERJFNZzTClJ3DDYmuN15BOKDmPJQfbsaefY0SFOHoEsrGDCozbtx/zBayqkQFuN/J9svAA1stojqOPycmWuQzfkdVFvqdd4/JGnGtuGZcO47dSs+wS2CEa6vKMH/97SPiKGdnfajjOEOajOscGbZjlxodxzMYqtgI+D2AIwM6gzFyHkxWwJGJsLMZ4bo9UAXe6dRl1CNRHWe1GkXntba6sHL1CWWyuTasRqCxDsb8QuhtkYy4zmyGMScfIWcPet59XZls23bW4cTJDtlfDodZnin3HQPBHEv3+jtH+81CHk4cPNSE/fsbpV0gKbGvjnOIjG+/c7TfTF8NGuIVmqOqYRCYxfJ5A1LONRCpqVYp95LeUAXgGAkakMkx5pHS4KTiVWUodXmD8AZDMYdos/yX/kKHV83F0Njci7rGHqTG6E1hRollwAcPtyiRjc+MpbVRh6EveLFyNt2uPeqc6J276sVpHihb1NHn56qcwT17G6TqYWCZasR5sEh/oyqHy3PoAMKhoBiVA2FISUOgoR7+WjUZB/bhReb0DtZxKalWyVTTsVGB8iMtEmRg4G0gWGXAz/gdFTh+vF1GR8UakcNACdsyeH+oAGd+NzY5pXx7IOhY04E9pEg2+FwId1QBZsdgPaI38D8Qbj6qRDTqLo7MoS6LpeNsNqOUx6uCe+8uwGiCbgDrrgTQk5LgLT+IkEtNxc3B8mZZv1iz2xnk5yidhianEtl4Dhm84d01ECmpNimN51xfDWcOztlW/TofoTmqGgZBSrd0kYtgIPieRIcVGebRv/tRbR9s/VGFj1oWfqSq8jcYCslzG2rddNAhpGjdoqWCQ8nGDHUoqE5BB0Nct9jC8W2VpY4fLVvkrPI7SsANNdSf5kOV4elqNt1HPTOeBZ5TVTru45ZE9IiqsyrPLPZZje5DVXpEDDmRTTfkunHciBqcOgtDXlw6pRdXRI9gaB2nUP+Cwa6PEo4Lq1KPDCma7tSdH44/HSfql/2WCo0lDRrOEJqjqmEQykamwWjQw+0enP1jyS8zNWWlaYpkS5csSJREqS96e/1SrlRWpka2RLNBynx9MS51GgK0zZMVkaDkZiUIWVJXjHULSGlSGKNHqlk3DkgvHZEqpb8xGWN9QUycoI7MY/LEbMn6DjQ4+Dv33ORJOcoIMyZNyJbMYCyjpLvbi5Li1JiZueGAZdRocUhjlfcGuzphyMiCKa9AiWxlZekwGPXwxCjF7+6O6DjuSRUYOTJNsiCxMuF8j5lLVTqOa8K1idX6wbVkSSHvDxVg20BGuj2mbMwssRVj1Mh0JbLBnBAhS/L2DvpIgjWhAHQZpUpEo+6aPClbdFksHedyBTB5sjrCOMuEyQj5vDEdvlBPN8wlI6GzqyECHDMqQ/zkyB06WP/yzmUZsAqMHJkuLQMkZhskW5dXMr6qSQo1aDgTaI6qhkGYOCFbWP5IyBI10PmiAdfj9GHRwhLpV1WBwoJkzJ5ZIOXH7JeKysZyuPZ2F6ZOyRWmWBWgk5qfYEYgFI4w/p66WPm7OxBCktmIDEWU9SxNunhpqTg0bR0RNkzC6wugvqEH+blJmDczX5mhdOklo6U8r6nZebqnhw5YXV2EXGnJ4hFQheUXjhTjvKHBKQYvwX8bGp1ITDRjxYUjlcm2eGEJMjMTpGfR5w/Ke1w/MlGazAZcevEoZU60fc4CmAuL4a+vRcjrOT1iItDeJj2qSRdfpmy8xJRJORg7OkN63qMBOZ5XOjk02JdcMELZKK6S4hTMmJYnY5DomEZ1HH9mSTI/Ky5KUSIb14RrwzXiWkV1HNeQazlmdIYEblSAvZUXLx8p+7+9r47zRnQcyWTmzFITGJF+1BHzAYMRYVfH6UqCcDAAOFsBeyp0+dOhCisuLBNdRp3WT8c1OEX3LV9Wpkw2x8KlMKZnItBQi7D/1FkNBhFoboLOYkXixVco03FzZxWgIC8JDQ09px1CuWPbXfLvJcvLYpYFDwemT82VkVaNjf11HMdK0a67cGmp9JZrOHNEdbHK1/kIzVHVMAjsd/vRrXMxbWquZC5ra7uFsMXnC2H5spG48fopSuXjCISFC4rhcQdENr5oOM2ZXYjv3jJLKR38yBQ7ihItUoLW6w+h1xcUpzXNasTkzASJ6qvC8sUjcM0VY8FWH/arsqeLfSqlJan48bdnKR0rweDD1746TbJF9fU9qKntkpmgubmJ+NGt85CXl6RMNl723/v2bGHD5KVP2ThCh0b7d26ZLbNeVYFjaH7yw3nIz0tGc1OvyEajnKRnX71xKubOKVQmmyEhAZk/+RfJrAbbW+GrrYa/rkbOZ/Lnv4jEiy9XJhszfz/6wTxMmZyDri6vjB2iHiHZ2EUrynD9FyYpk43rc8u3ZspoIRqUUR3ndvnlPX6mUsdxbVYsL5O1kruhpkvWkGvJNVU5uoyOwTVXjZNqTOq3mrqIjmOW96e3zhNiJVXQ5U6CfvxlgNEMOFsQ7mkGXO3QJWbBMP166BxqAqwEZ2tSl1GnUbdFdJxTdN73vzNb5pSrgik3H+nf+ymM2bkItDTCX1ct5Ep6ux2pN34DtumzlclGkqKffH+uzOnlPuNoH/alGvR6XHf1OKxYqiZLTtBB5t3A8Wmi42q7xI4LBkISwPzCtROVyaZBwyeBLny+uuh90N3djeTkZHR1dSEp6bMxiFn739zcjKysLOgHkACcK2BEkMREFSc7pISEmdaiwuS4GJwuA7Yr2nH4SKuUrY4qy8DoUelxIRvh9AWE4ZeTVBNNRnFUz7ZsZ7rnmlt7sfdgEzzeoDD+ThmfpSzqOxAkatm1u17KgMlWOH1artK5uH3BrD1ZQ5nVSk2xYvr0PKXO/cCyy9176iWTSlKUGdPzYhLeqNhv4YAf7n17I06qxQLblOkR5t840XEHDzXLeA4G6CaOz5LZkfEA6jgSOpEQKxowocMQLzqOY5sOHGqWDCZLCFmFc7bnMp7pniOp0t4DjdI2wGoRlu+rdKD7gmNowk2HgYBHxtTossdAZ1DnQPcFq5RIrNTR6RH9MXN6nrDZxgM47sqzZ6fMZtYnJsI2ZQYMKalxsd9Y/bP3QBPqGyNBwqmTcpCVqXYueRQ8nxEd1ylngI4rK9P+WWzt4ZT73x5cB6tdTSk34XE58b/fWnzOrd+nheaoao6qhn8SaHtOw3BC228ahhvantMwnND222cLzVH9dPCcp46qdvI0aNCgQYMGDRo0aNCgQUNcQQ0FqYZ+pY6bt9RI/wDJIKZOzpHSjIFzEVWVOm7ZViMltmQBnjA+C9On5cVFmSiJC7bvqJO5qqwJGD06A7Nn5secGTbc8AeC2LO3EQcONklJEJlX580pRGKiJW5KHVle6/EGkJ+XKH1vaWlqyLH6Qoge9h9B06ot8HX2wJ6fjdyLF8JRlId4QHVNJ7Zsqz1V+mvD7Fn58mzjAWF3O9ByCGF3B3QmG5A+BkgqiIsyUa7Xpi3V0rslZXFTckWXxIOOY6nj1q21qKiM6LiJE7MxbUpuXOi4oMuN1nVb0L2/XH5Pnjwe6YvmwGBXQ/LUF9Rru/c24MCBJmE8LS1Jw5w5BXFRCs9SR5Yk79nTcErHJWHBvKKzXgp/pjru6PE27NhdL/drVmaCEPLkZKvL0vQFS+C3bqtFp8zKtWPu7IK4KYXvra5Hw7sb4KprgjklEdnL5iJl0pi40HEkcozouEjpL+0k6rizXQp/JvB1dqPhvQ3oLq+A3mJGxpzJyFw4A3qTelvpXINqQqPweVoAq7T097e//S1eeuklHD58GDabDfPnz8fvf/97jBkz5vR3PB4Pfv7zn+OZZ56B1+vFxRdfjL/+9a/Izv6QLr26uhrf+973sHr1aiQkJODmm2+W/26j0RjXpb/7DzThnvu2CGlMZN5gZHD0rJkF+MH35ih1unhh3XHnJiHKiBxOztzWY8K4LPzkR/OVMWISzc1Oke3osbZTc8B0ciFwdMJPfzRfKfEOyaf+cs9m7NvfCL8/wp5I2UgK9ONb551VUoqP23N07u97YBs2ba6R3q1Tg/2QkWHHd789W9hEVSEUDKL8jw+h+rm3EXC6ooPeYElPwbh/uQWFn1uuTDbu/9ffPIIXXj4ojs2pyX1CzPL5q8bh81ePV2oshZv2Inz8XcDXZ7A8e95yp0M38mLo9GfH6ToTHbd3XyP+ev9WtLS6ZNWoRywWA+bMKhSCKo6TUgUG4P5y1yYh3WEAh0+QPVzsxf/JD+fL+AZVcNc2oPzffgvn4ePCcEroDAYkjBuFcf/zr7AV5CqTjWy/f75rkwTiyAzLs0CSuIL8JFm30rPIuv5xe4692txvdLao7+Rc6oDMDAdu/e5sTJmsbt24Vo8/tRcr11bA7QmcHr+ZkmzFzTdOxeIFxUp13EuvHMLLr5bD2RvVcTokJpiFdOfKy9U6hDUvv4/y2x6Et63z9N1gTLCj+EuXYdwvvgndWSrLPRMdt3N3Pe772za0ntJx3HBksJ8/j+SOsyUBoQod+w5jz6/+CGdlLYcgy3t6swkZ86Zh2m3/AnNy4rDKc66X/v6fB9YqL/39zS0XnHPr92mhNKS9du1a3HrrrdiyZQvef/99+P1+XHTRRejt/XDW2E9/+lO8/vrreP755+X79fX1uOaaa05/HgwGcfnll8Pn82HTpk147LHH8Oijj+Lf//3fEe+ZVDqpLS0uyWoVFSVL5JLG0abN1Xj+pYPKZKMTc9c9W1Bd3SUOFuUqLk6RGXV79zfikcd2Kb1Q73tgu2RSs7IcIhvXLjvbgWMn2nDP/VtPjzdRgSef3isXF6P3HCHBV25OotDX333vlpizaYcLr75ejrXrKpGQYD693woKkiTjda84E4Nn/A0Xal9+H5VPvCbjShJGlSBxVAkSyooRcHlw6Lf3o/PAUWWy7d7TgGee3y/OTFFBMooKU+RfgueUBEuqEO5pQPjY2xFiFnsm4MiK/Ks3AXXbgIZdSrMMPI9kw6QTw3XjeWDWbf3GKrz86iFlstGhueuvW4QFk+eTcvE8sLJgz94GPPbkbmWycYTP0f/9M7oPHoE1NweOkSPkxZ+79x/G0f/9i9LI+mNP7JY14loVRXVcbqKs5Z1/3RJzNu1w4aVXDsre4qgV3lnUc9x7bW0u3HPvVtmTqkAH9Z0PjovjUlTA8xCRzeX24+EndgvZjSps31mH5188II4W5ZJ7tTBJdN7Tz+4THagKrLI5+Lu/yV3AO0HuhlElkh08+cSr4sSqQktLr9ydvEN5l0btEd6xvGtfe+OwMtn8zl7s/T+3o/dkHRwlBUgcPUJelsx0NK/dhsO3P6xMNg0azhlH9Z133sHXvvY1TJgwAVOmTBEHk9nRnTt3yueMGjz00EO4/fbbsWzZMsyYMQOPPPKIOKR0bon33nsPhw4dwpNPPompU6fi0ksvxf/7f/8P99xzjziv8QqW+3JOaW5uQr8SOLvdBIfDhHXrT0r2RgVYFlpV3YmcnIR+TIm8YJlJ5ecs41MBsmCWH26ROa6MWkbBTDRZ9o4fb8eh8hZllxZLpZMTrf2YarmGXEuuGcuVVYAO8qrVFZLB4iUaRTTb29bmxsZNVUpko9Fd/fw78jMzqNHIPaPktoIcKV2qfXUlVGHVmgrJzjBQc1o2nQ7paXYJ6qxafUJpNhV+F2BNjWQaIsIBZjJO6hBu2Hl6ZuNwY+Pmasky5OUm9iuBI4uozWbEmnUnpfxRBXbuqpNqEerfvjqO54PBQp7TxqYeJbJ17zskDqk1J1uM8Sj4szUnS0qBu/eqCWRyTejUcI36ZsO5hlxLrikDdSrAvbR67UnYbcZ+TLXce3l5iZLVZ3mmCjB4+v6qCuj0QHKS5bQeoWzZWQ6pxFm7sRKqsHJVhcxhTh+g41htQ91HHagKta+tgr+rR+6CaOaUslnSUyWBySocVYGbDZuqJAiSO0DH8Y61WI1YubpCWXCaLTTOilrYi/Og71NdaHTYYE5LQcO7G+FuaFYi27kKJqVZxKfsFcZ5CfVNQn1Ax5RIS4uUDtFhZZZ1+fIPy/7Gjh2LoqIibN68WX7nv5MmTepXCszyYKbqDx5Ul5X8OLAnlbo1Vp8Wo8Ecz8EsnArU1nVJJLWvI9hXtl6Z7Rd5VirWjRcnHfqBYKk0e6f4HRVgCSHnuXKNBsJkNMjz5tqqAMeWdHV7kRBDNl6wNKBURfRphPRW1cOUMrgMiQaJwWZF5351GdXjJ9plvmsscBTMsePt6jJcPfWA3vihk9oX7FV1d0QcWQWoqukSsWL1abFfu7vHi6bmPuXKwwjO14y2WsSSLaLj1ATjXCerEfL5YvaiGhx2hLw++Y4K1NR0o9fli9lvz7XkOVB1N3AkDR2+WLLJHtSxTUiNbNzrrFiJ1cNLHce7lqXoKsBndvxEm+iyWOBde/x4G1Shc98RGKwfOvd9wTuDd4e/W40e4Z3JvRVTxyWYpUyeAWwVcJ6okRJplvrGWjd/jxPOiholsmnQcE6SKbEX4Cc/+QkWLFiAiRMjg4gbGxthNpuRktK/mZ9OKT+Lfqevkxr9PPpZLLDXla8o6NRGZYj0PH42/3t4AQz132e1GKDTsQtksIEbDIZhNOhgMuk/M3k+CcRBlSaV0KDLIRAMnpLNoEY2kwEMqjJLpKd31QehcDiSTFK1biY9jEadlKNzjfpCHBkdYDKfPdk+as+ZTDoYjDqEgqGYe45vcU+qWDeYjIDRgGAgiHAMY4TraUiwqZGNARCrAd09sdctGArBZjMrI1kI6S2RUGusP833dXp2cEN3Ftbu43Qc142fx1o37kOVOs5i1kvgKJaOi55fVbLpzGY5B7KfBvTFsSw4zMCS2azobtAL6ZScyQG9z+Go/lWk48xmHQwG6t/YZ5VP2WJVo+N4L3yUbHyfVQaqdJzVaoDb4x9SNn6uSjbqfu63mHdDIAj9qfsjpEzHSe3KYNlCIXnuJrNOydrprWaEdICweAxYu1AgCJ3JKFUawymbqj302aZUFaY1Q+dnSjVuHFX2qh44cAAbNmw463+LREv/9V//Nej9lpYWIW/6rA4kM8RUcrGa8EePsqK4yAS73T+o2T7U5sXIEUmwmL3SyD/cGFFswsgRZuj1filD7ovODh/GjbUjPT1CMjDcyMvVY/Qom5R1JyX1j04zml420orCQqMS2ZKTQpgwzoH2djfS0vobcexDGlHEdbWcNdk+as/xIp0+JVlIstLS+j9TlnwVFRgxbpxDyboRSZ9bgpb1O2HIS+9HjBHy+wFDGPaL5imTbcG8DKzb6ERKSqhf5JxVB2Q8XTQ/U5lsYVMxwv4mwGMi206fDxiR8wCppdC3MxD32WcHP07HjRllx4EDJjgSArD0qc6IGH4+jB6VDKPBjebmz0bnfhKMKDGjdIQZRqN/UHVGR4cX48c5kJaqRsf5y4oQnjIWzkAQppTk/p91dkE/ZSz8o4qVyMY1GT/Wjq5uzyAWXZfLH1nXEjU6zmgIY+qUJCk/Tkvrf6d6fUEUFRllT6o6q/NmpmL/oSakppj6FUCQZCkvR49pk5OUybZoQQY2bqqOqeMYOF+oUMfZL57PQwlfRko/ploGbfy6IDIXzUB7TzfA1zDrON6Zhw8bkZAYkCB6//9fPyaMZXmyC83Nbgw39NNGwzC5DF6LGcYEtoJ8CE9TK6yjp8CbnTKsz7WnR02VoIZzG3HhqP7gBz/AG2+8gXXr1qGgoOD0+zk5OeKQdHZ29suqNjU1yWfR72zbtq3ffx8/j34WC7/+9a/xs5/9rF9GtbCwEJmZmZ8p6y+jWPzvjKXg0tMzsHFzmzCw0hlkTwMvhLZ2l5TgfPXGScjJ6Z8pHi6Q4W7s2E68/c4xmC0B6anhhdXR4ZGo8DcumYDCAnUMsbNnOvH0c/ug0wWQkkJmTp2U2DDye+3nJ2BUWZEy2RYvmoAHHt6BuganGHKs7O7u8QnByLIlIzBl8sizxp74cXtu+YU63P6XjUKhn5Zmk4yR0+mT18wZ+Zg7e4yysRzWK1eg9+1NcK3ZAUtmBgxWMwLOXvjau5A6fTzGXnQBTElq2PYuuCABm7a0Yu/+LtlvNqtRnmdHp0dIUZZcMAEZGf0NgeFCOC0JYfcRoKsSMDkAAzOsAcDXAziSoRs9F7rELCX7LS2NOq4V23bUiY5LTDDBHwhLIIf67mtL+7dsDCco86hRHXjvg+OwWgIS9KKOa+9wS5n+lVeMR36+IobYrCwE581G1QN/R1hvgDk9cvf52jphCAVRfMuNyC8bqU7HLZ6ABx/eidp6J9JSbeLYdHd7ZRTMRcvLMHHiCHU6btlkYSTuq+PYRsOWjDmz8jF71uh+PcnDiQuXmrFjbxf2HHAiPdUqjk2vOyBlweNGZ2DhvHExW1qGA0svsGPzljbs3d+N1JRI/7HbHUBnlwclRcmi41SNMEtZsRjdL69Gx/rdMKemCNtv0OODt6UV9rxsjLtyBZKy1Oi41NR0bNjYih276qTUl3qNbP/UcdQpyy+cjOzssyPbx4EyO0dtQM1L7yFss8GcmiwM4p6WNhgsZoz72heQk58/rDJZreqY1DWcu1A6noZ/+oc//CFefvllrFmzBqNGjer3OSNZPGxPP/00rr32WnnvyJEj0qfK3tS5c+fi7bffxhVXXIGGhgZxsIi//e1v+OUvfymRIovFErfjadhk/8JLB7F2faVkA+kEkkHxumsmYMb04VUgsWaBvvHmEbz7/nFhtNORkCInAVddMQ4XLC5RO44jHBYD8823j8qoGoJEN5dcPBqXXTJa+ewysja/8no5aus49iKElGSbOKkcY3I2HcEzGxfSgOdfPIiKk+0SGKEDsWhBMb543aR+BCQqQGbfI3c+gY7dhxDy+WGwW5GzfD7G/PArsGadvbE+ZwL2iz/13D4Zt8IeaYvZiMmTsnH9FycLc6dKhH1OhE+uBFoPAwEvwJLMpCLoRiyFLunDwJ+K/cYsG9lEycTKsRfsyefsWeo4zitVCfazv/JaOVauOiFBhyjpzueuHIeFC4rV6rhQCPUvvon6516D5xThiTU3C3lfvAp5112hXP9u2Fgla1ff0CMOPp2b5ReOxNVXjlOu48hQyz3H6hHqODoPixYW44vXTlLmCEZBZvpnXzyAI8fa4A+EJOg1Z2Y+rr920qmgqzqQP+HpZ/fLaDWSxLEFaOqUHHz5S1OEEE0lPM1tOHLn42hcuRlBl0f6LtOmT8DoH30FKRP6243Dvd9I4kVWeJIRMiBCO650RBq+cN1ETJkUO1kyXAj6fDjxwPOoefFdGe2jM+iRUFqEkd+6DnmXLB52ec718TS/unc1LDZ142m8bid+/72l59z6ndOO6ve//3089dRTePXVV/vNTuWG4FxVgvNR33rrLWEE5oOhY0uQ+Zdg7wLZfvPy8vCHP/xB+lK/8pWv4Fvf+hZ+85vfnJEcqhzVKMjuS0OYFwNHOKh2tAY603S4DHodCouSJdsQL+BlWl0TIQAqLEhWOq9sIFjORVInKevKTRwWJ/BM9xyPPMvjmAHJzkpQOi8ylmy9VXXwdzlhy81U7qAOBAMjzDJwzbh28YSwtxvwdgFGG2BLP+vOzCfRccy4kTgpouOSlTpasZxpGunMtFH/qsq4xULQ44WrIsLGbS8tFlKZeAF1G/Uv/y3ITx4WJ/BM9xyd5yjxXk52ApKS4kvH0cEnYRcDrMxKxxOampxSoUTHOSvOdJy7qRWexlaYkhPgKM6PKx3HNaOOs1qMKIwzHSfESSdrpXQ6cVRxPxbg4YTmqH46eDVHVcEfH+IgcwQNx9YQ7Bn9+c9/LllVEiCR0fevf/1rv7LeqqoqcWiZlXU4HLj55pvxu9/9DsYzPIyqHVUNGj4LaHtOw3BC228ahhvantMwnND222cLzVH9dPCep46q0hTUmfjIrGnnTFS+hkJxcbFkXTVo0KBBgwYNGjRo0KDhswRdFnWpPSj92yoRP7WSGuIOh8qb8f7K4zh4qEXGNcyaVYAVF46UMi/VYI/l+ytPYPeeemE4nTI5B8svLMOoMvWlog2NPfhg1Qls3VYrjLpjx2RgxbKRmDRRbb8K0d7ukiHk7DETls4RqbhwaSlmzypQXqoU6HGi7Z330P7BagQ6OmAtLETaxcuRtuwC6BSVKkXBEsI1605i7bpKNLc4kZnhwAWLRmDpkhHKS85JIrZhUzVWr6mQEtbkJCsWLSzBhctKY85tHO5gZNPqLah56X10HTgqRCh5l16Awusuhi07A6px4GCTnFXqOqPRgNkz84UQKC9PfbSaczXZi8++cmLqlFxcdGEZSksjc8ZVor6+W2QjUVYgEMT4cVlyN0wYr4Ycqy/a2lzyTDdurobb5cfIkWlYtrQUs2ac/VLRjwPbfFauqsD6DZXCnMy7dOmSUiycXxRzpvpwggRxa9aexNr1J2Xma1ZmgvBRLFmsXseFAgHUv7kGNa98gN6KWpgzUlBw5TIUXnORMpK9vjqOd/2qNRU4ebJDSuDZ47582chBzNgqwJ5jnofDh1tgMhswd3ah9JPn5qjtO9ag4Zwo/Y0XaKW/g0GCpwcf3iEEKGQhZs8PWQAzM+34+U8WYsxodUYmDbe/3L1ZSJ7sp4aU0+kiy96t35uD2TPPHoHMx+FkZQduu2OD9BzbbCYhViDBgt1mxFdvmiZG8NnCx+059lf+7o/rcfJkuxgeJD1xuXxioF/zufH44nUTlRlyge4eVPzn/8C5Zx90JhP0FguCLjf7A5B+yQoU/eRW6AwGZU7qnXdvxpZttdI/TkZMGnWck8e99pMfzpf3VIDn8sFHduD9D06IwcQ95/MF4POFMH5cJv7l54vOWg/yx+03ynP8/mdw7L5nEPL6YEx0IOQPyHNNGjMCM+/5DzgK1REq0bB8+NGdojtoXEZ0nF96j3/xs4UoG6ku6LVzdz3uvmcLOrv66jgfUlJs+MH352LGNHWs68dPtOGPt2+QfjzuN56J6Bp+42szsGxJ6Vn72x+35xqbevC729ahqqoTFqsRJqMeLldA2H+/cO1EYYVXBfYw/uFP61FOh8Gkh9lMZl2/6NwVy0fiW1+fqYyfgvrsjjs3YcfOun46jmdi7uwC/OgH85Q5q3RS9//3Pah96X2Zn250OKR3O+z3IW32ZMy88/+DOTlRmY577oUDeOmVQxKw4VklSRvvjBEj0vCvv1iktM+XwaTHntgt+4znM3hKx+XlJIqOI+nTcOJcL/395d0rlZf+3vaDC8+59fu0OPc8KA1nHSSLefzJ3WLwkqQoPd2OzEwHCgqS0NrqwiOP75ILTBWB0sOP7kJXl1cICzIy7PIqLExCr8uHRx/bJYpYBXhpPfbkbnFSuW5ZmQ6kp9mFPIbr9fSz+9DS0gtVeP6lA6ioaJdsUXZ2goxvKBASKgNee+Mwjh1vUyZby8uvoWf3Xphzc2DJz4MpIx3WogIYk5PQ/u776Nq8VWnQhk5qerpNWGG5bvyX+277jjqsXluhTLZdu+vxwcoTSEw0Iz8/SWTLyUlETk4CDpY34423DiuTrbv8BE489AL0ZjMSyophzc6AvSAHjtJCdB+uwNG7n1RaWfDk3/dEiIAKkvrouGQ0Nffi0cd3n1FrytkAjdxHHt2JbudAHZcs7PCPPLZLvqMCXBPqf64R14prxrXjGpLF9smn9sjaqsIzz+1HZWWnnAUGHDhShbIxKPfyq4ekEkcVeBZ5Jql7eUZ5Viknz+4H0eoghUEbOqnpGf11HHUedd+6DZXKZGtesw21L38Ac1qyMNZas9PhKM6DrSAXbVv3ovLJ15TJxjvztTfK5Q7leeC68fnyjuVdyztXFWhrPPXMXnG2qTt4TmmT0DZpaHTi8Sf3KNNxGjR8EmiOqoZB2LGjTrKVNEL6ZtgYaaXBVFnZgaPHWpXItv9gE+obupGVZe8nG3+mEqYBtWtPpFRuuFFZ1Yljx9qEyXFgZJxzNjkvb9uOWiWydXS6sW17HZKSLYNYTcnuyIzIlq01SmQL+f1oe+8DGGw2yaT2hTEpEeFAEO2r1kKlo8rnyexRX9iskWwSy+VUYePmKnG2EhP7rxvZdRlBX7+hSplT0/D+Rvi7nbBk9Y/ak3HSnJGK5rXb4W5sUSIbzwIDckPpuOMV7Th+Qo1Ts2dfIxqbnMjO6i+bzHPMcqCxsUe+owJckxMVHbJGfXVcVP92dnokeKMCrW0uCdxQnw0so01NtcoYEZZoqgDP4Lr1VXImeTb7gmeXZ3jDpgi7swqsXXcSeoNOdFpfUOfp9PxcnaNa9+YahAMBmAZkTcmAbbDbUfvqSrlDVIB3JjP2A0cL8Y7lXUs9w7tXBbZur0VXt3fQjG+eWwYgaMPRZtGgId6hOaoaBoGKlYZHrJ4ZlgR5vUExSFSgs8Mts/FYNjUQ0fl9LLFSgc5ON7y+YMwy0KhRx3mNKsA1oWwDDRGCz5pVTa2KMiEhlwtBZy/09tj9PDqLGb7GJqhCS7MTFmvssmOrzSgGsqrIdHNz75BzKzmjkSMwGIRQAU9TKzd+zHJyo90m5Xve1g4lsrGkFroPz2Vf8PyymkSZjut0gwUrsZ6r2WRAKByW76iRzQOfN/AxOk6VbG65m2LJxj3IF8+qCtBJdrn9MfUvwWfNs6wC1F3sSaW+iAWOW2lWWAnkrm+CfoixTEa7Ff7uHgScap4r9xMd/Fg6juvJO1elPUKphtZx6uy4cxahOHidh9AcVQ2DkJpik8uLJC0Dwb4VlrmoGk6ekmqTvk8akgPB3hBC1VxQ9o9ZzAZZo4GIlkqnKlo3rgllc3sGOy181qEQkJFmVyKb3m6HIcGBEHtSYyDs9cGco46kJTMrAV5PZG8NhMcdkAy6qt7erCzH6X0/EG5PAA67aVjmW8YCS325sWI58QGXWzIiloxUJbKlJNuEhC1WCwPPLwNhynRcig20LWM9VxqXep1OvqNGNivMlkj/4tA6TpVsNrmbYsnGPcgXz6oKcI623WaKqX8JPmueZRWQTH2GQ/RFLHDeNrPlqmDLy0bI4435WcDlgSkpUUjaVID7KRSM7K2B4HryzlVpj1CqoXWcOjtOg4ZPAs1R1TAIM2fmC1sdo6h9FTAVHntUS0pSMXqUGjKlSROykZebhObm/lks/kx52R8yfaoagpaS4hSMGpUuUda+l0M0Yk02VrLrqgCNx9mz8tHd5ZUys75gVJXOzNw5hUpk4xDy9IuWI+h2I+T1DiJZ0hkNwvyrChcsKjlNtNMXNDr5/pILRiiTbcG8YikzY+/iQIeGmdRFC4uVkaDkrlggjJze5vZB5Ci+1g5kXTALtpxMJbLxLNBIaxlCx40amYaykWrYdadOzpEeRrYxDNRxlJdsnfyOCnBNykrTZI0G6jjqX67prJn5ypyG6dPyRJ8N1HFsZaGzOGe2Gv3LM7h4UbGcSZ7NvuDZ5RleOL8YqnDB4hHicA10pKnzwiF+XqJMtvzLlwjru6+rp9/7rMgIulwouPpCuUNUgHem3W4clJnk/uNdSz2jKnAzZ1YBkpMsaG3tnw3nuW1rc4sNR5tFg4Z4h+aoahiElGSrMNRazEbU1HQL3T8NpJrabul3+PpXpytjJ2QU8Btfm47kZAtqarrEAaTRRDkTHGaRbWAv4XBGpm++aRpycxNRU9slhhvXrrauW7LA139xkkSuVeEL10yU0RYcLdHU5ERbu0vkZLncVVeMVTraJ/PzVyFx2hT4GhrhrauHv7UNnuoaBLq6kXbxCiTPm6NMNo5noEHS3u5GfX2P/FtX3422VrcY5WeT5fTjQMOcowZ6enyore0W2djDyB7HCeOycMVlY5XJljRuJEZ+8zqEfD44j1dJKbCrthG9FTVIGluK0T+4SZlsJNm56Yap4iBQr/Gc8rxSp7A39OavTFOWJadT842bpyMpMaLjqN9obPJn9jN+/ebpyoIPXJOvfXWarBHlieo4riEZdrmmXFtVoI5lIFV0XPMpHVfTBb8/hM9fPX7YWU77gmeRZ7Kx0SlnVPRIXbec3RUXlmHaVHVMztRh1GXUaVy7vrqOuo+juFQha8lsFF6zHP72LjgrquFpakNvVT3ctY3ImDMVJTddpUw23plXXTFO7lDepdxvvFu5drxreeeqAvvvb7h+irAV8wz01XG5OQn46k1TlY9rOjfnqIYVvnBeQhtPo42nGRKcLUg2wgOHmmWOKrOBy+NkjirHwHDGK8kzZI7qlBwsXxafc1THjc3E8qWlZ32O6pnsOW2O6j9OhkJSJRInxfMcVRq+HNM0HHNUz2S/SaZtzVZUv/geug4eg9HBOaqL43aOKjNunHkcL3NU3//gOPacmqM6bUqezCqNlzmqnGNNwhaO5ZgwPkvmRp7tOapnsueic1Q3ba6WvtCRpWm4cOlIzJyRF1dzVLu7vcL6Gy9zVKnjVq/pP0eV1SKsKIm3OapsGci/cikKr70IpsQE5Tpu2/ZauVfjdY4qZSsvb1Y+R/VcH0/zi79wPI26ZIPX3Ys//vj8G0+jOaqao6rhnwTantMwnND2m4bhhrbnNAwntP322eJcd1R/fscHyh3VP/10+Tm3fp8W2snToEGDBg0aNGjQoEGDBg1xBbX1HBriHoFeF3pOVMvsw8RRxcpIC2KBpBTVNZE5YBxirbo8aSCZAmXjv/l5SULkES9gEQX7VMj8R/IpVayEQ8nWW1kLf5cTttzMCHNsHCEc9iGMAHQwQqeLn2caLetuaXXB4TDJnlNd5tgX3d0e6c2zWI1yVlX1uMcCS+DZc8x+Vco2cM6w6nJM9r7Fu45jO4gqdulYIGFMTV2k/z4nO0H6feNKx52slRnDtrwsWLPUt6v0BXssOWOYDPVZWWevrPYfAcu6SVaYmGAWLoh40nEcQ8O+aI5+4VmNJ9k4Himq44oKU+JKx2nQ8HGIn1tPQ1yBA7RPPPISqp9/G96Wduh0etiL8zDy69cg/6oLlSphXvTvfXAcb7515PR8t/Q0Oy69ZDQuu2S0ciN44+ZqvPLqISFRosFEcqplS0txzefGDznzcriwd18Dnn/xICpOtss8Wjo17Kf50nWTlDvTnQeO4ujdT6Bj1yGEfH6ZkZd94TyM/uFXlRtz4bAXgVATwnDyN3ZNQIcEGPXZ0OnUGsGcW/nMs/uwZVutBB9ohIwZnSHEMqrYufs6gc+9uB/rN1TB6fQJqVhJcSq+cO1ETFPEzt13JMgrr5dLzyANc6o0OvhXXzlOegZV6jjqjXfeO4a33jl6mrWTfdGXXToal1w0Srn+Xb+xCq++Vo66hh6EqeNSrNKT97mrxinXcXv2NeKFVw7iZHWnMNlSry2aX4QvfG6Ccme6Y99hHL3rSXTsLpc71mC3ImfFAoz50VdgzVDbe1xb14Wnn9mHfQeaJEDCoMjUKTn48pemIC93+PsZBwbhnnp2H7bvqIvoOJNeuB++/MXJ0oOs2gl89oUD2LipCs5en+jf0pKIjps8SQ07d99g/suvHsLK1SfQ1eUV26ggPwmfu3o8FswrUirbuQjqOr5U/v3zEVpYRUNMlP/xIRz582PwtnXBkpEGU2oSnCeqse8/7kLNi+8qle3V1w/joUd2SvSS2UC+yLb32BO78PyLB5TKtm5DJe65d4uQPSUkmpGaakVPrxfPPr8fDzy0I+a8teHC/gNNuP0vm3DwULMYbGlpVmHDfP2Nw/jL3ZuHnMc5HOg+ehK7fvw/aF6zDQab9ZRjqkP18+9g10/+Z9BoguFEOOxHIFSNEJjZ0p2K7+nkd3+oWrKsKo2kP96+Ae9+cBxhhJGWZoPVasDuPQ247fYNEpBQBWba7rpnM1597bAYvtxv3HeHj7Tgjjs3Ys/eCEmQCvAcPvz4Ljzz3H4huGH2iFm3qupO3Pu3bVizrhIqQePykcd2Ctt6VMcxKPfwo7vw0iuHlMq2Zt1J3Hv/NlTVdCIpquN6vHj6uX14+LFdSnUcndQ/37MFh4+2yQzhtFSbzNx+7c0juPv+bYPG1gwnuspPYOeP/gfN63ZAb7fCcir4Vv3sW9j549/A7+w/RmQ40dzsxB/+uB4bt1SLo5WebofBqJMA0x/+tE4ymarA8/n7P60XYkeCOo7zcnfsqBOZeWZVgXfmX+7ZjNffPCx3aXqaTeblHjzcgtvv3IT9B5uUycZz+ODDO8T2oAPNc0qbhLYJbRQSemnQcC5Ac1Q1DEL3sUrUvPQ+TMmJsOdni+NAtk5HSYEkk47/7VkEXG5l5TW8FHiZctYgy2z4YgkrI8DMQJCxUNWl9eJLB+XCIpsjLyzKxExIcooVGzZV4fiJdmWX1osvHxSDsrAwUopsNhvl0s/Mcgh78q496hyHyidfhauuCYllxTAnJ8JgtQizo6MkHx17DqPh7XXKZAuFOxCCBzpYIiW/0J/614IwPAiGO5TJtmlLNcqPtAiDI+f1cXxTQoJFnjGdnDfePKI0e79jVz0yMu1i+HK/cd8VFCRJdpX7UZVTU1nViXXrK4UhmeeT55RjrZhRJYvyS68eEudaVfaIz43rlZOTcFrH8Wc+3zfeOiJzQVWAa0IdxzXiWtmiOi7TIY4+15Rrq0zHvXZIAoMF+Ylw2KnjDFJtk5Fhx8499ZItVIWKR1+Cu6EFCWVFfXRcGuzF+ejYfRAN76xXJhsZ9KtruiTbxjPBdePcb57VqqpOYY1VhY0bq3HsWBvy8hIlc0/ZyGZeWJgswWpWVqkCA4J8ZWU55C6N6rjC/CRhdWZQSZWOO3a8Tdj9aXtEdRxtEtomtFFeEFtFXXBag4YzheaoahiE1k274e/phTl98DBoa04GXPXNaN91UFlWsLPTLZfCQJAKno7Yvn2Nyi4GjqZJTx8sG3tq3J6AsiwS52oeO9EmUdWBZYM2q0nKgHdz1I8CBNweNK/eAnNKEnQDmBUNZjN0Bj2aVm6EKgTDXaec0/7rxt/5figc6SFUAZbC8XnSeOsnm06HpGQLdu2pl6yrCnCvM4NF42igbDy/FSc7pG9KiWz7GuB2+2P2LtKxYZ/e0WOtynRcV48npo7jewzWceSEChw52oqm5l4JPAwEHRyuqSodxzLkk5WdSE+1DdJx3IP+QAh7FK2bv8eJlvU7YE5LHqzjLGYeCjSu3AJV2Li5Bja7adCIHP5OB2fj5iplsm3ZXiMlqwNLyvleYpIFO3ZFyoFVgMFdBm14h/YF9x/vWtoEPC8qsHdfI9zegNgeA0EbhbYK5dPwCcDSW9Wv8xCao6phEIJu9mvpYvZB6YwGhANBhDxqjF+vLyBDj2P1oUbf8/rURAlJ3MHeslhEBbKep3pGVIB/l/1ahiFIFHR6wKMogxTy+hAKhKA3xW6ZJ5FXwKkmgxQBywWH6gnk+yFlUXP2gHLGcSwYDXoJQHCWrwq43QHp+4wFGsA08HxeRefBG4R+CB3H8xsMhdTpEf7dsPgugxB9T6WO49oMpeOgUMcx28s9NRRRDMVTlSUPenwIBUPQGWP3yHJOdEBR6S91F9eF+iIWuJ5eRY4g4aaOG+KZUuZAIAx/QJGO8/hFjwwlG+0Blp6rAHUEJYul4wynZdMyqhriH5qjqmEQEkYW0etD0DvYGfV39cCU6EDCyEIlskWZL2mgDwSj+Yy68jsqwLIp9uD19MRYt0BQLowCRbJlZTqQnGSBM4ZsvLDCIaCkeHAGfTjAEnNHcV7MPlQaUUGPB8mTRkMVdLAhLM5qLITkc1XkNmUj0+DzxXaUe5w+ZGepYzwtLk6RoBL312DZvJKBY8m+CrCkkZ5LrNI3VmWwbJRMtkpky0+SDDkd/YHge/yM31EBaRuwm2WNBoJrqVLHRdl9ue8HQvZgOHJ/qAAzqfaCbPg7B1cQ8OyGPF6kKNJxfGZlI9PR64odfOZdW1amjsxuVFm6ONKxdBxbCHJzE5CgiAiQd2aQzy+mjvMhOTnSWqACLD/ms43lxDudXtjtZikD1vAJwD2o+nUeQnNUNQxC1uJZSBpTAldVHUL+QL9MKxmAsy6YhYQRhcouLbL9kdyhbzSQRhLJRsrK0jB+XKYS2dinNXdOoYziYKQ1CpY/NjY4padr9sx8JbKxl4zMwyyR4uUeBS/YhoZIufKC+cVKZONlWnTdJRL99bZ1nDZIwqEQ3LWNMCUnCdO0Khj0qVLkG4ZfCItENvk/PmOdfK4KFywegcREi5SqRo0lrh/LQ8kQuOLCkYPK+YYLZJXMzLCjvr6nnyHHUmS3K4Ali0coY5qeMS0fhQVJaGh09iPY4fkgA/CsGfni+KjA2DGZwtrMXvu+Oo4/8z1+Rh2oAjnZibI23F99yy25hg0NTukbnDEtT4lsdKCXLCoRx6pvuTv3Xn1Dj/Spzp+j5t7SGwwo+sKlCIdD8LZ39tNxrpoGmFOTkX/VMqjChctKYTYZ5F49LVs4jNZWlwSGly0pVarj2Hff3NzbTzaeU+KiC8uUBQoXzitGRppdymj76jjesXSuuW68e1Vg1swCsTloe/TVcbRN2D87b26hMidag4ZPAm08jYZBYM/M1N/+HLt/8Qf0HK+Sy1Q68owGZMyfhvH/+m1lsvFC+u4ts3DHnZtw9FgbQqdkY9nvqJHpuPU7c5QZ5sRXbpiKtna39Mm2BFzizrA0iPT+P7x1rrJLi+DYDV6omzbXoL39VCmtDshIt+N7356t9NIquOYi9FRUo+a5t9FzvPJUP2gY5rQUjPvlLUiZqDKjmgCDLhvBcDPCiGSSIgNqDNDrsqCDutENI0pS8c2vz8Cjj+9CbW33aclsViMuvXg0Llw2UplsaWl23Pq9Objnvm0yqini44eFcGTxwmJc87kJymQjOdEPvjcXd96zWWSj4UsbmCWG06bk4qs3TlUmG3XZ974zG3f8ZZOQr0V0HN/XY8yoDPlM5Xiam78yTUYiHTjYJGWXfKaUh47/j74/V9ZWFT5/1Tg0NjuxbUcdWk/pOK4UAybf+9YsYQFWhaLrLoaTOu6F9+BsrTrdTWBJT8WEX38byWPVnVUGHzhO5eVXy1FT86EeSUgw40tfmKR0lBSD01+/eToef2J3H9kiwdfLLxuNpQqdaAaneXfe9+D2iI7jjR+GOPcXLCrB1VeMUyYbq7t+8P05uPPuzRKoCYXDEc56owHTp+fjphumKJNNg4ZPAl1YJZd8nKC7uxvJycno6upCUtJnUwpB46K5uRlZWVliYJyLIAFE4/ub0HngmPQPps+ejMxFM4TgRjUYrdyxs04YT1m2Onp0hmQrVTqCUbDUZu/eRiFFYX8gnQlGL8lUeDZxJnuOUV+OpyHLL3tSGXGdP7dQnArVkEzg/iNoWr0Fvs4e2POykXPJIjgK1c7bjCIU9iAc7pZMqg4m6HRJ0OusiAcwo0oGYFYVsAyOhueoUeln1aE5Ux1HhlrKRkPOajGK0TthfJbSgFLfEuSt22plZAPlmTQxG1Mn5yifBRrNfHAEx+EjEVKnsWMyMJM6bgBxiwqwgmWP6LhGBIIhlJakYc6cgrjQcexTPXS4Bbv3NUrWl2XS82YXCCu2akgmcF9Ex/m7nLDlZyPvkkWwF6idtxlFZVWHnAcSFqam2jF3dgGKitS0hAwEg6ybt9SgpcUpGdZZM/PFiY0HHUem7k1balDfENFx06flYfy4LOUz3aM6jutGHcesOXXclCk5MBkN/xS29nDK/dPfvwuLVV1A3+vpxR2/uvicW79PC81R1RxVDf8k0PachuGEtt80DDe0PadhOKHtt88WmqP66eA9Tx1V7eRp0KBBgwYNGjRo0KBBg4a4gtajqhhsct+7vxG1tV1SMjJ5cg5yc9T1vA0sEy0/3IKKk+1SFkeSouKiFKX9Uf3KRA8eQ8duDtQGUiePQcqUsXEhG1FT2yWlv3y+ZD+dGCeljkRzsxO79zZIWVxebhKmTomPUkfC09qO5rXbhV3alpclxF5Gu/qSPcLZ68OuXfXSn8fB8yzvOtuljmcKPsvde+plZh9Lf2dMz5O5wvEAlsKzZzta+jt1Sq4ytt+YZaLlLVLyyPPJcxovpY7CeF19AoGqY/K7sXgUDEUj40bHdR+rRNu2fTKuLGlsKdJnTRo0I1QVemsa0LJxlxAAJpQWInP+NOhN6kumo6XwO2W+sR/ZWQ45DzwX8QCWiUZ0nEf0B4mx2KcaD2Ap/K7dDWhp6ZXZoNOp4+KgnDt6Vn2hIAKhkJxPi94AQ5ychVAwKOe058hJ6ExGZMydikROdtDwiUGCQr5U/v3zEfGhHc9T0Jm5+94tqKjokBITOlxkwbxoeRm+/KXJQ84OGw60tbtw971bxVFlPxJls9uMmD+vCN+4eYZSwgz2zu77j7vQvGabGCKEwWpBxoLpmPzfP4IlTZ2hybUisc3a9ZWRETq6yDw1MnX+8PtzkZWVoDTw8PyLB/Dm20eFnp6yGfQ6McxJLMNeWpUXfdWzb+Ho3U/C19556l2djK2Z9J8/RMYctcQPW7bV4NHHdwvrKg0RyksSqptvmiZnQiUOlTfjvr9tQ119z2kyj+Rkq5CjXHrxKKWOTVV1J+65d4v0R5ETiOtGw/eSi0YJSYvK4A2f5d1/3SI9oDLCIQzY7CYsnF+Mb9w8XQhRVCHkcsL13APwHd6LsC9C4KUzW2AeOwX2L94CvV2dHgl6vDj42/tQ/9Y6+J0uIWjRm01InTYeU3/7M9hys5Qa5dQhVU+9AX93j4wg0ukNSBpXiim/+RmSRpUok417/533j+GFlw8JazIXTneKaO8735yJ8WPVrRuxcVMVHntytzD/RpmeyJT89a9OFzZ7lWDQ9/4HtglLd5RMiTqOOuTiFaOUyuYPBdHt84qTSvJEEgE6dYDdYILDZFaqf111Tdjz6z+hc0+5THDgypkS7MIwPf5Xt8QF34gGDR+H+Aj5nIegEyPMtUdbZTQIZ89xth912quvl+PNt48ozTLcdc8W7NnbIHPpOHuOjI5k6/xgVQX+/uxeqMSB/6GRtBaGBDscI4vkZUxKQOP7G7H/P+6MOW9tuEBH8O13j0mQgc+Ta8fsGy/aP9+1OebcxuHC+x8cx/MvHZCgCOenUbb0dLsESu74y0YZq6MKTau2oPwPDyDQ64ajpAAJI4thL8xFb1U99vzqNjgra5XJdux4G+792za0d7ik2oHkLPyXWQeyPZLQSxUam3pkX9XVdyM72yF6hM+WRFmP/303Nm+tUSYbRzTccedGHDvRJvuM54Gy8Xi+9MohOSeqwEqHv9y1GXv3NcqsQ54Fymcy6uWcPPP8fmWyUX+5XngI3r1boLPaYMjOlxd/5nuuFx5WquOO3PUEqp59GzqzSWZuJ5QVCzt36+bd2P2rPyIUGDz/dbhQ9dTrOPG35yJB39Ii0SPWnAx07j+K3T//HfzddHTUYMu2Wjzx1N7TJHaF+cnIznTI2b3zr1vQ1KxONgak739gu+i0HOq4giTk5CQIOzx139FjEUIvFajn+ty9WZzUqI7LzU2UuemPPb4b23aouxvIpNvl84izatDpYGIm9ZRj2hvwwx1UdxaCPh92/eL3aNu6F+aMNDmnPK86k0kCOcfufVqZbOcsVM9QDZ+fGVXNUVWEbdtrUVXVKQo3Grln5I3lNkaTHu++d6zfLM7hxIGDzWJ8Z2U6hOI8KltSkkVKbtZvqEKrRF2HHz0nqtG0arNQ+puTE0UuvkxJCbBmZ6Bl024pCVYBRslXrq6QNWO0NxpJJRMxL/1jx1ul5FYF6CC/9e5RYSGk0xBlI+Tey8tLlLJMMgOqAI3uyr+/jqDbK+yXOkOkDJlZGseIArgbWlD76kqowvsrj8vcOTqn0SoH/pubkyDO2AerTiiTjWeRLJh5eUmny7f5bHl2/b4Q3nn3mDKnZsvWGlRXd0l5eVTHUba0NBsMRj3eff+YsHerANstjhxrlRLkKFM4zyvPrcNhwpq1J6XEWwWC9VXwl++BPilVMqdRHcef+Z6/fDeCDdXKSvNrX/lA9C0rV6I6zphgh60gBx27y9G6eY+yTG/lU29IeaM1O/10GbLBZpXgV8+xKjS8v1FdNvW9Y/AHQsjKcJzWvzyz+blJUs66fmMVVIHBmR6nT3RaPx2XmyC6b+VqdTpuzbqTUv2Qn594WsexEoPVSV5fAO+8c1SZjvMEA5JJNer0p88C/zXoImvoDviVydayfic69x+BvTAPRkekRFrKktNTJMhf8+J78HV0KZFNg4ZPAs1RVZilYSlmrN7AlGSrzOKMzEUcfpyoaJeMQ6xRLzTkenq8qKhoVyIbI+OBHhdMqYMZz5hVDbg88h0VYJ8bnVWu0UDQUA8Ewzh+vE2JbA0NPWhp7o0pW9QwOXxUTdQ80NOLrvLjMKUlD/qMxqbeapEeG1XYf7BZSvIHlnDxd4fdhP37m5QZI/sPNokOiTUGgYEl2ZPdkdLR4QYDM9HZpAPBLGZLq0uySSrAcxgMhGK2MCQlWcU4rzjZoUS2YE0Fwl4PdDHKe/le2OuW3lUV6D50Ar6ObpjTB7dXsJc8HAiIcawCzpO1cDe2SnZ3IDheLRwOoWPvYSWy8QxWVndKhdIg2fQ6GE0GHDjUpEQ26q59B5okQBNLx9ntRuzbp0Y2gvN6LRZDzBLaxEQLTpzskH5fFfAHg1LqG0s2zk8PhkMIKrobOg8cZXkcDLbBe45BJm97J7rK1QUgNGg4U2g9qopgMAzdtyCDmXWRC0wFJBgoVQaRQe59EXmP31Elmw5h3akSiBiXg449Ior63qKZj6GcFunlUrRueu43XeT5xQLfVjbzTS9X/dBEASE+U3VkT1yXIdcturaKwB7joZ9p5PzyOyrwUeMcOPtY6Xn4iL8rfWbUv6p6y/R6kWEosAuO31ECrhvXJcaek33IPadINt4N8siGcgzCOugV3Q3cS5FlG/qsqjqnH+oRDH03KNRx1CMfJZtKPRLPkHM45LpF7gaV9+o5CdooKgmNQlrpr4ZhxLixmeKsxip96+zwICc7AUWFgzNMwwGSOpgthggZ0ACwhyUl2YYxozKUyJY2Y6KU/PraooQ7H4JlLMYEh3xHBcpGpktZY0fH4F5Pl9svma9x49QQZkR6K5Nlb8UqC+ZFP2lCthLZTHxmsybB1941yJALB4MI+X3C/qsKZL7kWRgkWziMXpcPM6flKSPMIGNoIBCWvvKBsjGLQxIvVaydZAnnvvL5Bvdld3a6hUSGe1KVbDyPsXQcz0haqg2jytKVyGYsGQO9zYGwc3C2Oezsgt5mh3HEGCWypUwaA2tmKrwtg7PNAadLqh/SZqrRv+y/sxflwds6uNon6PVJADNt1mQlsiUmmjFmVLpk6gfqEZ7dYDAsZ1kFqLvIoOty+WLqOLc7gJkz8qEKZFf3+0MxdRzXc8L4rNMtSsMN8ylHL1YAggkHlgRHe1aHG2kzJkj7DM/lQHhb2mHNSkfKRLVEVBo0nAk0R1Wh8h07JhONjU6hhKeiY7ktR4fw4rjisrHKRoaMHpWO6VPzpPy4q9sTGZMQDAkbIMdgXLyiTMoKVcCen42Czy2X8SWe5jaEgyGEQyExTui85l66WBn1Oi/Lyy4ZLc+RPTVcs+hlyrLbiROzZfyFCrCn56orxkqJGfccZaRs7LGsb+hB2cg0zJldAFUY8ZWrYE5JhOtkrRiVch6cLjgrapAwohD5VyxRJtuKZSORmeGQPl7uf4IBJv6enmrH8gtHKpNt8cISITqrq+s57UzTMWxocMqYmssvHa3MiZ41s0B0SWNjj+yzqI5ranLKfrzy8rHKmM3Hjc0Sx6C11SUkYlEdx9+5fpdcPEqZg2/IyoV5+gKEersR6u4U/cYXfw71OuUzQ6Yap4ZBwuIbrkTQ65Xe8VCAjPBhCTJ5GpqRuXAG0qZPUCKb3mjEyK9fI1kiV01DhOU0HJa7wlVVh9Sp45C9dI4S2XgGL7tkDOwOE+obe2SPCWmW24+6hm4haFs0vxiqwDs9Lc2Ourru08Fz6jq2H5H5d/kyhTpuUYnwKFDHkUCJ4Prx3mIA4LJLRyuTzWIwwqjXIxAOi2PKZyp6TsbUAHbj4HLq4UL6nClInzsV7vomCeJTLp5Xd30zwv4ASm66CkaHXYlsGjR8EujCKukD4wTd3d1ITk5GV1cXkpIG9z7+IyCzanNzM7KysoYsgaPj98DDO7D/QCNcroCULmWk23D1lePE4VFJa97b68Mjj+/C1u210v9BUZKTrLhoRRmu+/wEpaNz6Mgc+ctjQurhbY+QAZhTkpB3+QUY94tvwmizqpMtGMIrr5XjrXeOyrw8Hi72MTJi/a2vzZD+t7OFj9tzPOok/nnxpYPSH8geaZvNKFlejkdQOTqHaFy5GYdvfwS9VXVyoXLkUPL4Mkz89+8jeaw6QynKivnwY7tQXdMp0X0SnhXmJ+FrX52uLBMdRXV1J/720A4cP9EGrzcolRpk7rzh+smYP/fsBW3ORMcxYPO3B7fj4KFmycwww0rD95qrx4suUanjGCB86JGd2LGzDr0uv5QQkqH70otH45rPjVc6OocjaVxvPgPfzg0Iu3pO9acmwjxzIeyXXS+jalSBrL7H//YcKp9+Hb7WTtErJFfKXjIHE/7tu+LMqtRxHHN14oHnxJGmg08imfRZkzHpP25VOjqHIAv308/vk2Ah7wpyFzBIyLuhqFDt/F4y0/PO50x36jiTSS+jyziqabyiSqAoON7qgYd2yEx36jijUSdVQjfeMBVzZhUo1XHBUAjdfq8w/0bMaZIp6WQ0jdVgVKrj6KAe+N/70Lx2G/w9kdFqlsw0cVLLvvWFYS/TPxu29nDK/eP/fhMWq0OZHF5PL/7y75efc+v3aaE5qgod1dOMp1WdcjmYLUZMGJcVNwO2ifqGbiEVoYHJcuV4GbBNuBuaI+QY4TBSJo+VbGu8gKRK0Rm0JSWpMv7ibONM9xwzbyTuYMSc5ZcjS9OUXqYDmTvbtu+Hr7Mb9rwsmc2oqudtIJgNJBt2R7tbHJpxLB81xkePD/XIkaOtaG7pFeInZu7P9hzQT6LjTp7sEOIk6jjKRhnjBbV1Xais7BQHn0EbktnFC4LtLQhUHZefjcVlMKRlIl7gbetA+86DElRKHluKhNKzP2vzTPccZ223bdsv+oRyJY0tjRsdx4zlwfJmCQBnZjqkJDheZOM84UPlLejsjJS/885XGZTuCwZWDx9pkSArpw9MiDMdFwiHJJOqPzWmRlmP+xDTEroPV0gpMEuCVc2a1xzVTwev5qiev1DpqGrQ8FlB23MahhPaftMw3ND2nIbhhLbfPluc647qj/7zDeWO6p3/ecU5t36fFtrJ06BBgwYNGjRo0KBBgwYNcQVtPM3/396dQFdVXQ0c35kJIQlhTCBAGMIog0xhkEHEotgFqEVEq4AUrUhFaLFqVRAKoohClYWCAtWqsKiKfICA8IEylRlklCkICGGQySSETOdb+9i8770MyBDy7kv+v7Wucu89776buL28/c4+5wgAALiW8b1r1x2RrVtP2HJRnRiww201JLryzRufiptfWrvzwBnZsOOE/HQuTSpEhUpCkxhpVMf7pcla+KdDadasPWKHI+nQizYJ1e2M7E4pTXYqnfPhxwUr5ezW3RIYGiIVO7SUKnd3lKBw785JAVwtElUAAHBVdGb6CW+uloOHztoJAHUs3patJ2TpsgMyZHAbado42tu3iGukEwJ9vGCPrNxwVNJ1IqVAf8k8kC1rtx2XLm2qS9+763ttrVJNUj+ft1s++2KXXErL/OXeMrNl9Zojclv76jL4iQSvrZDgdKdWbZLtL74laUln7PhUXSUhadk6OfrZUmkx6QWvTy7mc3SgpDcHSxopkfgqCgAAXFXSoLNf7z/wk0THlLHLqujSIdWqRcjZs5fk3Wkb7FJE8C3/2X5C/nf9UQktFSSx0eFSuUKYVK0cLiHBAbJs7Q+ycWeS1+5t565TNknVWbh1Ka6YGI23SImMDJFvV/0gS77+ZbIx5J3xd8eot+XymXNSpk4NCYuLda01fG7bHtn9+vvevkXgqpCoAgCAX3X06AW7jEm5cqEeM15raWh0dBm7Ru7Gzce8eo+4dt9uOmZLf8NzzcYdUSZEsrKMrPLif9NVqw/bnlSNOfcSZJ05PCDQT5b/70G71A88JS1bK6nHT0rp6lU9Zs7XnlVdoub0mi2SfJj/V+F8JKoAAOBXnTyVIpfSMvJdXihnrKC2gW/1kv94KllCS+U/EqxUqUA5djJZvOWHI+clJCT/0l6Nw5/Optol1+Ap5aj2gvuJfz5LqAWGl5HMlFRJPea9nnKflJ3t/a0EIlEFAAC/KiwsSAID/CU9PSvfhEcXuwsrHeSVe8P10V7KMqWDJCMz/w/BOllW7p7WohQRESIZGQXcW3qWBAcH3PT1VH1RUEQZu8Z8fitQZl9OF/+gICZUgk8gUQUAAL8qPr68HZd65qfUPB+Az51LkzJlgu1MrPAt7ZpVtV8+5E5WNUnVJLFt0xiv3VvbNtXtJDJpaZkex7XcV8dDt2tT3Sar8FSpUysJCg+zY1Td6f+3aUmnJDy+hkTeEu+1+/NFJtv7W0lEogoAAH6Vjkvt26ep7TU9euyiXLyYZpOF48cvStrlTPlt93pSpUrJWYi+uOjcKlbqVC8rSaeT5eyFS5JyKUN+On9Jks6kSN24KOnQItZr96aJaLNmMXLqdIqcPp0iKSnpcvbcJTl27KLUqF7WxhzyioiPkxoP/VayklMk5fCPknHhZ7l89rwkH/hBgstGSL2hj4p/AAk+nI96CQAAcFVatawqI4Z3kPkL9sr3+05LRkamxNWIkru6xUuXzrW8fXu4Djpp0jOPNJeF3ybK+u9O2C8ddMxql4Rq0r1jLa+W/uoY2eFPt5P/WbhXVn6TKMkp6XY5mru71ZWePepLpUqUrxak/tB+EhYbLYdnL5LUI8fFLyBAou9oK7X63yvlWzXx9u0BV4VEFQAAXLXGt1SWWxpVkvMX0uyaluWiQu3yIfBdZSNKycO/bSD33Rlvk8EyYcES6pCxnzpp0oMPNJF7ezaUCxfS7H5+E3rBk872W7333RJ7751y+fRZ8QsKlJDyUR6zJ+PqmWxjN2++f0nkjKcQAADwGfphN6psqLdvA4VMk1OnJKi56aRJ9KBeO//AQAmNqeTt2wCuC1+BAgAAAAAcxZlfmwEAAACAA/yyBJcXS39NySz9pUcVAAAAAOAo9KgCAAAAQEG0Q9Oba5kaKZHoUQUAAAAAOAqJKgAAAADAUSj9BQAAAICCZGf/snnz/UsgelQBAAAAAI5CogoAAAAAcBRKfwEAAACgACbb2M2b718S0aMKAAAAAHAUelQBAAAAoADG/LJ58/1LInpUAQAAAACOQqIKAAAAAHAUSn8BAAAAoCA6mZE3JzTKLpm1v/SoAgAAAAAchUQVAAAAAOAolP4CAAAAQEGyvVx+my0lEj2qAAAAAABHoUcVAAAAAApgso3dvPn+JRE9qgAAAABQzEyZMkXi4uKkVKlSkpCQIBs2bLhi+7lz50r9+vVt+8aNG8uiRYvEm0hUAQAAAKAYmTNnjgwfPlxGjhwpW7ZskaZNm0q3bt3k1KlT+bZfu3at9O3bVwYOHChbt26VXr162W3nzp3iLSSqAAAAAFAAY4zXt2v15ptvyqBBg2TAgAHSsGFDeffdd6V06dIyY8aMfNtPnjxZ7rrrLhkxYoQ0aNBAxowZI82bN5d33nlHvIVEFQAAAACKifT0dNm8ebN07drVdczf39/ur1u3Lt/X6HH39kp7YAtqXxSYTOm/35KoixcvFto1s7Oz5eeff7Y13hoYwM1GzKEoEW8oasQcihLxVrhyPmNfT8+gE6Snpzri/S/mylVCQkLsltuZM2ckKytLKleu7HFc9/fu3ZvveyQlJeXbXo97C4mqiH0QqWrVqnn7VgAAAIBi+5k7MjJSfEVwcLBER0fLzOn9vX0rUqZMmTy5io4/HTVqlBRXJKoiUqVKFTl69KiEh4eLn59foVxTv/HQYNLrRkREFMo1gSsh5lCUiDcUNWIORYl4K1zak6pJqn7m9iXao56YmGhLaZ3wO/TLlafk15uqKlSoIAEBAXLy5EmP47qviXd+9Pi1tC8KJKr/rdmOjY29KdfWhxsPOBQlYg5FiXhDUSPmUJSIt8LjSz2puZNV3XxJcHCwtGjRQpYvX25n7s0pZ9f9IUOG5Puatm3b2vPPPPOM69jXX39tj3sLiSoAAAAAFCPDhw+Xfv36ScuWLaV169YyadIkSUlJsbMAq0cffVSqVq0qr776qt0fOnSodOrUSSZOnCj33HOPzJ49WzZt2iTTpk3z2s9AogoAAAAAxUifPn3k9OnT8vLLL9sJkZo1ayaLFy92TZh05MgRj4nC2rVrJ5988om8+OKL8sILL0h8fLzMmzdPbrnlFq/9DCSqN4nWjOsA54Jqx4HCRsyhKBFvKGrEHIoS8YbiYMiQIQWW+q5cuTLPsd69e9vNKfyMr84TDQAAAAAollgYCgAAAADgKCSqAAAAAABHIVEFAAAAADhKiU1Up06dKk2aNHGtj6VrBH311Veu8zo71iOPPGIXuQ0LC5PmzZvLZ5995nGNuLg4u/Cu+zZ+/HjX+VGjRuU5r5teL8f06dOlQ4cOEhUVZbeuXbvKhg0bPN5HhxHrjF0xMTESGhpq2+zfv/+m/n5QfGPOnU49rudz1tjKQcz5PifF2/nz5+Wpp56y8aQTk9StW1cWLVrk0WbKlCn2/XStuoSEhDzPQTifk2JOl2GoV6+efX5Vq1ZNhg0bJmlpaR5tiDnfVhTxppYsWSJt2rSR8PBwqVixotx///1y+PDhPJPS6PX1+VanTh2ZNWtWnvsl3oDrYEqo+fPnm4ULF5p9+/aZ77//3rzwwgsmKCjI7Ny5056/8847TatWrcz69evNwYMHzZgxY4y/v7/ZsmWL6xo1atQwo0ePNidOnHBtycnJrvM///yzxzndGjZsaPr16+dq89BDD5kpU6aYrVu3mj179pj+/fubyMhIc+zYMVeb8ePH22Pz5s0z27dvNz169DA1a9Y0ly5dKrLfF4pPzOVITEw0VatWNR06dDA9e/b0OEfM+T6nxNvly5dNy5YtTffu3c3q1att3K1cudJs27bN1Wb27NkmODjYzJgxw+zatcsMGjTIlC1b1pw8ebLIfl8oPjH38ccfm5CQEPtvjbclS5aYmJgYM2zYMFcbYs73FUW8HTp0yMbS888/bw4cOGA2b95sOnbsaG699VaPNqVLlzbDhw83u3fvNm+//bYJCAgwixcvdrUh3oDrU2IT1fxERUWZ999/3/45LCzMfPjhhx7ny5UrZ6ZPn+7xgHvrrbeu+vr6wUy/G/j2228LbJOZmWnCw8PNP//5T7ufnZ1toqOjzYQJE1xtzp8/bx+cn3766TX9fHAeb8Wcxlm7du3se+sHPPdElZgrvrwRb1OnTjW1atUy6enpBb6udevW5qmnnnLtZ2VlmSpVqphXX331qt8bzuSNmNNY6tKli0c7TSLat2/v2ifmiqfCjre5c+eawMBAGx/uCbKfn5/rmfbss8+aRo0aebyuT58+plu3bq594g24PiW29NddVlaWLYFMSUmxpSM5i97OmTNHzp49K9nZ2fa8lg117tzZ47VaIlK+fHm59dZbZcKECZKZmVng+7z//vu25E1LfQuSmpoqGRkZUq5cObufmJhoy1e09DJHZGSkLRtZt25dIfz0KIkxN3r0aKlUqZIMHDgwz2uIueLHm/E2f/58+55a+quLjOvC4ePGjbP3pNLT02Xz5s0e8aYLkOs+8ea7vBlz+j4aUzmllYcOHbKl5t27d7f7xFzxc7PirUWLFjY2Zs6cad/jwoUL8tFHH9lYCQoKsm00ZtxjSXXr1s0VS8QbcANMCfbdd9/Zb9y0REPLHLWEJMe5c+fMb37zG/tNrX6bFhERYcuH3E2cONGsWLHClkZqr4GWcbiXFrnTkkn9pu+111674j09+eSTtvchp8RyzZo19h6OHz/u0a53797mgQceuIGfHiU15latWmVLfk+fPm33c/eoEnPFhxPirV69erY3/rHHHjObNm2yJXDaqzFq1Ch7/scff7T3sHbtWo/XjRgxwvZCwLc4IebU5MmTbRmovo++3x//+EfXOWKu+CiKeNOhCpUqVbLvoddq27atvXaO+Ph4M27cOI/X6H1o29TUVOINuAElOlHVsVP79++3H56ee+45U6FCBTt2QA0ZMsQ+QJYtW2ZLi/RDlT4E9aFYkA8++MA+DNPS0vKc++STT+y5pKSkAl+vJSD6l64+MHOQNBQv3o65ixcvmri4OLNo0SLXMRLV4svb8ZbzIa5atWq23Nz9w6GWlys+xBUvTog5TTwqV65sSzz12p9//rmNQR2LqIi54uNmx5uOWdVnmMaGjm395ptvTKdOncwdd9xhh8koElXg5inRiWpu+uB5/PHH7YB5fajkDMh3P//EE08U+Hptr6/bu3dvnnM6XqZXr14FvlbHA+oDdOPGjR7HdQIAvaZOtuROB/M//fTT1/DTwYmKOuY0jrS9fjOcs+lYG930z3ofxFzx5Y1nnMaNXtedflGi19EPmbpp7H3xxRcebR599FE7iRd8mzdi7rbbbjN/+ctfPI599NFHJjQ01I4NJOaKr8KOtxdffNFOBufu6NGjts26devsvk5IOHToUI82OmmS9uAq4g24foxRdaNjGC5fvmzHieaMIXAXEBBg2xRk27Zt9jU69i/3mL8VK1bkOx5Qvf766zJmzBhZvHixtGzZ0uNczZo17dTqy5cvdx27ePGirF+/3jUOA76rqGOufv36smPHDvu6nK1Hjx5y++232z/rMg7EXPHljWdc+/bt5cCBAx7X3bdvn12qJjg42G46Dsw93rSt7hNvvs8bMafvld/7KP2Cnpgrvgo73q4USznX0ZhxjyX19ddfu2KJeANugCmhtERESzh06notA9F97VVaunSpncmtTp069lsyndZcv5l744037Pmc8Q9awqEzxWk5ifZA/etf/zIVK1a035Dlpt/I6exu7qVv7suA6JTl//73vz2mR9cp+N3b6LiJL7/80t6rlmmyVIjvcUrM5Za79FcRc77PKfF25MgRO5O5luHpEhILFiyw473+/ve/u9rouFUdxzpr1iy7vIP2iGj8XWmoBJzHKTE3cuRIG3M6S7kuHaLvX7t2bY+hC8Sc7yuKeFu+fLl9zSuvvGKXwdHlaXQ2X50tWMt63Zen0VJeXWZQlxzMb3ka4g24diU2UdWJPfRBo0miPpi0HEQfbjn0gXTffffZD1T6AGrSpInHNOf6sEpISLDluqVKlTINGjSwYxRyj6PRMqPY2Fi7vld+9B70+4Lcm/5Fm0PHQbz00kt2zI0+6PRe9QMffItTYu5qElVizvc5Kd70A6FeS2NJJ4sbO3ZsngRD1x6sXr26vV8dt/Wf//ynUH8fKDkxl5GRYccjanKq19HxqYMHD/aYAEcRc76tqOJNv/DQdVN10iZ9Hy3X1YQ097joZs2a2XvRZ9zMmTPz3C/xBlw7P/3HjfTIAgAAAABQmBijCgAAAABwFBJVAAAAAICjkKgCAAAAAByFRBUAAAAA4CgkqgAAAAAARyFRBQAAAAA4CokqAAAAAMBRSFQBAAAAAI5CogoAKFb69+8vvXr1cu137txZnnnmGa/eEwAAuDYkqgBQwmVlZUm7du3kvvvu8zh+4cIFqVatmvztb3+74usPHDggAwYMkNjYWAkJCZGaNWtK3759ZdOmTeIEn3/+uYwZM6ZQrzlq1Chp1qxZoV4TAAD8PxJVACjhAgICZNasWbJ48WL5+OOPXcf/9Kc/Sbly5WTkyJEFvlaT0RYtWsi+ffvkvffek927d8sXX3wh9evXlz//+c839b4zMjKuqp3+DOHh4Tf1XgAAQOEiUQUASN26dWX8+PE2OT1x4oR8+eWXMnv2bPnwww8lODg439cYY2yZbXx8vKxatUruueceqV27tu1p1ORWr5Fjx44d0qVLFwkNDZXy5cvL448/LsnJya7z2dnZMnr0aFevrF5DE+cchw8fFj8/P5kzZ4506tRJSpUqZZNq7Q0ePny4lC1b1l732WeftfflLnfpb1xcnIwbN04ee+wxm8BWr15dpk2b5vGav/71r/Z3Urp0aalVq5a89NJLrsRYk/pXXnlFtm/fbu9JNz2mzp8/L3/4wx+kYsWKEhERYX9mbQcAAK4NiSoAwNIktWnTpvLII4/YRPLll1+2+wXZtm2b7Nq1y/ac+vvn/etEk0eVkpIi3bp1k6ioKNm4caPMnTtXli1bJkOGDHG1nTx5skycOFHeeOMN+e6772z7Hj16yP79+z2u+dxzz8nQoUNlz549to2+RpPEGTNmyOrVq+Xs2bO2R/fX6OtatmwpW7dulcGDB8uTTz4p33//veu8JrB6Xe0h1nubPn26vPXWW/Zcnz597M/cqFEjm9TrpsdU79695dSpU/LVV1/J5s2bpXnz5nLHHXfY+wIAANfAAADwX3v27NHuSNO4cWOTkZFxxbZz5syxbbds2XLFdtOmTTNRUVEmOTnZdWzhwoXG39/fJCUl2f0qVaqYsWPHeryuVatWZvDgwfbPiYmJ9r0mTZrk0SYmJsa8/vrrrn2959jYWNOzZ0/XsU6dOpmhQ4e69mvUqGF+//vfu/azs7NNpUqVzNSpUwv8GSZMmGBatGjh2h85cqRp2rSpR5tVq1aZiIgIk5aW5nG8du3a5r333rvi7wgAAHgKvJakFgBQvGnPpJa7JiYmyrFjx2yZbEFyl9gWRHs/tWc2LCzMdax9+/a23Fd7MbUc+Pjx4/aYO93PXTarvaDukz1pb2ZCQoLrWGBgoG3za/fWpEkT15+1dDc6Otr2hObQEuN//OMfcvDgQVuinJmZaUt5r0TvVdtqCbK7S5cu2esAAICrR+kvAMBau3atLW9dsGCBtG7dWgYOHHjFhE/HcKq9e/cW2T26J7s3IigoyGNfk1VNnNW6devk4Ycflu7du9vfhZYH68zH6enpV7ymJqkxMTG2JNp902R8xIgRhXLfAACUFCSqAABJTU21EyPpWM3bb79dPvjgA9mwYYO8++67Bb5GJzxq2LChHe+Zk+S504mFVIMGDWxvo45VzbFmzRo7rrVevXq2p7JKlSr2mDvd1+sXJDIy0iaG69evdx3Tnk8dG3qjCXuNGjVscqq9szpZ1A8//ODRRieY0omc3Ol41KSkJNurW6dOHY+tQoUKN3RPAACUNCSqAAB5/vnnbe+pzvyrtORXJzbSWXR1xt38aC/kzJkz7dI0HTp0kEWLFsmhQ4fsZEhjx46Vnj172nbaO6mz9Pbr10927twpK1assBM36aRNlStXtm20x/G1116zJbfaA6mTJmlvpE6cdCV6Xu953rx5tmdXJ0bKSZCvlyamR44csbMea8mulgDnnqBJfz9aHq33eObMGbl8+bJ07dpV2rZtK7169ZKlS5fa35smvZrwOmVNWQAAfAWJKgCUcN98841MmTLFJp06PjXHE088Ie3atbtiCbCWCGsSpr2GgwYNsr2nOluvzgY8adIk20avuWTJEjvzbatWreR3v/udnQn3nXfecV3n6aeftsvM6Gy6jRs3tkvTzJ8/3yaNV6LtNeHVJFiTRJ2t9957772h34fe/7Bhw+ysxNprrMmmLk/j7v7775e77rrL9j7rUjSffvqpTdw1We/YsaMMGDDAlkY/+OCDtjc2JyEHAABXx09nVLrKtgAAAAAA3HT0qAIAAAAAHIVEFQAAAADgKCSqAAAAAABHIVEFAAAAADgKiSoAAAAAwFFIVAEAAAAAjkKiCgAAAABwFBJVAAAAAICjkKgCAAAAAByFRBUAAAAA4CgkqgAAAAAARyFRBQAAAACIk/wfh6oRPX9k1u0AAAAASUVORK5CYII=", "text/plain": ["<Figure size 1000x800 with 2 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Visualize results for current site\n", "if not results_df.empty:\n", "    plt.figure(figsize=(10, 8))\n", "    \n", "    # Color by pile probability\n", "    scatter = plt.scatter(\n", "        results_df['x'], results_df['y'],\n", "        c=results_df['pile_probability'],\n", "        cmap='RdYlBu_r', s=30, alpha=0.7\n", "    )\n", "    \n", "    plt.title(f'DGCNN Pile Detection - {SITE_NAME}\\n{pile_count} piles detected')\n", "    plt.xlabel('X Coordinate')\n", "    plt.ylabel('Y Coordinate')\n", "    \n", "    # Add colorbar\n", "    cbar = plt.colorbar(scatter)\n", "    cbar.set_label('Pile Probability')\n", "    \n", "    plt.grid(True, alpha=0.3)\n", "    plt.tight_layout()\n", "    plt.savefig(f'dgcnn_{SITE_NAME}_results.png', dpi=300, bbox_inches='tight')\n", "    plt.show()\n", "else:\n", "    print(f\"No results to visualize for {SITE_NAME}\")\n"]}, {"cell_type": "markdown", "id": "dfafef67", "metadata": {"papermill": {"duration": 0.002627, "end_time": "2025-08-01T11:09:22.491746", "exception": false, "start_time": "2025-08-01T11:09:22.489119", "status": "completed"}, "tags": []}, "source": ["## Export Results\n"]}, {"cell_type": "code", "execution_count": 17, "id": "f0afd0b2", "metadata": {"execution": {"iopub.execute_input": "2025-08-01T11:09:22.497341Z", "iopub.status.busy": "2025-08-01T11:09:22.497228Z", "iopub.status.idle": "2025-08-01T11:09:22.504716Z", "shell.execute_reply": "2025-08-01T11:09:22.504490Z"}, "papermill": {"duration": 0.011165, "end_time": "2025-08-01T11:09:22.505436", "exception": false, "start_time": "2025-08-01T11:09:22.494271", "status": "completed"}, "tags": []}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Saved nortan_res results to dgcnn_nortan_res_detections.csv\n", "\n", "Summary for nortan_res:\n", "  site_name: nortan_res\n", "  total_locations: 880\n", "  pile_detections: 134\n", "  detection_rate: 0.152\n", "  avg_confidence: 0.21665114164352417\n", "  high_confidence_count: 147\n", "  model_loaded: True\n"]}], "source": ["# Export results for current site\n", "if not results_df.empty:\n", "    # Save to CSV\n", "    output_file = f\"dgcnn_{SITE_NAME}_detections.csv\"\n", "    results_df.to_csv(output_file, index=False)\n", "    print(f\"Saved {SITE_NAME} results to {output_file}\")\n", "    \n", "    # Create summary\n", "    summary = {\n", "        'site_name': SITE_NAME,\n", "        'total_locations': len(results_df),\n", "        'pile_detections': pile_count,\n", "        'detection_rate': pile_count/len(results_df),\n", "        'avg_confidence': avg_confidence,\n", "        'high_confidence_count': sum(results_df['pile_probability'] > 0.9),\n", "        'model_loaded': MODEL_LOADED\n", "    }\n", "    \n", "    print(f\"\\nSummary for {SITE_NAME}:\")\n", "    for key, value in summary.items():\n", "        if isinstance(value, float):\n", "            print(f\"  {key}: {value:.3f}\")\n", "        else:\n", "            print(f\"  {key}: {value}\")\n", "else:\n", "    print(f\"No results to export for {SITE_NAME}\")\n"]}, {"cell_type": "code", "execution_count": 18, "id": "80762bff", "metadata": {"execution": {"iopub.execute_input": "2025-08-01T11:09:22.512236Z", "iopub.status.busy": "2025-08-01T11:09:22.512120Z", "iopub.status.idle": "2025-08-01T11:09:22.614995Z", "shell.execute_reply": "2025-08-01T11:09:22.614735Z"}, "papermill": {"duration": 0.107283, "end_time": "2025-08-01T11:09:22.616049", "exception": false, "start_time": "2025-08-01T11:09:22.508766", "status": "completed"}, "tags": []}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Exported all detections to KML: output_runs/dgcnn_inference/nortan_res/dgcnn_nortan_res_all_detections.kml\n", "Exported pile detections to KML: output_runs/dgcnn_inference/nortan_res/dgcnn_nortan_res_pile_detections.kml\n", "Exported high confidence detections to KML: output_runs/dgcnn_inference/nortan_res/dgcnn_nortan_res_high_confidence.kml\n", "\n", "KML Export Summary:\n", "  All detections: 880 points\n", "  Pile detections: 134 points\n", "  High confidence: 147 points\n", "MLflow run completed\n", "\n", "DGCNN inference complete for nortan_res!\n", "Output directory: output_runs/dgcnn_inference/nortan_res\n"]}], "source": ["# Export results to KML format\n", "from shapely.geometry import Point\n", "import geopandas as gpd\n", "\n", "if not results_df.empty:\n", "    try:\n", "        # Create GeoDataFrame from results\n", "        geometry = [Point(xy) for xy in zip(results_df['x'], results_df['y'])]\n", "        \n", "        # Create GeoDataFrame\n", "        gdf = gpd.GeoDataFrame(results_df, geometry=geometry)\n", "        \n", "        # Set coordinate reference system (assuming local coordinates)\n", "        # You may need to adjust this based on your actual coordinate system\n", "        gdf.crs = \"EPSG:4326\"  # WGS84 - adjust if needed\n", "        \n", "        # Add additional attributes for better KML visualization\n", "        gdf['name'] = gdf.apply(lambda row: f\"Pile_{row.name}\" if row['prediction'] == 'PILE' else f\"NonPile_{row.name}\", axis=1)\n", "        gdf['description'] = gdf.apply(lambda row: f\"Confidence: {row['pile_probability']:.3f}\\nPrediction: {row['prediction']}\", axis=1)\n", "        \n", "        # Color coding based on confidence\n", "        gdf['confidence_level'] = pd.cut(gdf['pile_probability'], \n", "                                       bins=[0, 0.5, 0.8, 0.95, 1.0], \n", "                                       labels=['Low', 'Medium', 'High', 'Very High'])\n", "        \n", "        # Export all detections to KML\n", "        kml_all_file = Path(OUTPUT_DIR) / f\"dgcnn_{SITE_NAME}_all_detections.kml\"\n", "        gdf.to_file(kml_all_file, driver='KML')\n", "        print(f\"Exported all detections to KML: {kml_all_file}\")\n", "        \n", "        # Export only pile detections to separate KML\n", "        pile_gdf = gdf[gdf['prediction'] == 'PILE'].copy()\n", "        if not pile_gdf.empty:\n", "            kml_piles_file = Path(OUTPUT_DIR) / f\"dgcnn_{SITE_NAME}_pile_detections.kml\"\n", "            pile_gdf.to_file(kml_piles_file, driver='KML')\n", "            print(f\"Exported pile detections to KML: {kml_piles_file}\")\n", "            \n", "            # Log KML files to MLflow\n", "            mlflow.log_artifact(str(kml_piles_file))\n", "        \n", "        # Export high confidence detections (>0.9) to separate KML\n", "        high_conf_gdf = gdf[gdf['pile_probability'] > 0.9].copy()\n", "        if not high_conf_gdf.empty:\n", "            kml_high_conf_file = Path(OUTPUT_DIR) / f\"dgcnn_{SITE_NAME}_high_confidence.kml\"\n", "            high_conf_gdf.to_file(kml_high_conf_file, driver='KML')\n", "            print(f\"Exported high confidence detections to KML: {kml_high_conf_file}\")\n", "            \n", "            # Log to MLflow\n", "            mlflow.log_artifact(str(kml_high_conf_file))\n", "        \n", "        # Log main KML to MLflow\n", "        mlflow.log_artifact(str(kml_all_file))\n", "        \n", "        print(f\"\\nKML Export Summary:\")\n", "        print(f\"  All detections: {len(gdf)} points\")\n", "        print(f\"  Pile detections: {len(pile_gdf)} points\")\n", "        print(f\"  High confidence: {len(high_conf_gdf)} points\")\n", "        \n", "    except Exception as e:\n", "        print(f\"Error exporting to KML: {e}\")\n", "else:\n", "    print(\"No results available for KML export\")\n", "\n", "\n", "# Close MLflow run\n", "mlflow.end_run()\n", "print(\"MLflow run completed\")\n", "\n", "print(f\"\\nDGCNN inference complete for {SITE_NAME}!\")\n", "print(f\"Output directory: {OUTPUT_DIR}\")"]}, {"cell_type": "code", "execution_count": 19, "id": "72e057a7", "metadata": {"execution": {"iopub.execute_input": "2025-08-01T11:09:22.622327Z", "iopub.status.busy": "2025-08-01T11:09:22.622067Z", "iopub.status.idle": "2025-08-01T11:09:22.628382Z", "shell.execute_reply": "2025-08-01T11:09:22.628129Z"}, "papermill": {"duration": 0.010208, "end_time": "2025-08-01T11:09:22.629162", "exception": false, "start_time": "2025-08-01T11:09:22.618954", "status": "completed"}, "tags": []}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Prediction Distribution Analysis:\n", "Min probability: 0.0000\n", "Max probability: 1.0000\n", "Mean probability: 0.2167\n", "Std probability: 0.3811\n", "Detections > 0.5: 180 (20.5%)\n", "Detections > 0.7: 170 (19.3%)\n", "Detections > 0.8: 160 (18.2%)\n", "Detections > 0.9: 147 (16.7%)\n", "Detections > 0.95: 134 (15.2%)\n", "\n", "Top 10 predictions:\n", "          x            y  pile_probability prediction\n", "385726.4523 3529370.3306               1.0       PILE\n", "385731.4523 3529185.3306               1.0       PILE\n", "385731.4523 3529325.3306               1.0       PILE\n", "385731.4523 3529440.3306               1.0       PILE\n", "385736.4523 3529220.3306               1.0       PILE\n", "385736.4523 3529240.3306               1.0       PILE\n", "385736.4523 3529335.3306               1.0       PILE\n", "385741.4523 3529235.3306               1.0       PILE\n", "385741.4523 3529240.3306               1.0       PILE\n", "385741.4523 3529340.3306               1.0       PILE\n", "\n", "Model loaded successfully: True\n"]}], "source": ["# %%\n", "# Diagnostic: Check model predictions in detail\n", "if not results_df.empty:\n", "    print(\"Prediction Distribution Analysis:\")\n", "    print(f\"Min probability: {results_df['pile_probability'].min():.4f}\")\n", "    print(f\"Max probability: {results_df['pile_probability'].max():.4f}\")\n", "    print(f\"Mean probability: {results_df['pile_probability'].mean():.4f}\")\n", "    print(f\"Std probability: {results_df['pile_probability'].std():.4f}\")\n", "    \n", "    # Check distribution at different thresholds\n", "    for threshold in [0.5, 0.7, 0.8, 0.9, 0.95]:\n", "        count = sum(results_df['pile_probability'] > threshold)\n", "        print(f\"Detections > {threshold}: {count} ({count/len(results_df)*100:.1f}%)\")\n", "    \n", "    # Show top 10 predictions\n", "    print(\"\\nTop 10 predictions:\")\n", "    top_preds = results_df.nlargest(10, 'pile_probability')[['x', 'y', 'pile_probability', 'prediction']]\n", "    print(top_preds.to_string(index=False))\n", "else:\n", "    print(\"No results to analyze\")\n", "\n", "print(f\"\\nModel loaded successfully: {MODEL_LOADED}\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}, "papermill": {"default_parameters": {}, "duration": 254.95148, "end_time": "2025-08-01T11:09:23.454857", "environment_variables": {}, "exception": null, "input_path": "02_dgcnn_inference.ipynb", "output_path": "02_dgcnn_inference_nortan_res_executed.ipynb", "parameters": {"OUTPUT_DIR": "output_runs/dgcnn_inference/nortan_res", "POINT_CLOUD_PATH": "../../../../../data/raw/nortan_res/pointcloud/Block_11_2m.las", "RUN_NAME": "inference_nortan_res", "SITE_NAME": "nortan_res"}, "start_time": "2025-08-01T11:05:08.503377", "version": "2.6.0"}}, "nbformat": 4, "nbformat_minor": 5}