{"cells": [{"cell_type": "markdown", "id": "header", "metadata": {}, "source": ["# Multi-Site DGCNN Inference Runner\n", "\n", "This notebook uses Papermill to execute the DGCNN inference notebook across multiple construction sites with different point cloud data sources.\n", "\n", "**Workflow:**\n", "1. Configure multiple sites with their point cloud paths\n", "2. Execute inference notebook for each site using Papermill\n", "3. Generate separate executed notebooks for each site\n", "4. Provide summary of results\n", "\n", "**Author**: <PERSON><PERSON><PERSON>  \n", "**Date**: July 2025  \n", "**Project**: As-Built Foundation Analysis - Multi-Site Inference"]}, {"cell_type": "code", "execution_count": 8, "id": "imports", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Imports completed successfully\n"]}], "source": ["import os\n", "import sys\n", "import papermill as pm\n", "from pathlib import Path\n", "from datetime import datetime\n", "import pandas as pd\n", "\n", "print(\"Imports completed successfully\")"]}, {"cell_type": "code", "execution_count": 9, "id": "configuration", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Configured 2 sites for inference:\n", "  1. althea_rpcs: Althea RPCS site with Point_Cloud.las\n", "  2. nortan_res: Nortan RES site with Block_11_2m.las\n"]}], "source": ["# Site configurations for inference\n", "SITE_CONFIGS = [\n", "    {\n", "        \"site_name\": \"althea_rpcs\",\n", "        \"point_cloud_path\": \"../../../../../data/raw/althea_rpcs/pointcloud/Point_Cloud.las\",\n", "        \"description\": \"Althea RPCS site with Point_Cloud.las\"\n", "    },\n", "    {\n", "        \"site_name\": \"nortan_res\",\n", "        \"point_cloud_path\": \"../../../../../data/raw/nortan_res/pointcloud/Block_11_2m.las\",\n", "        \"description\": \"Nortan RES site with Block_11_2m.las\"\n", "    }\n", "]\n", "\n", "\n", "# Base parameters for all runs\n", "BASE_PARAMETERS = {\n", "    \"MODEL_PATH\": \"best_dgcnn.pth\",\n", "    \"DWG_PATH\": \"\",\n", "    \"CONFIDENCE_THRESHOLD\": 0.95,\n", "    \"BATCH_SIZE\": 16,\n", "    \"GRID_SPACING\": 5.0,\n", "    \"PATCH_SIZE\": 3.0,\n", "    \"NUM_POINTS\": 128,\n", "    \"EXPERIMENT_NAME\": \"dgcnn_inference\"\n", "}\n", "\n", "print(f\"Configured {len(SITE_CONFIGS)} sites for inference:\")\n", "for i, config in enumerate(SITE_CONFIGS, 1):\n", "    print(f\"  {i}. {config['site_name']}: {config['description']}\")"]}, {"cell_type": "code", "execution_count": 10, "id": "validation", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Validating file paths...\n", "All paths validated successfully\n", "Output directory ready\n"]}], "source": ["def validate_paths():\n", "    \"\"\"Validate that required files exist before execution\"\"\"\n", "    issues = []\n", "    \n", "    # Check if base notebook exists\n", "    base_notebook = \"02_dgcnn_inference.ipynb\"\n", "    if not Path(base_notebook).exists():\n", "        issues.append(f\"Base notebook not found: {base_notebook}\")\n", "    \n", "    # Check if model exists\n", "    model_path = BASE_PARAMETERS[\"MODEL_PATH\"]\n", "    if not Path(model_path).exists():\n", "        issues.append(f\"Model file not found: {model_path}\")\n", "    \n", "    # Check point cloud files\n", "    for config in SITE_CONFIGS:\n", "        pc_path = config[\"point_cloud_path\"]\n", "        if not Path(pc_path).exists():\n", "            issues.append(f\"Point cloud not found for {config['site_name']}: {pc_path}\")\n", "    \n", "    return issues\n", "\n", "# Validate paths before starting\n", "print(\"Validating file paths...\")\n", "validation_issues = validate_paths()\n", "\n", "if validation_issues:\n", "    print(\"Validation failed:\")\n", "    for issue in validation_issues:\n", "        print(f\"  - {issue}\")\n", "    print(\"\\nPlease fix these issues before proceeding.\")\n", "else:\n", "    print(\"All paths validated successfully\")\n", "    \n", "# Create output directory if it doesn't exist\n", "os.makedirs(\"output_runs/dgcnn_inference\", exist_ok=True)\n", "print(\"Output directory ready\")"]}, {"cell_type": "code", "execution_count": 11, "id": "execution_functions", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Inference runner ready.\n"]}], "source": ["import os\n", "\n", "def run_site_inference(site_config):\n", "    \"\"\"Run inference notebook for a given site using papermill shell command.\"\"\"\n", "    site = site_config[\"site_name\"]\n", "    input_nb = \"02_dgcnn_inference.ipynb\"\n", "    output_nb = f\"02_dgcnn_inference_{site}_executed.ipynb\"\n", "    output_dir = f\"output_runs/dgcnn_inference/{site}\"\n", "    \n", "    print(f\"\\n--- Running inference: {site} ---\")\n", "    \n", "    cmd = (\n", "        f'papermill {input_nb} {output_nb} '\n", "        f'-p SITE_NAME \"{site}\" '  \n", "        f'-p POINT_CLOUD_PATH \"{site_config[\"point_cloud_path\"]}\" '\n", "        f'-p OUTPUT_DIR \"{output_dir}\" '\n", "        f'-p RUN_NAME \"inference_{site}\" '\n", "        '--log-output --kernel pytorch-geo-dev'\n", "    )\n", "\n", "    try:\n", "        if os.system(cmd) == 0:\n", "            print(f\"✓ Completed: {site}\")\n", "            return True, None\n", "        else:\n", "            msg = f\"Papermill failed for {site}\"\n", "            print(msg)\n", "            return False, msg\n", "    except Exception as e:\n", "        print(f\"Exception: {e}\")\n", "        return False, str(e)\n", "\n", "print(\"Inference runner ready.\")"]}, {"cell_type": "code", "execution_count": 12, "id": "execute_sites", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Starting multi-site inference...\n", "==================================================\n", "\n", "--- Running inference: althea_rpcs ---\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Input Notebook:  02_dgcnn_inference.ipynb\n", "Output Notebook: 02_dgcnn_inference_althea_rpcs_executed.ipynb\n", "Executing notebook with kernel: pytorch-geo-dev\n", "Executing Cell 1---------------------------------------\n", "Ending Cell 1------------------------------------------\n", "Executing Cell 2---------------------------------------\n", "DGCNN Multi-Site Inference Pipeline\n", "Site Name: northan_res\n", "\n", "Ending Cell 2------------------------------------------\n", "Executing Cell 3---------------------------------------\n", "Ending Cell 3------------------------------------------\n", "Executing Cell 4---------------------------------------\n", "/Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages/torchvision/io/image.py:14: UserWarning: Failed to load image Python extension: 'dlopen(/Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages/torchvision/image.so, 0x0006): Library not loaded: @rpath/libjpeg.9.dylib\n", "  Referenced from: <EB3FF92A-5EB1-3EE8-AF8B-5923C1265422> /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages/torchvision/image.so\n", "  Reason: tried: '/Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages/torchvision/../../../libjpeg.9.dylib' (no such file), '/Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages/torchvision/../../../libjpeg.9.dylib' (no such file), '/Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/lib-dynload/../../libjpeg.9.dylib' (no such file), '/Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/bin/../lib/libjpeg.9.dylib' (no such file)'If you don't plan on using image functionality from `torchvision.io`, you can ignore this warning. Otherwise, there might be something wrong with your environment. Did you have `libjpeg` or `libpng` installed before building `torchvision` from source?\n", "  warn(\n", "\n", "Using device: cpu\n", "\n", "Ending Cell 4------------------------------------------\n", "Executing Cell 5---------------------------------------\n", "Output directory created: output_runs/dgcnn_inference/althea_rpcs\n", "MLflow experiment initialized\n", "\n", "Ending Cell 5------------------------------------------\n", "Executing Cell 6---------------------------------------\n", "Ending Cell 6------------------------------------------\n", "Executing Cell 7---------------------------------------\n", "Ending Cell 7------------------------------------------\n", "Executing Cell 8---------------------------------------\n", "Ending Cell 8------------------------------------------\n", "Executing Cell 9---------------------------------------\n", "Ending Cell 9------------------------------------------\n", "Executing Cell 10--------------------------------------\n", "Ending Cell 10-----------------------------------------\n", "Executing Cell 11--------------------------------------\n", "Ending Cell 11-----------------------------------------\n", "Executing Cell 12--------------------------------------\n", "Loaded 52,862,386 points from LAS\n", "Point cloud bounds:\n", "  X: 599595.18 to 599866.15\n", "  Y: 4334366.65 to 4334660.84\n", "\n", "  Z: 238.63 to 259.18\n", "\n", "Ending Cell 12-----------------------------------------\n", "Executing Cell 13--------------------------------------\n", "Ending Cell 13-----------------------------------------\n", "Executing Cell 14--------------------------------------\n", "Limiting to 1000 grid points for testing\n", "Created 1000 analysis locations\n", "\n", "Ending Cell 14-----------------------------------------\n", "Executing Cell 15--------------------------------------\n", "Ending Cell 15-----------------------------------------\n", "Executing Cell 16--------------------------------------\n", "Loaded DGCNN model from best_dgcnn.pth\n", "Model has 1,276,034 parameters\n", "Testing model with dummy data...\n", "\n", "Dummy test - Output shape: torch.Size([1, 2])\n", "Dummy test - Probabilities: [5.804399e-10 1.000000e+00]\n", "Model output might be problematic: pile_prob=1.0000\n", "\n", "Ending Cell 16-----------------------------------------\n", "Executing Cell 17--------------------------------------\n", "Ending Cell 17-----------------------------------------\n", "Executing Cell 18--------------------------------------\n", "Ending Cell 18-----------------------------------------\n", "Executing Cell 19--------------------------------------\n", "Running DGCNN inference on 1000 locations...\n", "\n", "DGCNN Results for althea_rpcs:\n", "  Total locations analyzed: 686\n", "  Pile detections: 63 (9.2%)\n", "  Average confidence: 0.151\n", "  High confidence (>0.9): 68\n", "\n", "Ending Cell 19-----------------------------------------\n", "Executing Cell 20--------------------------------------\n", "Ending Cell 20-----------------------------------------\n", "Executing Cell 21--------------------------------------\n", "<Figure size 1000x800 with 2 Axes>\n", "Ending Cell 21-----------------------------------------\n", "Executing Cell 22--------------------------------------\n", "Ending Cell 22-----------------------------------------\n", "Executing Cell 23--------------------------------------\n", "Saved althea_rpcs results to dgcnn_althea_rpcs_detections.csv\n", "\n", "Summary for althea_rpcs:\n", "  site_name: althea_rpcs\n", "  total_locations: 686\n", "  pile_detections: 63\n", "  detection_rate: 0.092\n", "  avg_confidence: 0.15127423405647278\n", "  high_confidence_count: 68\n", "  model_loaded: True\n", "\n", "Ending Cell 23-----------------------------------------\n", "Executing Cell 24--------------------------------------\n", "Exported all detections to KML: output_runs/dgcnn_inference/althea_rpcs/dgcnn_althea_rpcs_all_detections.kml\n", "Exported pile detections to KML: output_runs/dgcnn_inference/althea_rpcs/dgcnn_althea_rpcs_pile_detections.kml\n", "Exported high confidence detections to KML: output_runs/dgcnn_inference/althea_rpcs/dgcnn_althea_rpcs_high_confidence.kml\n", "\n", "KML Export Summary:\n", "  All detections: 686 points\n", "  Pile detections: 63 points\n", "  High confidence: 68 points\n", "MLflow run completed\n", "\n", "DGCNN inference complete for althea_rpcs!\n", "Output directory: output_runs/dgcnn_inference/althea_rpcs\n", "\n", "Ending Cell 24-----------------------------------------\n", "Executing Cell 25--------------------------------------\n", "Prediction Distribution Analysis:\n", "Min probability: 0.0000\n", "Max probability: 1.0000\n", "Mean probability: 0.1513\n", "Std probability: 0.3197\n", "Detections > 0.5: 95 (13.8%)\n", "Detections > 0.7: 87 (12.7%)\n", "Detections > 0.8: 78 (11.4%)\n", "Detections > 0.9: 68 (9.9%)\n", "Detections > 0.95: 63 (9.2%)\n", "\n", "Top 10 predictions:\n", "           x            y  pile_probability prediction\n", "599617.67945 4.334414e+06               1.0       PILE\n", "599622.67945 4.334484e+06               1.0       PILE\n", "599627.67945 4.334444e+06               1.0       PILE\n", "599627.67945 4.334499e+06               1.0       PILE\n", "599627.67945 4.334519e+06               1.0       PILE\n", "599627.67945 4.334594e+06               1.0       PILE\n", "599632.67945 4.334399e+06               1.0       PILE\n", "599637.67945 4.334404e+06               1.0       PILE\n", "599647.67945 4.334374e+06               1.0       PILE\n", "599647.67945 4.334424e+06               1.0       PILE\n", "\n", "Model loaded successfully: True\n", "\n", "Ending Cell 25-----------------------------------------\n"]}, {"name": "stdout", "output_type": "stream", "text": ["✓ Completed: althea_rpcs\n", "\n", "--- Running inference: nortan_res ---\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Input Notebook:  02_dgcnn_inference.ipynb\n", "Output Notebook: 02_dgcnn_inference_nortan_res_executed.ipynb\n", "Executing notebook with kernel: pytorch-geo-dev\n", "Executing Cell 1---------------------------------------\n", "Ending Cell 1------------------------------------------\n", "Executing Cell 2---------------------------------------\n", "DGCNN Multi-Site Inference Pipeline\n", "Site Name: northan_res\n", "\n", "Ending Cell 2------------------------------------------\n", "Executing Cell 3---------------------------------------\n", "Ending Cell 3------------------------------------------\n", "Executing Cell 4---------------------------------------\n", "/Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages/torchvision/io/image.py:14: UserWarning: Failed to load image Python extension: 'dlopen(/Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages/torchvision/image.so, 0x0006): Library not loaded: @rpath/libjpeg.9.dylib\n", "  Referenced from: <EB3FF92A-5EB1-3EE8-AF8B-5923C1265422> /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages/torchvision/image.so\n", "  Reason: tried: '/Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages/torchvision/../../../libjpeg.9.dylib' (no such file), '/Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages/torchvision/../../../libjpeg.9.dylib' (no such file), '/Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/lib-dynload/../../libjpeg.9.dylib' (no such file), '/Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/bin/../lib/libjpeg.9.dylib' (no such file)'If you don't plan on using image functionality from `torchvision.io`, you can ignore this warning. Otherwise, there might be something wrong with your environment. Did you have `libjpeg` or `libpng` installed before building `torchvision` from source?\n", "  warn(\n", "\n", "Using device: cpu\n", "\n", "Ending Cell 4------------------------------------------\n", "Executing Cell 5---------------------------------------\n", "Output directory created: output_runs/dgcnn_inference/nortan_res\n", "MLflow experiment initialized\n", "\n", "Ending Cell 5------------------------------------------\n", "Executing Cell 6---------------------------------------\n", "Ending Cell 6------------------------------------------\n", "Executing Cell 7---------------------------------------\n", "Ending Cell 7------------------------------------------\n", "Executing Cell 8---------------------------------------\n", "Ending Cell 8------------------------------------------\n", "Executing Cell 9---------------------------------------\n", "Ending Cell 9------------------------------------------\n", "Executing Cell 10--------------------------------------\n", "Ending Cell 10-----------------------------------------\n", "Executing Cell 11--------------------------------------\n", "Ending Cell 11-----------------------------------------\n", "Executing Cell 12--------------------------------------\n", "Loaded 35,565,352 points from LAS\n", "Point cloud bounds:\n", "  X: 385723.95 to 385809.63\n", "  Y: 3529182.83 to 3529447.01\n", "  Z: 553.38 to 556.27\n", "\n", "Ending Cell 12-----------------------------------------\n", "Executing Cell 13--------------------------------------\n", "Ending Cell 13-----------------------------------------\n", "Executing Cell 14--------------------------------------\n", "Limiting to 1000 grid points for testing\n", "Created 1000 analysis locations\n", "\n", "Ending Cell 14-----------------------------------------\n", "Executing Cell 15--------------------------------------\n", "Ending Cell 15-----------------------------------------\n", "Executing Cell 16--------------------------------------\n", "Loaded DGCNN model from best_dgcnn.pth\n", "Model has 1,276,034 parameters\n", "Testing model with dummy data...\n", "Dummy test - Output shape: torch.Size([1, 2])\n", "Dummy test - Probabilities: [0.10651635 0.89348364]\n", "Model producing reasonable probability ranges\n", "\n", "Ending Cell 16-----------------------------------------\n", "Executing Cell 17--------------------------------------\n", "Ending Cell 17-----------------------------------------\n", "Executing Cell 18--------------------------------------\n", "Ending Cell 18-----------------------------------------\n", "Executing Cell 19--------------------------------------\n", "Running DGCNN inference on 1000 locations...\n", "\n", "DGCNN Results for nortan_res:\n", "  Total locations analyzed: 880\n", "  Pile detections: 134 (15.2%)\n", "  Average confidence: 0.217\n", "  High confidence (>0.9): 147\n", "\n", "Ending Cell 19-----------------------------------------\n", "Executing Cell 20--------------------------------------\n", "Ending Cell 20-----------------------------------------\n", "Executing Cell 21--------------------------------------\n", "<Figure size 1000x800 with 2 Axes>\n", "Ending Cell 21-----------------------------------------\n", "Executing Cell 22--------------------------------------\n", "Ending Cell 22-----------------------------------------\n", "Executing Cell 23--------------------------------------\n", "Saved nortan_res results to dgcnn_nortan_res_detections.csv\n", "\n", "Summary for nortan_res:\n", "  site_name: nortan_res\n", "  total_locations: 880\n", "  pile_detections: 134\n", "  detection_rate: 0.152\n", "  avg_confidence: 0.21665114164352417\n", "  high_confidence_count: 147\n", "  model_loaded: True\n", "\n", "Ending Cell 23-----------------------------------------\n", "Executing Cell 24--------------------------------------\n", "Exported all detections to KML: output_runs/dgcnn_inference/nortan_res/dgcnn_nortan_res_all_detections.kml\n", "Exported pile detections to KML: output_runs/dgcnn_inference/nortan_res/dgcnn_nortan_res_pile_detections.kml\n", "Exported high confidence detections to KML: output_runs/dgcnn_inference/nortan_res/dgcnn_nortan_res_high_confidence.kml\n", "\n", "KML Export Summary:\n", "  All detections: 880 points\n", "  Pile detections: 134 points\n", "  High confidence: 147 points\n", "MLflow run completed\n", "\n", "DGCNN inference complete for nortan_res!\n", "Output directory: output_runs/dgcnn_inference/nortan_res\n", "\n", "Ending Cell 24-----------------------------------------\n", "Executing Cell 25--------------------------------------\n", "Prediction Distribution Analysis:\n", "Min probability: 0.0000\n", "Max probability: 1.0000\n", "Mean probability: 0.2167\n", "Std probability: 0.3811\n", "Detections > 0.5: 180 (20.5%)\n", "Detections > 0.7: 170 (19.3%)\n", "Detections > 0.8: 160 (18.2%)\n", "Detections > 0.9: 147 (16.7%)\n", "Detections > 0.95: 134 (15.2%)\n", "\n", "Top 10 predictions:\n", "          x            y  pile_probability prediction\n", "385726.4523 3529370.3306               1.0       PILE\n", "385731.4523 3529185.3306               1.0       PILE\n", "385731.4523 3529325.3306               1.0       PILE\n", "385731.4523 3529440.3306               1.0       PILE\n", "385736.4523 3529220.3306               1.0       PILE\n", "385736.4523 3529240.3306               1.0       PILE\n", "385736.4523 3529335.3306               1.0       PILE\n", "385741.4523 3529235.3306               1.0       PILE\n", "385741.4523 3529240.3306               1.0       PILE\n", "385741.4523 3529340.3306               1.0       PILE\n", "\n", "Model loaded successfully: True\n", "\n", "Ending Cell 25-----------------------------------------\n"]}, {"name": "stdout", "output_type": "stream", "text": ["✓ Completed: nortan_res\n", "\n", "Total execution time: 0:12:54.077845\n"]}], "source": ["if validation_issues:\n", "    print(\"Skipping execution due to validation errors.\")\n", "    results = []\n", "else:\n", "    print(\"Starting multi-site inference...\\n\" + \"=\" * 50)\n", "    start_time = datetime.now()\n", "    \n", "    results = [\n", "        {\n", "            \"site\": cfg[\"site_name\"],\n", "            \"description\": cfg[\"description\"],\n", "            \"success\": (res := run_site_inference(cfg))[0],\n", "            \"error\": res[1],\n", "            \"output_notebook\": f\"02_dgcnn_inference_{cfg['site_name']}_executed.ipynb\"\n", "        }\n", "        for i, cfg in enumerate(SITE_CONFIGS, 1)\n", "    ]\n", "    \n", "    print(f\"\\nTotal execution time: {datetime.now() - start_time}\")\n"]}, {"cell_type": "code", "execution_count": 13, "id": "summary_report", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "============================================================\n", "EXECUTION SUMMARY\n", "============================================================\n", "Total sites: 2 | Success: 2 | Failed: 0\n", "\n", "althea_rpcs: SUCCESS\n", "nortan_res: SUCCESS\n", "\n", "Generated Notebooks:\n", "============================================================\n", "  02_dgcnn_inference_althea_rpcs_executed.ipynb (0.3 MB)\n", "  02_dgcnn_inference_nortan_res_executed.ipynb (0.3 MB)\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>site</th>\n", "      <th>description</th>\n", "      <th>status</th>\n", "      <th>output_notebook</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>althea_rpcs</td>\n", "      <td>Althea RPCS site with Point_Cloud.las</td>\n", "      <td>SUCCESS</td>\n", "      <td>02_dgcnn_inference_althea_rpcs_executed.ipynb</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>nortan_res</td>\n", "      <td>Nortan RES site with Block_11_2m.las</td>\n", "      <td>SUCCESS</td>\n", "      <td>02_dgcnn_inference_nortan_res_executed.ipynb</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["          site                            description   status  \\\n", "0  althea_rpcs  Althea RPCS site with Point_Cloud.las  SUCCESS   \n", "1   nortan_res   Nortan RES site with Block_11_2m.las  SUCCESS   \n", "\n", "                                 output_notebook  \n", "0  02_dgcnn_inference_althea_rpcs_executed.ipynb  \n", "1   02_dgcnn_inference_nortan_res_executed.ipynb  "]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "Completed! Results saved to: multi_site_inference_results.csv\n"]}], "source": ["if results:\n", "    print(\"\\n\" + \"=\"*60)\n", "    print(\"EXECUTION SUMMARY\")\n", "    print(\"=\"*60)\n", "\n", "    success_count = sum(r[\"success\"] for r in results)\n", "    fail_count = len(results) - success_count\n", "\n", "    print(f\"Total sites: {len(results)} | Success: {success_count} | Failed: {fail_count}\\n\")\n", "\n", "    for r in results:\n", "        status = \"SUCCESS\" if r[\"success\"] else f\"FAILED\\n    Error: {r['error']}\"\n", "        print(f\"{r['site']}: {status}\")\n", "\n", "    print(\"\\nGenerated Notebooks:\")\n", "    print(\"=\"*60)\n", "    for r in results:\n", "        nb_path = Path(r[\"output_notebook\"])\n", "        size = f\"{nb_path.stat().st_size / (1024 * 1024):.1f} MB\" if nb_path.exists() else \"not found\"\n", "        print(f\"  {nb_path.name} ({size})\")\n", "\n", "    # Display and save results\n", "    df = pd.DataFrame(results)\n", "    df[\"status\"] = df[\"success\"].map({True: \"SUCCESS\", False: \"FAILED\"})\n", "    display(df[[\"site\", \"description\", \"status\", \"output_notebook\"]])\n", "    \n", "    csv_path = \"multi_site_inference_results.csv\"\n", "    df.to_csv(csv_path, index=False)\n", "    print(f\"\\nCompleted! Results saved to: {csv_path}\")\n", "else:\n", "    print(\"No results to display due to validation or execution failure.\")\n"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 5}