{"cells": [{"cell_type": "markdown", "metadata": {"id": "title"}, "source": ["# DGCNN Cross-Site Pile Detection (RES→RCPS)\n", "\n", "This notebook implements DGCNN for **true cross-site generalization**:\n", "- **Train on**: Nortan RES site data\n", "- **Test on**: Althea RCPS site data\n", "- **Goal**: Test if DGCNN can generalize across construction sites\n", "\n", "**Architecture:**\n", "- DGCNN with EdgeConv layers\n", "- Input: (N, 256, 3) - 3D coordinates (simplified from 20 features)\n", "- Binary classification (pile vs non-pile)\n", "- Cross-site validation protocol\n", "\n", "**Author**: <PERSON><PERSON><PERSON>  \n", "**Date**: August 2025  \n", "**Project**: Cross-Site Construction AI Generalization\n"]}, {"cell_type": "markdown", "metadata": {"id": "setup"}, "source": ["## Setup and Mount Google Drive"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "mount_drive"}, "outputs": [], "source": ["from google.colab import drive\n", "drive.mount('/content/drive')\n", "\n", "# Project paths\n", "GDRIVE_BASE = \"/content/drive/MyDrive\"\n", "PROJECT_FOLDER = \"pointnet_pile_detection\"\n", "project_path = f\"{GDRIVE_BASE}/{PROJECT_FOLDER}\"\n", "data_path = f\"{project_path}/data\"\n", "models_path = f\"{project_path}/models\"\n", "\n", "print(f\"Project path: {project_path}\")\n", "print(f\"Data path: {data_path}\")\n", "print(f\"Models path: {models_path}\")"]}, {"cell_type": "markdown", "metadata": {"id": "imports"}, "source": ["## Install Dependencies and Imports"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "install_deps"}, "outputs": [], "source": ["# Install required packages\n", "!pip install laspy geopandas scikit-learn mlflow\n", "\n", "import numpy as np\n", "import pandas as pd\n", "import torch\n", "import torch.nn as nn\n", "import torch.optim as optim\n", "import torch.nn.functional as F\n", "from torch.utils.data import Dataset, DataLoader, random_split\n", "import laspy\n", "import geopandas as gpd\n", "from scipy.spatial import cKDTree\n", "from sklearn.metrics import accuracy_score, f1_score, classification_report, confusion_matrix\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "from pathlib import Path\n", "import pickle\n", "import json\n", "import time\n", "from datetime import datetime\n", "import warnings\n", "warnings.filterwarnings('ignore')\n", "\n", "# Set random seeds\n", "np.random.seed(42)\n", "torch.manual_seed(42)\n", "if torch.cuda.is_available():\n", "    torch.cuda.manual_seed(42)\n", "\n", "device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')\n", "print(f\"Using device: {device}\")"]}, {"cell_type": "markdown", "metadata": {"id": "data_loading"}, "source": ["## RES/RCPS Data Loading (Same as PointNet++)"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "load_data_functions"}, "outputs": [], "source": ["def load_site_point_cloud(las_path):\n", "    \"\"\"Load point cloud from LAS file\"\"\"\n", "    print(f\"Loading point cloud: {las_path}\")\n", "    las_file = laspy.read(las_path)\n", "    points = np.vstack([las_file.x, las_file.y, las_file.z]).T\n", "    print(f\"  Loaded {len(points):,} points\")\n", "    print(f\"  Bounds: X[{points[:, 0].min():.1f}, {points[:, 0].max():.1f}], Y[{points[:, 1].min():.1f}, {points[:, 1].max():.1f}], Z[{points[:, 2].min():.1f}, {points[:, 2].max():.1f}]\")\n", "    return points\n", "\n", "def load_pile_locations_from_csv(csv_path, site_name):\n", "    \"\"\"Load pile locations from Classical ML results CSV\"\"\"\n", "    print(f\"Loading pile locations from: {csv_path}\")\n", "    df = pd.read_csv(csv_path)\n", "    \n", "    # Extract coordinates based on CSV format\n", "    if 'utm_x' in df.columns and 'utm_y' in df.columns:\n", "        pile_coords = df[['utm_x', 'utm_y']].values\n", "    elif 'x' in df.columns and 'y' in df.columns:\n", "        pile_coords = df[['x', 'y']].values\n", "    else:\n", "        raise ValueError(f\"Could not find coordinate columns in {csv_path}\")\n", "    \n", "    print(f\"  Loaded {len(pile_coords)} pile locations for {site_name}\")\n", "    print(f\"  Bounds: X[{pile_coords[:, 0].min():.1f}, {pile_coords[:, 0].max():.1f}], Y[{pile_coords[:, 1].min():.1f}, {pile_coords[:, 1].max():.1f}]\")\n", "    return pile_coords\n", "\n", "def extract_patches_for_dgcnn(points, pile_coords, site_name, patch_radius=10.0, min_points=20):\n", "    \"\"\"Extract smaller patches optimized for DGCNN (256 points)\"\"\"\n", "    print(f\"\\nExtracting DGCNN patches for {site_name}:\")\n", "    print(f\"  Patch radius: {patch_radius}m (smaller for DGCNN)\")\n", "    print(f\"  Minimum points per patch: {min_points}\")\n", "    \n", "    kdtree = cKDTree(points[:, :2])\n", "    positive_patches = []\n", "    \n", "    # Extract positive patches around known pile locations\n", "    for i, (pile_x, pile_y) in enumerate(pile_coords):\n", "        if i % 100 == 0:\n", "            print(f\"  Processing pile {i+1}/{len(pile_coords)}\")\n", "        \n", "        indices = kdtree.query_ball_point([pile_x, pile_y], patch_radius)\n", "        if len(indices) >= min_points:\n", "            patch_points = points[indices]\n", "            # Center the patch around pile location\n", "            centered_patch = patch_points - np.array([pile_x, pile_y, 0])\n", "            positive_patches.append(centered_patch)\n", "    \n", "    print(f\"  Extracted {len(positive_patches)} positive patches\")\n", "    \n", "    # Extract negative patches\n", "    negative_patches = []\n", "    x_min, x_max = points[:, 0].min(), points[:, 0].max()\n", "    y_min, y_max = points[:, 1].min(), points[:, 1].max()\n", "    \n", "    target_negatives = len(positive_patches)\n", "    attempts = 0\n", "    max_attempts = target_negatives * 10\n", "    \n", "    print(f\"  Extracting {target_negatives} negative patches...\")\n", "    \n", "    while len(negative_patches) < target_negatives and attempts < max_attempts:\n", "        rand_x = np.random.uniform(x_min, x_max)\n", "        rand_y = np.random.uniform(y_min, y_max)\n", "        \n", "        distances = np.sqrt((pile_coords[:, 0] - rand_x)**2 + (pile_coords[:, 1] - rand_y)**2)\n", "        \n", "        if distances.min() > patch_radius * 2.0:\n", "            indices = kdtree.query_ball_point([rand_x, rand_y], patch_radius)\n", "            if len(indices) >= min_points:\n", "                patch_points = points[indices]\n", "                centered_patch = patch_points - np.array([rand_x, rand_y, 0])\n", "                negative_patches.append(centered_patch)\n", "        \n", "        attempts += 1\n", "        \n", "        if attempts % 1000 == 0:\n", "            print(f\"    Negative patches: {len(negative_patches)}/{target_negatives} (attempts: {attempts})\")\n", "    \n", "    print(f\"  Extracted {len(negative_patches)} negative patches\")\n", "    print(f\"  Total patches: {len(positive_patches) + len(negative_patches)}\")\n", "    \n", "    return positive_patches, negative_patches\n", "\n", "def resample_patch_for_dgcnn(patch, target_points=256):\n", "    \"\"\"Resample patch to fixed size for DGCNN (256 points)\"\"\"\n", "    if len(patch) == 0:\n", "        return np.zeros((target_points, 3))\n", "    \n", "    if len(patch) >= target_points:\n", "        # Downsample\n", "        indices = np.random.choice(len(patch), target_points, replace=False)\n", "        resampled = patch[indices]\n", "    else:\n", "        # Upsample with noise\n", "        extra_needed = target_points - len(patch)\n", "        extra_indices = np.random.choice(len(patch), extra_needed, replace=True)\n", "        extra_points = patch[extra_indices] + np.random.normal(0, 0.005, (extra_needed, 3))  # Smaller noise\n", "        resampled = np.vstack([patch, extra_points])\n", "    \n", "    return resampled.astype(np.float32)"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "load_data_main"}, "outputs": [], "source": ["# Load RES data (training site)\n", "print(\"=== LOADING RES DATA (TRAINING SITE) ===\")\n", "res_points = load_site_point_cloud(f\"{data_path}/nortan_res/Block_11_2m.las\")\n", "res_pile_coords = load_pile_locations_from_csv(\n", "    f\"{data_path}/ground_truth/nortan_res_pile_detection_results_20250807_214148.csv\", \n", "    \"nortan_res\"\n", ")\n", "\n", "# Extract RES patches (smaller for DGCNN)\n", "res_pos_patches, res_neg_patches = extract_patches_for_dgcnn(\n", "    res_points, res_pile_coords, \"nortan_res\", patch_radius=10.0, min_points=20\n", ")\n", "\n", "print(\"\\n=== LOADING RCPS DATA (TEST SITE) ===\")\n", "rcps_points = load_site_point_cloud(f\"{data_path}/althea_rpcs/Point_Cloud.las\")\n", "rcps_pile_coords = load_pile_locations_from_csv(\n", "    f\"{data_path}/ground_truth/rcps_generalization_results_20250807_221320.csv\", \n", "    \"althea_rcps\"\n", ")\n", "\n", "# Extract RCPS patches (smaller for DGCNN)\n", "rcps_pos_patches, rcps_neg_patches = extract_patches_for_dgcnn(\n", "    rcps_points, rcps_pile_coords, \"althea_rcps\", patch_radius=10.0, min_points=20\n", ")\n", "\n", "print(\"\\n=== DATA SUMMARY ===\")\n", "print(f\"RES (training): {len(res_pos_patches)} positive, {len(res_neg_patches)} negative\")\n", "print(f\"RCPS (testing): {len(rcps_pos_patches)} positive, {len(rcps_neg_patches)} negative\")\n", "print(f\"Total training samples: {len(res_pos_patches) + len(res_neg_patches)}\")\n", "print(f\"Total test samples: {len(rcps_pos_patches) + len(rcps_neg_patches)}\")"]}, {"cell_type": "markdown", "metadata": {"id": "prepare_datasets"}, "source": ["## Prepare Datasets for DGCNN"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "create_datasets"}, "outputs": [], "source": ["# Resample all patches to fixed size (256 points for DGCNN)\n", "print(\"Resampling patches to 256 points for DGCNN...\")\n", "\n", "# Training data (RES)\n", "train_patches = []\n", "train_labels = []\n", "\n", "# Positive patches\n", "for patch in res_pos_patches:\n", "    resampled = resample_patch_for_dgcnn(patch, 256)\n", "    train_patches.append(resampled)\n", "    train_labels.append(1)\n", "\n", "# Negative patches\n", "for patch in res_neg_patches:\n", "    resampled = resample_patch_for_dgcnn(patch, 256)\n", "    train_patches.append(resampled)\n", "    train_labels.append(0)\n", "\n", "# Test data (RCPS)\n", "test_patches = []\n", "test_labels = []\n", "\n", "# Positive patches\n", "for patch in rcps_pos_patches:\n", "    resampled = resample_patch_for_dgcnn(patch, 256)\n", "    test_patches.append(resampled)\n", "    test_labels.append(1)\n", "\n", "# Negative patches\n", "for patch in rcps_neg_patches:\n", "    resampled = resample_patch_for_dgcnn(patch, 256)\n", "    test_patches.append(resampled)\n", "    test_labels.append(0)\n", "\n", "# Convert to numpy arrays\n", "train_patches = np.array(train_patches, dtype=np.float32)  # (N, 256, 3)\n", "train_labels = np.array(train_labels, dtype=np.int64)      # (N,)\n", "test_patches = np.array(test_patches, dtype=np.float32)    # (M, 256, 3)\n", "test_labels = np.array(test_labels, dtype=np.int64)        # (M,)\n", "\n", "print(f\"\\nFinal dataset shapes:\")\n", "print(f\"Training: {train_patches.shape}, labels: {train_labels.shape}\")\n", "print(f\"Testing: {test_patches.shape}, labels: {test_labels.shape}\")\n", "print(f\"Training class distribution: {np.bincount(train_labels)}\")\n", "print(f\"Testing class distribution: {np.bincount(test_labels)}\")"]}, {"cell_type": "markdown", "metadata": {"id": "dgcnn_architecture"}, "source": ["## DGCNN Architecture"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "dgcnn_model"}, "outputs": [], "source": ["def knn(x, k):\n", "    \"\"\"Find k nearest neighbors\"\"\"\n", "    inner = -2*torch.matmul(x.transpose(2, 1), x)\n", "    xx = torch.sum(x**2, dim=1, keepdim=True)\n", "    pairwise_distance = -xx - inner - xx.transpose(2, 1)\n", "    \n", "    idx = pairwise_distance.topk(k=k, dim=-1)[1]   # (batch_size, num_points, k)\n", "    return idx\n", "\n", "def get_graph_feature(x, k=20, idx=None):\n", "    \"\"\"Construct edge features\"\"\"\n", "    batch_size = x.size(0)\n", "    num_points = x.size(2)\n", "    x = x.view(batch_size, -1, num_points)\n", "    \n", "    if idx is None:\n", "        idx = knn(x, k=k)   # (batch_size, num_points, k)\n", "    \n", "    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')\n", "    \n", "    idx_base = torch.arange(0, batch_size, device=device).view(-1, 1, 1)*num_points\n", "    \n", "    idx = idx + idx_base\n", "    \n", "    idx = idx.view(-1)\n", "    \n", "    _, num_dims, _ = x.size()\n", "    \n", "    x = x.transpose(2, 1).contiguous()   # (batch_size, num_points, num_dims)  -> (batch_size*num_points, num_dims) #   batch_size * num_points * k + range(0, batch_size*num_points)\n", "    feature = x.view(batch_size*num_points, -1)[idx, :]\n", "    feature = feature.view(batch_size, num_points, k, num_dims) \n", "    x = x.view(batch_size, num_points, 1, num_dims).repeat(1, 1, k, 1)\n", "    \n", "    feature = torch.cat((feature-x, x), dim=3).permute(0, 3, 1, 2).contiguous()\n", "    \n", "    return feature      # (batch_size, 2*num_dims, num_points, k)\n", "\n", "class DGCNN(nn.Module):\n", "    def __init__(self, num_classes=2, k=20, dropout=0.5):\n", "        super(DGC<PERSON><PERSON>, self).__init__()\n", "        self.k = k\n", "        \n", "        self.bn1 = nn.BatchNorm2d(64)\n", "        self.bn2 = nn.BatchNorm2d(64)\n", "        self.bn3 = nn.<PERSON>ch<PERSON>orm2d(128)\n", "        self.bn4 = nn.BatchNorm2d(256)\n", "        self.bn5 = nn.<PERSON>chNorm1d(1024)\n", "\n", "        self.conv1 = nn.Sequential(nn.Conv2d(6, 64, kernel_size=1, bias=False),\n", "                                   self.bn1,\n", "                                   nn.LeakyReLU(negative_slope=0.2))\n", "        self.conv2 = nn.Sequential(nn.Conv2d(6*2, 64, kernel_size=1, bias=False),\n", "                                   self.bn2,\n", "                                   nn.LeakyReLU(negative_slope=0.2))\n", "        self.conv3 = nn.Sequential(nn.Conv2d(64*2, 128, kernel_size=1, bias=False),\n", "                                   self.bn3,\n", "                                   nn.LeakyReLU(negative_slope=0.2))\n", "        self.conv4 = nn.Sequential(nn.Conv2d(128*2, 256, kernel_size=1, bias=False),\n", "                                   self.bn4,\n", "                                   nn.LeakyReLU(negative_slope=0.2))\n", "        self.conv5 = nn.Sequential(nn.Conv1d(512, 1024, kernel_size=1, bias=False),\n", "                                   self.bn5,\n", "                                   nn.LeakyReLU(negative_slope=0.2))\n", "        \n", "        # Classification head\n", "        self.linear1 = nn.Linear(1024*2, 512, bias=False)\n", "        self.bn6 = nn.BatchNorm1d(512)\n", "        self.dp1 = nn.Dropout(p=dropout)\n", "        self.linear2 = nn.Linear(512, 256)\n", "        self.bn7 = nn.BatchNorm1d(256)\n", "        self.dp2 = nn.Dropout(p=dropout)\n", "        self.linear3 = nn.Linear(256, num_classes)\n", "\n", "    def forward(self, x):\n", "        batch_size = x.size(0)\n", "        \n", "        # EdgeConv layers\n", "        x = get_graph_feature(x, k=self.k)      # (batch_size, 3, num_points) -> (batch_size, 3*2, num_points, k)\n", "        x = self.conv1(x)                       # (batch_size, 3*2, num_points, k) -> (batch_size, 64, num_points, k)\n", "        x1 = x.max(dim=-1, keepdim=False)[0]    # (batch_size, 64, num_points, k) -> (batch_size, 64, num_points)\n", "\n", "        x = get_graph_feature(x1, k=self.k)     # (batch_size, 64, num_points) -> (batch_size, 64*2, num_points, k)\n", "        x = self.conv2(x)                       # (batch_size, 64*2, num_points, k) -> (batch_size, 64, num_points, k)\n", "        x2 = x.max(dim=-1, keepdim=False)[0]    # (batch_size, 64, num_points, k) -> (batch_size, 64, num_points)\n", "\n", "        x = get_graph_feature(x2, k=self.k)     # (batch_size, 64, num_points) -> (batch_size, 64*2, num_points, k)\n", "        x = self.conv3(x)                       # (batch_size, 64*2, num_points, k) -> (batch_size, 128, num_points, k)\n", "        x3 = x.max(dim=-1, keepdim=False)[0]    # (batch_size, 128, num_points, k) -> (batch_size, 128, num_points)\n", "\n", "        x = get_graph_feature(x3, k=self.k)     # (batch_size, 128, num_points) -> (batch_size, 128*2, num_points, k)\n", "        x = self.conv4(x)                       # (batch_size, 128*2, num_points, k) -> (batch_size, 256, num_points, k)\n", "        x4 = x.max(dim=-1, keepdim=False)[0]    # (batch_size, 256, num_points, k) -> (batch_size, 256, num_points)\n", "\n", "        x = torch.cat((x1, x2, x3, x4), dim=1)  # (batch_size, 64+64+128+256, num_points)\n", "\n", "        x = self.conv5(x)                       # (batch_size, 512, num_points) -> (batch_size, 1024, num_points)\n", "        x1 = F.adaptive_max_pool1d(x, 1).view(batch_size, -1)           # (batch_size, 1024, num_points) -> (batch_size, 1024)\n", "        x2 = F.adaptive_avg_pool1d(x, 1).view(batch_size, -1)           # (batch_size, 1024, num_points) -> (batch_size, 1024)\n", "        x = torch.cat((x1, x2), 1)              # (batch_size, 1024*2)\n", "\n", "        # Classification\n", "        x = F.leaky_relu(self.bn6(self.linear1(x)), negative_slope=0.2) # (batch_size, 1024*2) -> (batch_size, 512)\n", "        x = self.dp1(x)\n", "        x = F.leaky_relu(self.bn7(self.linear2(x)), negative_slope=0.2) # (batch_size, 512) -> (batch_size, 256)\n", "        x = self.dp2(x)\n", "        x = self.linear3(x)                                             # (batch_size, 256) -> (batch_size, num_classes)\n", "        \n", "        return x"]}, {"cell_type": "markdown", "metadata": {"id": "dataset_class"}, "source": ["## Dataset and DataLoader"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "create_dataset_class"}, "outputs": [], "source": ["class CrossSiteDataset(Dataset):\n", "    def __init__(self, patches, labels):\n", "        # DGCNN expects (batch_size, num_features, num_points)\n", "        self.patches = torch.FloatTensor(patches).transpose(2, 1)  # (N, 3, 256)\n", "        self.labels = torch.LongTensor(labels)\n", "    \n", "    def __len__(self):\n", "        return len(self.patches)\n", "    \n", "    def __getitem__(self, idx):\n", "        return self.patches[idx], self.labels[idx]\n", "\n", "# Create datasets\n", "train_dataset = CrossSiteDataset(train_patches, train_labels)\n", "test_dataset = CrossSiteDataset(test_patches, test_labels)\n", "\n", "# Create train/validation split from training data\n", "train_size = int(0.8 * len(train_dataset))\n", "val_size = len(train_dataset) - train_size\n", "train_subset, val_subset = random_split(train_dataset, [train_size, val_size])\n", "\n", "# Create data loaders\n", "batch_size = 8  # Smaller batch size for DGCNN (more memory intensive)\n", "train_loader = DataLoader(train_subset, batch_size=batch_size, shuffle=True)\n", "val_loader = DataLoader(val_subset, batch_size=batch_size, shuffle=False)\n", "test_loader = DataLoader(test_dataset, batch_size=batch_size, shuffle=False)\n", "\n", "print(f\"Dataset sizes:\")\n", "print(f\"  Training: {len(train_subset)}\")\n", "print(f\"  Validation: {len(val_subset)}\")\n", "print(f\"  Test (RCPS): {len(test_dataset)}\")\n", "print(f\"  Batch size: {batch_size}\")\n", "print(f\"  Input shape: (batch_size, 3, 256) for DGCNN\")"]}, {"cell_type": "markdown", "metadata": {"id": "training_setup"}, "source": ["## Training Setup and Execution"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "setup_training"}, "outputs": [], "source": ["# Initialize model\n", "model = DGCNN(num_classes=2, k=20, dropout=0.5).to(device)\n", "print(f\"Model initialized with {sum(p.numel() for p in model.parameters()):,} parameters\")\n", "\n", "# Training configuration\n", "criterion = nn.CrossEntropyLoss()\n", "optimizer = optim.<PERSON>(model.parameters(), lr=0.001, weight_decay=1e-4)\n", "scheduler = optim.lr_scheduler.StepLR(optimizer, step_size=20, gamma=0.7)\n", "\n", "# Training parameters\n", "num_epochs = 50  # Reduced for Colab\n", "best_val_acc = 0.0\n", "train_losses = []\n", "val_losses = []\n", "train_accs = []\n", "val_accs = []\n", "\n", "print(f\"Training configuration:\")\n", "print(f\"  Epochs: {num_epochs}\")\n", "print(f\"  Learning rate: 0.001\")\n", "print(f\"  Weight decay: 1e-4\")\n", "print(f\"  K-neighbors: 20\")\n", "print(f\"  Device: {device}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "training_loop"}, "outputs": [], "source": ["# Training loop\n", "print(\"\\n=== STARTING DGCNN TRAINING ===\")\n", "start_time = time.time()\n", "\n", "for epoch in range(num_epochs):\n", "    epoch_start = time.time()\n", "    \n", "    # Training phase\n", "    model.train()\n", "    train_loss = 0.0\n", "    train_correct = 0\n", "    train_total = 0\n", "    \n", "    for batch_idx, (data, target) in enumerate(train_loader):\n", "        data, target = data.to(device), target.to(device)\n", "        \n", "        optimizer.zero_grad()\n", "        output = model(data)\n", "        loss = criterion(output, target)\n", "        loss.backward()\n", "        optimizer.step()\n", "        \n", "        train_loss += loss.item()\n", "        pred = output.argmax(dim=1)\n", "        train_correct += pred.eq(target).sum().item()\n", "        train_total += target.size(0)\n", "        \n", "        if batch_idx % 10 == 0:\n", "            print(f'  Epoch {epoch+1}/{num_epochs}, Batch {batch_idx}/{len(train_loader)}, Loss: {loss.item():.4f}')\n", "    \n", "    # Validation phase\n", "    model.eval()\n", "    val_loss = 0.0\n", "    val_correct = 0\n", "    val_total = 0\n", "    \n", "    with torch.no_grad():\n", "        for data, target in val_loader:\n", "            data, target = data.to(device), target.to(device)\n", "            output = model(data)\n", "            loss = criterion(output, target)\n", "            \n", "            val_loss += loss.item()\n", "            pred = output.argmax(dim=1)\n", "            val_correct += pred.eq(target).sum().item()\n", "            val_total += target.size(0)\n", "    \n", "    # Calculate metrics\n", "    train_loss /= len(train_loader)\n", "    val_loss /= len(val_loader)\n", "    train_acc = 100. * train_correct / train_total\n", "    val_acc = 100. * val_correct / val_total\n", "    \n", "    # Store metrics\n", "    train_losses.append(train_loss)\n", "    val_losses.append(val_loss)\n", "    train_accs.append(train_acc)\n", "    val_accs.append(val_acc)\n", "    \n", "    # Update learning rate\n", "    scheduler.step()\n", "    \n", "    # Save best model\n", "    if val_acc > best_val_acc:\n", "        best_val_acc = val_acc\n", "        torch.save(model.state_dict(), f'{models_path}/dgcnn_best_model.pth')\n", "        print(f'  *** New best model saved! Validation accuracy: {val_acc:.2f}% ***')\n", "    \n", "    epoch_time = time.time() - epoch_start\n", "    print(f'Epoch {epoch+1}/{num_epochs}: Train Loss: {train_loss:.4f}, Train Acc: {train_acc:.2f}%, '\n", "          f'Val Loss: {val_loss:.4f}, Val Acc: {val_acc:.2f}%, Time: {epoch_time:.1f}s')\n", "    print('-' * 80)\n", "\n", "total_time = time.time() - start_time\n", "print(f\"\\nDGCNN Training completed in {total_time/60:.1f} minutes\")\n", "print(f\"Best validation accuracy: {best_val_acc:.2f}%\")"]}, {"cell_type": "markdown", "metadata": {"id": "evaluation"}, "source": ["## Cross-Site Evaluation on RCPS"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "test_evaluation"}, "outputs": [], "source": ["# Load best model\n", "model.load_state_dict(torch.load(f'{models_path}/dgcnn_best_model.pth'))\n", "model.eval()\n", "\n", "print(\"=== DGCNN CROSS-SITE EVALUATION ON RCPS ===\")\n", "print(\"Testing DGCNN trained on RES data on RCPS data...\")\n", "\n", "# Test evaluation\n", "test_correct = 0\n", "test_total = 0\n", "all_predictions = []\n", "all_targets = []\n", "all_probabilities = []\n", "\n", "with torch.no_grad():\n", "    for data, target in test_loader:\n", "        data, target = data.to(device), target.to(device)\n", "        output = model(data)\n", "        \n", "        # Get predictions and probabilities\n", "        probabilities = torch.softmax(output, dim=1)\n", "        pred = output.argmax(dim=1)\n", "        \n", "        test_correct += pred.eq(target).sum().item()\n", "        test_total += target.size(0)\n", "        \n", "        all_predictions.extend(pred.cpu().numpy())\n", "        all_targets.extend(target.cpu().numpy())\n", "        all_probabilities.extend(probabilities.cpu().numpy())\n", "\n", "# Calculate metrics\n", "test_acc = 100. * test_correct / test_total\n", "test_f1 = f1_score(all_targets, all_predictions)\n", "\n", "print(f\"\\nDGCNN Cross-Site Test Results (RES→RCPS):\")\n", "print(f\"  Test Accuracy: {test_acc:.2f}%\")\n", "print(f\"  Test F1-Score: {test_f1:.4f}\")\n", "print(f\"  Total test samples: {test_total}\")\n", "print(f\"  Correct predictions: {test_correct}\")\n", "\n", "# Detailed classification report\n", "print(\"\\nDetailed Classification Report:\")\n", "print(classification_report(all_targets, all_predictions, target_names=['Non-<PERSON>le', '<PERSON>le']))\n", "\n", "# Confusion matrix\n", "cm = confusion_matrix(all_targets, all_predictions)\n", "print(\"\\nConfusion Matrix:\")\n", "print(cm)\n", "\n", "# Plot confusion matrix\n", "plt.figure(figsize=(8, 6))\n", "sns.heatmap(cm, annot=True, fmt='d', cmap='Reds', \n", "            xticklabels=['Non-<PERSON><PERSON>', '<PERSON>le'], \n", "            yticklabels=['Non-<PERSON>le', '<PERSON>le'])\n", "plt.title('DGCNN Cross-Site Confusion Matrix (RES→RCPS)')\n", "plt.ylabel('True Label')\n", "plt.xlabel('Predicted Label')\n", "plt.show()\n", "\n", "# Save results\n", "timestamp = datetime.now().strftime(\"%Y%m%d_%H%M%S\")\n", "\n", "# Save training history\n", "training_history = {\n", "    'train_losses': train_losses,\n", "    'val_losses': val_losses,\n", "    'train_accs': train_accs,\n", "    'val_accs': val_accs,\n", "    'best_val_acc': best_val_acc,\n", "    'test_acc': test_acc,\n", "    'test_f1': test_f1,\n", "    'num_epochs': num_epochs,\n", "    'batch_size': batch_size,\n", "    'k_neighbors': 20\n", "}\n", "\n", "with open(f'{models_path}/dgcnn_training_history_{timestamp}.json', 'w') as f:\n", "    json.dump(training_history, f, indent=2)\n", "\n", "# Save predictions\n", "results_df = pd.DataFrame({\n", "    'true_label': all_targets,\n", "    'predicted_label': all_predictions,\n", "    'pile_probability': [prob[1] for prob in all_probabilities],\n", "    'non_pile_probability': [prob[0] for prob in all_probabilities]\n", "})\n", "\n", "results_df.to_csv(f'{models_path}/dgcnn_rcps_predictions_{timestamp}.csv', index=False)\n", "\n", "print(f\"\\nResults saved:\")\n", "print(f\"  Training history: dgcnn_training_history_{timestamp}.json\")\n", "print(f\"  Predictions: dgcnn_rcps_predictions_{timestamp}.csv\")\n", "print(f\"  Model: dgcnn_best_model.pth\")\n", "\n", "print(\"\\n=== DGCNN CROSS-SITE TRAINING COMPLETE ===\")\n", "print(f\"Successfully trained on RES data and tested on RCPS data\")\n", "print(f\"Cross-site generalization accuracy: {test_acc:.2f}%\")\n", "print(f\"\\nComparison Summary:\")\n", "print(f\"  Classical ML (local): 100.0% accuracy\")\n", "print(f\"  DGCNN (cross-site): {test_acc:.2f}% accuracy\")\n", "print(f\"  Generalization gap: {100.0 - test_acc:.2f}%\")"]}], "metadata": {"colab": {"provenance": [], "gpuType": "T4"}, "kernelspec": {"display_name": "Python 3", "name": "python3"}, "language_info": {"name": "python"}, "accelerator": "GPU"}, "nbformat": 4, "nbformat_minor": 0}