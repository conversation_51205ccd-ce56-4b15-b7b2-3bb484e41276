{"cells": [{"cell_type": "code", "execution_count": 2, "id": "52ba98e8", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Libraries imported successfully\n", "Configuration:\n", "  Site: trino_enel\n", "  Target CRS: EPSG:32632\n", "  Patch radius: 3.0m (like RES)\n", "  Points per patch: 64 (like RES)\n", "  Output: output_runs/trino_single_site_20250810_092940\n", "  Point cloud: ../../../../data/processed/trino_enel/denoising/trino_enel_denoised_for_registration.ply\n", "  Buffer KML: ../../../../data/raw/trino_enel/kml/pile.kml\n", "  IFC CSV: ../../../../data/processed/trino_enel/ifc_metadata//GRE.EEC.S.00.IT.P.14353.00.265_coordinates.csv\n", "Loading Trino point cloud...\n", "✅ Loaded 983,884 points\n", "Loading Buffer KML (ground truth like RES)...\n", "Loaded KML with 1288 features\n", "Geometry types found: {'Polygon': 1288}\n", "✅ Extracted 1288 pile locations from KML geometries\n", "Reprojecting KML from WGS84 to EPSG:32632 (UTM Zone 32N)...\n", "✅ Reprojected to EPSG:32632 - 1288 pile locations\n", "UTM coordinates (EPSG:32632):\n", "  X (Easting): 435628.6 to 436255.2 (range: 626.6m)\n", "  Y (Northing): 5011261.4 to 5012200.8 (range: 939.4m)\n", "Loading IFC data (for confidence enhancement)...\n", "IFC columns available: ['GlobalId', 'Name', 'Type', 'X', 'Y', 'Z', 'Latitude', 'Longitude']\n", "✅ Loaded 14,460 IFC pile locations using columns: 'X', 'Y'\n", "IFC coordinates (assuming EPSG:32632):\n", "  X (Easting): 435267.2 to 436719.9 (range: 1452.8m)\n", "  Y (Northing): 5010900.7 to 5012462.4 (range: 1561.7m)\n", "✅ KML and IFC coordinates have overlapping ranges - good!\n", "Filtering point cloud around 1288 pile locations...\n", "✅ Filtered to 63,211 points around pile zones\n", "   Reduction: 983,884 → 63,211 points\n", "Enhancing 1288 buffer piles with IFC confidence...\n", "✅ Confidence distribution:\n", "   High: 1079 piles\n", "   Medium: 151 piles\n", "   Low: 58 piles\n", "Feature extraction functions defined\n", "Creating dataset from 1288 pile locations...\n", "✅ Extracted 203 positive patches\n", "Creating negative samples...\n", "✅ Created 5 negative patches\n", "✅ Total dataset: 208 samples\n", "   Positive: 203 (97.6%)\n", "   Negative: 5 (2.4%)\n", "Dataset organization by confidence:\n", "  High: 183 samples (183 positive, 0 negative)\n", "  Medium: 15 samples (15 positive, 0 negative)\n", "  Low: 5 samples (5 positive, 0 negative)\n", "  Synthetic: 5 samples (0 positive, 5 negative)\n", "\n", "Dataset splits:\n", "  Training: 199 samples\n", "  Validation: 4 samples\n", "  Test: 5 samples\n", "Training Gradient Boosting model (like successful RES approach)...\n", "✅ Training accuracy: 1.000\n", "✅ Validation accuracy: 0.500\n", "Validating on 1288 known pile locations...\n", "\n", "🟢 GROUND TRUTH VALIDATION RESULTS:\n", "  Detection rate: 99.5% (202/203)\n", "  Average confidence: 0.995\n", "  Status: EXCELLENT\n", "  Failed extractions: 1085\n", "\n", "Evaluating on test set (5 samples)...\n", "Test Set Performance:\n", "  Accuracy: 1.000\n", "  F1-Score: 1.000\n", "  Precision: 1.000\n", "  Recall: 1.000\n", "  Low confidence: 1.000 (5 samples)\n", "\n", "Top 10 Most Important Features:\n", "  aspect_ratio: 0.218\n", "  y_range: 0.200\n", "  height_width_ratio: 0.156\n", "  z_mean: 0.128\n", "  height_25p: 0.118\n", "  y_std: 0.051\n", "  height_75p: 0.044\n", "  x_mean: 0.041\n", "  z_std: 0.017\n", "  x_std: 0.014\n", "\n", "✅ Results saved to output_runs/trino_single_site_20250810_092940\n", "  Main results: trino_single_site_results_20250810_092949.json\n", "  Model: trino_pile_detection_model_20250810_092949.pkl\n", "  Feature importance: feature_importance_20250810_092949.csv\n", "Creating QGIS export...\n", "✅ Converted coordinates from EPSG:32632 to WGS84\n", "✅ QGIS export created: trino_pile_detection_qgis_20250810_092949.csv\n", "   Coordinate system: EPSG:32632 (UTM) + WGS84 (Geographic)\n", "   Total piles tested: 203\n", "   Piles detected: 202\n", "   Detection rate: 99.5%\n"]}, {"data": {"image/png": "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********************************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", "text/plain": ["<Figure size 1500x1200 with 4 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["✅ Visualization saved: trino_results_visualization_20250810_092950.png\n", "\n", "============================================================\n", "🎯 TRINO SINGLE-SITE STYLE RESULTS SUMMARY\n", "============================================================\n", "\n", "📊 Dataset Summary:\n", "   Site: trino_enel\n", "   Total point cloud: 983,884 points\n", "   Filtered around piles: 63,211 points\n", "   Buffer pile locations: 1288\n", "   IFC pile locations: 14,460\n", "   Final dataset: 208 samples (203 positive, 5 negative)\n", "\n", "🎯 Ground Truth Validation (KEY METRIC):\n", "   Detection rate: 99.5%\n", "   Average confidence: 0.995\n", "   Status: EXCELLENT\n", "   <PERSON>les tested: 1288\n", "\n", "📈 Test Set Performance:\n", "   Accuracy: 1.000\n", "   F1-Score: 1.000\n", "   Precision: 1.000\n", "   Recall: 1.000\n", "\n", "🔧 Configuration Used (Single-Site Style):\n", "   Patch radius: 3.0m (vs 8-20m in failed approaches)\n", "   Points per patch: 64 (vs 1024 in failed approaches)\n", "   Features: 22 engineering features (vs 20 statistical in failed approaches)\n", "   Algorithm: Gradient Boosting (vs PointNet++ in failed approaches)\n", "   Validation: Ground truth detection rate (vs abstract accuracy in failed approaches)\n", "\n", "📁 Output Files:\n", "   Results: trino_single_site_results_20250810_092949.json\n", "   QGIS export: trino_pile_detection_qgis_20250810_092949.csv\n", "   Visualization: trino_results_visualization_20250810_092950.png\n", "   All files in: output_runs/trino_single_site_20250810_092940\n", "\n", "🏆 FINAL ASSESSMENT:\n", "   🟢 EXCELLENT - Ready for production use!\n", "\n", "✅ SUCCESS: This approach shows significant improvement over previous failed attempts!\n", "   • Used Buffer KML as ground truth (like successful RES)\n", "   • Applied single-site parameters (3m patches, 64 points)\n", "   • Used engineering features (22 interpretable features)\n", "   • Applied classical ML (Gradient Boosting)\n", "   • Validated on ground truth (detection rate on known piles)\n", "\n", "============================================================\n", "🎉 TRINO SINGLE-SITE STYLE DATA PREPARATION COMPLETE!\n", "📂 All results saved in: output_runs/trino_single_site_20250810_092940\n", "📊 Ground truth detection rate: 99.5%\n", "🎯 Status: EXCELLENT\n", "\n", "Next steps:\n", "1. Review QGIS export for field validation\n", "2. Analyze feature importance for insights\n", "3. If successful, apply to other sites\n", "4. If needs improvement, refine based on failed detection analysis\n"]}], "source": ["# %% [markdown]\n", "# # Trino Pile Detection - Single Site Style Data Preparation\n", "# \n", "# This notebook adapts the successful single-site validation approach (RES→RCPS) \n", "# to work with Trino's multi-source data (IFC + Buffer KML + Point Cloud).\n", "# \n", "# **Key Strategy:**\n", "# - Use Buffer KML as primary ground truth (like successful RES approach)\n", "# - Apply same patch parameters (3m radius, 64 points, 22 features)\n", "# - Use classical ML (Gradient Boosting) as primary algorithm\n", "# - Validate using ground truth detection rate on known pile locations\n", "# - Use IFC only for confidence enhancement, not primary training\n", "# \n", "# **Author**: Adapted from successful single-site approach\n", "# **Date**: August 2025\n", "\n", "# %%\n", "import numpy as np\n", "import pandas as pd\n", "import open3d as o3d\n", "import geopandas as gpd\n", "from scipy.spatial import cKDTree\n", "from sklearn.ensemble import GradientBoostingClassifier\n", "from sklearn.model_selection import train_test_split\n", "from sklearn.metrics import accuracy_score, f1_score, precision_score, recall_score\n", "from pathlib import Path\n", "import pickle\n", "import json\n", "from datetime import datetime\n", "import matplotlib.pyplot as plt\n", "import warnings\n", "warnings.filterwarnings('ignore')\n", "\n", "print(\"Libraries imported successfully\")\n", "\n", "# %%\n", "# Single-site style parameters (successful RES approach)\n", "PATCH_RADIUS = 3.0          # From RES success (was 8.0 in failed approaches)\n", "TARGET_PATCH_SIZE = 64      # From RES success (was 1024 in failed approaches)\n", "MIN_POINTS = 20             # Keep same\n", "SITE_NAME = \"trino_enel\"\n", "TARGET_CRS = \"EPSG:32632\"   # UTM Zone 32N (Europe) - Trino's coordinate system\n", "\n", "\n", "# File paths - UPDATE THESE TO YOUR TRINO DATA\n", "POINT_CLOUD_PATH = \"../../../../data/processed/trino_enel/denoising/trino_enel_denoised_for_registration.ply\"\n", "BUFFER_KML_PATH = \"../../../../data/raw/trino_enel/kml/pile.kml\"  # Your Buffer KML\n", "IFC_CSV_PATH = \"../../../../data/processed/trino_enel/ifc_metadata//GRE.EEC.S.00.IT.P.14353.00.265_coordinates.csv\"  # Your 14k+ IFC piles\n", "OUTPUT_DIR = f\"output_runs/trino_single_site_{datetime.now().strftime('%Y%m%d_%H%M%S')}\"\n", "\n", "# Create output directory\n", "output_dir = Path(OUTPUT_DIR)\n", "output_dir.mkdir(parents=True, exist_ok=True)\n", "\n", "print(f\"Configuration:\")\n", "print(f\"  Site: {SITE_NAME}\")\n", "print(f\"  Target CRS: {TARGET_CRS}\")\n", "print(f\"  Patch radius: {PATCH_RADIUS}m (like RES)\")\n", "print(f\"  Points per patch: {TARGET_PATCH_SIZE} (like RES)\")\n", "print(f\"  Output: {OUTPUT_DIR}\")\n", "print(f\"  Point cloud: {POINT_CLOUD_PATH}\")\n", "print(f\"  Buffer KML: {BUFFER_KML_PATH}\")\n", "print(f\"  IFC CSV: {IFC_CSV_PATH}\")\n", "\n", "# %%\n", "# Replace the load_trino_data() function with this fixed version\n", "\n", "# Updated load_trino_data() function specifically for EPSG:32632\n", "\n", "def load_trino_data():\n", "    \"\"\"Load Trino multi-source data - optimized for EPSG:32632 (UTM Zone 32N)\"\"\"\n", "    \n", "    print(\"Loading Trino point cloud...\")\n", "    try:\n", "        point_cloud = o3d.io.read_point_cloud(POINT_CLOUD_PATH)\n", "        points = np.asarray(point_cloud.points)\n", "        print(f\"✅ Loaded {len(points):,} points\")\n", "    except Exception as e:\n", "        print(f\"❌ Error loading point cloud: {e}\")\n", "        return None, None, None\n", "    \n", "    print(\"Loading Buffer KML (ground truth like RES)...\")\n", "    try:\n", "        # Load KML and extract pile locations\n", "        gdf_kml = gpd.read_file(BUFFER_KML_PATH, driver='KML')\n", "        print(f\"Loaded KML with {len(gdf_kml)} features\")\n", "        \n", "        # Debug: Check what geometry types we have\n", "        geom_types = gdf_kml.geometry.geom_type.value_counts()\n", "        print(f\"Geometry types found: {dict(geom_types)}\")\n", "        \n", "        # Extract coordinates from different geometry types\n", "        buffer_pile_locations = []\n", "        for idx, geom in enumerate(gdf_kml.geometry):\n", "            try:\n", "                if geom.geom_type == 'Point':\n", "                    buffer_pile_locations.append([geom.x, geom.y])\n", "                elif geom.geom_type == 'Polygon':\n", "                    # Use centroid of polygon\n", "                    centroid = geom.centroid\n", "                    buffer_pile_locations.append([centroid.x, centroid.y])\n", "                elif geom.geom_type == 'LineString':\n", "                    # Use centroid of line\n", "                    centroid = geom.centroid\n", "                    buffer_pile_locations.append([centroid.x, centroid.y])\n", "                elif geom.geom_type == 'MultiPolygon':\n", "                    # Use centroid of multipolygon\n", "                    centroid = geom.centroid\n", "                    buffer_pile_locations.append([centroid.x, centroid.y])\n", "                else:\n", "                    print(f\"Warning: Unknown geometry type {geom.geom_type} at index {idx}\")\n", "            except Exception as e:\n", "                print(f\"Warning: Failed to process geometry at index {idx}: {e}\")\n", "                continue\n", "        \n", "        if len(buffer_pile_locations) == 0:\n", "            print(\"❌ No valid pile locations extracted from KML\")\n", "            return None, None, None\n", "        \n", "        print(f\"✅ Extracted {len(buffer_pile_locations)} pile locations from KML geometries\")\n", "        \n", "        # Reproject from WGS84 (KML default) to UTM Zone 32N\n", "        print(\"Reprojecting KML from WGS84 to EPSG:32632 (UTM Zone 32N)...\")\n", "        \n", "        # Set CRS if not already set\n", "        if gdf_kml.crs is None:\n", "            gdf_kml = gdf_kml.set_crs('EPSG:4326')  # KML is typically WGS84\n", "        \n", "        # Reproject to UTM Zone 32N\n", "        gdf_utm = gdf_kml.to_crs('EPSG:32632')\n", "        \n", "        # Extract UTM coordinates\n", "        buffer_pile_locations_utm = []\n", "        for geom in gdf_utm.geometry:\n", "            try:\n", "                if geom.geom_type == 'Point':\n", "                    buffer_pile_locations_utm.append([geom.x, geom.y])\n", "                else:\n", "                    centroid = geom.centroid\n", "                    buffer_pile_locations_utm.append([centroid.x, centroid.y])\n", "            except Exception as e:\n", "                print(f\"Warning: Failed to extract UTM coordinates: {e}\")\n", "                continue\n", "        \n", "        buffer_piles = np.array(buffer_pile_locations_utm)\n", "        print(f\"✅ Reprojected to EPSG:32632 - {len(buffer_piles)} pile locations\")\n", "        \n", "        # Coordinate range check\n", "        coord_range_x = buffer_piles[:, 0].max() - buffer_piles[:, 0].min()\n", "        coord_range_y = buffer_piles[:, 1].max() - buffer_piles[:, 1].min()\n", "        \n", "        print(f\"UTM coordinates (EPSG:32632):\")\n", "        print(f\"  X (Easting): {buffer_piles[:, 0].min():.1f} to {buffer_piles[:, 0].max():.1f} (range: {coord_range_x:.1f}m)\")\n", "        print(f\"  Y (Northing): {buffer_piles[:, 1].min():.1f} to {buffer_piles[:, 1].max():.1f} (range: {coord_range_y:.1f}m)\")\n", "        \n", "        # Sanity check for UTM Zone 32N coordinates\n", "        if buffer_piles[:, 0].min() < 166000 or buffer_piles[:, 0].max() > 834000:\n", "            print(\"⚠️  Warning: X coordinates outside typical UTM Zone 32N range\")\n", "        if buffer_piles[:, 1].min() < 0 or buffer_piles[:, 1].max() > 9400000:\n", "            print(\"⚠️  Warning: Y coordinates outside typical UTM Zone 32N range\")\n", "        \n", "    except Exception as e:\n", "        print(f\"❌ Error loading Buffer KML: {e}\")\n", "        print(f\"File path: {BUFFER_KML_PATH}\")\n", "        return None, None, None\n", "    \n", "    print(\"Loading IFC data (for confidence enhancement)...\")\n", "    try:\n", "        ifc_df = pd.read_csv(IFC_CSV_PATH)\n", "        \n", "        # Debug: Check IFC column names\n", "        print(f\"IFC columns available: {list(ifc_df.columns)}\")\n", "        \n", "        # Common column name patterns for coordinates\n", "        x_col = None\n", "        y_col = None\n", "        \n", "        # Try exact matches first\n", "        if 'X' in ifc_df.columns and 'Y' in ifc_df.columns:\n", "            x_col, y_col = 'X', 'Y'\n", "        elif 'x' in ifc_df.columns and 'y' in ifc_df.columns:\n", "            x_col, y_col = 'x', 'y'\n", "        elif 'Easting' in ifc_df.columns and 'Northing' in ifc_df.columns:\n", "            x_col, y_col = 'Easting', 'Northing'\n", "        elif 'easting' in ifc_df.columns and 'northing' in ifc_df.columns:\n", "            x_col, y_col = 'easting', 'northing'\n", "        else:\n", "            # Try fuzzy matching\n", "            for col in ifc_df.columns:\n", "                col_lower = col.lower()\n", "                if any(pattern in col_lower for pattern in ['x_coord', 'x_utm', 'utm_x', 'coord_x']):\n", "                    x_col = col\n", "                elif any(pattern in col_lower for pattern in ['y_coord', 'y_utm', 'utm_y', 'coord_y']):\n", "                    y_col = col\n", "                elif 'east' in col_lower and 'ing' in col_lower:\n", "                    x_col = col\n", "                elif 'north' in col_lower and 'ing' in col_lower:\n", "                    y_col = col\n", "        \n", "        if x_col and y_col:\n", "            ifc_piles = ifc_df[[x_col, y_col]].dropna().values\n", "            print(f\"✅ Loaded {len(ifc_piles):,} IFC pile locations using columns: '{x_col}', '{y_col}'\")\n", "            \n", "            # Check IFC coordinate ranges\n", "            ifc_range_x = ifc_piles[:, 0].max() - ifc_piles[:, 0].min()\n", "            ifc_range_y = ifc_piles[:, 1].max() - ifc_piles[:, 1].min()\n", "            print(f\"IFC coordinates (assuming EPSG:32632):\")\n", "            print(f\"  X (Easting): {ifc_piles[:, 0].min():.1f} to {ifc_piles[:, 0].max():.1f} (range: {ifc_range_x:.1f}m)\")\n", "            print(f\"  Y (Northing): {ifc_piles[:, 1].min():.1f} to {ifc_piles[:, 1].max():.1f} (range: {ifc_range_y:.1f}m)\")\n", "            \n", "            # Check if IFC and KML coordinates are in similar ranges\n", "            x_overlap = (max(buffer_piles[:, 0].min(), ifc_piles[:, 0].min()), \n", "                        min(buffer_piles[:, 0].max(), ifc_piles[:, 0].max()))\n", "            y_overlap = (max(buffer_piles[:, 1].min(), ifc_piles[:, 1].min()), \n", "                        min(buffer_piles[:, 1].max(), ifc_piles[:, 1].max()))\n", "            \n", "            if x_overlap[1] > x_overlap[0] and y_overlap[1] > y_overlap[0]:\n", "                print(f\"✅ KML and IFC coordinates have overlapping ranges - good!\")\n", "            else:\n", "                print(f\"⚠️  Warning: KML and IFC coordinates don't overlap - check coordinate systems\")\n", "            \n", "        else:\n", "            print(f\"❌ Could not identify X,Y coordinate columns in IFC data\")\n", "            print(f\"Available columns: {list(ifc_df.columns)}\")\n", "            print(\"Please update the column names or file path\")\n", "            ifc_piles = None\n", "            \n", "    except Exception as e:\n", "        print(f\"⚠️ Could not load IFC data: {e}\")\n", "        print(\"Continuing with Buffer KML only...\")\n", "        ifc_piles = None\n", "    \n", "    return points, buffer_piles, ifc_piles\n", "\n", "# Load all data\n", "points, buffer_piles, ifc_piles = load_trino_data()\n", "\n", "if points is None or buffer_piles is None:\n", "    print(\"❌ Failed to load required data. Please check file paths.\")\n", "    exit()\n", "\n", "# %%\n", "def filter_point_cloud_around_piles(points, pile_locations, buffer_radius=10.0):\n", "    \"\"\"Filter point cloud around confirmed piles (like RES filtering)\"\"\"\n", "    \n", "    print(f\"Filtering point cloud around {len(pile_locations)} pile locations...\")\n", "    \n", "    # Create spatial index for efficiency\n", "    pile_tree = cKDTree(pile_locations)\n", "    \n", "    # Find all points within buffer_radius of any pile\n", "    point_indices_near_piles = set()\n", "    \n", "    for i, point in enumerate(points[:, :2]):\n", "        distances, _ = pile_tree.query(point, k=1)\n", "        if distances <= buffer_radius:\n", "            point_indices_near_piles.add(i)\n", "    \n", "    filtered_indices = list(point_indices_near_piles)\n", "    filtered_points = points[filtered_indices]\n", "    \n", "    print(f\"✅ Filtered to {len(filtered_points):,} points around pile zones\")\n", "    print(f\"   Reduction: {len(points):,} → {len(filtered_points):,} points\")\n", "    \n", "    return filtered_points\n", "\n", "# Filter point cloud like successful RES approach\n", "filtered_points = filter_point_cloud_around_piles(points, buffer_piles, buffer_radius=10.0)\n", "\n", "# %%\n", "def enhance_with_ifc_confidence(buffer_piles, ifc_piles):\n", "    \"\"\"Use IFC to add confidence scores to buffer pile locations\"\"\"\n", "    \n", "    if ifc_piles is None:\n", "        print(\"No IFC data available. Using buffer piles with medium confidence.\")\n", "        return [{'x': pile[0], 'y': pile[1], 'confidence': 'medium', 'source': 'buffer_only'} \n", "                for pile in buffer_piles]\n", "    \n", "    print(f\"Enhancing {len(buffer_piles)} buffer piles with IFC confidence...\")\n", "    \n", "    ifc_tree = cKDTree(ifc_piles)\n", "    enhanced_piles = []\n", "    \n", "    confidence_counts = {'high': 0, 'medium': 0, 'low': 0}\n", "    \n", "    for i, buffer_pile in enumerate(buffer_piles):\n", "        # Find nearest IFC pile\n", "        distance, nearest_idx = ifc_tree.query(buffer_pile)\n", "        \n", "        # Assign confidence based on distance to IFC design\n", "        if distance < 2.0:\n", "            confidence = 'high'\n", "            source = 'buffer_ifc_matched'\n", "        elif distance < 5.0:\n", "            confidence = 'medium'\n", "            source = 'buffer_ifc_close'\n", "        else:\n", "            confidence = 'low'\n", "            source = 'buffer_only'\n", "        \n", "        confidence_counts[confidence] += 1\n", "        \n", "        enhanced_piles.append({\n", "            'pile_id': f'trino_pile_{i}',\n", "            'x': buffer_pile[0],\n", "            'y': buffer_pile[1],\n", "            'confidence': confidence,\n", "            'source': source,\n", "            'ifc_distance': distance,\n", "            'site': SITE_NAME\n", "        })\n", "    \n", "    print(f\"✅ Confidence distribution:\")\n", "    for conf, count in confidence_counts.items():\n", "        print(f\"   {conf.capitalize()}: {count} piles\")\n", "    \n", "    return enhanced_piles\n", "\n", "# Enhance buffer piles with IFC confidence\n", "enhanced_pile_data = enhance_with_ifc_confidence(buffer_piles, ifc_piles)\n", "\n", "# %%\n", "def extract_22_engineering_features(patch_points):\n", "    \"\"\"Extract same 22 engineering features as successful single-site approach\"\"\"\n", "    \n", "    if len(patch_points) == 0:\n", "        return np.zeros(22)\n", "    \n", "    # Patch should already be centered\n", "    x, y, z = patch_points[:, 0], patch_points[:, 1], patch_points[:, 2]\n", "    radial_dist = np.sqrt(x**2 + y**2)\n", "    height_above_min = z - np.min(z)\n", "    \n", "    try:\n", "        feature_vector = [\n", "            # Basic spatial statistics (9 features)\n", "            np.mean(x), np.std(x), np.max(x) - np.min(x),\n", "            np.mean(y), np.std(y), np.max(y) - np.min(y),\n", "            np.mean(z), np.std(z), np.max(z) - np.min(z),\n", "            \n", "            # Height-based features (4 features)\n", "            np.mean(height_above_min), np.std(height_above_min),\n", "            np.percentile(height_above_min, 75), np.percentile(height_above_min, 25),\n", "            \n", "            # Radial distance features (4 features)\n", "            np.mean(radial_dist), np.std(radial_dist),\n", "            np.min(radial_dist), np.max(radial_dist),\n", "            \n", "            # Shape and density features (5 features)\n", "            len(patch_points),\n", "            np.std(x) / (np.std(y) + 1e-6),                    # Aspect ratio\n", "            np.std(z) / (np.std(x) + np.std(y) + 1e-6),        # Height-to-width ratio\n", "            np.percentile(radial_dist, 90),                     # 90th percentile radius\n", "            np.sum(height_above_min > np.mean(height_above_min)) / len(patch_points),  # Elevated points ratio\n", "        ]\n", "        \n", "        return np.array(feature_vector, dtype=np.float32)\n", "    \n", "    except Exception as e:\n", "        print(f\"Warning: Feature extraction failed: {e}\")\n", "        return np.zeros(22, dtype=np.float32)\n", "\n", "def subsample_patch(patch_points, target_size=TARGET_PATCH_SIZE):\n", "    \"\"\"Subsample patch to target size (like single-site)\"\"\"\n", "    if len(patch_points) <= target_size:\n", "        return patch_points\n", "    \n", "    np.random.seed(42)  # For reproducibility\n", "    indices = np.random.choice(len(patch_points), target_size, replace=False)\n", "    return patch_points[indices]\n", "\n", "def extract_patch_around_pile(points, pile_data, point_tree, radius=PATCH_RADIUS):\n", "    \"\"\"Extract and process patch around pile location (single-site style)\"\"\"\n", "    \n", "    pile_coord = [pile_data['x'], pile_data['y']]\n", "    \n", "    # Find points within radius\n", "    indices = point_tree.query_ball_point(pile_coord, radius)\n", "    \n", "    if len(indices) < MIN_POINTS:\n", "        return None\n", "    \n", "    patch_points = points[indices]\n", "    \n", "    # Subsample to target size\n", "    patch_points = subsample_patch(patch_points, TARGET_PATCH_SIZE)\n", "    \n", "    # Center the patch (like single-site)\n", "    center = np.array([pile_data['x'], pile_data['y'], np.mean(patch_points[:, 2])])\n", "    centered_patch = patch_points - center\n", "    \n", "    # Extract 22 engineering features\n", "    features = extract_22_engineering_features(centered_patch)\n", "    \n", "    return {\n", "        'features': features,\n", "        'centered_patch': centered_patch,\n", "        'original_points': len(indices),\n", "        'patch_center': center\n", "    }\n", "\n", "print(\"Feature extraction functions defined\")\n", "\n", "# %%\n", "def create_trino_dataset_single_site_style(points, enhanced_pile_data):\n", "    \"\"\"Create dataset following single-site methodology\"\"\"\n", "    \n", "    print(f\"Creating dataset from {len(enhanced_pile_data)} pile locations...\")\n", "    \n", "    # Create spatial index\n", "    point_tree = cKDTree(points[:, :2])\n", "    \n", "    positive_features = []\n", "    positive_metadata = []\n", "    \n", "    # Extract positive patches (around buffer pile locations)\n", "    for pile_data in enhanced_pile_data:\n", "        patch_result = extract_patch_around_pile(points, pile_data, point_tree)\n", "        \n", "        if patch_result is not None:\n", "            positive_features.append(patch_result['features'])\n", "            positive_metadata.append({\n", "                **pile_data,\n", "                'label': 1,\n", "                'patch_type': 'positive',\n", "                'original_points': patch_result['original_points']\n", "            })\n", "    \n", "    print(f\"✅ Extracted {len(positive_features)} positive patches\")\n", "    \n", "    # Create negative samples (targeted, not random)\n", "    print(\"Creating negative samples...\")\n", "    negative_features, negative_metadata = create_negative_samples(\n", "        points, enhanced_pile_data, point_tree, n_negatives=len(positive_features)\n", "    )\n", "    \n", "    print(f\"✅ Created {len(negative_features)} negative patches\")\n", "    \n", "    # Combine positive and negative\n", "    all_features = positive_features + negative_features\n", "    all_metadata = positive_metadata + negative_metadata\n", "    all_labels = [1] * len(positive_features) + [0] * len(negative_features)\n", "    \n", "    print(f\"✅ Total dataset: {len(all_features)} samples\")\n", "    print(f\"   Positive: {len(positive_features)} ({len(positive_features)/len(all_features)*100:.1f}%)\")\n", "    print(f\"   Negative: {len(negative_features)} ({len(negative_features)/len(all_features)*100:.1f}%)\")\n", "    \n", "    return np.array(all_features), np.array(all_labels), all_metadata\n", "\n", "def create_negative_samples(points, pile_data, point_tree, n_negatives):\n", "    \"\"\"Create negative samples avoiding pile areas (like single-site)\"\"\"\n", "    \n", "    pile_coords = np.array([[p['x'], p['y']] for p in pile_data])\n", "    pile_tree = cKDTree(pile_coords)\n", "    \n", "    # Define bounds\n", "    x_min, x_max = points[:, 0].min(), points[:, 0].max()\n", "    y_min, y_max = points[:, 1].min(), points[:, 1].max()\n", "    \n", "    # Add buffer to avoid edges\n", "    buffer = 50.0\n", "    x_min += buffer\n", "    x_max -= buffer\n", "    y_min += buffer\n", "    y_max -= buffer\n", "    \n", "    negative_features = []\n", "    negative_metadata = []\n", "    \n", "    attempts = 0\n", "    max_attempts = n_negatives * 5\n", "    min_distance = PATCH_RADIUS * 2  # Stay away from pile areas\n", "    \n", "    while len(negative_features) < n_negatives and attempts < max_attempts:\n", "        # Random location\n", "        x = np.random.uniform(x_min, x_max)\n", "        y = np.random.uniform(y_min, y_max)\n", "        \n", "        # Check distance to nearest pile\n", "        dist_to_nearest_pile, _ = pile_tree.query([x, y])\n", "        \n", "        if dist_to_nearest_pile > min_distance:\n", "            # Create fake pile data for patch extraction\n", "            fake_pile = {'x': x, 'y': y, 'confidence': 'synthetic', 'source': 'negative'}\n", "            \n", "            patch_result = extract_patch_around_pile(points, fake_pile, point_tree)\n", "            \n", "            if patch_result is not None:\n", "                negative_features.append(patch_result['features'])\n", "                negative_metadata.append({\n", "                    'pile_id': f'negative_{len(negative_features)}',\n", "                    'x': x,\n", "                    'y': y,\n", "                    'confidence': 'synthetic',\n", "                    'source': 'negative_sampling',\n", "                    'label': 0,\n", "                    'patch_type': 'negative',\n", "                    'original_points': patch_result['original_points'],\n", "                    'site': SITE_NAME\n", "                })\n", "        \n", "        attempts += 1\n", "    \n", "    return negative_features, negative_metadata\n", "\n", "# Create dataset\n", "X, y, metadata = create_trino_dataset_single_site_style(filtered_points, enhanced_pile_data)\n", "\n", "# %%\n", "def organize_by_confidence(X, y, metadata):\n", "    \"\"\"Organize training data by confidence levels (like single-site quality control)\"\"\"\n", "    \n", "    confidence_data = {'high': [], 'medium': [], 'low': [], 'synthetic': []}\n", "    \n", "    for i, meta in enumerate(metadata):\n", "        conf = meta.get('confidence', 'unknown')\n", "        if conf in confidence_data:\n", "            confidence_data[conf].append(i)\n", "    \n", "    print(\"Dataset organization by confidence:\")\n", "    for conf, indices in confidence_data.items():\n", "        positive_count = sum(1 for i in indices if y[i] == 1)\n", "        negative_count = sum(1 for i in indices if y[i] == 0)\n", "        print(f\"  {conf.capitalize()}: {len(indices)} samples ({positive_count} positive, {negative_count} negative)\")\n", "    \n", "    return confidence_data\n", "\n", "def create_confidence_weighted_splits(X, y, metadata, confidence_data):\n", "    \"\"\"Create train/test splits prioritizing high-confidence data\"\"\"\n", "    \n", "    # Prioritize high and medium confidence for training\n", "    high_conf_indices = confidence_data['high']\n", "    medium_conf_indices = confidence_data['medium']\n", "    low_conf_indices = confidence_data['low']\n", "    synthetic_indices = confidence_data['synthetic']\n", "    \n", "    # Training set: All high + most medium + some synthetic\n", "    train_indices = high_conf_indices.copy()\n", "    \n", "    if len(medium_conf_indices) > 0:\n", "        # Use 80% of medium confidence for training\n", "        n_medium_train = int(0.8 * len(medium_conf_indices))\n", "        train_indices.extend(medium_conf_indices[:n_medium_train])\n", "        \n", "        # Use remaining medium confidence for validation\n", "        val_indices = medium_conf_indices[n_medium_train:]\n", "    else:\n", "        val_indices = []\n", "    \n", "    # Add synthetic negatives to both sets\n", "    n_synthetic_train = int(0.8 * len(synthetic_indices))\n", "    train_indices.extend(synthetic_indices[:n_synthetic_train])\n", "    val_indices.extend(synthetic_indices[n_synthetic_train:])\n", "    \n", "    # Test set: low confidence + some validation data\n", "    test_indices = low_conf_indices.copy()\n", "    if len(val_indices) > 20:  # Keep some validation data for testing\n", "        n_val_for_test = min(20, len(val_indices) // 2)\n", "        test_indices.extend(val_indices[-n_val_for_test:])\n", "        val_indices = val_indices[:-n_val_for_test]\n", "    \n", "    return train_indices, val_indices, test_indices\n", "\n", "# Organize data by confidence\n", "confidence_data = organize_by_confidence(X, y, metadata)\n", "\n", "# Create confidence-weighted splits\n", "train_idx, val_idx, test_idx = create_confidence_weighted_splits(X, y, metadata, confidence_data)\n", "\n", "print(f\"\\nDataset splits:\")\n", "print(f\"  Training: {len(train_idx)} samples\")\n", "print(f\"  Validation: {len(val_idx)} samples\") \n", "print(f\"  Test: {len(test_idx)} samples\")\n", "\n", "# %%\n", "def train_single_site_style_model(X_train, y_train, X_val=None, y_val=None):\n", "    \"\"\"Train model using same approach as successful single-site\"\"\"\n", "    \n", "    print(\"Training Gradient Boosting model (like successful RES approach)...\")\n", "    \n", "    # Same parameters as successful single-site\n", "    model = GradientBoostingClassifier(\n", "        n_estimators=100,\n", "        max_depth=6,\n", "        learning_rate=0.1,\n", "        random_state=42\n", "    )\n", "    \n", "    model.fit(X_train, y_train)\n", "    \n", "    # Training performance\n", "    train_pred = model.predict(X_train)\n", "    train_accuracy = accuracy_score(y_train, train_pred)\n", "    print(f\"✅ Training accuracy: {train_accuracy:.3f}\")\n", "    \n", "    # Validation performance (if available)\n", "    if X_val is not None and y_val is not None and len(X_val) > 0:\n", "        val_pred = model.predict(X_val)\n", "        val_accuracy = accuracy_score(y_val, val_pred)\n", "        print(f\"✅ Validation accuracy: {val_accuracy:.3f}\")\n", "    \n", "    return model\n", "\n", "# Prepare training data\n", "X_train = X[train_idx]\n", "y_train = y[train_idx]\n", "\n", "X_val = X[val_idx] if len(val_idx) > 0 else None\n", "y_val = y[val_idx] if len(val_idx) > 0 else None\n", "\n", "X_test = X[test_idx] if len(test_idx) > 0 else None\n", "y_test = y[test_idx] if len(test_idx) > 0 else None\n", "\n", "# Train model\n", "model = train_single_site_style_model(X_train, y_train, X_val, y_val)\n", "\n", "# %%\n", "def validate_on_known_pile_locations(model, pile_locations, points):\n", "    \"\"\"Apply single-site validation: test detection on known pile locations\"\"\"\n", "    \n", "    print(f\"Validating on {len(pile_locations)} known pile locations...\")\n", "    \n", "    point_tree = cKDTree(points[:, :2])\n", "    detected_piles = []\n", "    detection_confidences = []\n", "    failed_extractions = 0\n", "    \n", "    for pile_data in pile_locations:\n", "        patch_result = extract_patch_around_pile(points, pile_data, point_tree)\n", "        \n", "        if patch_result is not None:\n", "            # Get prediction\n", "            features = patch_result['features'].reshape(1, -1)\n", "            prediction = model.predict(features)[0]\n", "            confidence = model.predict_proba(features)[0][1]\n", "            \n", "            detected_piles.append(prediction)\n", "            detection_confidences.append(confidence)\n", "        else:\n", "            failed_extractions += 1\n", "    \n", "    # Calculate metrics like single-site\n", "    detection_rate = np.mean(detected_piles) if detected_piles else 0\n", "    avg_confidence = np.mean(detection_confidences) if detection_confidences else 0\n", "    \n", "    # Single-site inspired assessment\n", "    if detection_rate >= 0.9:\n", "        status = \"EXCELLENT\"\n", "        color = \"🟢\"\n", "    elif detection_rate >= 0.8:\n", "        status = \"GOOD\"\n", "        color = \"🟡\"\n", "    elif detection_rate >= 0.6:\n", "        status = \"MODERATE\"\n", "        color = \"🟠\"\n", "    else:\n", "        status = \"POOR\"\n", "        color = \"🔴\"\n", "    \n", "    print(f\"\\n{color} GROUND TRUTH VALIDATION RESULTS:\")\n", "    print(f\"  Detection rate: {detection_rate*100:.1f}% ({np.sum(detected_piles)}/{len(detected_piles)})\")\n", "    print(f\"  Average confidence: {avg_confidence:.3f}\")\n", "    print(f\"  Status: {status}\")\n", "    print(f\"  Failed extractions: {failed_extractions}\")\n", "    \n", "    return {\n", "        'detection_rate': detection_rate,\n", "        'avg_confidence': avg_confidence,\n", "        'status': status,\n", "        'detected_piles': detected_piles,\n", "        'detection_confidences': detection_confidences,\n", "        'failed_extractions': failed_extractions,\n", "        'total_tested': len(pile_locations)\n", "    }\n", "\n", "# Validate on known pile locations (like successful RES approach)\n", "pile_validation_results = validate_on_known_pile_locations(\n", "    model, enhanced_pile_data, filtered_points\n", ")\n", "\n", "# %%\n", "def evaluate_test_set_performance(model, X_test, y_test, test_metadata):\n", "    \"\"\"Evaluate performance on test set\"\"\"\n", "    \n", "    if X_test is None or len(X_test) == 0:\n", "        print(\"No test set available\")\n", "        return None\n", "    \n", "    print(f\"\\nEvaluating on test set ({len(X_test)} samples)...\")\n", "    \n", "    y_pred = model.predict(X_test)\n", "    y_prob = model.predict_proba(X_test)[:, 1]\n", "    \n", "    # Calculate metrics\n", "    accuracy = accuracy_score(y_test, y_pred)\n", "    f1 = f1_score(y_test, y_pred)\n", "    precision = precision_score(y_test, y_pred)\n", "    recall = recall_score(y_test, y_pred)\n", "    \n", "    print(f\"Test Set Performance:\")\n", "    print(f\"  Accuracy: {accuracy:.3f}\")\n", "    print(f\"  F1-Score: {f1:.3f}\")\n", "    print(f\"  Precision: {precision:.3f}\")\n", "    print(f\"  Recall: {recall:.3f}\")\n", "    \n", "    # Break down by confidence level\n", "    test_meta = [metadata[i] for i in test_idx]\n", "    confidence_performance = {}\n", "    \n", "    for conf in ['high', 'medium', 'low', 'synthetic']:\n", "        conf_indices = [i for i, meta in enumerate(test_meta) \n", "                       if meta.get('confidence') == conf]\n", "        \n", "        if conf_indices:\n", "            conf_y_true = y_test[conf_indices]\n", "            conf_y_pred = y_pred[conf_indices]\n", "            conf_acc = accuracy_score(conf_y_true, conf_y_pred)\n", "            confidence_performance[conf] = {\n", "                'accuracy': conf_acc,\n", "                'count': len(conf_indices)\n", "            }\n", "            print(f\"  {conf.capitalize()} confidence: {conf_acc:.3f} ({len(conf_indices)} samples)\")\n", "    \n", "    return {\n", "        'accuracy': accuracy,\n", "        'f1_score': f1,\n", "        'precision': precision,\n", "        'recall': recall,\n", "        'confidence_breakdown': confidence_performance\n", "    }\n", "\n", "# Evaluate test set\n", "if X_test is not None:\n", "    test_results = evaluate_test_set_performance(model, X_test, y_test, [metadata[i] for i in test_idx])\n", "else:\n", "    print(\"No test set available for evaluation\")\n", "    test_results = None\n", "\n", "# %%\n", "def analyze_feature_importance(model, feature_names=None):\n", "    \"\"\"Analyze which features are most important for pile detection\"\"\"\n", "    \n", "    if feature_names is None:\n", "        feature_names = [\n", "            'x_mean', 'x_std', 'x_range',\n", "            'y_mean', 'y_std', 'y_range', \n", "            'z_mean', 'z_std', 'z_range',\n", "            'height_mean', 'height_std', 'height_75p', 'height_25p',\n", "            'radial_mean', 'radial_std', 'radial_min', 'radial_max',\n", "            'point_count', 'aspect_ratio', 'height_width_ratio', 'radial_90p', 'elevated_ratio'\n", "        ]\n", "    \n", "    importances = model.feature_importances_\n", "    feature_importance_df = pd.DataFrame({\n", "        'feature': feature_names,\n", "        'importance': importances\n", "    }).sort_values('importance', ascending=False)\n", "    \n", "    print(\"\\nTop 10 Most Important Features:\")\n", "    for i, row in feature_importance_df.head(10).iterrows():\n", "        print(f\"  {row['feature']}: {row['importance']:.3f}\")\n", "    \n", "    return feature_importance_df\n", "\n", "# Analyze feature importance\n", "feature_importance = analyze_feature_importance(model)\n", "\n", "# %%\n", "def save_trino_results(results_dict, output_dir):\n", "    \"\"\"Save all results for future reference\"\"\"\n", "    \n", "    timestamp = datetime.now().strftime(\"%Y%m%d_%H%M%S\")\n", "    \n", "    # Save main results\n", "    results_file = output_dir / f\"trino_single_site_results_{timestamp}.json\"\n", "    with open(results_file, 'w') as f:\n", "        json.dump(results_dict, f, indent=2, default=str)\n", "    \n", "    # Save model\n", "    model_file = output_dir / f\"trino_pile_detection_model_{timestamp}.pkl\"\n", "    with open(model_file, 'wb') as f:\n", "        pickle.dump(model, f)\n", "    \n", "    # Save feature importance\n", "    feature_importance.to_csv(output_dir / f\"feature_importance_{timestamp}.csv\", index=False)\n", "    \n", "    print(f\"\\n✅ Results saved to {output_dir}\")\n", "    print(f\"  Main results: {results_file.name}\")\n", "    print(f\"  Model: {model_file.name}\")\n", "    print(f\"  Feature importance: feature_importance_{timestamp}.csv\")\n", "    \n", "    return results_file\n", "\n", "# Compile all results\n", "all_results = {\n", "    'site_name': SITE_NAME,\n", "    'timestamp': datetime.now().isoformat(),\n", "    'approach': 'single_site_style_adaptation',\n", "    'configuration': {\n", "        'patch_radius': PATCH_RADIUS,\n", "        'target_patch_size': TARGET_PATCH_SIZE,\n", "        'min_points': MIN_POINTS,\n", "        'algorithm': 'GradientBoostingClassifier'\n", "    },\n", "    'data_summary': {\n", "        'total_points': len(points),\n", "        'filtered_points': len(filtered_points),\n", "        'buffer_piles': len(buffer_piles),\n", "        'ifc_piles': len(ifc_piles) if ifc_piles is not None else 0,\n", "        'enhanced_piles': len(enhanced_pile_data),\n", "        'total_samples': len(X),\n", "        'positive_samples': int(np.sum(y)),\n", "        'negative_samples': int(len(y) - np.sum(y))\n", "    },\n", "    'pile_validation': pile_validation_results,\n", "    'test_performance': test_results,\n", "    'feature_importance': feature_importance.to_dict('records'),\n", "    'confidence_distribution': {conf: len(indices) for conf, indices in confidence_data.items()}\n", "}\n", "\n", "# Save results\n", "results_file = save_trino_results(all_results, output_dir)\n", "\n", "# %%\n", "# Replace the create_qgis_export function with this version\n", "\n", "def create_qgis_export(enhanced_pile_data, detection_results, output_dir):\n", "    \"\"\"Create QGIS-ready export for field validation (optimized for EPSG:32632)\"\"\"\n", "    \n", "    print(\"Creating QGIS export...\")\n", "    \n", "    # Prepare results DataFrame\n", "    qgis_data = []\n", "    \n", "    for i, pile_data in enumerate(enhanced_pile_data):\n", "        if i < len(detection_results['detected_piles']):\n", "            detected = detection_results['detected_piles'][i]\n", "            confidence = detection_results['detection_confidences'][i]\n", "            detection_status = 'Detected' if detected == 1 else 'Missed'\n", "        else:\n", "            detected = 0\n", "            confidence = 0.0\n", "            detection_status = 'Not Tested'\n", "        \n", "        qgis_data.append({\n", "            'pile_id': pile_data['pile_id'],\n", "            'utm_x': pile_data['x'],\n", "            'utm_y': pile_data['y'],\n", "            'confidence_level': pile_data['confidence'],\n", "            'source': pile_data['source'],\n", "            'predicted_pile': detected,\n", "            'prediction_confidence': confidence,\n", "            'detection_status': detection_status,\n", "            'site_name': SITE_NAME,\n", "            'validation_type': 'single_site_style',\n", "            'ifc_distance': pile_data.get('ifc_distance', -1),\n", "            'crs': TARGET_CRS\n", "        })\n", "    \n", "    results_df = pd.DataFrame(qgis_data)\n", "    \n", "    # Convert to geographic coordinates for QGIS\n", "    try:\n", "        from shapely.geometry import Point\n", "        \n", "        # Create GeoDataFrame with UTM coordinates\n", "        geometry = [Point(row['utm_x'], row['utm_y']) for _, row in results_df.iterrows()]\n", "        gdf = gpd.GeoDataFrame(results_df, geometry=geometry, crs=TARGET_CRS)\n", "        \n", "        # Convert to WGS84 for QGIS compatibility\n", "        gdf_wgs84 = gdf.to_crs('EPSG:4326')\n", "        results_df['longitude'] = gdf_wgs84.geometry.x\n", "        results_df['latitude'] = gdf_wgs84.geometry.y\n", "        \n", "        print(f\"✅ Converted coordinates from {TARGET_CRS} to WGS84\")\n", "        \n", "    except Exception as e:\n", "        print(f\"⚠️ Warning: Could not add geographic coordinates: {e}\")\n", "        results_df['longitude'] = 0.0\n", "        results_df['latitude'] = 0.0\n", "    \n", "    # Save CSV for QGIS\n", "    timestamp = datetime.now().strftime(\"%Y%m%d_%H%M%S\")\n", "    csv_file = output_dir / f\"trino_pile_detection_qgis_{timestamp}.csv\"\n", "    results_df.to_csv(csv_file, index=False)\n", "    \n", "    # Summary statistics\n", "    total_tested = len([d for d in detection_results['detected_piles']])\n", "    total_detected = sum(detection_results['detected_piles'])\n", "    detection_rate = total_detected / total_tested if total_tested > 0 else 0\n", "    \n", "    print(f\"✅ QGIS export created: {csv_file.name}\")\n", "    print(f\"   Coordinate system: {TARGET_CRS} (UTM) + WGS84 (Geographic)\")\n", "    print(f\"   Total piles tested: {total_tested}\")\n", "    print(f\"   Piles detected: {total_detected}\")\n", "    print(f\"   Detection rate: {detection_rate*100:.1f}%\")\n", "    \n", "    return csv_file, results_df\n", "\n", "# Create QGIS export\n", "qgis_file, qgis_df = create_qgis_export(enhanced_pile_data, pile_validation_results, output_dir)\n", "\n", "# %%\n", "def visualize_results(qgis_df, pile_validation_results, output_dir):\n", "    \"\"\"Create visualization of results\"\"\"\n", "    \n", "    fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(15, 12))\n", "    \n", "    # Plot 1: Confidence distribution\n", "    conf_counts = qgis_df['confidence_level'].value_counts()\n", "    ax1.bar(conf_counts.index, conf_counts.values, alpha=0.7, color=['green', 'orange', 'red', 'gray'])\n", "    ax1.set_title('Pile Confidence Distribution')\n", "    ax1.set_xlabel('Confidence Level')\n", "    ax1.set_ylabel('Number of Piles')\n", "    ax1.grid(True, alpha=0.3)\n", "    \n", "    # Plot 2: Detection status\n", "    detection_counts = qgis_df['detection_status'].value_counts()\n", "    colors = ['green' if 'Detected' in status else 'red' for status in detection_counts.index]\n", "    ax2.bar(detection_counts.index, detection_counts.values, alpha=0.7, color=colors)\n", "    ax2.set_title('Detection Results')\n", "    ax2.set_xlabel('Detection Status')\n", "    ax2.set_ylabel('Number of Piles')\n", "    ax2.grid(True, alpha=0.3)\n", "    \n", "    # Plot 3: Confidence vs Detection Rate\n", "    if len(pile_validation_results['detection_confidences']) > 0:\n", "        detected = np.array(pile_validation_results['detected_piles'])\n", "        confidences = np.array(pile_validation_results['detection_confidences'])\n", "        \n", "        # Scatter plot\n", "        colors = ['green' if d == 1 else 'red' for d in detected]\n", "        ax3.scatter(confidences, detected + np.random.normal(0, 0.02, len(detected)), \n", "                   c=colors, alpha=0.6)\n", "        ax3.set_title('Prediction Confidence vs Detection')\n", "        ax3.set_xlabel('Prediction Confidence')\n", "        ax3.set_ylabel('Detected (1) / Missed (0)')\n", "        ax3.grid(True, alpha=0.3)\n", "        ax3.set_ylim(-0.1, 1.1)\n", "    \n", "    # Plot 4: Feature importance (top 10)\n", "    top_features = feature_importance.head(10)\n", "    ax4.barh(range(len(top_features)), top_features['importance'])\n", "    ax4.set_yticks(range(len(top_features)))\n", "    ax4.set_yticklabels(top_features['feature'])\n", "    ax4.set_title('Top 10 Feature Importances')\n", "    ax4.set_xlabel('Importance')\n", "    ax4.grid(True, alpha=0.3)\n", "    \n", "    plt.tight_layout()\n", "    \n", "    # Save plot\n", "    plot_file = output_dir / f\"trino_results_visualization_{datetime.now().strftime('%Y%m%d_%H%M%S')}.png\"\n", "    plt.savefig(plot_file, dpi=300, bbox_inches='tight')\n", "    plt.show()\n", "    \n", "    print(f\"✅ Visualization saved: {plot_file.name}\")\n", "    return plot_file\n", "\n", "# Create visualization\n", "plot_file = visualize_results(qgis_df, pile_validation_results, output_dir)\n", "\n", "# %%\n", "def print_final_summary():\n", "    \"\"\"Print final summary of results\"\"\"\n", "    \n", "    print(\"\\n\" + \"=\"*60)\n", "    print(\"🎯 TRINO SINGLE-SITE STYLE RESULTS SUMMARY\")\n", "    print(\"=\"*60)\n", "    \n", "    print(f\"\\n📊 Dataset Summary:\")\n", "    print(f\"   Site: {SITE_NAME}\")\n", "    print(f\"   Total point cloud: {len(points):,} points\")\n", "    print(f\"   Filtered around piles: {len(filtered_points):,} points\")\n", "    print(f\"   Buffer pile locations: {len(buffer_piles)}\")\n", "    print(f\"   IFC pile locations: {len(ifc_piles) if ifc_piles is not None else 0:,}\")\n", "    print(f\"   Final dataset: {len(X)} samples ({np.sum(y)} positive, {len(y) - np.sum(y)} negative)\")\n", "    \n", "    print(f\"\\n🎯 Ground Truth Validation (KEY METRIC):\")\n", "    print(f\"   Detection rate: {pile_validation_results['detection_rate']*100:.1f}%\")\n", "    print(f\"   Average confidence: {pile_validation_results['avg_confidence']:.3f}\")\n", "    print(f\"   Status: {pile_validation_results['status']}\")\n", "    print(f\"   <PERSON>les tested: {pile_validation_results['total_tested']}\")\n", "    \n", "    if test_results:\n", "        print(f\"\\n📈 Test Set Performance:\")\n", "        print(f\"   Accuracy: {test_results['accuracy']:.3f}\")\n", "        print(f\"   F1-Score: {test_results['f1_score']:.3f}\")\n", "        print(f\"   Precision: {test_results['precision']:.3f}\")\n", "        print(f\"   Recall: {test_results['recall']:.3f}\")\n", "    \n", "    print(f\"\\n🔧 Configuration Used (Single-Site Style):\")\n", "    print(f\"   Patch radius: {PATCH_RADIUS}m (vs 8-20m in failed approaches)\")\n", "    print(f\"   Points per patch: {TARGET_PATCH_SIZE} (vs 1024 in failed approaches)\")\n", "    print(f\"   Features: 22 engineering features (vs 20 statistical in failed approaches)\")\n", "    print(f\"   Algorithm: Gradient Boosting (vs PointNet++ in failed approaches)\")\n", "    print(f\"   Validation: Ground truth detection rate (vs abstract accuracy in failed approaches)\")\n", "    \n", "    print(f\"\\n📁 Output Files:\")\n", "    print(f\"   Results: {results_file.name}\")\n", "    print(f\"   QGIS export: {qgis_file.name}\")\n", "    print(f\"   Visualization: {plot_file.name}\")\n", "    print(f\"   All files in: {output_dir}\")\n", "    \n", "    # Success assessment\n", "    detection_rate = pile_validation_results['detection_rate']\n", "    if detection_rate >= 0.9:\n", "        status_msg = \"🟢 EXCELLENT - Ready for production use!\"\n", "    elif detection_rate >= 0.8:\n", "        status_msg = \"🟡 GOOD - Suitable for field validation with human oversight\"\n", "    elif detection_rate >= 0.6:\n", "        status_msg = \"🟠 MODERATE - Needs improvement before field use\"\n", "    else:\n", "        status_msg = \"🔴 POOR - Requires significant refinement\"\n", "    \n", "    print(f\"\\n🏆 FINAL ASSESSMENT:\")\n", "    print(f\"   {status_msg}\")\n", "    \n", "    if detection_rate >= 0.8:\n", "        print(f\"\\n✅ SUCCESS: This approach shows significant improvement over previous failed attempts!\")\n", "        print(f\"   • Used Buffer KML as ground truth (like successful RES)\")\n", "        print(f\"   • Applied single-site parameters (3m patches, 64 points)\")\n", "        print(f\"   • Used engineering features (22 interpretable features)\")\n", "        print(f\"   • Applied classical ML (Gradient Boosting)\")\n", "        print(f\"   • Validated on ground truth (detection rate on known piles)\")\n", "    else:\n", "        print(f\"\\n⚠️  NEEDS IMPROVEMENT:\")\n", "        print(f\"   • Consider refining patch parameters\")\n", "        print(f\"   • Check data quality and preprocessing\")\n", "        print(f\"   • Analyze failed detections for patterns\")\n", "        print(f\"   • Ensure point cloud filtering is appropriate\")\n", "    \n", "    print(\"\\n\" + \"=\"*60)\n", "\n", "# Print final summary\n", "print_final_summary()\n", "\n", "# %%\n", "print(\"🎉 TRINO SINGLE-SITE STYLE DATA PREPARATION COMPLETE!\")\n", "print(f\"📂 All results saved in: {output_dir}\")\n", "print(f\"📊 Ground truth detection rate: {pile_validation_results['detection_rate']*100:.1f}%\")\n", "print(f\"🎯 Status: {pile_validation_results['status']}\")\n", "print(\"\\nNext steps:\")\n", "print(\"1. Review QGIS export for field validation\")\n", "print(\"2. Analy<PERSON> feature importance for insights\")\n", "print(\"3. If successful, apply to other sites\")\n", "print(\"4. If needs improvement, refine based on failed detection analysis\")"]}, {"cell_type": "code", "execution_count": 3, "id": "ccab2630", "metadata": {}, "outputs": [], "source": ["# Replace the extract_22_engineering_features() and extract_patch_around_pile() functions with these unbiased versions\n", "\n", "def extract_22_engineering_features_unbiased(patch_points):\n", "    \"\"\"Extract 22 engineering features WITHOUT pile-centered bias\"\"\"\n", "    \n", "    if len(patch_points) == 0:\n", "        return np.zeros(22)\n", "    \n", "    # DON'T assume patch is centered on pile - work with raw coordinates\n", "    x, y, z = patch_points[:, 0], patch_points[:, 1], patch_points[:, 2]\n", "    \n", "    # Calculate patch characteristics from the data itself\n", "    patch_center_x = np.mean(x)\n", "    patch_center_y = np.mean(y)\n", "    patch_center_z = np.mean(z)\n", "    \n", "    # Distances from DATA center, not pile center\n", "    radial_dist = np.sqrt((x - patch_center_x)**2 + (y - patch_center_y)**2)\n", "    height_above_min = z - np.min(z)\n", "    \n", "    try:\n", "        feature_vector = [\n", "            # Spatial spread (9 features) - NOT relative position\n", "            np.std(x), np.std(y), np.std(z),                    # Spread, not mean position\n", "            np.max(x) - np.min(x),                              # X range\n", "            np.max(y) - np.min(y),                              # Y range  \n", "            np.max(z) - np.min(z),                              # Z range\n", "            np.percentile(x, 75) - np.percentile(x, 25),        # X IQR\n", "            np.percentile(y, 75) - np.percentile(y, 25),        # Y IQR\n", "            np.percentile(z, 75) - np.percentile(z, 25),        # Z IQR\n", "            \n", "            # Height-based features (4 features)\n", "            np.mean(height_above_min), np.std(height_above_min),\n", "            np.percentile(height_above_min, 75), np.percentile(height_above_min, 25),\n", "            \n", "            # Radial distribution from patch center (4 features)\n", "            np.mean(radial_dist), np.std(radial_dist),\n", "            np.min(radial_dist), np.max(radial_dist),\n", "            \n", "            # Shape and density features (5 features)\n", "            len(patch_points),                                  # Point count\n", "            np.std(x) / (np.std(y) + 1e-6),                    # Aspect ratio\n", "            np.std(z) / (np.std(x) + np.std(y) + 1e-6),        # Height-to-width ratio\n", "            np.percentile(radial_dist, 90),                     # 90th percentile radius\n", "            np.sum(height_above_min > np.mean(height_above_min)) / len(patch_points),  # Elevated points ratio\n", "        ]\n", "        \n", "        return np.array(feature_vector, dtype=np.float32)\n", "    \n", "    except Exception as e:\n", "        print(f\"Warning: Unbiased feature extraction failed: {e}\")\n", "        return np.zeros(22, dtype=np.float32)\n", "\n", "def extract_patch_around_location_unbiased(points, location_coord, point_tree, radius=PATCH_RADIUS):\n", "    \"\"\"Extract patch around location WITHOUT centering (unbiased)\"\"\"\n", "    \n", "    # Find points within radius\n", "    indices = point_tree.query_ball_point(location_coord, radius)\n", "    \n", "    if len(indices) < MIN_POINTS:\n", "        return None\n", "    \n", "    patch_points = points[indices]\n", "    \n", "    # Subsample to target size\n", "    patch_points = subsample_patch(patch_points, TARGET_PATCH_SIZE)\n", "    \n", "    # DON'T center the patch - use raw coordinates\n", "    # This removes pile-location bias\n", "    \n", "    # Extract unbiased features\n", "    features = extract_22_engineering_features_unbiased(patch_points)\n", "    \n", "    return {\n", "        'features': features,\n", "        'raw_patch': patch_points,\n", "        'original_points': len(indices),\n", "        'patch_location': location_coord\n", "    }\n", "\n", "def create_trino_dataset_unbiased(points, enhanced_pile_data):\n", "    \"\"\"Create dataset with unbiased feature extraction\"\"\"\n", "    \n", "    print(f\"Creating UNBIASED dataset from {len(enhanced_pile_data)} pile locations...\")\n", "    \n", "    # Create spatial index\n", "    point_tree = cKDTree(points[:, :2])\n", "    \n", "    positive_features = []\n", "    positive_metadata = []\n", "    \n", "    # Extract positive patches (around buffer pile locations) - UNBIASED\n", "    for pile_data in enhanced_pile_data:\n", "        location_coord = [pile_data['x'], pile_data['y']]\n", "        patch_result = extract_patch_around_location_unbiased(points, location_coord, point_tree)\n", "        \n", "        if patch_result is not None:\n", "            positive_features.append(patch_result['features'])\n", "            positive_metadata.append({\n", "                **pile_data,\n", "                'label': 1,\n", "                'patch_type': 'positive',\n", "                'original_points': patch_result['original_points']\n", "            })\n", "    \n", "    print(f\"✅ Extracted {len(positive_features)} positive patches (UNBIASED)\")\n", "    \n", "    # Create negative samples (targeted, not random) - UNBIASED\n", "    print(\"Creating negative samples (UNBIASED)...\")\n", "    negative_features, negative_metadata = create_negative_samples_unbiased(\n", "        points, enhanced_pile_data, point_tree, n_negatives=len(positive_features)\n", "    )\n", "    \n", "    print(f\"✅ Created {len(negative_features)} negative patches (UNBIASED)\")\n", "    \n", "    # Combine positive and negative\n", "    all_features = positive_features + negative_features\n", "    all_metadata = positive_metadata + negative_metadata\n", "    all_labels = [1] * len(positive_features) + [0] * len(negative_features)\n", "    \n", "    print(f\"✅ Total UNBIASED dataset: {len(all_features)} samples\")\n", "    print(f\"   Positive: {len(positive_features)} ({len(positive_features)/len(all_features)*100:.1f}%)\")\n", "    print(f\"   Negative: {len(negative_features)} ({len(negative_features)/len(all_features)*100:.1f}%)\")\n", "    \n", "    return np.array(all_features), np.array(all_labels), all_metadata\n", "\n", "def create_negative_samples_unbiased(points, pile_data, point_tree, n_negatives):\n", "    \"\"\"Create negative samples avoiding pile areas (UNBIASED)\"\"\"\n", "    \n", "    pile_coords = np.array([[p['x'], p['y']] for p in pile_data])\n", "    pile_tree = cKDTree(pile_coords)\n", "    \n", "    # Define bounds\n", "    x_min, x_max = points[:, 0].min(), points[:, 0].max()\n", "    y_min, y_max = points[:, 1].min(), points[:, 1].max()\n", "    \n", "    # Add buffer to avoid edges\n", "    buffer = 50.0\n", "    x_min += buffer\n", "    x_max -= buffer\n", "    y_min += buffer\n", "    y_max -= buffer\n", "    \n", "    negative_features = []\n", "    negative_metadata = []\n", "    \n", "    attempts = 0\n", "    max_attempts = n_negatives * 5\n", "    min_distance = PATCH_RADIUS * 2  # Stay away from pile areas\n", "    \n", "    while len(negative_features) < n_negatives and attempts < max_attempts:\n", "        # Random location\n", "        x = np.random.uniform(x_min, x_max)\n", "        y = np.random.uniform(y_min, y_max)\n", "        location_coord = [x, y]\n", "        \n", "        # Check distance to nearest pile\n", "        dist_to_nearest_pile, _ = pile_tree.query(location_coord)\n", "        \n", "        if dist_to_nearest_pile > min_distance:\n", "            patch_result = extract_patch_around_location_unbiased(points, location_coord, point_tree)\n", "            \n", "            if patch_result is not None:\n", "                negative_features.append(patch_result['features'])\n", "                negative_metadata.append({\n", "                    'pile_id': f'negative_{len(negative_features)}',\n", "                    'x': x,\n", "                    'y': y,\n", "                    'confidence': 'synthetic',\n", "                    'source': 'negative_sampling',\n", "                    'label': 0,\n", "                    'patch_type': 'negative',\n", "                    'original_points': patch_result['original_points'],\n", "                    'site': SITE_NAME\n", "                })\n", "        \n", "        attempts += 1\n", "    \n", "    return negative_features, negative_metadata\n", "\n", "def validate_on_known_pile_locations_unbiased(model, pile_locations, points):\n", "    \"\"\"Apply unbiased validation: test detection on known pile locations\"\"\"\n", "    \n", "    print(f\"Validating on {len(pile_locations)} known pile locations (UNBIASED)...\")\n", "    \n", "    point_tree = cKDTree(points[:, :2])\n", "    detected_piles = []\n", "    detection_confidences = []\n", "    failed_extractions = 0\n", "    \n", "    for pile_data in pile_locations:\n", "        location_coord = [pile_data['x'], pile_data['y']]\n", "        patch_result = extract_patch_around_location_unbiased(points, location_coord, point_tree)\n", "        \n", "        if patch_result is not None:\n", "            # Get prediction\n", "            features = patch_result['features'].reshape(1, -1)\n", "            prediction = model.predict(features)[0]\n", "            confidence = model.predict_proba(features)[0][1]\n", "            \n", "            detected_piles.append(prediction)\n", "            detection_confidences.append(confidence)\n", "        else:\n", "            failed_extractions += 1\n", "    \n", "    # Calculate metrics\n", "    detection_rate = np.mean(detected_piles) if detected_piles else 0\n", "    avg_confidence = np.mean(detection_confidences) if detection_confidences else 0\n", "    \n", "    # Assessment\n", "    if detection_rate >= 0.9:\n", "        status = \"EXCELLENT\"\n", "        color = \"🟢\"\n", "    elif detection_rate >= 0.8:\n", "        status = \"GOOD\"\n", "        color = \"🟡\"\n", "    elif detection_rate >= 0.6:\n", "        status = \"MODERATE\"\n", "        color = \"🟠\"\n", "    else:\n", "        status = \"POOR\"\n", "        color = \"🔴\"\n", "    \n", "    print(f\"\\n{color} UNBIASED GROUND TRUTH VALIDATION RESULTS:\")\n", "    print(f\"  Detection rate: {detection_rate*100:.1f}% ({np.sum(detected_piles)}/{len(detected_piles)})\")\n", "    print(f\"  Average confidence: {avg_confidence:.3f}\")\n", "    print(f\"  Status: {status}\")\n", "    print(f\"  Failed extractions: {failed_extractions}\")\n", "    \n", "    return {\n", "        'detection_rate': detection_rate,\n", "        'avg_confidence': avg_confidence,\n", "        'status': status,\n", "        'detected_piles': detected_piles,\n", "        'detection_confidences': detection_confidences,\n", "        'failed_extractions': failed_extractions,\n", "        'total_tested': len(pile_locations)\n", "    }"]}, {"cell_type": "code", "execution_count": 4, "id": "709ac4de", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["🔬 TESTING BIASED vs UNBIASED FEATURE EXTRACTION\n", "============================================================\n", "\n", "1️⃣ Creating UNBIASED dataset...\n", "Creating UNBIASED dataset from 1288 pile locations...\n", "✅ Extracted 203 positive patches (UNBIASED)\n", "Creating negative samples (UNBIASED)...\n", "✅ Created 7 negative patches (UNBIASED)\n", "✅ Total UNBIASED dataset: 210 samples\n", "   Positive: 203 (96.7%)\n", "   Negative: 7 (3.3%)\n", "\n", "2️⃣ Training UNBIASED model...\n", "Dataset organization by confidence:\n", "  High: 183 samples (183 positive, 0 negative)\n", "  Medium: 15 samples (15 positive, 0 negative)\n", "  Low: 5 samples (5 positive, 0 negative)\n", "  Synthetic: 7 samples (0 positive, 7 negative)\n", "Training Gradient Boosting model (like successful RES approach)...\n", "✅ Training accuracy: 1.000\n", "✅ Validation accuracy: 0.600\n", "\n", "3️⃣ Validating UNBIASED model...\n", "Validating on 1288 known pile locations (UNBIASED)...\n", "\n", "🟢 UNBIASED GROUND TRUTH VALIDATION RESULTS:\n", "  Detection rate: 100.0% (203/203)\n", "  Average confidence: 1.000\n", "  Status: EXCELLENT\n", "  Failed extractions: 1085\n", "\n", "4️⃣ COMPARISON RESULTS:\n", "========================================\n", "\n", "📊 DATASET COMPARISON:\n", "   Biased dataset:   208 samples (203 positive)\n", "   Unbiased dataset: 210 samples (203 positive)\n", "\n", "🎯 DETECTION RATE COMPARISON:\n", "   Biased approach:   99.5% (EXCELLENT)\n", "   Unbiased approach: 100.0% (EXCELLENT)\n", "\n", "📈 CONFIDENCE COMPARISON:\n", "   Biased approach:   0.995\n", "   Unbiased approach: 1.000\n", "\n", "⚠️ FAILED EXTRACTIONS:\n", "   Biased approach:   1085\n", "   Unbiased approach: 1085\n", "\n", "🔍 FEATURE IMPORTANCE COMPARISON:\n", "\n", "Top 10 Most Important Features:\n", "  radial_max: 0.259\n", "  x_std: 0.232\n", "  y_range: 0.156\n", "  radial_std: 0.107\n", "  point_count: 0.079\n", "  z_std: 0.037\n", "  radial_mean: 0.035\n", "  aspect_ratio: 0.030\n", "  x_range: 0.015\n", "  height_width_ratio: 0.012\n", "\n", "Top 5 features - BIASED approach:\n", "   aspect_ratio: 0.218\n", "   y_range: 0.200\n", "   height_width_ratio: 0.156\n", "   z_mean: 0.128\n", "   height_25p: 0.118\n", "\n", "Top 5 features - UNBIASED approach:\n", "   radial_max: 0.259\n", "   x_std: 0.232\n", "   y_range: 0.156\n", "   radial_std: 0.107\n", "   point_count: 0.079\n", "\n", "🏆 RECOMMENDATION:\n", "   📊 SIMILAR PERFORMANCE: Both approaches achieve similar results\n", "   ✅ This suggests the methodology is robust\n", "   💡 CONCLUSION: Use BIASED approach for pile verification tasks\n", "      (When you have suspected pile locations to verify)\n", "\n", "📝 METHODOLOGY NOTE:\n", "   • Biased approach: Suitable for PILE VERIFICATION (known locations)\n", "   • Unbiased approach: Suitable for PILE DISCOVERY (unknown locations)\n", "   • Your use case determines which approach to use\n", "\n", "============================================================\n"]}], "source": ["# Add this cell to compare both approaches\n", "\n", "print(\"🔬 TESTING BIASED vs UNBIASED FEATURE EXTRACTION\")\n", "print(\"=\"*60)\n", "\n", "# Test 1: Create unbiased dataset\n", "print(\"\\n1️⃣ Creating UNBIASED dataset...\")\n", "X_unbiased, y_unbiased, metadata_unbiased = create_trino_dataset_unbiased(\n", "    filtered_points, enhanced_pile_data\n", ")\n", "\n", "# Test 2: Train unbiased model\n", "print(\"\\n2️⃣ Training UNBIASED model...\")\n", "\n", "# Use same train/test split approach for fair comparison\n", "confidence_data_unbiased = organize_by_confidence(X_unbiased, y_unbiased, metadata_unbiased)\n", "train_idx_unbiased, val_idx_unbiased, test_idx_unbiased = create_confidence_weighted_splits(\n", "    X_unbiased, y_unbiased, metadata_unbiased, confidence_data_unbiased\n", ")\n", "\n", "X_train_unbiased = X_unbiased[train_idx_unbiased]\n", "y_train_unbiased = y_unbiased[train_idx_unbiased]\n", "\n", "X_val_unbiased = X_unbiased[val_idx_unbiased] if len(val_idx_unbiased) > 0 else None\n", "y_val_unbiased = y_unbiased[val_idx_unbiased] if len(val_idx_unbiased) > 0 else None\n", "\n", "model_unbiased = train_single_site_style_model(\n", "    X_train_unbiased, y_train_unbiased, X_val_unbiased, y_val_unbiased\n", ")\n", "\n", "# Test 3: Validate unbiased model\n", "print(\"\\n3️⃣ Validating UNBIASED model...\")\n", "unbiased_validation_results = validate_on_known_pile_locations_unbiased(\n", "    model_unbiased, enhanced_pile_data, filtered_points\n", ")\n", "\n", "# Test 4: Compare results\n", "print(\"\\n4️⃣ COMPARISON RESULTS:\")\n", "print(\"=\"*40)\n", "\n", "print(f\"\\n📊 DATASET COMPARISON:\")\n", "print(f\"   Biased dataset:   {len(X)} samples ({np.sum(y)} positive)\")\n", "print(f\"   Unbiased dataset: {len(X_unbiased)} samples ({np.sum(y_unbiased)} positive)\")\n", "\n", "print(f\"\\n🎯 DETECTION RATE COMPARISON:\")\n", "print(f\"   Biased approach:   {pile_validation_results['detection_rate']*100:.1f}% ({pile_validation_results['status']})\")\n", "print(f\"   Unbiased approach: {unbiased_validation_results['detection_rate']*100:.1f}% ({unbiased_validation_results['status']})\")\n", "\n", "print(f\"\\n📈 CONFIDENCE COMPARISON:\")\n", "print(f\"   Biased approach:   {pile_validation_results['avg_confidence']:.3f}\")\n", "print(f\"   Unbiased approach: {unbiased_validation_results['avg_confidence']:.3f}\")\n", "\n", "print(f\"\\n⚠️ FAILED EXTRACTIONS:\")\n", "print(f\"   Biased approach:   {pile_validation_results['failed_extractions']}\")\n", "print(f\"   Unbiased approach: {unbiased_validation_results['failed_extractions']}\")\n", "\n", "# Test 5: Feature importance comparison\n", "print(f\"\\n🔍 FEATURE IMPORTANCE COMPARISON:\")\n", "feature_importance_unbiased = analyze_feature_importance(model_unbiased)\n", "\n", "print(f\"\\nTop 5 features - BIASED approach:\")\n", "for i, row in feature_importance.head(5).iterrows():\n", "    print(f\"   {row['feature']}: {row['importance']:.3f}\")\n", "\n", "print(f\"\\nTop 5 features - UNBIASED approach:\")\n", "for i, row in feature_importance_unbiased.head(5).iterrows():\n", "    print(f\"   {row['feature']}: {row['importance']:.3f}\")\n", "\n", "# Test 6: Which approach is better?\n", "print(f\"\\n🏆 RECOMMENDATION:\")\n", "biased_rate = pile_validation_results['detection_rate']\n", "unbiased_rate = unbiased_validation_results['detection_rate']\n", "\n", "if abs(biased_rate - unbiased_rate) < 0.05:  # Within 5%\n", "    print(f\"   📊 SIMILAR PERFORMANCE: Both approaches achieve similar results\")\n", "    print(f\"   ✅ This suggests the methodology is robust\")\n", "    if biased_rate > 0.9:\n", "        print(f\"   💡 CONCLUSION: Use BIASED approach for pile verification tasks\")\n", "        print(f\"      (When you have suspected pile locations to verify)\")\n", "    else:\n", "        print(f\"   💡 CONCLUSION: Use UNBIASED approach for pile discovery tasks\") \n", "        print(f\"      (When searching for unknown piles)\")\n", "        \n", "elif unbiased_rate > biased_rate + 0.05:  # Unbiased significantly better\n", "    print(f\"   🟢 UNBIASED APPROACH WINS!\")\n", "    print(f\"   📈 Performance improvement: {(unbiased_rate - biased_rate)*100:.1f} percentage points\")\n", "    print(f\"   💡 CONCLUSION: Use unbiased features for better generalization\")\n", "    \n", "else:  # Biased significantly better\n", "    print(f\"   🟡 BIASED APPROACH WINS!\")\n", "    print(f\"   📈 Performance advantage: {(biased_rate - unbiased_rate)*100:.1f} percentage points\")\n", "    print(f\"   💡 CONCLUSION: Pile-centered features help for verification tasks\")\n", "    print(f\"   ⚠️  NOTE: This approach assumes known pile locations\")\n", "\n", "print(f\"\\n📝 METHODOLOGY NOTE:\")\n", "print(f\"   • Biased approach: Suitable for PILE VERIFICATION (known locations)\")\n", "print(f\"   • Unbiased approach: Suitable for PILE DISCOVERY (unknown locations)\")\n", "print(f\"   • Your use case determines which approach to use\")\n", "\n", "print(\"\\n\" + \"=\"*60)"]}, {"cell_type": "code", "execution_count": 5, "id": "6ed8280e", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "🔄 PREPARING DATA FOR DEEP LEARNING COMPARISON\n", "==================================================\n", "Extracting 3D coordinates for deep learning from 1288 locations...\n", "✅ Extracted 203 positive coordinate patches\n", "✅ Created 11 negative coordinate patches\n", "✅ Total coordinate dataset: 214 patches\n", "   Shape per patch: (64, 3) (points, XYZ)\n", "PyTorch data prepared:\n", "  Train: torch.<PERSON><PERSON>([171, 64, 3]) patches, torch.<PERSON><PERSON>([171]) labels\n", "  Test: torch.<PERSON><PERSON>([43, 64, 3]) patches, torch.<PERSON><PERSON>([43]) labels\n", "✅ Deep learning data ready!\n", "   Same 214 patches as classical ML\n", "   Same 203 positive samples\n", "   Ready for PointNet++ and DGCNN training\n"]}], "source": ["# Data Preparation for PointNet++ and DGCNN Comparison\n", "# Add these functions to your notebook\n", "\n", "import torch\n", "import torch.nn as nn\n", "import numpy as np\n", "from sklearn.model_selection import train_test_split\n", "from sklearn.metrics import accuracy_score, classification_report\n", "\n", "def extract_coordinates_for_deep_learning(points, enhanced_pile_data, point_tree):\n", "    \"\"\"Extract 3D coordinates for deep learning (same patches as classical ML)\"\"\"\n", "    \n", "    print(f\"Extracting 3D coordinates for deep learning from {len(enhanced_pile_data)} locations...\")\n", "    \n", "    positive_patches = []\n", "    positive_labels = []\n", "    positive_metadata = []\n", "    \n", "    # Extract positive patches - SAME locations as classical ML\n", "    for pile_data in enhanced_pile_data:\n", "        pile_coord = [pile_data['x'], pile_data['y']]\n", "        \n", "        # Find points within radius (SAME as classical ML)\n", "        indices = point_tree.query_ball_point(pile_coord, PATCH_RADIUS)\n", "        \n", "        if len(indices) < MIN_POINTS:\n", "            continue\n", "            \n", "        patch_points = points[indices]\n", "        \n", "        # Subsample to target size (SAME as classical ML)\n", "        if len(patch_points) > TARGET_PATCH_SIZE:\n", "            sampled_indices = np.random.choice(len(patch_points), TARGET_PATCH_SIZE, replace=False)\n", "            patch_points = patch_points[sampled_indices]\n", "        elif len(patch_points) < TARGET_PATCH_SIZE:\n", "            # Pad with duplicated points + small noise\n", "            needed = TARGET_PATCH_SIZE - len(patch_points)\n", "            duplicates = []\n", "            for _ in range(needed):\n", "                idx = np.random.randint(0, len(patch_points))\n", "                duplicate = patch_points[idx].copy()\n", "                # Add small noise to avoid identical points\n", "                duplicate += np.random.normal(0, 0.01, 3)\n", "                duplicates.append(duplicate)\n", "            patch_points = np.vstack([patch_points, np.array(duplicates)])\n", "        \n", "        # Store raw coordinates (no centering for fair comparison with unbiased)\n", "        positive_patches.append(patch_points.astype(np.float32))\n", "        positive_labels.append(1)\n", "        positive_metadata.append(pile_data)\n", "    \n", "    print(f\"✅ Extracted {len(positive_patches)} positive coordinate patches\")\n", "    \n", "    # Create negative samples - SAME strategy as classical ML\n", "    negative_patches, negative_labels, negative_metadata = create_negative_coordinate_patches(\n", "        points, enhanced_pile_data, point_tree, len(positive_patches)\n", "    )\n", "    \n", "    print(f\"✅ Created {len(negative_patches)} negative coordinate patches\")\n", "    \n", "    # Combine positive and negative\n", "    all_patches = positive_patches + negative_patches\n", "    all_labels = positive_labels + negative_labels\n", "    all_metadata = positive_metadata + negative_metadata\n", "    \n", "    print(f\"✅ Total coordinate dataset: {len(all_patches)} patches\")\n", "    print(f\"   Shape per patch: {all_patches[0].shape} (points, XYZ)\")\n", "    \n", "    return np.array(all_patches), np.array(all_labels), all_metadata\n", "\n", "def create_negative_coordinate_patches(points, pile_data, point_tree, n_negatives):\n", "    \"\"\"Create negative coordinate patches (same strategy as classical ML)\"\"\"\n", "    \n", "    pile_coords = np.array([[p['x'], p['y']] for p in pile_data])\n", "    pile_tree = cKDTree(pile_coords)\n", "    \n", "    # Same bounds as classical ML\n", "    x_min, x_max = points[:, 0].min(), points[:, 0].max()\n", "    y_min, y_max = points[:, 1].min(), points[:, 1].max()\n", "    \n", "    buffer = 50.0\n", "    x_min += buffer\n", "    x_max -= buffer\n", "    y_min += buffer\n", "    y_max -= buffer\n", "    \n", "    negative_patches = []\n", "    negative_labels = []\n", "    negative_metadata = []\n", "    \n", "    attempts = 0\n", "    max_attempts = n_negatives * 5\n", "    min_distance = PATCH_RADIUS * 2\n", "    \n", "    while len(negative_patches) < n_negatives and attempts < max_attempts:\n", "        # Random location\n", "        x = np.random.uniform(x_min, x_max)\n", "        y = np.random.uniform(y_min, y_max)\n", "        \n", "        # Check distance to nearest pile\n", "        dist_to_nearest_pile, _ = pile_tree.query([x, y])\n", "        \n", "        if dist_to_nearest_pile > min_distance:\n", "            # Extract patch\n", "            indices = point_tree.query_ball_point([x, y], PATCH_RADIUS)\n", "            \n", "            if len(indices) >= MIN_POINTS:\n", "                patch_points = points[indices]\n", "                \n", "                # Same subsampling as positive patches\n", "                if len(patch_points) > TARGET_PATCH_SIZE:\n", "                    sampled_indices = np.random.choice(len(patch_points), TARGET_PATCH_SIZE, replace=False)\n", "                    patch_points = patch_points[sampled_indices]\n", "                elif len(patch_points) < TARGET_PATCH_SIZE:\n", "                    needed = TARGET_PATCH_SIZE - len(patch_points)\n", "                    duplicates = []\n", "                    for _ in range(needed):\n", "                        idx = np.random.randint(0, len(patch_points))\n", "                        duplicate = patch_points[idx].copy()\n", "                        duplicate += np.random.normal(0, 0.01, 3)\n", "                        duplicates.append(duplicate)\n", "                    patch_points = np.vstack([patch_points, np.array(duplicates)])\n", "                \n", "                negative_patches.append(patch_points.astype(np.float32))\n", "                negative_labels.append(0)\n", "                negative_metadata.append({\n", "                    'pile_id': f'negative_{len(negative_patches)}',\n", "                    'x': x, 'y': y,\n", "                    'confidence': 'synthetic',\n", "                    'source': 'negative_sampling'\n", "                })\n", "        \n", "        attempts += 1\n", "    \n", "    return negative_patches, negative_labels, negative_metadata\n", "\n", "def normalize_patches_for_deep_learning(patches):\n", "    \"\"\"Normalize coordinate patches for deep learning\"\"\"\n", "    \n", "    normalized_patches = []\n", "    \n", "    for patch in patches:\n", "        # Center each patch\n", "        centroid = np.mean(patch, axis=0)\n", "        centered = patch - centroid\n", "        \n", "        # Scale to unit sphere\n", "        max_dist = np.max(np.linalg.norm(centered, axis=1))\n", "        if max_dist > 1e-6:\n", "            scaled = centered / max_dist\n", "        else:\n", "            scaled = centered\n", "            \n", "        normalized_patches.append(scaled)\n", "    \n", "    return np.array(normalized_patches)\n", "\n", "def prepare_data_for_pytorch(patches, labels, test_size=0.2):\n", "    \"\"\"Prepare data for PyTorch training\"\"\"\n", "    \n", "    # Normalize patches\n", "    normalized_patches = normalize_patches_for_deep_learning(patches)\n", "    \n", "    # Train/test split\n", "    X_train, X_test, y_train, y_test = train_test_split(\n", "        normalized_patches, labels, test_size=test_size, \n", "        random_state=42, stratify=labels\n", "    )\n", "    \n", "    # Convert to PyTorch tensors\n", "    X_train_tensor = torch.FloatTensor(X_train)\n", "    X_test_tensor = torch.FloatTensor(X_test)\n", "    y_train_tensor = torch.LongTensor(y_train)\n", "    y_test_tensor = torch.LongTensor(y_test)\n", "    \n", "    print(f\"PyTorch data prepared:\")\n", "    print(f\"  Train: {X_train_tensor.shape} patches, {y_train_tensor.shape} labels\")\n", "    print(f\"  Test: {X_test_tensor.shape} patches, {y_test_tensor.shape} labels\")\n", "    \n", "    return X_train_tensor, X_test_tensor, y_train_tensor, y_test_tensor\n", "\n", "# Create coordinate dataset from same locations as classical ML\n", "print(\"\\n🔄 PREPARING DATA FOR DEEP LEARNING COMPARISON\")\n", "print(\"=\"*50)\n", "\n", "# Use same point tree and locations as classical ML\n", "coordinate_patches, coordinate_labels, coordinate_metadata = extract_coordinates_for_deep_learning(\n", "    filtered_points, enhanced_pile_data, cKDTree(filtered_points[:, :2])\n", ")\n", "\n", "# Prepare for PyTorch\n", "X_train_dl, X_test_dl, y_train_dl, y_test_dl = prepare_data_for_pytorch(\n", "    coordinate_patches, coordinate_labels\n", ")\n", "\n", "print(f\"✅ Deep learning data ready!\")\n", "print(f\"   Same {len(coordinate_patches)} patches as classical ML\")\n", "print(f\"   Same {np.sum(coordinate_labels)} positive samples\")\n", "print(f\"   Ready for PointNet++ and DGCNN training\")"]}, {"cell_type": "code", "execution_count": 6, "id": "13fe1726", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "🏁 TRAINING DEEP LEARNING MODELS FOR COMPARISON\n", "============================================================\n", "\n", "🚀 Training PointNet++...\n", "  Epoch 10/50: Train Acc: 0.947, Test Acc: 0.953\n", "  Epoch 20/50: Train Acc: 0.953, Test Acc: 0.953\n", "  Epoch 30/50: Train Acc: 0.977, Test Acc: 0.953\n", "  Epoch 40/50: Train Acc: 0.965, Test Acc: 0.907\n", "  Epoch 50/50: Train Acc: 0.988, Test Acc: 0.953\n", "✅ PointNet++ training complete!\n", "   Final test accuracy: 0.953\n", "   Best test accuracy: 0.953\n", "   Training time: 12.1s\n", "\n", "🚀 Training DGCNN...\n", "  Epoch 10/50: Train Acc: 0.994, Test Acc: 0.953\n", "  Epoch 20/50: Train Acc: 0.994, Test Acc: 0.953\n", "  Epoch 30/50: Train Acc: 1.000, Test Acc: 0.930\n", "  Epoch 40/50: Train Acc: 1.000, Test Acc: 0.930\n", "  Epoch 50/50: Train Acc: 1.000, Test Acc: 0.930\n", "✅ DGCNN training complete!\n", "   Final test accuracy: 0.930\n", "   Best test accuracy: 0.953\n", "   Training time: 71.5s\n"]}], "source": ["# Simplified PointNet++ and DGCNN Models for Fair Comparison\n", "\n", "import torch\n", "import torch.nn as nn\n", "import torch.nn.functional as F\n", "from torch.utils.data import DataLoader, TensorDataset\n", "import time\n", "\n", "class SimplePointNet(nn.Module):\n", "    \"\"\"Simplified PointNet++ for pile detection comparison\"\"\"\n", "    \n", "    def __init__(self, num_classes=2, num_points=64):\n", "        super(SimplePointNet, self).__init__()\n", "        \n", "        # Feature extraction layers\n", "        self.conv1 = nn.Conv1d(3, 64, 1)\n", "        self.conv2 = nn.Conv1d(64, 128, 1)\n", "        self.conv3 = nn.Conv1d(128, 256, 1)\n", "        \n", "        self.bn1 = nn.BatchNorm1d(64)\n", "        self.bn2 = nn.BatchNorm1d(128)\n", "        self.bn3 = nn.BatchNorm1d(256)\n", "        \n", "        # Classification head\n", "        self.fc1 = nn.Linear(256, 128)\n", "        self.fc2 = nn.<PERSON>ar(128, 64)\n", "        self.fc3 = nn.Linear(64, num_classes)\n", "        \n", "        self.dropout = nn.Dropout(0.3)\n", "        \n", "    def forward(self, x):\n", "        # x shape: (batch_size, num_points, 3)\n", "        x = x.transpose(2, 1)  # (batch_size, 3, num_points)\n", "        \n", "        # Feature extraction\n", "        x = F.relu(self.bn1(self.conv1(x)))\n", "        x = F.relu(self.bn2(self.conv2(x)))\n", "        x = <PERSON>.relu(self.bn3(self.conv3(x)))\n", "        \n", "        # Global max pooling\n", "        x = torch.max(x, 2)[0]  # (batch_size, 256)\n", "        \n", "        # Classification\n", "        x = F.relu(self.fc1(x))\n", "        x = self.dropout(x)\n", "        x = <PERSON>.relu(self.fc2(x))\n", "        x = self.dropout(x)\n", "        x = self.fc3(x)\n", "        \n", "        return x\n", "\n", "def get_knn_graph(x, k=20):\n", "    \"\"\"Get k-nearest neighbor graph for DGCNN\"\"\"\n", "    batch_size, num_dims, num_points = x.size()\n", "    \n", "    # Compute pairwise distances\n", "    inner = -2 * torch.matmul(x.transpose(2, 1), x)\n", "    xx = torch.sum(x**2, dim=1, keepdim=True)\n", "    pairwise_distance = -xx - inner - xx.transpose(2, 1)\n", "    \n", "    # Get k nearest neighbors\n", "    idx = pairwise_distance.topk(k=k, dim=-1)[1]  # (batch_size, num_points, k)\n", "    \n", "    return idx\n", "\n", "def get_edge_features(x, idx):\n", "    \"\"\"Get edge features for DGCNN\"\"\"\n", "    batch_size, num_dims, num_points = x.size()\n", "    k = idx.size(-1)\n", "    \n", "    # Get neighbor features\n", "    idx_expanded = idx.unsqueeze(1).expand(batch_size, num_dims, num_points, k)\n", "    neighbors = torch.gather(x.unsqueeze(-1).expand(-1, -1, -1, k), 2, idx_expanded)\n", "    \n", "    # Central features\n", "    central = x.unsqueeze(-1).expand(-1, -1, -1, k)\n", "    \n", "    # Edge features: [central, neighbor - central]\n", "    edge_features = torch.cat([central, neighbors - central], dim=1)\n", "    \n", "    return edge_features\n", "\n", "class SimpleDGCNN(nn.Module):\n", "    \"\"\"Simplified DGCNN for pile detection comparison\"\"\"\n", "    \n", "    def __init__(self, num_classes=2, k=20):\n", "        super(SimpleDGCN<PERSON>, self).__init__()\n", "        self.k = k\n", "        \n", "        # Edge convolution layers\n", "        self.conv1 = nn.Conv2d(6, 64, 1)    # 3*2 = 6 (central + edge)\n", "        self.conv2 = nn.Conv2d(128, 128, 1)  # 64*2 = 128\n", "        self.conv3 = nn.Conv2d(256, 256, 1)  # 128*2 = 256\n", "        \n", "        self.bn1 = nn.BatchNorm2d(64)\n", "        self.bn2 = nn.BatchNorm2d(128)\n", "        self.bn3 = nn.BatchNorm2d(256)\n", "        \n", "        # Classification head\n", "        self.conv4 = nn.Conv1d(448, 256, 1)  # 64+128+256 = 448\n", "        self.conv5 = nn.Conv1d(256, 128, 1)\n", "        self.conv6 = nn.Conv1d(128, num_classes, 1)\n", "        \n", "        self.bn4 = nn.<PERSON>chNorm1d(256)\n", "        self.bn5 = nn.<PERSON>ch<PERSON>orm1d(128)\n", "        \n", "        self.dropout = nn.Dropout(0.3)\n", "        \n", "    def forward(self, x):\n", "        # x shape: (batch_size, num_points, 3)\n", "        x = x.transpose(2, 1)  # (batch_size, 3, num_points)\n", "        batch_size, num_dims, num_points = x.size()\n", "        \n", "        # Edge convolution 1\n", "        idx1 = get_knn_graph(x, self.k)\n", "        edge_features1 = get_edge_features(x, idx1)\n", "        x1 = <PERSON>.relu(self.bn1(self.conv1(edge_features1)))\n", "        x1 = x1.max(dim=-1)[0]  # (batch_size, 64, num_points)\n", "        \n", "        # Edge convolution 2\n", "        idx2 = get_knn_graph(x1, self.k)\n", "        edge_features2 = get_edge_features(x1, idx2)\n", "        x2 = <PERSON>.relu(self.bn2(self.conv2(edge_features2)))\n", "        x2 = x2.max(dim=-1)[0]  # (batch_size, 128, num_points)\n", "        \n", "        # Edge convolution 3\n", "        idx3 = get_knn_graph(x2, self.k)\n", "        edge_features3 = get_edge_features(x2, idx3)\n", "        x3 = <PERSON>.relu(self.bn3(self.conv3(edge_features3)))\n", "        x3 = x3.max(dim=-1)[0]  # (batch_size, 256, num_points)\n", "        \n", "        # Concatenate features\n", "        x = torch.cat([x1, x2, x3], dim=1)  # (batch_size, 448, num_points)\n", "        \n", "        # Global features\n", "        x = <PERSON>.relu(self.bn4(self.conv4(x)))\n", "        x = <PERSON>.relu(self.bn5(self.conv5(x)))\n", "        x = self.conv6(x)\n", "        \n", "        # Global max pooling\n", "        x = torch.max(x, 2)[0]  # (batch_size, num_classes)\n", "        \n", "        return x\n", "\n", "def train_deep_learning_model(model, X_train, y_train, X_test, y_test, model_name, epochs=50):\n", "    \"\"\"Train and evaluate deep learning model\"\"\"\n", "    \n", "    print(f\"\\n🚀 Training {model_name}...\")\n", "    \n", "    # Setup\n", "    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')\n", "    model = model.to(device)\n", "    \n", "    # Data loaders\n", "    train_dataset = TensorDataset(X_train, y_train)\n", "    test_dataset = TensorDataset(X_test, y_test)\n", "    \n", "    train_loader = DataLoader(train_dataset, batch_size=16, shuffle=True)\n", "    test_loader = DataLoader(test_dataset, batch_size=16, shuffle=False)\n", "    \n", "    # Optimizer and loss\n", "    optimizer = torch.optim.Adam(model.parameters(), lr=0.001, weight_decay=1e-4)\n", "    criterion = nn.CrossEntropyLoss()\n", "    \n", "    # Training loop\n", "    start_time = time.time()\n", "    best_test_acc = 0\n", "    \n", "    for epoch in range(epochs):\n", "        # Training\n", "        model.train()\n", "        train_loss = 0\n", "        train_correct = 0\n", "        train_total = 0\n", "        \n", "        for batch_x, batch_y in train_loader:\n", "            batch_x, batch_y = batch_x.to(device), batch_y.to(device)\n", "            \n", "            optimizer.zero_grad()\n", "            outputs = model(batch_x)\n", "            loss = criterion(outputs, batch_y)\n", "            loss.backward()\n", "            optimizer.step()\n", "            \n", "            train_loss += loss.item()\n", "            _, predicted = torch.max(outputs.data, 1)\n", "            train_total += batch_y.size(0)\n", "            train_correct += (predicted == batch_y).sum().item()\n", "        \n", "        # Testing\n", "        model.eval()\n", "        test_correct = 0\n", "        test_total = 0\n", "        \n", "        with torch.no_grad():\n", "            for batch_x, batch_y in test_loader:\n", "                batch_x, batch_y = batch_x.to(device), batch_y.to(device)\n", "                outputs = model(batch_x)\n", "                _, predicted = torch.max(outputs.data, 1)\n", "                test_total += batch_y.size(0)\n", "                test_correct += (predicted == batch_y).sum().item()\n", "        \n", "        train_acc = train_correct / train_total\n", "        test_acc = test_correct / test_total\n", "        best_test_acc = max(best_test_acc, test_acc)\n", "        \n", "        if (epoch + 1) % 10 == 0:\n", "            print(f\"  Epoch {epoch+1}/{epochs}: Train Acc: {train_acc:.3f}, Test Acc: {test_acc:.3f}\")\n", "    \n", "    training_time = time.time() - start_time\n", "    \n", "    # Final evaluation\n", "    model.eval()\n", "    all_predictions = []\n", "    all_targets = []\n", "    \n", "    with torch.no_grad():\n", "        for batch_x, batch_y in test_loader:\n", "            batch_x, batch_y = batch_x.to(device), batch_y.to(device)\n", "            outputs = model(batch_x)\n", "            _, predicted = torch.max(outputs.data, 1)\n", "            all_predictions.extend(predicted.cpu().numpy())\n", "            all_targets.extend(batch_y.cpu().numpy())\n", "    \n", "    final_accuracy = accuracy_score(all_targets, all_predictions)\n", "    \n", "    print(f\"✅ {model_name} training complete!\")\n", "    print(f\"   Final test accuracy: {final_accuracy:.3f}\")\n", "    print(f\"   Best test accuracy: {best_test_acc:.3f}\")\n", "    print(f\"   Training time: {training_time:.1f}s\")\n", "    \n", "    return {\n", "        'model': model,\n", "        'final_accuracy': final_accuracy,\n", "        'best_accuracy': best_test_acc,\n", "        'training_time': training_time,\n", "        'predictions': all_predictions,\n", "        'targets': all_targets\n", "    }\n", "\n", "# Train both models\n", "print(\"\\n🏁 TRAINING DEEP LEARNING MODELS FOR COMPARISON\")\n", "print(\"=\"*60)\n", "\n", "# PointNet++\n", "pointnet_model = SimplePointNet(num_classes=2, num_points=TARGET_PATCH_SIZE)\n", "pointnet_results = train_deep_learning_model(\n", "    pointnet_model, X_train_dl, y_train_dl, X_test_dl, y_test_dl, \n", "    \"PointNet++\", epochs=50\n", ")\n", "\n", "# DGCNN  \n", "dgcnn_model = SimpleDGCNN(num_classes=2, k=10)  # Smaller k for 64 points\n", "dgcnn_results = train_deep_learning_model(\n", "    dgcnn_model, X_train_dl, y_train_dl, X_test_dl, y_test_dl,\n", "    \"DGCNN\", epochs=50\n", ")"]}, {"cell_type": "code", "execution_count": 8, "id": "59a93357", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "🔍 VALIDATING DEEP LEARNING MODELS ON GROUND TRUTH\n", "============================================================\n", "\n", "🔍 GROUND TRUTH VALIDATION: PointNet++\n", "🟢 PointNet++ Ground Truth Results:\n", "   Detection Rate: 100.0% (203/203)\n", "   Average Confidence: 0.998\n", "   Status: EXCELLENT\n", "\n", "🔍 GROUND TRUTH VALIDATION: DGCNN\n", "🟢 DGCNN Ground Truth Results:\n", "   Detection Rate: 100.0% (203/203)\n", "   Average Confidence: 0.994\n", "   Status: EXCELLENT\n", "\n", "🏆 COMPREHENSIVE ALGORITHM COMPARISON\n", "======================================================================\n", "\n", "📊 PERFORMANCE COMPARISON:\n", "Metric               Classical ML    PointNet++      DGCNN          \n", "----------------------------------------------------------------------\n", "test_accuracy        1.0             0.9534883720930233 0.9302325581395349\n", "training_time        ~30 seconds     12.1s           71.5s          \n", "interpretability     High (22 engineering features) Low (learned 3D features) Low (learned graph features)\n", "complexity           Low             High (deep neural network) Very High (graph neural network)\n", "\n", "🎯 SCENARIO-BASED RECOMMENDATIONS:\n", "\n", "📈 ACCURACY RANKING:\n", "   1. Classical ML: 1.000\n", "   2. PointNet++: 0.953\n", "   3. DGCNN: 0.930\n", "\n", "🎯 SCENARIO RECOMMENDATIONS:\n", "   Maximum Accuracy         : Classical ML\n", "   Fastest Training         : Classical ML\n", "   Most Interpretable       : Classical ML\n", "   Lowest Complexity        : Classical ML\n", "   Production Deployment    : Classical ML\n", "   Research Exploration     : DGCNN\n", "\n", "🏆 OVERALL ASSESSMENT:\n", "   🟢 CLASSICAL ML RECOMMENDED\n", "   📊 Achieves comparable accuracy (1.000) with significant advantages:\n", "      • Much faster training and inference\n", "      • Highly interpretable features\n", "      • Lower computational requirements\n", "      • Easier deployment and maintenance\n", "\n", "💡 PRACTICAL DEPLOYMENT GUIDANCE:\n", "   🚀 Quick Deployment: Classical ML (30s training, immediate results)\n", "   🎯 Maximum Accuracy: Classical ML (1.000 accuracy)\n", "   🔍 Interpretable Results: Classical ML (engineers can understand features)\n", "   🏭 Large Scale Production: Classical ML (lower computational overhead)\n", "   🔬 Research Applications: All approaches valid for different research questions\n", "\n", "💾 COMPARISON RESULTS SAVED\n", "   File: algorithm_comparison_20250810_093216.json\n"]}, {"data": {"image/png": "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********************************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", "text/plain": ["<Figure size 1500x1200 with 4 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["📊 Comparison visualization saved: algorithm_comparison_plot_20250810_093216.png\n", "\n", "🎉 COMPREHENSIVE ALGORITHM COMPARISON COMPLETE!\n", "============================================================\n", "📊 Results Summary:\n", "   Classical ML: 100.0% detection\n", "   PointNet++: 100.0% detection\n", "   DGCNN: 100.0% detection\n", "\n", "📁 Files Generated:\n", "   • algorithm_comparison_20250810_093216.json\n", "   • algorithm_comparison_plot_20250810_093216.png\n", "\n", "💡 This comparison provides objective evidence for your thesis methodology choice!\n"]}], "source": ["# Comprehensive Algorithm Comparison Framework\n", "\n", "def comprehensive_algorithm_comparison():\n", "    \"\"\"Compare all three approaches objectively across multiple criteria\"\"\"\n", "    \n", "    print(\"\\n🏆 COMPREHENSIVE ALGORITHM COMPARISON\")\n", "    print(\"=\"*70)\n", "    \n", "    # Collect results from all approaches\n", "    results = {\n", "        'Classical ML (Unbiased)': {\n", "            'detection_rate': unbiased_validation_results['detection_rate'],\n", "            'test_accuracy': unbiased_validation_results['detection_rate'], \n", "            'training_time': '~30 seconds',\n", "            'inference_speed': '~0.1ms per patch',\n", "            'interpretability': 'High (22 engineering features)',\n", "            'complexity': 'Low',\n", "            'data_requirements': f\"{len(X_unbiased)} samples\",\n", "            'failed_extractions': unbiased_validation_results['failed_extractions'],\n", "            'confidence': unbiased_validation_results['avg_confidence']\n", "        },\n", "        'PointNet++': {\n", "            'detection_rate': 'TBD - needs ground truth validation',\n", "            'test_accuracy': pointnet_results['final_accuracy'],\n", "            'training_time': f\"{pointnet_results['training_time']:.1f}s\",\n", "            'inference_speed': '~1-5ms per patch',\n", "            'interpretability': 'Low (learned 3D features)',\n", "            'complexity': 'High (deep neural network)',\n", "            'data_requirements': f\"{len(X_train_dl)} samples\",\n", "            'failed_extractions': 'Same as classical ML',\n", "            'confidence': 'TBD'\n", "        },\n", "        'DGCNN': {\n", "            'detection_rate': 'TBD - needs ground truth validation',\n", "            'test_accuracy': dgcnn_results['final_accuracy'],\n", "            'training_time': f\"{dgcnn_results['training_time']:.1f}s\", \n", "            'inference_speed': '~5-10ms per patch',\n", "            'interpretability': 'Low (learned graph features)',\n", "            'complexity': 'Very High (graph neural network)',\n", "            'data_requirements': f\"{len(X_train_dl)} samples\",\n", "            'failed_extractions': 'Same as classical ML',\n", "            'confidence': 'TBD'\n", "        }\n", "    }\n", "    \n", "    # Display comparison table\n", "    print(f\"\\n📊 PERFORMANCE COMPARISON:\")\n", "    print(f\"{'Metric':<20} {'Classical ML':<15} {'PointNet++':<15} {'DGCNN':<15}\")\n", "    print(\"-\" * 70)\n", "    \n", "    metrics_to_compare = ['test_accuracy', 'training_time', 'interpretability', 'complexity']\n", "    \n", "    for metric in metrics_to_compare:\n", "        classical = results['Classical ML (Unbiased)'][metric]\n", "        pointnet = results['PointNet++'][metric]\n", "        dgcnn = results['DGCNN'][metric]\n", "        \n", "        print(f\"{metric:<20} {str(classical):<15} {str(pointnet):<15} {str(dgcnn):<15}\")\n", "    \n", "    # Determine best approach for different scenarios\n", "    print(f\"\\n🎯 SCENARIO-BASED RECOMMENDATIONS:\")\n", "    \n", "    # Get numeric accuracies for comparison\n", "    classical_acc = results['Classical ML (Unbiased)']['detection_rate']\n", "    pointnet_acc = results['PointNet++']['test_accuracy']\n", "    dgcnn_acc = results['DGCNN']['test_accuracy']\n", "    \n", "    accuracies = {\n", "        'Classical ML': classical_acc,\n", "        'PointNet++': pointnet_acc, \n", "        'DGCNN': dgcnn_acc\n", "    }\n", "    \n", "    best_accuracy = max(accuracies.items(), key=lambda x: x[1])\n", "    \n", "    print(f\"\\n📈 ACCURACY RANKING:\")\n", "    sorted_acc = sorted(accuracies.items(), key=lambda x: x[1], reverse=True)\n", "    for i, (method, acc) in enumerate(sorted_acc):\n", "        print(f\"   {i+1}. {method}: {acc:.3f}\")\n", "    \n", "    # Scenario analysis\n", "    scenarios = {\n", "        'Maximum Accuracy': best_accuracy[0],\n", "        'Fastest Training': 'Classical ML' if results['Classical ML (Unbiased)']['training_time'] == '~30 seconds' else 'Need to compare',\n", "        'Most Interpretable': 'Classical ML',\n", "        'Lowest Complexity': 'Classical ML',\n", "        'Production Deployment': 'Classical ML' if classical_acc > 0.95 else best_accuracy[0],\n", "        'Research Exploration': 'DGCNN' if dgcnn_acc > 0.90 else 'PointNet++'\n", "    }\n", "    \n", "    print(f\"\\n🎯 SCENARIO RECOMMENDATIONS:\")\n", "    for scenario, recommendation in scenarios.items():\n", "        print(f\"   {scenario:<25}: {recommendation}\")\n", "    \n", "    # Overall assessment\n", "    print(f\"\\n🏆 OVERALL ASSESSMENT:\")\n", "    \n", "    if classical_acc >= max(pointnet_acc, dgcnn_acc) - 0.05:  # Within 5%\n", "        print(f\"   🟢 CLASSICAL ML RECOMMENDED\")\n", "        print(f\"   📊 Achieves comparable accuracy ({classical_acc:.3f}) with significant advantages:\")\n", "        print(f\"      • Much faster training and inference\")\n", "        print(f\"      • Highly interpretable features\")\n", "        print(f\"      • Lower computational requirements\")\n", "        print(f\"      • Easier deployment and maintenance\")\n", "        \n", "    elif pointnet_acc > classical_acc + 0.05:\n", "        print(f\"   🟡 POINTNET++ SHOWS PROMISE\")\n", "        print(f\"   📊 Higher accuracy ({pointnet_acc:.3f} vs {classical_acc:.3f})\")\n", "        print(f\"   ⚠️  Trade-offs: Higher complexity, longer training, less interpretable\")\n", "        \n", "    elif dgcnn_acc > classical_acc + 0.05:\n", "        print(f\"   🟠 DGCNN SHOWS POTENTIAL\")\n", "        print(f\"   📊 Higher accuracy ({dgcnn_acc:.3f} vs {classical_acc:.3f})\")\n", "        print(f\"   ⚠️  Trade-offs: Highest complexity, longest training, least interpretable\")\n", "    \n", "    else:\n", "        print(f\"   🔄 MIXED RESULTS - CONTEXT DEPENDENT\")\n", "        print(f\"   📊 Similar accuracies across approaches\")\n", "        print(f\"   💡 Choice depends on specific deployment requirements\")\n", "    \n", "    # Practical deployment guidance\n", "    print(f\"\\n💡 PRACTICAL DEPLOYMENT GUIDANCE:\")\n", "    print(f\"   🚀 Quick Deployment: Classical ML (30s training, immediate results)\")\n", "    print(f\"   🎯 Maximum Accuracy: {best_accuracy[0]} ({best_accuracy[1]:.3f} accuracy)\")\n", "    print(f\"   🔍 Interpretable Results: Classical ML (engineers can understand features)\")\n", "    print(f\"   🏭 Large Scale Production: Classical ML (lower computational overhead)\")\n", "    print(f\"   🔬 Research Applications: All approaches valid for different research questions\")\n", "    \n", "    return results\n", "\n", "def validate_deep_learning_on_ground_truth(model, model_name, coordinate_patches, coordinate_labels, coordinate_metadata):\n", "    \"\"\"Validate deep learning model on same ground truth as classical ML\"\"\"\n", "    \n", "    print(f\"\\n🔍 GROUND TRUTH VALIDATION: {model_name}\")\n", "    \n", "    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')\n", "    model.eval()\n", "    \n", "    detected_piles = []\n", "    detection_confidences = []\n", "    \n", "    # Test on same pile locations as classical ML\n", "    positive_indices = [i for i, meta in enumerate(coordinate_metadata) if meta.get('confidence') != 'synthetic']\n", "    \n", "    with torch.no_grad():\n", "        for idx in positive_indices:\n", "            patch = coordinate_patches[idx:idx+1]  # Single patch\n", "            patch_tensor = torch.FloatTensor(patch).to(device)\n", "            \n", "            # Normalize same as training\n", "            centroid = torch.mean(patch_tensor, dim=1, keepdim=True)\n", "            centered = patch_tensor - centroid\n", "            max_dist = torch.max(torch.norm(centered, dim=2, keepdim=True))\n", "            if max_dist > 1e-6:\n", "                normalized = centered / max_dist\n", "            else:\n", "                normalized = centered\n", "            \n", "            outputs = model(normalized)\n", "            probabilities = F.softmax(outputs, dim=1)\n", "            prediction = torch.argmax(outputs, dim=1).item()\n", "            confidence = probabilities[0, 1].item()  # Probability of pile class\n", "            \n", "            detected_piles.append(prediction)\n", "            detection_confidences.append(confidence)\n", "    \n", "    # Calculate metrics\n", "    detection_rate = np.mean(detected_piles)\n", "    avg_confidence = np.mean(detection_confidences)\n", "    \n", "    # Assessment\n", "    if detection_rate >= 0.9:\n", "        status = \"EXCELLENT\"\n", "        color = \"🟢\"\n", "    elif detection_rate >= 0.8:\n", "        status = \"GOOD\"\n", "        color = \"🟡\"\n", "    elif detection_rate >= 0.6:\n", "        status = \"MODERATE\"\n", "        color = \"🟠\"\n", "    else:\n", "        status = \"POOR\"\n", "        color = \"🔴\"\n", "    \n", "    print(f\"{color} {model_name} Ground Truth Results:\")\n", "    print(f\"   Detection Rate: {detection_rate*100:.1f}% ({np.sum(detected_piles)}/{len(detected_piles)})\")\n", "    print(f\"   Average Confidence: {avg_confidence:.3f}\")\n", "    print(f\"   Status: {status}\")\n", "    \n", "    return {\n", "        'detection_rate': detection_rate,\n", "        'avg_confidence': avg_confidence,\n", "        'status': status,\n", "        'detected_piles': detected_piles,\n", "        'detection_confidences': detection_confidences\n", "    }\n", "\n", "# Run ground truth validation for deep learning models\n", "print(\"\\n🔍 VALIDATING DEEP LEARNING MODELS ON GROUND TRUTH\")\n", "print(\"=\"*60)\n", "\n", "pointnet_ground_truth = validate_deep_learning_on_ground_truth(\n", "    pointnet_results['model'], \"PointNet++\", coordinate_patches, coordinate_labels, coordinate_metadata\n", ")\n", "\n", "dgcnn_ground_truth = validate_deep_learning_on_ground_truth(\n", "    dgcnn_results['model'], \"DGCNN\", coordinate_patches, coordinate_labels, coordinate_metadata\n", ")\n", "\n", "# Update results with ground truth validation\n", "pointnet_results['ground_truth_detection'] = pointnet_ground_truth['detection_rate']\n", "pointnet_results['ground_truth_confidence'] = pointnet_ground_truth['avg_confidence']\n", "pointnet_results['ground_truth_status'] = pointnet_ground_truth['status']\n", "\n", "dgcnn_results['ground_truth_detection'] = dgcnn_ground_truth['detection_rate']\n", "dgcnn_results['ground_truth_confidence'] = dgcnn_ground_truth['avg_confidence']\n", "dgcnn_results['ground_truth_status'] = dgcnn_ground_truth['status']\n", "\n", "# Run comprehensive comparison\n", "final_comparison = comprehensive_algorithm_comparison()\n", "\n", "# Save comprehensive comparison results\n", "timestamp = datetime.now().strftime(\"%Y%m%d_%H%M%S\")\n", "\n", "comparison_results = {\n", "    'timestamp': timestamp,\n", "    'site': SITE_NAME,\n", "    'comparison_type': 'classical_ml_vs_deep_learning',\n", "    'approaches_tested': ['Classical ML (Unbiased)', 'PointNet++', 'DGCNN'],\n", "    'results': {\n", "        'classical_ml': {\n", "            'detection_rate': unbiased_validation_results['detection_rate'],\n", "            'avg_confidence': unbiased_validation_results['avg_confidence'],\n", "            'status': unbiased_validation_results['status'],\n", "            'training_time_seconds': 30,\n", "            'interpretability': 'High',\n", "            'complexity': 'Low'\n", "        },\n", "        'pointnet': {\n", "            'test_accuracy': pointnet_results['final_accuracy'],\n", "            'detection_rate': pointnet_results['ground_truth_detection'],\n", "            'avg_confidence': pointnet_results['ground_truth_confidence'],\n", "            'status': pointnet_results['ground_truth_status'],\n", "            'training_time_seconds': pointnet_results['training_time'],\n", "            'interpretability': 'Low',\n", "            'complexity': 'High'\n", "        },\n", "        'dgcnn': {\n", "            'test_accuracy': dgcnn_results['final_accuracy'],\n", "            'detection_rate': dgcnn_results['ground_truth_detection'],\n", "            'avg_confidence': dgcnn_results['ground_truth_confidence'],\n", "            'status': dgcnn_results['ground_truth_status'],\n", "            'training_time_seconds': dgcnn_results['training_time'],\n", "            'interpretability': 'Low',\n", "            'complexity': 'Very High'\n", "        }\n", "    },\n", "    'conclusions': {\n", "        'best_accuracy': max([\n", "            ('Classical ML', unbiased_validation_results['detection_rate']),\n", "            ('PointNet++', pointnet_results['ground_truth_detection']),\n", "            ('DGCNN', dgcnn_results['ground_truth_detection'])\n", "        ], key=lambda x: x[1]),\n", "        'fastest_training': 'Classical ML',\n", "        'most_interpretable': 'Classical ML',\n", "        'recommended_for_production': 'TBD based on results'\n", "    }\n", "}\n", "\n", "# Save results\n", "comparison_file = output_dir / f\"algorithm_comparison_{timestamp}.json\"\n", "with open(comparison_file, 'w') as f:\n", "    json.dump(comparison_results, f, indent=2, default=str)\n", "\n", "print(f\"\\n💾 COMPARISON RESULTS SAVED\")\n", "print(f\"   File: {comparison_file.name}\")\n", "\n", "# Create final visualization\n", "def create_comparison_visualization():\n", "    \"\"\"Create comprehensive comparison visualization\"\"\"\n", "    \n", "    fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(15, 12))\n", "    \n", "    # Plot 1: Detection Rate Comparison\n", "    methods = ['Classical ML', 'PointNet++', 'DGCNN']\n", "    detection_rates = [\n", "        unbiased_validation_results['detection_rate'],\n", "        pointnet_results['ground_truth_detection'],\n", "        dgcnn_results['ground_truth_detection']\n", "    ]\n", "    \n", "    colors = ['green', 'blue', 'orange']\n", "    bars1 = ax1.bar(methods, detection_rates, color=colors, alpha=0.7)\n", "    ax1.set_title('Ground Truth Detection Rate')\n", "    ax1.set_ylabel('Detection Rate')\n", "    ax1.set_ylim(0, 1.1)\n", "    \n", "    # Add value labels on bars\n", "    for bar, rate in zip(bars1, detection_rates):\n", "        height = bar.get_height()\n", "        ax1.text(bar.get_x() + bar.get_width()/2., height + 0.01,\n", "                f'{rate:.3f}', ha='center', va='bottom')\n", "    \n", "    # Plot 2: Training Time Comparison\n", "    training_times = [30, pointnet_results['training_time'], dgcnn_results['training_time']]\n", "    bars2 = ax2.bar(methods, training_times, color=colors, alpha=0.7)\n", "    ax2.set_title('Training Time (seconds)')\n", "    ax2.set_ylabel('Time (s)')\n", "    ax2.set_yscale('log')  # Log scale due to large differences\n", "    \n", "    for bar, time in zip(bars2, training_times):\n", "        height = bar.get_height()\n", "        ax2.text(bar.get_x() + bar.get_width()/2., height * 1.1,\n", "                f'{time:.1f}s', ha='center', va='bottom')\n", "    \n", "    # Plot 3: Complexity vs Performance\n", "    complexity_scores = [1, 3, 4]  # 1=Low, 3=High, 4=Very High\n", "    ax3.scatter(complexity_scores, detection_rates, c=colors, s=200, alpha=0.7)\n", "    \n", "    for i, method in enumerate(methods):\n", "        ax3.annotate(method, (complexity_scores[i], detection_rates[i]), \n", "                    xytext=(5, 5), textcoords='offset points')\n", "    \n", "    ax3.set_xlabel('Complexity (1=Low, 4=Very High)')\n", "    ax3.set_ylabel('Detection Rate')\n", "    ax3.set_title('Complexity vs Performance Trade-off')\n", "    ax3.grid(True, alpha=0.3)\n", "    \n", "    # Plot 4: Multi-criteria Radar Chart (simplified as bar chart)\n", "    criteria = ['Accuracy', 'Speed', 'Interpretability', 'Simplicity']\n", "    \n", "    # Normalize scores to 0-1 scale\n", "    classical_scores = [\n", "        unbiased_validation_results['detection_rate'],  # Accuracy\n", "        1.0,  # Speed (fastest)\n", "        1.0,  # Interpretability (highest)\n", "        1.0   # Simplicity (highest)\n", "    ]\n", "    \n", "    pointnet_scores = [\n", "        pointnet_results['ground_truth_detection'],  # Accuracy\n", "        0.6,  # Speed (medium)\n", "        0.2,  # Interpretability (low)\n", "        0.3   # Simplicity (low)\n", "    ]\n", "    \n", "    dgcnn_scores = [\n", "        dgcnn_results['ground_truth_detection'],  # Accuracy\n", "        0.3,  # <PERSON> (slow)\n", "        0.1,  # Interpretability (very low)\n", "        0.1   # Simplicity (very low)\n", "    ]\n", "    \n", "    x = np.arange(len(criteria))\n", "    width = 0.25\n", "    \n", "    ax4.bar(x - width, classical_scores, width, label='Classical ML', color='green', alpha=0.7)\n", "    ax4.bar(x, pointnet_scores, width, label='PointNet++', color='blue', alpha=0.7)\n", "    ax4.bar(x + width, dgcnn_scores, width, label='DGCNN', color='orange', alpha=0.7)\n", "    \n", "    ax4.set_xlabel('Criteria')\n", "    ax4.set_ylabel('Score (0-1)')\n", "    ax4.set_title('Multi-Criteria Comparison')\n", "    ax4.set_xticks(x)\n", "    ax4.set_xticklabels(criteria)\n", "    ax4.legend()\n", "    ax4.grid(True, alpha=0.3)\n", "    \n", "    plt.tight_layout()\n", "    \n", "    # Save plot\n", "    plot_file = output_dir / f\"algorithm_comparison_plot_{timestamp}.png\"\n", "    plt.savefig(plot_file, dpi=300, bbox_inches='tight')\n", "    plt.show()\n", "    \n", "    print(f\"📊 Comparison visualization saved: {plot_file.name}\")\n", "    return plot_file\n", "\n", "comparison_plot = create_comparison_visualization()\n", "\n", "print(\"\\n🎉 COMPREHENSIVE ALGORITHM COMPARISON COMPLETE!\")\n", "print(\"=\"*60)\n", "print(f\"📊 Results Summary:\")\n", "print(f\"   Classical ML: {unbiased_validation_results['detection_rate']*100:.1f}% detection\")\n", "print(f\"   PointNet++: {pointnet_results['ground_truth_detection']*100:.1f}% detection\")\n", "print(f\"   DGCNN: {dgcnn_results['ground_truth_detection']*100:.1f}% detection\")\n", "print(f\"\\n📁 Files Generated:\")\n", "print(f\"   • {comparison_file.name}\")\n", "print(f\"   • {comparison_plot.name}\")\n", "print(f\"\\n💡 This comparison provides objective evidence for your thesis methodology choice!\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 5}