{"cells": [{"cell_type": "markdown", "id": "4e86c2a3", "metadata": {"papermill": {"duration": 0.003746, "end_time": "2025-08-07T07:06:25.662827", "exception": false, "start_time": "2025-08-07T07:06:25.659081", "status": "completed"}, "tags": []}, "source": ["# PointNet++ Model Application to New Site\n", "This notebook applies the trained PointNet++ model to a new construction site\n", "with different data sources (DWG files instead of KML regions).\n", "\n", "**Workflow:**\n", "1. <PERSON><PERSON> trained PointNet++ model\n", "2. Process new site point cloud data\n", "3. Generate predictions across the site\n", "4. Compare with DWG-based pile locations (if available)\n", "5. Validate and export results\n", "\n", "**Author**: <PERSON><PERSON><PERSON>  \n", "**Date**: July 2025  \n", "**Project**: As-Built Foundation Analysis - Site Transfer"]}, {"cell_type": "code", "execution_count": 102, "id": "parameters", "metadata": {"execution": {"iopub.execute_input": "2025-08-07T07:06:25.667428Z", "iopub.status.busy": "2025-08-07T07:06:25.667255Z", "iopub.status.idle": "2025-08-07T07:06:25.672489Z", "shell.execute_reply": "2025-08-07T07:06:25.672207Z"}, "papermill": {"duration": 0.008565, "end_time": "2025-08-07T07:06:25.673405", "exception": false, "start_time": "2025-08-07T07:06:25.664840", "status": "completed"}, "tags": ["parameters"]}, "outputs": [], "source": ["# Parameters cell for Papermill execution\n", "NEW_SITE_NAME = \"althea_rpcs\"\n", "POINT_CLOUD_PATH = \"../../../../../data/raw/althea_rpcs/pointcloud/Point_Cloud.las\"\n", "OUTPUT_DIR = f\"output_runs/pointnet_plus_plus_inference/{NEW_SITE_NAME}\"\n", "\n", "MODEL_PATH = \"best_pointnet_iter4.pth\"  # Path to trained model\n", "\n", "DWG_PATH = \"\"  # Path to DWG file (optional)\n", "OUTPUT_DIR = \"output_runs/pointnet_plus_plus_inference\"  # Output directory\n", "\n", "# Model parameters (must match training)\n", "CONFIDENCE_THRESHOLD = 0.3  # Confidence threshold for pile detection\n", "\n", "GRID_SPACING = 10.0 # Grid spacing for analysis points\n", "PATCH_SIZE = 20.0  # meters radius for patch extraction\n", "NUM_POINTS = 1024 # KEEP 1024 to match training - CRITICAL for accuracy\n", "BATCH_SIZE = 8  # Batch size for inference\n", "\n", "# CRS and coordinate validation parameters\n", "SITE_CRS = \"EPSG:32615\"  # Will be set by papermill\n", "\n", "# Enhanced analysis parameters\n", "EXTENDED_ANALYSIS = True\n", "NEGATIVE_SAMPLE_DISTANCE = 8.0  # Distance for synthetic negatives\n", "FULL_SITE_ANALYSIS = True  # Run on all points, not subset\n", "\n", "# MLflow configuration\n", "EXPERIMENT_NAME = \"pointnet_plus_plus_inference\"\n", "RUN_NAME = f\"inference_{NEW_SITE_NAME}\""]}, {"cell_type": "code", "execution_count": 103, "id": "c4c20d9a", "metadata": {"execution": {"iopub.execute_input": "2025-08-07T07:06:25.695916Z", "iopub.status.busy": "2025-08-07T07:06:25.695769Z", "iopub.status.idle": "2025-08-07T07:06:25.697789Z", "shell.execute_reply": "2025-08-07T07:06:25.697566Z"}, "papermill": {"duration": 0.005193, "end_time": "2025-08-07T07:06:25.698515", "exception": false, "start_time": "2025-08-07T07:06:25.693322", "status": "completed"}, "tags": ["injected-parameters"]}, "outputs": [], "source": ["# Parameters\n", "NEW_SITE_NAME = \"nortan_res\"\n", "POINT_CLOUD_PATH = \"../../../../../data/raw/nortan_res/pointcloud/Block_11_2m.las\"\n", "SITE_CRS = \"EPSG:32614\"\n", "OUTPUT_DIR = \"output_runs/pointnet_plus_plus_inference/nortan_res\"\n", "RUN_NAME = \"inference_nortan_res\"\n"]}, {"cell_type": "code", "execution_count": 104, "id": "f22a587b", "metadata": {"execution": {"iopub.execute_input": "2025-08-07T07:06:25.702490Z", "iopub.status.busy": "2025-08-07T07:06:25.702373Z", "iopub.status.idle": "2025-08-07T07:06:25.704858Z", "shell.execute_reply": "2025-08-07T07:06:25.704632Z"}, "papermill": {"duration": 0.005325, "end_time": "2025-08-07T07:06:25.705697", "exception": false, "start_time": "2025-08-07T07:06:25.700372", "status": "completed"}, "tags": []}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["New Site Analysis Configuration:\n", "Site Name: nortan_res\n", "EPSG Configuration:\n", "  Inference EPSG: EPSG:32614\n", "Patch Size: 20.0m radius\n", "Points per Patch: 1024\n", "Model Path: best_pointnet_iter4.pth\n", "Point Cloud Path: ../../../../../data/raw/nortan_res/pointcloud/Block_11_2m.las\n", "Output Directory: output_runs/pointnet_plus_plus_inference/nortan_res\n", "Extended Analysis: True\n", "Full Site Analysis: True\n"]}], "source": ["# Display configuration\n", "print(f\"New Site Analysis Configuration:\")\n", "print(f\"Site Name: {NEW_SITE_NAME}\")\n", "print(f\"EPSG Configuration:\")\n", "print(f\"  Inference EPSG: {SITE_CRS}\")\n", "print(f\"Patch Size: {PATCH_SIZE}m radius\")\n", "print(f\"Points per Patch: {NUM_POINTS}\")\n", "print(f\"Model Path: {MODEL_PATH}\")\n", "print(f\"Point Cloud Path: {POINT_CLOUD_PATH}\")\n", "print(f\"Output Directory: {OUTPUT_DIR}\")\n", "print(f\"Extended Analysis: {EXTENDED_ANALYSIS}\")\n", "print(f\"Full Site Analysis: {FULL_SITE_ANALYSIS}\")\n"]}, {"cell_type": "code", "execution_count": 105, "id": "6b291543", "metadata": {"execution": {"iopub.execute_input": "2025-08-07T07:06:25.709642Z", "iopub.status.busy": "2025-08-07T07:06:25.709551Z", "iopub.status.idle": "2025-08-07T07:06:29.492402Z", "shell.execute_reply": "2025-08-07T07:06:29.492064Z"}, "papermill": {"duration": 3.78607, "end_time": "2025-08-07T07:06:29.493489", "exception": false, "start_time": "2025-08-07T07:06:25.707419", "status": "completed"}, "tags": []}, "outputs": [], "source": ["import os\n", "import json\n", "import pickle\n", "import torch\n", "import warnings\n", "import numpy as np\n", "import pandas as pd\n", "from pathlib import Path\n", "from datetime import datetime\n", "import torch.nn as nn\n", "import matplotlib.pyplot as plt\n", "import laspy\n", "import open3d as o3d\n", "import mlflow\n", "import mlflow.pytorch\n", "from scipy.spatial import cKDTree\n", "from scipy.spatial.distance import pdist\n", "import seaborn as sns\n", "\n", "warnings.filterwarnings('ignore')\n"]}, {"cell_type": "code", "execution_count": 106, "id": "setup_output_dir", "metadata": {"execution": {"iopub.execute_input": "2025-08-07T07:06:29.497739Z", "iopub.status.busy": "2025-08-07T07:06:29.497582Z", "iopub.status.idle": "2025-08-07T07:06:29.499987Z", "shell.execute_reply": "2025-08-07T07:06:29.499771Z"}, "papermill": {"duration": 0.005272, "end_time": "2025-08-07T07:06:29.500732", "exception": false, "start_time": "2025-08-07T07:06:29.495460", "status": "completed"}, "tags": []}, "outputs": [], "source": ["def setup_environment():\n", "    \"\"\"Setup output directory and MLflow tracking\"\"\"\n", "    os.makedirs(OUTPUT_DIR, exist_ok=True)\n", "    \n", "    if mlflow.active_run() is not None:\n", "        mlflow.end_run()\n", "    \n", "    mlflow.set_experiment(EXPERIMENT_NAME)\n", "    mlflow.start_run(run_name=RUN_NAME)\n", "    \n", "    # Log parameters\n", "    params = {\n", "        \"site_name\": NEW_SITE_NAME,\n", "        \"patch_size\": PATCH_SIZE,\n", "        \"num_points\": NUM_POINTS,\n", "        \"confidence_threshold\": CONFIDENCE_THRESHOLD,\n", "        \"batch_size\": BATCH_SIZE,\n", "        \"grid_spacing\": GRID_SPACING\n", "    }\n", "    for key, value in params.items():\n", "        mlflow.log_param(key, value)"]}, {"cell_type": "markdown", "id": "a0315f2c", "metadata": {"papermill": {"duration": 0.00164, "end_time": "2025-08-07T07:06:29.504282", "exception": false, "start_time": "2025-08-07T07:06:29.502642", "status": "completed"}, "tags": []}, "source": ["## PointNet++ Utility Functions "]}, {"cell_type": "code", "execution_count": 107, "id": "f547b445", "metadata": {"execution": {"iopub.execute_input": "2025-08-07T07:06:29.508067Z", "iopub.status.busy": "2025-08-07T07:06:29.507896Z", "iopub.status.idle": "2025-08-07T07:06:29.512433Z", "shell.execute_reply": "2025-08-07T07:06:29.512206Z"}, "papermill": {"duration": 0.007201, "end_time": "2025-08-07T07:06:29.513163", "exception": false, "start_time": "2025-08-07T07:06:29.505962", "status": "completed"}, "tags": []}, "outputs": [], "source": ["# PointNet++ utility functions\n", "def square_distance(src, dst):\n", "    \"\"\"Calculate squared distance between two point sets\"\"\"\n", "    B, N, _ = src.shape\n", "    _, M, _ = dst.shape\n", "    dist = -2 * torch.matmul(src, dst.permute(0, 2, 1))\n", "    dist += torch.sum(src ** 2, -1).view(B, N, 1)\n", "    dist += torch.sum(dst ** 2, -1).view(B, 1, M)\n", "    return dist\n", "\n", "def farthest_point_sample(xyz, npoint):\n", "    \"\"\"Farthest point sampling\"\"\"\n", "    device = xyz.device\n", "    B, N, C = xyz.shape\n", "    centroids = torch.zeros(B, npoint, dtype=torch.long).to(device)\n", "    distance = torch.ones(B, N).to(device) * 1e10\n", "    farthest = torch.randint(0, N, (B,), dtype=torch.long).to(device)\n", "    batch_indices = torch.arange(B, dtype=torch.long).to(device)\n", "    \n", "    for i in range(npoint):\n", "        centroids[:, i] = farthest\n", "        centroid = xyz[batch_indices, farthest, :].view(B, 1, 3)\n", "        dist = torch.sum((xyz - centroid) ** 2, -1)\n", "        mask = dist < distance\n", "        distance[mask] = dist[mask]\n", "        farthest = torch.max(distance, -1)[1]\n", "    \n", "    return centroids\n", "\n", "def query_ball_point(radius, nsample, xyz, new_xyz):\n", "    \"\"\"Query ball point grouping\"\"\"\n", "    device = xyz.device\n", "    B, N, C = xyz.shape\n", "    _, S, _ = new_xyz.shape\n", "    group_idx = torch.arange(N, dtype=torch.long).to(device).view(1, 1, N).repeat([B, S, 1])\n", "    sqrdists = square_distance(new_xyz, xyz)\n", "    group_idx[sqrdists > radius ** 2] = N\n", "    group_idx = group_idx.sort(dim=-1)[0][:, :, :nsample]\n", "    group_first = group_idx[:, :, 0].view(B, S, 1).repeat([1, 1, nsample])\n", "    mask = group_idx == N\n", "    group_idx[mask] = group_first[mask]\n", "    return group_idx"]}, {"cell_type": "markdown", "id": "8cbfede7", "metadata": {"papermill": {"duration": 0.001536, "end_time": "2025-08-07T07:06:29.516384", "exception": false, "start_time": "2025-08-07T07:06:29.514848", "status": "completed"}, "tags": []}, "source": ["## PointNet++ Set Abstraction Layer\n"]}, {"cell_type": "code", "execution_count": 108, "id": "bf8dd055", "metadata": {"execution": {"iopub.execute_input": "2025-08-07T07:06:29.520502Z", "iopub.status.busy": "2025-08-07T07:06:29.520394Z", "iopub.status.idle": "2025-08-07T07:06:29.524336Z", "shell.execute_reply": "2025-08-07T07:06:29.524105Z"}, "papermill": {"duration": 0.007172, "end_time": "2025-08-07T07:06:29.525070", "exception": false, "start_time": "2025-08-07T07:06:29.517898", "status": "completed"}, "tags": []}, "outputs": [], "source": ["class PointNetSetAbstraction(nn.Module):\n", "    def __init__(self, npoint, radius, nsample, in_channel, mlp, group_all):\n", "        super(PointNetSetAbstraction, self).__init__()\n", "        self.npoint = npoint\n", "        self.radius = radius\n", "        self.nsample = nsample\n", "        self.mlp_convs = nn.ModuleList()\n", "        self.mlp_bns = nn.ModuleList()\n", "        self.group_all = group_all\n", "        \n", "        last_channel = in_channel\n", "        for out_channel in mlp:\n", "            self.mlp_convs.append(nn.Conv2d(last_channel, out_channel, 1))\n", "            self.mlp_bns.append(nn.BatchNorm2d(out_channel))\n", "            last_channel = out_channel\n", "    \n", "    def forward(self, xyz, points):\n", "        \"\"\"Forward pass of PointNet++ Set Abstraction layer\"\"\"\n", "        B, N, C = xyz.shape\n", "        \n", "        if self.group_all:\n", "            new_xyz = torch.zeros(B, 1, C).to(xyz.device)\n", "            new_points = points.view(B, 1, N, -1) if points is not None else xyz.view(B, 1, N, C)\n", "        else:\n", "            fps_idx = farthest_point_sample(xyz, self.npoint)\n", "            new_xyz = xyz[torch.arange(B)[:, None], fps_idx]\n", "            idx = query_ball_point(self.radius, self.nsample, xyz, new_xyz)\n", "            grouped_xyz = xyz[torch.arange(B)[:, None, None], idx]\n", "            grouped_xyz_norm = grouped_xyz - new_xyz.view(B, self.npoint, 1, C)\n", "            \n", "            if points is not None:\n", "                grouped_points = points[torch.arange(B)[:, None, None], idx]\n", "                new_points = torch.cat([grouped_xyz_norm, grouped_points], dim=-1)\n", "            else:\n", "                new_points = grouped_xyz_norm\n", "        \n", "        new_points = new_points.permute(0, 3, 2, 1)\n", "        for i, conv in enumerate(self.mlp_convs):\n", "            bn = self.mlp_bns[i]\n", "            new_points = torch.relu(bn(conv(new_points)))\n", "        \n", "        new_points = torch.max(new_points, 2)[0]\n", "        new_points = new_points.permute(0, 2, 1)\n", "        \n", "        return new_xyz, new_points"]}, {"cell_type": "markdown", "id": "d5232c15", "metadata": {"papermill": {"duration": 0.001636, "end_time": "2025-08-07T07:06:29.528770", "exception": false, "start_time": "2025-08-07T07:06:29.527134", "status": "completed"}, "tags": []}, "source": ["## PointNet++ Model Architecture\n"]}, {"cell_type": "code", "execution_count": 109, "id": "3fb10d9f", "metadata": {"execution": {"iopub.execute_input": "2025-08-07T07:06:29.532481Z", "iopub.status.busy": "2025-08-07T07:06:29.532376Z", "iopub.status.idle": "2025-08-07T07:06:29.536291Z", "shell.execute_reply": "2025-08-07T07:06:29.536098Z"}, "papermill": {"duration": 0.00665, "end_time": "2025-08-07T07:06:29.537120", "exception": false, "start_time": "2025-08-07T07:06:29.530470", "status": "completed"}, "tags": []}, "outputs": [], "source": ["class PointNetPlusPlus(nn.Module):\n", "    def __init__(self, num_classes=2, in_channels=20, dropout=0.3):  # ✅ CRITICAL: in_channels=20\n", "        super(PointNetPlusPlus, self).__init__()\n", "\n", "        # Set Abstraction Layers (SAME AS TRAINING)\n", "        self.sa1 = PointNetSetAbstraction(256, 0.2, 32, in_channels, [64, 64, 128], False)\n", "        self.sa2 = PointNetSetAbstraction(64, 0.4, 64, 128 + 3, [128, 128, 256], False)\n", "        self.sa3 = PointNetSetAbstraction(None, None, None, 256, [256, 512, 1024], True)\n", "\n", "        # Classification head (SAME AS TRAINING)\n", "        self.fc1 = nn.Linear(1024, 512)\n", "        self.bn1 = nn.BatchNorm1d(512)\n", "        self.drop1 = nn.Dropout(dropout)\n", "\n", "        self.fc2 = nn.Linear(512, 256)\n", "        self.bn2 = nn.BatchNorm1d(256)\n", "        self.drop2 = nn.Dropout(dropout)\n", "\n", "        self.fc3 = nn.Linear(256, 64)\n", "        self.bn3 = nn.<PERSON><PERSON><PERSON>orm1d(64)\n", "        self.drop3 = nn.Dropout(dropout * 0.6)\n", "\n", "        self.fc4 = nn.Linear(64, num_classes)\n", "\n", "    def forward(self, xyz):\n", "        # Input shape: (B, N, C), C = in_channels (20 features)\n", "        if len(xyz.shape) == 4:\n", "            xyz = xyz.squeeze(1)\n", "\n", "        B, N, C = xyz.shape\n", "\n", "        # Split input into xyz coords and features (SAME AS TRAINING)\n", "        coords = xyz[:, :, :3]       # (B, N, 3)\n", "        features = xyz[:, :, 3:]     # (B, N, C-3) = (B, N, 17)\n", "\n", "        # Pass through SA layers\n", "        l1_xyz, l1_points = self.sa1(coords, features)\n", "        l2_xyz, l2_points = self.sa2(l1_xyz, l1_points)\n", "        l3_xyz, l3_points = self.sa3(l2_xyz, l2_points)\n", "\n", "        global_feat = l3_points.view(B, -1)\n", "\n", "        # Classification head\n", "        x = self.drop1(torch.relu(self.bn1(self.fc1(global_feat))))\n", "        x = self.drop2(torch.relu(self.bn2(self.fc2(x))))\n", "        x = self.drop3(torch.relu(self.bn3(self.fc3(x))))\n", "        x = self.fc4(x)\n", "\n", "        return x"]}, {"cell_type": "markdown", "id": "2f5e408b", "metadata": {"papermill": {"duration": 0.00159, "end_time": "2025-08-07T07:06:29.540896", "exception": false, "start_time": "2025-08-07T07:06:29.539306", "status": "completed"}, "tags": []}, "source": ["## Load"]}, {"cell_type": "code", "execution_count": 110, "id": "3838119b", "metadata": {"execution": {"iopub.execute_input": "2025-08-07T07:06:29.545083Z", "iopub.status.busy": "2025-08-07T07:06:29.544778Z", "iopub.status.idle": "2025-08-07T07:06:29.549134Z", "shell.execute_reply": "2025-08-07T07:06:29.548903Z"}, "papermill": {"duration": 0.007006, "end_time": "2025-08-07T07:06:29.549854", "exception": false, "start_time": "2025-08-07T07:06:29.542848", "status": "completed"}, "tags": []}, "outputs": [], "source": ["def load_point_cloud(file_path):\n", "    \"\"\"Load point cloud from supported formats (.las, .ply, .pcd)\"\"\"\n", "    file_path = Path(file_path)\n", "    \n", "    if not file_path.exists():\n", "        print(f\"File not found: {file_path}\")\n", "        return None\n", "    \n", "    suffix = file_path.suffix.lower()\n", "    \n", "    try:\n", "        if suffix == '.las':\n", "            las_file = laspy.read(file_path)\n", "            points = np.vstack([las_file.x, las_file.y, las_file.z]).T\n", "            print(f\"Loaded LAS file with {len(points):,} points\")\n", "                \n", "        elif suffix in ['.ply', '.pcd']:\n", "            pcd = o3d.io.read_point_cloud(str(file_path))\n", "            points = np.asarray(pcd.points)\n", "            \n", "            if len(points) == 0:\n", "                print(f\"Warning: {suffix.upper()} file contains no points\")\n", "                return None\n", "                \n", "            print(f\"Loaded {suffix.upper()} file with {len(points):,} points\")\n", "                \n", "        else:\n", "            print(f\"Unsupported file format: {suffix}. Supported formats: .las, .ply, .pcd\")\n", "            return None\n", "        \n", "        if len(points) == 0:\n", "            print(\"Warning: Point cloud contains no points\")\n", "            return None\n", "            \n", "        print(f\"Point cloud bounds:\")\n", "        print(f\"  X: {points[:, 0].min():.2f} to {points[:, 0].max():.2f}\")\n", "        print(f\"  Y: {points[:, 1].min():.2f} to {points[:, 1].max():.2f}\")\n", "        print(f\"  Z: {points[:, 2].min():.2f} to {points[:, 2].max():.2f}\")\n", "        \n", "        return points\n", "        \n", "    except Exception as e:\n", "        print(f\"Error loading point cloud: {e}\")\n", "        return None\n", "\n", "from pyproj import Transformer\n", "\n", "def transform_coordinates(coords, source_crs, target_crs):\n", "    \"\"\"\n", "    Transform coordinates from source CRS to target CRS\n", "    \"\"\"\n", "    if source_crs == target_crs:\n", "        return coords\n", "    \n", "    try:\n", "        # Create transformer\n", "        transformer = Transformer.from_crs(source_crs, target_crs, always_xy=True)\n", "        \n", "        # Transform X, Y coordinates (Z stays the same)\n", "        x_new, y_new = transformer.transform(coords[:, 0], coords[:, 1])\n", "        \n", "        # Combine with original Z\n", "        coords_new = np.column_stack([x_new, y_new, coords[:, 2]])\n", "        \n", "        print(f\"Transformed {len(coords)} points from {source_crs} to {target_crs}\")\n", "        return coords_new\n", "        \n", "    except Exception as e:\n", "        print(f\"CRS transformation failed: {e}\")\n", "        return coords"]}, {"cell_type": "code", "execution_count": 111, "id": "model_validation", "metadata": {"execution": {"iopub.execute_input": "2025-08-07T07:06:29.553636Z", "iopub.status.busy": "2025-08-07T07:06:29.553544Z", "iopub.status.idle": "2025-08-07T07:06:29.556149Z", "shell.execute_reply": "2025-08-07T07:06:29.555940Z"}, "papermill": {"duration": 0.005373, "end_time": "2025-08-07T07:06:29.556863", "exception": false, "start_time": "2025-08-07T07:06:29.551490", "status": "completed"}, "tags": []}, "outputs": [], "source": ["def load_model(model_path, device):\n", "    \"\"\"Load trained PointNet++ model\"\"\"\n", "    try:\n", "        model = PointNetPlusPlus(num_classes=2, in_channels=20, dropout=0.3).to(device)\n", "        \n", "        if Path(model_path).exists():\n", "            checkpoint = torch.load(model_path, map_location=device)\n", "            \n", "            if isinstance(checkpoint, dict) and 'model_state_dict' in checkpoint:\n", "                model.load_state_dict(checkpoint['model_state_dict'])\n", "                print(f\"Loaded checkpoint from epoch {checkpoint.get('epoch', 'unknown')}\")\n", "            else:\n", "                model.load_state_dict(checkpoint)\n", "            \n", "            model.eval()\n", "            param_count = sum(p.numel() for p in model.parameters())\n", "            print(f\"Loaded trained PointNet++ model with {param_count:,} parameters\")\n", "            return model, True\n", "        else:\n", "            print(f\"Model file not found: {model_path}\")\n", "            return model, False\n", "            \n", "    except Exception as e:\n", "        print(f\"Error loading model: {e}\")\n", "        return PointNetPlusPlus(num_classes=2, in_channels=20, dropout=0.3).to(device), False"]}, {"cell_type": "code", "execution_count": 112, "id": "a5f0d927", "metadata": {"execution": {"iopub.execute_input": "2025-08-07T07:06:29.560520Z", "iopub.status.busy": "2025-08-07T07:06:29.560440Z", "iopub.status.idle": "2025-08-07T07:06:29.563076Z", "shell.execute_reply": "2025-08-07T07:06:29.562882Z"}, "papermill": {"duration": 0.005277, "end_time": "2025-08-07T07:06:29.563793", "exception": false, "start_time": "2025-08-07T07:06:29.558516", "status": "completed"}, "tags": []}, "outputs": [], "source": ["def create_analysis_grid(point_cloud, grid_spacing=15.0, buffer_factor=1.5):\n", "    \"\"\"Create analysis grid with spatial bounds optimization\"\"\"\n", "    print(f\"Point cloud has {len(point_cloud):,} points\")\n", "    \n", "    # Get bounds efficiently\n", "    x_min, x_max = point_cloud[:, 0].min(), point_cloud[:, 0].max()\n", "    y_min, y_max = point_cloud[:, 1].min(), point_cloud[:, 1].max()\n", "    \n", "    print(f\"Point cloud bounds: X({x_min:.1f}, {x_max:.1f}), Y({y_min:.1f}, {y_max:.1f})\")\n", "    \n", "    buffer = grid_spacing * buffer_factor\n", "    x_coords = np.arange(x_min - buffer, x_max + buffer, grid_spacing)\n", "    y_coords = np.arange(y_min - buffer, y_max + buffer, grid_spacing)\n", "    \n", "    grid_points = np.array([[x, y] for x in x_coords for y in y_coords])\n", "    print(f\"Created analysis grid with {len(grid_points)} points\")\n", "    \n", "    return grid_points\n"]}, {"cell_type": "code", "execution_count": 113, "id": "2d36b96c", "metadata": {"execution": {"iopub.execute_input": "2025-08-07T07:06:29.567542Z", "iopub.status.busy": "2025-08-07T07:06:29.567423Z", "iopub.status.idle": "2025-08-07T07:06:29.573312Z", "shell.execute_reply": "2025-08-07T07:06:29.573085Z"}, "papermill": {"duration": 0.008557, "end_time": "2025-08-07T07:06:29.574021", "exception": false, "start_time": "2025-08-07T07:06:29.565464", "status": "completed"}, "tags": []}, "outputs": [], "source": ["def extract_patch_features(point_cloud, center_xy, radius=20.0, num_points=1024, kdtree=None):\n", "    \"\"\"Extract patch features EXACTLY matching training preprocessing\"\"\"\n", "    center_x, center_y = center_xy\n", "    \n", "    if kdtree is not None:\n", "        indices = kdtree.query_ball_point([center_x, center_y], radius)\n", "        if len(indices) < 50:\n", "            return None\n", "        patch_xyz = point_cloud[indices]\n", "    else:\n", "        distances_2d = np.sqrt((point_cloud[:, 0] - center_x)**2 + \n", "                              (point_cloud[:, 1] - center_y)**2)\n", "        mask = distances_2d <= radius\n", "        patch_xyz = point_cloud[mask]\n", "        \n", "        if len(patch_xyz) < 50:\n", "            return None\n", "    \n", "    # MATCH training preprocessing exactly - use same feature engineering as training data creation\n", "    x, y, z = patch_xyz[:, 0], patch_xyz[:, 1], patch_xyz[:, 2]\n", "    \n", "    # Calculate patch statistics\n", "    patch_center = np.mean(patch_xyz, axis=0)\n", "    z_mean, z_std = z.mean(), z.std() + 1e-6\n", "    z_min, z_max = z.min(), z.max()\n", "    \n", "    # Distance calculations\n", "    dist_to_center = np.sqrt((x - center_x)**2 + (y - center_y)**2)\n", "    \n", "    # RECREATE EXACT TRAINING FEATURES - these come from the original data creation process\n", "    features_list = []\n", "    for i, point in enumerate(patch_xyz):\n", "        px, py, pz = point\n", "        \n", "        # Match the exact feature engineering from training data preparation\n", "        feature_vector = [\n", "            px,  # 0: raw X coordinate\n", "            py - center_y,  # 1: relative Y (small values like training)\n", "            pz,  # 2: raw Z coordinate  \n", "            (pz - z_mean) / z_std,  # 3: height_norm\n", "            dist_to_center[i],  # 4: distance_norm\n", "            len(patch_xyz) / 1000.0,  # 5: density\n", "            px - center_x,  # 6: relative_x\n", "            py - center_y,  # 7: relative_y  \n", "            pz - patch_center[2],  # 8: relative_z\n", "            pz - z_min,  # 9: height_above_min\n", "            z_max - pz,  # 10: depth_below_max\n", "            abs(pz - z_mean),  # 11: abs_height_deviation\n", "            np.sqrt(px**2 + py**2),  # 12: distance_from_origin\n", "            np.arctan2(py - center_y, px - center_x),  # 13: angle\n", "            (px - center_x) * (py - center_y),  # 14: interaction\n", "            px - patch_center[0],  # 15: patch_relative_x\n", "            py - patch_center[1],  # 16: patch_relative_y\n", "            pz / (dist_to_center[i] + 1e-6),  # 17: height_distance_ratio\n", "            np.sqrt((px - center_x)**2 + (py - center_y)**2 + (pz - z_mean)**2),  # 18: 3d_distance\n", "            dist_to_center[i] + np.random.normal(0, 0.01)  # 19: distance with slight variation\n", "        ]\n", "        features_list.append(feature_vector)\n", "    \n", "    patch_features = np.array(features_list, dtype=np.float32)\n", "    \n", "    # EXACT SAME SAMPLING LOGIC AS TRAINING\n", "    if len(patch_features) >= num_points:\n", "        # Distance-weighted sampling like training\n", "        distances = patch_features[:, 4]  # distance_norm column\n", "        probabilities = 1 / (distances + 0.1)\n", "        probabilities /= probabilities.sum()\n", "        \n", "        sampled_indices = np.random.choice(len(patch_features), num_points, \n", "                                         replace=False, p=probabilities)\n", "        sampled = patch_features[sampled_indices]\n", "    else:\n", "        # Upsample with weighted selection like training\n", "        upsampled = patch_features.copy()\n", "        needed = num_points - len(patch_features)\n", "        \n", "        for _ in range(needed):\n", "            distances = patch_features[:, 4]\n", "            weights = 1 / (distances + 0.1)\n", "            weights /= weights.sum()\n", "            source_idx = np.random.choice(len(patch_features), p=weights)\n", "            \n", "            new_point = patch_features[source_idx].copy()\n", "            new_point[:3] += np.random.normal(0, 0.02, 3)  # Add noise to spatial like training\n", "            upsampled = np.vstack([upsampled, new_point])\n", "        \n", "        sampled = upsampled[:num_points]\n", "    \n", "    # EXACT SAME NORMALIZATION AS TRAINING\n", "    # Training shows: sampled /= np.max(np.linalg.norm(sampled, axis=1)) or 1\n", "    spatial_coords = sampled[:, :3]\n", "    spatial_extent = np.max(np.linalg.norm(spatial_coords, axis=1))\n", "    if spatial_extent > 0:\n", "        sampled[:, :3] /= spatial_extent\n", "    \n", "    return sampled\n", "\n"]}, {"cell_type": "code", "execution_count": 114, "id": "8444b060", "metadata": {"execution": {"iopub.execute_input": "2025-08-07T07:06:29.577857Z", "iopub.status.busy": "2025-08-07T07:06:29.577751Z", "iopub.status.idle": "2025-08-07T07:06:29.582470Z", "shell.execute_reply": "2025-08-07T07:06:29.582226Z"}, "papermill": {"duration": 0.007478, "end_time": "2025-08-07T07:06:29.583208", "exception": false, "start_time": "2025-08-07T07:06:29.575730", "status": "completed"}, "tags": []}, "outputs": [], "source": ["def process_site_inference(point_cloud, grid_points, model, device, \n", "                          batch_size=16, radius=20.0, num_points=1024):\n", "    \"\"\"CPU-optimized inference maintaining 1024 points for accuracy\"\"\"\n", "    print(f\"Building spatial index for {len(point_cloud):,} points...\")\n", "    kdtree = cKDTree(point_cloud[:, :2])\n", "    \n", "    print(\"Pre-filtering valid grid points...\")\n", "    valid_grid_points = []\n", "    for i, center in enumerate(grid_points):\n", "        if i % 500 == 0:\n", "            print(f\"  Pre-filtering progress: {i}/{len(grid_points)}\")\n", "        \n", "        indices = kdtree.query_ball_point([center[0], center[1]], radius)\n", "        if len(indices) >= 50:  # Keep higher threshold for 1024 points\n", "            valid_grid_points.append(center)\n", "    \n", "    valid_grid_points = np.array(valid_grid_points)\n", "    print(f\"Filtered to {len(valid_grid_points)} valid grid points (from {len(grid_points)})\")\n", "    \n", "    if len(valid_grid_points) == 0:\n", "        return pd.DataFrame()\n", "    \n", "    results = []\n", "    total_batches = len(valid_grid_points) // batch_size + (1 if len(valid_grid_points) % batch_size != 0 else 0)\n", "    \n", "    print(f\"Processing {len(valid_grid_points)} points with 1024 points/patch (CPU optimized)...\")\n", "    print(f\"This maintains training accuracy but will be slower than 512 points\")\n", "    \n", "    model.eval()\n", "    if device.type == 'cpu':\n", "        torch.set_num_threads(4)\n", "    \n", "    for i in range(0, len(valid_grid_points), batch_size):\n", "        batch_idx = i // batch_size + 1\n", "        if batch_idx % 3 == 1:  # More frequent updates since batches are smaller\n", "            print(f\"  Batch {batch_idx}/{total_batches} ({i}/{len(valid_grid_points)} points)\")\n", "        \n", "        batch_centers = valid_grid_points[i:i+batch_size]\n", "        batch_patches = []\n", "        valid_centers = []\n", "        \n", "        for center in batch_centers:\n", "            patch = extract_patch_features(point_cloud, center, radius, num_points, kdtree)\n", "            if patch is not None:\n", "                batch_patches.append(patch)\n", "                valid_centers.append(center)\n", "        \n", "        if len(batch_patches) == 0:\n", "            continue\n", "        \n", "        batch_tensor = torch.FloatTensor(np.array(batch_patches)).to(device)\n", "        \n", "        with torch.no_grad():\n", "            outputs = model(batch_tensor)\n", "            probabilities = torch.softmax(outputs, dim=1)\n", "            pile_probs = probabilities[:, 1].cpu().numpy()\n", "        \n", "        for center, prob in zip(valid_centers, pile_probs):\n", "            results.append({\n", "                'x': center[0],\n", "                'y': center[1],\n", "                'pile_probability': prob,\n", "                'prediction': 'PILE' if prob > CONFIDENCE_THRESHOLD else 'NON-PILE'\n", "            })\n", "    \n", "    print(f\"CPU processing complete: {len(results)} successful predictions\")\n", "    return pd.DataFrame(results)\n"]}, {"cell_type": "code", "execution_count": 115, "id": "89ce4683", "metadata": {"execution": {"iopub.execute_input": "2025-08-07T07:06:29.587105Z", "iopub.status.busy": "2025-08-07T07:06:29.586989Z", "iopub.status.idle": "2025-08-07T07:06:29.590523Z", "shell.execute_reply": "2025-08-07T07:06:29.590196Z"}, "papermill": {"duration": 0.006661, "end_time": "2025-08-07T07:06:29.591590", "exception": false, "start_time": "2025-08-07T07:06:29.584929", "status": "completed"}, "tags": []}, "outputs": [], "source": ["def visualize_results(results_df, output_dir, site_name):\n", "    \"\"\"Create visualization of pile detection results\"\"\"\n", "    if results_df.empty:\n", "        print(\"No results to visualize\")\n", "        return\n", "    \n", "    pile_detections = results_df[results_df['prediction'] == 'PILE']\n", "    \n", "    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))\n", "    \n", "    # Plot 1: Probability heatmap\n", "    sc1 = ax1.scatter(\n", "        results_df['x'], results_df['y'],\n", "        c=results_df['pile_probability'],\n", "        cmap='viridis', s=25, alpha=0.8\n", "    )\n", "    ax1.set_title('Pile Probability Heatmap')\n", "    ax1.set_xlabel('X Coordinate')\n", "    ax1.set_ylabel('Y Coordinate')\n", "    plt.colorbar(sc1, ax=ax1, label='Probability')\n", "    \n", "    # Plot 2: Pile classifications\n", "    ax2.scatter(\n", "        pile_detections['x'], pile_detections['y'],\n", "        color='darkgreen', label='Pile Detections',\n", "        s=30, alpha=0.8\n", "    )\n", "    \n", "    non_pile = results_df[results_df['prediction'] == 'NON-PILE']\n", "    if not non_pile.empty:\n", "        ax2.scatter(\n", "            non_pile['x'], non_pile['y'],\n", "            color='gray', label='Non-Pile',\n", "            s=15, alpha=0.4\n", "        )\n", "    \n", "    ax2.set_title('Pile Classification Results')\n", "    ax2.set_xlabel('X Coordinate')\n", "    ax2.set_ylabel('Y Coordinate')\n", "    ax2.legend()\n", "    \n", "    plt.tight_layout()\n", "    \n", "    plot_path = Path(output_dir) / f\"{site_name}_pile_visualization.png\"\n", "    plt.savefig(plot_path, dpi=300, bbox_inches='tight')\n", "    print(f\"Saved visualization to: {plot_path}\")\n", "    \n", "    plt.show()\n", "    return str(plot_path)"]}, {"cell_type": "code", "execution_count": 116, "id": "1d9a4e23", "metadata": {"execution": {"iopub.execute_input": "2025-08-07T07:06:29.596599Z", "iopub.status.busy": "2025-08-07T07:06:29.596479Z", "iopub.status.idle": "2025-08-07T07:06:29.600133Z", "shell.execute_reply": "2025-08-07T07:06:29.599762Z"}, "papermill": {"duration": 0.007, "end_time": "2025-08-07T07:06:29.601156", "exception": false, "start_time": "2025-08-07T07:06:29.594156", "status": "completed"}, "tags": []}, "outputs": [], "source": ["def export_results(results_df, output_dir, site_name, model_path, patch_size, confidence_threshold):\n", "    \"\"\"Export results and summary statistics\"\"\"\n", "    if results_df.empty:\n", "        print(\"No results to export\")\n", "        return\n", "    \n", "    timestamp = datetime.now().strftime(\"%Y%m%d_%H%M%S\")\n", "    \n", "    # Export main results\n", "    output_file = Path(output_dir) / f\"{site_name}_pile_detections_{timestamp}.csv\"\n", "    results_df.to_csv(output_file, index=False)\n", "    print(f\"Results exported to: {output_file}\")\n", "    \n", "    # Create summary statistics\n", "    pile_detections = results_df[results_df['prediction'] == 'PILE']\n", "    \n", "    summary = {\n", "        'site_name': site_name,\n", "        'analysis_timestamp': timestamp,\n", "        'total_analysis_points': len(results_df),\n", "        'pile_detections': len(pile_detections),\n", "        'detection_rate': len(pile_detections) / len(results_df),\n", "        'average_pile_probability': float(results_df['pile_probability'].mean()),\n", "        'high_confidence_detections': int(sum(results_df['pile_probability'] > 0.9)),\n", "        'model_path': model_path,\n", "        'patch_size_meters': patch_size,\n", "        'confidence_threshold': confidence_threshold\n", "    }\n", "    \n", "    summary_file = Path(output_dir) / f\"{site_name}_analysis_summary_{timestamp}.json\"\n", "    with open(summary_file, 'w') as f:\n", "        json.dump(summary, f, indent=2)\n", "    \n", "    print(f\"Summary statistics saved to: {summary_file}\")\n", "    \n", "    # Log to MLflow\n", "    for key, value in summary.items():\n", "        if isinstance(value, (int, float)):\n", "            mlflow.log_metric(key, value)\n", "    \n", "    mlflow.log_artifact(str(output_file))\n", "    mlflow.log_artifact(str(summary_file))\n", "    \n", "    return summary\n", "\n"]}, {"cell_type": "code", "execution_count": 117, "id": "4a52cac4", "metadata": {"execution": {"iopub.execute_input": "2025-08-07T07:06:29.605520Z", "iopub.status.busy": "2025-08-07T07:06:29.605199Z", "iopub.status.idle": "2025-08-07T07:06:29.676418Z", "shell.execute_reply": "2025-08-07T07:06:29.676118Z"}, "papermill": {"duration": 0.074394, "end_time": "2025-08-07T07:06:29.677341", "exception": false, "start_time": "2025-08-07T07:06:29.602947", "status": "completed"}, "tags": []}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Starting PointNet++ Pile Detection Pipeline\n", "Site: nortan_res\n", "Configuration: 20.0m patches, 1024 points each\n", "Using device: cpu\n"]}], "source": ["print(\"Starting PointNet++ Pile Detection Pipeline\")\n", "print(f\"Site: {NEW_SITE_NAME}\")\n", "print(f\"Configuration: {PATCH_SIZE}m patches, {NUM_POINTS} points each\")\n", "    \n", "# Setup\n", "setup_environment()\n", "device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')\n", "print(f\"Using device: {device}\")"]}, {"cell_type": "code", "execution_count": 118, "id": "6bb565de", "metadata": {"execution": {"iopub.execute_input": "2025-08-07T07:06:29.682746Z", "iopub.status.busy": "2025-08-07T07:06:29.682585Z", "iopub.status.idle": "2025-08-07T07:06:31.225349Z", "shell.execute_reply": "2025-08-07T07:06:31.224365Z"}, "papermill": {"duration": 1.549229, "end_time": "2025-08-07T07:06:31.229203", "exception": false, "start_time": "2025-08-07T07:06:29.679974", "status": "completed"}, "tags": []}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Loaded checkpoint from epoch 97\n", "Loaded trained PointNet++ model with 1,482,434 parameters\n", "Loaded LAS file with 35,565,352 points\n", "Point cloud bounds:\n", "  X: 385723.95 to 385809.63\n", "  Y: 3529182.83 to 3529447.01\n", "  Z: 553.38 to 556.27\n"]}], "source": ["# Load model\n", "model, model_loaded = load_model(MODEL_PATH, device)\n", "    \n", "# Load point cloud\n", "point_cloud = load_point_cloud(POINT_CLOUD_PATH)\n", "if point_cloud is None:\n", "    print(\"Failed to load point cloud. Exiting.\")\n", "    mlflow.end_run()    "]}, {"cell_type": "code", "execution_count": 119, "id": "703fc072", "metadata": {"execution": {"iopub.execute_input": "2025-08-07T07:06:31.234766Z", "iopub.status.busy": "2025-08-07T07:06:31.234622Z", "iopub.status.idle": "2025-08-07T07:06:31.258164Z", "shell.execute_reply": "2025-08-07T07:06:31.257929Z"}, "papermill": {"duration": 0.027013, "end_time": "2025-08-07T07:06:31.258961", "exception": false, "start_time": "2025-08-07T07:06:31.231948", "status": "completed"}, "tags": []}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Point cloud has 35,565,352 points\n", "Point cloud bounds: X(385724.0, 385809.6), Y(3529182.8, 3529447.0)\n", "Created analysis grid with 360 points\n"]}], "source": ["# Create analysis grid\n", "grid_points = create_analysis_grid(point_cloud, GRID_SPACING)"]}, {"cell_type": "code", "execution_count": 120, "id": "98181738", "metadata": {"execution": {"iopub.execute_input": "2025-08-07T07:06:31.263974Z", "iopub.status.busy": "2025-08-07T07:06:31.263879Z", "iopub.status.idle": "2025-08-07T07:13:22.313510Z", "shell.execute_reply": "2025-08-07T07:13:22.313159Z"}, "papermill": {"duration": 411.058113, "end_time": "2025-08-07T07:13:22.319227", "exception": false, "start_time": "2025-08-07T07:06:31.261114", "status": "completed"}, "tags": []}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Building spatial index for 35,565,352 points...\n", "Pre-filtering valid grid points...\n", "  Pre-filtering progress: 0/360\n", "Filtered to 357 valid grid points (from 360)\n", "Processing 357 points with 1024 points/patch (CPU optimized)...\n", "This maintains training accuracy but will be slower than 512 points\n", "  Batch 1/45 (0/357 points)\n", "  Batch 4/45 (24/357 points)\n", "  Batch 7/45 (48/357 points)\n", "  Batch 10/45 (72/357 points)\n", "  Batch 13/45 (96/357 points)\n", "  Batch 16/45 (120/357 points)\n", "  Batch 19/45 (144/357 points)\n", "  Batch 22/45 (168/357 points)\n", "  Batch 25/45 (192/357 points)\n", "  Batch 28/45 (216/357 points)\n", "  Batch 31/45 (240/357 points)\n", "  Batch 34/45 (264/357 points)\n", "  Batch 37/45 (288/357 points)\n", "  Batch 40/45 (312/357 points)\n", "  Batch 43/45 (336/357 points)\n", "CPU processing complete: 357 successful predictions\n"]}], "source": ["# Run inference\n", "results_df = process_site_inference(\n", "    point_cloud, grid_points, model, device,\n", "    batch_size=BATCH_SIZE, radius=PATCH_SIZE, num_points=NUM_POINTS\n", ")\n"]}, {"cell_type": "code", "execution_count": 121, "id": "8e8f2c88", "metadata": {"execution": {"iopub.execute_input": "2025-08-07T07:13:22.324806Z", "iopub.status.busy": "2025-08-07T07:13:22.324519Z", "iopub.status.idle": "2025-08-07T07:13:22.868613Z", "shell.execute_reply": "2025-08-07T07:13:22.868338Z"}, "papermill": {"duration": 0.547664, "end_time": "2025-08-07T07:13:22.869398", "exception": false, "start_time": "2025-08-07T07:13:22.321734", "status": "completed"}, "tags": []}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "Analysis Results:\n", "  Total analysis points: 357\n", "  Pile detections: 357 (100.0%)\n", "  Average confidence: 1.0000\n", "Saved visualization to: output_runs/pointnet_plus_plus_inference/nortan_res/nortan_res_pile_visualization.png\n"]}, {"data": {"image/png": "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**********************************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", "text/plain": ["<Figure size 1500x600 with 3 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["Results exported to: output_runs/pointnet_plus_plus_inference/nortan_res/nortan_res_pile_detections_20250807_141938.csv\n", "Summary statistics saved to: output_runs/pointnet_plus_plus_inference/nortan_res/nortan_res_analysis_summary_20250807_141938.json\n", "\n", "Pipeline complete. Results saved to: output_runs/pointnet_plus_plus_inference/nortan_res\n"]}], "source": ["if not results_df.empty:\n", "    # Print summary\n", "    pile_count = len(results_df[results_df['prediction'] == 'PILE'])\n", "    print(f\"\\nAnalysis Results:\")\n", "    print(f\"  Total analysis points: {len(results_df)}\")\n", "    print(f\"  Pile detections: {pile_count} ({pile_count/len(results_df)*100:.1f}%)\")\n", "    print(f\"  Average confidence: {results_df['pile_probability'].mean():.4f}\")\n", "        \n", "    # Visualize and export\n", "    plot_path = visualize_results(results_df, OUTPUT_DIR, NEW_SITE_NAME)\n", "    \n", "    if plot_path:\n", "        mlflow.log_artifact(plot_path)\n", "\n", "    export_results(results_df, OUTPUT_DIR, NEW_SITE_NAME, \n", "                      MOD<PERSON>_PATH, PATCH_SIZE, CONFIDENCE_THRESHOLD)\n", "    \n", "    # Cleanup\n", "    mlflow.end_run()\n", "    print(f\"\\nPipeline complete. Results saved to: {OUTPUT_DIR}\")"]}, {"cell_type": "code", "execution_count": 122, "id": "27ac0f40", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["🔧 FIXING YOUR EXISTING FILE: output_runs/pointnet_plus_plus_inference/nortan_res/nortan_res_pile_detections_20250807_141938.csv\n", "✅ Loaded 357 predictions from CSV\n", "✅ Loaded point cloud with 35,565,352 points\n", "\n", "✅ RESULTS:\n", "   • Spatial offset corrected: 3.2 meters\n", "   • Realistic detections: 357 (instead of 357)\n", "   • Fixed file saved: output_runs/pointnet_plus_plus_inference/nortan_res/nortan_res_pile_detections_20250807_141938_FIXED.csv\n", "\n", "📋 NEXT STEPS:\n", "1. Load this file in QGIS: output_runs/pointnet_plus_plus_inference/nortan_res/nortan_res_pile_detections_20250807_141938_FIXED.csv\n", "2. Use X=x, Y=y, CRS=EPSG:32614\n", "3. Filter to show only prediction='PILE' for pile locations\n"]}], "source": ["# SIMPLE FIX FOR YOUR EXISTING CSV FILE\n", "import pandas as pd\n", "import numpy as np\n", "from pathlib import Path\n", "\n", "def fix_existing_csv_file():\n", "    \"\"\"Fix the CSV file you already have\"\"\"\n", "    \n", "    # Your existing file\n", "    csv_file = \"output_runs/pointnet_plus_plus_inference/nortan_res/nortan_res_pile_detections_20250807_141938.csv\"\n", "    \n", "    print(f\"🔧 FIXING YOUR EXISTING FILE: {csv_file}\")\n", "    \n", "    # 1. Load your CSV\n", "    try:\n", "        df = pd.read_csv(csv_file)\n", "        print(f\"✅ Loaded {len(df)} predictions from CSV\")\n", "    except:\n", "        print(f\"❌ Could not find file: {csv_file}\")\n", "        print(\"Make sure the file path is correct\")\n", "        return None\n", "    \n", "    # 2. Load point cloud for spatial reference\n", "    point_cloud_file = \"../../../../../data/raw/nortan_res/pointcloud/Block_11_2m.las\"\n", "    \n", "    try:\n", "        import laspy\n", "        las_file = laspy.read(point_cloud_file)\n", "        points = np.vstack([las_file.x, las_file.y, las_file.z]).T\n", "        print(f\"✅ Loaded point cloud with {len(points):,} points\")\n", "    except:\n", "        print(\"❌ Could not load point cloud. Using estimated bounds.\")\n", "        # Use your known site bounds from the notebook\n", "        points = np.array([[385723.95, 3529182.83, 553.38],\n", "                          [385809.63, 3529447.01, 556.27]])\n", "    \n", "    # 3. FIX SPATIAL SHIFT\n", "    # Point cloud center\n", "    pc_center_x = (points[:, 0].min() + points[:, 0].max()) / 2\n", "    pc_center_y = (points[:, 1].min() + points[:, 1].max()) / 2\n", "    \n", "    # Prediction center\n", "    pred_center_x = df['x'].mean()\n", "    pred_center_y = df['y'].mean()\n", "    \n", "    # Calculate and apply offset\n", "    offset_x = pc_center_x - pred_center_x\n", "    offset_y = pc_center_y - pred_center_y\n", "    \n", "    df['x_fixed'] = df['x'] + offset_x\n", "    df['y_fixed'] = df['y'] + offset_y\n", "    \n", "    # 4. FIX OVERFITTING ISSUE\n", "    # Apply realistic confidence threshold (since 100% detection is unrealistic)\n", "    confidence_threshold = 0.85\n", "    df['realistic_prediction'] = df['pile_probability'].apply(\n", "        lambda x: 'PILE' if x > confidence_threshold else 'NON-PILE'\n", "    )\n", "    \n", "    realistic_piles = len(df[df['realistic_prediction'] == 'PILE'])\n", "    \n", "    # 5. CREATE FIXED CSV\n", "    df_fixed = df[['x_fixed', 'y_fixed', 'pile_probability', 'realistic_prediction']].copy()\n", "    df_fixed.columns = ['x', 'y', 'pile_probability', 'prediction']\n", "    \n", "    # Save fixed file\n", "    fixed_file = csv_file.replace('.csv', '_FIXED.csv')\n", "    df_fixed.to_csv(fixed_file, index=False)\n", "    \n", "    print(f\"\\n✅ RESULTS:\")\n", "    print(f\"   • Spatial offset corrected: {np.sqrt(offset_x**2 + offset_y**2):.1f} meters\")\n", "    print(f\"   • Realistic detections: {realistic_piles} (instead of {len(df)})\")\n", "    print(f\"   • Fixed file saved: {fixed_file}\")\n", "    \n", "    print(f\"\\n📋 NEXT STEPS:\")\n", "    print(f\"1. Load this file in QGIS: {fixed_file}\")\n", "    print(f\"2. Use X=x, Y=y, CRS=EPSG:32614\")\n", "    print(f\"3. Filter to show only prediction='PILE' for pile locations\")\n", "    \n", "    return df_fixed, fixed_file\n", "\n", "# RUN THE FIX\n", "fixed_df, fixed_file_path = fix_existing_csv_file()"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}, "papermill": {"default_parameters": {}, "duration": 419.056281, "end_time": "2025-08-07T07:13:23.898106", "environment_variables": {}, "exception": null, "input_path": "04_pointnet_plus_plus_inference.ipynb", "output_path": "04_pointnet_plus_plus_inference_nortan_res_executed.ipynb", "parameters": {"NEW_SITE_NAME": "nortan_res", "OUTPUT_DIR": "output_runs/pointnet_plus_plus_inference/nortan_res", "POINT_CLOUD_PATH": "../../../../../data/raw/nortan_res/pointcloud/Block_11_2m.las", "RUN_NAME": "inference_nortan_res", "SITE_CRS": "EPSG:32614"}, "start_time": "2025-08-07T07:06:24.841825", "version": "2.6.0"}}, "nbformat": 4, "nbformat_minor": 5}