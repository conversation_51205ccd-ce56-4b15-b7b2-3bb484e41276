{
 "cells": [
  {
   "cell_type": "markdown",
   "id": "header",
   "metadata": {},
   "source": [
    "# Multi-Site PointNet++ Pile Verification Runner\n",
    "\n",
    "This notebook uses Papermill to execute the PointNet++ pile verification notebook across multiple construction sites.\n",
    "\n",
    "**Workflow:**\n",
    "1. Configure multiple sites with their point cloud and expected pile coordinate paths\n",
    "2. Execute pile verification notebook for each site using Papermill\n",
    "3. Generate separate executed notebooks for each site\n",
    "4. Provide summary of verification results\n",
    "\n",
    "**Author**: Preetam Balijepalli  \n",
    "**Date**: August 2025"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "id": "imports",
   "metadata": {},
   "outputs": [],
   "source": [
    "import os\n",
    "import sys\n",
    "import papermill as pm\n",
    "from pathlib import Path\n",
    "from datetime import datetime\n",
    "import pandas as pd\n",
    "\n",
    "print(\"Imports completed successfully\")"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "id": "site_configs",
   "metadata": {},
   "outputs": [],
   "source": [
    "# Site configurations for pile verification\n",
    "SITE_CONFIGS = [\n",
    "    {\n",
    "        \"site_name\": \"trino_enel\",\n",
    "        \"point_cloud_path\": \"../../../../../data/processed/trino_enel/denoising/trino_enel_denoised_for_registration.ply\",\n",
    "        \"expected_piles_path\": \"../../../../../data/processed/trino_enel/ifc_metadata/ifc_pile_coordinates.csv\",\n",
    "        \"description\": \"Trino ENEL site with IFC pile coordinates\"\n",
    "    },\n",
    "    {\n",
    "        \"site_name\": \"nortan_res\",\n",
    "        \"point_cloud_path\": \"../../../../../data/processed/nortan_res/denoising/nortan_res_denoised.ply\",\n",
    "        \"expected_piles_path\": \"../../../../../data/processed/nortan_res/buffer_zones/kml_center_coordinates.csv\",\n",
    "        \"description\": \"Nortan RES site with KML buffer zone centers\"\n",
    "    },\n",
    "    {\n",
    "        \"site_name\": \"althea_rpcs\",\n",
    "        \"point_cloud_path\": \"../../../../../data/processed/althea_rpcs/denoising/althea_rpcs_denoised.ply\",\n",
    "        \"expected_piles_path\": \"../../../../../data/processed/althea_rpcs/buffer_zones/kml_center_coordinates.csv\",\n",
    "        \"description\": \"Althea RPCS site with KML buffer zone centers\"\n",
    "    }\n",
    "]\n",
    "\n",
    "# Base parameters for all runs\n",
    "BASE_PARAMETERS = {\n",
    "    \"MODEL_PATH\": \"best_pointnet_plus_plus.pth\",\n",
    "    \"CONFIDENCE_THRESHOLD\": 0.5,\n",
    "    \"BATCH_SIZE\": 16,\n",
    "    \"PATCH_SIZE\": 8.0,\n",
    "    \"NUM_POINTS\": 1024,\n",
    "    \"OUTPUT_DIR\": \"output_runs/pile_verification\"\n",
    "}\n",
    "\n",
    "print(f\"Configured {len(SITE_CONFIGS)} sites for pile verification:\")\n",
    "for i, config in enumerate(SITE_CONFIGS, 1):\n",
    "    print(f\"  {i}. {config['site_name']}: {config['description']}\")"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "id": "validation",
   "metadata": {},
   "outputs": [],
   "source": [
    "def validate_files():\n",
    "    \"\"\"Validate that all required files exist\"\"\"\n",
    "    issues = []\n",
    "    \n",
    "    # Check model file\n",
    "    model_path = BASE_PARAMETERS[\"MODEL_PATH\"]\n",
    "    if not Path(model_path).exists():\n",
    "        issues.append(f\"Model file not found: {model_path}\")\n",
    "    \n",
    "    # Check point cloud and expected pile files\n",
    "    for config in SITE_CONFIGS:\n",
    "        pc_path = config[\"point_cloud_path\"]\n",
    "        if not Path(pc_path).exists():\n",
    "            issues.append(f\"Point cloud not found for {config['site_name']}: {pc_path}\")\n",
    "        \n",
    "        ep_path = config[\"expected_piles_path\"]\n",
    "        if not Path(ep_path).exists():\n",
    "            issues.append(f\"Expected piles file not found for {config['site_name']}: {ep_path}\")\n",
    "    \n",
    "    return issues\n",
    "\n",
    "# Run validation\n",
    "validation_issues = validate_files()\n",
    "if validation_issues:\n",
    "    print(\"Validation Issues Found:\")\n",
    "    for issue in validation_issues:\n",
    "        print(f\"  - {issue}\")\n",
    "else:\n",
    "    print(\"All files validated successfully\")"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "id": "execution",
   "metadata": {},
   "outputs": [],
   "source": [
    "# Select sites to run (modify as needed)\n",
    "INCLUDE_SITES = [\"trino_enel\"]  # Start with trino_enel for testing\n",
    "\n",
    "# Filter configurations\n",
    "filtered_configs = [cfg for cfg in SITE_CONFIGS if cfg[\"site_name\"] in INCLUDE_SITES]\n",
    "\n",
    "if validation_issues:\n",
    "    print(\"Skipping execution due to validation errors.\")\n",
    "    results = []\n",
    "else:\n",
    "    print(f\"\\nExecuting pile verification for {len(filtered_configs)} sites...\")\n",
    "    \n",
    "    results = []\n",
    "    timestamp = datetime.now().strftime(\"%Y%m%d_%H%M%S\")\n",
    "    \n",
    "    for i, config in enumerate(filtered_configs, 1):\n",
    "        site_name = config[\"site_name\"]\n",
    "        print(f\"\\n[{i}/{len(filtered_configs)}] Processing {site_name}...\")\n",
    "        \n",
    "        # Prepare parameters for this site\n",
    "        site_params = {\n",
    "            \"SITE_NAME\": site_name,\n",
    "            \"POINT_CLOUD_PATH\": config[\"point_cloud_path\"],\n",
    "            \"EXPECTED_PILES_PATH\": config[\"expected_piles_path\"],\n",
    "            **BASE_PARAMETERS\n",
    "        }\n",
    "        \n",
    "        # Define output notebook path\n",
    "        output_notebook = f\"04_pointnet_plus_plus_pile_verification_{site_name}_executed.ipynb\"\n",
    "        \n",
    "        try:\n",
    "            # Execute notebook with Papermill\n",
    "            pm.execute_notebook(\n",
    "                input_path=\"04_pointnet_plus_plus_pile_verification.ipynb\",\n",
    "                output_path=output_notebook,\n",
    "                parameters=site_params,\n",
    "                progress_bar=False\n",
    "            )\n",
    "            \n",
    "            print(f\"  ✓ Successfully executed: {output_notebook}\")\n",
    "            results.append({\n",
    "                \"site_name\": site_name,\n",
    "                \"status\": \"success\",\n",
    "                \"output_notebook\": output_notebook,\n",
    "                \"timestamp\": timestamp\n",
    "            })\n",
    "            \n",
    "        except Exception as e:\n",
    "            print(f\"  ✗ Failed to execute {site_name}: {str(e)}\")\n",
    "            results.append({\n",
    "                \"site_name\": site_name,\n",
    "                \"status\": \"failed\",\n",
    "                \"error\": str(e),\n",
    "                \"timestamp\": timestamp\n",
    "            })\n",
    "    \n",
    "    print(f\"\\nExecution completed. {len([r for r in results if r['status'] == 'success'])} successful, {len([r for r in results if r['status'] == 'failed'])} failed.\")"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "id": "summary",
   "metadata": {},
   "outputs": [],
   "source": [
    "# Create summary report\n",
    "if results:\n",
    "    results_df = pd.DataFrame(results)\n",
    "    \n",
    "    print(\"\\nExecution Summary:\")\n",
    "    print(results_df.to_string(index=False))\n",
    "    \n",
    "    # Save summary\n",
    "    summary_file = f\"multi_site_pile_verification_results_{timestamp}.csv\"\n",
    "    results_df.to_csv(summary_file, index=False)\n",
    "    print(f\"\\nSummary saved to: {summary_file}\")\n",
    "    \n",
    "    # List generated notebooks\n",
    "    successful_notebooks = [r[\"output_notebook\"] for r in results if r[\"status\"] == \"success\"]\n",
    "    if successful_notebooks:\n",
    "        print(\"\\nGenerated notebooks:\")\n",
    "        for notebook in successful_notebooks:\n",
    "            print(f\"  - {notebook}\")\n",
    "else:\n",
    "    print(\"No results to summarize.\")"
   ]
  }
 ],
 "metadata": {
  "kernelspec": {
   "display_name": "Python 3",\n",
   "language": "python",\n",
   "name": "python3"\n",
  },\n",
  "language_info": {\n",
   "codemirror_mode": {\n",
    "    \"name\": \"ipython\",\n",
    "    \"version\": 3\n",
   },\n",
   "file_extension\": \".py\",\n",
   "mimetype\": \"text/x-python\",\n",
   "name\": \"python\",\n",
   "nbconvert_exporter\": \"python\",\n",
   "pygments_lexer\": \"ipython3\",\n",
   "version\": \"3.8.5\"\n",
  }\n",
 },\n",
 \"nbformat\": 4,\n",
 \"nbformat_minor\": 5\n",
}
