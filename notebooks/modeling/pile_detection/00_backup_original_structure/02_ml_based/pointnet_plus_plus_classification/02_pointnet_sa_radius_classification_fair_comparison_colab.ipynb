{"cells": [{"cell_type": "markdown", "metadata": {"id": "title"}, "source": ["# SimplePointNet Fair Comparison with Classical ML (RES→RCPS)\n", "\n", "This notebook implements SimplePointNet using **EXACTLY the same data pipeline** as Classical ML:\n", "- **Same point clouds**: `Block_11_2m.las` and `Point_Cloud.las`\n", "- **Same ground truth**: `Buffer_2m.kml` files (not CSV results)\n", "- **Same validation protocol**: Train on RES → Test on RCPS\n", "- **Same patch parameters**: 3m radius (like Classical ML)\n", "\n", "**Key Difference from Classical ML:**\n", "- ✅ **Classical ML**: Extracts 22 statistical features from 3D coordinates\n", "- ✅ **SimplePointNet**: Uses raw 3D coordinates directly\n", "- ✅ **Fair comparison**: Both use identical data sources and validation\n", "\n", "**Research Question**: Can deep learning match classical ML performance using raw coordinates vs engineered features?\n", "\n", "**Author**: <PERSON><PERSON><PERSON>  \n", "**Date**: August 2025  \n", "**Project**: Fair Deep Learning vs Classical ML Comparison\n"]}, {"cell_type": "markdown", "metadata": {"id": "setup"}, "source": ["## Setup and Mount Google Drive"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "mount_drive"}, "outputs": [], "source": ["from google.colab import drive\n", "drive.mount('/content/drive')\n", "\n", "# Project paths\n", "GDRIVE_BASE = \"/content/drive/MyDrive\"\n", "PROJECT_FOLDER = \"pointnet_pile_detection\"\n", "project_path = f\"{GDRIVE_BASE}/{PROJECT_FOLDER}\"\n", "data_path = f\"{project_path}/data\"\n", "models_path = f\"{project_path}/models\"\n", "\n", "print(f\"Project path: {project_path}\")\n", "print(f\"Data path: {data_path}\")\n", "print(f\"Models path: {models_path}\")"]}, {"cell_type": "markdown", "metadata": {"id": "imports"}, "source": ["## Install Dependencies and Imports"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "install_deps"}, "outputs": [], "source": ["# Install required packages\n", "!pip install laspy geopandas scikit-learn mlflow\n", "\n", "import numpy as np\n", "import pandas as pd\n", "import torch\n", "import torch.nn as nn\n", "import torch.optim as optim\n", "import torch.nn.functional as F\n", "from torch.utils.data import Dataset, DataLoader, random_split\n", "import laspy\n", "import geopandas as gpd\n", "from scipy.spatial import cKDTree\n", "from sklearn.metrics import accuracy_score, f1_score, classification_report, confusion_matrix\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "from pathlib import Path\n", "import pickle\n", "import json\n", "import time\n", "from datetime import datetime\n", "import warnings\n", "warnings.filterwarnings('ignore')\n", "\n", "# Set random seeds\n", "np.random.seed(42)\n", "torch.manual_seed(42)\n", "if torch.cuda.is_available():\n", "    torch.cuda.manual_seed(42)\n", "\n", "device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')\n", "print(f\"Using device: {device}\")"]}, {"cell_type": "markdown", "metadata": {"id": "data_loading"}, "source": ["## Data Loading (EXACT SAME as Classical ML)"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "load_data_functions"}, "outputs": [], "source": ["def load_and_reproject_kml(kml_path):\n", "    \"\"\"Load KML and reproject to UTM Zone 14N/15N (SAME as Classical ML)\"\"\"\n", "    gdf = gpd.read_file(kml_path)\n", "    \n", "    # Extract coordinates from polygon centroids\n", "    pile_coords = []\n", "    for geom in gdf.geometry:\n", "        if geom.geom_type == 'Point':\n", "            pile_coords.append([geom.x, geom.y])\n", "        elif geom.geom_type == 'Polygon':\n", "            centroid = geom.centroid\n", "            pile_coords.append([centroid.x, centroid.y])\n", "    \n", "    pile_locations = np.array(pile_coords)\n", "    \n", "    # Create GeoDataFrame and reproject\n", "    gdf_geo = gpd.GeoDataFrame(\n", "        geometry=gpd.points_from_xy(pile_locations[:, 0], pile_locations[:, 1]),\n", "        crs='EPSG:4326'  # WGS84 geographic\n", "    )\n", "    \n", "    # Reproject to UTM (Zone 14N for RES, Zone 15N for RCPS)\n", "    if 'nortan' in kml_path:\n", "        gdf_utm = gdf_geo.to_crs('EPSG:32614')  # UTM Zone 14N\n", "    else:\n", "        gdf_utm = gdf_geo.to_crs('EPSG:32615')  # UTM Zone 15N\n", "    \n", "    pile_locations_utm = np.array([[geom.x, geom.y] for geom in gdf_utm.geometry])\n", "    \n", "    return pile_locations_utm\n", "\n", "def subsample_patch(patch_points, target_size=1024):\n", "    \"\"\"Subsample patch to target size (Classical ML uses 64, we use 1024 for deep learning)\"\"\"\n", "    if len(patch_points) <= target_size:\n", "        # Upsample with noise if needed\n", "        if len(patch_points) < target_size:\n", "            extra_needed = target_size - len(patch_points)\n", "            extra_indices = np.random.choice(len(patch_points), extra_needed, replace=True)\n", "            extra_points = patch_points[extra_indices] + np.random.normal(0, 0.01, (extra_needed, 3))\n", "            return np.vstack([patch_points, extra_points])\n", "        return patch_points\n", "    \n", "    np.random.seed(42)  # For reproducibility\n", "    indices = np.random.choice(len(patch_points), target_size, replace=False)\n", "    return patch_points[indices]\n", "\n", "def extract_patches(points, locations, radius=3.0, min_points=20, target_size=1024):\n", "    \"\"\"Extract patches around locations (SAME as Classical ML but more points)\"\"\"\n", "    print(f\"Extracting patches (radius={radius}m, min_points={min_points}, target_size={target_size})\")\n", "    \n", "    tree = cKDTree(points[:, :2])\n", "    patches = []\n", "    valid_locs = []\n", "    original_sizes = []\n", "    \n", "    for i, loc in enumerate(locations):\n", "        indices = tree.query_ball_point(loc[:2], radius)\n", "        \n", "        if len(indices) >= min_points:\n", "            patch_points = points[indices]\n", "            original_sizes.append(len(patch_points))\n", "            \n", "            # Subsample to target size\n", "            patch_points = subsample_patch(patch_points, target_size)\n", "            \n", "            # Center patch (SAME as Classical ML)\n", "            center = np.array([loc[0], loc[1], np.mean(patch_points[:, 2])])\n", "            centered_patch = patch_points - center\n", "            \n", "            patches.append(centered_patch)\n", "            valid_locs.append(loc)\n", "    \n", "    print(f\"Extracted {len(patches)} valid patches\")\n", "    if original_sizes:\n", "        print(f\"Original sizes: min={min(original_sizes)}, max={max(original_sizes)}, mean={np.mean(original_sizes):.1f}\")\n", "        final_sizes = [len(p) for p in patches]\n", "        print(f\"Final sizes: min={min(final_sizes)}, max={max(final_sizes)}, mean={np.mean(final_sizes):.1f}\")\n", "    \n", "    return patches, np.array(valid_locs)\n", "\n", "def create_negative_samples(points, pile_locations, n_negative, radius=3.0, min_points=20, target_size=1024):\n", "    \"\"\"Create negative samples (SAME as Classical ML)\"\"\"\n", "    print(f\"Creating {n_negative} negative samples...\")\n", "    \n", "    np.random.seed(42)\n", "    x_min, x_max = points[:, 0].min(), points[:, 0].max()\n", "    y_min, y_max = points[:, 1].min(), points[:, 1].max()\n", "    \n", "    # Generate random locations (avoiding pile areas)\n", "    random_x = np.random.uniform(x_min + 50, x_max - 50, n_negative * 3)  # Generate extra\n", "    random_y = np.random.uniform(y_min + 50, y_max - 50, n_negative * 3)\n", "    random_locations = np.column_stack([random_x, random_y])\n", "    \n", "    # Filter out locations too close to piles\n", "    pile_tree = cKDTree(pile_locations)\n", "    valid_negatives = []\n", "    \n", "    for loc in random_locations:\n", "        distances, _ = pile_tree.query(loc, k=1)\n", "        if distances > radius * 2:  # At least 2x radius away from any pile\n", "            valid_negatives.append(loc)\n", "            if len(valid_negatives) >= n_negative:\n", "                break\n", "    \n", "    valid_negatives = np.array(valid_negatives[:n_negative])\n", "    \n", "    # Extract patches\n", "    neg_patches, _ = extract_patches(points, valid_negatives, radius, min_points, target_size)\n", "    \n", "    return neg_patches"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "load_data_main"}, "outputs": [], "source": ["# STEP 1: LOAD RES DATA (TRAINING SITE) - EXACT SAME as Classical ML\n", "print(\"=== LOADING RES DATA (TRAINING SITE) - SAME AS CLASSICAL ML ===\")\n", "\n", "# Load RES point cloud\n", "print(f\"Loading RES point cloud: {data_path}/nortan_res/Block_11_2m.las\")\n", "res_las = laspy.read(f\"{data_path}/nortan_res/Block_11_2m.las\")\n", "res_points = np.vstack([res_las.x, res_las.y, res_las.z]).T\n", "print(f\"Loaded {len(res_points):,} points\")\n", "\n", "# Load RES pile locations (SAME file as Classical ML)\n", "print(f\"Loading RES pile locations: {data_path}/nortan_res/Buffer_2m.kml\")\n", "res_pile_locations = load_and_reproject_kml(f\"{data_path}/nortan_res/Buffer_2m.kml\")\n", "print(f\"Loaded {len(res_pile_locations)} pile locations\")\n", "\n", "# Extract positive patches (SAME as Classical ML: 3m radius)\n", "print(f\"Extracting positive patches from RES...\")\n", "res_pos_patches, _ = extract_patches(res_points, res_pile_locations, radius=3.0, min_points=20, target_size=1024)\n", "print(f\"Extracted {len(res_pos_patches)} positive patches\")\n", "\n", "# Create negative samples (SAME as Classical ML)\n", "res_neg_patches = create_negative_samples(res_points, res_pile_locations, len(res_pos_patches), radius=3.0, min_points=20, target_size=1024)\n", "print(f\"Created {len(res_neg_patches)} negative patches\")\n", "\n", "print(f\"\\nRES training dataset: {len(res_pos_patches) + len(res_neg_patches)} samples ({len(res_pos_patches)} positive, {len(res_neg_patches)} negative)\")\n", "\n", "# STEP 2: LOAD RCPS DATA (TEST SITE) - EXACT SAME as Classical ML\n", "print(\"\\n=== LOADING RCPS DATA (TEST SITE) - SAME AS CLASSICAL ML ===\")\n", "\n", "# Load RCPS point cloud\n", "print(f\"Loading RCPS point cloud: {data_path}/althea_rpcs/Point_Cloud.las\")\n", "rcps_las = laspy.read(f\"{data_path}/althea_rpcs/Point_Cloud.las\")\n", "rcps_points = np.vstack([rcps_las.x, rcps_las.y, rcps_las.z]).T\n", "print(f\"Loaded {len(rcps_points):,} points\")\n", "\n", "# Load RCPS pile locations (SAME file as Classical ML)\n", "print(f\"Loading RCPS pile locations: {data_path}/althea_rpcs/Buffer_2m.kml\")\n", "rcps_pile_locations = load_and_reproject_kml(f\"{data_path}/althea_rpcs/Buffer_2m.kml\")\n", "print(f\"Loaded {len(rcps_pile_locations)} pile locations\")\n", "\n", "# Extract positive patches (SAME as Classical ML: 3m radius)\n", "print(f\"Extracting positive patches from RCPS...\")\n", "rcps_pos_patches, _ = extract_patches(rcps_points, rcps_pile_locations, radius=3.0, min_points=20, target_size=1024)\n", "print(f\"Extracted {len(rcps_pos_patches)} positive patches\")\n", "\n", "# Create negative samples (SAME as Classical ML)\n", "rcps_neg_patches = create_negative_samples(rcps_points, rcps_pile_locations, len(rcps_pos_patches), radius=3.0, min_points=20, target_size=1024)\n", "print(f\"Created {len(rcps_neg_patches)} negative patches\")\n", "\n", "print(f\"\\nRCPS test dataset: {len(rcps_pos_patches) + len(rcps_neg_patches)} samples ({len(rcps_pos_patches)} positive, {len(rcps_neg_patches)} negative)\")\n", "\n", "print(\"\\n=== DATA SUMMARY (FAIR COMPARISON) ===\")\n", "print(f\"✅ SAME point clouds as Classical ML\")\n", "print(f\"✅ SAME ground truth (Buffer_2m.kml) as Classical ML\")\n", "print(f\"✅ SAME patch radius (3m) as Classical ML\")\n", "print(f\"✅ SAME validation protocol (RES→RCPS) as Classical ML\")\n", "print(f\"📊 Classical ML: 22 statistical features from 64 points\")\n", "print(f\"📊 SimplePointNet: Raw 3D coordinates from 1024 points\")\n", "print(f\"🎯 Research Question: Raw coordinates vs engineered features\")"]}, {"cell_type": "markdown", "metadata": {"id": "prepare_datasets"}, "source": ["## Prepare Datasets for SimplePointNet"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "create_datasets"}, "outputs": [], "source": ["# Prepare training data (RES)\n", "train_patches = []\n", "train_labels = []\n", "\n", "# Add positive patches\n", "for patch in res_pos_patches:\n", "    train_patches.append(patch.astype(np.float32))\n", "    train_labels.append(1)\n", "\n", "# Add negative patches\n", "for patch in res_neg_patches:\n", "    train_patches.append(patch.astype(np.float32))\n", "    train_labels.append(0)\n", "\n", "# Prepare test data (RCPS)\n", "test_patches = []\n", "test_labels = []\n", "\n", "# Add positive patches\n", "for patch in rcps_pos_patches:\n", "    test_patches.append(patch.astype(np.float32))\n", "    test_labels.append(1)\n", "\n", "# Add negative patches\n", "for patch in rcps_neg_patches:\n", "    test_patches.append(patch.astype(np.float32))\n", "    test_labels.append(0)\n", "\n", "# Convert to numpy arrays\n", "train_patches = np.array(train_patches, dtype=np.float32)  # (N, 1024, 3)\n", "train_labels = np.array(train_labels, dtype=np.int64)      # (N,)\n", "test_patches = np.array(test_patches, dtype=np.float32)    # (M, 1024, 3)\n", "test_labels = np.array(test_labels, dtype=np.int64)        # (M,)\n", "\n", "print(f\"\\nFinal dataset shapes:\")\n", "print(f\"Training: {train_patches.shape}, labels: {train_labels.shape}\")\n", "print(f\"Testing: {test_patches.shape}, labels: {test_labels.shape}\")\n", "print(f\"Training class distribution: {np.bincount(train_labels)}\")\n", "print(f\"Testing class distribution: {np.bincount(test_labels)}\")\n", "\n", "# Verify data integrity\n", "print(f\"\\nData integrity checks:\")\n", "print(f\"Training patches - min: {train_patches.min():.3f}, max: {train_patches.max():.3f}\")\n", "print(f\"Test patches - min: {test_patches.min():.3f}, max: {test_patches.max():.3f}\")\n", "print(f\"No NaN values: {not np.isnan(train_patches).any() and not np.isnan(test_patches).any()}\")"]}, {"cell_type": "markdown", "metadata": {"id": "pointnet_architecture"}, "source": ["## SimplePointNet Architecture"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "pointnet_model"}, "outputs": [], "source": ["class SimplePointNet(nn.Module):\n", "    def __init__(self, num_classes=2):\n", "        super(SimplePointNet, self).__init__()\n", "        \n", "        # Point-wise MLPs\n", "        self.conv1 = nn.Conv1d(3, 64, 1)\n", "        self.conv2 = nn.Conv1d(64, 128, 1)\n", "        self.conv3 = nn.Conv1d(128, 256, 1)\n", "        \n", "        self.bn1 = nn.BatchNorm1d(64)\n", "        self.bn2 = nn.BatchNorm1d(128)\n", "        self.bn3 = nn.BatchNorm1d(256)\n", "        \n", "        # Classification head\n", "        self.fc1 = nn.Linear(256, 128)\n", "        self.fc2 = nn.<PERSON>ar(128, 64)\n", "        self.fc3 = nn.Linear(64, num_classes)\n", "        \n", "        self.dropout = nn.Dropout(0.3)\n", "\n", "    def forward(self, x):\n", "        # x shape: (batch_size, 3, num_points)\n", "        x = F.relu(self.bn1(self.conv1(x)))\n", "        x = F.relu(self.bn2(self.conv2(x)))\n", "        x = <PERSON>.relu(self.bn3(self.conv3(x)))\n", "        \n", "        # Global max pooling\n", "        x = torch.max(x, 2)[0]  # (batch_size, 256)\n", "        \n", "        # Classification\n", "        x = F.relu(self.fc1(x))\n", "        x = self.dropout(x)\n", "        x = <PERSON>.relu(self.fc2(x))\n", "        x = self.dropout(x)\n", "        x = self.fc3(x)\n", "        \n", "        return x"]}, {"cell_type": "markdown", "metadata": {"id": "dataset_class"}, "source": ["## Dataset and DataLoader"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "create_dataset_class"}, "outputs": [], "source": ["class FairComparisonDataset(Dataset):\n", "    def __init__(self, patches, labels):\n", "        # SimplePointNet expects (batch_size, 3, num_points)\n", "        self.patches = torch.FloatTensor(patches).transpose(2, 1)  # (N, 3, 1024)\n", "        self.labels = torch.LongTensor(labels)\n", "    \n", "    def __len__(self):\n", "        return len(self.patches)\n", "    \n", "    def __getitem__(self, idx):\n", "        return self.patches[idx], self.labels[idx]\n", "\n", "# Create datasets\n", "train_dataset = FairComparisonDataset(train_patches, train_labels)\n", "test_dataset = FairComparisonDataset(test_patches, test_labels)\n", "\n", "# Create train/validation split from training data\n", "train_size = int(0.8 * len(train_dataset))\n", "val_size = len(train_dataset) - train_size\n", "train_subset, val_subset = random_split(train_dataset, [train_size, val_size])\n", "\n", "# Create data loaders\n", "batch_size = 4  # Small batch size for Colab\n", "train_loader = DataLoader(train_subset, batch_size=batch_size, shuffle=True)\n", "val_loader = DataLoader(val_subset, batch_size=batch_size, shuffle=False)\n", "test_loader = DataLoader(test_dataset, batch_size=batch_size, shuffle=False)\n", "\n", "print(f\"Dataset sizes:\")\n", "print(f\"  Training: {len(train_subset)}\")\n", "print(f\"  Validation: {len(val_subset)}\")\n", "print(f\"  Test (RCPS): {len(test_dataset)}\")\n", "print(f\"  Batch size: {batch_size}\")\n", "print(f\"  Input shape: (batch_size, 3, 1024) for SimplePointNet\")"]}, {"cell_type": "markdown", "metadata": {"id": "training_setup"}, "source": ["## Training Setup and Execution"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "setup_training"}, "outputs": [], "source": ["# Initialize model\n", "model = SimplePointNet(num_classes=2).to(device)\n", "print(f\"Model initialized with {sum(p.numel() for p in model.parameters()):,} parameters\")\n", "\n", "# Training configuration\n", "criterion = nn.CrossEntropyLoss()\n", "optimizer = optim.<PERSON>(model.parameters(), lr=0.001, weight_decay=1e-4)\n", "scheduler = optim.lr_scheduler.StepLR(optimizer, step_size=20, gamma=0.7)\n", "\n", "# Training parameters\n", "num_epochs = 50  # Reduced for Colab\n", "best_val_acc = 0.0\n", "train_losses = []\n", "val_losses = []\n", "train_accs = []\n", "val_accs = []\n", "\n", "print(f\"Training configuration:\")\n", "print(f\"  Epochs: {num_epochs}\")\n", "print(f\"  Learning rate: 0.001\")\n", "print(f\"  Weight decay: 1e-4\")\n", "print(f\"  Device: {device}\")\n", "print(f\"  Data source: SAME as Classical ML (Buffer_2m.kml)\")\n", "print(f\"  Validation: SAME as Classical ML (RES→RCPS)\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "training_loop"}, "outputs": [], "source": ["# Training loop\n", "print(\"\\n=== STARTING FAIR COMPARISON TRAINING ===\")\n", "print(\"SimplePointNet vs Classical ML using IDENTICAL data sources\")\n", "start_time = time.time()\n", "\n", "for epoch in range(num_epochs):\n", "    epoch_start = time.time()\n", "    \n", "    # Training phase\n", "    model.train()\n", "    train_loss = 0.0\n", "    train_correct = 0\n", "    train_total = 0\n", "    \n", "    for batch_idx, (data, target) in enumerate(train_loader):\n", "        data, target = data.to(device), target.to(device)\n", "        \n", "        optimizer.zero_grad()\n", "        output = model(data)\n", "        loss = criterion(output, target)\n", "        loss.backward()\n", "        optimizer.step()\n", "        \n", "        train_loss += loss.item()\n", "        pred = output.argmax(dim=1)\n", "        train_correct += pred.eq(target).sum().item()\n", "        train_total += target.size(0)\n", "        \n", "        if batch_idx % 10 == 0:\n", "            print(f'  Epoch {epoch+1}/{num_epochs}, Batch {batch_idx}/{len(train_loader)}, Loss: {loss.item():.4f}')\n", "    \n", "    # Validation phase\n", "    model.eval()\n", "    val_loss = 0.0\n", "    val_correct = 0\n", "    val_total = 0\n", "    \n", "    with torch.no_grad():\n", "        for data, target in val_loader:\n", "            data, target = data.to(device), target.to(device)\n", "            output = model(data)\n", "            loss = criterion(output, target)\n", "            \n", "            val_loss += loss.item()\n", "            pred = output.argmax(dim=1)\n", "            val_correct += pred.eq(target).sum().item()\n", "            val_total += target.size(0)\n", "    \n", "    # Calculate metrics\n", "    train_loss /= len(train_loader)\n", "    val_loss /= len(val_loader)\n", "    train_acc = 100. * train_correct / train_total\n", "    val_acc = 100. * val_correct / val_total\n", "    \n", "    # Store metrics\n", "    train_losses.append(train_loss)\n", "    val_losses.append(val_loss)\n", "    train_accs.append(train_acc)\n", "    val_accs.append(val_acc)\n", "    \n", "    # Update learning rate\n", "    scheduler.step()\n", "    \n", "    # Save best model\n", "    if val_acc > best_val_acc:\n", "        best_val_acc = val_acc\n", "        torch.save(model.state_dict(), f'{models_path}/simplepointnet_fair_comparison_best_model.pth')\n", "        print(f'  *** New best model saved! Validation accuracy: {val_acc:.2f}% ***')\n", "    \n", "    epoch_time = time.time() - epoch_start\n", "    print(f'Epoch {epoch+1}/{num_epochs}: Train Loss: {train_loss:.4f}, Train Acc: {train_acc:.2f}%, '\n", "          f'Val Loss: {val_loss:.4f}, Val Acc: {val_acc:.2f}%, Time: {epoch_time:.1f}s')\n", "    print('-' * 80)\n", "\n", "total_time = time.time() - start_time\n", "print(f\"\\nTraining completed in {total_time/60:.1f} minutes\")\n", "print(f\"Best validation accuracy: {best_val_acc:.2f}%\")"]}, {"cell_type": "markdown", "metadata": {"id": "evaluation"}, "source": ["## Fair Cross-Site Evaluation (RES→RCPS)"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "test_evaluation"}, "outputs": [], "source": ["# Load best model\n", "model.load_state_dict(torch.load(f'{models_path}/simplepointnet_fair_comparison_best_model.pth'))\n", "model.eval()\n", "\n", "print(\"=== FAIR CROSS-SITE EVALUATION (RES→RCPS) ===\")\n", "print(\"Testing SimplePointNet trained on RES data on RCPS data...\")\n", "print(\"SAME validation protocol as Classical ML\")\n", "\n", "# Test evaluation\n", "test_correct = 0\n", "test_total = 0\n", "all_predictions = []\n", "all_targets = []\n", "all_probabilities = []\n", "\n", "with torch.no_grad():\n", "    for data, target in test_loader:\n", "        data, target = data.to(device), target.to(device)\n", "        output = model(data)\n", "        \n", "        # Get predictions and probabilities\n", "        probabilities = torch.softmax(output, dim=1)\n", "        pred = output.argmax(dim=1)\n", "        \n", "        test_correct += pred.eq(target).sum().item()\n", "        test_total += target.size(0)\n", "        \n", "        all_predictions.extend(pred.cpu().numpy())\n", "        all_targets.extend(target.cpu().numpy())\n", "        all_probabilities.extend(probabilities.cpu().numpy())\n", "\n", "# Calculate metrics\n", "test_acc = 100. * test_correct / test_total\n", "test_f1 = f1_score(all_targets, all_predictions)\n", "\n", "print(f\"\\nSimplePointNet Cross-Site Test Results (RES→RCPS):\")\n", "print(f\"  Test Accuracy: {test_acc:.2f}%\")\n", "print(f\"  Test F1-Score: {test_f1:.4f}\")\n", "print(f\"  Total test samples: {test_total}\")\n", "print(f\"  Correct predictions: {test_correct}\")\n", "\n", "# Check class distribution in predictions\n", "unique_predictions = np.unique(all_predictions)\n", "unique_targets = np.unique(all_targets)\n", "\n", "print(f\"\\nPredicted classes: {unique_predictions}\")\n", "print(f\"True classes: {unique_targets}\")\n", "\n", "# Detailed classification report with proper handling\n", "if len(unique_predictions) == 1:\n", "    print(\"\\nNote: Model predicted only one class\")\n", "    print(f\"All predictions were: {'Pile' if unique_predictions[0] == 1 else 'Non-Pile'}\")\n", "    print(f\"Accuracy: {test_acc:.2f}%\")\n", "    \n", "    # Show distribution\n", "    print(f\"\\nActual distribution:\")\n", "    print(f\"  Piles: {np.sum(all_targets)}\")\n", "    print(f\"  Non-Piles: {len(all_targets) - np.sum(all_targets)}\")\n", "else:\n", "    print(\"\\nDetailed Classification Report:\")\n", "    print(classification_report(all_targets, all_predictions, target_names=['Non-<PERSON>le', '<PERSON>le']))\n", "\n", "# Confusion matrix\n", "cm = confusion_matrix(all_targets, all_predictions)\n", "print(\"\\nConfusion Matrix:\")\n", "print(cm)\n", "\n", "# Plot confusion matrix\n", "plt.figure(figsize=(8, 6))\n", "sns.heatmap(cm, annot=True, fmt='d', cmap='Blues', \n", "            xticklabels=['Non-<PERSON><PERSON>', '<PERSON>le'], \n", "            yticklabels=['Non-<PERSON>le', '<PERSON>le'])\n", "plt.title('SimplePointNet Fair Comparison Confusion Matrix (RES→RCPS)')\n", "plt.ylabel('True Label')\n", "plt.xlabel('Predicted Label')\n", "plt.show()\n", "\n", "# Save results\n", "timestamp = datetime.now().strftime(\"%Y%m%d_%H%M%S\")\n", "\n", "# Save training history\n", "training_history = {\n", "    'train_losses': train_losses,\n", "    'val_losses': val_losses,\n", "    'train_accs': train_accs,\n", "    'val_accs': val_accs,\n", "    'best_val_acc': best_val_acc,\n", "    'test_acc': test_acc,\n", "    'test_f1': test_f1,\n", "    'num_epochs': num_epochs,\n", "    'batch_size': batch_size,\n", "    'data_source': 'Buffer_2m.kml_same_as_classical_ml',\n", "    'validation_protocol': 'RES_to_RCPS_same_as_classical_ml',\n", "    'patch_radius': '3.0m_same_as_classical_ml'\n", "}\n", "\n", "with open(f'{models_path}/simplepointnet_fair_comparison_training_history_{timestamp}.json', 'w') as f:\n", "    json.dump(training_history, f, indent=2)\n", "\n", "# Save predictions\n", "results_df = pd.DataFrame({\n", "    'true_label': all_targets,\n", "    'predicted_label': all_predictions,\n", "    'pile_probability': [prob[1] for prob in all_probabilities],\n", "    'non_pile_probability': [prob[0] for prob in all_probabilities]\n", "})\n", "\n", "results_df.to_csv(f'{models_path}/simplepointnet_fair_comparison_rcps_predictions_{timestamp}.csv', index=False)\n", "\n", "print(f\"\\nResults saved:\")\n", "print(f\"  Training history: simplepointnet_fair_comparison_training_history_{timestamp}.json\")\n", "print(f\"  Predictions: simplepointnet_fair_comparison_rcps_predictions_{timestamp}.csv\")\n", "print(f\"  Model: simplepointnet_fair_comparison_best_model.pth\")\n", "\n", "print(\"\\n=== FAIR COMPARISON COMPLETE ===\")\n", "print(f\"SimplePointNet (Raw Coordinates): {test_acc:.2f}% accuracy\")\n", "print(f\"Classical ML (22 Features): TBD% accuracy (from their results)\")\n", "print(f\"\\n🎯 Research Question Answered:\")\n", "print(f\"   Can deep learning match classical ML using raw coordinates vs engineered features?\")\n", "print(f\"   Answer: SimplePointNet achieved {test_acc:.2f}% vs Classical ML's performance\")\n", "print(f\"\\n✅ FAIR COMPARISON ACHIEVED:\")\n", "print(f\"   ✅ Same point clouds (LAS files)\")\n", "print(f\"   ✅ Same ground truth (Buffer_2m.kml)\")\n", "print(f\"   ✅ Same validation protocol (RES→RCPS)\")\n", "print(f\"   ✅ Same patch extraction (3m radius)\")\n", "print(f\"   📊 Different approaches: Raw coordinates vs Statistical features\")"]}], "metadata": {"colab": {"provenance": [], "gpuType": "T4"}, "kernelspec": {"display_name": "Python 3", "name": "python3"}, "language_info": {"name": "python"}, "accelerator": "GPU"}, "nbformat": 4, "nbformat_minor": 0}