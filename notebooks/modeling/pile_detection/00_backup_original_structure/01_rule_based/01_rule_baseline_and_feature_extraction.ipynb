{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Feature Analysis for Machine Learning\n", "\n", "This notebook analyzes prepared patch data and extracts features for ML-based pile detection training.\n", "\n", "## What we will do:\n", "1. Load the prepared patch data\n", "2. Extract geometric features from each patch\n", "3. Analyze differences between pile and non-pile patches\n", "4. <PERSON><PERSON>p baseline rule-based classification\n", "5. Prepare features for ML model training\n", "6. Visualize feature distributions and insights\n", "\n", "**Author**: <PERSON><PERSON><PERSON>  \n", "**Date**: July 2025  \n", "**Project**: As-Built Foundation Analysis"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 1: Setup Environment"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Import required libraries\n", "import numpy as np\n", "import pandas as pd\n", "import matplotlib.pyplot as plt\n", "from sklearn.cluster import DBSCAN\n", "from sklearn.metrics import classification_report, confusion_matrix\n", "import json\n", "from pathlib import Path\n", "from datetime import datetime\n", "import warnings\n", "import pickle\n", "\n", "warnings.filterwarnings('ignore')\n", "\n", "print(\"Libraries imported successfully\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 2: Load Prepared Data"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from pathlib import Path\n", "import pickle, json\n", "\n", "print(\"Loading prepared patch data...\")\n", "\n", "try:\n", "    #TODO : fix path\n", "    base_dir = \"../../00_data_preprocessing/\"\n", "    ml_patch_data_dir= \"../../00_data_preprocessing/output/ml_patch_data\"\n", "    output_dir_file = Path(f\"{base_dir}/output_dir.txt\")\n", "    output_dir = Path(output_dir_file.read_text().strip()).resolve()\n", "    print(f\"Loading data from: {output_dir}\")\n", "\n", "    datasets = {\n", "        split: {\n", "            'patches': pickle.load(open(ml_patch_data_dir / f\"{split}_patches.pkl\", 'rb')),\n", "            'metadata': json.load(open(ml_patch_data_dir / f\"{split}_metadata.json\"))\n", "        }\n", "        for split in ['train', 'val', 'test']\n", "    }\n", "\n", "    for split, data in datasets.items():\n", "        print(f\"  {split.capitalize()}: {len(data['patches'])} patches\")\n", "\n", "    config = json.load(open(output_dir / \"config.json\"))\n", "    print(f\"Configuration: {config['parameters']}\")\n", "\n", "except FileNotFoundError as e:\n", "    print(f\"Error: {e}\\nPlease run the patch extraction notebook first.\")\n", "    raise"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(\"Reconstructing combined dataset for analysis...\")\n", "\n", "all_patches = []\n", "all_metadata = []\n", "\n", "for split_name, split_data in datasets.items():\n", "    patches = split_data['patches']\n", "    metadata = split_data['metadata']\n", "    \n", "    for i, meta in enumerate(metadata):\n", "        meta['split'] = split_name\n", "        all_metadata.append(meta)\n", "        all_patches.append(patches[i])\n", "\n", "positive_patches = []\n", "negative_patches = []\n", "positive_info = []\n", "negative_info = []\n", "\n", "for patch, meta in zip(all_patches, all_metadata):\n", "    if meta.get('label', 0) == 1 or meta.get('patch_type') == 'positive':\n", "        positive_patches.append(patch)\n", "        positive_info.append(meta)\n", "    else:\n", "        negative_patches.append(patch)\n", "        negative_info.append(meta)\n", "\n", "print(\"Data reconstruction complete:\")\n", "print(f\"  Positive patches: {len(positive_patches)}\")\n", "print(f\"  Negative patches: {len(negative_patches)}\")\n", "print(f\"  Train/Val/Test: {len(datasets['train']['patches'])}/\"\n", "      f\"{len(datasets['val']['patches'])}/\"\n", "      f\"{len(datasets['test']['patches'])}\")\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Extract pile coordinates if available\n", "pile_coordinates = []\n", "for meta in positive_info:\n", "    if 'center_location' in meta:\n", "        pile_coordinates.append(meta['center_location'])\n", "\n", "pile_coordinates = np.array(pile_coordinates)\n", "print(f\"  Extracted {len(pile_coordinates)} pile coordinate references\")\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 3: Define Feature Extraction Functions"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def extract_geometric_features(patch_points):\n", "    if len(patch_points) == 0:\n", "        return {}\n", "    \n", "    x, y, z = patch_points[:, 0], patch_points[:, 1], patch_points[:, 2]\n", "    r = np.linalg.norm(patch_points[:, :2], axis=1)\n", "    \n", "    x_extent = x.max() - x.min()\n", "    y_extent = y.max() - y.min()\n", "    footprint_area = x_extent * y_extent\n", "    height_range = z.max() - z.min()\n", "    \n", "    return {\n", "        'num_points': len(patch_points),\n", "        'height_range': height_range,\n", "        'footprint_area': footprint_area,\n", "        'point_density': len(patch_points) / footprint_area if footprint_area > 1e-6 else 0,\n", "        'max_radial_distance': r.max(),\n", "        'height_to_width_ratio': height_range / max(r.max(), 1e-6),\n", "        'aspect_ratio_xy': x_extent / max(y_extent, 1e-6),\n", "    }\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def extract_confidence_features(metadata):\n", "    \"\"\"Extract features from pile metadata (confidence, source, etc.)\"\"\"\n", "    features = {}\n", "    \n", "    # Confidence level encoding\n", "    confidence_map = {'high': 3, 'medium': 2, 'low': 1, 'unknown': 0}\n", "    features['confidence_score'] = confidence_map.get(metadata.get('confidence', 'unknown'), 0)\n", "    \n", "    # Source encoding\n", "    source_map = {'matched': 3, 'ifc_only': 2, 'kml_only': 1, 'unknown': 0}\n", "    features['source_score'] = source_map.get(metadata.get('source', 'unknown'), 0)\n", "    \n", "    # Extraction metadata\n", "    features['radius_used'] = metadata.get('radius_used', 5.0)\n", "    features['original_num_points'] = metadata.get('num_points', 0)\n", "    \n", "    return features\n", "\n", "print(\"Feature extraction function defined\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 4: Extract Features from All Patches"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Extract features from positive patches (pile locations)\n", "print(\"Extracting enhanced features from positive patches...\")\n", "\n", "positive_features = []\n", "for i, (patch, meta) in enumerate(zip(positive_patches, positive_info)):\n", "    # Geometric features\n", "    geo_features = extract_geometric_features(patch)\n", "    \n", "    # Metadata features\n", "    conf_features = extract_confidence_features(meta)\n", "    \n", "    # Combine all features\n", "    features = {**geo_features, **conf_features}\n", "    features['patch_type'] = 'positive'\n", "    features['patch_index'] = i\n", "    features['split'] = meta.get('split', 'unknown')\n", "    \n", "    positive_features.append(features)\n", "    \n", "    if (i + 1) % 100 == 0:\n", "        print(f\"  Processed {i + 1} positive patches...\")\n", "\n", "print(f\"Completed feature extraction for {len(positive_features)} positive patches\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Extract features from negative patches (non-pile locations)\n", "print(\"Extracting enhanced features from negative patches...\")\n", "\n", "negative_features = []\n", "for i, (patch, meta) in enumerate(zip(negative_patches, negative_info)):\n", "    # Geometric features\n", "    geo_features = extract_geometric_features(patch)\n", "    \n", "    # For negative patches, add dummy confidence features\n", "    conf_features = {\n", "        'confidence_score': 0,\n", "        'source_score': 0,\n", "        'radius_used': meta.get('radius_used', 5.0),\n", "        'original_num_points': meta.get('num_points', 0)\n", "    }\n", "    \n", "    # Combine all features\n", "    features = {**geo_features, **conf_features}\n", "    features['patch_type'] = 'negative'\n", "    features['patch_index'] = i\n", "    features['split'] = meta.get('split', 'unknown')\n", "    \n", "    negative_features.append(features)\n", "    \n", "    if (i + 1) % 100 == 0:\n", "        print(f\"  Processed {i + 1} negative patches...\")\n", "\n", "print(f\"Completed enhanced feature extraction for {len(negative_features)} negative patches\")\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(\"Creating enhanced feature DataFrame...\")\n", "\n", "# Combine and create DataFrame\n", "feature_df = pd.DataFrame(positive_features + negative_features)\n", "\n", "# Add derived columns\n", "feature_df['is_pile'] = feature_df['patch_type'] == 'positive'\n", "feature_df['confidence_category'] = feature_df['confidence_score'].map({3: 'high', 2: 'medium', 1: 'low', 0: 'none'})\n", "\n", "# Print summary\n", "print(f\"Feature DataFrame created: {len(feature_df)} rows, {feature_df.shape[1]} columns\")\n", "print(\"Feature count (excluding meta):\", len([c for c in feature_df.columns if c not in ['patch_type', 'patch_index', 'split', 'is_pile']]))\n", "\n", "print(\"\\nDataset distribution (by split and type):\")\n", "print(feature_df.pivot_table(index='patch_type', columns='split', aggfunc='size', fill_value=0))\n", "\n", "print(\"\\nSample features:\")\n", "cols_to_show = ['patch_type', 'confidence_category'] + feature_df.select_dtypes(include=[np.number]).columns[:10].tolist()\n", "print(feature_df[cols_to_show].head())"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 5: Analyze Feature Differences"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Compare features between positive and negative patches\n", "print(\"Analyzing feature differences between pile and non-pile patches...\")\n", "\n", "# Separate positive and negative features\n", "positive_df = feature_df[feature_df['patch_type'] == 'positive']\n", "negative_df = feature_df[feature_df['patch_type'] == 'negative']\n", "\n", "print(f\"\\nPositive patches: {len(positive_df)}\")\n", "print(f\"Negative patches: {len(negative_df)}\")\n", "\n", "# Select numeric columns for comparison\n", "numeric_columns = feature_df.select_dtypes(include=[np.number]).columns\n", "numeric_columns = [col for col in numeric_columns if col not in ['patch_index']]\n", "\n", "print(f\"\\nNumeric features for comparison: {len(numeric_columns)}\")\n", "print(numeric_columns)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Calculate summary statistics for each feature\n", "print(\"Comparing feature stats (mean ± std)...\")\n", "\n", "summary = []\n", "for f in numeric_columns:\n", "    pos, neg = positive_df[f].dropna(), negative_df[f].dropna()\n", "    if not pos.empty and not neg.empty:\n", "        stats = {\n", "            'feature': f,\n", "            'pos_mean': pos.mean(), 'neg_mean': neg.mean(),\n", "            'pos_std': pos.std(), 'neg_std': neg.std(),\n", "            'diff': pos.mean() - neg.mean()\n", "        }\n", "        summary.append(stats)\n", "        print(f\"{f:20s}: {stats['pos_mean']:.2f} ± {stats['pos_std']:.2f} | \"\n", "              f\"{stats['neg_mean']:.2f} ± {stats['neg_std']:.2f} | Δ={stats['diff']:.2f}\")\n", "\n", "comparison_df = pd.DataFrame(summary)\n", "print(f\"\\nFeature comparison completed for {len(comparison_df)} features\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 6: Develop Rule-Based Classification"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def classify_pile_with_rules(features, debug=False):\n", "    \"\"\"Rule-based classification TUNED for your data\"\"\"\n", "    score = 0\n", "    rules = {}\n", "    \n", "    # Based on your data analysis - REVERSED logic!\n", "    rules['lower_density'] = features.get('point_density', 0) < 0.7        # Piles are less dense\n", "    rules['compact_footprint'] = features.get('max_radial_distance', 0) > 4.7  # Piles are more spread\n", "    rules['moderate_height'] = 1.5 < features.get('height_range', 0) < 3.0     # Piles in height range\n", "    rules['high_confidence'] = features.get('confidence_score', 0) >= 2        # Use confidence\n", "    \n", "    # Scoring - count positive rules\n", "    score = sum([rules['lower_density'], rules['compact_footprint'], \n", "                rules['moderate_height'], rules['high_confidence']])\n", "    \n", "    is_pile = score >= 2  # Need at least 2 rules\n", "    \n", "    rules.update({\n", "        'total_score': score,\n", "        'classification': 'pile' if is_pile else 'non-pile'\n", "    })\n", "    \n", "    return is_pile, rules"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(\"Applying rule-based classification...\")\n", "\n", "def apply_rules(row):\n", "    is_pile, details = classify_pile_with_rules(row.to_dict())\n", "    return pd.Series({\n", "        'predicted_pile': is_pile,\n", "        'rule_details': details  # Optional: if you want to inspect rule breakdown\n", "    })\n", "\n", "result = feature_df.apply(apply_rules, axis=1)\n", "feature_df = pd.concat([feature_df, result], axis=1)\n", "\n", "print(f\"Classification completed for {len(feature_df)} patches\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 7: Evaluate Classification Performance"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from sklearn.metrics import confusion_matrix, classification_report\n", "\n", "print(\"Evaluating classification performance...\")\n", "\n", "# Ensure binary values\n", "true_labels = feature_df['true_pile'].astype(int)\n", "predicted_labels = feature_df['predicted_pile'].astype(int)\n", "\n", "# Confirm\n", "assert isinstance(true_labels, pd.Series)\n", "assert isinstance(predicted_labels, pd.Series)\n", "assert set(true_labels.unique()).issubset({0, 1})\n", "assert set(predicted_labels.unique()).issubset({0, 1})\n", "\n", "# Confusion matrix\n", "cm = confusion_matrix(true_labels, predicted_labels, labels=[0, 1])\n", "tn, fp, fn, tp = cm.ravel()\n", "\n", "# Metrics\n", "accuracy = (tp + tn) / (tp + tn + fp + fn)\n", "precision = tp / (tp + fp) if (tp + fp) > 0 else 0\n", "recall = tp / (tp + fn) if (tp + fn) > 0 else 0\n", "f1 = 2 * (precision * recall) / (precision + recall) if (precision + recall) > 0 else 0\n", "\n", "print(\"\\nClassification Results:\")\n", "print(\"=\" * 50)\n", "print(f\"Accuracy:         {accuracy:.3f}\")\n", "print(f\"Precision:        {precision:.3f}\")\n", "print(f\"Recall:           {recall:.3f}\")\n", "print(f\"F1-Score:         {f1:.3f}\")\n", "\n", "print(\"\\nConfusion Matrix:\")\n", "print(f\"True Negatives:   {tn}\")\n", "print(f\"False Positives:  {fp}\")\n", "print(f\"False Negatives:  {fn}\")\n", "print(f\"True Positives:   {tp}\")\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Analyze by confidence level\n", "print(\"\\nPerformance by confidence level:\")\n", "for conf in ['high', 'medium', 'low', 'none']:\n", "    subset = feature_df[feature_df['confidence_category'] == conf]\n", "    if len(subset) > 0:\n", "        acc = (subset['true_pile'] == subset['predicted_pile']).mean()\n", "        print(f\"  {conf.capitalize()}: {len(subset)} patches, Accuracy: {acc:.3f}\")\n", "\n", "# Analyze by data split\n", "print(\"\\nPerformance by data split:\")\n", "for split in ['train', 'val', 'test']:\n", "    subset = feature_df[feature_df['split'] == split]\n", "    if len(subset) > 0:\n", "        acc = (subset['true_pile'] == subset['predicted_pile']).mean()\n", "        print(f\"  {split.capitalize()}: {len(subset)} patches, Accuracy: {acc:.3f}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 8: Save Results"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(\"Saving enhanced analysis results...\")\n", "\n", "# Save main feature dataframe\n", "feature_df.to_csv(output_dir / \"feature_analysis_results.csv\", index=False)\n", "\n", "# Save per-split CSVs\n", "for split in ['train', 'val', 'test']:\n", "    split_df = feature_df[feature_df['split'] == split]\n", "    if not split_df.empty:\n", "        split_df.to_csv(output_dir / f\"feature_analysis_{split}.csv\", index=False)\n", "\n", "# Save comparison stats\n", "comparison_df.to_csv(output_dir / \"feature_comparison.csv\", index=False)\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Summary\n", "\n", "Analysis and validation is now complete! Here's what we accomplished:\n", "\n", "1. **Loaded prepared patch data** from the first notebook\n", "2. **Extracted geometric features** from all patches\n", "3. **Analyzed feature differences** between pile and non-pile patches\n", "4. **Developed rule-based classification** using 4 geometric criteria\n", "5. **Evaluated classification performance** with standard metrics\n", "6. **Saved all results** for future reference\n", "\n", "The rule-based approach provides interpretable results and can be used as a baseline for more advanced machine learning approaches. The analysis shows which geometric features are most discriminative for pile detection in point cloud data."]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 4}