{"cells": [{"cell_type": "markdown", "id": "4e86c2a3", "metadata": {"papermill": {"duration": 0.010743, "end_time": "2025-08-10T08:50:47.596861", "exception": false, "start_time": "2025-08-10T08:50:47.586118", "status": "completed"}, "tags": []}, "source": ["# DGCNN Site Inference Pipeline\n", "This notebook applies the trained PointNet++ model to a new construction site\n", "with different data sources (DWG files instead of KML regions).\n", "\n", "**Workflow:**\n", "1. Simple exploratory pipeline to test DGCNN on multiple sites.\n", "2. Tests our trained DGCNN model on 3 different construction sites.\n", "\n", "**Author**: <PERSON><PERSON><PERSON>  \n", "**Date**: July 2025  \n", "**Project**: As-Built Foundation Analysis - Site Transfer"]}, {"cell_type": "code", "execution_count": 1, "id": "parameters", "metadata": {"execution": {"iopub.execute_input": "2025-08-10T08:50:47.604973Z", "iopub.status.busy": "2025-08-10T08:50:47.604682Z", "iopub.status.idle": "2025-08-10T08:50:47.613178Z", "shell.execute_reply": "2025-08-10T08:50:47.612762Z"}, "papermill": {"duration": 0.013808, "end_time": "2025-08-10T08:50:47.614345", "exception": false, "start_time": "2025-08-10T08:50:47.600537", "status": "completed"}, "tags": ["parameters"]}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["DGCNN Multi-Site Inference Pipeline\n", "Site Name: northan_res\n"]}], "source": ["# Model and processing parameters\n", "SITE_NAME = \"northan_res\"  # Will be overridden by papermill\n", "POINT_CLOUD_PATH = \"../../../../../data/raw/nortan_res/pointcloud/Block_11_2m.las\"  # Will be overridden\n", "\n", "RUN_NAME = f\"inference_{SITE_NAME}\"\n", "OUTPUT_DIR = \"output_runs/dgcnn_inference\"\n", "\n", "MODEL_PATH = \"best_dgcnn_fixed.pth\"  # FIXED: Use harmonized model\n", "CONFIDENCE_THRESHOLD = 0.3  # FIXED: Lower threshold for better detection\n", "BATCH_SIZE = 16\n", "GRID_SPACING = 20.0  # FIXED: Larger spacing for efficiency\n", "PATCH_SIZE = 8.0  # FIXED: harmonized training uses 8m radius\n", "NUM_POINTS = 256\n", "K_NEIGHBORS = 20\n", "\n", "EXPERIMENT_NAME = \"dgcnn_inference\"\n", "\n", "# MLflow configuration\n", "\n", "print(\"DGCNN Multi-Site Inference Pipeline\")\n", "print(f\"Site Name: {SITE_NAME}\")"]}, {"cell_type": "code", "execution_count": 2, "id": "190e9927", "metadata": {"execution": {"iopub.execute_input": "2025-08-10T08:50:47.636402Z", "iopub.status.busy": "2025-08-10T08:50:47.636145Z", "iopub.status.idle": "2025-08-10T08:50:47.638264Z", "shell.execute_reply": "2025-08-10T08:50:47.638012Z"}, "papermill": {"duration": 0.005722, "end_time": "2025-08-10T08:50:47.639176", "exception": false, "start_time": "2025-08-10T08:50:47.633454", "status": "completed"}, "tags": ["injected-parameters"]}, "outputs": [], "source": ["# Parameters\n", "SITE_NAME = \"nortan_res\"\n", "POINT_CLOUD_PATH = \"../../../../../data/raw/nortan_res/pointcloud/Block_11_2m.las\"\n", "OUTPUT_DIR = \"output_runs/dgcnn_harmonized_inference/nortan_res\"\n", "RUN_NAME = \"inference_nortan_res\"\n"]}, {"cell_type": "code", "execution_count": 3, "id": "6b291543", "metadata": {"execution": {"iopub.execute_input": "2025-08-10T08:50:47.643615Z", "iopub.status.busy": "2025-08-10T08:50:47.643499Z", "iopub.status.idle": "2025-08-10T08:50:50.926252Z", "shell.execute_reply": "2025-08-10T08:50:50.925942Z"}, "papermill": {"duration": 3.285857, "end_time": "2025-08-10T08:50:50.927068", "exception": false, "start_time": "2025-08-10T08:50:47.641211", "status": "completed"}, "tags": []}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages/torchvision/io/image.py:14: UserWarning: Failed to load image Python extension: 'dlopen(/Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages/torchvision/image.so, 0x0006): Library not loaded: @rpath/libjpeg.9.dylib\n", "  Referenced from: <EB3FF92A-5EB1-3EE8-AF8B-5923C1265422> /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages/torchvision/image.so\n", "  Reason: tried: '/Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages/torchvision/../../../libjpeg.9.dylib' (no such file), '/Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages/torchvision/../../../libjpeg.9.dylib' (no such file), '/Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/lib-dynload/../../libjpeg.9.dylib' (no such file), '/Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/bin/../lib/libjpeg.9.dylib' (no such file)'If you don't plan on using image functionality from `torchvision.io`, you can ignore this warning. Otherwise, there might be something wrong with your environment. Did you have `libjpeg` or `libpng` installed before building `torchvision` from source?\n", "  warn(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Using device: cpu\n"]}], "source": ["import torch\n", "import torch.nn as nn\n", "import torch.nn.functional as F\n", "import numpy as np\n", "import pandas as pd\n", "from pathlib import Path\n", "import matplotlib.pyplot as plt\n", "import mlflow\n", "import mlflow.pytorch\n", "import os\n", "from datetime import datetime\n", "import warnings\n", "warnings.filterwarnings('ignore')\n", "\n", "device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')\n", "print(f\"Using device: {device}\")"]}, {"cell_type": "code", "execution_count": 4, "id": "setup_output_dir", "metadata": {"execution": {"iopub.execute_input": "2025-08-10T08:50:50.931706Z", "iopub.status.busy": "2025-08-10T08:50:50.930874Z", "iopub.status.idle": "2025-08-10T08:50:51.003732Z", "shell.execute_reply": "2025-08-10T08:50:51.003327Z"}, "papermill": {"duration": 0.075749, "end_time": "2025-08-10T08:50:51.004631", "exception": false, "start_time": "2025-08-10T08:50:50.928882", "status": "completed"}, "tags": []}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Output directory created: output_runs/dgcnn_harmonized_inference/nortan_res\n", "MLflow experiment initialized\n"]}], "source": ["# Setup output directory and initialize MLflow\n", "os.makedirs(OUTPUT_DIR, exist_ok=True)\n", "print(f\"Output directory created: {OUTPUT_DIR}\")\n", "\n", "if mlflow.active_run() is not None:\n", "    print(f\"Ending previous active run: {mlflow.active_run().info.run_id}\")\n", "    mlflow.end_run()\n", "\n", "mlflow.set_experiment(EXPERIMENT_NAME)\n", "mlflow.start_run(run_name=RUN_NAME)\n", "\n", "# Log parameters\n", "mlflow.log_param(\"site_name\", SITE_NAME)\n", "mlflow.log_param(\"model_type\", \"DGCNN\")\n", "mlflow.log_param(\"patch_size\", PATCH_SIZE)\n", "mlflow.log_param(\"num_points\", NUM_POINTS)\n", "mlflow.log_param(\"k_neighbors\", K_NEIGHBORS)\n", "mlflow.log_param(\"confidence_threshold\", CONFIDENCE_THRESHOLD)\n", "mlflow.log_param(\"batch_size\", BATCH_SIZE)\n", "mlflow.log_param(\"grid_spacing\", GRID_SPACING)\n", "mlflow.log_param(\"model_path\", MODEL_PATH)\n", "\n", "print(\"MLflow experiment initialized\")"]}, {"cell_type": "markdown", "id": "085ade32", "metadata": {"papermill": {"duration": 0.001764, "end_time": "2025-08-10T08:50:51.008268", "exception": false, "start_time": "2025-08-10T08:50:51.006504", "status": "completed"}, "tags": []}, "source": ["## DGCNN Architecture (same as training)"]}, {"cell_type": "code", "execution_count": 5, "id": "f547b445", "metadata": {"execution": {"iopub.execute_input": "2025-08-10T08:50:51.012976Z", "iopub.status.busy": "2025-08-10T08:50:51.012837Z", "iopub.status.idle": "2025-08-10T08:50:51.016838Z", "shell.execute_reply": "2025-08-10T08:50:51.016573Z"}, "papermill": {"duration": 0.007639, "end_time": "2025-08-10T08:50:51.017759", "exception": false, "start_time": "2025-08-10T08:50:51.010120", "status": "completed"}, "tags": []}, "outputs": [], "source": ["def knn(x, k):\n", "    \"\"\"Find k nearest neighbors\"\"\"\n", "    inner = -2 * torch.matmul(x.transpose(2, 1), x)\n", "    xx = torch.sum(x**2, dim=1, keepdim=True)\n", "    pairwise_distance = -xx - inner - xx.transpose(2, 1)\n", "    idx = pairwise_distance.topk(k=k, dim=-1)[1]\n", "    return idx\n", "\n", "def get_graph_feature(x, k=20, idx=None):\n", "    \"\"\"Create graph features from k-NN\"\"\"\n", "    batch_size = x.size(0)\n", "    num_points = x.size(2)\n", "    x = x.view(batch_size, -1, num_points)\n", "    \n", "    if idx is None:\n", "        idx = knn(x, k=k)\n", "    \n", "    device = x.device\n", "    idx_base = torch.arange(0, batch_size, device=device).view(-1, 1, 1) * num_points\n", "    idx = idx + idx_base\n", "    idx = idx.view(-1)\n", "    \n", "    _, num_dims, _ = x.size()\n", "    x = x.transpose(2, 1).contiguous()\n", "    feature = x.view(batch_size * num_points, -1)[idx, :]\n", "    feature = feature.view(batch_size, num_points, k, num_dims)\n", "    x = x.view(batch_size, num_points, 1, num_dims).repeat(1, 1, k, 1)\n", "    \n", "    feature = torch.cat((feature - x, x), dim=3).permute(0, 3, 1, 2).contiguous()\n", "    return feature"]}, {"cell_type": "code", "execution_count": 6, "id": "bf8dd055", "metadata": {"execution": {"iopub.execute_input": "2025-08-10T08:50:51.021983Z", "iopub.status.busy": "2025-08-10T08:50:51.021848Z", "iopub.status.idle": "2025-08-10T08:50:51.024491Z", "shell.execute_reply": "2025-08-10T08:50:51.024266Z"}, "papermill": {"duration": 0.005559, "end_time": "2025-08-10T08:50:51.025334", "exception": false, "start_time": "2025-08-10T08:50:51.019775", "status": "completed"}, "tags": []}, "outputs": [], "source": ["class EdgeConv(nn.Module):\n", "    def __init__(self, in_channels, out_channels, k=20):\n", "        super(<PERSON><PERSON><PERSON><PERSON>, self).__init__()\n", "        self.k = k\n", "        self.conv = nn.Sequential(\n", "            nn.Conv2d(in_channels * 2, out_channels, kernel_size=1, bias=False),\n", "            nn.BatchNorm2d(out_channels),\n", "            nn.LeakyReLU(negative_slope=0.2)\n", "        )\n", "    \n", "    def forward(self, x):\n", "        x = get_graph_feature(x, k=self.k)\n", "        x = self.conv(x)\n", "        x = x.max(dim=-1, keepdim=False)[0]\n", "        return x"]}, {"cell_type": "code", "execution_count": 7, "id": "b2b14a92", "metadata": {"execution": {"iopub.execute_input": "2025-08-10T08:50:51.029455Z", "iopub.status.busy": "2025-08-10T08:50:51.029346Z", "iopub.status.idle": "2025-08-10T08:50:51.033951Z", "shell.execute_reply": "2025-08-10T08:50:51.033724Z"}, "papermill": {"duration": 0.007518, "end_time": "2025-08-10T08:50:51.034674", "exception": false, "start_time": "2025-08-10T08:50:51.027156", "status": "completed"}, "tags": []}, "outputs": [], "source": ["class DGCNN(nn.Module):\n", "    \"\"\"Fixed DGCNN with 20 feature input - exact copy from training\"\"\"\n", "    def __init__(self, num_classes=2, in_channels=20, k=20, dropout=0.3):\n", "        super(DGC<PERSON><PERSON>, self).__init__()\n", "        self.k = k\n", "\n", "        # Split input: 3D coords + features\n", "        self.coord_conv1 = EdgeConv(3, 64, k)\n", "        self.coord_conv2 = EdgeConv(64, 64, k)\n", "        self.coord_conv3 = EdgeConv(64, 128, k)\n", "\n", "        # Feature processing\n", "        self.feature_conv = nn.Sequential(\n", "            nn.Conv1d(in_channels - 3, 64, 1),\n", "            nn.<PERSON><PERSON><PERSON>orm1d(64),\n", "            nn.LeakyReLU(negative_slope=0.2)\n", "        )\n", "\n", "        # Combined processing\n", "        self.conv4 = EdgeConv(128 + 64, 256, k)\n", "\n", "        # Global feature aggregation\n", "        self.conv5 = nn.Sequential(\n", "            nn.Conv1d(64 + 64 + 128 + 256, 1024, kernel_size=1, bias=False),\n", "            nn.<PERSON>ch<PERSON>orm1d(1024),\n", "            nn.LeakyReLU(negative_slope=0.2)\n", "        )\n", "\n", "        # Classification head - simplified\n", "        self.classifier = nn.Sequential(\n", "            nn.<PERSON>(1024, 512),\n", "            nn.BatchNorm1d(512),\n", "            nn.LeakyReLU(negative_slope=0.2),\n", "            nn.Dropout(dropout),\n", "            nn.<PERSON><PERSON>(512, 256),\n", "            nn.BatchNorm1d(256),\n", "            nn.LeakyReLU(negative_slope=0.2),\n", "            nn.Dropout(dropout),\n", "            nn.Linear(256, num_classes)\n", "        )\n", "\n", "    def forward(self, x):\n", "        batch_size = x.size(0)\n", "\n", "        # Split coordinates and features\n", "        coords = x[:, :, :3].transpose(2, 1)  # (B, 3, N)\n", "        features = x[:, :, 3:].transpose(2, 1)  # (B, 17, N)\n", "\n", "        # Process coordinates with EdgeConv\n", "        x1 = self.coord_conv1(coords)  # (B, 64, N)\n", "        x2 = self.coord_conv2(x1)      # (B, 64, N)\n", "        x3 = self.coord_conv3(x2)      # (B, 128, N)\n", "\n", "        # Process features\n", "        feat = self.feature_conv(features)  # (B, 64, N)\n", "\n", "        # Combine and process\n", "        combined = torch.cat([x3, feat], dim=1)  # (B, 192, N)\n", "        x4 = self.conv4(combined)  # (B, 256, N)\n", "\n", "        # Concatenate all features\n", "        x = torch.cat((x1, x2, x3, x4), dim=1)  # (B, 512, N)\n", "\n", "        # Global feature extraction\n", "        x = self.conv5(x)  # (B, 1024, N)\n", "        x = F.adaptive_max_pool1d(x, 1).view(batch_size, -1)  # (B, 1024)\n", "\n", "        # Classification\n", "        x = self.classifier(x)\n", "        return x"]}, {"cell_type": "markdown", "id": "6e12b384", "metadata": {"papermill": {"duration": 0.001614, "end_time": "2025-08-10T08:50:51.038153", "exception": false, "start_time": "2025-08-10T08:50:51.036539", "status": "completed"}, "tags": []}, "source": ["## Load Data and DGCNN Model\n"]}, {"cell_type": "code", "execution_count": 8, "id": "1035c7b5", "metadata": {"execution": {"iopub.execute_input": "2025-08-10T08:50:51.042234Z", "iopub.status.busy": "2025-08-10T08:50:51.042122Z", "iopub.status.idle": "2025-08-10T08:50:51.044887Z", "shell.execute_reply": "2025-08-10T08:50:51.044641Z"}, "papermill": {"duration": 0.005636, "end_time": "2025-08-10T08:50:51.045637", "exception": false, "start_time": "2025-08-10T08:50:51.040001", "status": "completed"}, "tags": []}, "outputs": [], "source": ["def load_point_cloud(file_path):\n", "    \"\"\"Load point cloud from LAS or PLY files\"\"\"\n", "    file_path = Path(file_path)\n", "    \n", "    if not file_path.exists():\n", "        print(f\"File not found: {file_path}\")\n", "        return None\n", "    \n", "    try:\n", "        if file_path.suffix.lower() == '.las':\n", "            import laspy\n", "            las_file = laspy.read(file_path)\n", "            points = np.vstack([las_file.x, las_file.y, las_file.z]).T\n", "            print(f\"Loaded {len(points):,} points from LAS\")\n", "            return points\n", "        elif file_path.suffix.lower() == '.ply':\n", "            import open3d as o3d\n", "            pcd = o3d.io.read_point_cloud(str(file_path))\n", "            points = np.asarray(pcd.points)\n", "            print(f\"Loaded {len(points):,} points from PLY\")\n", "            return points\n", "        else:\n", "            print(f\"Unsupported format: {file_path.suffix}\")\n", "            return None\n", "    except Exception as e:\n", "        print(f\"Error loading {file_path}: {e}\")\n", "        return None"]}, {"cell_type": "code", "execution_count": 9, "id": "0767778f", "metadata": {"execution": {"iopub.execute_input": "2025-08-10T08:50:51.049556Z", "iopub.status.busy": "2025-08-10T08:50:51.049464Z", "iopub.status.idle": "2025-08-10T08:50:52.209965Z", "shell.execute_reply": "2025-08-10T08:50:52.209541Z"}, "papermill": {"duration": 1.163634, "end_time": "2025-08-10T08:50:52.211105", "exception": false, "start_time": "2025-08-10T08:50:51.047471", "status": "completed"}, "tags": []}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Loaded 35,565,352 points from LAS\n", "Point cloud bounds:\n", "  X: 385723.95 to 385809.63\n", "  Y: 3529182.83 to 3529447.01\n", "  Z: 553.38 to 556.27\n"]}], "source": ["# Load point cloud for current site\n", "point_cloud = load_point_cloud(POINT_CLOUD_PATH)\n", "\n", "if point_cloud is None:\n", "    print(f\"Generating synthetic data for {SITE_NAME}\")\n", "    np.random.seed(hash(SITE_NAME) % 2**32)\n", "    point_cloud = np.random.randn(5000, 3) * 20\n", "\n", "print(f\"Point cloud bounds:\")\n", "print(f\"  X: {point_cloud[:, 0].min():.2f} to {point_cloud[:, 0].max():.2f}\")\n", "print(f\"  Y: {point_cloud[:, 1].min():.2f} to {point_cloud[:, 1].max():.2f}\")\n", "print(f\"  Z: {point_cloud[:, 2].min():.2f} to {point_cloud[:, 2].max():.2f}\")\n", "\n", "# Log point cloud metrics to MLflow\n", "mlflow.log_metric(\"point_cloud_size\", len(point_cloud))\n", "mlflow.log_metric(\"x_range\", point_cloud[:, 0].max() - point_cloud[:, 0].min())\n", "mlflow.log_metric(\"y_range\", point_cloud[:, 1].max() - point_cloud[:, 1].min())\n", "mlflow.log_metric(\"z_range\", point_cloud[:, 2].max() - point_cloud[:, 2].min())\n"]}, {"cell_type": "code", "execution_count": 10, "id": "61fee98e", "metadata": {"execution": {"iopub.execute_input": "2025-08-10T08:50:52.216321Z", "iopub.status.busy": "2025-08-10T08:50:52.216104Z", "iopub.status.idle": "2025-08-10T08:50:52.218842Z", "shell.execute_reply": "2025-08-10T08:50:52.218604Z"}, "papermill": {"duration": 0.006208, "end_time": "2025-08-10T08:50:52.219670", "exception": false, "start_time": "2025-08-10T08:50:52.213462", "status": "completed"}, "tags": []}, "outputs": [], "source": ["def create_analysis_grid(point_cloud, grid_spacing=5.0):\n", "    \"\"\"Create regular grid of analysis points\"\"\"\n", "    x_min, x_max = point_cloud[:, 0].min(), point_cloud[:, 0].max()\n", "    y_min, y_max = point_cloud[:, 1].min(), point_cloud[:, 1].max()\n", "    \n", "    padding = grid_spacing * 0.5\n", "    x_coords = np.arange(x_min - padding, x_max + padding, grid_spacing)\n", "    y_coords = np.arange(y_min - padding, y_max + padding, grid_spacing)\n", "    \n", "    return np.array([[x, y] for x in x_coords for y in y_coords])"]}, {"cell_type": "code", "execution_count": 11, "id": "6de0bd17", "metadata": {"execution": {"iopub.execute_input": "2025-08-10T08:50:52.223666Z", "iopub.status.busy": "2025-08-10T08:50:52.223565Z", "iopub.status.idle": "2025-08-10T08:50:52.250172Z", "shell.execute_reply": "2025-08-10T08:50:52.249911Z"}, "papermill": {"duration": 0.029561, "end_time": "2025-08-10T08:50:52.251067", "exception": false, "start_time": "2025-08-10T08:50:52.221506", "status": "completed"}, "tags": []}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Created 90 analysis locations\n"]}], "source": ["# Create analysis grid\n", "grid_points = create_analysis_grid(point_cloud, GRID_SPACING)\n", "\n", "# Limit grid size for testing\n", "MAX_GRID_POINTS = 1000\n", "if len(grid_points) > MAX_GRID_POINTS:\n", "    print(f\"Limiting to {MAX_GRID_POINTS} grid points for testing\")\n", "    grid_points = grid_points[:MAX_GRID_POINTS]\n", "\n", "print(f\"Created {len(grid_points)} analysis locations\")\n", "\n", "# Log grid metrics to MLflow\n", "mlflow.log_metric(\"analysis_grid_points\", len(grid_points))"]}, {"cell_type": "code", "execution_count": 12, "id": "3fb10d9f", "metadata": {"execution": {"iopub.execute_input": "2025-08-10T08:50:52.255606Z", "iopub.status.busy": "2025-08-10T08:50:52.255481Z", "iopub.status.idle": "2025-08-10T08:50:52.261118Z", "shell.execute_reply": "2025-08-10T08:50:52.260847Z"}, "papermill": {"duration": 0.008834, "end_time": "2025-08-10T08:50:52.261906", "exception": false, "start_time": "2025-08-10T08:50:52.253072", "status": "completed"}, "tags": []}, "outputs": [], "source": ["def extract_patch_around_point(point_cloud, center_xy, radius=3.0, num_points=256):\n", "    \"\"\"Extract patch with 20-channel features - exact same method as training\"\"\"\n", "    center_x, center_y = center_xy\n", "    \n", "    # Calculate distances to center (XY only)\n", "    distances = np.sqrt((point_cloud[:, 0] - center_x)**2 + \n", "                       (point_cloud[:, 1] - center_y)**2)\n", "    \n", "    # Select points within radius\n", "    mask = distances <= radius\n", "    patch_xyz = point_cloud[mask]\n", "    \n", "    if len(patch_xyz) < 10:\n", "        return None\n", "    \n", "    # Calculate patch statistics for feature engineering\n", "    patch_center = np.mean(patch_xyz, axis=0)\n", "    z_min, z_max = patch_xyz[:, 2].min(), patch_xyz[:, 2].max()\n", "    z_mean, z_std = patch_xyz[:, 2].mean(), patch_xyz[:, 2].std()\n", "    if z_std == 0:\n", "        z_std = 1e-6\n", "    \n", "    # Calculate distance to center for each point\n", "    dist_to_center = np.sqrt((patch_xyz[:, 0] - center_x)**2 + (patch_xyz[:, 1] - center_y)**2)\n", "    \n", "    # Generate 20-channel features for each point\n", "    features_list = []\n", "    for i, (px, py, pz) in enumerate(patch_xyz):\n", "        # Match the exact feature engineering from training data preparation\n", "        feature_vector = [\n", "            px,  # 0: raw X coordinate\n", "            py - center_y,  # 1: relative Y (small values like training)\n", "            pz,  # 2: raw Z coordinate  \n", "            (pz - z_mean) / z_std,  # 3: height_norm\n", "            dist_to_center[i],  # 4: distance_norm\n", "            len(patch_xyz) / 1000.0,  # 5: density\n", "            px - center_x,  # 6: relative_x\n", "            py - center_y,  # 7: relative_y  \n", "            pz - patch_center[2],  # 8: relative_z\n", "            pz - z_min,  # 9: height_above_min\n", "            z_max - pz,  # 10: depth_below_max\n", "            abs(pz - z_mean),  # 11: abs_height_deviation\n", "            np.sqrt(px**2 + py**2),  # 12: distance_from_origin\n", "            np.arctan2(py - center_y, px - center_x),  # 13: angle\n", "            (px - center_x) * (py - center_y),  # 14: interaction\n", "            px - patch_center[0],  # 15: patch_relative_x\n", "            py - patch_center[1],  # 16: patch_relative_y\n", "            pz / (dist_to_center[i] + 1e-6),  # 17: height_distance_ratio\n", "            np.sqrt((px - center_x)**2 + (py - center_y)**2 + (pz - z_mean)**2),  # 18: 3d_distance\n", "            dist_to_center[i] + np.random.normal(0, 0.01)  # 19: distance with slight variation\n", "        ]\n", "        features_list.append(feature_vector)\n", "    \n", "    patch_features = np.array(features_list, dtype=np.float32)\n", "    n = len(patch_features)\n", "    \n", "    # Sample to fixed size\n", "    if n >= num_points:\n", "        indices = np.random.choice(n, num_points, replace=False)\n", "        patch_fixed = patch_features[indices]\n", "    else:\n", "        # Pad with jittered copies\n", "        extra_indices = np.random.choice(n, num_points - n, replace=True)\n", "        extra = patch_features[extra_indices].copy()\n", "        # Add small noise to coordinates only (first 3 features)\n", "        extra[:, :3] += np.random.normal(0, 0.01, (len(extra), 3))\n", "        patch_fixed = np.vstack([patch_features, extra])\n", "    \n", "    # Normalize spatial coordinates only (first 3 features)\n", "    spatial_coords = patch_fixed[:, :3]\n", "    max_dist = np.max(np.linalg.norm(spatial_coords, axis=1))\n", "    if max_dist > 0:\n", "        patch_fixed[:, :3] /= max_dist\n", "    \n", "    return patch_fixed.astype(np.float32)"]}, {"cell_type": "code", "execution_count": 13, "id": "model_validation", "metadata": {"execution": {"iopub.execute_input": "2025-08-10T08:50:52.266014Z", "iopub.status.busy": "2025-08-10T08:50:52.265920Z", "iopub.status.idle": "2025-08-10T08:50:52.294455Z", "shell.execute_reply": "2025-08-10T08:50:52.294105Z"}, "papermill": {"duration": 0.031659, "end_time": "2025-08-10T08:50:52.295465", "exception": false, "start_time": "2025-08-10T08:50:52.263806", "status": "completed"}, "tags": []}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Loaded DGCNN model from best_dgcnn_fixed.pth\n", "Model has 1,310,082 parameters\n", "Testing model with dummy data...\n", "Dummy test - Output shape: torch.Size([1, 2])\n", "Dummy test - Probabilities: [0. 1.]\n", "Model output might be problematic: pile_prob=1.0000\n"]}], "source": ["# Load trained DGCNN model with validation\n", "try:\n", "    model = DGCNN(num_classes=2, in_channels=20, k=K_NEIGHBORS, dropout=0.3).to(device)\n", "    model.load_state_dict(torch.load(MODEL_PATH, map_location=device))\n", "    model.eval()\n", "    print(f\"Loaded DGCNN model from {MODEL_PATH}\")\n", "    \n", "    # Log model info\n", "    param_count = sum(p.numel() for p in model.parameters())\n", "    print(f\"Model has {param_count:,} parameters\")\n", "    \n", "    # Test model with dummy data to verify it works\n", "    print(\"Testing model with dummy data...\")\n", "    dummy_input = torch.randn(1, 256, 20).to(device)\n", "    with torch.no_grad():\n", "        dummy_output = model(dummy_input)\n", "        dummy_probs = torch.softmax(dummy_output, dim=1)\n", "        print(f\"Dummy test - Output shape: {dummy_output.shape}\")\n", "        print(f\"Dummy test - Probabilities: {dummy_probs[0].cpu().numpy()}\")\n", "        \n", "        # Check if probabilities are reasonable\n", "        pile_prob = dummy_probs[0, 1].item()\n", "        if pile_prob > 0.1 and pile_prob < 0.9:\n", "            print(\"Model producing reasonable probability ranges\")\n", "        else:\n", "            print(f\"Model output might be problematic: pile_prob={pile_prob:.4f}\")\n", "    \n", "    MODEL_LOADED = True\n", "    \n", "except Exception as e:\n", "    print(f\"Error loading model: {e}\")\n", "    print(\"Creating untrained model for testing...\")\n", "    model = DGCNN(num_classes=2, in_channels=20, k=K_NEIGHBORS, dropout=0.3).to(device)\n", "    model.eval()\n", "    MODEL_LOADED = False"]}, {"cell_type": "markdown", "id": "6b0a249e", "metadata": {"papermill": {"duration": 0.001837, "end_time": "2025-08-10T08:50:52.299452", "exception": false, "start_time": "2025-08-10T08:50:52.297615", "status": "completed"}, "tags": []}, "source": ["## Process site with DGCNN\n"]}, {"cell_type": "code", "execution_count": 14, "id": "a5f0d927", "metadata": {"execution": {"iopub.execute_input": "2025-08-10T08:50:52.303556Z", "iopub.status.busy": "2025-08-10T08:50:52.303457Z", "iopub.status.idle": "2025-08-10T08:50:52.306818Z", "shell.execute_reply": "2025-08-10T08:50:52.306560Z"}, "papermill": {"duration": 0.006311, "end_time": "2025-08-10T08:50:52.307610", "exception": false, "start_time": "2025-08-10T08:50:52.301299", "status": "completed"}, "tags": []}, "outputs": [], "source": ["def process_site_dgcnn(point_cloud, grid_points, model, device, batch_size=16):\n", "    \"\"\"Process site with DGCNN model\"\"\"\n", "    results = []\n", "    \n", "    for i in range(0, len(grid_points), batch_size):\n", "        batch_centers = grid_points[i:i+batch_size]\n", "        batch_patches = []\n", "        valid_indices = []\n", "        \n", "        # Extract valid patches\n", "        for j, center in enumerate(batch_centers):\n", "            patch = extract_patch_around_point(point_cloud, center, radius=PATCH_SIZE, num_points=NUM_POINTS)\n", "\n", "            if patch is not None:\n", "                batch_patches.append(patch)\n", "                valid_indices.append(i + j)\n", "        \n", "        if len(batch_patches) == 0:\n", "            continue\n", "        \n", "        # Run DGCNN inference\n", "        batch_tensor = torch.FloatTensor(np.array(batch_patches)).to(device)\n", "        \n", "        with torch.no_grad():\n", "            outputs = model(batch_tensor)\n", "            probabilities = torch.softmax(outputs, dim=1)\n", "            pile_probs = probabilities[:, 1].cpu().numpy()\n", "        \n", "        # Store results\n", "        for j, (idx, prob) in enumerate(zip(valid_indices, pile_probs)):\n", "            center = grid_points[idx]\n", "            results.append({\n", "                'x': center[0],\n", "                'y': center[1],\n", "                'pile_probability': prob,\n", "                'prediction': 'PILE' if prob > CONFIDENCE_THRESHOLD else 'NON-PILE'\n", "            })\n", "    \n", "    return results\n", "\n"]}, {"cell_type": "code", "execution_count": 15, "id": "8444b060", "metadata": {"execution": {"iopub.execute_input": "2025-08-10T08:50:52.311770Z", "iopub.status.busy": "2025-08-10T08:50:52.311670Z", "iopub.status.idle": "2025-08-10T08:52:41.318168Z", "shell.execute_reply": "2025-08-10T08:52:41.317890Z"}, "papermill": {"duration": 109.011368, "end_time": "2025-08-10T08:52:41.320903", "exception": false, "start_time": "2025-08-10T08:50:52.309535", "status": "completed"}, "tags": []}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Running DGCNN inference on 90 locations...\n"]}, {"name": "stdout", "output_type": "stream", "text": ["DGCNN Results for nortan_res:\n", "  Total locations analyzed: 69\n", "  Pile detections: 69 (100.0%)\n", "  Average confidence: 1.000\n", "  High confidence (>0.9): 69\n"]}], "source": ["# Process site with DGCNN\n", "print(f\"Running DGCNN inference on {len(grid_points)} locations...\")\n", "\n", "results = process_site_dgcnn(point_cloud, grid_points, model, device, BATCH_SIZE)\n", "\n", "if results:\n", "    results_df = pd.DataFrame(results)\n", "    pile_count = len(results_df[results_df['prediction'] == 'PILE'])\n", "    avg_confidence = results_df['pile_probability'].mean()\n", "    \n", "    print(f\"DGCNN Results for {SITE_NAME}:\")\n", "    print(f\"  Total locations analyzed: {len(results_df)}\")\n", "    print(f\"  Pile detections: {pile_count} ({pile_count/len(results_df)*100:.1f}%)\")\n", "    print(f\"  Average confidence: {avg_confidence:.3f}\")\n", "    print(f\"  High confidence (>0.9): {sum(results_df['pile_probability'] > 0.9)}\")\n", "    \n", "    # Log results to MLflow\n", "    mlflow.log_metric(\"total_locations\", len(results_df))\n", "    mlflow.log_metric(\"pile_detections\", pile_count)\n", "    mlflow.log_metric(\"detection_rate\", pile_count/len(results_df))\n", "    mlflow.log_metric(\"avg_confidence\", avg_confidence)\n", "    mlflow.log_metric(\"high_confidence_count\", sum(results_df['pile_probability'] > 0.9))\n", "else:\n", "    print(f\"No valid results for {SITE_NAME}\")\n", "    results_df = pd.DataFrame()\n", "    pile_count = 0\n", "    avg_confidence = 0.0\n"]}, {"cell_type": "markdown", "id": "c4f86d3f", "metadata": {"papermill": {"duration": 0.0018, "end_time": "2025-08-10T08:52:41.324773", "exception": false, "start_time": "2025-08-10T08:52:41.322973", "status": "completed"}, "tags": []}, "source": ["## Visualization\n"]}, {"cell_type": "code", "execution_count": 16, "id": "a9c7014e", "metadata": {"execution": {"iopub.execute_input": "2025-08-10T08:52:41.329058Z", "iopub.status.busy": "2025-08-10T08:52:41.328929Z", "iopub.status.idle": "2025-08-10T08:52:41.675206Z", "shell.execute_reply": "2025-08-10T08:52:41.674905Z"}, "papermill": {"duration": 0.349573, "end_time": "2025-08-10T08:52:41.676160", "exception": false, "start_time": "2025-08-10T08:52:41.326587", "status": "completed"}, "tags": []}, "outputs": [{"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAA7sAAAMWCAYAAAAwGJHGAAAAOnRFWHRTb2Z0d2FyZQBNYXRwbG90bGliIHZlcnNpb24zLjEwLjMsIGh0dHBzOi8vbWF0cGxvdGxpYi5vcmcvZiW1igAAAAlwSFlzAAAPYQAAD2EBqD+naQAAuMxJREFUeJzs3QecFOX5wPFn7rjj6L13sCAKFmyoQRQElRgLSWxRbBgNGgELwagg1ujfgoolFrARK2pEoyIKRgVRxFiIRBAB6b0fd9zN//O8+866e+wed8uy797u75vP5O5m93aGudeZed7nmff1fN/3BQAAAACADJLjegcAAAAAAEg2gl0AAAAAQMYh2AUAAAAAZByCXQAAAABAxiHYBQAAAABkHIJdAAAAAEDGIdgFAAAAAGQcgl0AAAAAQMYh2AUAAAAAZByCXQCoYn766SfxPE/Gjx8fXjdq1CizDhXTvn17ueCCC1zvBgAA2IMIdgE4pQGbBmnBUlBQIC1btpR+/frJAw88IJs2bYr7u19//bVceOGF0qFDB/N7tWvXloMOOkiuu+46+fHHH2P+ztSpU+WMM86Q5s2bS35+vjRt2lROOeUUmThx4k7BpC6vvvrqTp8RBJarV68Or9PASdd169ZNfN/f6Xf0tSuuuKJCQVjk8dD9+9WvfiWvvfaaVJW/y67MmTPHHEM9znvSp59+arazfv36PbqdTPHwww9HdaAAAFDVEewCSAujR4+WZ599Vh555BG58sorzbohQ4ZI165dTVBb1uOPPy6HHHKI/Otf/zLB64MPPih33323HH300fLMM89I586dpaSkJOp3Ro4cKccdd5x8++238sc//lEeffRRufbaa2Xz5s0yYMAAmTBhQsz9ihW8xvPNN99EBc6J0IBdj4Uu11xzjSxdutT8G3V/Vbt27WTbtm1y3nnnSbr9XSoa7N58880pCXZ1O7GC3blz55o2hF8Q7AIAMk011zsAAOqkk06SQw89NPzziBEj5IMPPpBf//rX8pvf/Eb++9//So0aNcJBzOWXX24C20mTJkmdOnWiPuuee+6R2267LWrdK6+8YgK33/72tyaozcvLC7+mAe+7774rxcXFOwWdX331lcmqarC5K7p/bdq0MdvR9ydaVtyqVSv5wx/+EP75/PPPl7322kvuu+8+ueyyy8KZ1nT7u1Ql1atXd70LaWPr1q1Ss2ZNSXdbtmyRWrVqud4NAEAVQmYXQNo6/vjj5cYbb5SFCxfKc889F16v2ToN+J5//vmdAl2lgeAtt9wiubm54XX6OQ0bNpSnnnoqKtANaHmuBnCRzjrrLNlnn30qnN3NycmRG264wWQ8k1l2rCXX++23nyxYsCDuM7vx6HHr3r27CUj136//psWLF++Rv4v6/vvvTYeCbkv/Dhoo//Of/wy/rvv8u9/9znyvWfagTFrLywOardfSbQ1s9O/bv39/+e6773baD93W73//e2nSpIn59+27777y17/+1bym5cvaiaG0zD3YTpBNjvXMrpa+677pvmvwd+SRR8pbb70V9R7dT/2cl156yXSotG7d2vw7e/fuLfPmzZM9UUr+ySefyLBhw8y/U4/J6aefLqtWrYqZmd1///1NIK8l54MHD94pq92rVy854IADZNasWdKzZ0/z77z++uvN8dBjPG3atPCx0veqtWvXmgoDzebrowJ169Y1nSD/+c9/9tixCf7tuj9/+tOfTDm/fl5l2sjy5cvNYw76e3pMWrRoIaeeeuoerygAAKQPgl0AaS0o1X3vvffCWSjNLOqNeOTNb3l++OEHExiddtppMYPjeDRY1uBVb+orGryec845svfee1e6/Lk8mnHWALVRo0aV+j0NODQrrPtz7733mvLjKVOmmCBnd59jLft3URpsaICo2d6//OUvJsOuwYge9+D46bb//Oc/m+81yArKtTWYV/q9Bi4aVP3tb38zQbWWPR9zzDFRQYp2KBxxxBGmLQwaNEjGjBljtvPmm2+a1zWzfvbZZ5vvNSMebEcDxlhWrFghRx11lMnwa3Clx66wsNBkr2P97e+8806zXoNAzXbPmDFDzj33XNkTtHxc26CW4WtFg/4byz7/rcG9Brca5Opx17L8xx57TPr27btTxcKaNWtMsKqVC/fff7/pdNCv+t+Tlv8HxyroONBOgNdff910Bmk70k4ELdc/9thjTYn9njw2+rfQv/9NN91k2lRl2ogeA90PDXi1I0DbnT5rvmjRooT2BQBQBfkA4NC4ceM0IvQ///zzuO+pV6+ef/DBB5vv//Of/5j3DxkyZKf3rVmzxl+1alV42b59u1n/xhtvmN+57777KrRPCxYsMO+/++67/R07dvh77723f+CBB/qlpaXm9ZEjR5rXdRuBgQMH+rVq1TLfP/300+b1iRMnhl/XnwcPHrzLbbdr187v27dv+N+g/96zzjrL/P6VV14ZtX967ALBPgV++uknPzc317/tttuiPv+bb77xq1WrttP63f27qN69e/tdu3b1CwsLw+v0mB111FHmGAZefvll89kffvhh1Odt2rTJr1+/vj9o0KCo9cuXLzfbilzfs2dPv06dOv7ChQuj3hv8jZT+/XQ7erxiHWf9mwW0Pel7//3vf0ftT4cOHfz27dv7JSUlZp3us75vv/32C7cvNWbMGLNej2+yBH+DPn36RP27hg4dav6269evNz+vXLnSz8/PN+0m2E/10EMPmd9/6qmnwuuOPfZYs+7RRx/daXv777+/eb0s/XtGfq7SY1q9enV/9OjR4XXJPDbBv/2YY44x/w1Wto2sW7cu/N8wACB7kdkFkPY0gxOM/rtx48bwurI6duxoMnfBEpTPBr9TmaxurOyuZrcqQrNYu5Pd1Wxp8G848MAD5eWXXzaZVM1iVZQOklVaWmrKfHXU6GDRkmjdtw8//FCS+XfRUlfNsur2dF2wPc0iaom4ZteXLFlS7udNnjzZZJw1Ixu5z/o30CxusM9awvvRRx/JRRddJG3bto36jESfk3777bfl8MMPN9nByH/fpZdearKFmjmMpNlCHc07oCW1Kt4o4LtD9yHy36Xb0sHXtIxcvf/++1JUVGQy91pKH9CMt5Ycly3F1pJe3f+K0vcHn6vb1b+pHhstG//yyy93en8yj43+GyIfR6hoG9Gydt0HLa1et25dpbcLAMgMDFAFIO3paMn6zF5kwKrrynrjjTdMyaYGplpCGdAbfpXodDkavOozwBq8aqlsRQPkgQMHmgBZn7GsDL1pv/XWW02Ao89Uaolv/fr1K/UZGlxqoK2BbSyxnlvenb+LPpOp29OSUl1iWblypRl8q7x9Dp4JjiX4OwZBkz57miwaOOpxLysor9bXI7dXNshu0KCB+VpeYKUBqXYKRNIOjchgLpZdbSsIejX4jKTBnnYABa8H9G8QGYzuinaaaJm4lgLrc+ORo5zHKq1P5NjEo89bJ9JGNEDXzqGrr75amjVrZsrrtQxby/q1wwcAkB0IdgGktZ9//lk2bNhgRiNW+rVatWpm+qCy9BlCpa9H0ucQlT5nmIggeNUBjTSg3hMBcqTGjRtLnz59ZHdogKLBsg7kEyuYipUZ352/i25PaSeDZnJjCd5b3j4Hz2TGCkjK/l1diheglpfJ11HE9fnYSBo86uBQyd5WeSo7evbtt99uOjA0k65tWgfw0kyvZpKDv9me2t+y+1qZNqL7p3Noa4eTPout/4Y77rjDVCAcfPDBld4XAEDVkz53DgAQg97UqiCA0gGPdHAqHaVVy2LLyxQGdERlzXppoKoZqkQCPZ0KSLOtOhK0Dlq0JwLkZOrUqZMJLjQzpv/+Pf130QxikDHeVaAer9RY91lptri8zwi2FavDoyLbiUXnLta5d8vSgc2C13eXlqRrGW6kZGQZg33T/Q+OTZBJ1mC6oh0n8Y6XTtulQfqTTz4ZtV7LibVjJpUq2kYi36/ZXV00K6yDcukAXmVHEQcAZCae2QWQtjQDo5kkDdgiR3PVkVm1lFID0FjlzLEySBqk6rOGl1xyiezYsSPmc7I6Z++ugleddzdyKp3y6P5pNlO3nWo6GrHus2677PHQn/VYJPPvosGHdkLoCMDLli3b6Xcip8oJ5kotOyK0Bs5ahqqZxLIjCEd+hpb+6qjOOo1U2ZF1I/+t8bYTy8knnywzZ86U6dOnR83r+ve//91kXrt06SK7S8t5NUCLXJIxX7J+jpYlP/DAA1H/fg1ONfuuIxdXhB6vWMdK21HZNqTPke/qGew9oaJtREdt19G0ywa++hjE9u3bU7a/AAC3yOwCSAtabqtZNA1EdRoYDag0C6ZZKw0uI4MCHfDmoYceMlOy6DOpGnBpqbJmsv73v/+Z+Xf15j8ya3bmmWeaMmadUmb27NlmgBv9bA363nnnHTMlz4QJEypUmqwBb0VokKDTt1RmMKBk0Rt7zUTr1C86wFIw7ZJm+nQ6Fh30KPK55mT8XcaOHWsGeNL5WHVgIc0y6u9oAKllz8G8rJpd02Ojz1RqMKbPV+ozmBowP/LII2YwrkMOOcTMCayBrQa0OsjS0Ucfbf7uSgM73Za+T/8tGnjrv1PfF/x9dH5hpX8D/SzNOmtZaxAER9Jpbf7xj3+YKXl0ihot1X366afN8Xr11VejBn5KN3qM9O+sHRsnnniiqTzQLK8+Y3vYYYeZTpeK0OOlx1/bjXbS6N9D/y76rKuW42s71umZ9L8j/W8sMoucKhroVqSN6HlA5/fVAdO0o0LLm7Xda3vU3wEAZAnXw0EDyG7BFCPBolOoNG/e3D/hhBPMlCUbN26M+7uzZ8/2zz//fL9t27bm93Tqn27duvlXX321P2/evJi/M2XKFP/UU0/1mzZtaqbgadKkiX/KKaeY6YliTT1U3v7Gm3ooUnFxsd+pU6dKTT3Uv3//ct9TkamHAq+++qqZvkX3TZfOnTub/Zg7d+4e+bvMnz/f/E30vXl5eX6rVq38X//61/4rr7wS9b7HH3/c79ixo5lCp+w0RPp9v379zFQyBQUF5vhdcMEF/hdffBH1Gd9++61/+umnm6lo9H377ruvf+ONN0a955ZbbjH7kJOTEzUNUdmph4J9/+1vfxv+vMMPP9yfNGlS1HuC6XV0+qRd/U12V7zpn4J9KDt1k041pH9fPe7NmjXzL7/8cjMFTySdWkinGIpFp+/RtqdTOunnB9MQ6dRD+t9UixYt/Bo1avhHH320P336dPN65FRFyTw2u5r6aldtZPXq1aad6/HQdq/vO+KII/yXXnqpwvsAAKj6PP0/1wE3AAAAAADJlL51WQAAAAAAJIhndgEAQEps27bNPKddHn1eujLzAAMAEA/BLgAASIkXX3xxlwO2ffjhh2ZkbwAAdhfP7AIAgJTQaam+++67XY4KrdM0AQCwuwh2AQAAAAAZhwGqAAAAAAAZh2AXAFBhF1xwgbRv3z5qned5MmrUKHFt6tSpZl/0azbSv4v+fQAAQAjBLgCkuS+//FJ+85vfmFFqa9asKQcccIA88MADUe8pLi6Wm2++WTp27CjVq1c3X2+99VbZsWOHs/2uSj799FMTsK9fv36Pbuftt99Oi44BAACyAcEuAKSx9957T3r06CErV66UG2+8UcaMGSO//vWv5eeff4563x/+8AcT7B5//PHmPT179jTv/9Of/pTU/Xn88cdl7ty5konBrh6/VAS7uh0AALDnMfUQAKSpjRs3yvnnny/9+/eXV155RXJyYvdPfv755/LSSy+Z4Hb06NFm3WWXXSaNGzeWe++9V6644grp1q1bUvYpLy8vKZ8DAACwp5HZBYA0NWHCBFmxYoXcdtttJtDdsmWLlJaW7vS+f//73+brWWedFbVef9YB93Vu04o866rvu/7666V58+ZSq1YtUzq9ePHiXT6zG8uSJUvkoosukmbNmpmy6v3331+eeuqpnd734IMPmte0PFunmzn00EPNv3tXNLN92mmnmf1s2rSpDB06VLZv3x7zvZ999pmceOKJUq9ePbOdY489Vj755JPw61pWfO2115rvO3ToYI6FLj/99FP4Pc8995yZEqdGjRqmnFyPbdljE2zr5JNPNv8W3TftZNBMe3Dsxo4da74PtqFLQP+2999/vzkeBQUF5tj98Y9/lHXr1kVtQ/+mWqLeunVr8+857rjjdjmdDwAA2YjMLgCkqffff1/q1q1rAkcN7P73v/+ZAOq8886T++67zwREKgjyNBCLpIGQmjVrVoW2p0G1Bl/Dhw83ZdMaePXp00e++uqrnT67PBqgH3nkkeazNKvcpEkT+de//iUXX3yxyVYPGTIkXBL95z//WX7729/KVVddJYWFhfL111+bgPGcc86J+/nbtm2T3r17y6JFi8zvt2zZUp599ln54IMPdnqvrjvppJNMoDpy5EjTaTBu3DhT7q2dBIcffricccYZ5tj+4x//MMdVM+JK9zs4Lpo1//3vfy+XXHKJrFq1ygTpWio+e/ZsqV+/vnnf5MmTTYl5ixYtzL9HOw3++9//yqRJk8zPGrguXbrUvE/3tyx9ffz48XLhhReaf9eCBQvkoYceMtvQ4DzIqt90000m2NWgWhd9prtv375SVFRU4b8RAABZQefZBQCkn27duvk1a9Y0y5VXXum/+uqr5queus8666zw+3S9rnv22Wejfv/RRx816w844IByt/Phhx+a97Vq1crfuHFjeP1LL71k1o8ZMya8buDAgX67du2ifl/fM3LkyPDPF198sd+iRQt/9erVUe/Tfa5Xr56/detW8/Opp57q77///pU+Lvfff7/Zpu5fYMuWLf5ee+1l1uu/R5WWlvp77723369fP/N9QLffoUMH/4QTTgivu/vuu83vLliwIGpbP/30k5+bm+vfdtttUeu/+eYbv1q1auH1O3bsMJ+px2bdunVR743c9uDBg812yvr3v/9t1j///PNR6995552o9StXrvTz8/P9/v37R33u9ddfb96nfx8AABBCGTMApKnNmzfL1q1bzXO7OvqyZiD1q2YAX3jhBfnhhx/M+zS7165dO7nmmmtk4sSJsnDhQvMM71//+lepVq2ayYRWhG6nTp064Z8146pZSh1UqaI09n311VfllFNOMd+vXr06vPTr1082bNhgMpFKM6JajqzPHFeG7o/ul+5fZBb70ksvjXqfZqT1GGmWeM2aNeH90HJwzQx/9NFHMcvCI+nx1PdoVjfy36JZ27333ls+/PBD8z7NvmomVrPWQaY3EFmqHM/LL79syqxPOOGEqO1oRrp27drh7Wi2XzO4V155ZdTnBtlyAADwC4LdBOmzbG3btjVlhHrTpWWFWp5Wnl69ekU9p6WLDiIT+M9//iNnn322tGnTxpQM7rfffuFnvSLpM1/6mr5n3333lWeeeSahf8Nbb70lRxxxhPkcfb5MyyQBpI+gdFjPC5GCEt/p06ebr3oe0v+eGzVqJAMGDDDP1GrgquWu+nypBksVocFbJD1H7bXXXlHPru6KlvjqiMZ///vfTRlw5KLluUpLpJWWS+u+aSmxbnvw4MFRz9LGo8G87lfZIFLPh5GCzoCBAwfutC9PPPGEKf/W4Ls8+hkatOv+lf0MLVEO/i3z5883X3VaqETodnRf9PnjstvRTo9gO/pvj/W30vfpeRwAAPyCZ3Z3EZzqgCK6lKUDguhALhro6vN0mlHRLINOX1GeQYMGhUdLjXymLniuTm90dCAUDXj1szRTkZuba557U4888oiMGDHCPOt22GGHycyZM81n6k2OZlIqSjMv+nu33367eXZN5+L89ttvK/z7APY8fRZVBx7SgYoi6XlCRQ5cpIMa6X/Dc+bMMeu7dOligmUduEkHZEqVIFOqUyFpkBlLMDK0dtrpNEb6TOs777xjzksPP/ywCdKTMT1PsC933323HHTQQTHfs6uOAP0MDar1mWM9F1f29yuzr/p3ff7552O+Hjw/DAAAKo5gN0F6AxnQ8sG//OUvJjNaXFxc7tQcGtxq+VssOnJppI4dO5rMjZbRBcGuDmqiJYxnnnlm+D1aAvi3v/0tKtjVrMU999xjyuo0y6ODnQTzbWpgq4Ol6A2gDhgT0JtjAOlDS1h1MCPtUIvMWgZVJGUDIA3KNOiNLPfVIEoHmaqIIBMa0IzmvHnzKjVtke6TlkKXlJRUaLs64Jaez3TR8lwt1dYBobRTLxiAqyw952pgr/sXmd0tO/9vp06dzFcd5GtX+xKv1Fg/Q7ejozTvs88+cX8/2JbuV3nbKm87WqJ89NFHlzsYmP7bg7+Vnv8jM+plR20GACDbUcacBGvXrjW98UcdddQu56DU9+lIn1rqpjdz+jxeebSsTcsQA1p2V/YGUG+MNMOrgXawDc2M6A2jltlp9lZHEn366afN6/q8nN4866ikBx98sMlO62ilZHaB9KLPiaonn3wyar12ZumzuFp9Eo8+p6v/3et/32XLoOPRRyI2bdoU/lnn9l22bJk5P1SUZj+1lFqztLHOKRqUBfQ52kj5+fmm002Dy+B8Fos+o6wBv+5fQM+lWjpdtrNAg8j/+7//M6XA5e2LBt1KS7AjafCt/ybNNIfG4vqF/hz8Gw455BATEOsI1mU/I/L34m1H/9baQXDLLbfstJ/aQRm8XwNpvc7oaNCRn6vbBQAA0cjs7gZ93kynhdCbLJ1mQ0vxyqPP2WmvvJYm6vQa+vuaidDMbSxaxqzzXuqzeAEd4EVvdDWLrDdXWvqsP+uNoQ5moje2Or2GZnX1Jk3pDZiWNj722GOmrPDHH38Mzy157733msyvvl9vnHX6jcjgGoA72hmlFR86P60GPFqOrHPi6mBG2lmm55LIYEl/1mBRp/fR39H/1vX8ETnoVHn0v/1jjjnGPFur0wdpAKXPxuojD5Vx5513mgGVdEwA/V3dJ+0U1I42zV7q90qny9FKF81maqm2ds7pObV///7l7rN+pr5Pn0vWc6Ce97TqJfKxEKUdenp+1GBdM97672rVqpXp7NP904zvm2++GQ6MlQ7qpXPoakCp1TIaLOs0P3q89dllPffqvmnVzGuvvWYeNdHHWHRb+piJ/o6WTOu2dL++//57U4r+7rvvRm1Hq230fK6BtG5P/7ZatXPHHXeYgbX02Og+aAZX/946foM+KqOZc92evk+nOdLAXwfH0jLrYMokAABg2VGZ4ftmColatWqFl5ycHL969epR6xYuXBh+/6pVq/y5c+f67733nn/00Uf7J598ctRUELsyZcoUM1XEvHnzdnpNp7Vo3Lixf8stt0St1ykzLrzwQjPlhU6H0bJlS/+6664zn7N8+XJ/8+bN5vsaNWpE7bf+O5o2bWo+Q6ew0Pc89thj4c8tLCw029OpSgCkj6KiIn/UqFFmSpu8vDwzvc5999230/v+9re/+Z07d/YLCgr8Bg0a+L/5zW/82bNnV2gbwdRD//jHP/wRI0aYc4WeQ3R6m8hzXkWnHlIrVqww0+y0adPG7Hfz5s393r17+3//+9/D79FzUM+ePf1GjRqZc1SnTp38a6+91t+wYcMu91n3S/+NOi2Tnruuuuqq8DQ9wdRDAT0OZ5xxRng7uv+///3vzTk4kp5vdfolPfeXnYZIp3c65phjwudUPdb679NrQKSPP/7YTGlUp04d8z6dPurBBx8Mv65TFOn0UU2aNPE9z9tpGiI9Pt27dzfHXz+ja9eu5hy/dOnS8HtKSkr8m2++2UzvpO/r1auX/+2335p/F1MPAQDwC0//Lwh8s51mG4KMgzr33HNNOV6QIVWaBdXywbJ0+oxgUKkePXpUaHs6/YUObqIDs2gPf0CzsDoA1iWXXGJKkWPRTK5mXjRzoKV7miXWMjcty9NMiQ5ypVmVSJpB0CyvZjR0UKp///vfJosT0PdriVy8bQLITJot1nOOZhAjp/MBAACoyihjLlPCF1nCq8/C6uiYWsZX0VE/9ZnaitJSNaUBa0DL3TQQ1XLj8oJOLW9r3bq1+V7n29RyNi2j01JALWXU8kUN1mPRMrrq1aubEuog2NXgWUv0gsFPAAAAAKAqI9hNwGeffWZGQNZAUaf80fkVdSAYfbYryOrqM2G9e/c2A77oHJL6ngkTJpjnq3QuTH1mV0d07tmzZ3ikUx3MRQNdzfIOGzZMli9fHs7IBqOu6jO1OhiVZmF15E195lZ/Lxh8SulAKvo8WL169eTEE080AfgXX3xh3q+fq8+p6fy++myvZqM1wNWRmdXvfvc7B0cUAAAAAJKLYDcBOgiKDiqlwaKWImtmVoPKG264wWRMg0ypZk6D0ZZ1lFEdmEUHfNHf0SBTS6T1dwI6sqiWIWsJsi4BDUY166p0tE4dTEo/W7O7WnqopdNaXh3Q8mfdRw1gr732WjP6Z9euXWXIkCHh9+hrWo593nnnmVFbNXj+4IMPTPAOAAAAAFUdz+wCAAAAQIb46KOPTGJr1qxZZgpBnT1AZxOIR99z9dVXm0rQefPmmQrRWFPa6dgeWs2qSbi9995b/va3v5mq1YCGlZoMfPzxx81YQjrbgs5UoO91hXl2AQAAACBDaBXpgQceKGPHjq3Q+/WRR31kUitO9fdi0UrSs88+Wy6++GIz5Z0Gz7ro45SBu+66Sx544AF59NFHzWOfWl2qj2cWFhaKK2R2AQAAACADeZ63y8xupF69epn54stmds8880wTRE+aNCm87sgjjzTv1eBWQ0odJFczxDofvNqwYYMZPHf8+PFmTnkXeGbXjqS8dOlSqVOnjmkQAAAAAJJDA6FNmzaZYEhnD6lKNCtZVFSUFsewbJyiYwUF4wXtadOnTzcD3UbSrO3rr79uvl+wYIEZXFenMQ3oYLk6LpD+LsGuQxro6oBRAAAAAPaMxYsXh6fOrCqBbqv6jWTt9tCAsy7Vrl1bNm/eHLVOn48dNWpUSra/fPlyk6WNpD8Hs8cEX8t7jwsEuyImo6sWLlwo9evXd707VT5LriNKa91/Veu5Szccy+ThWCYPxzK5OJ7Jw7FMHo5l8nAsQzZu3GgSS8E9d1WhGV0NdF/qfa7UrJbvbD+27iiS30953nQW6BSigVRldasygl1by6608UQ2ICR2UtdeMD2O2XxSTwaOZfJwLJOHY5lcHM/k4VgmD8cyeTiW0arq44Ia6NbKcxfsBlzGKs2bN5cVK1ZErdOfdX3werBOp2WNfI8+1+sK/9UBAAAAQBw5nvvFtR49esiUKVOi1k2ePNmsVx06dDABb+R7NKOvozIH73GBzC4AAAAAZAh9tlfnyw3o4FFfffWVNGzYUNq2bSsjRoyQJUuWyDPPPBN+j74e/K6W3uvP+fn50qVLF7P+qquukmOPPVbuuece6d+/v7zwwgtmXt6///3v4az9kCFD5NZbbzXz6mrwq3Py6qBkFR0Jek8g2AUAAACADKFB6HHHHRf+ORhFeeDAgWYaoGXLlsmiRYuifufggw8Ofz9r1iyZMGGCtGvXTn766Sez7qijjjLrdC7e66+/3gS0OhLzAQccEP696667zkxPdOmll8r69evlmGOOkXfeeUcKCgrEFYJdAAAAAIjDywktLrdfGTpXrk5VFI8GvGWV9/7A7373O7PEo9nd0aNHmyVd8MwuAAAAACDjkNkFAAAAgDhcDxKVDgNUVVVkdgEAAAAAGYdgFwAAAACQcShjBgAAAIAMGaAKv+DQAQAAAAAyDsEuAAAAACDjUMYMAAAAAHHoYMguB0RmMObEkdkFAAAAAGQcMrsAAAAAEEdOTmhxuX0khkMHAAAAAMg4BLsAAAAAgIxDGTMAAAAAxOOJeIxQVSWR2QUAAAAAZByCXQAAAABAxqGMGQAAAADiyPFCi8vtIzFkdgEAAAAAGYfMLgAAAADE4eWEFpfbR2I4dAAAAACAjENmN0344puvHmOLI43QLpGOaJdI33bp25ZJ2wSAdECw6/zCuE5EVonIVpNo96WBiDQVTwpc7x6ymC9bRGSliGywLbWuiDQRz3wF3PClyLbLtSJSIr45TzYRkUYEF3DGl1IRWW2v5dpGq4kvjey1nNssIBN4jufZdTrHbxXHWdhpoLtURJaJmAul/il2iMhyEVkvvuwlntR0vZvIQr4JcH8UkWIRybVr15jA15d24pmbOCC1fNkuIj+EOwZDy2a7bBFf2hLwwlGgu8B2wIg9Z2pb/dmeM/cm4AUAh3hm15mtNrDVm7MaIpInIvkiJlNRKCJLXO8gsvbGbZHteCmwbTJolyUislh88xqQasvsebO6XfJsu6xmM2obXe8gstI6G+gG7TEvoo1uEpEVrncQALIa3Y3OrLfBQ9lyZc9eLDeKL4WUMyPFNtnOFg1wI7Nknr15K7Rtt7HDfUS2CXWwrLNZs5wYl7FiG3DUc7SHyF5a9SIRVTCBoPpgjfjSkqoDoIrLyQktLrePxHDonNGbM+XF+bNomTMZNLhol36cU4NnF9olUk3bXGmMgCLgRZxTgVTSZ3TjBbI5tu1qxzYAwAUyu85o5kzCIzdGK7EXSc3wAqmUZ9teSYzAojTiPUAq5dn2GKtdBiPgauUBkGra7rbFea3Uvh6vkwZAVWG6+10OUOVu01UemV1nGti+Bu0VjhRkdOuJx80bUq6ufYZc22VoepcQ367TNlnf4f4hG3kmWGhog92g0yWg58vgdSDVggH7yla8BG21MSXMAOBQ2gS7d955p3ieJ0OGDAmv69Wrl1kXuVx22WVRv7do0SLp37+/1KxZU5o2bSrXXnut7NiR/mWWngkoWtm+mm02kNhun4nUUZhbu95FZKHQTVlbW3lQaNtk0C61c0ZHvCVLARdaiEjtiPZYZM+dGlA0s68BLjqudfqrHRHX8kJbVq8dg01d7yAAZLW0KGP+/PPP5bHHHpNu3brt9NqgQYNk9OjR4Z81qA2UlJSYQLd58+by6aefyrJly+T888+XvLw8uf322yXdedJMfBP0rrbTZwTZCZ0zMihzBlLLkzriy762XepgVEHGVzMUtRzvHbKVJ3niyz52QKA14QqYUGatPtkzOKHtTqdkE6lj22WhLbtvZK/ldA4CmYB5dqsu58Hu5s2b5dxzz5XHH39cbr311p1e1+BWg9lY3nvvPZkzZ468//770qxZMznooIPklltukeHDh8uoUaMkPz/9A0bPBBG6AOlWedDGLkB6CM1X2swuQHoIdbSEglsAQHpxHuwOHjzYZGf79OkTM9h9/vnn5bnnnjMB7ymnnCI33nhjOLs7ffp06dq1qwl0A/369ZPLL79cvvvuOzn44INjbnP79u1mCWzcGJqfsbS01CxInB4/3/c5jknAsUwejmXycCyTi+OZPBzL5OFYJg/HMiTb//3I0mD3hRdekC+//NKUMcdyzjnnSLt27aRly5by9ddfm4zt3LlzZeLEieb15cuXRwW6KvhZX4vnjjvukJtvvnmn9atWrZKiorIDRqGyJ7MNGzaYE3sOk4LtFo5l8nAsk4djmVwcz+ThWCYPxzJ5OJYhmzZtkqqMMuaqy1mwu3jxYrnqqqtk8uTJUlBQEPM9l156afh7zeC2aNFCevfuLfPnz5dOnTolvO0RI0bIsGHDojK7bdq0kSZNmkj9+ow0u7sndR1ITI9lNp/Uk4FjmTwcy+ThWCYXxzN5OJbJw7FMHo5lSLx7fSBjg91Zs2bJypUr5ZBDDokacOqjjz6Shx56yJQZ5+ZGD+xwxBFHmK/z5s0zwa6WNs+cOTPqPStWrDBf4z3nq6pXr26WsvQklM0nomTRkzrHMjk4lsnDsUwejmVycTyTh2OZPBzL5OFYhu6xqzLdfZf/hCp++Jxydug0Q/vNN9/IV199FV4OPfRQM1iVfl820FW6XmmGV/Xo0cN8hgbNAc0U161bV7p06ZLCfw0AAAAAIJ04y+zWqVNHDjjggKh1tWrVkkaNGpn1Wqo8YcIEOfnkk806fWZ36NCh0rNnz/AURX379jVB7XnnnSd33XWXeU73hhtuMINexcrcAgAAAACyg/PRmOPRaYN0SqH7779ftmzZYp6pHTBggAlmA5r9nTRpkhl9WbO8GiwPHDgwal5eAAAAAEgUA1RVXWkV7E6dOjX8vQa306ZN2+Xv6GjNb7/99h7eMwAAAABAVcLjzgAAAACAjJNWmV0AAAAASCc5XmhxuX0khswuAAAAACDjkNkFAAAAgDi8nNDicvtIDIcOAAAAAJBxCHYBAAAAABmHMmYAAAAAiIN5dqsuMrsAAAAAgIxDsAsAAAAAyDiUMQMAAABAOaMh5zAac5XEoQMAAAAAZBwyuwAAAAAQBwNUVV1kdgEAAAAAGYdgFwAAAACQcShjBgAAAIA4crzQ4nL7SAyZXQAAAABAxiHYBQAAAABkHMqYAQAAACAORmOuusjsAgAAAAAyDpldAAAAAIiDzG7VRWYXAAAAAJBxCHYBAAAAABmHMmYAAAAAiCMnJ7S43D4Sw6EDAAAAAGQcgl0AAAAAQMahjBkAAAAA4vD0fw6HRNbtIzFkdgEAAAAAGYfMLgAAAADE4eV4ZnG5fSSGzC4AAAAAIOOQ2U0DvuwQke2276GAunykBV9KRaTQfCdSQzz6xpAGfNMe9XxZIiL54kme610CDF+KRaTI3Fp5Ut317gCGb86Vei337LWce0xkF4Jd5yegZSKyWsQEvHoCqim+tBRP6rnePWR1MLFGRJbbC6SqLr40FZGmXCjhjC+bRWSpiGyynTC54ksjEXPO5HIGN3wT4C4RkXUippMwR3ypa9tlTde7h6zusNbr+CoR0xETCnZ9aSGeNHC9e1WOlxNaXG4fieHQOQ0ofrI3bhr0VrN/Dr2Jmy++bHC9i8haK23b3GbbZTWbSVtk2yvgKtCdJyLr7blS22VwM6fnTP0ecFGZNc8GFCqoNFhr1vvmPAq4uMdcZDthiu35MldEtojIj+Kb9glkB4JdZzbaXmC9MObbk5CejApslnepPVkBqb5x02oDsW0x1y7V7elihfgm8AVSTYPaItsugxu3fLsE51Mg1bQKZrM9R+bZ82SebaeFtvMQSLWttm0G1+/gWq7tstTeY9JBiOxAsOuM3pzpiaZs6Z1nL5R6oqJHGC7aZbENIMrSdqnBMFUHcPEs5EZ7vixbRq83cNoxSLuEC+tsmyx7O+XZtrmOoAIObIioGox1LS+0WV5UmM6x63pBQgh2ndGTUHl/Fr154wKJVNM2F6+iwLML7RIuzpfaLuNd7D3bEQOkWjDeRizB+ZIqLaRacJ2O1Ta5x0R2YUQPZ2rYr7Fu4HZElJ4AqW6XuXF6hIMOGi2DAlIp32YjQiPdRvPtwkBAcKGWrcSKpcS+Tl4BqVYQ0dmSE+cek2t5ZTDPbtXFGdiZBjaY3V6m17fELo2YUgMOaMBQx5YyR/b6ltpAQ19npHCkVmjaqyb2XBmZwQ2mIdJzpY7KDKRaYxs4RF7LfXsOVYxgDxfql3OPqefQBkyPleE++ugjOeWUU6Rly5bieZ68/vrru/ydqVOnyiGHHCLVq1eXvfbaS8aPHx/1evv27c1nlV0GDx4cfk+vXr12ev2yyy4Tlwh2HfFMpqJ9xLMT2+xSbAPhVq53EVkodFPWTkRq2+A2aJfbbda3AzducKSZDWhLItploc30thMvXC0DpI5nOgfb2Nup4FoezE8etFkgtUJTsXWMGCgt8h5Tp8Vq7XoXsYdt2bJFDjzwQBk7dmyF3r9gwQLp37+/HHfccfLVV1/JkCFD5JJLLpF33303/J7PP/9cli1bFl4mT55s1v/ud7+L+qxBgwZFve+uu+4Slyhjdkjn0vWli52iYJu9WGrWrB4BBZzR3l5f9rVTvATzmda2PcGcMuAuu+tLB5tJW2+D3gLbLinHgzueNBXfBL1rIyoNtNO6FtdyOONJbfFlP9sutdTei7jHJNeV6fPsnnTSSWapqEcffVQ6dOgg99xzj/l5v/32k48//ljuu+8+6devn1nXpIlWWP3izjvvlE6dOsmxxx4btb5mzZrSvHlzSRe09jTI8HrSXDyTMdPsRH0ujnDOk1zxTCl9e9s2mxDowjk9N3pSVzxpa9tlCwJdpAWtLPCklXjSUTxpYwINruVwTR+H86SZPV/q9Vw7B7n1x86mT58uffr0iVqnQa6uj6WoqEiee+45ueiii0ypcqTnn39eGjduLAcccICMGDFCtm6NN65BanD3CgAAAABpbuNGnYbvF/p8rS67a/ny5dKsmT568Qv9Wbe3bds2qVEj+lEhfQZ4/fr1csEFF0StP+ecc6Rdu3bmWeGvv/5ahg8fLnPnzpWJEyeKKwS7AAAAABCPjobsckRku+02bXSMgF+MHDlSRo0alfLdefLJJ02ZtAa1kS699NLw9127dpUWLVpI7969Zf78+abk2QWCXQAAAABIc4sXL5a6dXWQsZBkZHWVPmO7YsUKiaQ/67bKZnUXLlwo77//foWytUcccYT5Om/ePIJdAAAAAEg3OZ5nFpfbVxp8Rga7ydKjRw95++23o9bpaMu6vqxx48ZJ06ZNzejNu6IjOyvN8LrCU+oAAAAAkCE2b95sAs2vbLCpUwvp94sWLTI/68BR559/fvj9Ohfujz/+KNddd518//338vDDD8tLL70kQ4cOjfrc0tJSE+wOHDhQqlWLzplqqfItt9wis2bNkp9++kn++c9/mm307NlTunXrJq6Q2QUAAACADPHFF1+YOXMDw4YNM181SB0/fryZ/zYIfJVOO/TWW2+Z4HbMmDHSunVreeKJJ8LTDgW0fFl/T0dhLis/P9+8fv/995t5fvX54gEDBsgNN9wgLhHsAgAAAEB5tbBOB6iq3Nt79eolvu/HfV0D3li/M3v27HI/t2/fvnE/V4PbadOmSbqhjBkAAAAAkHEIdgEAAAAAGYcyZgAAAACIw8sJLS63j8Rw6AAAAAAAGYfMLgAAAADE4Xk5ZnG5fSSGIwcAAAAAyDgEuwAAAACAjEMZMwAAAADE4XmOB6hyOMVvVUdmFwAAAACQcQh2AQAAAAAZhzJmAAAAAIgnxwstLrePhJDZBQAAAABkHDK7AAAAABCP54nncpQoRqhKGJldAAAAAEDGIdgFAAAAAGQcypgBAAAAIA6dY9fpPLukJxPGoQMAAAAAZByCXQAAAABAxqGMGQAAAADi8ByPxux0JOgqjswuAAAAACDjEOwCAAAAADIOZcwAAAAAEIeX45nF5faRGDK7AAAAAICMQ2YXAAAAAOLRzKrL7CqZ3YSR2QUAAAAAZByCXQAAAABAxqGMGQAAAADi0GluXU51yzS7iSOzCwAAAADIOAS7AAAAAICMQxkzAAAAAMTBPLtVF8GuY774IrJFRAq1KYtIXfEkz/VuAeLLdhHZZH+qJZ7UcLxHgLbLHSKyUURKRaS6iNQWz5w7AdfXcm2XRfbWSq/lua53C1ku1C63isg2e49ZRzzJd71bQEoR7DoPJhZG3LipPPGlhYg04wYOTvimLf4sIqtFTGChcsWXBiLSVjxOG3DEl1UistQGFL59EqeW+NKezhg448tmey3XgELPn3rtri6+tBZPGrrePWQpX4pF5Cd7j1kScY/ZVERaco9ZSWR2qy6e2XUaUMwXkfUmkBApsIuekBaLmJs6wAUNdJfb74N26dngd6HtKQZSy5e1IrJIxNzAaWZCg9tqtvpgnr2xA1LLN1VZ822FVjXbLrV9amf2T+KbQANIrdB1WtvlOnurH1zLS22H4TLXuwikDMGuM+vsxbG6vUB64d7gkBU2IAZSXW2w2rbJvIh2mWeX9bYkCkj1jduKiNLl4NIVdBRqRk2DYSDV1tjAVtthULacY9upVsasdLx/yE7aybLZdrxE3mPm26+r7CMhQOYj2HVmc0QZXll59uKpN3BAqtulXgBjlSrn2soDfQ+QSkW2kyVWuwxKu8igwYUN9jpetsTQC1ce0HGN1NOKF213uXHuMYNzKio90a7LBQkh2HWGUlAAADL/Ws71HumIdonsQLDrTG3b8xurx3dHxDNpQCrVstmIWOVNJeEBgYDUCs6HO8q5YauT4n0CVF17HS8bOPi2vepo4YzKjFSrZe8xg4GpIu2w2V2u5cgODKvqTAP7DNoWeyOXay+OxfarjsZMXwRSy5MC8c3ooSvLnCJKbNvUdssFEqmlo4aGRhD9yT7iETx3VhrxvGQj17uJrNTYPrdbGPE8uW/bpV7Xtd0CqVbPJlU22fNlTkSgq9fz5sysUEk5nic5DkdE1u0jMURTjoR6ejvZbMQO+3xuMNduSy6QcKiNiDSxN2yFdtGgQoNgneKFEy5caGTbZq4NJArtc2c1zbmU+cnhQmjKq4628qDItkttn9oe24lngg4gtULJko628mBHxLVcNRcxU1wC2YFuHedZtM62502D3Rw7EX0wIjPgqiOmg/jSLGIwKs3m1iTQhTOhttfMVh5ssNmJ6vacSb8t3PGkrviyv22XRfbWqh6ZMzil95K+7Guv4zoYlWfPl1oJg0rTy4zLSw2XuYRxJk6LG7i6dgHSh2cyZroA6SOUwdXSUSB9hDpc9DEPIN3uMbWCkDENkL3oJwAAAAAAZBwyuwAAAAAQh5fjmcXl9pEYMrsAAAAAgIxDsAsAAAAAyDiUMQMAAABAHJ7nmcXl9pEYMrsAAAAAgIxDZhcAAAAA4tEBolwOEsUAVQkjswsAAAAAyDgEuwAAAACAjEMZMwAAAACUlx50mSIkPZkwDh0AAAAAIOMQ7AIAAAAAMg5lzAAAAAAQT05OaHG5fSSEIwcAAAAAyDhkdgEAAAAgHp3m1uVUt0yzmzAyuwAAAACAjEOwCwAAAADIOJQxAwAAAEAcXo5nFpfbR2LI7AIAAAAAMg7BLgAAAAAg46RNsHvnnXeK53kyZMiQ8LrCwkIZPHiwNGrUSGrXri0DBgyQFStWRP3eokWLpH///lKzZk1p2rSpXHvttbJjxw4H/wIAAAAAGRkxaSmxs8X1Aai60uLQff755/LYY49Jt27dotYPHTpU3nzzTXn55Zdl2rRpsnTpUjnjjDPCr5eUlJhAt6ioSD799FN5+umnZfz48XLTTTc5+FcAAAAAANKF82B38+bNcu6558rjjz8uDRo0CK/fsGGDPPnkk3LvvffK8ccfL927d5dx48aZoHbGjBnmPe+9957MmTNHnnvuOTnooIPkpJNOkltuuUXGjh1rAmAAAAAA2C1ekN11tDA+VdUNdrVMWbOzffr0iVo/a9YsKS4ujlrfuXNnadu2rUyfPt38rF+7du0qzZo1C7+nX79+snHjRvnuu+9S+K8AAAAAAKQTp1MPvfDCC/Lll1+aMuayli9fLvn5+VK/fv2o9RrY6mvBeyID3eD14LV4tm/fbpaABseqtLTULEicHj/f9zmOScCxTB6OZfJwLJOL45k8HMvk4VgmD8cyJNv//cjCYHfx4sVy1VVXyeTJk6WgoCCl277jjjvk5ptv3mn9qlWrKH9OwslMS9D1xJ6T47xwoErjWCYPxzJ5OJbJxfFMHo5l8nAsk4djGbJp0yapynQQXV1cbh9VLNjVMuWVK1fKIYccEjXg1EcffSQPPfSQvPvuuybwXL9+fVR2V0djbt68uflev86cOTPqc4PRmoP3xDJixAgZNmxYVGa3TZs20qRJk50yyaj8SV3/g9Rjmc0n9WTgWCYPxzJ5OJbJxfFMHo5l8nAsk4djGZLqxBbgPNjt3bu3fPPNN1HrLrzwQvNc7vDhw03wmZeXJ1OmTDFTDqm5c+eaqYZ69Ohhftavt912mwmaddohpZniunXrSpcuXeJuu3r16mYpS09C2XwiShY9qXMsk4NjmTwcy+ThWCYXxzN5OJbJw7FMHo5l6B4byKpgt06dOnLAAQdEratVq5aZUzdYf/HFF5sMbMOGDU0Ae+WVV5oA98gjjzSv9+3b1wS15513ntx1113mOd0bbrjBDHoVK5gFAAAAgErRYN1lwE5nQdUcoGpX7rvvPtMTpJldHVBKR1p++OGHw6/n5ubKpEmT5PLLLzdBsAbLAwcOlNGjRzvdbwAAAACAW2kV7E6dOnWn+n6dM1eXeNq1aydvv/12CvYOAAAAQLbxckKLy+0jMRw6AAAAAEDGIdgFAAAAAGSctCpjBgAAAIC0kuOFFpfbR0LI7AIAAABAhvjoo4/klFNOkZYtW5qpr15//fUKjZ10yCGHmBlt9tprLxk/fnzU66NGjTKfFbnolLGRCgsLzaw4OrtO7dq1zSDDK1asEJcIdgEAAAAgQ2zZskUOPPDAcgf5jbRgwQLp37+/HHfccfLVV1/JkCFD5JJLLpF333036n3777+/LFu2LLx8/PHHUa8PHTpU3nzzTXn55Zdl2rRpsnTpUjnjjDPEJcqYAQAAACBDyphPOukks1TUo48+Kh06dJB77rnH/LzffvuZQFangdWpXwPVqlWT5s2bx/yMDRs2yJNPPikTJkyQ448/3qwbN26c+awZM2bIkUceKS6Q2XXMlx3iyyrxZZH48rP4slF88V3vFrKcL6XiyzrxZbFtm2vNOsAlPTf6sll8WWLb5Qrxpcj1bgHiy3bxZbltl0vFl62udwkQX0rEl9W2Xer1fAP3mFXcxo0bo5bt27cn5XOnT58uffr0iVqnQa6uj/TDDz+Y0uiOHTvKueeeK4sWLQq/NmvWLCkuLo76HC1zbtu27U6fk0pkdh3yZZMWDmiFe8Ta5SLSUHxpLx59EXAgFDz8KGLaZ3BR1B7FmuJLJ/GkwPEeIhuFbtD0orpaREoiXllmz5f1He4dspkGEyKLRaS4TLvU7EdL8cz5E0gtX7bYa3lhxLVcn52sJ750EI8QoHL0P2OX/ynbbbdp0yZq9ciRI82ztLtr+fLl0qxZs6h1+rMG1Nu2bZMaNWrIEUccYZ7j3XfffU0J88033yy/+tWv5Ntvv5U6deqYz8jPz5f69evv9Dn6miu0dEd8c1EMAt0C24p9exOnF858EWntejeRlQHFQu07tG0w176iWV29cC4QXzpz8wYHVtolt8w5c7ttl/vREYOU00qDUCdMaZl2qdf4ZXZdI9e7iSzM6IYC3W0iUj2ikFPXrxORPBFp73gvkYjFixdL3bp1wz/rYFKpclJEWXS3bt1M8NuuXTt56aWX5OKLL5Z0RerQmXVlAl2xX6vZmzktO9nheB+RfbbZQDcvItANThX5NuDV14HUCZXQr7TnyLwy58zqNrBY63gvkZ20c3qHPT9Gtst8G/TqY0qUjSLV1tt7zMhAV+x1Xe8z9dEkHgGpijTQjVySFew2b958p1GT9WfdhmZ1Y9EM7j777CPz5s0Lf0ZRUZGsX79+p8+J95xvKhDsOhM8zxMrQ1bNXjwjy5uBVLXLkjKBbkDXldqAGEilIrvkllNbphk2INU221upWNfyXHtOZbwDpJpep/04t/nV7HWea3lCA1S5XPagHj16yJQpU6LWTZ482ayPZ/PmzTJ//nxp0aKF+bl79+6Sl5cX9Tlz5841z/WW9zl7GmXMadnPEPQC0xeBVIt306Zol3DdLuNlyPw4gTCwp+Xuol2Wd04F9hQvog2WbX/BOq7lmUwD0SDjGkwtpFMKNWzY0AwYNWLECFmyZIk888wz5vXLLrtMHnroIbnuuuvkoosukg8++MCUJ7/11lsSuOaaa8zcvVq6rFMK6fPCubm5cvbZZ5vX69WrZ8qZhw0bZrajWeErr7zSBLquRmJWBLvO1LVleWWzaMGzPrVEJHbZALDn1LGnBc2ilS2N2WFf++VZESA1tHS5ti3Nq1bm5i3ImtVztG/Ibg3sYH6lZYKHYAyOhgw2CQfq2WfGS2Lc6hfZR+j0PhOZ6osvvjBz5gY0AFUDBw40g0zpAFORIynrtEMa2Oo8uWPGjJHWrVvLE088ETXt0M8//2wC2zVr1kiTJk3kmGOOMVMK6fcBnaooJydHBgwYYEaK1t9/+OGHxSWCXacnonr22d3gGYog0NULYwsGAULKeZJnRxD92Q78o0GG2Hap7bM5gwAh5fRc6EsL+8x4YcTzkSW2E6auDTqAVGtkn9vdGjHmRqk9Z2o7jR7dFEgNDWQb2rZZWuYe07P3mHTCZPI8u7169RLfjz9egAa8sX5n9uzZcX/nhRde2OV2CwoKZOzYsWZJFwS7Tm/eOtjs2Rrb0xaa3iV0EuLGDa5osKsXQR2oIBjAQoNe7bkLPZcBpJondcSXjiKyxAYWQemytsvW4lHGDGcdhHvbqYc22nNmju2A0Xap13TAxT1me3vtXh1xj1nDXuMZIRzZg2DXodAcZ21txkIHCtCbtZpkdOFUqP01E98EEZpJE9suCSbglmfmh6wbMZBadfF2KrcHUivUBvcS31QdaFCh1/YaXMvhVChz2ybiHjOUUCGjmxgvxzOLy+0jMQS7adIz/Eu5KJAeQhdEfYYXSB+hAIJnzZB+Qo948JgH0jGxwrUc2YvuHQAAAABAxiGzCwAAAADxeF5ocbl9JITMLgAAAAAg4xDsAgAAAAAyDmXMAAAAAFBeGbHLEZEpY04YmV0AAAAAQMYhswsAAAAAcXg5ocXl9pEYDh0AAAAAIOMQ7AIAAAAAMg5lzAAAAAAQT05OaHG5fSSEIwcAAAAAyDgEuwAAAACAjEMZMwAAAACUlx50mSIkPZkwDh0AAAAAIOOQ2QUAAACAeDwvtLjcPhJCZhcAAAAAkHEIdgEAAAAAGYcyZgAAAACIJ9cLLS63j4SQ2QUAAAAAZByCXQAAAABAxqGMGQAAAADi0Spil5XEVDEnjMwuAAAAACDjkNkFAAAAgDi8HM8sLrePxJDZBQAAAABkHIJdAAAAAEDGoYwZAAAAAOLxckKLy+0jIRw5AAAAAEDGIdgFAAAAAGQcypgBAAAAoLz0oMsRkUlPJoxDBwAAAADIOGR2AQAAACAuT8RzOdct8+wmiswuAAAAACDjEOwCAAAAADIOZcwAAAAAEI8OTuV0gCrKmBNFZhcAAAAAkHEIdgEAAAAAGYcyZgAAAACIx3M8GrPTkaCrNoJdx3zZLCKrRWSLiOSKSAMRaSQefxo45Mt2EVkjIhvMTyJ1RKSxeFLD9a4hi/lSIiJr7bJDRGqa86W2T49pGeCIb86RG+w5s1BE8kSkoVk8CujgkC9b7T2m3mtqW6xv7zG1jQLZgYjKId+cgBbZmzY9CekFc6O5YPqyl3iS73oXkYV80/Ey3960eXbZbNtlR/GkrutdRBbyzXlynj1HBu1yiw18W4ovzQl44SjQ/VlEVohIqb2Wb7XB73p7ziTgRer55tz4U4x7zNX2HrPA9S5WLV5OaHG5fSSEI+eIbwKJRfbiqCec6hFfN9uLJ+Dixm2hDXQLyrTNYnPhDGXXgFRbZm/UIs+VQaXBUhv4Aqm23ga6ObY9Bu1TM2cabKx0vYPIQr65Xus9ZkmZ63iB7YxZZK/3QOYj2HVmne1t0+ytV+ZPUs32CBc53D9kp032Qli2XXp23XabsQBSJ9TBssY+6lH2sqVBRVDeDKTaGttpXbYsNNeeNzWLRlABF/eYRTbILXstz7PXeu3UBjIfZczOaNCgYpXd6UWy2J6oKGVGKhXZGzdtg2UFQQadMEi1YhvQxmqXQUkzN25wobCcvEGuPV9q2+V2C+l0j7nDtk3G4agw5tmtssjsOhNc+GL1+GqwoY2aiyNSrZpte9oGywraKu0SqVbNXq5itcugbTLgClzQdhcvcxt0HMbqpAH2JO4xgQDBrjP1I3rXIvk2i1HHlp8AqVTXtruiGBfJIntjV8/RviFbhUanr2/Pl2XbZTD4io5kD6SajgauSmIEFKV25FsyMkg1PV9Ws/eTkXx7La9pFyDz0a3jTC0RaSoiyyPK83z7vQYbrbhAIuV01FBf2ojIAlueVy0ioMi17ZIMGlxoYQfv2xbxPGQQYDSmEwaONLTPR66znS65tl2W2ut8M9c7iCyk0wTqCPWhwfsKy9xj6jW8DfeYlcVozFUWwa4jepLxpbV9XmJlxHM/euFsJh49bnDEkwbimwvjSjuIhW97iZuKZ74CqafTZPiyjx35dq0NJmraQLcJN25w2EHY0Z4vV9tMWjDPrk6HRecgXHYQavJklR14MqiA0XtM7YgBsgPBrkOhm7PG4psyKO1ty2E+PqSF0Fy6dSOmGdK2STABtzxz49bWdhSGnoekXcI1z3QOtrCZNK7lSA+hc2Mj8U3HC+1ytzFAVZVFsJs2JyT+FEjXmzggvYRu2LhpQ3rhWo50RLtEtuNuAQAAAACQcejqAQAAAIB4PC+0uNw+EkJmFwAAAACQcQh2AQAAAAAZhzJmAAAAAIiHMuYqi8wuAAAAACDjkNkFAAAAgHhyckKLy+0jIRw5AAAAAEDGIdgFAAAAAGQcypgBAAAAIB4GqKqyyOwCAAAAADIOwS4AAAAAIONQxgwAAAAAcXieZxaX20diyOwCAAAAADIOmV0AAAAAiMfLCS0ut4+EcOQAAAAAABmHYBcAAAAAkHEoYwYAAACAeHR8qByX8+y623RVR2YXAAAAAJBxCHYBAAAAABmHMmYAAAAAiEfnuXU51y3z7CaMzC4AAAAAIOOQ2QUAAACAeHRwKpcDVLncdhVHZhcAAAAAMsRHH30kp5xyirRs2VI8z5PXX399l78zdepUOeSQQ6R69eqy1157yfjx46Nev+OOO+Swww6TOnXqSNOmTeW0006TuXPnRr2nV69eZnuRy2WXXSYuEewCAAAAQIbYsmWLHHjggTJ27NgKvX/BggXSv39/Oe644+Srr76SIUOGyCWXXCLvvvtu+D3Tpk2TwYMHy4wZM2Ty5MlSXFwsffv2NduKNGjQIFm2bFl4ueuuu8QlypgBAAAAIB4vJ7S43H4lnHTSSWapqEcffVQ6dOgg99xzj/l5v/32k48//ljuu+8+6devn1n3zjvvRP2OZn41wztr1izp2bNneH3NmjWlefPmki7I7AIAAABAlpo+fbr06dMnap0Gubo+ng0bNpivDRs2jFr//PPPS+PGjeWAAw6QESNGyNatW8UlMrsAAAAAkOY2btwY9bM+X6vL7lq+fLk0a9Ysap3+rNvbtm2b1KhRI+q10tJSU+p89NFHm6A2cM4550i7du3Ms8Jff/21DB8+3DzXO3HiRHGFYBcAAAAA0nye3TZt2kStHjlypIwaNSrluzN48GD59ttvTalzpEsvvTT8fdeuXaVFixbSu3dvmT9/vnTq1ElcINgFAAAAgDS3ePFiqVu3bvjnZGR1lT5ju2LFComkP+u2ymZ1r7jiCpk0aZIZ8bl169ZSniOOOMJ8nTdvHsEuAAAAAKSdNJlnV4PPyGA3WXr06CFvv/121DodcVnXB3zflyuvvFJee+01M02RDmi1Kzqys9IMrysEuwAAAACQITZv3myyqZFTC2ngqYNJtW3b1gwctWTJEnnmmWfM6zoX7kMPPSTXXXedXHTRRfLBBx/ISy+9JG+99VZU6fKECRPkjTfeMHPt6nO+ql69eib7q6XK+vrJJ58sjRo1Ms/sDh061IzU3K1bN3GFYBcAAAAAMsQXX3xh5swNDBs2zHwdOHCgmTJI579dtGhR+HXN0mpgq8HpmDFjTHnyE088EZ52SD3yyCPma69evSTSuHHj5IILLpD8/Hx5//335f777zdz7+rzxQMGDJAbbrhBXCLYBQAAAIAMmWdXA1ItO45HA95YvzN79uy4v1Pe5ykNbqdNmybphnl2AQAAAAAZh2AXAAAAAJBxKGNOA75oWcAO0/fgSa7r3QHCfNMuVa544nAUQiCCL6UiUmIuYbRLpN+1XM+X5BKQHrjHzKzRmFF5BLvOT0CrRWSliGzXgnzxpZ7OdiWe1HS9e8hivmwUER1lb7NdU1N8aSaeNHC8Z8hmvhTqzH8istb8JJIvvjQWkaYEF3DGNx0vK+z1PBRU+NLQXsvzXe8esvoec42IrBKRbfYeU6es0Wt5bde7B6QMwa7Tk9Bie4EM/hSl9mK5UXzZWzyp5XgvkY18E0j8ZG/aglOEBr9bxJc24klTx3uI7A10/ydivmp2IsfewOl5dKv40oEsLxxVGcwXkfW2TWrbLLGdhZvstZyAFy4sFZFltmNQ22UQ/Oo9ZifxTOCLCvO80OJy+0gIXeHObLG9bXoCKrBBRb79vkhEltiAGEj1jdvP9mZN22KeXWrYC6W2y2LXu4msvXErtO0y354zg3OndtBscL2DyEpB29M2WT3iWl7dXue1cgtILd90BGoyxStzLdfv9Rr+M/eYyBoEu86sDz9zFs2zJ6RN9sYOSKWNtqReb9bK9iLm22wvQQVSK9TBst6eL8u2y6AqZp2jvUN200yZyo1xe6XLGoIKOLDeXq/1fjKSZ6/lW21nDJD5KGN2Jhj4J1ZZQo4NhHUBUt0u/Tj9YF6ZtgukSklEKZ7EaZtUHMAFbXfxygsjr+XcbiGd7jH1fMo9ZqVQxlxlkdl1RkucVKwe3xL7p+E5H6RafsQNWlmlEe8BUikv4lnIsny7aHkekGo1Is6NZZXY8yUj4CLVuMcEAgS7zjSwN3BFZU5GpbZHrgGDWsCBOmbk5Z3bpW/LmzWgqO9w/5CNQtNlNIpT8aKZNX1dR78FUq2RvZUqW1kQtNMmDJwGR/eY+fa6XfYeU9tqXfFMRw2Q+ZwGu4888oh069ZN6tata5YePXrIv/71r/DrvXr1Es/zopbLLrss6jMWLVok/fv3l5o1a0rTpk3l2muvlR070r/M0jNBQxv7Jyi0J6Tgqw4J38r1LiILhW7K2tmgtrDMohfOdkzxAkdaiJip2YojzpU6CItqxVQacETbZDMbRGyLaJfFtgOmiesdRBbyTDKlnS2fL3uPWdPef6JScnLcL0iI04dIWrduLXfeeafsvffe4vu+PP3003LqqafK7NmzZf/99zfvGTRokIwePTr8OxrUBkpKSkyg27x5c/n0009l2bJlcv7550teXp7cfvvtku48aSy+6VlbY+czzbW9cQ3F4/keOKJTXvnS2bbLDbZXWKcoaGQ7aYDU03OiL3vZgah0BFzt1NTzZ2PxTEUC4KaD0JfW9hy5xgYUeTbQ1QotblDhhicNxDflzGvsoKe5tjJL7zHLDlwFZC6nEdUpp5wS9fNtt91msr0zZswIB7sa3GowG8t7770nc+bMkffff1+aNWsmBx10kNxyyy0yfPhwGTVqlOTnp38ZcGguXebTRXoJldC3sAuQTuXMje0CpFNFTD27AOnDM1ncX5JE2B3637nLRxJ4HCJRaZM+1Cztyy+/LFu2bDHlzIHnn39ennvuORPwanB84403hrO706dPl65du5pAN9CvXz+5/PLL5bvvvpODDz445ra2b99ulsDGjTrdikhpaalZkDg9fpql5zjuPo5l8nAsk4djmVwcz+ThWCYPxzJ5OJYh2f7vRxYHu998840JbgsLC6V27dry2muvSZcuXcxr55xzjrRr105atmwpX3/9tcnYzp07VyZOnGheX758eVSgq4Kf9bV47rjjDrn55pt3Wr9q1SopKtKBebA7J7MNGzaYE3sOzxfsFo5l8nAsk4djmVwcz+ThWCYPxzJ5OJYhmzZpKTWQhcHuvvvuK1999ZU5EbzyyisycOBAmTZtmgl4L7300vD7NIPbokUL6d27t8yfP186deqU8DZHjBghw4YNi8rstmnTRpo0aSL16zPS7O6e1HUgMT2W2XxSTwaOZfJwLJOHY5lcHM/k4VgmD8cyeTiWIQUFVXzMDy8ntLjcPqpmsKvP1e61lw46ItK9e3f5/PPPZcyYMfLYY4/t9N4jjjjCfJ03b54JdrW0eebMmVHvWbFihfka7zlfVb16dbOUpSehbD4RJYue1DmWycGxTB6OZfJwLJOL45k8HMvk4VgmD8cydI8NuJCTjj1gkc/TRtIMsNIMr9LyZy2DXrlyZfg9kydPNtMYBaXQAAAAAIDs4zSzq+XEJ510krRt29bU8k+YMEGmTp0q7777rilV1p9PPvlkadSokXlmd+jQodKzZ08zN6/q27evCWrPO+88ueuuu8xzujfccIMMHjw4ZuYWAAAAAColxwstLrePqhfsakZW58XV+XHr1atnglgNdE844QRZvHixmVLo/vvvNyM06zO1AwYMMMFsIDc3VyZNmmRGX9Ysb61atcwzv5Hz8gIAAAAAso/TYPfJJ5+M+5oGtzpQ1a7oaM1vv/12kvcMAAAAABTz7FZVaffMLgAAAAAAu4tgFwAAAACQcZxPPQQAAAAAac2jlLgqIrMLAAAAAMg4BLsAAAAAgIxDGTMAAAAAlFfC7LKMmRLqhJHZBQAAAABkHDK7AAAAABCPlxNaXG4/C2zZskVq1aqV1M/MjiMHAAAAAEhbzZo1k4suukg+/vjjpH0mwS4AAAAAwKnnnntO1q5dK8cff7zss88+cuedd8rSpUt36zMJdgEAAABgVwNUuVyywGmnnSavv/66LFmyRC677DKZMGGCtGvXTn7961/LxIkTZceOHZX+TIJdAAAAAEBaaNKkiQwbNky+/vpruffee+X999+X3/72t9KyZUu56aabZOvWrRX+LAaoAgAAAACkhRUrVsjTTz8t48ePl4ULF5pA9+KLL5aff/5Z/va3v8mMGTPkvffeq9BnEewCAAAAQLnFsC4LYrOjGHfixIkybtw4effdd6VLly7ypz/9Sf7whz9I/fr1w+856qijZL/99qvwZxLsAgAAAACcuvDCC+Wss86STz75RA477LCY79FS5r/+9a97NtjVh4OnTp0q8+fPl3POOUfq1KljRsqqW7eu1K5dO5GPBAAAAID0o+NDuRwjKjvGp5Jly5ZJzZo1y31PjRo1ZOTIkXsu2NW66RNPPFEWLVok27dvlxNOOMEEu1o/rT8/+uijlf1IAAAAAEAWq1Onjgl4mzZtGrV+zZo1Zl1JScmeLwC/6qqr5NBDD5V169aZyDpw+umny5QpUyq9AwAAAACA7Ob7fsz1mlDNz89P6DMrndn997//LZ9++ulOG2zfvr2ZEwkAAAAAMoaXE1pcbj+DPfDAA+ar53nyxBNPRD0Wq9ncjz76SDp37pyaYLe0tDRmClmHgtbUMwAAAAAAFXHfffeFM7v6SGxubm74NU2walI10UdlKx3s9u3bV+6//375+9//Ho7AN2/ebB4UPvnkkxPaCQAAAABA9lmwYIH5etxxx5nphxo0aJC0z650sHvPPfdIv379zNxHhYWFZjTmH374QRo3biz/+Mc/krZjAAAAAOCc54UWl9vPAh9++GHSP7PSwW7r1q3lP//5j7z44ovmq2Z1L774Yjn33HOjBqwCAAAAACCeYcOGyS233CK1atUy35fn3nvvlT0e7OoDwkcddZQJbnWJnHtXX+vZs2eldwIAAAAA0pHn5ZjF5fYz1ezZs6W4uDj8fTz66GwiKh3sai11rPmPNmzYYF5LZP4jAAAAAEB2+TCidHlPlDFXuptAR8mKFVnrZL+afgYAAAAAwLUKZ3bPOOMM81UD3QsuuECqV68efk2zuV9//bUpbwYAAACAjMEAVXtMEGNWhI7UvMeC3Xr16oUzuzqfbuRgVDr/0ZFHHimDBg2q9A4AAAAAALJPPRtj7ikVDnbHjRtnvuqkvtdccw0lywAAAACAhAUx5p5S6QGqRo4cuWf2JIv5UiQihfYR6priVf5RaiDpfNHB5raa70LtstKnCyDpfNMet4hIqYhUF09+eaQGcMk31/Ht9tZKz5mZW3aIqsMXHeV2m9bBikgt7jETpsfP5X/TnE8SldDd6yuvvCIvvfSSLFq0SIqKNFD7xZdffpnwzmQbX3aIyM8islaffLYNuUB8aSmeNHS9e8jqYGKFXfQiqT/niS9NRKQFF0o448sGEVkS0QmTK77U1xngxZN817uHrA5yF4vIRtsJo+fIWuKbdlnb9e4hqzus9Xy5RicItfeY1cWXFiLSiM4YpI1DDjlEpkyZIg0aNJCDDz643CmGEokzKx3sPvDAA/LXv/7VDFL1xhtvyIUXXijz58+Xzz//XAYPHlzpHcjugOJHEVlv/wx6o+bb3rcF5jsCXrixzF4g9WSTZ9ftsOv04tnW8f4hG/kmkJhv22K+bZ/aHlebbJov+4gnua53E1mZNZtnO2Dy7KLtUtvrPNsua7reTWTlPeZPNtDNjbjHLLTr9XvtwEaFMUDVHnPqqaeGBz4+7bTTkv75lQ52H374Yfn73/8uZ599towfP16uu+466dixo9x0002ydq1mKFExG+yiJ6DIG7RcezJaJr40oOcNDm7cVtjMRGSmTL/X11aLL03FkwKHe4nsvHFbbgNdbXvBeTHHnjM3i8g6EWnseE+RfVbbQDeyXVaLuJbr+bSD431E9gnOiXllbvWDdrlcfJPdpVIL7kU+IrsnHpetdLCrpcvBFEM6IvOmTZvM9+edd54Zkfmhhx5K+k5mpo3hMryd5dkMry70CCOVNtmgtiDO6aLQtl2CXaRSsb1503Nj2Q7AHHsu1c5Dgl2k2vo4z/J59vq+XnwpJahAigUl9bEe78i3z5brObWug30Ddu2LL76Q//73v+b7Ll26SPfu3SVlwW7z5s1NBrddu3bStm1bmTFjhhx44IGyYMECMy0RKkpPQvF4FXgPsCfQLpGOtM2FHu6ILShpBlItGG8jFs+2W+6NkGpBm4vVNmmXCaGMOSV+/vlnUz38ySefSP36OiaHyPr1602i9YUXXpDWrVtX+jMr3dV4/PHHyz//+U/zvT6vO3ToUDnhhBPkzDPPlNNPP73SO5C9goxtrJPNDtsPQfYMLtplbpzAoSQ8YjiQWvl20XNjWcFNGwMBwYU6EZ0xsc6Zer4kq4tUq2GD2lid0zvsdV7fA6SXSy65RIqLi01WV5Oruuj3paWl5rWUZHb1eV3doNIBqRo1aiSffvqp/OY3v5E//vGPCe1Edmpgn0HTstDqEaV4JXZpxlQvcEAvfjq599qIMjyl/80X2ZInvbkDUkdLQPVZcZFFtqS5WkR2YrsNhBnQDy40toMAbbfX8qBdFtvvmzD2Bhyob6/nWyPuMYNAd4e9x2QEe6SfadOmmbhy3333Da/T7x988EH51a9+ldBnVjqaysnJMUvgrLPOMgsqxzNTuXS0IzJrwBvIsRfPlg73DtlKb8p8aWcvhvr8bjC1mGczZ+25cYMjTW1AsarMObO6bZdUwiD1PDPFUDvbEVNY5vZKp3ihEwappyPT/3KPuS2i8iDHtsnKl4JmPS8ntLjcfhZo06aNyeyWVVJSIi1bJhYbJZQ61NrpmTNnysqVK8NZ3sD555+f0I5kI51/z5cudoALPRnl2KyaTvpNQAGXHTH72gF/NtuLZC3TU8wgK3DbEdPGzA8ZapslNtDVUeuDKbKA1POkkfim4mWd7SCsZs+XlInCHZ3yypf97D2mZni9cHUW95hIV3fffbdceeWVMnbsWDn00EPDg1VdddVV8n//93+pCXbffPNNOffcc2Xz5s1St27dqIl/9XuC3coJlSozgijSS+hCqGVQocEBgPRpl7XsAqSPUEloM9e7AUQJzT3eyC7YLWbQdZcDVEnGatCgQVQ8uWXLFjniiCOkWrVQmLpjxw7z/UUXXZTQPLyVDnavvvpqs7Hbb79datZkoBoAAAAAQOXdf//9sidVOthdsmSJ/PnPfybQBQAAAAAkbODAgZJWwW6/fv1M7XTHjvrgOwAAAABkMAaoSrnCwkIpKgoGSg3RR2j3eLDbv39/ufbaa2XOnDnStWtXycuLHhhEpyACAAAAAKCi9Hnd4cOHy0svvSRr1ui0bjuPyrzHg91BgwaZr6NHj97pNX24OJGdAAAAAABkr+uuu04+/PBDeeSRR+S8884zozLrI7SPPfaY3HnnnQl9ZqWD3bJTDQEAAABAxtLRgp2OxpzBwzGXmfXnmWeekV69esmFF14ov/rVr2SvvfaSdu3ayfPPP29mBKqs7CsABwAAAACklbVr14bHhdLnc/Vndcwxx8hHH3205zK7DzzwgFx66aVSUFBgvi+PjtQMAAAAABmBzG5KaKC7YMECadu2rXTu3Nk8u3v44YebjG/9+vX3XLB73333mbSxBrv6fTz6zC7BLgAAAACgMrR0+T//+Y8ce+yx8pe//EVOOeUUeeihh6S4uFjuvfde2WPBrkbYsb4HAAAAAGB3DR06NPx9nz595L///a98+eWX5rndbt26pWaAKgAAAADIHlpG7LKUODvKmMtq3769WXZHhYLdYcOGVfgDE00xAwAAAACy15QpU8xjs5rVVfvtt58MGTLEZHr3WLA7e/bsqJ81nbxjxw7Zd999zc//+9//JDc3V7p3757QTgAAAAAAstfDDz8sV111lfz2t781X9WMGTPk5JNPNgHw4MGD90ywq5P7RmZu69SpI08//bQ0aNDArFu3bl14LiQAAAAAyBheTmhxuf0scPvtt5ug9oorrgiv08GPjz76aPNaIsFupY/cPffcI3fccUc40FX6/a233mpeAwAAAAC4oXPS6kjGLVu2NLPlvP7667v8nalTp8ohhxwi1atXNwNCjR8/fqf3jB071jxDqzP0HHHEETJz5syo1wsLC01A2qhRI6ldu7YMGDBAVqxYUeH9Xr9+vZx44ok7re/bt69s2LBBElHpYHfjxo2yatWqndbruk2bNiW0EwAAAACQ1vPsulwqYcuWLXLggQea4LQidLad/v37y3HHHSdfffWVeUb2kksukXfffTf8nhdffNGM4zRy5EjzSKt+fr9+/WTlypVRoynrnLgvv/yyTJs2TZYuXSpnnHFGhff7N7/5jbz22ms7rX/jjTfk17/+taRkNObTTz/dlCxrFlcn+VWfffaZXHvttZX6xwAAAAAAkuukk04yS0U9+uij0qFDh3CVrg4K9fHHH5uSYg1og0dZBw0aZOLA4Hfeeusteeqpp8ycuJp5ffLJJ2XChAly/PHHm/eMGzfOfJY+d3vkkUfG3PYDDzwQ/r5Lly5y2223mSxzjx49zDr93U8++USuvvpqSUmwq/+wa665Rs455xwzwa/5kGrV5OKLL5a77747oZ0AAAAAAJRfYRtJS4512V3Tp0/fabRjDXI1w6uKiopk1qxZMmLEiPDrOTk55nf0d5W+rrFh5Od07txZ2rZta94TL9jVgDqSPh47Z84cswTq169vguobbrhhzwa7JSUl8sUXX5iIWwPb+fPnm/WdOnWSWrVqVXrjAAAAAJDOfM8zi8vtqzZt2kSt15LiUaNG7fbnL1++XJo1axa1Tn/W4Hrbtm1mMGKNA2O95/vvvw9/Rn5+vglMy75HXyuvhHpPqlSwq9ML6QPCOu+Rprq7deu25/YMAAAAAGAsXrxY6tatG/45GVnddOX7vvmqA2ztjkoPUHXAAQfIjz/+uFsbBQAAAABUnAa6kUuygt3mzZvvNGqy/qzbqFGjhjRu3NgkPWO9R383+Awtd9YRleO9pyKeeeYZ6dq1q9muLppcffbZZ1MX7OoUQ/rM7qRJk2TZsmUmvR25AAAAAECm8P0c58ue1KNHD5kyZUrUusmTJ4cHidLy5O7du0e9p7S01PwcvEdfz8vLi3rP3LlzZdGiReH37IoOgnX55ZfLySefLC+99JJZdCqiyy67bKdne/fYAFW68WBo6Mi0sqaa9Wet5wYAAAAApN7mzZtl3rx5Uc/F6pRCDRs2NANG6UBTS5YsMVlUpcHkQw89JNddd51cdNFF8sEHH5hAU0dbDui0QwMHDpRDDz3UzMhz//33mymOgtGZ69WrZwYs1vfpdjQrfOWVV5pAN97gVGU9+OCD8sgjj8j5558fXqcx5/7772+eTdapjfZ4sPvhhx9WeiMAAAAAUBX5kmMWl9uvDB1QWOfMDWgAqjRYHT9+vKnO1YxrQMdi0sBWg8kxY8ZI69at5YknnghPO6TOPPNMWbVqldx0001mwKmDDjpI3nnnnahBqzT7qqM0DxgwQLZv325+/+GHH5aK0v066qijdlqv6/S1RFQ62D322GMT2hAAAAAAYM/q1atXeICnWDTgjfU7s2fPlvJcccUVZomnoKBAxo4da5ZE7LXXXiajfP3110etf/HFF2XvvfdOTbCr9MFjnTRYR2VWmlrWlLemrwEAAAAAqIybb77ZZJA/+ugjOfroo826Tz75xDwHrEFwIiqdj9e0uM6rq2nqtWvXmkUfJtZ1X375ZUI7AQAAAABpyffcL1lgwIABMnPmTDP68+uvv24W/V7XnX766anJ7Gottz4o/Pjjj0u1aqFf37Fjh1xyySUyZMgQE4kDAAAAAFARxcXF8sc//lFuvPFGee655yRZEsrsDh8+PBzoKv1eR+/S1wAAAAAAqCidtujVV1+VZKt0sKvDSEeO3hVYvHix1KlTJ1n7BQAAAABpMxqzyyUbnHbaaaZ0OZkqXcasDw3rHEr/93//Fx4aWh8cvvbaa+Xss89O6s4BAAAAADLf3nvvLaNHjzaxZffu3aVWrVpRr//5z3/e88GuBrme55nJfvVZ3SDtfPnll8udd95Z6R0AAAAAgHTl+55ZXG4/Gzz55JNSv359mTVrllkiafyZkmA3Pz/fTDZ8xx13yPz58806HYm5Zs2ald44AAAAAAALFixI+mcmNM+u0uC2QYMG4e8BAAAAAKisGTNmyJtvvilFRUXSu3dvOfHEEyUZKv20c2lpqamlrlevnrRr184smm6+5ZZbzGsAAAAAkCl88RwPUJXZZcyvvPKKHH300aZ6+IknnpD+/fubR2edBLt//etf5aGHHjLP586ePdsst99+uzz44INmXiQAAAAAACpCH48dNGiQbNiwQdatWye33nqriS+dlDE//fTTJuL+zW9+E17XrVs3adWqlfzpT3+S2267LSk7BgAAAADIbHPnzpUXX3xRcnNzzc9XX3213HTTTbJy5Upp2rRpaoPdtWvXSufOnXdar+v0NVSOL76IbBSRbTbRXk88qe56t5DlQu1yq4hstmt06Pda4mV4GQ3Sny9FIrJBREpEzLlSz5nZMf8g0pdv2qO2yyJ7a6XtMs/1biHLha7lm+z1XM+TdcSTGq53q0pyPddtps+zu3XrVqlbt27UgMgFBQWyefPm1Ae7Bx54oCljfuCBB6LW6zp9DRXnmwB3gT0J+XapJr7oH7UVgQUc3rQtFJF1NqBQepKtK7504AYODm/aVojIMhEp1kkI7Cs1bLuMnosPSBXfBLl6ztwesTZPfGktnjR2uGfIZr5pjwtsp3Uwpk6u+KZNtqGTEGlHK4dr164d/lmnuB0/frw0bvzLeTQlUw/ddddd5qHh999/X3r06GHWTZ8+XRYvXixvv/12pXcguwOK+TbQzTcnoFCwW2xv5vRP09z1biIrLRKR1eZmLbQoba/rzYXTl73piIEDa0TkZxvkFtivpfYcOl986SyeOZcCLjqt9dqt7S/HXss1w7tQfMkTT+q53k1kGd+cG+fbQDfyHnOHiKy0P7d2vZtVcIAqh/PsZvh9V9u2beXxxx+PWte8eXN59tlnUz/P7rHHHiv/+9//ZOzYsfL999+bdWeccYZ5Xrdly5aV3oHstc7epGkZXtC75tmTkvbGrRRfmohnTkhAavhSaNtmtTKnh+B7LbnfIiK/9LwBqcnq6g2afo18zCPHBr7abvUxGjoIkWqrbWAbdMBEXssLbTUCwS5SbYO9VgeBbtAu8+x5dLX40ly8xGcgBZLqp59+kj0loVauQS0DUe2u4FnInDh/Fr14ao8xQQVSaYvt+dUbt7JybfaCYBepFpwPY12yPLvoc2kEu0i1jfY6Xjbr4tn2usVUctFxjdTaYoPa3HLuMfU9dMQg81W4YP+HH36Qs88+WzZu1BN7NB0m+pxzzpEff/wx2fuX5Yc+s0sWACA59KYOcKEi12mu5Ug12uWeKmN2uWAPB7t33323tGnTJmqkrEC9evXMa/oeVFTtiGfOyiq2pXqMmAcX7bKabYNlldhe4joO9gvZTUvxatqqg7KCcygZCrhQz7bBsh0uwfORdRkICI6u5TkRg0xGCp4vZ1A/ZIcKn4GnTZsmv/vd7+K+/vvf/14++OCDZO1XFqhvT0bb7QUxGI15uw2C9VkKLpBIrdC0V03sBbIool0W26UBnTBIudCAaFqinGOfgwyCixJ7ztRAuKHr3URW0lFCq9t2GQQWpbZdasfh7k2ZASSmrl2KytxjBtf1ppTWI2tU+JndRYsWlTvPkQ4LrSMyo2I0kPWlkz6SbZ81K44YPEBv6piuAK60sl9X2Ru44FShQXBbRmKGE540EF/aiciSiBu20JRYIu0YaAXOOgh92ctey7fZthmMGK5TD1EJg9TT67ROyRaaEmtDRLVWcI/J+AaV5fs5ZnG5fSSmwncHWqo8f/58addObzZ2Nm/evJglzohPp8nQaVxCozJvC9+4cdMGl0IVBW3sfM/BQGq1xIs5aBWQOjpnqW+qCzbaLFqBbZt0wMAdnePZly6241qDXb2GU74MtzwT2O4lvrnH1EXbYx27HkhfGm+OGzfOfB0zZoxJtv7rX/8y0xPtv//+lf68Cp+Je/bsKQ8++GDc1x944AH51a9+VekdyHZ6k6YXSr2J86QhgS7SKmPhSSO7EOgiPWjpnWZ5Q+fM2gS6SKNreV3bLusT6CJteFIz4h6TQDdxOWmwZL5p06ZJ165d5bPPPpOJEyfK5s2hpMt//vMfGTlyZEKfWeEjN2LECBNV//a3v5WZM2eaEZh10Z0ZMGCAvPvuu+Y9AAAAAABUxl/+8he59dZbZfLkyZKfrwOphRx//PEyY8YMSUSF04gHH3ywvPLKK3LRRRfJa6+9FvVao0aN5KWXXpJDDjkkoZ0AAAAAAGSvb775RiZMmLDTei1lXr16dUKfWama2V//+teycOFCeeedd8wzur7vyz777CN9+/aVmjV1NEwAAAAAyBy+75nF5fazQf369WXZsmXSoYMOsPaL2bNnS6tWwQCqlVPpB0Rr1Kghp59+ekIbAwAAAACgrLPOOkuGDx8uL7/8snieJ6WlpfLJJ5/INddcI+eff74kIjuedgYAAAAApK3bb79dOnfuLG3atDGDU3Xp0sUMknzUUUfJDTfckNBnMvQvAAAAAMThS45ZXG4/G+Tn58vjjz8uN954o3z77bcm4NVxo/beW6dqlT0b7C5dulRatmyZ8IYAAAAAACiPzqmrSzJUONjVSXzHjh0r55xzTlI2DAAAAADpzhfPLC63n6mGDRtW4ffee++9ey7Yve222+SPf/yjmXbosccek4YNG1Z6YwAAAAAABCMtV4QOWJWICge7f/rTn+Skk06Siy++2DwsrPXUp5xySkIbBQAAAABktw8//HCPfn6lBqjSOY8++OADeeihh+SMM86Q/fbbT6pVi/6IL7/8Mtn7CAAAAABOMEBV1VXp0ZgXLlwoEydOlAYNGsipp566U7ALAAAAAMCuaAJ1/PjxUrduXfN9eTQGraxKRapaunz11VdLnz595LvvvpMmTZpUeoMAAAAAANSrVy/8PK4GvIk+m7vbwe6JJ54oM2fONCXM559/flJ3AgAAAADSke/nmMXl9jPVuHHjwt9rhjfZKhzslpSUyNdffy2tW7dO+k4AAAAAALJPaWmp3H333fLPf/5TioqKpHfv3jJy5EipUaPGbn92hbsJJk+eTKALAAAAIAt5DpfMdtttt8n1118vtWvXllatWsmYMWNk8ODBSfnszM2JAwAAAADS2jPPPCMPP/ywvPvuu/L666/Lm2++Kc8//7zJ+O4ugl0AAAAAgBOLFi2Sk08+OfyzDoasA1UtXbp0tz+beYMAAAAAIA7f98zicvuZbMeOHVJQUBC1Li8vT4qLi3f7swl2AQAAAABO+L4vF1xwgVSvXj28rrCwUC677DKpVavWbs2z67SM+ZFHHpFu3bqZOZV06dGjh/zrX/+K+kfqw8mNGjUyDywPGDBAVqxYsVPau3///lKzZk1p2rSpXHvttaZ3AAAAAACQ3gYOHGjiOJ1zN1j+8Ic/SMuWLaPWVbnMro7ufOedd8ree+9tIvqnn35aTj31VJk9e7bsv//+MnToUHnrrbfk5ZdfNv/AK664Qs444wz55JNPwtMhaaDbvHlz+fTTT2XZsmVmDmBNe99+++0u/2kAAAAAMoAvOWZxuf1MNi5irt1kcxrsnnLKKTsNO63Z3hkzZphA+Mknn5QJEybI8ccfHz4Q++23n3n9yCOPlPfee0/mzJkj77//vjRr1kwOOuggueWWW2T48OEyatQoyc/Pd/QvAwAAAAC4lDbdBJqlfeGFF2TLli2mnHnWrFnmoWQdjSvQuXNnadu2rUyfPt38rF+7du1qAt1Av379ZOPGjfLdd985+XcAAAAAyBy+n+N8QRUdoOqbb74xwa0+n6vP5b722mvSpUsX+eqrr0xmtn79+lHv18B2+fLl5nv9GhnoBq8Hr8Wzfft2swQ0OFY6l1My5nPKZnr8tCSd47j7OJbJw7FMHo5lcnE8k4djmTwcy+ThWIZk+78fWRzs7rvvviaw3bBhg7zyyivmAeVp06bt0W3ecccdcvPNN++0ftWqVVJUVLRHt50NJzP9W+qJPSeHXqjdwbFMHo5l8nAsk4vjmTwcy+ThWCYPxzJk06ZNrncBWcp5sKvZ27322st83717d/n8889lzJgxcuaZZ5rAc/369VHZXR2NWQekUvp15syZUZ8XjNYcvCeWESNGyLBhw6Iyu23atJEmTZrslElG5U/qOgm0HstsPqknA8cyeTiWycOxTC6OZ/JwLJOHY5k8HMuQsnOoVj2e+OJyrtvMnmc3o4PdWCcFLTHWwFdHVZ4yZYqZckjNnTvXTDWkZc9Kv+qgVitXrjTDVavJkyebaYy0FDoencMpch6ngJ6EsvlElCx6UudYJgfHMnk4lsnDsUwujmfycCyTh2OZPBzL0D02kHXBrmZYTzrpJDPolJY36MjLU6dOlXfffddMNXTxxRebDGzDhg1NAHvllVeaAFdHYlZ9+/Y1Qe15550nd911l3lO94YbbjBz88YKZgEAAAAA2cFpsKsZWZ0XV+fH1eC2W7duJtA94YQTzOv33Xef6QnSzK5me3Wk5Ycffjj8+7m5uTJp0iS5/PLLTRBcq1Yt88zv6NGjHf6rAAAAAGQOzUy7zE6TGa+Swa7Oo7ur+v6xY8eaJZ527drJ22+/vQf2DgAAAABQVaXdM7sAAAAAkC5KJccsLrePxHDkAAAAAAAZh2AXAAAAAJBxKGMGAAAAgDh83zOLy+0jMWR2AQAAAAAZh2AXAAAAAJBxKGN2zJftIrJWRLbZvod6ZvHoh4BDvpSIyDoR2WR+EqktIg3F45QBh3zTFjeKyHoR00YLbLvUr4A7vmy11/Iie2vVwJw3PaH0EO74pj3qtXyLvcesKyL1ucdMCPPsVlXcuTrkywYRWWAvjoFVJtj1pSOBBRx2wMwTMTdvGlyo1SKyQnzpJJ7UdLyHyEa+lIrITzag0O8D2i7biieNHO4dspkvK0RkiYjsiFi7UkSaii9tCHjhhG86q38UMdf0yHvMOvZanudw74DUIZpy2tumgW6xzU4EF8MSm7VYKiJtHe8lsjNz9pPtBa4e0ZPo2+qDBeLLfvQKw4EVttMlz7bNoF3qjdxC8aWmeFLD8T4i2/im0uBn+1NwLfdt4KttVttkE8d7iWzjm/a3wJ4fI+8xtaNQ2+xiEenoeC+rllLfN4vL7SMx3LE6o2UlRfamLbLXN9cua8Q3gTCQShrkbrIBReTpwbNtdau9UAKpzuqusm2yWox2qTd2axzuIbLXGttJnR9xLdevQdZsle1EBFJJkyaFMe4xg3PoelvFBWQ+gl1nNEumYpU3VbMXT05ESDW9OJbaDpd4pwt9D5BK2jFYHKddenbRjhgg1YJnIWPJjTinAqkUXKdzyrnH5FqO7EAZszOxbtoCpfbmjb4IuGiXQRle2Y4Y3y60S7hsl7Hoei5ncKHaLtpl0HaBVIp8BKls++MeMxHBHZDL7SMxtHRn6tnDHzmgRdCcNYOhgwDx/BlSrY4tv4tVQl9sb+y07QKpExpIpa49X5a95JfYG7f6jvYO2a2BbZNls7e+bZsNGOMADtSzHS3x7jH1Od5ajvYNSC3OwE6Diob2pLPdXhR32LISDShaMoIjUi40AnhL+1OhbZdBuZPezDUTLzw4EJBKLexzkYX2XFliy5uL7I0dwS5caGSnZttu22KJva4X2oCimesdRFbShEnjiEfignvM7fbWX+8xCQGQHaj7ckQDWV/a28EDVtuLo2ezFy3EI3sGZ3Tk0Bw7kmih7QkObtoYVRRueFJLfNnLjlQfzP+sl7Cm9pzJjRvcdBD6sredemidDShybGd2K+aAhsN7zDa2g3CV7YjxbMdMc/FMRQIqw/d9s7jcPhJDsOtQ6OaslfjSPKK3rToZXTgVan+NxTcZi1+CXYIJuOZJbRtYBBm0fOYjh3OhMvv24kur8OMengkyAHdC1+wW4puOar2We/Zazj0msgt3CWnAM89VaMkJkD5CF0SeG0c6tktK6ZGuQW8w5RCQTkEv95i7q9QPLS63j8SQqgEAAAAAZByCXQAAAABAxqGMGQAAAADi8O3/XG4fiSGzCwAAAAAZZuzYsdK+fXspKCiQI444QmbOnBn3vcXFxTJ69Gjp1KmTef+BBx4o77zzTtR79LM8z9tpGTx4cPg9vXr12un1yy67TFwh2AUAAACADPLiiy/KsGHDZOTIkfLll1+a4LVfv36ycuXKmO+/4YYb5LHHHpMHH3xQ5syZYwLU008/XWbPnh1+z+effy7Lli0LL5MnTzbrf/e730V91qBBg6Led9ddd4krBLsAAAAAsIvRmF0ulXXvvfeaoPPCCy+ULl26yKOPPio1a9aUp556Kub7n332Wbn++uvl5JNPlo4dO8rll19uvr/nnnvC72nSpIk0b948vEyaNMlkgo899tioz9LtRL6vbt264grBLgAAAACkuY0bN0Yt27dvj/m+oqIimTVrlvTp0ye8Licnx/w8ffr0mL+jn6Xly5Fq1KghH3/8cdxtPPfcc3LRRReZUuVIzz//vDRu3FgOOOAAGTFihGzdulVcYYAqAAAAAEjzeXbbtGkTtV5LlEeNGrXT+1evXi0lJSXSrFmzqPX68/fffx9zG1rirNngnj17mmztlClTZOLEieZzYnn99ddl/fr1csEFF0StP+ecc6Rdu3bSsmVL+frrr2X48OEyd+5c81kuEOwCAAAAQJpbvHhxVElw9erVk/bZY8aMMWXPnTt3NplaDXi1BDpe2fOTTz4pJ510kglqI1166aXh77t27SotWrSQ3r17y/z5881nphplzAAAAACQ5jTQjVziBbtaQpybmysrVqyIWq8/6zO0sejzuJqt3bJliyxcuNBkgGvXrm2e3y1LX3///fflkksu2eU+6yjQat68eeICwS4AAAAAxOH7vvOlMvLz86V79+6mFDlQWlpqfu7Ro0e5v6vP7bZq1Up27Nghr776qpx66qk7vWfcuHHStGlT6d+//y735auvvjJfNcPrAmXMAAAAAJBBdNqhgQMHyqGHHiqHH3643H///SZrq6XJ6vzzzzdB7R133GF+/uyzz2TJkiVy0EEHma/6LLAGyNddd13U5+o6DXb1s6tViw4ltVR5woQJZhTnRo0amWd2hw4dap4D7tatm7hAsAsAAAAAGeTMM8+UVatWyU033STLly83Qew777wTHrRq0aJFZoTmQGFhoZlr98cffzTlyxqw6nRE9evXj/pcLV/W39VRmGNllPX1ILDWAbUGDBhgPtcVgl0AAAAAiKPULi63n4grrrjCLLFMnTo16medK3fOnDm7/My+ffvGLavW4HbatGmSTnhmFwAAAACQccjsAgAAAEA8CQwSleztIzFkdgEAAAAAGYdgFwAAAACQcShjBgAAAIByqoipYq6ayOwCAAAAADIOwS4AAAAAIONQxgwAAAAAGTbPLsjsAgAAAAAyEJldAAAAAIjDdzzPrtM5fqs4MrsAAAAAgIxDsAsAAAAAyDiUMQMAAABAeQNUOawkZoCqxJHZBQAAAABkHIJdAAAAAEDGoYwZAAAAAOLw7f9cbh+JIbMLAAAAAMg4ZHYBAAAAIA6d5tblVLdMs5s4MrsAAAAAgIxDsAsAAAAAyDiUMQMAAABAHDrHrtN5diljThiZXQAAAABAxiHYBQAAAABkHMqYHQrNmbVRRFaJyBYRyRWRhiLSWDzJd717yGK+bLPtcr1dU1dEmogntRzvGbKZLztsu1wrYr6vKSKNRKSBeOK53j1k9bV8jV0KRSTPtku9lut1HXDDN/eYq0Vkk81vNbDX8uqud63K8X3fLC63j8QQ7Dq1QkSWiEiJDXSLReRnc8H0ZW/xpMD1DiIL+eaiOF9EiuzFUYOIlSbA8KWDeOZiCaSWb86PP4jIZtsmc2xnzAYRaSa+tCbghaNA9ycbUIhtl0W2na4XXzqJx60WHPBNx+CiiHtMbatL7bV8L/FMZyGQ+Shjdpo505OOqiFiMrna06YB7jYbBAOp5UupiCy0N2sFtk3m2+/1grlIfPMVSLVlNoAIzpNBu8yxHYfaSQOk2job6FYr0y7zbUeMBhxAavmy3SZP/Ij2GJw7tfpgse2oATIfwa7TC+QOW+4UybMXzQ32ZAWk0iZ7IdQLY2SWzLPriiJKm4FUli+vsdmJspctPV+W2nMqkGraLjVoKJu9DdrqaoIKOKDX6eI41/I823GoiRVUVGkaLEgMwa4zGjSoWGV3ubZZ64kKSKVi2/Zyyzld0C6RajvKaZeeXegchAvby7mVyrHnS6phkGrcYwIBHiRxJsjo+jFORiURGV4g1e3SsxfCsjdwQXaCdolUq2bbY/DsmcRomwzqBxe03W2N81qpfZ1BqpBu95h6PuVaXhmlvm8Wl9tHYsjsOtMgYlCqSL7NYtRjgCo4UMc+Q669wpEnVt9mMPTGrb7D/UM2Cg3w09DepJUt5iqOGGUUSLVGNpjQ63akoPBQR2Rm4DSkmp4Pq0VkeCOv5XrO1JkVGKAK2YFg15HQKHgt7Imn0J6QttvvNcht5XoXkYU8c0poYy+SQbssst9r50wbRhaFIy3sDdp2uwTtUgPgpnZ6LMBFUNHIBruFNpAotG20rm2bQGqFphZqbTtitpW5lle313I6YZAduGt1fvNWo8w8u3rhbMocaHDGk3riyz5l5tkN5ubTzC+Qejr3+C/tco0NLjT4bWyCDW7c4KqD0Jf2IlLbjsq83V7XG9lzJrdZcMOTpuKbe8lVEfPsNrHtUtsoKkOriF1WElPFnDjOwg6Fbs40iGgQHq2RGzakA88EEbVol0grnnkOraVZtG3SLpE+FTGawdXggnaJ9Oq81sfiuJYjmxHspglOQEhHtEukK9om0hHtEumIdolsRrALAAAAAHFodtzlnNnM1504BqgCAAAAAGQcMrsAAAAAUM4AUaUMUFUlkdkFAAAAAGQcgl0AAAAAQMahjBkAAAAA4mCe3aqLzC4AAAAAIOMQ7AIAAAAAMg5lzAAAAAAQR6n4ZnG5fSSGzC4AAAAAIOOQ2QUAAACAOPzS0OJy+0gMmV0AAAAAQMYh2AUAAAAAZBzKmAEAAAAgDt/3zeJy+0gMmV0AAAAAQMYh2AUAAAAAZBzKmAEAAAAgjlI/tLjcPhJDZhcAAAAAkHHI7AIAAABAHKW+bxaX20diyOwCAAAAADIOwS4AAAAAIONQxgwAAAAAcfilujicZ7fU2aarPDK7AAAAAICMQ7ALAAAAAMg4lDEDAAAAQBxaReyykpgq5sSR2QUAAAAAZBwyuwAAAAAQhw5O5XaAKubZTRSZXQAAAABAxiHYBQAAAABkHMqYAQAAACCOUt83i8vtIzFkdgEAAAAAGYdgFwAAAACQcShjBgAAAIA4fN/xaMyUMSeMzC4AAAAAIOOQ2QUAAACAcpBbrZrI7AIAAAAAMg6Z3TTgS6mIFNm+hzzxxHO9S4D4pg9T26V+rU67RNrwpVhESuz5Mtf17gCGb9qkts1c8STP9e4AZe4x9Rqez7UcWYdg1/kJaIWIrLIXSD0B1RZfWogndVzvHrKYL2tt29xq1xSIL81EpBEXSjjjm/a4TEQ22E6YauJLYxFpTtALx50v2i71vKkBb474Ul/EXMsLXO8esrrDeqVdgmC3lvjmfFnP9e5VOaWlvllcbh+JoYzZ6UlooYj8bE9CwY3aehGZJ75sdLyHyFa+rBaRBSKy2Z4idNEg4ycRWe5695DVge4PIrLGrtFzpgYZS0TkR9t5CLjI5s6358YS2y5924n9g/iy3fUuImvvMReLyCIRKbTXcc92FM4X39xrAtmBYNeZzfamTZPr1e0FUsueCuwN3FJ7sgJSfeOmwUOpbYvV7FJgL5TLxTedM0CqaeZsu22LefacqefOfNtJyM0bXNDr+MaIthh5Ld9mK2SAVNtmO1xyY1zLQ9d57jGRLQh2nVlvA4qypXeevVBusb1xQCrpTVuRvWkrK892xGjPMJDqMtEN9matbBl9kEkj2IULa22bLHs75dm2uZaqAziw3ga11eLcY26z95moqFLfd74kYuzYsdK+fXspKCiQI444QmbOnBn3vcXFxTJ69Gjp1KmTef+BBx4o77zzTtR7Ro0aJZ7nRS2dO3eOek9hYaEMHjxYGjVqJLVr15YBAwbIihXuOv4Idp3Rk5Dy4vxZ/Ij3AKlSYtterHbp2YV2CVftMt4lS9vljhTvEyC23cUbx0DXa6BLBg2pFnSwcI+ZzV588UUZNmyYjBw5Ur788ksTvPbr109WrtTnuHd2ww03yGOPPSYPPvigzJkzRy677DI5/fTTZfbs2VHv23///WXZsmXh5eOPP456fejQofLmm2/Kyy+/LNOmTZOlS5fKGWecIa4Q7DoTDFrhx7l4BiV6QKrbpZ4WYmUignW0S6Ravs1QxApofbvUcLBfQM0450uxwYSeL7nVQqpVt4Eu95jJ4pe6Xyrr3nvvlUGDBsmFF14oXbp0kUcffVRq1qwpTz31VMz3P/vss3L99dfLySefLB07dpTLL7/cfH/PPfdEva9atWrSvHnz8NK4sQ4UGbJhwwZ58sknzbaPP/546d69u4wbN04+/fRTmTFjhrjAGdiZBvYGbnuZk1GJXRoydQEcqGVGBA+VMkeeWX3bVjWgYBRHpJZnLlVNbJuMzEYE02NpINzI4R4iezWygUMwTVsg6Jhpwgj2SKN7zFLbNusxUniGKyoqklmzZkmfPn3C63JycszP06dPj/k727dvN+XLkWrUqLFT5vaHH36Qli1bmoD43HPPlUWLdCC0EN2mlkNHblfLnNu2bRt3uxkd7N5xxx1y2GGHSZ06daRp06Zy2mmnydy5c6Pe06tXr51qwzWtHkkPcv/+/U1vhX7OtddeKzt2pHdJm2d61NrZm7RCu+gzFPpsWl0RaeV6F5GFQjdl7W22YntEu9SvegLsYAMPINV06quG9hwZtMlglNE24pk2C6SaXq9b2O8jr+XaKdPYdtIAqeWZe8sO9vncyHZZZDu027jeRSRo48aNUYsGqLGsXr1aSkpKpFkzvXb+Qn9evjz2zBpa4qwZWQ1mS0tLZfLkyTJx4kRTqhzQ537Hjx9vnuV95JFHZMGCBfKrX/1KNm3aZF7Xz87Pz5f69etXeLsZPc+u1nHrA8wa8Gpwqqnzvn37mjrxWrU0wxSiKXh9YDqgQW1A/5Aa6GoaXVPk+gc5//zzJS8vT26//XZJZ540EN9kynQ0x622d1izZg0IKOCM9vb60tkOvLLR9grXsXPsUm0AN3QeXV862oGq1tlgosC2SwJduOsg9E2wW9eeM7fbWyvtmKlLVhfOeFJXfNnPtstgKsHgHpN5ySur1Aw153CeXbvtNm2iOyr0eVwdNCoZxowZY2IuzcRqclEHqtIS6Miy55NOOin8fbdu3Uzw265dO3nppZfk4osvlnTkNNgtO8KX9hRoZlZT4D179owKbjWYjeW9994zwfH7779veg0OOuggueWWW2T48OHmj6+9C+ksVEZCFhfp2Cvc1C5Aegh1AjawC5AeQgFtbbsA6VZFGFQeIBMsXrxY6tbVzrWQ6tVjP3utz9Hm5ubuNAqy/hwvpmrSpIm8/vrrZjTlNWvWmFLlv/zlL6ZcOR7N4O6zzz4yb94887N+tpZQr1+/Piq7W952MzrYLUsfalYNG2qP6C+ef/55ee6558xBOuWUU+TGG28MZ3e1/rtr165RaXpNw+tD1d99950cfPDBO21HU/6RaX8tA1CastcFidPj5+sQ6RzH3caxTB6OZfJwLJOL45k8HMvk4VgmD8cyJNv//cmigW5ksBuPJvt0cKgpU6aYx0SDv4H+fMUVV5T7u/rcbqtWrcyzt6+++qr8/ve/j/vezZs3y/z58+W8884zP+s2tbpWt6NTDil9RFUfOe3Ro4dkdbCrf4AhQ4bI0UcfLQcccEB4/TnnnGPS49q78PXXX5uMrR40rSFXWv8dqx49eC3es8I333zzTutXrVpleiOwe39H7bTQE7s+CI/EcSyTh2OZPBzL5OJ4Jg/HMnk4lsnDsQwJnumsqvxS3ywut19ZOu3QwIED5dBDD5XDDz9c7r//ftmyZYspTVb62KcGtRoXqc8++0yWLFliqmT1q1bIavu97rrrwp95zTXXmMSjxmY6pZCWUWsG+eyzzzav16tXz5Qz67Y1eamB+ZVXXmkC3SOPPFKyOtjVZ3e//fbbnUb8uvTSS8Pfawa3RYsW0rt3b9OLoLXkiRgxYoT5I0RmdrUGXtP3ZR+oRuXofxRa56/HMptP6snAsUwejmXycCyTi+OZPBzL5OFYJg/HMqTsKL/Y884880yTyLvppptMAlCDWH2ENEgKarY1sk1q+bLOtfvjjz9K7dq1zbRDOh1RZGz0888/m8BWy5y1TR9zzDFmSiH9PnDfffeZz9XMrlbSasXtww8/LK6kRbCr6fRJkybJRx99JK1bty73vfogtNLacA12tbR55syZUe8J6tPj1YZrfXusGnf9w2TziShZ9KTOsUwOjmXycCyTh2OZXBzP5OFYJg/HMnk4lqF77KrM90OLy+0nGmNdEadseerUqVE/H3vssWYcpPK88MILFerYGDt2rFnSgdOWpyUd+gd47bXX5IMPPpAOHXSY9PJ99dVX5qtmeJWmxb/55htZuXJl+D06VLamzXUCZQAAAABA9qnmunR5woQJ8sYbb5i5doNnbLXeWycx1lJlfV3T6I0aNTLP7A4dOtSM1KzDXSudqkiDWn0w+q677jKfoSl4/ex4I5QBAAAAADKb02BXJyNWvXr1ilo/btw4ueCCC8xIYjqlUPBAtT5Xq/XfGswG9KFoLYHW0Zc1y6vz8+rD2JHz8gIAAABAIkp83ywut48qGOxqGXN5NLidNm3aLj9HRwR7++23k7hnAAAAAICqrGo/LQ4AAAAAQLqOxgwAAAAA6aiqjsYMMrsAAAAAgAxEZhcAAAAA4vBLfbO43D4SQ2YXAAAAAJBxCHYBAAAAABmHMmYAAAAAiKPU983icvtIDJldAAAAAEDGIdgFAAAAAGQcypgBAAAAIA7m2a26yOwCAAAAADIOmV0AAAAAiMN3PECVbh+JIbMLAAAAAMg4BLsAAAAAgIxDGTMAAAAAxME8u1UXmV0AAAAAQMYh2AUAAAAAZBzKmAEAAACgvHl2S91uH4khswsAAAAAyDhkdgEAAACgnHluXc51yzy7iSOzCwAAAADIOAS7AAAAAICMQxkzAAAAAMTBPLtVF5ldAAAAAEDGIdgFAAAAAGQcypgBAAAAoLx5dh1WElPFnDgyuwAAAACAjENmFwAAAADiKC31zeJy+0gMmV0AAAAAQMYhs5sGfNkmYhbte6gjnuS63iVAfCkWkc32p1riSb7jPQK0XZaKyCYRKRGRAhGpIZ54rncLWc4XzbpsEZEie2ul13LaJdzzpVBEttp7zNriceuPLEOLdx5MLBSRDfbGTS+M+eJLCxFpwoUSDm/alorIShHTRlU18aWRiLSmMwbO+LJWRJaImJs3bafaFuuIL23FM4EvkHq+CSQW2c7BUhtUFIgvbcSTeq53D1nKlx0islhE1omY7/WeMk98aS4izbjHrCS/1DeLy+0jMZQxO81OzBcxN2+hC6MGuqHgQi+aa1zvIrLWEhvsltp2GQQRK2zbBFLPl/UissAGuvm2Xeq5U9fPtzd2QGr5sl1E5onIRps/KLBftVrrR/HD1TFAqjutfxSRVXZNcI8ZBMDamQ1kB4JdZzbYUrx8e2H07J+jus1YLLcBMZA6vinBW2nbYr5tl6He4FA7XWuzGECqb9yW2wqY6rZ9erZNVrflo5q9AFJtje2AKbCVBp79Wt12XhNUwIVNtgMm316/I+8xPXuPqedTIPNRxuz0RBSU4ZWVZy+e2jNcy8G+Ibvbpfb8FsQ5XRTa99R0sG/IXkU2oA1u2iIFga92IDZxtH/IXusjOgUjBUHvBtNx7ZFbQErpdbq0nHvM4Jxa18G+VU2+75vF5faRGM6+zlQka0vDRqpVpM3RLpGOaJdw1e7iPfsYrKdtItW4lgMBMrvO1LblTcFgFpF22J63Go72Ddmrlu0JDtpgpKCtUm2AVAue0d0a47Ll20XPqUCq1bEZslhBr55H65NXgAM1bXuMd4+p51EqtCrDL3U7SJRuH4nhDOxMfRvM6uAWQQv27TM++rUpo94i5TzTJhvYi6EuwYm9xJY9ackTQQVSKzRqaFP7U1FEuyy1pfX6HJqOFg6kWmPbMbg9ol369me9xWJmBbi6x6xp22FJmXtM/bmxeDt1aAOZiWDXkdA8Z53syajIPp8bTKfRTMQMDQ+40FZEGtoLYvDs+A4b6Lbnxg0Og4qWNltRaJcim/HtyDzQcMIz1/AOtvqgMGLJtedSDTqA1Ao9I97Jdk4Xl7nH1LENWrneRSBlKGN2fJH0pYsdWEVPRHpyqmeza4C7jhjfXCS32EEuxJYu1yHQhTOhttfKzvcczE2uGd36VMHAKU/qi2+CivW2A6aabZd0wMAdnXvcl852VOat9h5Ty+5rci1PQKnvm8Xl9pEYgt206H1rYBcgPYQuhHrzRsky0u8GLvZo4YDrai2tPgDS7R5TqwuoMED2oowZAAAAAJBxyOwCAAAAQBxaReyykpgq5sSR2QUAAAAAZBwyuwAAAAAQh1/im8Xl9pEYMrsAAAAAgIxDsAsAAAAAyDiUMQMAAABAHL7jeXZ1+0gMmV0AAAAAQMYh2AUAAAAAZBzKmAEAAACgPFQSV0lkdgEAAAAAGYfMLgAAAADEUVpaahaX20diyOwCAAAAADIOwS4AAAAAIONQxgwAAAAA5cxz63KuW+bZTRyZXQAAAABAxiHYBQAAAABkHMqYAQAAACAOHQzZ5YDIDMacODK7AAAAAICMQ2YXAAAAAOLwS32zuNw+EkNmFwAAAACQcQh2AQAAAAAZhzJmAAAAAIiDeXarLjK7AAAAAICMQ7ALAAAAAMg4lDEDAAAAQBxaReyykpgq5sSR2QUAAAAAZBwyuwAAAAAQR2mpbxaX20diyOwCAAAAADIOwS4AAAAAIONQxgwAAAAAcfilvllcbh+JIbMLAAAAABlm7Nix0r59eykoKJAjjjhCZs6cGfe9xcXFMnr0aOnUqZN5/4EHHijvvPNO1HvuuOMOOeyww6ROnTrStGlTOe2002Tu3LlR7+nVq5d4nhe1XHbZZeIKwS4AAAAAZJAXX3xRhg0bJiNHjpQvv/zSBK/9+vWTlStXxnz/DTfcII899pg8+OCDMmfOHBOgnn766TJ79uzwe6ZNmyaDBw+WGTNmyOTJk02A3LdvX9myZUvUZw0aNEiWLVsWXu666y5xhTJmAAAAAIjD932zuNx+Zd17770m6LzwwgvNz48++qi89dZb8tRTT8lf/vKXnd7/7LPPyl//+lc5+eSTzc+XX365vP/++3LPPffIc889Z9aVzfSOHz/eZHhnzZolPXv2DK+vWbOmNG/eXNIBmV0AAAAASHMbN26MWrZv3x7zfUVFRSYA7dOnT3hdTk6O+Xn69Okxf0c/S8uXI9WoUUM+/vjjuPuzYcMG87Vhw4ZR659//nlp3LixHHDAATJixAjZunWruEJmFwAAAADSfJ7dNm3aRK3XEuVRo0bt9P7Vq1dLSUmJNGvWLGq9/vz999/H3IaWOGs2WDO0+tzulClTZOLEieZzYu9TqQwZMkSOPvpoE9QGzjnnHGnXrp20bNlSvv76axk+fLh5rlc/ywWCXQAAAABIc4sXL5a6deuGf65evXrSPnvMmDGm7Llz585mUCkNeLUEWsueY9Fnd7/99tudMr+XXnpp+PuuXbtKixYtpHfv3jJ//nzzmalGGbNjvpSIL2vElyXiyzLxZYv4wvDicEvboC8bxZeltm2up10iLfiyzZ4rtV2uFl92uN4lQHwpFl9WiS8/iy8rxJfYpYVAKvlSKr6stedLvZ5v5lpexWmgG7nEC3a1hDg3N1dWrFgRtV5/jvcsbZMmTeT11183g00tXLjQZIBr164tHTt23Om9V1xxhUyaNEk+/PBDad26dbn7rKNAq3nz5okLZHYd0sBW5EcRKQyvEckVkUbiS1vx6IuAA6HgQdvlRi1SsWu1LdYWXzqIJ8nrRQQqKnSDtkQv1SISlFR52q8tvrQXT37p6QZSSYMJkUX6lFzEWg0uWmrRoHimnQKp7xgMXcv1WUk/4lreUHxpJ56530SmDlCVn58v3bt3N6XIOj1QUHasP2ugWh59brdVq1ZmpOVXX31Vfv/730ftx5VXXimvvfaaTJ06VTp06LDLffnqq6/MV83wukCw6zyg0JNRdXsC0oas61eJSJ6ItHK9m8hKC0VkvZ4qbeeL2OBCg98F4su+3LzBAT0vLrPnSh1Aw7OdMZpB+1F82Y+OGDjqtP7JniODdqnX8mIR+dmeR6MHbgFSkdEN3WNuKXOPqe10tb39b+t6N7GH6bRDAwcOlEMPPVQOP/xwuf/++03WNhid+fzzzzdBrc6dqz777DNZsmSJHHTQQearPgusAfJ1110XVbo8YcIEeeONN8xcu8uXLzfr69WrZwaz0lJlfV1HdG7UqJF5Znfo0KHmOeBu3bo5OQ4Eu86ss4FucHEU+1WDXD1JaXlec3re4KAneL1th5FtL9fetG0WkU1aSONwL5GdWd1gXkBth4EceyOn1TGaXXPTa4xstsZ2Upe9lms71fOpljY3oIMQKbbeZnSDQFd59rZf7zH18bkW4plrPTLVmWeeKatWrZKbbrrJBKUaxOrUQcGgVYsWLTIjNAcKCwvNXLs//vijKV/WgFWnI6pfv374PY888oj52qtXr6htjRs3Ti644AKTUdbpioLAWgfUGjBggPlcVwh2nQkmX451AcyzvcJ6oayd4v1Cdttie35jXQBz7EVSL6AEu0ilIpvBjXXJ8uyiHTFAqm2y58ZY1/Jq9nyp5006rpFKQelyTpx2WWTfU8/BvlVNOhhyaanb7SfiiiuuiFu2rGXIkY499liZM2fObpVTa3A7bdo0SSc8FJqWhz5oSPQEIx1PCbRLpFoQ0O7qPUCqBWXLsXAthytBm/N345wKZAYyu87UiRhopWyPr2Z1a4hITUf7huxul0FlQWS5qNhSPW2rZHWRatoma+n09bYNRt6kldobOjIUcKGBrSrwy7TL4PlILWEmrwAX1/Ic2wbL3uoH13c9p6JyqV2HI1m73HYVxxnYmXo2aCiyJx7f3rQV2gumPktBrxtSK/T8TlPbFosiAokiG+w2Es90xACpEzoXNrc3bdvtDVwwoN92+7iHBh1AqjWyndOFtj0GQa7+rOfT0LNxQOqD3fr2/jLyHjOYEosxYZA9CHYdCfX06rxVje2aQhtQ6CAX7Rm9EQ61tCOB50Y8K5ljB/9h9Ea44ZkOwg42sNhhz5ml9lzZSTwKleCAZzJke9kO7KDDeoftgNF2SfYMrjoI29vOa7HX8SLbAdNGZ1R1vIdA6nB34DyL1lF8c3EsDM9lSskT3F8kW4pvLpJbbI9wLUZthHOeNBDfBBWbw1O9UGkA17QN+rKPHfAnCCj0nEl1FtwJdQC2N6MuhwY89ew9JhndROjATKVVaJ5d/IJgNw14JpurC5BuF0qeg0R6CXUG8tw40ksosNUsLplcpJfQ/OPMQY7sRQoRAAAAAJBxyOwCAAAAQBxaReyykpgq5sSR2QUAAAAAZBwyuwAAAAAQh19aahaX20diyOwCAAAAADIOwS4AAAAAIONQxgwAAAAA8WgVsctKYqqYE0ZmFwAAAACQcQh2AQAAAAAZhzJmAAAAACh3nl13k90yz27iyOwCAAAAADIOmV0AAAAAiMMv9c3icvtIDJldAAAAAEDGIdgFAAAAAGQcypgBAAAAIA7KmKsuMrsAAAAAgIxDsAsAAAAAyDiUMQMAAABAPFpG7LKUmDLmhJHZBQAAAABkHDK7AAAAABCP74cWl9tH1cvs3nHHHXLYYYdJnTp1pGnTpnLaaafJ3Llzo95TWFgogwcPlkaNGknt2rVlwIABsmLFiqj3LFq0SPr37y81a9Y0n3PttdfKjh07UvyvAQAAAACkC6fB7rRp00wgO2PGDJk8ebIUFxdL3759ZcuWLeH3DB06VN588015+eWXzfuXLl0qZ5xxRvj1kpISE+gWFRXJp59+Kk8//bSMHz9ebrrpJkf/KgAAAACAa07LmN95552onzVI1czsrFmzpGfPnrJhwwZ58sknZcKECXL88ceb94wbN072228/EyAfeeSR8t5778mcOXPk/fffl2bNmslBBx0kt9xyiwwfPlxGjRol+fn5jv51AAAAADKiirnU7faRAQNUaXCrGjZsaL5q0KvZ3j59+oTf07lzZ2nbtq1Mnz7d/Kxfu3btagLdQL9+/WTjxo3y3XffpfzfAAAAAABwL20GqCotLZUhQ4bI0UcfLQcccIBZt3z5cpOZrV+/ftR7NbDV14L3RAa6wevBa7Fs377dLAENjIN90AWJ0+Pn+z7HMQk4lsnDsUwejmVycTyTh2OZPBzL5OFYhmT7vx/upE2wq8/ufvvtt/Lxxx+nZGCsm2++eaf1q1atMs/+YvdOZpqh1xN7Tk5aFQ5UORzL5OFYJg/HMrk4nsnDsUwejmXycCxDNm3aJFWZX+qbxeX2UYWD3SuuuEImTZokH330kbRu3Tq8vnnz5ib4XL9+fVR2V0dj1teC98ycOTPq84LRmoP3lDVixAgZNmxYVGa3TZs20qRJk52yyKj8Sd3zPHMss/mkngwcy+ThWCYPxzK5OJ7Jw7FMHo5l8nAsQwoKClzvArKU02BXe7muvPJKee2112Tq1KnSoUOHqNe7d+8ueXl5MmXKFDPlkNKpiXSqoR49epif9ettt90mK1euNINbKR3ZuW7dutKlS5eY261evbpZytKTUDafiJJFT+ocy+TgWCYPxzJ5OJbJxfFMHo5l8nAsk4djGbrHrtKYZ7fKqua6dFlHWn7jjTfMXLvBM7b16tWTGjVqmK8XX3yxycLqoFUawGpwrAGujsSsdKoiDWrPO+88ueuuu8xn3HDDDeazYwW0AAAAAIDM5zTYfeSRR8zXXr16Ra3X6YUuuOAC8/19991neoM0s6uDSulIyw8//HD4vbm5uaYE+vLLLzdBcK1atWTgwIEyevToFP9rAAAAAADpwnkZc0Vq/MeOHWuWeNq1aydvv/12kvcOAAAAQNbTAaJcDhLFAFUJq+IF9AAAAAAA7IxgFwAAAACQcdJi6iEAAAAASEfMs1t1kdkFAAAAAGQcMrsAAAAAEAeZ3aqLzC4AAAAAIOOQ2XXMl20islpEtuqswSJSX0QaiGe+B9zwpVhE1orIBrumjog0FE+qO94zZDNfSkVkvYisE5EdIlJDRBqJJ7Vc7xqymC+acdksImtEpFBE8s11XK/nnniudw9ZzDftcY1tn5rfqmev5dz+I3vQ2h3yTTCxUMQEFsEFUW/iVosvncSTPMd7iOztgJknYr4G7VIDjJW2XdZ2vIfIRr6UiMh82wHj27ap368SX9qIJ01d7yKyNtBdKiLLRUwb1Xap6/T63lB8aS8eRXRwwDfX7Z9EpKjMtTy4x6TzulL0P2uXlcRUMSeMM7Ajvmy3ga5mJwoiFu0R3igiS1zvIrL2xu0nG+gWlFm0zf5ks2tAqi23N2p5NqMbtEttsz+LL1tc7yCykl6vl9lgImiX+jXXZtS0cgtwUZ0VJFPK3mNqlnex610EUoZg15m19iSkPWtemT+JJtzX2pMVkEp6EdxiL4iR7dKzbXVbRGkzkMqs7mp7fswt0y7zbaehnlOBVNOAVjsA8+IUzmnlASkZpNp620Ed7x5zgy1xBjIfZczO6ElIxXqeR2/miu17KGVGKm23N26x+sFyyrRdIFWKbUAb65Ll2UU7YoBU21pO3iA34pzKOBxIpcJy7jGr2Xa53WZ7USGlpaHF5faREDK7zgQ3bbF6fEvtCYqLI1ItN+KZs3gPrNAu4apdxrvYa7uk7xYuaId0vMxtcL7kVgupxj0mEOAM7Ew9e/i1PC+Sb7MYOrooPW5Itbq27KkoxkWyyN7YadsFUic0WF99m90t2y6DQYF09Fsg1Rrar2U7YnzbNnXkW0ZkRqrVs8GsnjNj3WPqc+WMYl8Zfqn7BYkh2HVGR7RtbE9EhfZrsf1eb+xacYFEyoWmvGppTw1BuwzaqGohnnlGEki15rYDsDCirHm77YTRQJdOGLgKdutElIXusG1S22lNEUYJhwNeuO2VxLjH1Ot8a+4xkTWo+3JETzK+tLO9aysjhobXC2dzpneBM540Ft+cGpbb59F82wPcLCKLAaT+5s2Xve3ItzpImnZz59tOQz1n0ncLNx2Evuxlz5drbFChbbGR7Rxkehe40tp2EK60Qa5nK2T0fKlVXEB2INh1KNSr1kx8aWJ73HKYWxdpwZP64ptMWTAieB69wHDOM52DHcU3AUWJbZcEuXDLM7dSrcWXFjbYzbXrAHdC1+wm4psOwaLwSMxcyxPjl/pmcbl9JIazcRoI3azR+4v0ErogUrKM9BMKJLh8IR0fA2HQH6TjtZx7TGQvusQBAAAAABmHrnEAAAAAiMP3fbO43D4SQ2YXAAAAAJBxyOwCAAAAQDyaWHU51y2J3YSR2QUAAAAAZByCXQAAAABAxqGMGQAAAADiKS0NLS63j4SQ2QUAAAAAZByCXQAAAABAxqGMGcD/t3cv4DbV+R/Hv8h93O/XgyOkcanjMhiUzJiHeZAudJFkdEG5zGR0QxpSMpiSJJepaWI8SaZEk0eGiMml5C6nUSFdBiEOzu//fH7/9n72Pjcb2zl7b+/X8+zOWXutvdY635a113f9vr/fAgAAQDZcuvOvvNw+zg8tuwAAAACAhEPLLgAAAABkw7n/f+Xl9nF+aNkFAAAAACQckl0AAAAAQMKhjBkAAAAAsqMBovJykCgGqDpvtOwCAAAAABIOyS4AAAAAIOFQxgwAAAAA2UnP41JibR/nhZZdAAAAAEDCoWUXAAAAALLh0p1/5eX2cX5o2QUAAACABDN16lSrVauWFSlSxFq2bGnr1q3LdtlTp07ZmDFjLDk52S/fpEkTW7JkyTmv88SJEzZw4EArV66c/exnP7MbbrjBvv76a8srJLsAAAAAkEDmzZtnw4YNs1GjRtmGDRt88tqpUyc7ePBglss/+uijNn36dHv22Wdt69atdu+999r1119vGzduPKd1Dh061P75z3/a/PnzbcWKFbZv3z7r0aOH5RWSXQAAAADIhnMuz1/n6s9//rP179/f+vbtaw0bNrQXXnjBihUrZrNmzcpy+VdeecUefvhh69y5s9WpU8fuu+8+//vEiRMjXufhw4dt5syZfrkOHTpYSkqKzZ4921avXm0ffvih5QWSXQAAAABIEGlpabZ+/Xrr2LFj8L38+fP76TVr1mT5mZMnT/rS5FBFixa1VatWRbxOzVc5dOgyDRo0sJo1a2a73YuNAap+ulsjR44c8f/TcP7S09Pthx9+8P9YiOWFIZbRQyyjh1hGF/GMHmIZPcQyeoilBa+x5XxaKGNBWtrxmNh+II4BhQsX9q+Mvv32Wztz5oxVqlQp7H1Nb9++PcttqBxZLbLt2rXz/XaXLVtmCxYs8OuJdJ0HDhywQoUKWenSpTMto3l5gWTXzL777jv/MykpKa93BQAAAEhISvxLlSpl8UKJW+XKlW32jDvzelf8YE81atQIe099Z0ePHh2V9U+ZMsWXKKslNl++fD7hVblydmXP8YJk18zKli3rf+7duzeu/gHGIt1x0j/EL774wkqWLJnXuxPXiGX0EMvoIZbRRTyjh1hGD7GMHmJpwRZdJbpVq1a1eKIW+dTUVF/CGwsxVBIaKqtWXSlfvrwVKFAg0yjImlbynpUKFSrYwoUL/WjKagjU/6sRI0b4/ruRrlM/FatDhw6Fte7mtN2LjWT3p3pzUaJ7KZ+IoklxJJbRQSyjh1hGD7GMLuIZPcQyeohl9BDL/7/OjkdKeDP2ZY2HFmkNDrVs2TLr3r17sKRe04MGDcrxs/pbq1Wr5vvevv7663bzzTdHvE7NL1iwoH9PjxySHTt2+AbFVq1aWV4g2QUAAACABKJHBPXp08eaNWtmLVq0sMmTJ9uxY8d8abLccccdPql98skn/fTatWvtq6++sqZNm/qfKo9WMjt8+PCI16kbGv369fPLqXJWN3juv/9+n+j+4he/yJM4kOwCAAAAQALp2bOnffPNNzZy5Eg/OJSS2CVLlgQHmFJra+igaSpf1rN29+zZ4/sH67FDehxRaDny2dYpkyZN8utVy65GeNbAV88//7zlFZLdn+rd1cE7u7p3RI5YRg+xjB5iGT3EMrqIZ/QQy+ghltFDLJGXBg0alG3Z8vvvvx823b59e9u6desFrTNQBj116lT/igX5XLyOAQ4AAAAAQDYu3Qd+AQAAAAASFskuAAAAACDhkOwCAAAAABJOXCS706ZNs8aNGwefUabhq995553gfI0G1rt3b/+w4uLFi9vVV1/tnwsVqlatWv5BzKGv8ePHB+dreO2M8/XS+gIWLFjgh9rWqGR6XyOQaZSyUOoCrRHKqlSpYkWLFrWOHTvarl27LFbESixDzZ07188PPLMrgFhGFss5c+Zkmp/xeXDEMvLjUg9CHzhwoI+VBhSpV6+eLV68OGwZDbqg7SnOLVu2tHXr1lmsiJVYXnPNNVku06VLl+AyHJeRH5d6vEP9+vV9nGrUqGFDhw71I2eG4rg8eyz13MgxY8ZYcnKyj1OTJk38SKIZxXIscyuesnTpUv+4kBIlSliFChX86Kqff/55pkFutH6dL+vWreu/k+IpnrESy/3799utt97qv3M0ku2QIUOy3N/58+dbgwYNfCwbNWqU6fsJQAYuDixatMi9/fbbbufOnW7Hjh3u4YcfdgULFnSffvqpn/+rX/3KNW/e3K1du9Z99tln7oknnnD58+d3GzZsCK4jKSnJjRkzxu3fvz/4Onr0aHD+Dz/8EDZPr4YNG7o+ffoEl1m+fLlbsGCB27p1q9u9e7ebPHmyK1CggFuyZElwmfHjx7tSpUq5hQsXuo8//th17drV1a5d2/34448uFsRKLANSU1NdtWrVXNu2bV23bt3C5hHLyGI5e/ZsV7JkybBlDhw4ELavxDKyWJ48edI1a9bMde7c2a1atcofn++//77btGlTcJm5c+e6QoUKuVmzZrktW7a4/v37u9KlS7uvv/7axYJYieV3330XNl/b1/lSx2sAx2VksXz11Vdd4cKF/U8dk0uXLnVVqlRxQ4cODS7DcRlZLIcPH+6qVq3q90Xbef75512RIkXCthPrscyteO7Zs8cfdw899JC/5lm/fr1r166du+qqq8KWKVasmBs2bJi/Nnr22WczXRfFejxjJZb6t/3AAw+4v/71r65p06Zu8ODBmfb1gw8+8PF9+umnfbwfffRRv6+bN2++6HEC4lVcJLtZKVOmjHvppZf878WLF3cvv/xy2PyyZcu6GTNmhJ2IJk2aFPH6dXGrewH//ve/c1xOJyqdbCQ9Pd1VrlzZTZgwITj/0KFD/gT32muvuViVV7E8ffq0a926td+2LkZCk11iGXkslTwoYcgOsYw8ltOmTXN16tRxaWlp2X6uRYsWbuDAgcHpM2fO+IvnJ5980sWqWDhfan0lSpQIXgByXEYeSx1vHTp0CFtOyUWbNm2C0xyXkcVSNwmee+65sOV69OjhbrvttriO5cWI5/z5891ll13m//7QxDBfvnzBc6RuHlx55ZVhn+vZs6fr1KlTXMczL2IZqn379lkmuzfffLPr0qVL2HstW7Z099xzzzn+hcClIy7KmEOdOXPGl70eO3bMl5pI69atbd68efb9999benq6n6/yLpXRhVJJSbly5eyqq66yCRMm2OnTp7PdzksvveRLSdq2bZvlfN0oWLZsme3YscPatWvn30tNTfXlLirFCyhVqpQv2VmzZo3FmryOpUrJKlasaP369cv0GWJ5brE8evSoJSUl+fLGbt262ZYtW4LziGXksVy0aJHfpsqY9YD0n//85zZu3Di/T5KWlmbr168Pi6XKzTRNLLM/X8rMmTOtV69ewZJSjsvIY6nt6LgLlH7u2bPHly527tzZT3NcRh7LkydPZurmodLwVatWxWUsL2Y8U1JS/N8+e/Zsv43Dhw/7rluKRcGCBf0yiklorKRTp07BWMVbPPMylpE4W7wBZMHFiU8++cTfXVP5hlqxVHIS8L///c/9+te/9ndwdedMJZ0q8wo1ceJEX4asUjm13qiEJrQELJRK6HRX76mnnso0Ty0P2g9tRy0QM2fODCsv0T7s27cv7DM33XSTvxsXK2IhlitXrvTly998842fztiySywjj+Xq1at92dPGjRt9ye1vf/tbv60vvvjCzyeWkceyfv36/t/1XXfd5T766CNffqc7+KNHj/bzv/rqK78PinmoBx980LdexIpYiGUolf9pe/oZwHF5brGcMmWKL1fUdrS9e++9NziP4zLyWN5yyy2+tFklq2ple/fdd13RokV9mW08xTK34qnvlIoVK/ptaF2tWrXy6w64/PLL3bhx48I+o/3QssePH4+beMZCLCNp2dU54O9//3vYe1OnTvXrBZC1uEl21Zdu165d/gJ0xIgRrnz58r7vhwwaNMifNN977z1ftqQLU52sdPLKjpJUnbROnDiRaZ5OJJqXsd+j6MtR+6HE4plnnvHb0Qkuni7e8jqWR44ccbVq1XKLFy8OvhevyW5exzIrKolKTk4OltcTy8hjqQu3GjVq+BL70IsYldtKvFy4xUIsQ919992uUaNGYe9xXEYeS33HVKpUyZdNat0aO0LHqfoICsdl5LE8ePCg/65Rn0slHfXq1XMDBgzw/XbjKZa5EU/1O9U5UX+7+qeuWLHCJ2HXXXed74aQSMluLMQyFMkucAkmuxnpBKELKHX014k0MJBA6Pyc+jBoeX1u+/btmeapb1T37t0j2o9+/fr5O36igQu0TiXCoTQIgQYdiFW5HUvFR8vrQiPwUr8VvfS79oNYXthxeeONN7pevXr534ll5LFUTLTeULopo/XoYkgvHaNvvPFG2DJ33HGHH1wpVuXlcak+umoJ0YB+oTguI4/lL3/5S/eHP/wh7L1XXnnFt0jqBizH5bkfl2r5/fLLL32ioX6nau2VeI3lxYinbphqwL5QqhjSMmvWrPHTGlwyY1Kmgaj0bz6e45kXsYwk2dVNrox9g0eOHOkaN258zn8jcKmIuz67Aeo3ob43x48f99PqCxGqQIECfpnsbNq0yX9GfUZDqR/Z8uXLs+xHmtN+SO3atf3Q9OrLG3DkyBFbu3ZtsO9HLMrtWGrI/M2bN/vPBV5du3a1a6+91v+ufqfE8vyPS/UHUnz1OBchlpHHsk2bNrZ79+6w9e7cudPHslChQv6lvlehsdSymiaWlu1jMrTt22+/Pex9jsvIY6ltZbUd0U1rjstzPy7Vb7datWq+X6UeI6OxDiReY3kx4pnTcRdYj2ISGiv517/+FYxVvMYzL2IZibPFG0AWXBxQSYlKPjQsu8pGNK1WQPW1Uclm3bp1/d1F9QfTXTiVF2t+oM+Fymd0J0zlJ2pN+Nvf/uYqVKjg7yxmpLtvGiUwtIwxQKU62qbWoSHftR2VqYSOyKdHaaivxptvvun3VeVSsfQojViJZUYZy5iFWEYWy8cff9z3H9I69DgDteiqJC9QgiXEMrJY7t27148YrLI1PYLirbfe8uVhf/rTn4LLqB+v+vXOmTPHnwd091+xPVt5+aUWy9BWSY3OmhWOy8hiOWrUKH9capRqPcJE21dXhdByb47LyGL54Ycfutdff92vQ6M0qwVYx1xo38lYj2VuxXPZsmX+M/qOUR9nfb9olGWNPKwS5dBHD6k8d9u2bb6kNqtHD8VyPGMllqJKF71SUlLcrbfe6n8P/S5X9w9dd2ofFG+dG3j0EJCzuEh2NViMTggaQEInEJWP6CQUoBOHHh2gi1KddFXOETpMvE4qGppdfSyUBFxxxRU+cc3Yz0flYNWrV/fPWMvKI4884k96WocGvtDgAjqJh1JJ1GOPPeb7V+nkrn3VRXOsiJVYRpLsEsvIYjlkyBBXs2ZNvx+KlZ4RG/r8PyGWkR+XunDRuhQnPYZo7NixmS6a9SzJQMzVl0sX0LEilmKpEj7dUw3dfiiOy8hieerUKd9PUAmu1qNSRvUzzTi4Dcfl2WOpQYL0WR1v5cqVc7179/b9SjOK5VjmZjx1g0WPWNTgTdqOSo+VZGXsU67nwmpfdM4MfZZ2PMQzlmKp82XGl/Yt1D/+8Q/f11z7q8c+hQ6mBSCzfPpPVi2+AAAAAADEq7jtswsAAAAAQHZIdgEAAAAACYdkFwAAAACQcEh2AQAAAAAJh2QXAAAAAJBwSHYBAAAAAAmHZBcAAAAAkHBIdgEAAAAACYdkFwCQUO68807r3r17cPqaa66xIUOG5Ok+AQCA3EeyCwCXuDNnzljr1q2tR48eYe8fPnzYatSoYY888kiOn9+9e7f17dvXqlevboULF7batWvbLbfcYh999JHFggULFtgTTzwR1XWOHj3amjZtGtV1AgCA6CLZBYBLXIECBWzOnDm2ZMkSe/XVV4Pv33///Va2bFkbNWpUtp9VQpuSkmI7d+606dOn29atW+2NN96wBg0a2O9///uLut+nTp2KaDn9DSVKlLio+wIAAGIPyS4AwOrVq2fjx4/3Ce7+/fvtzTfftLlz59rLL79shQoVyvIzzjlfMnz55ZfbypUrrUuXLpacnOxbPJUgax0Bmzdvtg4dOljRokWtXLlydvfdd9vRo0eD89PT023MmDHB1mGtQ8l3wOeff2758uWzefPmWfv27a1IkSI+MVer9LBhw6x06dJ+vcOHD/f7FSpjGXOtWrVs3Lhxdtddd/kkuGbNmvbiiy+GfeaPf/yjj0mxYsWsTp069thjjwWTa90YePzxx+3jjz/2+6SX3pNDhw7Z7373O6tQoYKVLFnS/81aDgAA5D6SXQCAp0S3SZMm1rt3b5+Mjhw50k9nZ9OmTbZlyxbfgps/f+avEyWgcuzYMevUqZOVKVPG/vOf/9j8+fPtvffes0GDBgWXnTJlik2cONGeeeYZ++STT/zyXbt2tV27doWtc8SIETZ48GDbtm2bX0afUaI5a9YsW7VqlX3//fe+Zfls9LlmzZrZxo0bbcCAAXbffffZjh07gvOVBGu9aqnWvs2YMcMmTZrk5/Xs2dP/zVdeeaW/MaCX3pObbrrJDh48aO+8846tX7/err76arvuuuv8fgEAgFzmAAD4ybZt29Qs6ho1auROnTqV47Lz5s3zy27YsCHH5V588UVXpkwZd/To0eB7b7/9tsufP787cOCAn65ataobO3Zs2OeaN2/uBgwY4H9PTU3125o8eXLYMlWqVHFPP/10cFr7XL16ddetW7fge+3bt3eDBw8OTiclJbnbb789OJ2enu4qVqzopk2blu3fMGHCBJeSkhKcHjVqlGvSpEnYMitXrnQlS5Z0J06cCHs/OTnZTZ8+PccYAQCA6Lsst5NrAEDsUgupSndTU1Ptyy+/9CW/2clYLpwdtcKqhbh48eLB99q0aeNLl9WaqtLmffv2+fdCaTpjCbBaY0MH0FKrasuWLYPvXXbZZX6Zs+1b48aNg7+rDLly5cq+RTZA5dJ/+ctf7LPPPvPl1qdPn/ZlyTnRvmpZlVOH+vHHH/16AABA7qKMGQDgrV692pfqvvXWW9aiRQvr169fjkmj+rTK9u3bc20fQxPmC1GwYMGwaSW8Sr5lzZo1dtttt1nnzp19LFTqrBGp09LSclynEt0qVar48u7QlxL6Bx98MCr7DQAAIkeyCwCw48eP+8Gm1Hf12muvtZkzZ9q6devshRdeyPYzGkSqYcOGvv9rIFEMpcGa5IorrvCtnuq7G/DBBx/4fr7169f3LaZVq1b174XStNafnVKlSvnkcu3atcH31AKrvrIXmvQnJSX5BFetxBqA67///W/YMhq0S4NjhVL/3AMHDvjW5bp164a9ypcvf0H7BAAAzh3JLgDAHnroId+KqxGZReXLGixKoxtrJOSsqDV09uzZ/rFDbdu2tcWLF9uePXv8AFNjx461bt26+eXUSqrRk/v06WOffvqpLV++3A+GpYGwKlWq5JdRy+dTTz3ly4fVEqqBqNQqqsGocqL52ueFCxf6FmYNNhVIss+Xktu9e/f60ahVfqxy5oyDXik+KvXWPn777bd28uRJ69ixo7Vq1cq6d+9u7777ro+bEmclzbHyzGEAAC4lJLsAcIlbsWKFTZ061Seu6q8bcM8991jr1q1zLGdWubMSObVe9u/f37fiahRljdI8efJkv4zWuXTpUj8icfPmze3GG2/0IxQ/99xzwfU88MAD/hFCGuW4UaNG/rFDixYt8olnTrS8kmYl0ko0NYry9ddff0Hx0P4PHTrUjxat1mslrHr0UKgbbrjBfvOb3/hWcD1m6LXXXvPJvxL+du3aWd++fX2Zd69evXyrcCCpBwAAuSefRqnKxe0BAAAAAHDR0bILAAAAAEg4JLsAAAAAgIRDsgsAAAAASDgkuwAAAACAhEOyCwAAAABIOCS7AAAAAICEQ7ILAAAAAEg4JLsAAAAAgIRDsgsAAAAASDgkuwAAAACAhEOyCwAAAABIOCS7AAAAAABLNP8HxbwpwNUxWd4AAAAASUVORK5CYII=", "text/plain": ["<Figure size 1000x800 with 2 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Visualize results for current site\n", "if not results_df.empty:\n", "    plt.figure(figsize=(10, 8))\n", "    \n", "    # Color by pile probability\n", "    scatter = plt.scatter(\n", "        results_df['x'], results_df['y'],\n", "        c=results_df['pile_probability'],\n", "        cmap='RdYlBu_r', s=30, alpha=0.7\n", "    )\n", "    \n", "    plt.title(f'DGCNN Pile Detection - {SITE_NAME}\\n{pile_count} piles detected')\n", "    plt.xlabel('X Coordinate')\n", "    plt.ylabel('Y Coordinate')\n", "    \n", "    # Add colorbar\n", "    cbar = plt.colorbar(scatter)\n", "    cbar.set_label('Pile Probability')\n", "    \n", "    plt.grid(True, alpha=0.3)\n", "    plt.tight_layout()\n", "    plt.savefig(f'dgcnn_{SITE_NAME}_results.png', dpi=300, bbox_inches='tight')\n", "    plt.show()\n", "else:\n", "    print(f\"No results to visualize for {SITE_NAME}\")\n"]}, {"cell_type": "markdown", "id": "dfafef67", "metadata": {"papermill": {"duration": 0.002534, "end_time": "2025-08-10T08:52:41.681242", "exception": false, "start_time": "2025-08-10T08:52:41.678708", "status": "completed"}, "tags": []}, "source": ["## Export Results\n"]}, {"cell_type": "code", "execution_count": 17, "id": "f0afd0b2", "metadata": {"execution": {"iopub.execute_input": "2025-08-10T08:52:41.686440Z", "iopub.status.busy": "2025-08-10T08:52:41.686224Z", "iopub.status.idle": "2025-08-10T08:52:41.690668Z", "shell.execute_reply": "2025-08-10T08:52:41.690465Z"}, "papermill": {"duration": 0.007799, "end_time": "2025-08-10T08:52:41.691424", "exception": false, "start_time": "2025-08-10T08:52:41.683625", "status": "completed"}, "tags": []}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Saved nortan_res results to dgcnn_nortan_res_detections.csv\n", "\n", "Summary for nortan_res:\n", "  site_name: nortan_res\n", "  total_locations: 69\n", "  pile_detections: 69\n", "  detection_rate: 1.000\n", "  avg_confidence: 1.0\n", "  high_confidence_count: 69\n", "  model_loaded: True\n"]}], "source": ["# Export results for current site\n", "if not results_df.empty:\n", "    # Save to CSV\n", "    output_file = f\"dgcnn_{SITE_NAME}_detections.csv\"\n", "    results_df.to_csv(output_file, index=False)\n", "    print(f\"Saved {SITE_NAME} results to {output_file}\")\n", "    \n", "    # Create summary\n", "    summary = {\n", "        'site_name': SITE_NAME,\n", "        'total_locations': len(results_df),\n", "        'pile_detections': pile_count,\n", "        'detection_rate': pile_count/len(results_df),\n", "        'avg_confidence': avg_confidence,\n", "        'high_confidence_count': sum(results_df['pile_probability'] > 0.9),\n", "        'model_loaded': MODEL_LOADED\n", "    }\n", "    \n", "    print(f\"\\nSummary for {SITE_NAME}:\")\n", "    for key, value in summary.items():\n", "        if isinstance(value, float):\n", "            print(f\"  {key}: {value:.3f}\")\n", "        else:\n", "            print(f\"  {key}: {value}\")\n", "else:\n", "    print(f\"No results to export for {SITE_NAME}\")\n"]}, {"cell_type": "code", "execution_count": 18, "id": "80762bff", "metadata": {"execution": {"iopub.execute_input": "2025-08-10T08:52:41.696365Z", "iopub.status.busy": "2025-08-10T08:52:41.696253Z", "iopub.status.idle": "2025-08-10T08:52:41.815489Z", "shell.execute_reply": "2025-08-10T08:52:41.815012Z"}, "papermill": {"duration": 0.122671, "end_time": "2025-08-10T08:52:41.816327", "exception": false, "start_time": "2025-08-10T08:52:41.693656", "status": "completed"}, "tags": []}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Exported all detections to KML: output_runs/dgcnn_harmonized_inference/nortan_res/dgcnn_nortan_res_all_detections.kml\n", "Exported pile detections to KML: output_runs/dgcnn_harmonized_inference/nortan_res/dgcnn_nortan_res_pile_detections.kml\n", "Exported high confidence detections to KML: output_runs/dgcnn_harmonized_inference/nortan_res/dgcnn_nortan_res_high_confidence.kml\n", "\n", "KML Export Summary:\n", "  All detections: 69 points\n", "  Pile detections: 69 points\n", "  High confidence: 69 points\n", "MLflow run completed\n", "\n", "DGCNN inference complete for nortan_res!\n", "Output directory: output_runs/dgcnn_harmonized_inference/nortan_res\n"]}], "source": ["# Export results to KML format\n", "from shapely.geometry import Point\n", "import geopandas as gpd\n", "\n", "if not results_df.empty:\n", "    try:\n", "        # Create GeoDataFrame from results\n", "        geometry = [Point(xy) for xy in zip(results_df['x'], results_df['y'])]\n", "        \n", "        # Create GeoDataFrame\n", "        gdf = gpd.GeoDataFrame(results_df, geometry=geometry)\n", "        \n", "        # Set coordinate reference system (assuming local coordinates)\n", "        # You may need to adjust this based on your actual coordinate system\n", "        gdf.crs = \"EPSG:4326\"  # WGS84 - adjust if needed\n", "        \n", "        # Add additional attributes for better KML visualization\n", "        gdf['name'] = gdf.apply(lambda row: f\"Pile_{row.name}\" if row['prediction'] == 'PILE' else f\"NonPile_{row.name}\", axis=1)\n", "        gdf['description'] = gdf.apply(lambda row: f\"Confidence: {row['pile_probability']:.3f}\\nPrediction: {row['prediction']}\", axis=1)\n", "        \n", "        # Color coding based on confidence\n", "        gdf['confidence_level'] = pd.cut(gdf['pile_probability'], \n", "                                       bins=[0, 0.5, 0.8, 0.95, 1.0], \n", "                                       labels=['Low', 'Medium', 'High', 'Very High'])\n", "        \n", "        # Export all detections to KML\n", "        kml_all_file = Path(OUTPUT_DIR) / f\"dgcnn_{SITE_NAME}_all_detections.kml\"\n", "        gdf.to_file(kml_all_file, driver='KML')\n", "        print(f\"Exported all detections to KML: {kml_all_file}\")\n", "        \n", "        # Export only pile detections to separate KML\n", "        pile_gdf = gdf[gdf['prediction'] == 'PILE'].copy()\n", "        if not pile_gdf.empty:\n", "            kml_piles_file = Path(OUTPUT_DIR) / f\"dgcnn_{SITE_NAME}_pile_detections.kml\"\n", "            pile_gdf.to_file(kml_piles_file, driver='KML')\n", "            print(f\"Exported pile detections to KML: {kml_piles_file}\")\n", "            \n", "            # Log KML files to MLflow\n", "            mlflow.log_artifact(str(kml_piles_file))\n", "        \n", "        # Export high confidence detections (>0.9) to separate KML\n", "        high_conf_gdf = gdf[gdf['pile_probability'] > 0.9].copy()\n", "        if not high_conf_gdf.empty:\n", "            kml_high_conf_file = Path(OUTPUT_DIR) / f\"dgcnn_{SITE_NAME}_high_confidence.kml\"\n", "            high_conf_gdf.to_file(kml_high_conf_file, driver='KML')\n", "            print(f\"Exported high confidence detections to KML: {kml_high_conf_file}\")\n", "            \n", "            # Log to MLflow\n", "            mlflow.log_artifact(str(kml_high_conf_file))\n", "        \n", "        # Log main KML to MLflow\n", "        mlflow.log_artifact(str(kml_all_file))\n", "        \n", "        print(f\"\\nKML Export Summary:\")\n", "        print(f\"  All detections: {len(gdf)} points\")\n", "        print(f\"  Pile detections: {len(pile_gdf)} points\")\n", "        print(f\"  High confidence: {len(high_conf_gdf)} points\")\n", "        \n", "    except Exception as e:\n", "        print(f\"Error exporting to KML: {e}\")\n", "else:\n", "    print(\"No results available for KML export\")\n", "\n", "\n", "# Close MLflow run\n", "mlflow.end_run()\n", "print(\"MLflow run completed\")\n", "\n", "print(f\"\\nDGCNN inference complete for {SITE_NAME}!\")\n", "print(f\"Output directory: {OUTPUT_DIR}\")"]}, {"cell_type": "code", "execution_count": 19, "id": "72e057a7", "metadata": {"execution": {"iopub.execute_input": "2025-08-10T08:52:41.822451Z", "iopub.status.busy": "2025-08-10T08:52:41.822198Z", "iopub.status.idle": "2025-08-10T08:52:41.828646Z", "shell.execute_reply": "2025-08-10T08:52:41.828234Z"}, "papermill": {"duration": 0.010431, "end_time": "2025-08-10T08:52:41.829598", "exception": false, "start_time": "2025-08-10T08:52:41.819167", "status": "completed"}, "tags": []}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Prediction Distribution Analysis:\n", "Min probability: 1.0000\n", "Max probability: 1.0000\n", "Mean probability: 1.0000\n", "Std probability: 0.0000\n", "Detections > 0.5: 69 (100.0%)\n", "Detections > 0.7: 69 (100.0%)\n", "Detections > 0.8: 69 (100.0%)\n", "Detections > 0.9: 69 (100.0%)\n", "Detections > 0.95: 69 (100.0%)\n", "\n", "Top 10 predictions:\n", "          x            y  pile_probability prediction\n", "385733.9523 3529192.8306               1.0       PILE\n", "385733.9523 3529212.8306               1.0       PILE\n", "385733.9523 3529232.8306               1.0       PILE\n", "385733.9523 3529252.8306               1.0       PILE\n", "385733.9523 3529272.8306               1.0       PILE\n", "385733.9523 3529292.8306               1.0       PILE\n", "385733.9523 3529312.8306               1.0       PILE\n", "385733.9523 3529332.8306               1.0       PILE\n", "385733.9523 3529352.8306               1.0       PILE\n", "385733.9523 3529372.8306               1.0       PILE\n", "\n", "Model loaded successfully: True\n"]}], "source": ["# %%\n", "# Diagnostic: Check model predictions in detail\n", "if not results_df.empty:\n", "    print(\"Prediction Distribution Analysis:\")\n", "    print(f\"Min probability: {results_df['pile_probability'].min():.4f}\")\n", "    print(f\"Max probability: {results_df['pile_probability'].max():.4f}\")\n", "    print(f\"Mean probability: {results_df['pile_probability'].mean():.4f}\")\n", "    print(f\"Std probability: {results_df['pile_probability'].std():.4f}\")\n", "    \n", "    # Check distribution at different thresholds\n", "    for threshold in [0.5, 0.7, 0.8, 0.9, 0.95]:\n", "        count = sum(results_df['pile_probability'] > threshold)\n", "        print(f\"Detections > {threshold}: {count} ({count/len(results_df)*100:.1f}%)\")\n", "    \n", "    # Show top 10 predictions\n", "    print(\"\\nTop 10 predictions:\")\n", "    top_preds = results_df.nlargest(10, 'pile_probability')[['x', 'y', 'pile_probability', 'prediction']]\n", "    print(top_preds.to_string(index=False))\n", "else:\n", "    print(\"No results to analyze\")\n", "\n", "print(f\"\\nModel loaded successfully: {MODEL_LOADED}\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}, "papermill": {"default_parameters": {}, "duration": 116.046687, "end_time": "2025-08-10T08:52:42.654487", "environment_variables": {}, "exception": null, "input_path": "00_dgcnn_inference_harmonized.ipynb", "output_path": "00_dgcnn_inference_harmonized_nortan_res_executed.ipynb", "parameters": {"OUTPUT_DIR": "output_runs/dgcnn_harmonized_inference/nortan_res", "POINT_CLOUD_PATH": "../../../../../data/raw/nortan_res/pointcloud/Block_11_2m.las", "RUN_NAME": "inference_nortan_res", "SITE_NAME": "nortan_res"}, "start_time": "2025-08-10T08:50:46.607800", "version": "2.6.0"}}, "nbformat": 4, "nbformat_minor": 5}