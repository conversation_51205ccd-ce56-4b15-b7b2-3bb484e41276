{"cells": [{"cell_type": "markdown", "id": "4e86c2a3", "metadata": {"papermill": {"duration": 0.007377, "end_time": "2025-08-10T08:33:37.720041", "exception": false, "start_time": "2025-08-10T08:33:37.712664", "status": "completed"}, "tags": []}, "source": ["# PointNet++ Model Application to New Site\n", "This notebook applies the trained PointNet++ model to a new construction site\n", "with different data sources (DWG files instead of KML regions).\n", "\n", "**Workflow:**\n", "1. <PERSON><PERSON> trained PointNet++ model\n", "2. Process new site point cloud data\n", "3. Generate predictions across the site\n", "4. Compare with DWG-based pile locations (if available)\n", "5. Validate and export results\n", "\n", "**Author**: <PERSON><PERSON><PERSON>  \n", "**Date**: July 2025  \n", "**Project**: As-Built Foundation Analysis - Site Transfer"]}, {"cell_type": "code", "execution_count": 1, "id": "parameters", "metadata": {"execution": {"iopub.execute_input": "2025-08-10T08:33:37.729298Z", "iopub.status.busy": "2025-08-10T08:33:37.729002Z", "iopub.status.idle": "2025-08-10T08:33:37.736887Z", "shell.execute_reply": "2025-08-10T08:33:37.736371Z"}, "papermill": {"duration": 0.013565, "end_time": "2025-08-10T08:33:37.738182", "exception": false, "start_time": "2025-08-10T08:33:37.724617", "status": "completed"}, "tags": ["parameters"]}, "outputs": [], "source": ["# Parameters cell for Papermill execution\n", "NEW_SITE_NAME = \"althea_rpcs\"\n", "POINT_CLOUD_PATH = \"../../../../../data/raw/althea_rpcs/pointcloud/Point_Cloud.las\"\n", "OUTPUT_DIR = f\"output_runs/pointnet_plus_plus_inference/{NEW_SITE_NAME}\"\n", "\n", "MODEL_PATH = \"best_pointnet_iter4.pth\"  # Path to trained model\n", "\n", "DWG_PATH = \"\"  # Path to DWG file (optional)\n", "OUTPUT_DIR = \"output_runs/pointnet_plus_plus_inference\"  # Output directory\n", "\n", "# Model parameters (must match training)\n", "CONFIDENCE_THRESHOLD = 0.3  # Confidence threshold for pile detection\n", "\n", "GRID_SPACING = 20.0 # Grid spacing for analysis points\n", "PATCH_SIZE = 8.0  # meters radius for patch extraction - FIXED: harmonized training uses 8m\n", "NUM_POINTS = 1024 # KEEP 1024 to match training - CRITICAL for accuracy\n", "BATCH_SIZE = 8   # Batch size for inference\n", "\n", "# CRS and coordinate validation parameters\n", "SITE_CRS = \"EPSG:32615\"  # Will be set by papermill\n", "\n", "USE_FAST_MODE = False  # Overwritten by papermill\n", "\n", "# Enhanced analysis parameters\n", "EXTENDED_ANALYSIS = True\n", "NEGATIVE_SAMPLE_DISTANCE = 8.0  # Distance for synthetic negatives\n", "FULL_SITE_ANALYSIS = True  # Run on all points, not subset\n", "\n", "# MLflow configuration\n", "EXPERIMENT_NAME = \"pointnet_plus_plus_inference\"\n", "RUN_NAME = f\"inference_{NEW_SITE_NAME}\""]}, {"cell_type": "code", "execution_count": 2, "id": "c17ed748", "metadata": {"execution": {"iopub.execute_input": "2025-08-10T08:33:37.766063Z", "iopub.status.busy": "2025-08-10T08:33:37.765889Z", "iopub.status.idle": "2025-08-10T08:33:37.768256Z", "shell.execute_reply": "2025-08-10T08:33:37.768001Z"}, "papermill": {"duration": 0.006369, "end_time": "2025-08-10T08:33:37.769148", "exception": false, "start_time": "2025-08-10T08:33:37.762779", "status": "completed"}, "tags": ["injected-parameters"]}, "outputs": [], "source": ["# Parameters\n", "NEW_SITE_NAME = \"trino_enel\"\n", "POINT_CLOUD_PATH = \"../../../../../data/raw/trino_enel/pointcloud/Trino_Fly_2_Shifted.las\"\n", "SITE_CRS = \"EPSG:32632\"\n", "USE_FAST_MODE = \"true\"\n", "OUTPUT_DIR = \"output_runs/pointnet_plus_plus_harmonized_inference/trino_enel\"\n", "RUN_NAME = \"harmonized_inference_trino_enel\"\n", "PATCH_SIZE = 8.0\n", "NUM_POINTS = 1024\n", "MODEL_PATH = \"best_pointnet_iter4.pth\"\n"]}, {"cell_type": "code", "execution_count": 3, "id": "f22a587b", "metadata": {"execution": {"iopub.execute_input": "2025-08-10T08:33:37.773718Z", "iopub.status.busy": "2025-08-10T08:33:37.773614Z", "iopub.status.idle": "2025-08-10T08:33:37.776425Z", "shell.execute_reply": "2025-08-10T08:33:37.776145Z"}, "papermill": {"duration": 0.00601, "end_time": "2025-08-10T08:33:37.777264", "exception": false, "start_time": "2025-08-10T08:33:37.771254", "status": "completed"}, "tags": []}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["New Site Analysis Configuration:\n", "Site: trino_enel\n", "Using fast mode: true\n", "EPSG Configuration:\n", "  Inference EPSG: EPSG:32632\n", "Patch Size: 8.0m radius\n", "Points per Patch: 1024\n", "Model Path: best_pointnet_iter4.pth\n", "Point Cloud Path: ../../../../../data/raw/trino_enel/pointcloud/Trino_Fly_2_Shifted.las\n", "Output Directory: output_runs/pointnet_plus_plus_harmonized_inference/trino_enel\n", "Extended Analysis: True\n", "Full Site Analysis: True\n"]}], "source": ["# Display configuration\n", "print(f\"New Site Analysis Configuration:\")\n", "print(f\"Site: {NEW_SITE_NAME}\")\n", "print(f\"Using fast mode: {USE_FAST_MODE}\")\n", "print(f\"EPSG Configuration:\")\n", "print(f\"  Inference EPSG: {SITE_CRS}\")\n", "print(f\"Patch Size: {PATCH_SIZE}m radius\")\n", "print(f\"Points per Patch: {NUM_POINTS}\")\n", "print(f\"Model Path: {MODEL_PATH}\")\n", "print(f\"Point Cloud Path: {POINT_CLOUD_PATH}\")\n", "print(f\"Output Directory: {OUTPUT_DIR}\")\n", "print(f\"Extended Analysis: {EXTENDED_ANALYSIS}\")\n", "print(f\"Full Site Analysis: {FULL_SITE_ANALYSIS}\")\n", "\n"]}, {"cell_type": "code", "execution_count": 4, "id": "6b291543", "metadata": {"execution": {"iopub.execute_input": "2025-08-10T08:33:37.781701Z", "iopub.status.busy": "2025-08-10T08:33:37.781599Z", "iopub.status.idle": "2025-08-10T08:33:42.475432Z", "shell.execute_reply": "2025-08-10T08:33:42.475156Z"}, "papermill": {"duration": 4.697074, "end_time": "2025-08-10T08:33:42.476430", "exception": false, "start_time": "2025-08-10T08:33:37.779356", "status": "completed"}, "tags": []}, "outputs": [], "source": ["import os\n", "import json\n", "import pickle\n", "import torch\n", "import warnings\n", "import numpy as np\n", "import pandas as pd\n", "from pathlib import Path\n", "from datetime import datetime\n", "import torch.nn as nn\n", "import matplotlib.pyplot as plt\n", "import laspy\n", "import open3d as o3d\n", "import mlflow\n", "import mlflow.pytorch\n", "from scipy.spatial import cKDTree\n", "from scipy.spatial.distance import pdist\n", "import seaborn as sns\n", "\n", "warnings.filterwarnings('ignore')\n"]}, {"cell_type": "code", "execution_count": 5, "id": "setup_output_dir", "metadata": {"execution": {"iopub.execute_input": "2025-08-10T08:33:42.480785Z", "iopub.status.busy": "2025-08-10T08:33:42.480633Z", "iopub.status.idle": "2025-08-10T08:33:42.482876Z", "shell.execute_reply": "2025-08-10T08:33:42.482658Z"}, "papermill": {"duration": 0.005324, "end_time": "2025-08-10T08:33:42.483651", "exception": false, "start_time": "2025-08-10T08:33:42.478327", "status": "completed"}, "tags": []}, "outputs": [], "source": ["def setup_environment():\n", "    \"\"\"Setup output directory and MLflow tracking\"\"\"\n", "    os.makedirs(OUTPUT_DIR, exist_ok=True)\n", "    \n", "    if mlflow.active_run() is not None:\n", "        mlflow.end_run()\n", "    \n", "    mlflow.set_experiment(EXPERIMENT_NAME)\n", "    mlflow.start_run(run_name=RUN_NAME)\n", "    \n", "    # Log parameters\n", "    params = {\n", "        \"site_name\": NEW_SITE_NAME,\n", "        \"patch_size\": PATCH_SIZE,\n", "        \"num_points\": NUM_POINTS,\n", "        \"confidence_threshold\": CONFIDENCE_THRESHOLD,\n", "        \"batch_size\": BATCH_SIZE,\n", "        \"grid_spacing\": GRID_SPACING\n", "    }\n", "    for key, value in params.items():\n", "        mlflow.log_param(key, value)"]}, {"cell_type": "markdown", "id": "a0315f2c", "metadata": {"papermill": {"duration": 0.001645, "end_time": "2025-08-10T08:33:42.487097", "exception": false, "start_time": "2025-08-10T08:33:42.485452", "status": "completed"}, "tags": []}, "source": ["## PointNet++ Utility Functions "]}, {"cell_type": "code", "execution_count": 6, "id": "f547b445", "metadata": {"execution": {"iopub.execute_input": "2025-08-10T08:33:42.490989Z", "iopub.status.busy": "2025-08-10T08:33:42.490894Z", "iopub.status.idle": "2025-08-10T08:33:42.495313Z", "shell.execute_reply": "2025-08-10T08:33:42.495080Z"}, "papermill": {"duration": 0.007203, "end_time": "2025-08-10T08:33:42.496092", "exception": false, "start_time": "2025-08-10T08:33:42.488889", "status": "completed"}, "tags": []}, "outputs": [], "source": ["# PointNet++ utility functions\n", "def square_distance(src, dst):\n", "    \"\"\"Calculate squared distance between two point sets\"\"\"\n", "    B, N, _ = src.shape\n", "    _, M, _ = dst.shape\n", "    dist = -2 * torch.matmul(src, dst.permute(0, 2, 1))\n", "    dist += torch.sum(src ** 2, -1).view(B, N, 1)\n", "    dist += torch.sum(dst ** 2, -1).view(B, 1, M)\n", "    return dist\n", "\n", "def farthest_point_sample(xyz, npoint):\n", "    \"\"\"Farthest point sampling\"\"\"\n", "    device = xyz.device\n", "    B, N, C = xyz.shape\n", "    centroids = torch.zeros(B, npoint, dtype=torch.long).to(device)\n", "    distance = torch.ones(B, N).to(device) * 1e10\n", "    farthest = torch.randint(0, N, (B,), dtype=torch.long).to(device)\n", "    batch_indices = torch.arange(B, dtype=torch.long).to(device)\n", "    \n", "    for i in range(npoint):\n", "        centroids[:, i] = farthest\n", "        centroid = xyz[batch_indices, farthest, :].view(B, 1, 3)\n", "        dist = torch.sum((xyz - centroid) ** 2, -1)\n", "        mask = dist < distance\n", "        distance[mask] = dist[mask]\n", "        farthest = torch.max(distance, -1)[1]\n", "    \n", "    return centroids\n", "\n", "def query_ball_point(radius, nsample, xyz, new_xyz):\n", "    \"\"\"Query ball point grouping\"\"\"\n", "    device = xyz.device\n", "    B, N, C = xyz.shape\n", "    _, S, _ = new_xyz.shape\n", "    group_idx = torch.arange(N, dtype=torch.long).to(device).view(1, 1, N).repeat([B, S, 1])\n", "    sqrdists = square_distance(new_xyz, xyz)\n", "    group_idx[sqrdists > radius ** 2] = N\n", "    group_idx = group_idx.sort(dim=-1)[0][:, :, :nsample]\n", "    group_first = group_idx[:, :, 0].view(B, S, 1).repeat([1, 1, nsample])\n", "    mask = group_idx == N\n", "    group_idx[mask] = group_first[mask]\n", "    return group_idx"]}, {"cell_type": "markdown", "id": "8cbfede7", "metadata": {"papermill": {"duration": 0.001595, "end_time": "2025-08-10T08:33:42.499487", "exception": false, "start_time": "2025-08-10T08:33:42.497892", "status": "completed"}, "tags": []}, "source": ["## PointNet++ Set Abstraction Layer\n"]}, {"cell_type": "code", "execution_count": 7, "id": "bf8dd055", "metadata": {"execution": {"iopub.execute_input": "2025-08-10T08:33:42.503214Z", "iopub.status.busy": "2025-08-10T08:33:42.503016Z", "iopub.status.idle": "2025-08-10T08:33:42.507154Z", "shell.execute_reply": "2025-08-10T08:33:42.506938Z"}, "papermill": {"duration": 0.006858, "end_time": "2025-08-10T08:33:42.507886", "exception": false, "start_time": "2025-08-10T08:33:42.501028", "status": "completed"}, "tags": []}, "outputs": [], "source": ["class PointNetSetAbstraction(nn.Module):\n", "    def __init__(self, npoint, radius, nsample, in_channel, mlp, group_all):\n", "        super(PointNetSetAbstraction, self).__init__()\n", "        self.npoint = npoint\n", "        self.radius = radius\n", "        self.nsample = nsample\n", "        self.mlp_convs = nn.ModuleList()\n", "        self.mlp_bns = nn.ModuleList()\n", "        self.group_all = group_all\n", "        \n", "        last_channel = in_channel\n", "        for out_channel in mlp:\n", "            self.mlp_convs.append(nn.Conv2d(last_channel, out_channel, 1))\n", "            self.mlp_bns.append(nn.BatchNorm2d(out_channel))\n", "            last_channel = out_channel\n", "    \n", "    def forward(self, xyz, points):\n", "        \"\"\"Forward pass of PointNet++ Set Abstraction layer\"\"\"\n", "        B, N, C = xyz.shape\n", "        \n", "        if self.group_all:\n", "            new_xyz = torch.zeros(B, 1, C).to(xyz.device)\n", "            new_points = points.view(B, 1, N, -1) if points is not None else xyz.view(B, 1, N, C)\n", "        else:\n", "            fps_idx = farthest_point_sample(xyz, self.npoint)\n", "            new_xyz = xyz[torch.arange(B)[:, None], fps_idx]\n", "            idx = query_ball_point(self.radius, self.nsample, xyz, new_xyz)\n", "            grouped_xyz = xyz[torch.arange(B)[:, None, None], idx]\n", "            grouped_xyz_norm = grouped_xyz - new_xyz.view(B, self.npoint, 1, C)\n", "            \n", "            if points is not None:\n", "                grouped_points = points[torch.arange(B)[:, None, None], idx]\n", "                new_points = torch.cat([grouped_xyz_norm, grouped_points], dim=-1)\n", "            else:\n", "                new_points = grouped_xyz_norm\n", "        \n", "        new_points = new_points.permute(0, 3, 2, 1)\n", "        for i, conv in enumerate(self.mlp_convs):\n", "            bn = self.mlp_bns[i]\n", "            new_points = torch.relu(bn(conv(new_points)))\n", "        \n", "        new_points = torch.max(new_points, 2)[0]\n", "        new_points = new_points.permute(0, 2, 1)\n", "        \n", "        return new_xyz, new_points"]}, {"cell_type": "markdown", "id": "d5232c15", "metadata": {"papermill": {"duration": 0.001689, "end_time": "2025-08-10T08:33:42.511433", "exception": false, "start_time": "2025-08-10T08:33:42.509744", "status": "completed"}, "tags": []}, "source": ["## PointNet++ Model Architecture\n"]}, {"cell_type": "code", "execution_count": 8, "id": "3fb10d9f", "metadata": {"execution": {"iopub.execute_input": "2025-08-10T08:33:42.515231Z", "iopub.status.busy": "2025-08-10T08:33:42.515141Z", "iopub.status.idle": "2025-08-10T08:33:42.519023Z", "shell.execute_reply": "2025-08-10T08:33:42.518790Z"}, "papermill": {"duration": 0.006538, "end_time": "2025-08-10T08:33:42.519739", "exception": false, "start_time": "2025-08-10T08:33:42.513201", "status": "completed"}, "tags": []}, "outputs": [], "source": ["class PointNetPlusPlus(nn.Module):\n", "    def __init__(self, num_classes=2, in_channels=20, dropout=0.3):  # ✅ CRITICAL: in_channels=20\n", "        super(PointNetPlusPlus, self).__init__()\n", "\n", "        # Set Abstraction Layers (SAME AS TRAINING)\n", "        self.sa1 = PointNetSetAbstraction(256, 0.2, 32, in_channels, [64, 64, 128], False)\n", "        self.sa2 = PointNetSetAbstraction(64, 0.4, 64, 128 + 3, [128, 128, 256], False)\n", "        self.sa3 = PointNetSetAbstraction(None, None, None, 256, [256, 512, 1024], True)\n", "\n", "        # Classification head (SAME AS TRAINING)\n", "        self.fc1 = nn.Linear(1024, 512)\n", "        self.bn1 = nn.BatchNorm1d(512)\n", "        self.drop1 = nn.Dropout(dropout)\n", "\n", "        self.fc2 = nn.Linear(512, 256)\n", "        self.bn2 = nn.BatchNorm1d(256)\n", "        self.drop2 = nn.Dropout(dropout)\n", "\n", "        self.fc3 = nn.Linear(256, 64)\n", "        self.bn3 = nn.<PERSON><PERSON><PERSON>orm1d(64)\n", "        self.drop3 = nn.Dropout(dropout * 0.6)\n", "\n", "        self.fc4 = nn.Linear(64, num_classes)\n", "\n", "    def forward(self, xyz):\n", "        # Input shape: (B, N, C), C = in_channels (20 features)\n", "        if len(xyz.shape) == 4:\n", "            xyz = xyz.squeeze(1)\n", "\n", "        B, N, C = xyz.shape\n", "\n", "        # Split input into xyz coords and features (SAME AS TRAINING)\n", "        coords = xyz[:, :, :3]       # (B, N, 3)\n", "        features = xyz[:, :, 3:]     # (B, N, C-3) = (B, N, 17)\n", "\n", "        # Pass through SA layers\n", "        l1_xyz, l1_points = self.sa1(coords, features)\n", "        l2_xyz, l2_points = self.sa2(l1_xyz, l1_points)\n", "        l3_xyz, l3_points = self.sa3(l2_xyz, l2_points)\n", "\n", "        global_feat = l3_points.view(B, -1)\n", "\n", "        # Classification head\n", "        x = self.drop1(torch.relu(self.bn1(self.fc1(global_feat))))\n", "        x = self.drop2(torch.relu(self.bn2(self.fc2(x))))\n", "        x = self.drop3(torch.relu(self.bn3(self.fc3(x))))\n", "        x = self.fc4(x)\n", "\n", "        return x"]}, {"cell_type": "markdown", "id": "2f5e408b", "metadata": {"papermill": {"duration": 0.001756, "end_time": "2025-08-10T08:33:42.523525", "exception": false, "start_time": "2025-08-10T08:33:42.521769", "status": "completed"}, "tags": []}, "source": ["## Load"]}, {"cell_type": "code", "execution_count": 9, "id": "3838119b", "metadata": {"execution": {"iopub.execute_input": "2025-08-10T08:33:42.527239Z", "iopub.status.busy": "2025-08-10T08:33:42.527148Z", "iopub.status.idle": "2025-08-10T08:33:42.530439Z", "shell.execute_reply": "2025-08-10T08:33:42.530210Z"}, "papermill": {"duration": 0.00607, "end_time": "2025-08-10T08:33:42.531185", "exception": false, "start_time": "2025-08-10T08:33:42.525115", "status": "completed"}, "tags": []}, "outputs": [], "source": ["def load_point_cloud(file_path):\n", "    \"\"\"Load point cloud from supported formats (.las, .ply, .pcd)\"\"\"\n", "    file_path = Path(file_path)\n", "    \n", "    if not file_path.exists():\n", "        print(f\"File not found: {file_path}\")\n", "        return None\n", "    \n", "    suffix = file_path.suffix.lower()\n", "    \n", "    try:\n", "        if suffix == '.las':\n", "            las_file = laspy.read(file_path)\n", "            points = np.vstack([las_file.x, las_file.y, las_file.z]).T\n", "            print(f\"Loaded LAS file with {len(points):,} points\")\n", "                \n", "        elif suffix in ['.ply', '.pcd']:\n", "            pcd = o3d.io.read_point_cloud(str(file_path))\n", "            points = np.asarray(pcd.points)\n", "            \n", "            if len(points) == 0:\n", "                print(f\"Warning: {suffix.upper()} file contains no points\")\n", "                return None\n", "                \n", "            print(f\"Loaded {suffix.upper()} file with {len(points):,} points\")\n", "                \n", "        else:\n", "            print(f\"Unsupported file format: {suffix}. Supported formats: .las, .ply, .pcd\")\n", "            return None\n", "        \n", "        if len(points) == 0:\n", "            print(\"Warning: Point cloud contains no points\")\n", "            return None\n", "            \n", "        print(f\"Point cloud bounds:\")\n", "        print(f\"  X: {points[:, 0].min():.2f} to {points[:, 0].max():.2f}\")\n", "        print(f\"  Y: {points[:, 1].min():.2f} to {points[:, 1].max():.2f}\")\n", "        print(f\"  Z: {points[:, 2].min():.2f} to {points[:, 2].max():.2f}\")\n", "        \n", "        return points\n", "        \n", "    except Exception as e:\n", "        print(f\"Error loading point cloud: {e}\")\n", "        return None"]}, {"cell_type": "code", "execution_count": 10, "id": "model_validation", "metadata": {"execution": {"iopub.execute_input": "2025-08-10T08:33:42.534977Z", "iopub.status.busy": "2025-08-10T08:33:42.534884Z", "iopub.status.idle": "2025-08-10T08:33:42.537726Z", "shell.execute_reply": "2025-08-10T08:33:42.537540Z"}, "papermill": {"duration": 0.005535, "end_time": "2025-08-10T08:33:42.538435", "exception": false, "start_time": "2025-08-10T08:33:42.532900", "status": "completed"}, "tags": []}, "outputs": [], "source": ["def load_model(model_path, device):\n", "    \"\"\"Load trained PointNet++ model\"\"\"\n", "    try:\n", "        model = PointNetPlusPlus(num_classes=2, in_channels=20, dropout=0.3).to(device)\n", "        \n", "        if Path(model_path).exists():\n", "            checkpoint = torch.load(model_path, map_location=device)\n", "            \n", "            if isinstance(checkpoint, dict) and 'model_state_dict' in checkpoint:\n", "                model.load_state_dict(checkpoint['model_state_dict'])\n", "                print(f\"Loaded checkpoint from epoch {checkpoint.get('epoch', 'unknown')}\")\n", "            else:\n", "                model.load_state_dict(checkpoint)\n", "            \n", "            model.eval()\n", "            param_count = sum(p.numel() for p in model.parameters())\n", "            print(f\"Loaded trained PointNet++ model with {param_count:,} parameters\")\n", "            return model, True\n", "        else:\n", "            print(f\"Model file not found: {model_path}\")\n", "            return model, False\n", "            \n", "    except Exception as e:\n", "        print(f\"Error loading model: {e}\")\n", "        return PointNetPlusPlus(num_classes=2, in_channels=20, dropout=0.3).to(device), False"]}, {"cell_type": "code", "execution_count": 11, "id": "a5f0d927", "metadata": {"execution": {"iopub.execute_input": "2025-08-10T08:33:42.542245Z", "iopub.status.busy": "2025-08-10T08:33:42.542161Z", "iopub.status.idle": "2025-08-10T08:33:42.545606Z", "shell.execute_reply": "2025-08-10T08:33:42.545384Z"}, "papermill": {"duration": 0.006125, "end_time": "2025-08-10T08:33:42.546305", "exception": false, "start_time": "2025-08-10T08:33:42.540180", "status": "completed"}, "tags": []}, "outputs": [], "source": ["def create_analysis_grid(point_cloud, grid_spacing=15.0, buffer_factor=1.5):\n", "    \"\"\"Create analysis grid with spatial bounds optimization\"\"\"\n", "    print(f\"Point cloud has {len(point_cloud):,} points\")\n", "    \n", "    # Get bounds efficiently\n", "    x_min, x_max = point_cloud[:, 0].min(), point_cloud[:, 0].max()\n", "    y_min, y_max = point_cloud[:, 1].min(), point_cloud[:, 1].max()\n", "    \n", "    print(f\"Point cloud bounds: X({x_min:.1f}, {x_max:.1f}), Y({y_min:.1f}, {y_max:.1f})\")\n", "    \n", "    buffer = grid_spacing * buffer_factor\n", "    x_coords = np.arange(x_min - buffer, x_max + buffer, grid_spacing)\n", "    y_coords = np.arange(y_min - buffer, y_max + buffer, grid_spacing)\n", "    \n", "    grid_points = np.array([[x, y] for x in x_coords for y in y_coords])\n", "    print(f\"Created analysis grid with {len(grid_points)} points\")\n", "    \n", "    return grid_points\n", "\n", "def create_analysis_smart_grid(point_cloud, grid_spacing=5.0):\n", "    \"\"\"Create analysis grid with spatial bounds optimization\"\"\"\n", "    print(f\"Point cloud has {len(point_cloud):,} points\")\n", "    \n", "    x_min, x_max = point_cloud[:, 0].min(), point_cloud[:, 0].max()\n", "    y_min, y_max = point_cloud[:, 1].min(), point_cloud[:, 1].max()\n", "    \n", "    print(f\"Point cloud bounds: X({x_min:.1f}, {x_max:.1f}), Y({y_min:.1f}, {y_max:.1f})\")\n", "    \n", "    x_coords = np.arange(x_min, x_max, grid_spacing)\n", "    y_coords = np.arange(y_min, y_max, grid_spacing)\n", "    \n", "    grid_points = np.array([[x, y] for x in x_coords for y in y_coords])\n", "    print(f\"Created analysis grid with {len(grid_points)} points\")\n", "    \n", "    return grid_points\n"]}, {"cell_type": "code", "execution_count": 12, "id": "2d36b96c", "metadata": {"execution": {"iopub.execute_input": "2025-08-10T08:33:42.550117Z", "iopub.status.busy": "2025-08-10T08:33:42.550011Z", "iopub.status.idle": "2025-08-10T08:33:42.556005Z", "shell.execute_reply": "2025-08-10T08:33:42.555733Z"}, "papermill": {"duration": 0.008734, "end_time": "2025-08-10T08:33:42.556737", "exception": false, "start_time": "2025-08-10T08:33:42.548003", "status": "completed"}, "tags": []}, "outputs": [], "source": ["def extract_patch_features(point_cloud, center_xy, radius=20.0, num_points=1024, kdtree=None):\n", "    \"\"\"Extract patch features EXACTLY matching training preprocessing\"\"\"\n", "    center_x, center_y = center_xy\n", "    \n", "    if kdtree is not None:\n", "        indices = kdtree.query_ball_point([center_x, center_y], radius)\n", "        if len(indices) < 50:\n", "            return None\n", "        patch_xyz = point_cloud[indices]\n", "    else:\n", "        distances_2d = np.sqrt((point_cloud[:, 0] - center_x)**2 + \n", "                              (point_cloud[:, 1] - center_y)**2)\n", "        mask = distances_2d <= radius\n", "        patch_xyz = point_cloud[mask]\n", "        \n", "        if len(patch_xyz) < 50:\n", "            return None\n", "    \n", "    # MATCH training preprocessing exactly - use same feature engineering as training data creation\n", "    x, y, z = patch_xyz[:, 0], patch_xyz[:, 1], patch_xyz[:, 2]\n", "    \n", "    # Calculate patch statistics\n", "    patch_center = np.mean(patch_xyz, axis=0)\n", "    z_mean, z_std = z.mean(), z.std() + 1e-6\n", "    z_min, z_max = z.min(), z.max()\n", "    \n", "    # Distance calculations\n", "    dist_to_center = np.sqrt((x - center_x)**2 + (y - center_y)**2)\n", "    \n", "    # RECREATE EXACT TRAINING FEATURES - these come from the original data creation process\n", "    features_list = []\n", "    for i, point in enumerate(patch_xyz):\n", "        px, py, pz = point\n", "        \n", "        # Match the exact feature engineering from training data preparation\n", "        feature_vector = [\n", "            px,  # 0: raw X coordinate\n", "            py - center_y,  # 1: relative Y (small values like training)\n", "            pz,  # 2: raw Z coordinate  \n", "            (pz - z_mean) / z_std,  # 3: height_norm\n", "            dist_to_center[i],  # 4: distance_norm\n", "            len(patch_xyz) / 1000.0,  # 5: density\n", "            px - center_x,  # 6: relative_x\n", "            py - center_y,  # 7: relative_y  \n", "            pz - patch_center[2],  # 8: relative_z\n", "            pz - z_min,  # 9: height_above_min\n", "            z_max - pz,  # 10: depth_below_max\n", "            abs(pz - z_mean),  # 11: abs_height_deviation\n", "            np.sqrt(px**2 + py**2),  # 12: distance_from_origin\n", "            np.arctan2(py - center_y, px - center_x),  # 13: angle\n", "            (px - center_x) * (py - center_y),  # 14: interaction\n", "            px - patch_center[0],  # 15: patch_relative_x\n", "            py - patch_center[1],  # 16: patch_relative_y\n", "            pz / (dist_to_center[i] + 1e-6),  # 17: height_distance_ratio\n", "            np.sqrt((px - center_x)**2 + (py - center_y)**2 + (pz - z_mean)**2),  # 18: 3d_distance\n", "            dist_to_center[i] + np.random.normal(0, 0.01)  # 19: distance with slight variation\n", "        ]\n", "        features_list.append(feature_vector)\n", "    \n", "    patch_features = np.array(features_list, dtype=np.float32)\n", "    \n", "    # EXACT SAME SAMPLING LOGIC AS TRAINING\n", "    if len(patch_features) >= num_points:\n", "        # Distance-weighted sampling like training\n", "        distances = patch_features[:, 4]  # distance_norm column\n", "        probabilities = 1 / (distances + 0.1)\n", "        probabilities /= probabilities.sum()\n", "        \n", "        sampled_indices = np.random.choice(len(patch_features), num_points, \n", "                                         replace=False, p=probabilities)\n", "        sampled = patch_features[sampled_indices]\n", "    else:\n", "        # Upsample with weighted selection like training\n", "        upsampled = patch_features.copy()\n", "        needed = num_points - len(patch_features)\n", "        \n", "        for _ in range(needed):\n", "            distances = patch_features[:, 4]\n", "            weights = 1 / (distances + 0.1)\n", "            weights /= weights.sum()\n", "            source_idx = np.random.choice(len(patch_features), p=weights)\n", "            \n", "            new_point = patch_features[source_idx].copy()\n", "            new_point[:3] += np.random.normal(0, 0.02, 3)  # Add noise to spatial like training\n", "            upsampled = np.vstack([upsampled, new_point])\n", "        \n", "        sampled = upsampled[:num_points]\n", "    \n", "    # EXACT SAME NORMALIZATION AS TRAINING\n", "    # Training shows: sampled /= np.max(np.linalg.norm(sampled, axis=1)) or 1\n", "    spatial_coords = sampled[:, :3]\n", "    spatial_extent = np.max(np.linalg.norm(spatial_coords, axis=1))\n", "    if spatial_extent > 0:\n", "        sampled[:, :3] /= spatial_extent\n", "    \n", "    return sampled\n", "\n"]}, {"cell_type": "code", "execution_count": 13, "id": "8444b060", "metadata": {"execution": {"iopub.execute_input": "2025-08-10T08:33:42.560562Z", "iopub.status.busy": "2025-08-10T08:33:42.560467Z", "iopub.status.idle": "2025-08-10T08:33:42.565234Z", "shell.execute_reply": "2025-08-10T08:33:42.565016Z"}, "papermill": {"duration": 0.007439, "end_time": "2025-08-10T08:33:42.565921", "exception": false, "start_time": "2025-08-10T08:33:42.558482", "status": "completed"}, "tags": []}, "outputs": [], "source": ["def process_site_inference(point_cloud, grid_points, model, device, \n", "                          batch_size=16, radius=20.0, num_points=1024):\n", "    \"\"\"CPU-optimized inference maintaining 1024 points for accuracy\"\"\"\n", "    print(f\"Building spatial index for {len(point_cloud):,} points...\")\n", "    kdtree = cKDTree(point_cloud[:, :2])\n", "    \n", "    print(\"Pre-filtering valid grid points...\")\n", "    valid_grid_points = []\n", "    for i, center in enumerate(grid_points):\n", "        if i % 500 == 0:\n", "            print(f\"  Pre-filtering progress: {i}/{len(grid_points)}\")\n", "        \n", "        indices = kdtree.query_ball_point([center[0], center[1]], radius)\n", "        if len(indices) >= 50:  # Keep higher threshold for 1024 points\n", "            valid_grid_points.append(center)\n", "    \n", "    valid_grid_points = np.array(valid_grid_points)\n", "    print(f\"Filtered to {len(valid_grid_points)} valid grid points (from {len(grid_points)})\")\n", "    \n", "    if len(valid_grid_points) == 0:\n", "        return pd.DataFrame()\n", "    \n", "    results = []\n", "    total_batches = len(valid_grid_points) // batch_size + (1 if len(valid_grid_points) % batch_size != 0 else 0)\n", "    \n", "    print(f\"Processing {len(valid_grid_points)} points with 1024 points/patch (CPU optimized)...\")\n", "    print(f\"This maintains training accuracy but will be slower than 512 points\")\n", "    \n", "    model.eval()\n", "    if device.type == 'cpu':\n", "        torch.set_num_threads(4)\n", "    \n", "    for i in range(0, len(valid_grid_points), batch_size):\n", "        batch_idx = i // batch_size + 1\n", "        if batch_idx % 3 == 1:  # More frequent updates since batches are smaller\n", "            print(f\"  Batch {batch_idx}/{total_batches} ({i}/{len(valid_grid_points)} points)\")\n", "        \n", "        batch_centers = valid_grid_points[i:i+batch_size]\n", "        batch_patches = []\n", "        valid_centers = []\n", "        \n", "        for center in batch_centers:\n", "            patch = extract_patch_features(point_cloud, center, radius, num_points, kdtree)\n", "            if patch is not None:\n", "                batch_patches.append(patch)\n", "                valid_centers.append(center)\n", "        \n", "        if len(batch_patches) == 0:\n", "            continue\n", "        \n", "        batch_tensor = torch.FloatTensor(np.array(batch_patches)).to(device)\n", "        \n", "        with torch.no_grad():\n", "            outputs = model(batch_tensor)\n", "            probabilities = torch.softmax(outputs, dim=1)\n", "            pile_probs = probabilities[:, 1].cpu().numpy()\n", "        \n", "        for center, prob in zip(valid_centers, pile_probs):\n", "            results.append({\n", "                'x': center[0],\n", "                'y': center[1],\n", "                'pile_probability': prob,\n", "                'prediction': 'PILE' if prob > CONFIDENCE_THRESHOLD else 'NON-PILE'\n", "            })\n", "    \n", "    print(f\"CPU processing complete: {len(results)} successful predictions\")\n", "    return pd.DataFrame(results)\n"]}, {"cell_type": "code", "execution_count": 14, "id": "89ce4683", "metadata": {"execution": {"iopub.execute_input": "2025-08-10T08:33:42.569719Z", "iopub.status.busy": "2025-08-10T08:33:42.569601Z", "iopub.status.idle": "2025-08-10T08:33:42.572857Z", "shell.execute_reply": "2025-08-10T08:33:42.572600Z"}, "papermill": {"duration": 0.005967, "end_time": "2025-08-10T08:33:42.573566", "exception": false, "start_time": "2025-08-10T08:33:42.567599", "status": "completed"}, "tags": []}, "outputs": [], "source": ["def visualize_results(results_df, output_dir, site_name):\n", "    \"\"\"Create visualization of pile detection results\"\"\"\n", "    if results_df.empty:\n", "        print(\"No results to visualize\")\n", "        return\n", "    \n", "    pile_detections = results_df[results_df['prediction'] == 'PILE']\n", "    \n", "    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))\n", "    \n", "    # Plot 1: Probability heatmap\n", "    sc1 = ax1.scatter(\n", "        results_df['x'], results_df['y'],\n", "        c=results_df['pile_probability'],\n", "        cmap='viridis', s=25, alpha=0.8\n", "    )\n", "    ax1.set_title('Pile Probability Heatmap')\n", "    ax1.set_xlabel('X Coordinate')\n", "    ax1.set_ylabel('Y Coordinate')\n", "    plt.colorbar(sc1, ax=ax1, label='Probability')\n", "    \n", "    # Plot 2: Pile classifications\n", "    ax2.scatter(\n", "        pile_detections['x'], pile_detections['y'],\n", "        color='darkgreen', label='Pile Detections',\n", "        s=30, alpha=0.8\n", "    )\n", "    \n", "    non_pile = results_df[results_df['prediction'] == 'NON-PILE']\n", "    if not non_pile.empty:\n", "        ax2.scatter(\n", "            non_pile['x'], non_pile['y'],\n", "            color='gray', label='Non-Pile',\n", "            s=15, alpha=0.4\n", "        )\n", "    \n", "    ax2.set_title('Pile Classification Results')\n", "    ax2.set_xlabel('X Coordinate')\n", "    ax2.set_ylabel('Y Coordinate')\n", "    ax2.legend()\n", "    \n", "    plt.tight_layout()\n", "    \n", "    plot_path = Path(output_dir) / f\"{site_name}_pile_visualization.png\"\n", "    plt.savefig(plot_path, dpi=300, bbox_inches='tight')\n", "    print(f\"Saved visualization to: {plot_path}\")\n", "    \n", "    plt.show()\n", "    return str(plot_path)"]}, {"cell_type": "code", "execution_count": 15, "id": "1d9a4e23", "metadata": {"execution": {"iopub.execute_input": "2025-08-10T08:33:42.577434Z", "iopub.status.busy": "2025-08-10T08:33:42.577343Z", "iopub.status.idle": "2025-08-10T08:33:42.580641Z", "shell.execute_reply": "2025-08-10T08:33:42.580412Z"}, "papermill": {"duration": 0.006042, "end_time": "2025-08-10T08:33:42.581337", "exception": false, "start_time": "2025-08-10T08:33:42.575295", "status": "completed"}, "tags": []}, "outputs": [], "source": ["def export_results(results_df, output_dir, site_name, model_path, patch_size, confidence_threshold):\n", "    \"\"\"Export results and summary statistics\"\"\"\n", "    if results_df.empty:\n", "        print(\"No results to export\")\n", "        return\n", "    \n", "    timestamp = datetime.now().strftime(\"%Y%m%d_%H%M%S\")\n", "    \n", "    # Export main results\n", "    output_file = Path(output_dir) / f\"{site_name}_pile_detections_{timestamp}.csv\"\n", "    results_df.to_csv(output_file, index=False)\n", "    print(f\"Results exported to: {output_file}\")\n", "    \n", "    # Create summary statistics\n", "    pile_detections = results_df[results_df['prediction'] == 'PILE']\n", "    \n", "    summary = {\n", "        'site_name': site_name,\n", "        'analysis_timestamp': timestamp,\n", "        'total_analysis_points': len(results_df),\n", "        'pile_detections': len(pile_detections),\n", "        'detection_rate': len(pile_detections) / len(results_df),\n", "        'average_pile_probability': float(results_df['pile_probability'].mean()),\n", "        'high_confidence_detections': int(sum(results_df['pile_probability'] > 0.9)),\n", "        'model_path': model_path,\n", "        'patch_size_meters': patch_size,\n", "        'confidence_threshold': confidence_threshold\n", "    }\n", "    \n", "    summary_file = Path(output_dir) / f\"{site_name}_analysis_summary_{timestamp}.json\"\n", "    with open(summary_file, 'w') as f:\n", "        json.dump(summary, f, indent=2)\n", "    \n", "    print(f\"Summary statistics saved to: {summary_file}\")\n", "    \n", "    # Log to MLflow\n", "    for key, value in summary.items():\n", "        if isinstance(value, (int, float)):\n", "            mlflow.log_metric(key, value)\n", "    \n", "    mlflow.log_artifact(str(output_file))\n", "    mlflow.log_artifact(str(summary_file))\n", "    \n", "    return summary\n", "\n"]}, {"cell_type": "code", "execution_count": 16, "id": "4a52cac4", "metadata": {"execution": {"iopub.execute_input": "2025-08-10T08:33:42.585082Z", "iopub.status.busy": "2025-08-10T08:33:42.584999Z", "iopub.status.idle": "2025-08-10T08:33:42.664091Z", "shell.execute_reply": "2025-08-10T08:33:42.663798Z"}, "papermill": {"duration": 0.081923, "end_time": "2025-08-10T08:33:42.664938", "exception": false, "start_time": "2025-08-10T08:33:42.583015", "status": "completed"}, "tags": []}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025/08/10 14:03:42 INFO mlflow.tracking.fluent: Experiment with name 'pointnet_plus_plus_inference' does not exist. Creating a new experiment.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Starting PointNet++ Pile Detection Pipeline\n", "Site: trino_enel\n", "Configuration: 8.0m patches, 1024 points each\n", "Using device: cpu\n"]}], "source": ["print(\"Starting PointNet++ Pile Detection Pipeline\")\n", "print(f\"Site: {NEW_SITE_NAME}\")\n", "print(f\"Configuration: {PATCH_SIZE}m patches, {NUM_POINTS} points each\")\n", "    \n", "# Setup\n", "setup_environment()\n", "device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')\n", "print(f\"Using device: {device}\")"]}, {"cell_type": "code", "execution_count": 17, "id": "6bb565de", "metadata": {"execution": {"iopub.execute_input": "2025-08-10T08:33:42.669295Z", "iopub.status.busy": "2025-08-10T08:33:42.669184Z", "iopub.status.idle": "2025-08-10T08:35:16.126979Z", "shell.execute_reply": "2025-08-10T08:35:16.051042Z"}, "papermill": {"duration": 93.499191, "end_time": "2025-08-10T08:35:16.166008", "exception": false, "start_time": "2025-08-10T08:33:42.666817", "status": "completed"}, "tags": []}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Loaded checkpoint from epoch 97\n", "Loaded trained PointNet++ model with 1,482,434 parameters\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Loaded LAS file with 282,518,678 points\n", "Point cloud bounds:\n"]}, {"name": "stdout", "output_type": "stream", "text": ["  X: 435219.87 to 436795.75\n"]}, {"name": "stdout", "output_type": "stream", "text": ["  Y: 5010811.23 to 5012553.00\n"]}, {"name": "stdout", "output_type": "stream", "text": ["  Z: -7.02 to 31.14\n"]}], "source": ["# Load model\n", "model, model_loaded = load_model(MODEL_PATH, device)\n", "    \n", "# Load point cloud\n", "point_cloud = load_point_cloud(POINT_CLOUD_PATH)\n", "if point_cloud is None:\n", "    print(\"Failed to load point cloud. Exiting.\")\n", "    mlflow.end_run()    "]}, {"cell_type": "code", "execution_count": 18, "id": "703fc072", "metadata": {"execution": {"iopub.execute_input": "2025-08-10T08:35:16.293415Z", "iopub.status.busy": "2025-08-10T08:35:16.290682Z", "iopub.status.idle": "2025-08-10T08:35:16.790458Z", "shell.execute_reply": "2025-08-10T08:35:16.789908Z"}, "papermill": {"duration": 0.591559, "end_time": "2025-08-10T08:35:16.792775", "exception": false, "start_time": "2025-08-10T08:35:16.201216", "status": "completed"}, "tags": []}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Using fast demo grid for thesis demonstration\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Created thesis demo grid: 625 points\n", "Final grid size: 625 points\n", "Processing with 512 points per patch, batch size 8\n"]}], "source": ["# Create analysis grid\n", "#grid_points = create_analysis_grid(point_cloud, GRID_SPACING)\n", "\n", "# Grid creation with site-specific logic\n", "if USE_FAST_MODE:\n", "    print(\"Using fast demo grid for thesis demonstration\")\n", "    # Fast mode - smaller grid for thesis sites\n", "    def create_thesis_demo_grid(point_cloud, max_points=1000):\n", "        \"\"\"Create small, fast grid for thesis demonstration\"\"\"\n", "        # Get point cloud center region (most interesting area)\n", "        x_center = (point_cloud[:, 0].min() + point_cloud[:, 0].max()) / 2\n", "        y_center = (point_cloud[:, 1].min() + point_cloud[:, 1].max()) / 2\n", "        \n", "        # Create grid in 500m x 500m center region\n", "        extent = 250  # 250m radius from center\n", "        x_coords = np.arange(x_center - extent, x_center + extent, GRID_SPACING)\n", "        y_coords = np.arange(y_center - extent, y_center + extent, GRID_SPACING)\n", "        \n", "        full_grid = np.array([[x, y] for x in x_coords for y in y_coords])\n", "        \n", "        # Sample down to manageable size\n", "        if len(full_grid) > max_points:\n", "            indices = np.random.choice(len(full_grid), max_points, replace=False)\n", "            grid_points = full_grid[indices]\n", "        else:\n", "            grid_points = full_grid\n", "            \n", "        print(f\"Created thesis demo grid: {len(grid_points)} points\")\n", "        return grid_points\n", "    \n", "    # Use fast grid\n", "    grid_points = create_thesis_demo_grid(point_cloud, max_points=1000)\n", "    \n", "    # Fast processing parameters\n", "    DEMO_BATCH_SIZE = 8\n", "    DEMO_NUM_POINTS = 512  # Faster processing\n", "    \n", "else:\n", "    print(\"Using standard grid for production analysis\")\n", "    # Standard mode - your existing grid creation\n", "    grid_points = create_analysis_grid(point_cloud, grid_spacing=GRID_SPACING)\n", "    \n", "    # Standard processing parameters  \n", "    DEMO_BATCH_SIZE = BATCH_SIZE\n", "    DEMO_NUM_POINTS = NUM_POINTS\n", "\n", "print(f\"Final grid size: {len(grid_points)} points\")\n", "print(f\"Processing with {DEMO_NUM_POINTS} points per patch, batch size {DEMO_BATCH_SIZE}\")"]}, {"cell_type": "code", "execution_count": 19, "id": "98181738", "metadata": {"execution": {"iopub.execute_input": "2025-08-10T08:35:16.813297Z", "iopub.status.busy": "2025-08-10T08:35:16.811367Z", "iopub.status.idle": "2025-08-10T08:38:54.063146Z", "shell.execute_reply": "2025-08-10T08:38:54.062709Z"}, "papermill": {"duration": 217.269956, "end_time": "2025-08-10T08:38:54.066578", "exception": false, "start_time": "2025-08-10T08:35:16.796622", "status": "completed"}, "tags": []}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Running fast inference for thesis demonstration\n", "Processing 500 points for thesis demo\n", "Building spatial index for 282,518,678 points...\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Pre-filtering valid grid points...\n", "  Pre-filtering progress: 0/500\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Filtered to 500 valid grid points (from 500)\n", "Processing 500 points with 1024 points/patch (CPU optimized)...\n", "This maintains training accuracy but will be slower than 512 points\n", "  Batch 1/63 (0/500 points)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["  <PERSON><PERSON> 4/63 (24/500 points)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["  <PERSON><PERSON> 7/63 (48/500 points)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["  <PERSON>ch 10/63 (72/500 points)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["  <PERSON><PERSON> 13/63 (96/500 points)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["  Batch 16/63 (120/500 points)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["  Batch 19/63 (144/500 points)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["  Batch 22/63 (168/500 points)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["  <PERSON>ch 25/63 (192/500 points)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["  Batch 28/63 (216/500 points)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["  Batch 31/63 (240/500 points)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["  Batch 34/63 (264/500 points)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["  <PERSON><PERSON> 37/63 (288/500 points)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["  <PERSON>ch 40/63 (312/500 points)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["  Batch 43/63 (336/500 points)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["  <PERSON>ch 46/63 (360/500 points)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["  Batch 49/63 (384/500 points)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["  Batch 52/63 (408/500 points)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["  Batch 55/63 (432/500 points)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["  Batch 58/63 (456/500 points)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["  Batch 61/63 (480/500 points)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["CPU processing complete: 500 successful predictions\n"]}], "source": ["# Run inference\n", "# Inference with site-specific parameters\n", "if USE_FAST_MODE:\n", "    print(\"Running fast inference for thesis demonstration\")\n", "    # Fast inference - process subset for demo\n", "    test_grid_size = min(500, len(grid_points))  # Max 500 points\n", "    test_grid = grid_points[:test_grid_size]\n", "    print(f\"Processing {len(test_grid)} points for thesis demo\")\n", "    # Run inference (same function, different parameters)\n", "    results_df = process_site_inference(\n", "        point_cloud,\n", "        grid_points=test_grid,\n", "        model=model,\n", "        device=device,\n", "        batch_size=DEMO_BATCH_SIZE,\n", "        radius=PATCH_SIZE,\n", "        num_points=DEMO_NUM_POINTS\n", "    )\n", "\n", "else:\n", "    print(\"Running full inference for production analysis\")\n", "    # Full inference - process all points\n", "    test_grid = grid_points\n", "    results_df = process_site_inference(\n", "        point_cloud, \n", "        grid_points, \n", "        model, \n", "        device,\n", "        batch_size=BATCH_SIZE, \n", "        radius=PATCH_SIZE, \n", "        num_points=NUM_POINTS\n", "    )\n", "\n"]}, {"cell_type": "code", "execution_count": 20, "id": "8e8f2c88", "metadata": {"execution": {"iopub.execute_input": "2025-08-10T08:38:54.074369Z", "iopub.status.busy": "2025-08-10T08:38:54.074203Z", "iopub.status.idle": "2025-08-10T08:38:54.610767Z", "shell.execute_reply": "2025-08-10T08:38:54.610485Z"}, "papermill": {"duration": 0.541938, "end_time": "2025-08-10T08:38:54.611710", "exception": false, "start_time": "2025-08-10T08:38:54.069772", "status": "completed"}, "tags": []}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "Analysis Results:\n", "  Total analysis points: 500\n", "  Pile detections: 500 (100.0%)\n", "  Average confidence: 1.0000\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Saved visualization to: output_runs/pointnet_plus_plus_harmonized_inference/trino_enel/trino_enel_pile_visualization.png\n"]}, {"data": {"image/png": "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************************************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", "text/plain": ["<Figure size 1500x600 with 3 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["Results exported to: output_runs/pointnet_plus_plus_harmonized_inference/trino_enel/trino_enel_pile_detections_20250810_140854.csv\n", "Summary statistics saved to: output_runs/pointnet_plus_plus_harmonized_inference/trino_enel/trino_enel_analysis_summary_20250810_140854.json\n", "\n", "Pipeline complete. Results saved to: output_runs/pointnet_plus_plus_harmonized_inference/trino_enel\n"]}], "source": ["if not results_df.empty:\n", "    # Print summary\n", "    pile_count = len(results_df[results_df['prediction'] == 'PILE'])\n", "    print(f\"\\nAnalysis Results:\")\n", "    print(f\"  Total analysis points: {len(results_df)}\")\n", "    print(f\"  Pile detections: {pile_count} ({pile_count/len(results_df)*100:.1f}%)\")\n", "    print(f\"  Average confidence: {results_df['pile_probability'].mean():.4f}\")\n", "        \n", "    # Visualize and export\n", "    plot_path = visualize_results(results_df, OUTPUT_DIR, NEW_SITE_NAME)\n", "    \n", "    if plot_path:\n", "        mlflow.log_artifact(plot_path)\n", "\n", "    export_results(results_df, OUTPUT_DIR, NEW_SITE_NAME, \n", "                      MOD<PERSON>_PATH, PATCH_SIZE, CONFIDENCE_THRESHOLD)\n", "    \n", "    # Cleanup\n", "    mlflow.end_run()\n", "    print(f\"\\nPipeline complete. Results saved to: {OUTPUT_DIR}\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}, "papermill": {"default_parameters": {}, "duration": 318.912578, "end_time": "2025-08-10T08:38:55.749202", "environment_variables": {}, "exception": null, "input_path": "00_pointnet_plus_plus_inference_harmonized.ipynb", "output_path": "00_pointnet_plus_plus_inference_harmonized_trino_enel_executed.ipynb", "parameters": {"MODEL_PATH": "best_pointnet_iter4.pth", "NEW_SITE_NAME": "trino_enel", "NUM_POINTS": 1024, "OUTPUT_DIR": "output_runs/pointnet_plus_plus_harmonized_inference/trino_enel", "PATCH_SIZE": 8.0, "POINT_CLOUD_PATH": "../../../../../data/raw/trino_enel/pointcloud/Trino_Fly_2_Shifted.las", "RUN_NAME": "harmonized_inference_trino_enel", "SITE_CRS": "EPSG:32632", "USE_FAST_MODE": "true"}, "start_time": "2025-08-10T08:33:36.836624", "version": "2.6.0"}}, "nbformat": 4, "nbformat_minor": 5}