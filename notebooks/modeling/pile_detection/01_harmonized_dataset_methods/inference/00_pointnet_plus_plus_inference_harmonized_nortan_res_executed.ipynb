{"cells": [{"cell_type": "markdown", "id": "4e86c2a3", "metadata": {"papermill": {"duration": 0.00719, "end_time": "2025-08-10T09:00:53.913900", "exception": false, "start_time": "2025-08-10T09:00:53.906710", "status": "completed"}, "tags": []}, "source": ["# PointNet++ Model Application to New Site\n", "This notebook applies the trained PointNet++ model to a new construction site\n", "with different data sources (DWG files instead of KML regions).\n", "\n", "**Workflow:**\n", "1. <PERSON><PERSON> trained PointNet++ model\n", "2. Process new site point cloud data\n", "3. Generate predictions across the site\n", "4. Compare with DWG-based pile locations (if available)\n", "5. Validate and export results\n", "\n", "**Author**: <PERSON><PERSON><PERSON>  \n", "**Date**: July 2025  \n", "**Project**: As-Built Foundation Analysis - Site Transfer"]}, {"cell_type": "code", "execution_count": 1, "id": "parameters", "metadata": {"execution": {"iopub.execute_input": "2025-08-10T09:00:53.922594Z", "iopub.status.busy": "2025-08-10T09:00:53.922283Z", "iopub.status.idle": "2025-08-10T09:00:53.929645Z", "shell.execute_reply": "2025-08-10T09:00:53.929278Z"}, "papermill": {"duration": 0.012973, "end_time": "2025-08-10T09:00:53.930970", "exception": false, "start_time": "2025-08-10T09:00:53.917997", "status": "completed"}, "tags": ["parameters"]}, "outputs": [], "source": ["# Parameters cell for Papermill execution\n", "NEW_SITE_NAME = \"althea_rpcs\"\n", "POINT_CLOUD_PATH = \"../../../../../data/raw/althea_rpcs/pointcloud/Point_Cloud.las\"\n", "OUTPUT_DIR = f\"output_runs/pointnet_plus_plus_inference/{NEW_SITE_NAME}\"\n", "\n", "MODEL_PATH = \"best_pointnet_iter4.pth\"  # Path to trained model\n", "\n", "DWG_PATH = \"\"  # Path to DWG file (optional)\n", "OUTPUT_DIR = \"output_runs/pointnet_plus_plus_inference\"  # Output directory\n", "\n", "# Model parameters (must match training)\n", "CONFIDENCE_THRESHOLD = 0.3  # Confidence threshold for pile detection\n", "\n", "GRID_SPACING = 20.0 # Grid spacing for analysis points\n", "PATCH_SIZE = 8.0  # meters radius for patch extraction - FIXED: harmonized training uses 8m\n", "NUM_POINTS = 1024 # KEEP 1024 to match training - CRITICAL for accuracy\n", "BATCH_SIZE = 8   # Batch size for inference\n", "\n", "# CRS and coordinate validation parameters\n", "SITE_CRS = \"EPSG:32615\"  # Will be set by papermill\n", "\n", "USE_FAST_MODE = False  # Overwritten by papermill\n", "\n", "# Enhanced analysis parameters\n", "EXTENDED_ANALYSIS = True\n", "NEGATIVE_SAMPLE_DISTANCE = 8.0  # Distance for synthetic negatives\n", "FULL_SITE_ANALYSIS = True  # Run on all points, not subset\n", "\n", "# MLflow configuration\n", "EXPERIMENT_NAME = \"pointnet_plus_plus_inference\"\n", "RUN_NAME = f\"inference_{NEW_SITE_NAME}\""]}, {"cell_type": "code", "execution_count": 2, "id": "7d97dfa7", "metadata": {"execution": {"iopub.execute_input": "2025-08-10T09:00:53.965985Z", "iopub.status.busy": "2025-08-10T09:00:53.965837Z", "iopub.status.idle": "2025-08-10T09:00:53.967999Z", "shell.execute_reply": "2025-08-10T09:00:53.967752Z"}, "papermill": {"duration": 0.005658, "end_time": "2025-08-10T09:00:53.968837", "exception": false, "start_time": "2025-08-10T09:00:53.963179", "status": "completed"}, "tags": ["injected-parameters"]}, "outputs": [], "source": ["# Parameters\n", "NEW_SITE_NAME = \"nortan_res\"\n", "POINT_CLOUD_PATH = \"../../../../../data/raw/nortan_res/pointcloud/Block_11_2m.las\"\n", "SITE_CRS = \"EPSG:32614\"\n", "USE_FAST_MODE = \"false\"\n", "OUTPUT_DIR = \"output_runs/pointnet_plus_plus_harmonized_inference/nortan_res\"\n", "RUN_NAME = \"harmonized_inference_nortan_res\"\n", "PATCH_SIZE = 8.0\n", "NUM_POINTS = 1024\n", "MODEL_PATH = \"best_pointnet_iter4.pth\"\n"]}, {"cell_type": "code", "execution_count": 3, "id": "f22a587b", "metadata": {"execution": {"iopub.execute_input": "2025-08-10T09:00:53.973359Z", "iopub.status.busy": "2025-08-10T09:00:53.973232Z", "iopub.status.idle": "2025-08-10T09:00:53.975720Z", "shell.execute_reply": "2025-08-10T09:00:53.975488Z"}, "papermill": {"duration": 0.005666, "end_time": "2025-08-10T09:00:53.976556", "exception": false, "start_time": "2025-08-10T09:00:53.970890", "status": "completed"}, "tags": []}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["New Site Analysis Configuration:\n", "Site: nortan_res\n", "Using fast mode: false\n", "EPSG Configuration:\n", "  Inference EPSG: EPSG:32614\n", "Patch Size: 8.0m radius\n", "Points per Patch: 1024\n", "Model Path: best_pointnet_iter4.pth\n", "Point Cloud Path: ../../../../../data/raw/nortan_res/pointcloud/Block_11_2m.las\n", "Output Directory: output_runs/pointnet_plus_plus_harmonized_inference/nortan_res\n", "Extended Analysis: True\n", "Full Site Analysis: True\n"]}], "source": ["# Display configuration\n", "print(f\"New Site Analysis Configuration:\")\n", "print(f\"Site: {NEW_SITE_NAME}\")\n", "print(f\"Using fast mode: {USE_FAST_MODE}\")\n", "print(f\"EPSG Configuration:\")\n", "print(f\"  Inference EPSG: {SITE_CRS}\")\n", "print(f\"Patch Size: {PATCH_SIZE}m radius\")\n", "print(f\"Points per Patch: {NUM_POINTS}\")\n", "print(f\"Model Path: {MODEL_PATH}\")\n", "print(f\"Point Cloud Path: {POINT_CLOUD_PATH}\")\n", "print(f\"Output Directory: {OUTPUT_DIR}\")\n", "print(f\"Extended Analysis: {EXTENDED_ANALYSIS}\")\n", "print(f\"Full Site Analysis: {FULL_SITE_ANALYSIS}\")\n", "\n"]}, {"cell_type": "code", "execution_count": 4, "id": "6b291543", "metadata": {"execution": {"iopub.execute_input": "2025-08-10T09:00:53.980514Z", "iopub.status.busy": "2025-08-10T09:00:53.980410Z", "iopub.status.idle": "2025-08-10T09:00:58.218310Z", "shell.execute_reply": "2025-08-10T09:00:58.218020Z"}, "papermill": {"duration": 4.240941, "end_time": "2025-08-10T09:00:58.219319", "exception": false, "start_time": "2025-08-10T09:00:53.978378", "status": "completed"}, "tags": []}, "outputs": [], "source": ["import os\n", "import json\n", "import pickle\n", "import torch\n", "import warnings\n", "import numpy as np\n", "import pandas as pd\n", "from pathlib import Path\n", "from datetime import datetime\n", "import torch.nn as nn\n", "import matplotlib.pyplot as plt\n", "import laspy\n", "import open3d as o3d\n", "import mlflow\n", "import mlflow.pytorch\n", "from scipy.spatial import cKDTree\n", "from scipy.spatial.distance import pdist\n", "import seaborn as sns\n", "\n", "warnings.filterwarnings('ignore')\n"]}, {"cell_type": "code", "execution_count": 5, "id": "setup_output_dir", "metadata": {"execution": {"iopub.execute_input": "2025-08-10T09:00:58.223716Z", "iopub.status.busy": "2025-08-10T09:00:58.223491Z", "iopub.status.idle": "2025-08-10T09:00:58.225777Z", "shell.execute_reply": "2025-08-10T09:00:58.225561Z"}, "papermill": {"duration": 0.005195, "end_time": "2025-08-10T09:00:58.226512", "exception": false, "start_time": "2025-08-10T09:00:58.221317", "status": "completed"}, "tags": []}, "outputs": [], "source": ["def setup_environment():\n", "    \"\"\"Setup output directory and MLflow tracking\"\"\"\n", "    os.makedirs(OUTPUT_DIR, exist_ok=True)\n", "    \n", "    if mlflow.active_run() is not None:\n", "        mlflow.end_run()\n", "    \n", "    mlflow.set_experiment(EXPERIMENT_NAME)\n", "    mlflow.start_run(run_name=RUN_NAME)\n", "    \n", "    # Log parameters\n", "    params = {\n", "        \"site_name\": NEW_SITE_NAME,\n", "        \"patch_size\": PATCH_SIZE,\n", "        \"num_points\": NUM_POINTS,\n", "        \"confidence_threshold\": CONFIDENCE_THRESHOLD,\n", "        \"batch_size\": BATCH_SIZE,\n", "        \"grid_spacing\": GRID_SPACING\n", "    }\n", "    for key, value in params.items():\n", "        mlflow.log_param(key, value)"]}, {"cell_type": "markdown", "id": "a0315f2c", "metadata": {"papermill": {"duration": 0.001596, "end_time": "2025-08-10T09:00:58.229750", "exception": false, "start_time": "2025-08-10T09:00:58.228154", "status": "completed"}, "tags": []}, "source": ["## PointNet++ Utility Functions "]}, {"cell_type": "code", "execution_count": 6, "id": "f547b445", "metadata": {"execution": {"iopub.execute_input": "2025-08-10T09:00:58.233626Z", "iopub.status.busy": "2025-08-10T09:00:58.233529Z", "iopub.status.idle": "2025-08-10T09:00:58.238076Z", "shell.execute_reply": "2025-08-10T09:00:58.237865Z"}, "papermill": {"duration": 0.007467, "end_time": "2025-08-10T09:00:58.238794", "exception": false, "start_time": "2025-08-10T09:00:58.231327", "status": "completed"}, "tags": []}, "outputs": [], "source": ["# PointNet++ utility functions\n", "def square_distance(src, dst):\n", "    \"\"\"Calculate squared distance between two point sets\"\"\"\n", "    B, N, _ = src.shape\n", "    _, M, _ = dst.shape\n", "    dist = -2 * torch.matmul(src, dst.permute(0, 2, 1))\n", "    dist += torch.sum(src ** 2, -1).view(B, N, 1)\n", "    dist += torch.sum(dst ** 2, -1).view(B, 1, M)\n", "    return dist\n", "\n", "def farthest_point_sample(xyz, npoint):\n", "    \"\"\"Farthest point sampling\"\"\"\n", "    device = xyz.device\n", "    B, N, C = xyz.shape\n", "    centroids = torch.zeros(B, npoint, dtype=torch.long).to(device)\n", "    distance = torch.ones(B, N).to(device) * 1e10\n", "    farthest = torch.randint(0, N, (B,), dtype=torch.long).to(device)\n", "    batch_indices = torch.arange(B, dtype=torch.long).to(device)\n", "    \n", "    for i in range(npoint):\n", "        centroids[:, i] = farthest\n", "        centroid = xyz[batch_indices, farthest, :].view(B, 1, 3)\n", "        dist = torch.sum((xyz - centroid) ** 2, -1)\n", "        mask = dist < distance\n", "        distance[mask] = dist[mask]\n", "        farthest = torch.max(distance, -1)[1]\n", "    \n", "    return centroids\n", "\n", "def query_ball_point(radius, nsample, xyz, new_xyz):\n", "    \"\"\"Query ball point grouping\"\"\"\n", "    device = xyz.device\n", "    B, N, C = xyz.shape\n", "    _, S, _ = new_xyz.shape\n", "    group_idx = torch.arange(N, dtype=torch.long).to(device).view(1, 1, N).repeat([B, S, 1])\n", "    sqrdists = square_distance(new_xyz, xyz)\n", "    group_idx[sqrdists > radius ** 2] = N\n", "    group_idx = group_idx.sort(dim=-1)[0][:, :, :nsample]\n", "    group_first = group_idx[:, :, 0].view(B, S, 1).repeat([1, 1, nsample])\n", "    mask = group_idx == N\n", "    group_idx[mask] = group_first[mask]\n", "    return group_idx"]}, {"cell_type": "markdown", "id": "8cbfede7", "metadata": {"papermill": {"duration": 0.001587, "end_time": "2025-08-10T09:00:58.242267", "exception": false, "start_time": "2025-08-10T09:00:58.240680", "status": "completed"}, "tags": []}, "source": ["## PointNet++ Set Abstraction Layer\n"]}, {"cell_type": "code", "execution_count": 7, "id": "bf8dd055", "metadata": {"execution": {"iopub.execute_input": "2025-08-10T09:00:58.246171Z", "iopub.status.busy": "2025-08-10T09:00:58.246065Z", "iopub.status.idle": "2025-08-10T09:00:58.250249Z", "shell.execute_reply": "2025-08-10T09:00:58.250039Z"}, "papermill": {"duration": 0.006999, "end_time": "2025-08-10T09:00:58.251030", "exception": false, "start_time": "2025-08-10T09:00:58.244031", "status": "completed"}, "tags": []}, "outputs": [], "source": ["class PointNetSetAbstraction(nn.Module):\n", "    def __init__(self, npoint, radius, nsample, in_channel, mlp, group_all):\n", "        super(PointNetSetAbstraction, self).__init__()\n", "        self.npoint = npoint\n", "        self.radius = radius\n", "        self.nsample = nsample\n", "        self.mlp_convs = nn.ModuleList()\n", "        self.mlp_bns = nn.ModuleList()\n", "        self.group_all = group_all\n", "        \n", "        last_channel = in_channel\n", "        for out_channel in mlp:\n", "            self.mlp_convs.append(nn.Conv2d(last_channel, out_channel, 1))\n", "            self.mlp_bns.append(nn.BatchNorm2d(out_channel))\n", "            last_channel = out_channel\n", "    \n", "    def forward(self, xyz, points):\n", "        \"\"\"Forward pass of PointNet++ Set Abstraction layer\"\"\"\n", "        B, N, C = xyz.shape\n", "        \n", "        if self.group_all:\n", "            new_xyz = torch.zeros(B, 1, C).to(xyz.device)\n", "            new_points = points.view(B, 1, N, -1) if points is not None else xyz.view(B, 1, N, C)\n", "        else:\n", "            fps_idx = farthest_point_sample(xyz, self.npoint)\n", "            new_xyz = xyz[torch.arange(B)[:, None], fps_idx]\n", "            idx = query_ball_point(self.radius, self.nsample, xyz, new_xyz)\n", "            grouped_xyz = xyz[torch.arange(B)[:, None, None], idx]\n", "            grouped_xyz_norm = grouped_xyz - new_xyz.view(B, self.npoint, 1, C)\n", "            \n", "            if points is not None:\n", "                grouped_points = points[torch.arange(B)[:, None, None], idx]\n", "                new_points = torch.cat([grouped_xyz_norm, grouped_points], dim=-1)\n", "            else:\n", "                new_points = grouped_xyz_norm\n", "        \n", "        new_points = new_points.permute(0, 3, 2, 1)\n", "        for i, conv in enumerate(self.mlp_convs):\n", "            bn = self.mlp_bns[i]\n", "            new_points = torch.relu(bn(conv(new_points)))\n", "        \n", "        new_points = torch.max(new_points, 2)[0]\n", "        new_points = new_points.permute(0, 2, 1)\n", "        \n", "        return new_xyz, new_points"]}, {"cell_type": "markdown", "id": "d5232c15", "metadata": {"papermill": {"duration": 0.001614, "end_time": "2025-08-10T09:00:58.254383", "exception": false, "start_time": "2025-08-10T09:00:58.252769", "status": "completed"}, "tags": []}, "source": ["## PointNet++ Model Architecture\n"]}, {"cell_type": "code", "execution_count": 8, "id": "3fb10d9f", "metadata": {"execution": {"iopub.execute_input": "2025-08-10T09:00:58.258298Z", "iopub.status.busy": "2025-08-10T09:00:58.258196Z", "iopub.status.idle": "2025-08-10T09:00:58.262142Z", "shell.execute_reply": "2025-08-10T09:00:58.261922Z"}, "papermill": {"duration": 0.006831, "end_time": "2025-08-10T09:00:58.262876", "exception": false, "start_time": "2025-08-10T09:00:58.256045", "status": "completed"}, "tags": []}, "outputs": [], "source": ["class PointNetPlusPlus(nn.Module):\n", "    def __init__(self, num_classes=2, in_channels=20, dropout=0.3):  # ✅ CRITICAL: in_channels=20\n", "        super(PointNetPlusPlus, self).__init__()\n", "\n", "        # Set Abstraction Layers (SAME AS TRAINING)\n", "        self.sa1 = PointNetSetAbstraction(256, 0.2, 32, in_channels, [64, 64, 128], False)\n", "        self.sa2 = PointNetSetAbstraction(64, 0.4, 64, 128 + 3, [128, 128, 256], False)\n", "        self.sa3 = PointNetSetAbstraction(None, None, None, 256, [256, 512, 1024], True)\n", "\n", "        # Classification head (SAME AS TRAINING)\n", "        self.fc1 = nn.Linear(1024, 512)\n", "        self.bn1 = nn.BatchNorm1d(512)\n", "        self.drop1 = nn.Dropout(dropout)\n", "\n", "        self.fc2 = nn.Linear(512, 256)\n", "        self.bn2 = nn.BatchNorm1d(256)\n", "        self.drop2 = nn.Dropout(dropout)\n", "\n", "        self.fc3 = nn.Linear(256, 64)\n", "        self.bn3 = nn.<PERSON><PERSON><PERSON>orm1d(64)\n", "        self.drop3 = nn.Dropout(dropout * 0.6)\n", "\n", "        self.fc4 = nn.Linear(64, num_classes)\n", "\n", "    def forward(self, xyz):\n", "        # Input shape: (B, N, C), C = in_channels (20 features)\n", "        if len(xyz.shape) == 4:\n", "            xyz = xyz.squeeze(1)\n", "\n", "        B, N, C = xyz.shape\n", "\n", "        # Split input into xyz coords and features (SAME AS TRAINING)\n", "        coords = xyz[:, :, :3]       # (B, N, 3)\n", "        features = xyz[:, :, 3:]     # (B, N, C-3) = (B, N, 17)\n", "\n", "        # Pass through SA layers\n", "        l1_xyz, l1_points = self.sa1(coords, features)\n", "        l2_xyz, l2_points = self.sa2(l1_xyz, l1_points)\n", "        l3_xyz, l3_points = self.sa3(l2_xyz, l2_points)\n", "\n", "        global_feat = l3_points.view(B, -1)\n", "\n", "        # Classification head\n", "        x = self.drop1(torch.relu(self.bn1(self.fc1(global_feat))))\n", "        x = self.drop2(torch.relu(self.bn2(self.fc2(x))))\n", "        x = self.drop3(torch.relu(self.bn3(self.fc3(x))))\n", "        x = self.fc4(x)\n", "\n", "        return x"]}, {"cell_type": "markdown", "id": "2f5e408b", "metadata": {"papermill": {"duration": 0.001751, "end_time": "2025-08-10T09:00:58.266461", "exception": false, "start_time": "2025-08-10T09:00:58.264710", "status": "completed"}, "tags": []}, "source": ["## Load"]}, {"cell_type": "code", "execution_count": 9, "id": "3838119b", "metadata": {"execution": {"iopub.execute_input": "2025-08-10T09:00:58.270254Z", "iopub.status.busy": "2025-08-10T09:00:58.270160Z", "iopub.status.idle": "2025-08-10T09:00:58.273617Z", "shell.execute_reply": "2025-08-10T09:00:58.273377Z"}, "papermill": {"duration": 0.00621, "end_time": "2025-08-10T09:00:58.274393", "exception": false, "start_time": "2025-08-10T09:00:58.268183", "status": "completed"}, "tags": []}, "outputs": [], "source": ["def load_point_cloud(file_path):\n", "    \"\"\"Load point cloud from supported formats (.las, .ply, .pcd)\"\"\"\n", "    file_path = Path(file_path)\n", "    \n", "    if not file_path.exists():\n", "        print(f\"File not found: {file_path}\")\n", "        return None\n", "    \n", "    suffix = file_path.suffix.lower()\n", "    \n", "    try:\n", "        if suffix == '.las':\n", "            las_file = laspy.read(file_path)\n", "            points = np.vstack([las_file.x, las_file.y, las_file.z]).T\n", "            print(f\"Loaded LAS file with {len(points):,} points\")\n", "                \n", "        elif suffix in ['.ply', '.pcd']:\n", "            pcd = o3d.io.read_point_cloud(str(file_path))\n", "            points = np.asarray(pcd.points)\n", "            \n", "            if len(points) == 0:\n", "                print(f\"Warning: {suffix.upper()} file contains no points\")\n", "                return None\n", "                \n", "            print(f\"Loaded {suffix.upper()} file with {len(points):,} points\")\n", "                \n", "        else:\n", "            print(f\"Unsupported file format: {suffix}. Supported formats: .las, .ply, .pcd\")\n", "            return None\n", "        \n", "        if len(points) == 0:\n", "            print(\"Warning: Point cloud contains no points\")\n", "            return None\n", "            \n", "        print(f\"Point cloud bounds:\")\n", "        print(f\"  X: {points[:, 0].min():.2f} to {points[:, 0].max():.2f}\")\n", "        print(f\"  Y: {points[:, 1].min():.2f} to {points[:, 1].max():.2f}\")\n", "        print(f\"  Z: {points[:, 2].min():.2f} to {points[:, 2].max():.2f}\")\n", "        \n", "        return points\n", "        \n", "    except Exception as e:\n", "        print(f\"Error loading point cloud: {e}\")\n", "        return None"]}, {"cell_type": "code", "execution_count": 10, "id": "model_validation", "metadata": {"execution": {"iopub.execute_input": "2025-08-10T09:00:58.278256Z", "iopub.status.busy": "2025-08-10T09:00:58.278171Z", "iopub.status.idle": "2025-08-10T09:00:58.280965Z", "shell.execute_reply": "2025-08-10T09:00:58.280728Z"}, "papermill": {"duration": 0.005527, "end_time": "2025-08-10T09:00:58.281726", "exception": false, "start_time": "2025-08-10T09:00:58.276199", "status": "completed"}, "tags": []}, "outputs": [], "source": ["def load_model(model_path, device):\n", "    \"\"\"Load trained PointNet++ model\"\"\"\n", "    try:\n", "        model = PointNetPlusPlus(num_classes=2, in_channels=20, dropout=0.3).to(device)\n", "        \n", "        if Path(model_path).exists():\n", "            checkpoint = torch.load(model_path, map_location=device)\n", "            \n", "            if isinstance(checkpoint, dict) and 'model_state_dict' in checkpoint:\n", "                model.load_state_dict(checkpoint['model_state_dict'])\n", "                print(f\"Loaded checkpoint from epoch {checkpoint.get('epoch', 'unknown')}\")\n", "            else:\n", "                model.load_state_dict(checkpoint)\n", "            \n", "            model.eval()\n", "            param_count = sum(p.numel() for p in model.parameters())\n", "            print(f\"Loaded trained PointNet++ model with {param_count:,} parameters\")\n", "            return model, True\n", "        else:\n", "            print(f\"Model file not found: {model_path}\")\n", "            return model, False\n", "            \n", "    except Exception as e:\n", "        print(f\"Error loading model: {e}\")\n", "        return PointNetPlusPlus(num_classes=2, in_channels=20, dropout=0.3).to(device), False"]}, {"cell_type": "code", "execution_count": 11, "id": "a5f0d927", "metadata": {"execution": {"iopub.execute_input": "2025-08-10T09:00:58.285608Z", "iopub.status.busy": "2025-08-10T09:00:58.285513Z", "iopub.status.idle": "2025-08-10T09:00:58.289013Z", "shell.execute_reply": "2025-08-10T09:00:58.288744Z"}, "papermill": {"duration": 0.006356, "end_time": "2025-08-10T09:00:58.289877", "exception": false, "start_time": "2025-08-10T09:00:58.283521", "status": "completed"}, "tags": []}, "outputs": [], "source": ["def create_analysis_grid(point_cloud, grid_spacing=15.0, buffer_factor=1.5):\n", "    \"\"\"Create analysis grid with spatial bounds optimization\"\"\"\n", "    print(f\"Point cloud has {len(point_cloud):,} points\")\n", "    \n", "    # Get bounds efficiently\n", "    x_min, x_max = point_cloud[:, 0].min(), point_cloud[:, 0].max()\n", "    y_min, y_max = point_cloud[:, 1].min(), point_cloud[:, 1].max()\n", "    \n", "    print(f\"Point cloud bounds: X({x_min:.1f}, {x_max:.1f}), Y({y_min:.1f}, {y_max:.1f})\")\n", "    \n", "    buffer = grid_spacing * buffer_factor\n", "    x_coords = np.arange(x_min - buffer, x_max + buffer, grid_spacing)\n", "    y_coords = np.arange(y_min - buffer, y_max + buffer, grid_spacing)\n", "    \n", "    grid_points = np.array([[x, y] for x in x_coords for y in y_coords])\n", "    print(f\"Created analysis grid with {len(grid_points)} points\")\n", "    \n", "    return grid_points\n", "\n", "def create_analysis_smart_grid(point_cloud, grid_spacing=5.0):\n", "    \"\"\"Create analysis grid with spatial bounds optimization\"\"\"\n", "    print(f\"Point cloud has {len(point_cloud):,} points\")\n", "    \n", "    x_min, x_max = point_cloud[:, 0].min(), point_cloud[:, 0].max()\n", "    y_min, y_max = point_cloud[:, 1].min(), point_cloud[:, 1].max()\n", "    \n", "    print(f\"Point cloud bounds: X({x_min:.1f}, {x_max:.1f}), Y({y_min:.1f}, {y_max:.1f})\")\n", "    \n", "    x_coords = np.arange(x_min, x_max, grid_spacing)\n", "    y_coords = np.arange(y_min, y_max, grid_spacing)\n", "    \n", "    grid_points = np.array([[x, y] for x in x_coords for y in y_coords])\n", "    print(f\"Created analysis grid with {len(grid_points)} points\")\n", "    \n", "    return grid_points\n"]}, {"cell_type": "code", "execution_count": 12, "id": "2d36b96c", "metadata": {"execution": {"iopub.execute_input": "2025-08-10T09:00:58.293813Z", "iopub.status.busy": "2025-08-10T09:00:58.293700Z", "iopub.status.idle": "2025-08-10T09:00:58.299823Z", "shell.execute_reply": "2025-08-10T09:00:58.299609Z"}, "papermill": {"duration": 0.008827, "end_time": "2025-08-10T09:00:58.300526", "exception": false, "start_time": "2025-08-10T09:00:58.291699", "status": "completed"}, "tags": []}, "outputs": [], "source": ["def extract_patch_features(point_cloud, center_xy, radius=20.0, num_points=1024, kdtree=None):\n", "    \"\"\"Extract patch features EXACTLY matching training preprocessing\"\"\"\n", "    center_x, center_y = center_xy\n", "    \n", "    if kdtree is not None:\n", "        indices = kdtree.query_ball_point([center_x, center_y], radius)\n", "        if len(indices) < 50:\n", "            return None\n", "        patch_xyz = point_cloud[indices]\n", "    else:\n", "        distances_2d = np.sqrt((point_cloud[:, 0] - center_x)**2 + \n", "                              (point_cloud[:, 1] - center_y)**2)\n", "        mask = distances_2d <= radius\n", "        patch_xyz = point_cloud[mask]\n", "        \n", "        if len(patch_xyz) < 50:\n", "            return None\n", "    \n", "    # MATCH training preprocessing exactly - use same feature engineering as training data creation\n", "    x, y, z = patch_xyz[:, 0], patch_xyz[:, 1], patch_xyz[:, 2]\n", "    \n", "    # Calculate patch statistics\n", "    patch_center = np.mean(patch_xyz, axis=0)\n", "    z_mean, z_std = z.mean(), z.std() + 1e-6\n", "    z_min, z_max = z.min(), z.max()\n", "    \n", "    # Distance calculations\n", "    dist_to_center = np.sqrt((x - center_x)**2 + (y - center_y)**2)\n", "    \n", "    # RECREATE EXACT TRAINING FEATURES - these come from the original data creation process\n", "    features_list = []\n", "    for i, point in enumerate(patch_xyz):\n", "        px, py, pz = point\n", "        \n", "        # Match the exact feature engineering from training data preparation\n", "        feature_vector = [\n", "            px,  # 0: raw X coordinate\n", "            py - center_y,  # 1: relative Y (small values like training)\n", "            pz,  # 2: raw Z coordinate  \n", "            (pz - z_mean) / z_std,  # 3: height_norm\n", "            dist_to_center[i],  # 4: distance_norm\n", "            len(patch_xyz) / 1000.0,  # 5: density\n", "            px - center_x,  # 6: relative_x\n", "            py - center_y,  # 7: relative_y  \n", "            pz - patch_center[2],  # 8: relative_z\n", "            pz - z_min,  # 9: height_above_min\n", "            z_max - pz,  # 10: depth_below_max\n", "            abs(pz - z_mean),  # 11: abs_height_deviation\n", "            np.sqrt(px**2 + py**2),  # 12: distance_from_origin\n", "            np.arctan2(py - center_y, px - center_x),  # 13: angle\n", "            (px - center_x) * (py - center_y),  # 14: interaction\n", "            px - patch_center[0],  # 15: patch_relative_x\n", "            py - patch_center[1],  # 16: patch_relative_y\n", "            pz / (dist_to_center[i] + 1e-6),  # 17: height_distance_ratio\n", "            np.sqrt((px - center_x)**2 + (py - center_y)**2 + (pz - z_mean)**2),  # 18: 3d_distance\n", "            dist_to_center[i] + np.random.normal(0, 0.01)  # 19: distance with slight variation\n", "        ]\n", "        features_list.append(feature_vector)\n", "    \n", "    patch_features = np.array(features_list, dtype=np.float32)\n", "    \n", "    # EXACT SAME SAMPLING LOGIC AS TRAINING\n", "    if len(patch_features) >= num_points:\n", "        # Distance-weighted sampling like training\n", "        distances = patch_features[:, 4]  # distance_norm column\n", "        probabilities = 1 / (distances + 0.1)\n", "        probabilities /= probabilities.sum()\n", "        \n", "        sampled_indices = np.random.choice(len(patch_features), num_points, \n", "                                         replace=False, p=probabilities)\n", "        sampled = patch_features[sampled_indices]\n", "    else:\n", "        # Upsample with weighted selection like training\n", "        upsampled = patch_features.copy()\n", "        needed = num_points - len(patch_features)\n", "        \n", "        for _ in range(needed):\n", "            distances = patch_features[:, 4]\n", "            weights = 1 / (distances + 0.1)\n", "            weights /= weights.sum()\n", "            source_idx = np.random.choice(len(patch_features), p=weights)\n", "            \n", "            new_point = patch_features[source_idx].copy()\n", "            new_point[:3] += np.random.normal(0, 0.02, 3)  # Add noise to spatial like training\n", "            upsampled = np.vstack([upsampled, new_point])\n", "        \n", "        sampled = upsampled[:num_points]\n", "    \n", "    # EXACT SAME NORMALIZATION AS TRAINING\n", "    # Training shows: sampled /= np.max(np.linalg.norm(sampled, axis=1)) or 1\n", "    spatial_coords = sampled[:, :3]\n", "    spatial_extent = np.max(np.linalg.norm(spatial_coords, axis=1))\n", "    if spatial_extent > 0:\n", "        sampled[:, :3] /= spatial_extent\n", "    \n", "    return sampled\n", "\n"]}, {"cell_type": "code", "execution_count": 13, "id": "8444b060", "metadata": {"execution": {"iopub.execute_input": "2025-08-10T09:00:58.304654Z", "iopub.status.busy": "2025-08-10T09:00:58.304547Z", "iopub.status.idle": "2025-08-10T09:00:58.309233Z", "shell.execute_reply": "2025-08-10T09:00:58.309013Z"}, "papermill": {"duration": 0.007613, "end_time": "2025-08-10T09:00:58.310030", "exception": false, "start_time": "2025-08-10T09:00:58.302417", "status": "completed"}, "tags": []}, "outputs": [], "source": ["def process_site_inference(point_cloud, grid_points, model, device, \n", "                          batch_size=16, radius=20.0, num_points=1024):\n", "    \"\"\"CPU-optimized inference maintaining 1024 points for accuracy\"\"\"\n", "    print(f\"Building spatial index for {len(point_cloud):,} points...\")\n", "    kdtree = cKDTree(point_cloud[:, :2])\n", "    \n", "    print(\"Pre-filtering valid grid points...\")\n", "    valid_grid_points = []\n", "    for i, center in enumerate(grid_points):\n", "        if i % 500 == 0:\n", "            print(f\"  Pre-filtering progress: {i}/{len(grid_points)}\")\n", "        \n", "        indices = kdtree.query_ball_point([center[0], center[1]], radius)\n", "        if len(indices) >= 50:  # Keep higher threshold for 1024 points\n", "            valid_grid_points.append(center)\n", "    \n", "    valid_grid_points = np.array(valid_grid_points)\n", "    print(f\"Filtered to {len(valid_grid_points)} valid grid points (from {len(grid_points)})\")\n", "    \n", "    if len(valid_grid_points) == 0:\n", "        return pd.DataFrame()\n", "    \n", "    results = []\n", "    total_batches = len(valid_grid_points) // batch_size + (1 if len(valid_grid_points) % batch_size != 0 else 0)\n", "    \n", "    print(f\"Processing {len(valid_grid_points)} points with 1024 points/patch (CPU optimized)...\")\n", "    print(f\"This maintains training accuracy but will be slower than 512 points\")\n", "    \n", "    model.eval()\n", "    if device.type == 'cpu':\n", "        torch.set_num_threads(4)\n", "    \n", "    for i in range(0, len(valid_grid_points), batch_size):\n", "        batch_idx = i // batch_size + 1\n", "        if batch_idx % 3 == 1:  # More frequent updates since batches are smaller\n", "            print(f\"  Batch {batch_idx}/{total_batches} ({i}/{len(valid_grid_points)} points)\")\n", "        \n", "        batch_centers = valid_grid_points[i:i+batch_size]\n", "        batch_patches = []\n", "        valid_centers = []\n", "        \n", "        for center in batch_centers:\n", "            patch = extract_patch_features(point_cloud, center, radius, num_points, kdtree)\n", "            if patch is not None:\n", "                batch_patches.append(patch)\n", "                valid_centers.append(center)\n", "        \n", "        if len(batch_patches) == 0:\n", "            continue\n", "        \n", "        batch_tensor = torch.FloatTensor(np.array(batch_patches)).to(device)\n", "        \n", "        with torch.no_grad():\n", "            outputs = model(batch_tensor)\n", "            probabilities = torch.softmax(outputs, dim=1)\n", "            pile_probs = probabilities[:, 1].cpu().numpy()\n", "        \n", "        for center, prob in zip(valid_centers, pile_probs):\n", "            results.append({\n", "                'x': center[0],\n", "                'y': center[1],\n", "                'pile_probability': prob,\n", "                'prediction': 'PILE' if prob > CONFIDENCE_THRESHOLD else 'NON-PILE'\n", "            })\n", "    \n", "    print(f\"CPU processing complete: {len(results)} successful predictions\")\n", "    return pd.DataFrame(results)\n"]}, {"cell_type": "code", "execution_count": 14, "id": "89ce4683", "metadata": {"execution": {"iopub.execute_input": "2025-08-10T09:00:58.314001Z", "iopub.status.busy": "2025-08-10T09:00:58.313867Z", "iopub.status.idle": "2025-08-10T09:00:58.317055Z", "shell.execute_reply": "2025-08-10T09:00:58.316827Z"}, "papermill": {"duration": 0.005909, "end_time": "2025-08-10T09:00:58.317768", "exception": false, "start_time": "2025-08-10T09:00:58.311859", "status": "completed"}, "tags": []}, "outputs": [], "source": ["def visualize_results(results_df, output_dir, site_name):\n", "    \"\"\"Create visualization of pile detection results\"\"\"\n", "    if results_df.empty:\n", "        print(\"No results to visualize\")\n", "        return\n", "    \n", "    pile_detections = results_df[results_df['prediction'] == 'PILE']\n", "    \n", "    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))\n", "    \n", "    # Plot 1: Probability heatmap\n", "    sc1 = ax1.scatter(\n", "        results_df['x'], results_df['y'],\n", "        c=results_df['pile_probability'],\n", "        cmap='viridis', s=25, alpha=0.8\n", "    )\n", "    ax1.set_title('Pile Probability Heatmap')\n", "    ax1.set_xlabel('X Coordinate')\n", "    ax1.set_ylabel('Y Coordinate')\n", "    plt.colorbar(sc1, ax=ax1, label='Probability')\n", "    \n", "    # Plot 2: Pile classifications\n", "    ax2.scatter(\n", "        pile_detections['x'], pile_detections['y'],\n", "        color='darkgreen', label='Pile Detections',\n", "        s=30, alpha=0.8\n", "    )\n", "    \n", "    non_pile = results_df[results_df['prediction'] == 'NON-PILE']\n", "    if not non_pile.empty:\n", "        ax2.scatter(\n", "            non_pile['x'], non_pile['y'],\n", "            color='gray', label='Non-Pile',\n", "            s=15, alpha=0.4\n", "        )\n", "    \n", "    ax2.set_title('Pile Classification Results')\n", "    ax2.set_xlabel('X Coordinate')\n", "    ax2.set_ylabel('Y Coordinate')\n", "    ax2.legend()\n", "    \n", "    plt.tight_layout()\n", "    \n", "    plot_path = Path(output_dir) / f\"{site_name}_pile_visualization.png\"\n", "    plt.savefig(plot_path, dpi=300, bbox_inches='tight')\n", "    print(f\"Saved visualization to: {plot_path}\")\n", "    \n", "    plt.show()\n", "    return str(plot_path)"]}, {"cell_type": "code", "execution_count": 15, "id": "1d9a4e23", "metadata": {"execution": {"iopub.execute_input": "2025-08-10T09:00:58.321735Z", "iopub.status.busy": "2025-08-10T09:00:58.321638Z", "iopub.status.idle": "2025-08-10T09:00:58.324932Z", "shell.execute_reply": "2025-08-10T09:00:58.324719Z"}, "papermill": {"duration": 0.006052, "end_time": "2025-08-10T09:00:58.325646", "exception": false, "start_time": "2025-08-10T09:00:58.319594", "status": "completed"}, "tags": []}, "outputs": [], "source": ["def export_results(results_df, output_dir, site_name, model_path, patch_size, confidence_threshold):\n", "    \"\"\"Export results and summary statistics\"\"\"\n", "    if results_df.empty:\n", "        print(\"No results to export\")\n", "        return\n", "    \n", "    timestamp = datetime.now().strftime(\"%Y%m%d_%H%M%S\")\n", "    \n", "    # Export main results\n", "    output_file = Path(output_dir) / f\"{site_name}_pile_detections_{timestamp}.csv\"\n", "    results_df.to_csv(output_file, index=False)\n", "    print(f\"Results exported to: {output_file}\")\n", "    \n", "    # Create summary statistics\n", "    pile_detections = results_df[results_df['prediction'] == 'PILE']\n", "    \n", "    summary = {\n", "        'site_name': site_name,\n", "        'analysis_timestamp': timestamp,\n", "        'total_analysis_points': len(results_df),\n", "        'pile_detections': len(pile_detections),\n", "        'detection_rate': len(pile_detections) / len(results_df),\n", "        'average_pile_probability': float(results_df['pile_probability'].mean()),\n", "        'high_confidence_detections': int(sum(results_df['pile_probability'] > 0.9)),\n", "        'model_path': model_path,\n", "        'patch_size_meters': patch_size,\n", "        'confidence_threshold': confidence_threshold\n", "    }\n", "    \n", "    summary_file = Path(output_dir) / f\"{site_name}_analysis_summary_{timestamp}.json\"\n", "    with open(summary_file, 'w') as f:\n", "        json.dump(summary, f, indent=2)\n", "    \n", "    print(f\"Summary statistics saved to: {summary_file}\")\n", "    \n", "    # Log to MLflow\n", "    for key, value in summary.items():\n", "        if isinstance(value, (int, float)):\n", "            mlflow.log_metric(key, value)\n", "    \n", "    mlflow.log_artifact(str(output_file))\n", "    mlflow.log_artifact(str(summary_file))\n", "    \n", "    return summary\n", "\n"]}, {"cell_type": "code", "execution_count": 16, "id": "4a52cac4", "metadata": {"execution": {"iopub.execute_input": "2025-08-10T09:00:58.329777Z", "iopub.status.busy": "2025-08-10T09:00:58.329676Z", "iopub.status.idle": "2025-08-10T09:00:58.398951Z", "shell.execute_reply": "2025-08-10T09:00:58.398618Z"}, "papermill": {"duration": 0.07238, "end_time": "2025-08-10T09:00:58.399789", "exception": false, "start_time": "2025-08-10T09:00:58.327409", "status": "completed"}, "tags": []}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Starting PointNet++ Pile Detection Pipeline\n", "Site: nortan_res\n", "Configuration: 8.0m patches, 1024 points each\n", "Using device: cpu\n"]}], "source": ["print(\"Starting PointNet++ Pile Detection Pipeline\")\n", "print(f\"Site: {NEW_SITE_NAME}\")\n", "print(f\"Configuration: {PATCH_SIZE}m patches, {NUM_POINTS} points each\")\n", "    \n", "# Setup\n", "setup_environment()\n", "device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')\n", "print(f\"Using device: {device}\")"]}, {"cell_type": "code", "execution_count": 17, "id": "6bb565de", "metadata": {"execution": {"iopub.execute_input": "2025-08-10T09:00:58.404161Z", "iopub.status.busy": "2025-08-10T09:00:58.404041Z", "iopub.status.idle": "2025-08-10T09:01:00.120434Z", "shell.execute_reply": "2025-08-10T09:01:00.119705Z"}, "papermill": {"duration": 1.719739, "end_time": "2025-08-10T09:01:00.121536", "exception": false, "start_time": "2025-08-10T09:00:58.401797", "status": "completed"}, "tags": []}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Loaded checkpoint from epoch 97\n", "Loaded trained PointNet++ model with 1,482,434 parameters\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Loaded LAS file with 35,565,352 points\n", "Point cloud bounds:\n", "  X: 385723.95 to 385809.63\n", "  Y: 3529182.83 to 3529447.01\n", "  Z: 553.38 to 556.27\n"]}], "source": ["# Load model\n", "model, model_loaded = load_model(MODEL_PATH, device)\n", "    \n", "# Load point cloud\n", "point_cloud = load_point_cloud(POINT_CLOUD_PATH)\n", "if point_cloud is None:\n", "    print(\"Failed to load point cloud. Exiting.\")\n", "    mlflow.end_run()    "]}, {"cell_type": "code", "execution_count": 18, "id": "703fc072", "metadata": {"execution": {"iopub.execute_input": "2025-08-10T09:01:00.127365Z", "iopub.status.busy": "2025-08-10T09:01:00.127199Z", "iopub.status.idle": "2025-08-10T09:01:00.152424Z", "shell.execute_reply": "2025-08-10T09:01:00.151903Z"}, "papermill": {"duration": 0.029001, "end_time": "2025-08-10T09:01:00.153382", "exception": false, "start_time": "2025-08-10T09:01:00.124381", "status": "completed"}, "tags": []}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Using fast demo grid for thesis demonstration\n", "Created thesis demo grid: 625 points\n", "Final grid size: 625 points\n", "Processing with 512 points per patch, batch size 8\n"]}], "source": ["# Create analysis grid\n", "#grid_points = create_analysis_grid(point_cloud, GRID_SPACING)\n", "\n", "# Grid creation with site-specific logic\n", "if USE_FAST_MODE:\n", "    print(\"Using fast demo grid for thesis demonstration\")\n", "    # Fast mode - smaller grid for thesis sites\n", "    def create_thesis_demo_grid(point_cloud, max_points=1000):\n", "        \"\"\"Create small, fast grid for thesis demonstration\"\"\"\n", "        # Get point cloud center region (most interesting area)\n", "        x_center = (point_cloud[:, 0].min() + point_cloud[:, 0].max()) / 2\n", "        y_center = (point_cloud[:, 1].min() + point_cloud[:, 1].max()) / 2\n", "        \n", "        # Create grid in 500m x 500m center region\n", "        extent = 250  # 250m radius from center\n", "        x_coords = np.arange(x_center - extent, x_center + extent, GRID_SPACING)\n", "        y_coords = np.arange(y_center - extent, y_center + extent, GRID_SPACING)\n", "        \n", "        full_grid = np.array([[x, y] for x in x_coords for y in y_coords])\n", "        \n", "        # Sample down to manageable size\n", "        if len(full_grid) > max_points:\n", "            indices = np.random.choice(len(full_grid), max_points, replace=False)\n", "            grid_points = full_grid[indices]\n", "        else:\n", "            grid_points = full_grid\n", "            \n", "        print(f\"Created thesis demo grid: {len(grid_points)} points\")\n", "        return grid_points\n", "    \n", "    # Use fast grid\n", "    grid_points = create_thesis_demo_grid(point_cloud, max_points=1000)\n", "    \n", "    # Fast processing parameters\n", "    DEMO_BATCH_SIZE = 8\n", "    DEMO_NUM_POINTS = 512  # Faster processing\n", "    \n", "else:\n", "    print(\"Using standard grid for production analysis\")\n", "    # Standard mode - your existing grid creation\n", "    grid_points = create_analysis_grid(point_cloud, grid_spacing=GRID_SPACING)\n", "    \n", "    # Standard processing parameters  \n", "    DEMO_BATCH_SIZE = BATCH_SIZE\n", "    DEMO_NUM_POINTS = NUM_POINTS\n", "\n", "print(f\"Final grid size: {len(grid_points)} points\")\n", "print(f\"Processing with {DEMO_NUM_POINTS} points per patch, batch size {DEMO_BATCH_SIZE}\")"]}, {"cell_type": "code", "execution_count": 19, "id": "98181738", "metadata": {"execution": {"iopub.execute_input": "2025-08-10T09:01:00.158340Z", "iopub.status.busy": "2025-08-10T09:01:00.158231Z", "iopub.status.idle": "2025-08-10T09:02:39.468791Z", "shell.execute_reply": "2025-08-10T09:02:39.468431Z"}, "papermill": {"duration": 99.314253, "end_time": "2025-08-10T09:02:39.469851", "exception": false, "start_time": "2025-08-10T09:01:00.155598", "status": "completed"}, "tags": []}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Running fast inference for thesis demonstration\n", "Processing 500 points for thesis demo\n", "Building spatial index for 35,565,352 points...\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Pre-filtering valid grid points...\n", "  Pre-filtering progress: 0/500\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Filtered to 61 valid grid points (from 500)\n", "Processing 61 points with 1024 points/patch (CPU optimized)...\n", "This maintains training accuracy but will be slower than 512 points\n", "  Batch 1/8 (0/61 points)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["  <PERSON><PERSON> 4/8 (24/61 points)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["  <PERSON><PERSON> 7/8 (48/61 points)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["CPU processing complete: 61 successful predictions\n"]}], "source": ["# Run inference\n", "# Inference with site-specific parameters\n", "if USE_FAST_MODE:\n", "    print(\"Running fast inference for thesis demonstration\")\n", "    # Fast inference - process subset for demo\n", "    test_grid_size = min(500, len(grid_points))  # Max 500 points\n", "    test_grid = grid_points[:test_grid_size]\n", "    print(f\"Processing {len(test_grid)} points for thesis demo\")\n", "    # Run inference (same function, different parameters)\n", "    results_df = process_site_inference(\n", "        point_cloud,\n", "        grid_points=test_grid,\n", "        model=model,\n", "        device=device,\n", "        batch_size=DEMO_BATCH_SIZE,\n", "        radius=PATCH_SIZE,\n", "        num_points=DEMO_NUM_POINTS\n", "    )\n", "\n", "else:\n", "    print(\"Running full inference for production analysis\")\n", "    # Full inference - process all points\n", "    test_grid = grid_points\n", "    results_df = process_site_inference(\n", "        point_cloud, \n", "        grid_points, \n", "        model, \n", "        device,\n", "        batch_size=BATCH_SIZE, \n", "        radius=PATCH_SIZE, \n", "        num_points=NUM_POINTS\n", "    )\n", "\n"]}, {"cell_type": "code", "execution_count": 20, "id": "8e8f2c88", "metadata": {"execution": {"iopub.execute_input": "2025-08-10T09:02:39.474826Z", "iopub.status.busy": "2025-08-10T09:02:39.474716Z", "iopub.status.idle": "2025-08-10T09:02:39.941880Z", "shell.execute_reply": "2025-08-10T09:02:39.941577Z"}, "papermill": {"duration": 0.470644, "end_time": "2025-08-10T09:02:39.942904", "exception": false, "start_time": "2025-08-10T09:02:39.472260", "status": "completed"}, "tags": []}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "Analysis Results:\n", "  Total analysis points: 61\n", "  Pile detections: 61 (100.0%)\n", "  Average confidence: 1.0000\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Saved visualization to: output_runs/pointnet_plus_plus_harmonized_inference/nortan_res/nortan_res_pile_visualization.png\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 1500x600 with 3 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["Results exported to: output_runs/pointnet_plus_plus_harmonized_inference/nortan_res/nortan_res_pile_detections_20250810_143239.csv\n", "Summary statistics saved to: output_runs/pointnet_plus_plus_harmonized_inference/nortan_res/nortan_res_analysis_summary_20250810_143239.json\n", "\n", "Pipeline complete. Results saved to: output_runs/pointnet_plus_plus_harmonized_inference/nortan_res\n"]}], "source": ["if not results_df.empty:\n", "    # Print summary\n", "    pile_count = len(results_df[results_df['prediction'] == 'PILE'])\n", "    print(f\"\\nAnalysis Results:\")\n", "    print(f\"  Total analysis points: {len(results_df)}\")\n", "    print(f\"  Pile detections: {pile_count} ({pile_count/len(results_df)*100:.1f}%)\")\n", "    print(f\"  Average confidence: {results_df['pile_probability'].mean():.4f}\")\n", "        \n", "    # Visualize and export\n", "    plot_path = visualize_results(results_df, OUTPUT_DIR, NEW_SITE_NAME)\n", "    \n", "    if plot_path:\n", "        mlflow.log_artifact(plot_path)\n", "\n", "    export_results(results_df, OUTPUT_DIR, NEW_SITE_NAME, \n", "                      MOD<PERSON>_PATH, PATCH_SIZE, CONFIDENCE_THRESHOLD)\n", "    \n", "    # Cleanup\n", "    mlflow.end_run()\n", "    print(f\"\\nPipeline complete. Results saved to: {OUTPUT_DIR}\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}, "papermill": {"default_parameters": {}, "duration": 107.771098, "end_time": "2025-08-10T09:02:40.767773", "environment_variables": {}, "exception": null, "input_path": "00_pointnet_plus_plus_inference_harmonized.ipynb", "output_path": "00_pointnet_plus_plus_inference_harmonized_nortan_res_executed.ipynb", "parameters": {"MODEL_PATH": "best_pointnet_iter4.pth", "NEW_SITE_NAME": "nortan_res", "NUM_POINTS": 1024, "OUTPUT_DIR": "output_runs/pointnet_plus_plus_harmonized_inference/nortan_res", "PATCH_SIZE": 8.0, "POINT_CLOUD_PATH": "../../../../../data/raw/nortan_res/pointcloud/Block_11_2m.las", "RUN_NAME": "harmonized_inference_nortan_res", "SITE_CRS": "EPSG:32614", "USE_FAST_MODE": "false"}, "start_time": "2025-08-10T09:00:52.996675", "version": "2.6.0"}}, "nbformat": 4, "nbformat_minor": 5}