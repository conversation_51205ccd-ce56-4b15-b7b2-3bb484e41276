{"cells": [{"cell_type": "markdown", "id": "4e86c2a3", "metadata": {"papermill": {"duration": 0.006132, "end_time": "2025-08-10T08:47:05.874498", "exception": false, "start_time": "2025-08-10T08:47:05.868366", "status": "completed"}, "tags": []}, "source": ["# DGCNN Site Inference Pipeline\n", "This notebook applies the trained PointNet++ model to a new construction site\n", "with different data sources (DWG files instead of KML regions).\n", "\n", "**Workflow:**\n", "1. Simple exploratory pipeline to test DGCNN on multiple sites.\n", "2. Tests our trained DGCNN model on 3 different construction sites.\n", "\n", "**Author**: <PERSON><PERSON><PERSON>  \n", "**Date**: July 2025  \n", "**Project**: As-Built Foundation Analysis - Site Transfer"]}, {"cell_type": "code", "execution_count": 1, "id": "parameters", "metadata": {"execution": {"iopub.execute_input": "2025-08-10T08:47:05.881878Z", "iopub.status.busy": "2025-08-10T08:47:05.881601Z", "iopub.status.idle": "2025-08-10T08:47:05.888855Z", "shell.execute_reply": "2025-08-10T08:47:05.888528Z"}, "papermill": {"duration": 0.012157, "end_time": "2025-08-10T08:47:05.890062", "exception": false, "start_time": "2025-08-10T08:47:05.877905", "status": "completed"}, "tags": ["parameters"]}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["DGCNN Multi-Site Inference Pipeline\n", "Site Name: northan_res\n"]}], "source": ["# Model and processing parameters\n", "SITE_NAME = \"northan_res\"  # Will be overridden by papermill\n", "POINT_CLOUD_PATH = \"../../../../../data/raw/nortan_res/pointcloud/Block_11_2m.las\"  # Will be overridden\n", "\n", "RUN_NAME = f\"inference_{SITE_NAME}\"\n", "OUTPUT_DIR = \"output_runs/dgcnn_inference\"\n", "\n", "MODEL_PATH = \"best_dgcnn_fixed.pth\"  # FIXED: Use harmonized model\n", "CONFIDENCE_THRESHOLD = 0.3  # FIXED: Lower threshold for better detection\n", "BATCH_SIZE = 16\n", "GRID_SPACING = 20.0  # FIXED: Larger spacing for efficiency\n", "PATCH_SIZE = 8.0  # FIXED: harmonized training uses 8m radius\n", "NUM_POINTS = 256\n", "K_NEIGHBORS = 20\n", "\n", "EXPERIMENT_NAME = \"dgcnn_inference\"\n", "\n", "# MLflow configuration\n", "\n", "print(\"DGCNN Multi-Site Inference Pipeline\")\n", "print(f\"Site Name: {SITE_NAME}\")"]}, {"cell_type": "code", "execution_count": 2, "id": "bd3d7430", "metadata": {"execution": {"iopub.execute_input": "2025-08-10T08:47:05.914334Z", "iopub.status.busy": "2025-08-10T08:47:05.914081Z", "iopub.status.idle": "2025-08-10T08:47:05.916218Z", "shell.execute_reply": "2025-08-10T08:47:05.915957Z"}, "papermill": {"duration": 0.005451, "end_time": "2025-08-10T08:47:05.917117", "exception": false, "start_time": "2025-08-10T08:47:05.911666", "status": "completed"}, "tags": ["injected-parameters"]}, "outputs": [], "source": ["# Parameters\n", "SITE_NAME = \"althea_rpcs\"\n", "POINT_CLOUD_PATH = \"../../../../../data/raw/althea_rpcs/pointcloud/Point_Cloud.las\"\n", "OUTPUT_DIR = \"output_runs/dgcnn_harmonized_inference/althea_rpcs\"\n", "RUN_NAME = \"inference_althea_rpcs\"\n"]}, {"cell_type": "code", "execution_count": 3, "id": "6b291543", "metadata": {"execution": {"iopub.execute_input": "2025-08-10T08:47:05.921588Z", "iopub.status.busy": "2025-08-10T08:47:05.921496Z", "iopub.status.idle": "2025-08-10T08:47:08.733151Z", "shell.execute_reply": "2025-08-10T08:47:08.732844Z"}, "papermill": {"duration": 2.81471, "end_time": "2025-08-10T08:47:08.734022", "exception": false, "start_time": "2025-08-10T08:47:05.919312", "status": "completed"}, "tags": []}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages/torchvision/io/image.py:14: UserWarning: Failed to load image Python extension: 'dlopen(/Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages/torchvision/image.so, 0x0006): Library not loaded: @rpath/libjpeg.9.dylib\n", "  Referenced from: <EB3FF92A-5EB1-3EE8-AF8B-5923C1265422> /Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages/torchvision/image.so\n", "  Reason: tried: '/Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages/torchvision/../../../libjpeg.9.dylib' (no such file), '/Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages/torchvision/../../../libjpeg.9.dylib' (no such file), '/Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/lib-dynload/../../libjpeg.9.dylib' (no such file), '/Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/bin/../lib/libjpeg.9.dylib' (no such file)'If you don't plan on using image functionality from `torchvision.io`, you can ignore this warning. Otherwise, there might be something wrong with your environment. Did you have `libjpeg` or `libpng` installed before building `torchvision` from source?\n", "  warn(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Using device: cpu\n"]}], "source": ["import torch\n", "import torch.nn as nn\n", "import torch.nn.functional as F\n", "import numpy as np\n", "import pandas as pd\n", "from pathlib import Path\n", "import matplotlib.pyplot as plt\n", "import mlflow\n", "import mlflow.pytorch\n", "import os\n", "from datetime import datetime\n", "import warnings\n", "warnings.filterwarnings('ignore')\n", "\n", "device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')\n", "print(f\"Using device: {device}\")"]}, {"cell_type": "code", "execution_count": 4, "id": "setup_output_dir", "metadata": {"execution": {"iopub.execute_input": "2025-08-10T08:47:08.738805Z", "iopub.status.busy": "2025-08-10T08:47:08.737927Z", "iopub.status.idle": "2025-08-10T08:47:08.803481Z", "shell.execute_reply": "2025-08-10T08:47:08.803122Z"}, "papermill": {"duration": 0.068534, "end_time": "2025-08-10T08:47:08.804434", "exception": false, "start_time": "2025-08-10T08:47:08.735900", "status": "completed"}, "tags": []}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Output directory created: output_runs/dgcnn_harmonized_inference/althea_rpcs\n", "MLflow experiment initialized\n"]}], "source": ["# Setup output directory and initialize MLflow\n", "os.makedirs(OUTPUT_DIR, exist_ok=True)\n", "print(f\"Output directory created: {OUTPUT_DIR}\")\n", "\n", "if mlflow.active_run() is not None:\n", "    print(f\"Ending previous active run: {mlflow.active_run().info.run_id}\")\n", "    mlflow.end_run()\n", "\n", "mlflow.set_experiment(EXPERIMENT_NAME)\n", "mlflow.start_run(run_name=RUN_NAME)\n", "\n", "# Log parameters\n", "mlflow.log_param(\"site_name\", SITE_NAME)\n", "mlflow.log_param(\"model_type\", \"DGCNN\")\n", "mlflow.log_param(\"patch_size\", PATCH_SIZE)\n", "mlflow.log_param(\"num_points\", NUM_POINTS)\n", "mlflow.log_param(\"k_neighbors\", K_NEIGHBORS)\n", "mlflow.log_param(\"confidence_threshold\", CONFIDENCE_THRESHOLD)\n", "mlflow.log_param(\"batch_size\", BATCH_SIZE)\n", "mlflow.log_param(\"grid_spacing\", GRID_SPACING)\n", "mlflow.log_param(\"model_path\", MODEL_PATH)\n", "\n", "print(\"MLflow experiment initialized\")"]}, {"cell_type": "markdown", "id": "085ade32", "metadata": {"papermill": {"duration": 0.001597, "end_time": "2025-08-10T08:47:08.807774", "exception": false, "start_time": "2025-08-10T08:47:08.806177", "status": "completed"}, "tags": []}, "source": ["## DGCNN Architecture (same as training)"]}, {"cell_type": "code", "execution_count": 5, "id": "f547b445", "metadata": {"execution": {"iopub.execute_input": "2025-08-10T08:47:08.811951Z", "iopub.status.busy": "2025-08-10T08:47:08.811829Z", "iopub.status.idle": "2025-08-10T08:47:08.815757Z", "shell.execute_reply": "2025-08-10T08:47:08.815319Z"}, "papermill": {"duration": 0.006916, "end_time": "2025-08-10T08:47:08.816573", "exception": false, "start_time": "2025-08-10T08:47:08.809657", "status": "completed"}, "tags": []}, "outputs": [], "source": ["def knn(x, k):\n", "    \"\"\"Find k nearest neighbors\"\"\"\n", "    inner = -2 * torch.matmul(x.transpose(2, 1), x)\n", "    xx = torch.sum(x**2, dim=1, keepdim=True)\n", "    pairwise_distance = -xx - inner - xx.transpose(2, 1)\n", "    idx = pairwise_distance.topk(k=k, dim=-1)[1]\n", "    return idx\n", "\n", "def get_graph_feature(x, k=20, idx=None):\n", "    \"\"\"Create graph features from k-NN\"\"\"\n", "    batch_size = x.size(0)\n", "    num_points = x.size(2)\n", "    x = x.view(batch_size, -1, num_points)\n", "    \n", "    if idx is None:\n", "        idx = knn(x, k=k)\n", "    \n", "    device = x.device\n", "    idx_base = torch.arange(0, batch_size, device=device).view(-1, 1, 1) * num_points\n", "    idx = idx + idx_base\n", "    idx = idx.view(-1)\n", "    \n", "    _, num_dims, _ = x.size()\n", "    x = x.transpose(2, 1).contiguous()\n", "    feature = x.view(batch_size * num_points, -1)[idx, :]\n", "    feature = feature.view(batch_size, num_points, k, num_dims)\n", "    x = x.view(batch_size, num_points, 1, num_dims).repeat(1, 1, k, 1)\n", "    \n", "    feature = torch.cat((feature - x, x), dim=3).permute(0, 3, 1, 2).contiguous()\n", "    return feature"]}, {"cell_type": "code", "execution_count": 6, "id": "bf8dd055", "metadata": {"execution": {"iopub.execute_input": "2025-08-10T08:47:08.820309Z", "iopub.status.busy": "2025-08-10T08:47:08.820211Z", "iopub.status.idle": "2025-08-10T08:47:08.822536Z", "shell.execute_reply": "2025-08-10T08:47:08.822306Z"}, "papermill": {"duration": 0.004945, "end_time": "2025-08-10T08:47:08.823238", "exception": false, "start_time": "2025-08-10T08:47:08.818293", "status": "completed"}, "tags": []}, "outputs": [], "source": ["class EdgeConv(nn.Module):\n", "    def __init__(self, in_channels, out_channels, k=20):\n", "        super(<PERSON><PERSON><PERSON><PERSON>, self).__init__()\n", "        self.k = k\n", "        self.conv = nn.Sequential(\n", "            nn.Conv2d(in_channels * 2, out_channels, kernel_size=1, bias=False),\n", "            nn.BatchNorm2d(out_channels),\n", "            nn.LeakyReLU(negative_slope=0.2)\n", "        )\n", "    \n", "    def forward(self, x):\n", "        x = get_graph_feature(x, k=self.k)\n", "        x = self.conv(x)\n", "        x = x.max(dim=-1, keepdim=False)[0]\n", "        return x"]}, {"cell_type": "code", "execution_count": 7, "id": "b2b14a92", "metadata": {"execution": {"iopub.execute_input": "2025-08-10T08:47:08.827283Z", "iopub.status.busy": "2025-08-10T08:47:08.827114Z", "iopub.status.idle": "2025-08-10T08:47:08.831325Z", "shell.execute_reply": "2025-08-10T08:47:08.831119Z"}, "papermill": {"duration": 0.006876, "end_time": "2025-08-10T08:47:08.832016", "exception": false, "start_time": "2025-08-10T08:47:08.825140", "status": "completed"}, "tags": []}, "outputs": [], "source": ["class DGCNN(nn.Module):\n", "    \"\"\"Fixed DGCNN with 20 feature input - exact copy from training\"\"\"\n", "    def __init__(self, num_classes=2, in_channels=20, k=20, dropout=0.3):\n", "        super(DGC<PERSON><PERSON>, self).__init__()\n", "        self.k = k\n", "\n", "        # Split input: 3D coords + features\n", "        self.coord_conv1 = EdgeConv(3, 64, k)\n", "        self.coord_conv2 = EdgeConv(64, 64, k)\n", "        self.coord_conv3 = EdgeConv(64, 128, k)\n", "\n", "        # Feature processing\n", "        self.feature_conv = nn.Sequential(\n", "            nn.Conv1d(in_channels - 3, 64, 1),\n", "            nn.<PERSON><PERSON><PERSON>orm1d(64),\n", "            nn.LeakyReLU(negative_slope=0.2)\n", "        )\n", "\n", "        # Combined processing\n", "        self.conv4 = EdgeConv(128 + 64, 256, k)\n", "\n", "        # Global feature aggregation\n", "        self.conv5 = nn.Sequential(\n", "            nn.Conv1d(64 + 64 + 128 + 256, 1024, kernel_size=1, bias=False),\n", "            nn.<PERSON>ch<PERSON>orm1d(1024),\n", "            nn.LeakyReLU(negative_slope=0.2)\n", "        )\n", "\n", "        # Classification head - simplified\n", "        self.classifier = nn.Sequential(\n", "            nn.<PERSON>(1024, 512),\n", "            nn.BatchNorm1d(512),\n", "            nn.LeakyReLU(negative_slope=0.2),\n", "            nn.Dropout(dropout),\n", "            nn.<PERSON><PERSON>(512, 256),\n", "            nn.BatchNorm1d(256),\n", "            nn.LeakyReLU(negative_slope=0.2),\n", "            nn.Dropout(dropout),\n", "            nn.Linear(256, num_classes)\n", "        )\n", "\n", "    def forward(self, x):\n", "        batch_size = x.size(0)\n", "\n", "        # Split coordinates and features\n", "        coords = x[:, :, :3].transpose(2, 1)  # (B, 3, N)\n", "        features = x[:, :, 3:].transpose(2, 1)  # (B, 17, N)\n", "\n", "        # Process coordinates with EdgeConv\n", "        x1 = self.coord_conv1(coords)  # (B, 64, N)\n", "        x2 = self.coord_conv2(x1)      # (B, 64, N)\n", "        x3 = self.coord_conv3(x2)      # (B, 128, N)\n", "\n", "        # Process features\n", "        feat = self.feature_conv(features)  # (B, 64, N)\n", "\n", "        # Combine and process\n", "        combined = torch.cat([x3, feat], dim=1)  # (B, 192, N)\n", "        x4 = self.conv4(combined)  # (B, 256, N)\n", "\n", "        # Concatenate all features\n", "        x = torch.cat((x1, x2, x3, x4), dim=1)  # (B, 512, N)\n", "\n", "        # Global feature extraction\n", "        x = self.conv5(x)  # (B, 1024, N)\n", "        x = F.adaptive_max_pool1d(x, 1).view(batch_size, -1)  # (B, 1024)\n", "\n", "        # Classification\n", "        x = self.classifier(x)\n", "        return x"]}, {"cell_type": "markdown", "id": "6e12b384", "metadata": {"papermill": {"duration": 0.001603, "end_time": "2025-08-10T08:47:08.835483", "exception": false, "start_time": "2025-08-10T08:47:08.833880", "status": "completed"}, "tags": []}, "source": ["## Load Data and DGCNN Model\n"]}, {"cell_type": "code", "execution_count": 8, "id": "1035c7b5", "metadata": {"execution": {"iopub.execute_input": "2025-08-10T08:47:08.838997Z", "iopub.status.busy": "2025-08-10T08:47:08.838901Z", "iopub.status.idle": "2025-08-10T08:47:08.841824Z", "shell.execute_reply": "2025-08-10T08:47:08.841599Z"}, "papermill": {"duration": 0.005495, "end_time": "2025-08-10T08:47:08.842536", "exception": false, "start_time": "2025-08-10T08:47:08.837041", "status": "completed"}, "tags": []}, "outputs": [], "source": ["def load_point_cloud(file_path):\n", "    \"\"\"Load point cloud from LAS or PLY files\"\"\"\n", "    file_path = Path(file_path)\n", "    \n", "    if not file_path.exists():\n", "        print(f\"File not found: {file_path}\")\n", "        return None\n", "    \n", "    try:\n", "        if file_path.suffix.lower() == '.las':\n", "            import laspy\n", "            las_file = laspy.read(file_path)\n", "            points = np.vstack([las_file.x, las_file.y, las_file.z]).T\n", "            print(f\"Loaded {len(points):,} points from LAS\")\n", "            return points\n", "        elif file_path.suffix.lower() == '.ply':\n", "            import open3d as o3d\n", "            pcd = o3d.io.read_point_cloud(str(file_path))\n", "            points = np.asarray(pcd.points)\n", "            print(f\"Loaded {len(points):,} points from PLY\")\n", "            return points\n", "        else:\n", "            print(f\"Unsupported format: {file_path.suffix}\")\n", "            return None\n", "    except Exception as e:\n", "        print(f\"Error loading {file_path}: {e}\")\n", "        return None"]}, {"cell_type": "code", "execution_count": 9, "id": "0767778f", "metadata": {"execution": {"iopub.execute_input": "2025-08-10T08:47:08.846172Z", "iopub.status.busy": "2025-08-10T08:47:08.846083Z", "iopub.status.idle": "2025-08-10T08:47:11.526471Z", "shell.execute_reply": "2025-08-10T08:47:11.525916Z"}, "papermill": {"duration": 2.683543, "end_time": "2025-08-10T08:47:11.527768", "exception": false, "start_time": "2025-08-10T08:47:08.844225", "status": "completed"}, "tags": []}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Loaded 52,862,386 points from LAS\n", "Point cloud bounds:\n", "  X: 599595.18 to 599866.15\n", "  Y: 4334366.65 to 4334660.84\n", "  Z: 238.63 to 259.18\n"]}], "source": ["# Load point cloud for current site\n", "point_cloud = load_point_cloud(POINT_CLOUD_PATH)\n", "\n", "if point_cloud is None:\n", "    print(f\"Generating synthetic data for {SITE_NAME}\")\n", "    np.random.seed(hash(SITE_NAME) % 2**32)\n", "    point_cloud = np.random.randn(5000, 3) * 20\n", "\n", "print(f\"Point cloud bounds:\")\n", "print(f\"  X: {point_cloud[:, 0].min():.2f} to {point_cloud[:, 0].max():.2f}\")\n", "print(f\"  Y: {point_cloud[:, 1].min():.2f} to {point_cloud[:, 1].max():.2f}\")\n", "print(f\"  Z: {point_cloud[:, 2].min():.2f} to {point_cloud[:, 2].max():.2f}\")\n", "\n", "# Log point cloud metrics to MLflow\n", "mlflow.log_metric(\"point_cloud_size\", len(point_cloud))\n", "mlflow.log_metric(\"x_range\", point_cloud[:, 0].max() - point_cloud[:, 0].min())\n", "mlflow.log_metric(\"y_range\", point_cloud[:, 1].max() - point_cloud[:, 1].min())\n", "mlflow.log_metric(\"z_range\", point_cloud[:, 2].max() - point_cloud[:, 2].min())\n"]}, {"cell_type": "code", "execution_count": 10, "id": "61fee98e", "metadata": {"execution": {"iopub.execute_input": "2025-08-10T08:47:11.534160Z", "iopub.status.busy": "2025-08-10T08:47:11.533835Z", "iopub.status.idle": "2025-08-10T08:47:11.537768Z", "shell.execute_reply": "2025-08-10T08:47:11.537407Z"}, "papermill": {"duration": 0.007838, "end_time": "2025-08-10T08:47:11.538808", "exception": false, "start_time": "2025-08-10T08:47:11.530970", "status": "completed"}, "tags": []}, "outputs": [], "source": ["def create_analysis_grid(point_cloud, grid_spacing=5.0):\n", "    \"\"\"Create regular grid of analysis points\"\"\"\n", "    x_min, x_max = point_cloud[:, 0].min(), point_cloud[:, 0].max()\n", "    y_min, y_max = point_cloud[:, 1].min(), point_cloud[:, 1].max()\n", "    \n", "    padding = grid_spacing * 0.5\n", "    x_coords = np.arange(x_min - padding, x_max + padding, grid_spacing)\n", "    y_coords = np.arange(y_min - padding, y_max + padding, grid_spacing)\n", "    \n", "    return np.array([[x, y] for x in x_coords for y in y_coords])"]}, {"cell_type": "code", "execution_count": 11, "id": "6de0bd17", "metadata": {"execution": {"iopub.execute_input": "2025-08-10T08:47:11.543124Z", "iopub.status.busy": "2025-08-10T08:47:11.543002Z", "iopub.status.idle": "2025-08-10T08:47:11.585122Z", "shell.execute_reply": "2025-08-10T08:47:11.583853Z"}, "papermill": {"duration": 0.045753, "end_time": "2025-08-10T08:47:11.586535", "exception": false, "start_time": "2025-08-10T08:47:11.540782", "status": "completed"}, "tags": []}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Created 240 analysis locations\n"]}], "source": ["# Create analysis grid\n", "grid_points = create_analysis_grid(point_cloud, GRID_SPACING)\n", "\n", "# Limit grid size for testing\n", "MAX_GRID_POINTS = 1000\n", "if len(grid_points) > MAX_GRID_POINTS:\n", "    print(f\"Limiting to {MAX_GRID_POINTS} grid points for testing\")\n", "    grid_points = grid_points[:MAX_GRID_POINTS]\n", "\n", "print(f\"Created {len(grid_points)} analysis locations\")\n", "\n", "# Log grid metrics to MLflow\n", "mlflow.log_metric(\"analysis_grid_points\", len(grid_points))"]}, {"cell_type": "code", "execution_count": 12, "id": "3fb10d9f", "metadata": {"execution": {"iopub.execute_input": "2025-08-10T08:47:11.593747Z", "iopub.status.busy": "2025-08-10T08:47:11.593530Z", "iopub.status.idle": "2025-08-10T08:47:11.600325Z", "shell.execute_reply": "2025-08-10T08:47:11.600070Z"}, "papermill": {"duration": 0.010482, "end_time": "2025-08-10T08:47:11.601268", "exception": false, "start_time": "2025-08-10T08:47:11.590786", "status": "completed"}, "tags": []}, "outputs": [], "source": ["def extract_patch_around_point(point_cloud, center_xy, radius=3.0, num_points=256):\n", "    \"\"\"Extract patch with 20-channel features - exact same method as training\"\"\"\n", "    center_x, center_y = center_xy\n", "    \n", "    # Calculate distances to center (XY only)\n", "    distances = np.sqrt((point_cloud[:, 0] - center_x)**2 + \n", "                       (point_cloud[:, 1] - center_y)**2)\n", "    \n", "    # Select points within radius\n", "    mask = distances <= radius\n", "    patch_xyz = point_cloud[mask]\n", "    \n", "    if len(patch_xyz) < 10:\n", "        return None\n", "    \n", "    # Calculate patch statistics for feature engineering\n", "    patch_center = np.mean(patch_xyz, axis=0)\n", "    z_min, z_max = patch_xyz[:, 2].min(), patch_xyz[:, 2].max()\n", "    z_mean, z_std = patch_xyz[:, 2].mean(), patch_xyz[:, 2].std()\n", "    if z_std == 0:\n", "        z_std = 1e-6\n", "    \n", "    # Calculate distance to center for each point\n", "    dist_to_center = np.sqrt((patch_xyz[:, 0] - center_x)**2 + (patch_xyz[:, 1] - center_y)**2)\n", "    \n", "    # Generate 20-channel features for each point\n", "    features_list = []\n", "    for i, (px, py, pz) in enumerate(patch_xyz):\n", "        # Match the exact feature engineering from training data preparation\n", "        feature_vector = [\n", "            px,  # 0: raw X coordinate\n", "            py - center_y,  # 1: relative Y (small values like training)\n", "            pz,  # 2: raw Z coordinate  \n", "            (pz - z_mean) / z_std,  # 3: height_norm\n", "            dist_to_center[i],  # 4: distance_norm\n", "            len(patch_xyz) / 1000.0,  # 5: density\n", "            px - center_x,  # 6: relative_x\n", "            py - center_y,  # 7: relative_y  \n", "            pz - patch_center[2],  # 8: relative_z\n", "            pz - z_min,  # 9: height_above_min\n", "            z_max - pz,  # 10: depth_below_max\n", "            abs(pz - z_mean),  # 11: abs_height_deviation\n", "            np.sqrt(px**2 + py**2),  # 12: distance_from_origin\n", "            np.arctan2(py - center_y, px - center_x),  # 13: angle\n", "            (px - center_x) * (py - center_y),  # 14: interaction\n", "            px - patch_center[0],  # 15: patch_relative_x\n", "            py - patch_center[1],  # 16: patch_relative_y\n", "            pz / (dist_to_center[i] + 1e-6),  # 17: height_distance_ratio\n", "            np.sqrt((px - center_x)**2 + (py - center_y)**2 + (pz - z_mean)**2),  # 18: 3d_distance\n", "            dist_to_center[i] + np.random.normal(0, 0.01)  # 19: distance with slight variation\n", "        ]\n", "        features_list.append(feature_vector)\n", "    \n", "    patch_features = np.array(features_list, dtype=np.float32)\n", "    n = len(patch_features)\n", "    \n", "    # Sample to fixed size\n", "    if n >= num_points:\n", "        indices = np.random.choice(n, num_points, replace=False)\n", "        patch_fixed = patch_features[indices]\n", "    else:\n", "        # Pad with jittered copies\n", "        extra_indices = np.random.choice(n, num_points - n, replace=True)\n", "        extra = patch_features[extra_indices].copy()\n", "        # Add small noise to coordinates only (first 3 features)\n", "        extra[:, :3] += np.random.normal(0, 0.01, (len(extra), 3))\n", "        patch_fixed = np.vstack([patch_features, extra])\n", "    \n", "    # Normalize spatial coordinates only (first 3 features)\n", "    spatial_coords = patch_fixed[:, :3]\n", "    max_dist = np.max(np.linalg.norm(spatial_coords, axis=1))\n", "    if max_dist > 0:\n", "        patch_fixed[:, :3] /= max_dist\n", "    \n", "    return patch_fixed.astype(np.float32)"]}, {"cell_type": "code", "execution_count": 13, "id": "model_validation", "metadata": {"execution": {"iopub.execute_input": "2025-08-10T08:47:11.606701Z", "iopub.status.busy": "2025-08-10T08:47:11.606557Z", "iopub.status.idle": "2025-08-10T08:47:11.653160Z", "shell.execute_reply": "2025-08-10T08:47:11.652787Z"}, "papermill": {"duration": 0.051752, "end_time": "2025-08-10T08:47:11.655075", "exception": false, "start_time": "2025-08-10T08:47:11.603323", "status": "completed"}, "tags": []}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Loaded DGCNN model from best_dgcnn_fixed.pth\n", "Model has 1,310,082 parameters\n", "Testing model with dummy data...\n", "Dummy test - Output shape: torch.Size([1, 2])\n", "Dummy test - Probabilities: [0. 1.]\n", "Model output might be problematic: pile_prob=1.0000\n"]}], "source": ["# Load trained DGCNN model with validation\n", "try:\n", "    model = DGCNN(num_classes=2, in_channels=20, k=K_NEIGHBORS, dropout=0.3).to(device)\n", "    model.load_state_dict(torch.load(MODEL_PATH, map_location=device))\n", "    model.eval()\n", "    print(f\"Loaded DGCNN model from {MODEL_PATH}\")\n", "    \n", "    # Log model info\n", "    param_count = sum(p.numel() for p in model.parameters())\n", "    print(f\"Model has {param_count:,} parameters\")\n", "    \n", "    # Test model with dummy data to verify it works\n", "    print(\"Testing model with dummy data...\")\n", "    dummy_input = torch.randn(1, 256, 20).to(device)\n", "    with torch.no_grad():\n", "        dummy_output = model(dummy_input)\n", "        dummy_probs = torch.softmax(dummy_output, dim=1)\n", "        print(f\"Dummy test - Output shape: {dummy_output.shape}\")\n", "        print(f\"Dummy test - Probabilities: {dummy_probs[0].cpu().numpy()}\")\n", "        \n", "        # Check if probabilities are reasonable\n", "        pile_prob = dummy_probs[0, 1].item()\n", "        if pile_prob > 0.1 and pile_prob < 0.9:\n", "            print(\"Model producing reasonable probability ranges\")\n", "        else:\n", "            print(f\"Model output might be problematic: pile_prob={pile_prob:.4f}\")\n", "    \n", "    MODEL_LOADED = True\n", "    \n", "except Exception as e:\n", "    print(f\"Error loading model: {e}\")\n", "    print(\"Creating untrained model for testing...\")\n", "    model = DGCNN(num_classes=2, in_channels=20, k=K_NEIGHBORS, dropout=0.3).to(device)\n", "    model.eval()\n", "    MODEL_LOADED = False"]}, {"cell_type": "markdown", "id": "6b0a249e", "metadata": {"papermill": {"duration": 0.001854, "end_time": "2025-08-10T08:47:11.659159", "exception": false, "start_time": "2025-08-10T08:47:11.657305", "status": "completed"}, "tags": []}, "source": ["## Process site with DGCNN\n"]}, {"cell_type": "code", "execution_count": 14, "id": "a5f0d927", "metadata": {"execution": {"iopub.execute_input": "2025-08-10T08:47:11.664328Z", "iopub.status.busy": "2025-08-10T08:47:11.664190Z", "iopub.status.idle": "2025-08-10T08:47:11.668238Z", "shell.execute_reply": "2025-08-10T08:47:11.667805Z"}, "papermill": {"duration": 0.008205, "end_time": "2025-08-10T08:47:11.669308", "exception": false, "start_time": "2025-08-10T08:47:11.661103", "status": "completed"}, "tags": []}, "outputs": [], "source": ["def process_site_dgcnn(point_cloud, grid_points, model, device, batch_size=16):\n", "    \"\"\"Process site with DGCNN model\"\"\"\n", "    results = []\n", "    \n", "    for i in range(0, len(grid_points), batch_size):\n", "        batch_centers = grid_points[i:i+batch_size]\n", "        batch_patches = []\n", "        valid_indices = []\n", "        \n", "        # Extract valid patches\n", "        for j, center in enumerate(batch_centers):\n", "            patch = extract_patch_around_point(point_cloud, center, radius=PATCH_SIZE, num_points=NUM_POINTS)\n", "\n", "            if patch is not None:\n", "                batch_patches.append(patch)\n", "                valid_indices.append(i + j)\n", "        \n", "        if len(batch_patches) == 0:\n", "            continue\n", "        \n", "        # Run DGCNN inference\n", "        batch_tensor = torch.FloatTensor(np.array(batch_patches)).to(device)\n", "        \n", "        with torch.no_grad():\n", "            outputs = model(batch_tensor)\n", "            probabilities = torch.softmax(outputs, dim=1)\n", "            pile_probs = probabilities[:, 1].cpu().numpy()\n", "        \n", "        # Store results\n", "        for j, (idx, prob) in enumerate(zip(valid_indices, pile_probs)):\n", "            center = grid_points[idx]\n", "            results.append({\n", "                'x': center[0],\n", "                'y': center[1],\n", "                'pile_probability': prob,\n", "                'prediction': 'PILE' if prob > CONFIDENCE_THRESHOLD else 'NON-PILE'\n", "            })\n", "    \n", "    return results\n", "\n"]}, {"cell_type": "code", "execution_count": 15, "id": "8444b060", "metadata": {"execution": {"iopub.execute_input": "2025-08-10T08:47:11.674770Z", "iopub.status.busy": "2025-08-10T08:47:11.674561Z", "iopub.status.idle": "2025-08-10T08:50:44.509325Z", "shell.execute_reply": "2025-08-10T08:50:44.509040Z"}, "papermill": {"duration": 212.842873, "end_time": "2025-08-10T08:50:44.514997", "exception": false, "start_time": "2025-08-10T08:47:11.672124", "status": "completed"}, "tags": []}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Running DGCNN inference on 240 locations...\n"]}, {"name": "stdout", "output_type": "stream", "text": ["DGCNN Results for althea_rpcs:\n", "  Total locations analyzed: 191\n", "  Pile detections: 191 (100.0%)\n", "  Average confidence: 1.000\n", "  High confidence (>0.9): 191\n"]}], "source": ["# Process site with DGCNN\n", "print(f\"Running DGCNN inference on {len(grid_points)} locations...\")\n", "\n", "results = process_site_dgcnn(point_cloud, grid_points, model, device, BATCH_SIZE)\n", "\n", "if results:\n", "    results_df = pd.DataFrame(results)\n", "    pile_count = len(results_df[results_df['prediction'] == 'PILE'])\n", "    avg_confidence = results_df['pile_probability'].mean()\n", "    \n", "    print(f\"DGCNN Results for {SITE_NAME}:\")\n", "    print(f\"  Total locations analyzed: {len(results_df)}\")\n", "    print(f\"  Pile detections: {pile_count} ({pile_count/len(results_df)*100:.1f}%)\")\n", "    print(f\"  Average confidence: {avg_confidence:.3f}\")\n", "    print(f\"  High confidence (>0.9): {sum(results_df['pile_probability'] > 0.9)}\")\n", "    \n", "    # Log results to MLflow\n", "    mlflow.log_metric(\"total_locations\", len(results_df))\n", "    mlflow.log_metric(\"pile_detections\", pile_count)\n", "    mlflow.log_metric(\"detection_rate\", pile_count/len(results_df))\n", "    mlflow.log_metric(\"avg_confidence\", avg_confidence)\n", "    mlflow.log_metric(\"high_confidence_count\", sum(results_df['pile_probability'] > 0.9))\n", "else:\n", "    print(f\"No valid results for {SITE_NAME}\")\n", "    results_df = pd.DataFrame()\n", "    pile_count = 0\n", "    avg_confidence = 0.0\n"]}, {"cell_type": "markdown", "id": "c4f86d3f", "metadata": {"papermill": {"duration": 0.002054, "end_time": "2025-08-10T08:50:44.519230", "exception": false, "start_time": "2025-08-10T08:50:44.517176", "status": "completed"}, "tags": []}, "source": ["## Visualization\n"]}, {"cell_type": "code", "execution_count": 16, "id": "a9c7014e", "metadata": {"execution": {"iopub.execute_input": "2025-08-10T08:50:44.523901Z", "iopub.status.busy": "2025-08-10T08:50:44.523784Z", "iopub.status.idle": "2025-08-10T08:50:44.888228Z", "shell.execute_reply": "2025-08-10T08:50:44.887890Z"}, "papermill": {"duration": 0.367964, "end_time": "2025-08-10T08:50:44.889294", "exception": false, "start_time": "2025-08-10T08:50:44.521330", "status": "completed"}, "tags": []}, "outputs": [{"data": {"image/png": "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*****************************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", "text/plain": ["<Figure size 1000x800 with 2 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Visualize results for current site\n", "if not results_df.empty:\n", "    plt.figure(figsize=(10, 8))\n", "    \n", "    # Color by pile probability\n", "    scatter = plt.scatter(\n", "        results_df['x'], results_df['y'],\n", "        c=results_df['pile_probability'],\n", "        cmap='RdYlBu_r', s=30, alpha=0.7\n", "    )\n", "    \n", "    plt.title(f'DGCNN Pile Detection - {SITE_NAME}\\n{pile_count} piles detected')\n", "    plt.xlabel('X Coordinate')\n", "    plt.ylabel('Y Coordinate')\n", "    \n", "    # Add colorbar\n", "    cbar = plt.colorbar(scatter)\n", "    cbar.set_label('Pile Probability')\n", "    \n", "    plt.grid(True, alpha=0.3)\n", "    plt.tight_layout()\n", "    plt.savefig(f'dgcnn_{SITE_NAME}_results.png', dpi=300, bbox_inches='tight')\n", "    plt.show()\n", "else:\n", "    print(f\"No results to visualize for {SITE_NAME}\")\n"]}, {"cell_type": "markdown", "id": "dfafef67", "metadata": {"papermill": {"duration": 0.002084, "end_time": "2025-08-10T08:50:44.893686", "exception": false, "start_time": "2025-08-10T08:50:44.891602", "status": "completed"}, "tags": []}, "source": ["## Export Results\n"]}, {"cell_type": "code", "execution_count": 17, "id": "f0afd0b2", "metadata": {"execution": {"iopub.execute_input": "2025-08-10T08:50:44.898553Z", "iopub.status.busy": "2025-08-10T08:50:44.898361Z", "iopub.status.idle": "2025-08-10T08:50:44.903549Z", "shell.execute_reply": "2025-08-10T08:50:44.903337Z"}, "papermill": {"duration": 0.008702, "end_time": "2025-08-10T08:50:44.904432", "exception": false, "start_time": "2025-08-10T08:50:44.895730", "status": "completed"}, "tags": []}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Saved althea_rpcs results to dgcnn_althea_rpcs_detections.csv\n", "\n", "Summary for althea_rpcs:\n", "  site_name: althea_rpcs\n", "  total_locations: 191\n", "  pile_detections: 191\n", "  detection_rate: 1.000\n", "  avg_confidence: 1.0\n", "  high_confidence_count: 191\n", "  model_loaded: True\n"]}], "source": ["# Export results for current site\n", "if not results_df.empty:\n", "    # Save to CSV\n", "    output_file = f\"dgcnn_{SITE_NAME}_detections.csv\"\n", "    results_df.to_csv(output_file, index=False)\n", "    print(f\"Saved {SITE_NAME} results to {output_file}\")\n", "    \n", "    # Create summary\n", "    summary = {\n", "        'site_name': SITE_NAME,\n", "        'total_locations': len(results_df),\n", "        'pile_detections': pile_count,\n", "        'detection_rate': pile_count/len(results_df),\n", "        'avg_confidence': avg_confidence,\n", "        'high_confidence_count': sum(results_df['pile_probability'] > 0.9),\n", "        'model_loaded': MODEL_LOADED\n", "    }\n", "    \n", "    print(f\"\\nSummary for {SITE_NAME}:\")\n", "    for key, value in summary.items():\n", "        if isinstance(value, float):\n", "            print(f\"  {key}: {value:.3f}\")\n", "        else:\n", "            print(f\"  {key}: {value}\")\n", "else:\n", "    print(f\"No results to export for {SITE_NAME}\")\n"]}, {"cell_type": "code", "execution_count": 18, "id": "80762bff", "metadata": {"execution": {"iopub.execute_input": "2025-08-10T08:50:44.909798Z", "iopub.status.busy": "2025-08-10T08:50:44.909686Z", "iopub.status.idle": "2025-08-10T08:50:45.049614Z", "shell.execute_reply": "2025-08-10T08:50:45.049243Z"}, "papermill": {"duration": 0.143838, "end_time": "2025-08-10T08:50:45.050719", "exception": false, "start_time": "2025-08-10T08:50:44.906881", "status": "completed"}, "tags": []}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Exported all detections to KML: output_runs/dgcnn_harmonized_inference/althea_rpcs/dgcnn_althea_rpcs_all_detections.kml\n", "Exported pile detections to KML: output_runs/dgcnn_harmonized_inference/althea_rpcs/dgcnn_althea_rpcs_pile_detections.kml\n", "Exported high confidence detections to KML: output_runs/dgcnn_harmonized_inference/althea_rpcs/dgcnn_althea_rpcs_high_confidence.kml\n", "\n", "KML Export Summary:\n", "  All detections: 191 points\n", "  Pile detections: 191 points\n", "  High confidence: 191 points\n", "MLflow run completed\n", "\n", "DGCNN inference complete for althea_rpcs!\n", "Output directory: output_runs/dgcnn_harmonized_inference/althea_rpcs\n"]}], "source": ["# Export results to KML format\n", "from shapely.geometry import Point\n", "import geopandas as gpd\n", "\n", "if not results_df.empty:\n", "    try:\n", "        # Create GeoDataFrame from results\n", "        geometry = [Point(xy) for xy in zip(results_df['x'], results_df['y'])]\n", "        \n", "        # Create GeoDataFrame\n", "        gdf = gpd.GeoDataFrame(results_df, geometry=geometry)\n", "        \n", "        # Set coordinate reference system (assuming local coordinates)\n", "        # You may need to adjust this based on your actual coordinate system\n", "        gdf.crs = \"EPSG:4326\"  # WGS84 - adjust if needed\n", "        \n", "        # Add additional attributes for better KML visualization\n", "        gdf['name'] = gdf.apply(lambda row: f\"Pile_{row.name}\" if row['prediction'] == 'PILE' else f\"NonPile_{row.name}\", axis=1)\n", "        gdf['description'] = gdf.apply(lambda row: f\"Confidence: {row['pile_probability']:.3f}\\nPrediction: {row['prediction']}\", axis=1)\n", "        \n", "        # Color coding based on confidence\n", "        gdf['confidence_level'] = pd.cut(gdf['pile_probability'], \n", "                                       bins=[0, 0.5, 0.8, 0.95, 1.0], \n", "                                       labels=['Low', 'Medium', 'High', 'Very High'])\n", "        \n", "        # Export all detections to KML\n", "        kml_all_file = Path(OUTPUT_DIR) / f\"dgcnn_{SITE_NAME}_all_detections.kml\"\n", "        gdf.to_file(kml_all_file, driver='KML')\n", "        print(f\"Exported all detections to KML: {kml_all_file}\")\n", "        \n", "        # Export only pile detections to separate KML\n", "        pile_gdf = gdf[gdf['prediction'] == 'PILE'].copy()\n", "        if not pile_gdf.empty:\n", "            kml_piles_file = Path(OUTPUT_DIR) / f\"dgcnn_{SITE_NAME}_pile_detections.kml\"\n", "            pile_gdf.to_file(kml_piles_file, driver='KML')\n", "            print(f\"Exported pile detections to KML: {kml_piles_file}\")\n", "            \n", "            # Log KML files to MLflow\n", "            mlflow.log_artifact(str(kml_piles_file))\n", "        \n", "        # Export high confidence detections (>0.9) to separate KML\n", "        high_conf_gdf = gdf[gdf['pile_probability'] > 0.9].copy()\n", "        if not high_conf_gdf.empty:\n", "            kml_high_conf_file = Path(OUTPUT_DIR) / f\"dgcnn_{SITE_NAME}_high_confidence.kml\"\n", "            high_conf_gdf.to_file(kml_high_conf_file, driver='KML')\n", "            print(f\"Exported high confidence detections to KML: {kml_high_conf_file}\")\n", "            \n", "            # Log to MLflow\n", "            mlflow.log_artifact(str(kml_high_conf_file))\n", "        \n", "        # Log main KML to MLflow\n", "        mlflow.log_artifact(str(kml_all_file))\n", "        \n", "        print(f\"\\nKML Export Summary:\")\n", "        print(f\"  All detections: {len(gdf)} points\")\n", "        print(f\"  Pile detections: {len(pile_gdf)} points\")\n", "        print(f\"  High confidence: {len(high_conf_gdf)} points\")\n", "        \n", "    except Exception as e:\n", "        print(f\"Error exporting to KML: {e}\")\n", "else:\n", "    print(\"No results available for KML export\")\n", "\n", "\n", "# Close MLflow run\n", "mlflow.end_run()\n", "print(\"MLflow run completed\")\n", "\n", "print(f\"\\nDGCNN inference complete for {SITE_NAME}!\")\n", "print(f\"Output directory: {OUTPUT_DIR}\")"]}, {"cell_type": "code", "execution_count": 19, "id": "72e057a7", "metadata": {"execution": {"iopub.execute_input": "2025-08-10T08:50:45.058101Z", "iopub.status.busy": "2025-08-10T08:50:45.057785Z", "iopub.status.idle": "2025-08-10T08:50:45.072183Z", "shell.execute_reply": "2025-08-10T08:50:45.071473Z"}, "papermill": {"duration": 0.020999, "end_time": "2025-08-10T08:50:45.074288", "exception": false, "start_time": "2025-08-10T08:50:45.053289", "status": "completed"}, "tags": []}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Prediction Distribution Analysis:\n", "Min probability: 1.0000\n", "Max probability: 1.0000\n", "Mean probability: 1.0000\n", "Std probability: 0.0000\n", "Detections > 0.5: 191 (100.0%)\n", "Detections > 0.7: 191 (100.0%)\n", "Detections > 0.8: 191 (100.0%)\n", "Detections > 0.9: 191 (100.0%)\n", "Detections > 0.95: 191 (100.0%)\n", "\n", "Top 10 predictions:\n", "           x            y  pile_probability prediction\n", "599605.17945 4.334377e+06               1.0       PILE\n", "599605.17945 4.334397e+06               1.0       PILE\n", "599605.17945 4.334417e+06               1.0       PILE\n", "599605.17945 4.334437e+06               1.0       PILE\n", "599605.17945 4.334457e+06               1.0       PILE\n", "599605.17945 4.334477e+06               1.0       PILE\n", "599605.17945 4.334497e+06               1.0       PILE\n", "599605.17945 4.334517e+06               1.0       PILE\n", "599605.17945 4.334537e+06               1.0       PILE\n", "599605.17945 4.334557e+06               1.0       PILE\n", "\n", "Model loaded successfully: True\n"]}], "source": ["# %%\n", "# Diagnostic: Check model predictions in detail\n", "if not results_df.empty:\n", "    print(\"Prediction Distribution Analysis:\")\n", "    print(f\"Min probability: {results_df['pile_probability'].min():.4f}\")\n", "    print(f\"Max probability: {results_df['pile_probability'].max():.4f}\")\n", "    print(f\"Mean probability: {results_df['pile_probability'].mean():.4f}\")\n", "    print(f\"Std probability: {results_df['pile_probability'].std():.4f}\")\n", "    \n", "    # Check distribution at different thresholds\n", "    for threshold in [0.5, 0.7, 0.8, 0.9, 0.95]:\n", "        count = sum(results_df['pile_probability'] > threshold)\n", "        print(f\"Detections > {threshold}: {count} ({count/len(results_df)*100:.1f}%)\")\n", "    \n", "    # Show top 10 predictions\n", "    print(\"\\nTop 10 predictions:\")\n", "    top_preds = results_df.nlargest(10, 'pile_probability')[['x', 'y', 'pile_probability', 'prediction']]\n", "    print(top_preds.to_string(index=False))\n", "else:\n", "    print(\"No results to analyze\")\n", "\n", "print(f\"\\nModel loaded successfully: {MODEL_LOADED}\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}, "papermill": {"default_parameters": {}, "duration": 221.036903, "end_time": "2025-08-10T08:50:46.001650", "environment_variables": {}, "exception": null, "input_path": "00_dgcnn_inference_harmonized.ipynb", "output_path": "00_dgcnn_inference_harmonized_althea_rpcs_executed.ipynb", "parameters": {"OUTPUT_DIR": "output_runs/dgcnn_harmonized_inference/althea_rpcs", "POINT_CLOUD_PATH": "../../../../../data/raw/althea_rpcs/pointcloud/Point_Cloud.las", "RUN_NAME": "inference_althea_rpcs", "SITE_NAME": "althea_rpcs"}, "start_time": "2025-08-10T08:47:04.964747", "version": "2.6.0"}}, "nbformat": 4, "nbformat_minor": 5}