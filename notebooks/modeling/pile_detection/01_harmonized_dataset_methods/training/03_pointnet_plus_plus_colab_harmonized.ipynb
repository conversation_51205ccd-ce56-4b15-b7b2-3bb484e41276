{"cells": [{"cell_type": "markdown", "metadata": {"id": "uwcbcYyM5alm"}, "source": ["# PointNet++ <PERSON>le Detection\n", "\n", "This notebook implements PointNet++ architecture for pile detection using the patch data prepared from the successful harmonization and extraction pipeline.\n", "\n", "\n", "**Architecture:**\n", "- Set abstraction layers for hierarchical feature learning\n", "- Multi-scale grouping for robust feature extraction\n", "- Feature propagation for dense prediction\n", "- Binary classification (pile vs non-pile)\n", "\n", "**Author**: <PERSON><PERSON><PERSON>  \n", "**Date**: July 2025  \n", "**Project**: As-Built Foundation Analysis"]}, {"cell_type": "code", "source": ["from google.colab import drive\n", "drive.mount('/content/drive')\n"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "oKrohjNO6YP9", "outputId": "23c964ba-51b7-4026-cea6-99ec01382901"}, "execution_count": null, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Mounted at /content/drive\n"]}]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "9CEYy2EX5aln", "outputId": "31d33dab-94cb-454c-be3a-2e51d4f75aeb"}, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["=== POINTNET++ PILE DETECTION ===\n", "Goal: Beat rule-based baseline F1 of 0.932\n", "Goal: Beat rule-based baseline accuracy of 0.885\n"]}], "source": ["import sys\n", "from pathlib import Path\n", "import pickle\n", "import json\n", "\n", "# Parameters\n", "batch_size = 16\n", "num_epochs = 50\n", "learning_rate = 0.001\n", "num_points = 256 #512  # Reduced from 1024 to match your data size\n", "save_model = True\n", "\n", "\n", "# Baseline performance to beat (from your rule-based classifier)\n", "BASELINE_F1 = 0.932  # 93.2% F1-score from rule-based\n", "BASELINE_ACCURACY = 0.885  # 88.5% accuracy\n", "\n", "print(\"=== POINTNET++ PILE DETECTION ===\")\n", "print(f\"Goal: Beat rule-based baseline F1 of {BASELINE_F1:.3f}\")\n", "print(f\"Goal: Beat rule-based baseline accuracy of {BASELINE_ACCURACY:.3f}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "yChKecXP5aln", "outputId": "109dfb9c-eada-4d2d-9f34-41334bef4a1d"}, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Using device: cuda\n"]}], "source": ["import torch\n", "import torch.nn as nn\n", "import torch.optim as optim\n", "from torch.utils.data import Dataset, DataLoader\n", "import numpy as np\n", "from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score, confusion_matrix\n", "import matplotlib.pyplot as plt\n", "from datetime import datetime\n", "import warnings\n", "warnings.filterwarnings('ignore')\n", "\n", "device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')\n", "#device = torch.device('mps' if torch.backends.mps.is_available() else 'cpu')\n", "\n", "print(f\"Using device: {device}\")\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "7O065U-t5aln", "outputId": "e8256f9d-2343-4240-f590-10dc398c8ba6"}, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["ls: cannot access '../../00_data_preprocessing/output/ml_patch_data/patches_20250722_160244': No such file or directory\n"]}], "source": ["!ls -lh ../../00_data_preprocessing/output/ml_patch_data/patches_20250722_160244"]}, {"cell_type": "code", "source": ["!pwd"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "d1LhCVzr67Hk", "outputId": "1b7bdf8c-8219-4661-dea8-4d9c86b5a90e"}, "execution_count": null, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["/content\n"]}]}, {"cell_type": "code", "source": ["!ls -lh /content/drive/MyDrive/'Colab Notebooks'/data\n", "!ls -lh /content/drive/MyDrive/'Colab Notebooks'/data/patches_20250722_160244"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "8o4R78ml7BKm", "outputId": "d7be3a84-76f0-4dc4-e65e-20bb71855bc1"}, "execution_count": null, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["total 4.0K\n", "drwx------ 2 <USER> <GROUP> 4.0K Jul 22 16:13 patches_20250722_160244\n", "total 9.0M\n", "-rw------- 1 <USER> <GROUP>  415 Jul 22 10:32 config.json\n", "-rw------- 1 <USER> <GROUP> 2.7K Jul 22 11:03 enhanced_classification_metrics.json\n", "-rw------- 1 <USER> <GROUP> 1.6M Jul 22 11:51 feature_analysis_results.csv\n", "-rw------- 1 <USER> <GROUP> 232K Jul 22 11:51 feature_analysis_test.csv\n", "-rw------- 1 <USER> <GROUP> 1.1M Jul 22 11:51 feature_analysis_train.csv\n", "-rw------- 1 <USER> <GROUP> 231K Jul 22 11:51 feature_analysis_val.csv\n", "-rw------- 1 <USER> <GROUP> 1.2K Jul 22 11:51 feature_comparison.csv\n", "-rw------- 1 <USER> <GROUP> 207K Jul 22 10:32 test_metadata.json\n", "-rw------- 1 <USER> <GROUP> 699K Jul 22 10:32 test_patches.pkl\n", "-rw------- 1 <USER> <GROUP> 962K Jul 22 10:32 train_metadata.json\n", "-rw------- 1 <USER> <GROUP> 3.2M Jul 22 10:32 train_patches.pkl\n", "-rw------- 1 <USER> <GROUP> 207K Jul 22 10:32 val_metadata.json\n", "-rw------- 1 <USER> <GROUP> 705K Jul 22 10:32 val_patches.pkl\n"]}]}, {"cell_type": "markdown", "metadata": {"id": "E2F5qbC-5aln"}, "source": ["## Data Loading"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "J1LQok965aln"}, "outputs": [], "source": ["import pickle\n", "import json\n", "from pathlib import Path\n", "\n", "\n", "#def load_preprocessed_patches(base_dir=\"../../00_data_preprocessing/output/ml_patch_data\"):\n", "def load_preprocessed_patches(base_dir=\"/content/drive/MyDrive/Colab Notebooks/data\"):\n", "\n", "    \"\"\"\n", "    Load pre-extracted and labeled patch datasets from the most recent patch folder.\n", "    Assumes the structure:\n", "      ../../00_data_preprocessing/output/ml_patch_data/patches_<timestamp>/{train,val,test}_patches.pkl\n", "    \"\"\"\n", "    try:\n", "        patch_root = Path(base_dir).resolve()\n", "        if not patch_root.exists():\n", "            raise FileNotFoundError(f\"Patch root not found: {patch_root}\")\n", "\n", "        # Auto-detect latest patch directory\n", "        latest_patch_dir = sorted(patch_root.glob(\"patches_*\"))[-1]\n", "        print(f\"Using latest patch data from: {latest_patch_dir}\")\n", "\n", "        datasets = {}\n", "        for split in ['train', 'val', 'test']:\n", "            patch_file = latest_patch_dir / f\"{split}_patches.pkl\"\n", "            meta_file = latest_patch_dir / f\"{split}_metadata.json\"\n", "\n", "            if not patch_file.exists() or not meta_file.exists():\n", "                raise FileNotFoundError(f\"Missing files for '{split}':\\n  - {patch_file}\\n  - {meta_file}\")\n", "\n", "            datasets[split] = {\n", "                'patches': pickle.load(open(patch_file, 'rb')),\n", "                'metadata': json.load(open(meta_file))\n", "            }\n", "            print(f\"  {split.capitalize()}: {len(datasets[split]['patches'])} patches\")\n", "\n", "        return datasets\n", "\n", "    except Exception as e:\n", "        print(f\"Failed to load patch data: {e}\")\n", "        raise\n", "\n", "\n", "def sample_patch_to_fixed_size(patch, target_n=512, noise_scale=0.01):\n", "    \"\"\"Randomly sample or pad a point patch to a fixed number of points\"\"\"\n", "    patch = np.array(patch, dtype=np.float32)\n", "    n = len(patch)\n", "\n", "    if n >= target_n:\n", "        return patch[np.random.choice(n, target_n, replace=False)]\n", "\n", "    # Pad with jittered copies of existing points\n", "    extra = np.stack([\n", "        patch[np.random.randint(n)] + np.random.normal(0, noise_scale, 3)\n", "        for _ in range(target_n - n)\n", "    ])\n", "    return np.vstack([patch, extra])\n", "\n", "\n", "def preprocess_patches_for_pointnet(patches, metadata, num_points=256):\n", "    \"\"\"Convert patches and metadata into PointNet++-ready format\"\"\"\n", "    out_patches, out_labels = [], []\n", "\n", "    for patch, meta in zip(patches, metadata):\n", "        if len(patch) < 10:\n", "            continue  # Ignore too-small patches\n", "\n", "        patch_fixed = sample_patch_to_fixed_size(patch, num_points)\n", "        patch_fixed /= np.max(np.linalg.norm(patch_fixed, axis=1)) or 1  # Normalize\n", "\n", "        out_patches.append(patch_fixed)\n", "        out_labels.append(int(meta.get('label', meta.get('patch_type') == 'positive')))\n", "\n", "    return np.array(out_patches, dtype=np.float32), np.array(out_labels, dtype=np.int64)"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "k383XElV5alo", "outputId": "af0bf1a7-4ee4-4cab-b1b0-75cbddafed00"}, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Using latest patch data from: /content/drive/MyDrive/Colab Notebooks/data/patches_20250722_160244\n", "  Train: 3345 patches\n", "  Val: 717 patches\n", "  Test: 717 patches\n", "Formatting patches for PointNet++...\n", "\n", "Final shapes:\n", "Train: <PERSON><PERSON> (3345, 256, 3), <PERSON>s (3345,)\n", "Val: <PERSON><PERSON> (717, 256, 3), <PERSON><PERSON> (717,)\n", "Test: <PERSON><PERSON> (717, 256, 3), <PERSON><PERSON> (717,)\n", "\n", "Class distribution:\n", "  Train: 2684 positive, 661 negative (80.2% positive)\n", "  Val: 576 positive, 141 negative (80.3% positive)\n", "  Test: 575 positive, 142 negative (80.2% positive)\n"]}], "source": ["datasets = load_preprocessed_patches()\n", "print(\"Formatting patches for PointNet++...\")\n", "\n", "train_patches, train_labels = preprocess_patches_for_pointnet(**datasets['train'])\n", "val_patches, val_labels = preprocess_patches_for_pointnet(**datasets['val'])\n", "test_patches, test_labels = preprocess_patches_for_pointnet(**datasets['test'])\n", "\n", "print(f\"\\nFinal shapes:\")\n", "for split, data in zip(['<PERSON>', 'Val', 'Test'],\n", "                       [(train_patches, train_labels), (val_patches, val_labels), (test_patches, test_labels)]):\n", "    print(f\"{split}: Patches {data[0].shape}, Labels {data[1].shape}\")\n", "\n", "# Check class distribution\n", "print(f\"\\nClass distribution:\")\n", "for split, labels in [('Train', train_labels), ('Val', val_labels), ('Test', test_labels)]:\n", "    pos_count = np.sum(labels)\n", "    neg_count = len(labels) - pos_count\n", "    print(f\"  {split}: {pos_count} positive, {neg_count} negative ({pos_count/len(labels)*100:.1f}% positive)\")\n", "\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 858}, "id": "Ubnzuc9l5alo", "outputId": "d2c44d85-38d4-4090-cc28-fb765e4e0fd7"}, "outputs": [{"output_type": "display_data", "data": {"text/plain": ["<Figure size 640x480 with 1 Axes>"], "image/png": "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\n"}, "metadata": {}}, {"output_type": "display_data", "data": {"text/plain": ["<Figure size 640x480 with 1 Axes>"], "image/png": "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***************************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\n"}, "metadata": {}}], "source": ["import matplotlib.pyplot as plt\n", "\n", "def plot_patch(patch, title):\n", "    fig = plt.figure()\n", "    ax = fig.add_subplot(111, projection='3d')\n", "    ax.scatter(patch[:, 0], patch[:, 1], patch[:, 2], s=1)\n", "    ax.set_title(title)\n", "    plt.show()\n", "\n", "# Show one positive and negative sample\n", "plot_patch(train_patches[train_labels == 1][0], \"Positive Patch\")\n", "plot_patch(train_patches[train_labels == 0][0], \"Negative Patch\")\n"]}, {"cell_type": "markdown", "metadata": {"id": "-CZQN30P5alo"}, "source": ["## Dataset and DataLoader"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "KXuZO9065alo", "outputId": "a16de900-b7ef-41c4-d41d-02b0447f021d"}, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Train: 3345, Val: 717, Test: 717\n"]}], "source": ["from torch.utils.data import DataLoader, Dataset\n", "import torch\n", "\n", "class PileDataset(Dataset):\n", "    def __init__(self, points, labels):\n", "        self.points = torch.FloatTensor(points)\n", "        self.labels = torch.LongTensor(labels)\n", "\n", "    def __len__(self):\n", "        return len(self.points)\n", "\n", "    def __getitem__(self, idx):\n", "        return self.points[idx], self.labels[idx]\n", "\n", "# Create datasets\n", "train_dataset = PileDataset(train_patches, train_labels)\n", "val_dataset = PileDataset(val_patches, val_labels)\n", "test_dataset = PileDataset(test_patches, test_labels)\n", "\n", "# Create dataloaders\n", "batch_size = 32  # or whatever value you use\n", "#train_loader = DataLoader(train_dataset, batch_size=batch_size, shuffle=True)\n", "train_loader = DataLoader(train_dataset, batch_size=16, shuffle=True, drop_last=True)\n", "\n", "val_loader = DataLoader(val_dataset, batch_size=batch_size, shuffle=False)\n", "test_loader = DataLoader(test_dataset, batch_size=batch_size, shuffle=False)\n", "\n", "print(f\"Train: {len(train_dataset)}, Val: {len(val_dataset)}, Test: {len(test_dataset)}\")"]}, {"cell_type": "markdown", "metadata": {"id": "bqYTve2-5alo"}, "source": ["## PointNet++ Architecture"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "bbAq5dny5alo"}, "outputs": [], "source": ["def square_distance(src, dst):\n", "    \"\"\"Calculate squared distance between points\"\"\"\n", "    B, N, _ = src.shape\n", "    _, M, _ = dst.shape\n", "    dist = -2 * torch.matmul(src, dst.permute(0, 2, 1))\n", "    dist += torch.sum(src ** 2, -1).view(B, N, 1)\n", "    dist += torch.sum(dst ** 2, -1).view(B, 1, M)\n", "    return dist\n", "\n", "def farthest_point_sample(xyz, npoint):\n", "    \"\"\"Farthest point sampling\"\"\"\n", "    device = xyz.device\n", "    B, N, C = xyz.shape\n", "    centroids = torch.zeros(B, npoint, dtype=torch.long).to(device)\n", "    distance = torch.ones(B, N).to(device) * 1e10\n", "    farthest = torch.randint(0, N, (B,), dtype=torch.long).to(device)\n", "    batch_indices = torch.arange(B, dtype=torch.long).to(device)\n", "\n", "    for i in range(npoint):\n", "        centroids[:, i] = farthest\n", "        centroid = xyz[batch_indices, farthest, :].view(B, 1, 3)\n", "        dist = torch.sum((xyz - centroid) ** 2, -1)\n", "        mask = dist < distance\n", "        distance[mask] = dist[mask]\n", "        farthest = torch.max(distance, -1)[1]\n", "\n", "    return centroids\n", "\n", "def query_ball_point(radius, nsample, xyz, new_xyz):\n", "    \"\"\"Ball query\"\"\"\n", "    device = xyz.device\n", "    B, N, C = xyz.shape\n", "    _, S, _ = new_xyz.shape\n", "    group_idx = torch.arange(N, dtype=torch.long).to(device).view(1, 1, N).repeat([B, S, 1])\n", "    sqrdists = square_distance(new_xyz, xyz)\n", "    group_idx[sqrdists > radius ** 2] = N\n", "    group_idx = group_idx.sort(dim=-1)[0][:, :, :nsample]\n", "    group_first = group_idx[:, :, 0].view(B, S, 1).repeat([1, 1, nsample])\n", "    mask = group_idx == N\n", "    group_idx[mask] = group_first[mask]\n", "    return group_idx\n", "\n", "class PointNetSetAbstraction(nn.Module):\n", "    def __init__(self, npoint, radius, nsample, in_channel, mlp, group_all):\n", "        super(PointNetSetAbstraction, self).__init__()\n", "        self.npoint = npoint\n", "        self.radius = radius\n", "        self.nsample = nsample\n", "        self.mlp_convs = nn.ModuleList()\n", "        self.mlp_bns = nn.ModuleList()\n", "        self.group_all = group_all\n", "\n", "        last_channel = in_channel\n", "        for out_channel in mlp:\n", "            self.mlp_convs.append(nn.Conv2d(last_channel, out_channel, 1))\n", "            self.mlp_bns.append(nn.BatchNorm2d(out_channel))\n", "            last_channel = out_channel\n", "\n", "    def forward(self, xyz, points):\n", "        B, N, C = xyz.shape\n", "\n", "        if self.group_all:\n", "            new_xyz = torch.zeros(B, 1, C).to(xyz.device)\n", "            new_points = points.view(B, 1, N, -1)\n", "        else:\n", "            fps_idx = farthest_point_sample(xyz, self.npoint)\n", "            new_xyz = xyz[torch.arange(B)[:, None], fps_idx]\n", "            idx = query_ball_point(self.radius, self.nsample, xyz, new_xyz)\n", "            grouped_xyz = xyz[torch.arange(B)[:, None, None], idx]\n", "            grouped_xyz_norm = grouped_xyz - new_xyz.view(B, self.npoint, 1, C)\n", "\n", "            if points is not None:\n", "                grouped_points = points[torch.arange(B)[:, None, None], idx]\n", "                new_points = torch.cat([grouped_xyz_norm, grouped_points], dim=-1)\n", "            else:\n", "                new_points = grouped_xyz_norm\n", "\n", "        new_points = new_points.permute(0, 3, 2, 1)\n", "        for i, conv in enumerate(self.mlp_convs):\n", "            bn = self.mlp_bns[i]\n", "            new_points = torch.relu(bn(conv(new_points)))\n", "\n", "        new_points = torch.max(new_points, 2)[0]\n", "        new_points = new_points.permute(0, 2, 1)\n", "\n", "        return new_xyz, new_points"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "ABOzWGAi5alo", "outputId": "c1e2900b-f4d3-4c51-b098-caffa5497363"}, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Model initialized with 1,465,154 parameters\n"]}], "source": ["class PointNetPlusPlus(nn.Module):\n", "    def __init__(self, num_classes=2):\n", "        super(PointNetPlusPlus, self).__init__()\n", "\n", "        # Set abstraction layers\n", "        # self.sa1 = PointNetSetAbstraction(512, 0.2, 32, 3, [64, 64, 128], False)\n", "        # self.sa2 = PointNetSetAbstraction(128, 0.4, 64, 128 + 3, [128, 128, 256], False)\n", "        # self.sa3 = PointNetSetAbstraction(None, None, None, 256 + 3, [256, 512, 1024], True)\n", "\n", "        # self.sa1 = PointNetSetAbstraction(512, 0.2, 32, 3, [64, 64, 128], False)\n", "        # self.sa2 = PointNetSetAbstraction(128, 0.4, 64, 128, [128, 128, 256], False)\n", "        # self.sa3 = PointNetSetAbstraction(None, None, None, 256, [256, 512, 1024], True)\n", "\n", "        self.sa1 = PointNetSetAbstraction(512, 0.2, 32, 3, [64, 64, 128], False)           # 3\n", "        self.sa2 = PointNetSetAbstraction(128, 0.4, 64, 128 + 3, [128, 128, 256], False)   # 128+3=131\n", "        self.sa3 = PointNetSetAbstraction(None, None, None, 256, [256, 512, 1024], True)    # 256 only\n", "\n", "        # Classification head\n", "        self.fc1 = nn.Linear(1024, 512)\n", "        self.bn1 = nn.BatchNorm1d(512)\n", "        self.drop1 = nn.Dropout(0.4)\n", "        self.fc2 = nn.Linear(512, 256)\n", "        self.bn2 = nn.BatchNorm1d(256)\n", "        self.drop2 = nn.Dropout(0.4)\n", "        self.fc3 = nn.Linear(256, num_classes)\n", "\n", "    def forward(self, xyz):\n", "        if xyz.shape[1] == 3:\n", "            xyz = xyz.transpose(1, 2).contiguous()  # Ensure shape is (B, N, 3)\n", "\n", "        B, _, _ = xyz.shape\n", "\n", "        l1_xyz, l1_points = self.sa1(xyz, None)\n", "        l2_xyz, l2_points = self.sa2(l1_xyz, l1_points)\n", "        l3_xyz, l3_points = self.sa3(l2_xyz, l2_points)\n", "\n", "        x = l3_points.view(B, 1024)\n", "        x = self.drop1(torch.relu(self.bn1(self.fc1(x))))\n", "        x = self.drop2(torch.relu(self.bn2(self.fc2(x))))\n", "        x = self.fc3(x)\n", "\n", "        return x\n", "\n", "# Initialize model\n", "model = PointNetPlusPlus(num_classes=2).to(device)\n", "criterion = nn.CrossEntropyLoss()\n", "optimizer = optim.Adam(model.parameters(), lr=learning_rate)\n", "\n", "print(f\"Model initialized with {sum(p.numel() for p in model.parameters()):,} parameters\")"]}, {"cell_type": "markdown", "metadata": {"id": "tH5jMoiu5alo"}, "source": ["## Training"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "z4gYwAPX5alo"}, "outputs": [], "source": ["import torch\n", "from torch.cuda.amp import autocast, GradScaler\n", "\n", "use_amp = torch.cuda.is_available()\n", "scaler = GradScaler(enabled=use_amp)\n", "\n", "def train_epoch(model, loader, criterion, optimizer, device):\n", "    model.train()\n", "    total_loss = 0\n", "    correct = 0\n", "    total = 0\n", "\n", "    for batch_idx, (data, target) in enumerate(loader):\n", "        data, target = data.to(device), target.to(device)\n", "\n", "        optimizer.zero_grad()\n", "        with autocast(enabled=use_amp):  # Only use AMP on CUDA\n", "            output = model(data)\n", "            loss = criterion(output, target)\n", "\n", "        if use_amp:\n", "            scaler.scale(loss).backward()\n", "            scaler.step(optimizer)\n", "            scaler.update()\n", "        else:\n", "            loss.backward()\n", "            optimizer.step()\n", "\n", "        total_loss += loss.item()\n", "        pred = output.argmax(dim=1)\n", "        correct += pred.eq(target).sum().item()\n", "        total += target.size(0)\n", "\n", "    return total_loss / len(loader), correct / total\n", "\n", "def validate_epoch(model, loader, criterion, device):\n", "    model.eval()\n", "    total_loss = 0\n", "    correct = 0\n", "    total = 0\n", "\n", "    with torch.no_grad():\n", "        for data, target in loader:\n", "            data, target = data.to(device), target.to(device)\n", "            output = model(data)\n", "            loss = criterion(output, target)\n", "\n", "            total_loss += loss.item()\n", "            pred = output.argmax(dim=1)\n", "            correct += pred.eq(target).sum().item()\n", "            total += target.size(0)\n", "\n", "    return total_loss / len(loader), correct / total\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "oOjv-NDJ5alo", "outputId": "06c154b3-e789-4a8e-f3c1-cbf611551f71"}, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Starting training...\n"]}], "source": ["print(\"Starting training...\")\n", "\n", "train_losses = []\n", "val_losses = []\n", "train_accs = []\n", "val_accs = []\n", "\n", "best_val_acc = 0\n", "patience = 10\n", "patience_counter = 0\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "IhOj-TLb5alo", "outputId": "71bb7725-f977-42c6-be59-38e328bc8332"}, "outputs": [{"output_type": "stream", "name": "stderr", "text": ["PointNet++ Training:  20%|██        | 10/50 [08:41<34:37, 51.94s/it]"]}, {"output_type": "stream", "name": "stdout", "text": ["Epoch 10/50:\n", "  Train Loss: 0.2535, Train Acc: 0.9007\n", "  Val Loss: 0.2546, Val Acc: 0.8996\n"]}, {"output_type": "stream", "name": "stderr", "text": ["PointNet++ Training:  32%|███▏      | 16/50 [14:43<31:18, 55.24s/it]"]}, {"output_type": "stream", "name": "stdout", "text": ["Early stopping at epoch 17\n", "Training completed. Best validation accuracy: 0.9121\n"]}, {"output_type": "stream", "name": "stderr", "text": ["\n"]}], "source": ["from tqdm import tqdm\n", "\n", "pbar = tqdm(range(num_epochs), desc=\"PointNet++ Training\")\n", "\n", "for epoch in pbar:\n", "    train_loss, train_acc = train_epoch(model, train_loader, criterion, optimizer, device)\n", "    val_loss, val_acc = validate_epoch(model, val_loader, criterion, device)\n", "\n", "    train_losses.append(train_loss)\n", "    val_losses.append(val_loss)\n", "    train_accs.append(train_acc)\n", "    val_accs.append(val_acc)\n", "\n", "    if (epoch + 1) % 10 == 0:\n", "        print(f\"Epoch {epoch+1}/{num_epochs}:\")\n", "        print(f\"  Train Loss: {train_loss:.4f}, Train Acc: {train_acc:.4f}\")\n", "        print(f\"  Val Loss: {val_loss:.4f}, Val Acc: {val_acc:.4f}\")\n", "\n", "    # Early stopping\n", "    if val_acc > best_val_acc:\n", "        best_val_acc = val_acc\n", "        patience_counter = 0\n", "        if save_model:\n", "            torch.save(model.state_dict(), 'best_pointnet_plus_plus.pth')\n", "    else:\n", "        patience_counter += 1\n", "        if patience_counter >= patience:\n", "            print(f\"Early stopping at epoch {epoch+1}\")\n", "            break\n", "print(f\"Training completed. Best validation accuracy: {best_val_acc:.4f}\")"]}, {"cell_type": "markdown", "metadata": {"id": "P7Znbj0E5alo"}, "source": ["## Evaluation"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "lKuowIZk5alo", "outputId": "66020254-b8f5-4a8d-f897-ae99d713956c"}, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["=== POINTNET++ TEST RESULTS ===\n", "Accuracy: 0.9052\n", "Precision: 0.9232\n", "Recall: 0.9617\n", "F1-Score: 0.9421\n"]}], "source": ["# Load best model for evaluation\n", "if save_model:\n", "    model.load_state_dict(torch.load('best_pointnet_plus_plus.pth'))\n", "\n", "# Test evaluation\n", "model.eval()\n", "all_preds = []\n", "all_targets = []\n", "\n", "with torch.no_grad():\n", "    for data, target in test_loader:\n", "        data, target = data.to(device), target.to(device)\n", "        output = model(data)\n", "        pred = output.argmax(dim=1)\n", "\n", "        all_preds.extend(pred.cpu().numpy())\n", "        all_targets.extend(target.cpu().numpy())\n", "\n", "# Calculate metrics\n", "test_accuracy = accuracy_score(all_targets, all_preds)\n", "test_precision = precision_score(all_targets, all_preds)\n", "test_recall = recall_score(all_targets, all_preds)\n", "test_f1 = f1_score(all_targets, all_preds)\n", "\n", "print(\"=== POINTNET++ TEST RESULTS ===\")\n", "print(f\"Accuracy: {test_accuracy:.4f}\")\n", "print(f\"Precision: {test_precision:.4f}\")\n", "print(f\"Recall: {test_recall:.4f}\")\n", "print(f\"F1-Score: {test_f1:.4f}\")"]}, {"cell_type": "code", "source": ["from collections import Counter\n", "import numpy as np\n", "\n", "pred_dist = Counter(all_preds)\n", "target_dist = Counter(all_targets)\n", "\n", "print(\"=== PREDICTION ANALYSIS ===\")\n", "print(f\"True positives: {target_dist[1]} ({target_dist[1]/len(all_targets)*100:.1f}%)\")\n", "print(f\"Predicted positives: {pred_dist[1]} ({pred_dist[1]/len(all_preds)*100:.1f}%)\")\n", "\n", "# Check if model is just predicting everything as positive\n", "if pred_dist[1]/len(all_preds) > 0.95:\n", "    print(\"Model might be biased - predicting almost everything as positive\")\n", "else:\n", "    print(\"Model shows good discrimination between classes\")\n", "\n", "# Per-class metrics\n", "from sklearn.metrics import classification_report\n", "print(\"\\n=== PER-CLASS PERFORMANCE ===\")\n", "print(classification_report(all_targets, all_preds, target_names=['Non-pile', 'Pile']))"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "HxmcIMg4kfgm", "outputId": "87eedb67-035a-48a9-de1a-004dd0732335"}, "execution_count": null, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["=== PREDICTION ANALYSIS ===\n", "True positives: 575 (80.2%)\n", "Predicted positives: 599 (83.5%)\n", "Model shows good discrimination between classes\n", "\n", "=== PER-CLASS PERFORMANCE ===\n", "              precision    recall  f1-score   support\n", "\n", "    Non-pile       0.81      0.68      0.74       142\n", "        <PERSON>le       0.92      0.96      0.94       575\n", "\n", "    accuracy                           0.91       717\n", "   macro avg       0.87      0.82      0.84       717\n", "weighted avg       0.90      0.91      0.90       717\n", "\n"]}]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "APJaegye5alo", "outputId": "d75d1649-8896-43a2-cdf0-609b4eeddbb7"}, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["\n", "=== COMPARISON WITH RULE-BASED BASELINE ===\n", "Rule-based F1: 0.9320\n", "PointNet++ F1: 0.9421\n", "Improvement: 1.1%\n"]}], "source": ["# Compare with rule-based baseline\n", "print(\"\\n=== COMPARISON WITH RULE-BASED BASELINE ===\")\n", "print(f\"Rule-based F1: {BASELINE_F1:.4f}\")\n", "print(f\"PointNet++ F1: {test_f1:.4f}\")\n", "improvement = ((test_f1 - BASELINE_F1) / BASELINE_F1) * 100\n", "print(f\"Improvement: {improvement:.1f}%\")\n", "\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "hXpPV7cC5alo", "outputId": "01df20b8-988c-43e4-882e-1fdc5d29679a"}, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["\n", "Results saved to pointnet_plus_plus_results.json\n", "Ready for DGCNN comparison.\n"]}], "source": ["# Save results\n", "results = {\n", "    'model': 'PointNet++',\n", "    'test_metrics': {\n", "        'accuracy': float(test_accuracy),\n", "        'precision': float(test_precision),\n", "        'recall': float(test_recall),\n", "        'f1_score': float(test_f1)\n", "    },\n", "    'training_info': {\n", "        'num_epochs': len(train_losses),\n", "        'best_val_acc': float(best_val_acc),\n", "        'final_train_acc': float(train_accs[-1]),\n", "        'final_val_acc': float(val_accs[-1])\n", "    },\n", "    'comparison': {\n", "        'rule_based_f1': float(BASELINE_F1),\n", "        'pointnet_plus_plus_f1': float(test_f1),\n", "        'improvement_percent': float(improvement)\n", "    }\n", "}\n", "\n", "with open('pointnet_plus_plus_results.json', 'w') as f:\n", "    json.dump(results, f, indent=2)\n", "\n", "print(f\"\\nResults saved to pointnet_plus_plus_results.json\")\n", "print(\"Ready for DGCNN comparison.\")"]}, {"cell_type": "code", "source": ["import shutil\n", "from pathlib import Path\n", "\n", "# Destination path – replace with your actual drive mount path if using Colab\n", "dest_dir = Path(\"/content/drive/MyDrive/pointnet_results/\")\n", "dest_dir.mkdir(parents=True, exist_ok=True)\n", "\n", "# Move model checkpoint\n", "shutil.move(\"best_pointnet_plus_plus.pth\", dest_dir / \"best_pointnet_plus_plus.pth\")\n", "\n", "# Move JSON results\n", "shutil.move(\"pointnet_plus_plus_results.json\", dest_dir / \"pointnet_plus_plus_results.json\")\n", "\n", "print(\"Model and results moved to Google Drive.\")\n"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "thYpWxET_thO", "outputId": "7210fdc5-e7c2-4980-c0df-b186c3ac3166"}, "execution_count": null, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Model and results moved to Google Drive.\n"]}]}], "metadata": {"kernelspec": {"display_name": "Python 3", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}, "colab": {"provenance": [], "gpuType": "T4"}, "accelerator": "GPU"}, "nbformat": 4, "nbformat_minor": 0}