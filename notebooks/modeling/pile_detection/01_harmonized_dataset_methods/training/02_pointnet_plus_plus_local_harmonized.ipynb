{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# PointNet++ <PERSON>le Detection\n", "\n", "This notebook implements PointNet++ architecture for pile detection using the patch data prepared from the successful harmonization and extraction pipeline.\n", "\n", "\n", "**Architecture:**\n", "- Set abstraction layers for hierarchical feature learning\n", "- Multi-scale grouping for robust feature extraction\n", "- Feature propagation for dense prediction\n", "- Binary classification (pile vs non-pile)\n", "\n", "**Author**: <PERSON><PERSON><PERSON>  \n", "**Date**: July 2025  \n", "**Project**: As-Built Foundation Analysis"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["=== POINTNET++ PILE DETECTION ===\n", "Goal: Beat rule-based baseline F1 of 0.932\n", "Goal: Beat rule-based baseline accuracy of 0.885\n"]}], "source": ["import sys\n", "from pathlib import Path\n", "import pickle\n", "import json\n", "\n", "# Parameters\n", "batch_size = 4 #16\n", "num_epochs = 5  # 100\n", "learning_rate = 0.001\n", "num_points = 128 #256 #512  # Reduced from 1024 to match your data size\n", "save_model = True\n", "\n", "# Baseline performance to beat (from your rule-based classifier)\n", "BASELINE_F1 = 0.932  # 93.2% F1-score from rule-based\n", "BASELINE_ACCURACY = 0.885  # 88.5% accuracy\n", "\n", "print(\"=== POINTNET++ PILE DETECTION ===\")\n", "print(f\"Goal: Beat rule-based baseline F1 of {BASELINE_F1:.3f}\")\n", "print(f\"Goal: Beat rule-based baseline accuracy of {BASELINE_ACCURACY:.3f}\")"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Using device: mps\n"]}], "source": ["import torch\n", "import torch.nn as nn\n", "import torch.optim as optim\n", "from torch.utils.data import Dataset, DataLoader\n", "import numpy as np\n", "from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score, confusion_matrix\n", "import matplotlib.pyplot as plt\n", "from datetime import datetime\n", "import warnings\n", "warnings.filterwarnings('ignore')\n", "\n", "#device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')\n", "device = torch.device('mps' if torch.backends.mps.is_available() else 'cpu')\n", "\n", "print(f\"Using device: {device}\")\n"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["total 21376\n", "-rw-r--r--@ 1 <USER>  <GROUP>   415B Jul 22 16:02 config.json\n", "-rw-r--r--@ 1 <USER>  <GROUP>   2.6K Jul 22 16:33 enhanced_classification_metrics.json\n", "-rw-r--r--@ 1 <USER>  <GROUP>   1.5M Jul 22 17:21 feature_analysis_results.csv\n", "-rw-r--r--@ 1 <USER>  <GROUP>   232K Jul 22 17:21 feature_analysis_test.csv\n", "-rw-r--r--@ 1 <USER>  <GROUP>   1.1M Jul 22 17:21 feature_analysis_train.csv\n", "-rw-r--r--@ 1 <USER>  <GROUP>   231K Jul 22 17:21 feature_analysis_val.csv\n", "-rw-r--r--@ 1 <USER>  <GROUP>   1.1K Jul 22 17:21 feature_comparison.csv\n", "-rw-r--r--@ 1 <USER>  <GROUP>   206K Jul 22 16:02 test_metadata.json\n", "-rw-r--r--@ 1 <USER>  <GROUP>   699K Jul 22 16:02 test_patches.pkl\n", "-rw-r--r--@ 1 <USER>  <GROUP>   962K Jul 22 16:02 train_metadata.json\n", "-rw-r--r--@ 1 <USER>  <GROUP>   3.2M Jul 22 16:02 train_patches.pkl\n", "-rw-r--r--@ 1 <USER>  <GROUP>   206K Jul 22 16:02 val_metadata.json\n", "-rw-r--r--@ 1 <USER>  <GROUP>   705K Jul 22 16:02 val_patches.pkl\n"]}], "source": ["!ls -lh ../../00_data_preprocessing/output/ml_patch_data/patches_20250722_160244"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Data Loading"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["import pickle\n", "import json\n", "from pathlib import Path\n", "\n", "\n", "def load_preprocessed_patches(base_dir=\"../../00_data_preprocessing/output/ml_patch_data\"):\n", "    \"\"\"\n", "    Load pre-extracted and labeled patch datasets from the most recent patch folder.\n", "    Assumes the structure:\n", "      ../../00_data_preprocessing/output/ml_patch_data/patches_<timestamp>/{train,val,test}_patches.pkl\n", "    \"\"\"\n", "    try:\n", "        patch_root = Path(base_dir).resolve()\n", "        if not patch_root.exists():\n", "            raise FileNotFoundError(f\"Patch root not found: {patch_root}\")\n", "\n", "        # Auto-detect latest patch directory\n", "        latest_patch_dir = sorted(patch_root.glob(\"patches_*\"))[-1]\n", "        print(f\"Using latest patch data from: {latest_patch_dir}\")\n", "\n", "        datasets = {}\n", "        for split in ['train', 'val', 'test']:\n", "            patch_file = latest_patch_dir / f\"{split}_patches.pkl\"\n", "            meta_file = latest_patch_dir / f\"{split}_metadata.json\"\n", "\n", "            if not patch_file.exists() or not meta_file.exists():\n", "                raise FileNotFoundError(f\"Missing files for '{split}':\\n  - {patch_file}\\n  - {meta_file}\")\n", "\n", "            datasets[split] = {\n", "                'patches': pickle.load(open(patch_file, 'rb')),\n", "                'metadata': json.load(open(meta_file))\n", "            }\n", "            print(f\"  {split.capitalize()}: {len(datasets[split]['patches'])} patches\")\n", "\n", "        return datasets\n", "\n", "    except Exception as e:\n", "        print(f\"Failed to load patch data: {e}\")\n", "        raise\n", "\n", "\n", "def sample_patch_to_fixed_size(patch, target_n=512, noise_scale=0.01):\n", "    \"\"\"Randomly sample or pad a point patch to a fixed number of points\"\"\"\n", "    patch = np.array(patch, dtype=np.float32)\n", "    n = len(patch)\n", "\n", "    if n >= target_n:\n", "        return patch[np.random.choice(n, target_n, replace=False)]\n", "\n", "    # Pad with jittered copies of existing points\n", "    extra = np.stack([\n", "        patch[np.random.randint(n)] + np.random.normal(0, noise_scale, 3)\n", "        for _ in range(target_n - n)\n", "    ])\n", "    return np.vstack([patch, extra])\n", "\n", "\n", "def preprocess_patches_for_pointnet(patches, metadata, num_points=128):\n", "    \"\"\"Convert patches and metadata into PointNet++-ready format\"\"\"\n", "    out_patches, out_labels = [], []\n", "\n", "    for patch, meta in zip(patches, metadata):\n", "        if len(patch) < 10:\n", "            continue  # Ignore too-small patches\n", "\n", "        patch_fixed = sample_patch_to_fixed_size(patch, num_points)\n", "        patch_fixed /= np.max(np.linalg.norm(patch_fixed, axis=1)) or 1  # Normalize\n", "\n", "        out_patches.append(patch_fixed)\n", "        out_labels.append(int(meta.get('label', meta.get('patch_type') == 'positive')))\n", "\n", "    return np.array(out_patches, dtype=np.float32), np.array(out_labels, dtype=np.int64)"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Using latest patch data from: /Users/<USER>/Documents/GitHub/asbuilt-foundation-analysis/notebooks/modeling/pile_detection/00_data_preprocessing/output/ml_patch_data/patches_20250722_160244\n", "  Train: 3345 patches\n", "  Val: 717 patches\n", "  Test: 717 patches\n", "Formatting patches for PointNet++...\n", "\n", "Final shapes:\n", "Train: <PERSON><PERSON> (3345, 128, 3), <PERSON><PERSON> (3345,)\n", "Val: <PERSON><PERSON> (717, 128, 3), <PERSON><PERSON> (717,)\n", "Test: <PERSON><PERSON> (717, 128, 3), <PERSON><PERSON> (717,)\n", "\n", "Class distribution:\n", "  Train: 2684 positive, 661 negative (80.2% positive)\n", "  Val: 576 positive, 141 negative (80.3% positive)\n", "  Test: 575 positive, 142 negative (80.2% positive)\n"]}], "source": ["datasets = load_preprocessed_patches()\n", "print(\"Formatting patches for PointNet++...\")\n", "\n", "train_patches, train_labels = preprocess_patches_for_pointnet(**datasets['train'])\n", "val_patches, val_labels = preprocess_patches_for_pointnet(**datasets['val'])\n", "test_patches, test_labels = preprocess_patches_for_pointnet(**datasets['test'])\n", "\n", "print(f\"\\nFinal shapes:\")\n", "for split, data in zip(['<PERSON>', 'Val', 'Test'],\n", "                       [(train_patches, train_labels), (val_patches, val_labels), (test_patches, test_labels)]):\n", "    print(f\"{split}: Patches {data[0].shape}, Labels {data[1].shape}\")\n", "\n", "# Check class distribution\n", "print(f\"\\nClass distribution:\")\n", "for split, labels in [('Train', train_labels), ('Val', val_labels), ('Test', test_labels)]:\n", "    pos_count = np.sum(labels)\n", "    neg_count = len(labels) - pos_count\n", "    print(f\"  {split}: {pos_count} positive, {neg_count} negative ({pos_count/len(labels)*100:.1f}% positive)\")\n", "\n"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import matplotlib.pyplot as plt\n", "\n", "def plot_patch(patch, title):\n", "    fig = plt.figure()\n", "    ax = fig.add_subplot(111, projection='3d')\n", "    ax.scatter(patch[:, 0], patch[:, 1], patch[:, 2], s=1)\n", "    ax.set_title(title)\n", "    plt.show()\n", "\n", "# Show one positive and negative sample\n", "plot_patch(train_patches[train_labels == 1][0], \"Positive Patch\")\n", "plot_patch(train_patches[train_labels == 0][0], \"Negative Patch\")\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Dataset and DataLoader"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Train: 3345, Val: 717, Test: 717\n"]}], "source": ["from torch.utils.data import DataLoader, Dataset\n", "import torch\n", "\n", "class PileDataset(Dataset):\n", "    def __init__(self, points, labels):\n", "        self.points = torch.FloatTensor(points)\n", "        self.labels = torch.LongTensor(labels)\n", "    \n", "    def __len__(self):\n", "        return len(self.points)\n", "    \n", "    def __getitem__(self, idx):\n", "        return self.points[idx], self.labels[idx]\n", "\n", "# Create datasets\n", "train_dataset = PileDataset(train_patches, train_labels)\n", "val_dataset = PileDataset(val_patches, val_labels)\n", "test_dataset = PileDataset(test_patches, test_labels)\n", "\n", "# Create dataloaders\n", "batch_size = 16  # or whatever value you use\n", "#train_loader = DataLoader(train_dataset, batch_size=batch_size, shuffle=True)\n", "train_loader = DataLoader(train_dataset, batch_size=16, shuffle=True, drop_last=True)\n", "\n", "val_loader = DataLoader(val_dataset, batch_size=batch_size, shuffle=False)\n", "test_loader = DataLoader(test_dataset, batch_size=batch_size, shuffle=False)\n", "\n", "print(f\"Train: {len(train_dataset)}, Val: {len(val_dataset)}, Test: {len(test_dataset)}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## PointNet++ Architecture"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [], "source": ["def square_distance(src, dst):\n", "    \"\"\"Calculate squared distance between points\"\"\"\n", "    B, N, _ = src.shape\n", "    _, M, _ = dst.shape\n", "    dist = -2 * torch.matmul(src, dst.permute(0, 2, 1))\n", "    dist += torch.sum(src ** 2, -1).view(B, N, 1)\n", "    dist += torch.sum(dst ** 2, -1).view(B, 1, M)\n", "    return dist\n", "\n", "def farthest_point_sample(xyz, npoint):\n", "    \"\"\"Farthest point sampling\"\"\"\n", "    device = xyz.device\n", "    B, N, C = xyz.shape\n", "    centroids = torch.zeros(B, npoint, dtype=torch.long).to(device)\n", "    distance = torch.ones(B, N).to(device) * 1e10\n", "    farthest = torch.randint(0, N, (B,), dtype=torch.long).to(device)\n", "    batch_indices = torch.arange(B, dtype=torch.long).to(device)\n", "    \n", "    for i in range(npoint):\n", "        centroids[:, i] = farthest\n", "        centroid = xyz[batch_indices, farthest, :].view(B, 1, 3)\n", "        dist = torch.sum((xyz - centroid) ** 2, -1)\n", "        mask = dist < distance\n", "        distance[mask] = dist[mask]\n", "        farthest = torch.max(distance, -1)[1]\n", "    \n", "    return centroids\n", "\n", "def query_ball_point(radius, nsample, xyz, new_xyz):\n", "    \"\"\"Ball query\"\"\"\n", "    device = xyz.device\n", "    B, N, C = xyz.shape\n", "    _, S, _ = new_xyz.shape\n", "    group_idx = torch.arange(N, dtype=torch.long).to(device).view(1, 1, N).repeat([B, S, 1])\n", "    sqrdists = square_distance(new_xyz, xyz)\n", "    group_idx[sqrdists > radius ** 2] = N\n", "    group_idx = group_idx.sort(dim=-1)[0][:, :, :nsample]\n", "    group_first = group_idx[:, :, 0].view(B, S, 1).repeat([1, 1, nsample])\n", "    mask = group_idx == N\n", "    group_idx[mask] = group_first[mask]\n", "    return group_idx\n", "\n", "class PointNetSetAbstraction(nn.Module):\n", "    def __init__(self, npoint, radius, nsample, in_channel, mlp, group_all):\n", "        super(PointNetSetAbstraction, self).__init__()\n", "        self.npoint = npoint\n", "        self.radius = radius\n", "        self.nsample = nsample\n", "        self.mlp_convs = nn.ModuleList()\n", "        self.mlp_bns = nn.ModuleList()\n", "        self.group_all = group_all\n", "        \n", "        last_channel = in_channel\n", "        for out_channel in mlp:\n", "            self.mlp_convs.append(nn.Conv2d(last_channel, out_channel, 1))\n", "            self.mlp_bns.append(nn.BatchNorm2d(out_channel))\n", "            last_channel = out_channel\n", "    \n", "    def forward(self, xyz, points):\n", "        B, N, C = xyz.shape\n", "        \n", "        if self.group_all:\n", "            new_xyz = torch.zeros(B, 1, C).to(xyz.device)\n", "            new_points = points.view(B, 1, N, -1)\n", "        else:\n", "            fps_idx = farthest_point_sample(xyz, self.npoint)\n", "            new_xyz = xyz[torch.arange(B)[:, None], fps_idx]\n", "            idx = query_ball_point(self.radius, self.nsample, xyz, new_xyz)\n", "            grouped_xyz = xyz[torch.arange(B)[:, None, None], idx]\n", "            grouped_xyz_norm = grouped_xyz - new_xyz.view(B, self.npoint, 1, C)\n", "            \n", "            if points is not None:\n", "                grouped_points = points[torch.arange(B)[:, None, None], idx]\n", "                new_points = torch.cat([grouped_xyz_norm, grouped_points], dim=-1)\n", "            else:\n", "                new_points = grouped_xyz_norm\n", "        \n", "        new_points = new_points.permute(0, 3, 2, 1)\n", "        for i, conv in enumerate(self.mlp_convs):\n", "            bn = self.mlp_bns[i]\n", "            new_points = torch.relu(bn(conv(new_points)))\n", "        \n", "        new_points = torch.max(new_points, 2)[0]\n", "        new_points = new_points.permute(0, 2, 1)\n", "        \n", "        return new_xyz, new_points"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Model initialized with 1,465,154 parameters\n"]}], "source": ["class PointNetPlusPlus(nn.Module):\n", "    def __init__(self, num_classes=2):\n", "        super(PointNetPlusPlus, self).__init__()\n", "        \n", "        # Set abstraction layers\n", "        # self.sa1 = PointNetSetAbstraction(512, 0.2, 32, 3, [64, 64, 128], False)\n", "        # self.sa2 = PointNetSetAbstraction(128, 0.4, 64, 128 + 3, [128, 128, 256], False)\n", "        # self.sa3 = PointNetSetAbstraction(None, None, None, 256 + 3, [256, 512, 1024], True)\n", "        \n", "        # self.sa1 = PointNetSetAbstraction(512, 0.2, 32, 3, [64, 64, 128], False)\n", "        # self.sa2 = PointNetSetAbstraction(128, 0.4, 64, 128, [128, 128, 256], False)\n", "        # self.sa3 = PointNetSetAbstraction(None, None, None, 256, [256, 512, 1024], True)\n", "\n", "        self.sa1 = PointNetSetAbstraction(512, 0.2, 32, 3, [64, 64, 128], False)           # 3\n", "        self.sa2 = PointNetSetAbstraction(128, 0.4, 64, 128 + 3, [128, 128, 256], False)   # 128+3=131\n", "        self.sa3 = PointNetSetAbstraction(None, None, None, 256, [256, 512, 1024], True)    # 256 only\n", "\n", "        # Classification head\n", "        self.fc1 = nn.Linear(1024, 512)\n", "        self.bn1 = nn.BatchNorm1d(512)\n", "        self.drop1 = nn.Dropout(0.4)\n", "        self.fc2 = nn.Linear(512, 256)\n", "        self.bn2 = nn.BatchNorm1d(256)\n", "        self.drop2 = nn.Dropout(0.4)\n", "        self.fc3 = nn.Linear(256, num_classes)\n", "    \n", "    def forward(self, xyz):\n", "        if xyz.shape[1] == 3:\n", "            xyz = xyz.transpose(1, 2).contiguous()  # Ensure shape is (B, N, 3)\n", "\n", "        B, _, _ = xyz.shape\n", "        \n", "        l1_xyz, l1_points = self.sa1(xyz, None)\n", "        l2_xyz, l2_points = self.sa2(l1_xyz, l1_points)\n", "        l3_xyz, l3_points = self.sa3(l2_xyz, l2_points)\n", "        \n", "        x = l3_points.view(B, 1024)\n", "        x = self.drop1(torch.relu(self.bn1(self.fc1(x))))\n", "        x = self.drop2(torch.relu(self.bn2(self.fc2(x))))\n", "        x = self.fc3(x)\n", "        \n", "        return x\n", "\n", "# Initialize model\n", "model = PointNetPlusPlus(num_classes=2).to(device)\n", "criterion = nn.CrossEntropyLoss()\n", "optimizer = optim.Adam(model.parameters(), lr=learning_rate)\n", "\n", "print(f\"Model initialized with {sum(p.numel() for p in model.parameters()):,} parameters\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Training"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [], "source": ["def train_epoch(model, loader, criterion, optimizer, device):\n", "    model.train()\n", "    total_loss = 0\n", "    correct = 0\n", "    total = 0\n", "\n", "    for batch_idx, (data, target) in enumerate(loader):\n", "        data, target = data.to(device), target.to(device)\n", "\n", "        optimizer.zero_grad()\n", "        output = model(data)\n", "        loss = criterion(output, target)\n", "        loss.backward()\n", "        optimizer.step()\n", "\n", "        total_loss += loss.item()\n", "        pred = output.argmax(dim=1)\n", "        correct += pred.eq(target).sum().item()\n", "        total += target.size(0)\n", "\n", "    return total_loss / len(loader), correct / total\n", "\n", "def validate_epoch(model, loader, criterion, device):\n", "    model.eval()\n", "    total_loss = 0\n", "    correct = 0\n", "    total = 0\n", "\n", "    with torch.no_grad():\n", "        for data, target in loader:\n", "            data, target = data.to(device), target.to(device)\n", "            output = model(data)\n", "            loss = criterion(output, target)\n", "\n", "            total_loss += loss.item()\n", "            pred = output.argmax(dim=1)\n", "            correct += pred.eq(target).sum().item()\n", "            total += target.size(0)\n", "\n", "    return total_loss / len(loader), correct / total\n"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Starting training...\n"]}], "source": ["print(\"Starting training...\")\n", "\n", "train_losses = []\n", "val_losses = []\n", "train_accs = []\n", "val_accs = []\n", "\n", "best_val_acc = 0\n", "patience = 10\n", "patience_counter = 0\n"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["PointNet++ Training: 100%|██████████| 5/5 [21:02<00:00, 252.53s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["Training completed. Best validation accuracy: 0.9121\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}], "source": ["from tqdm import tqdm\n", "\n", "pbar = tqdm(range(num_epochs), desc=\"PointNet++ Training\")\n", "\n", "for epoch in pbar:\n", "    train_loss, train_acc = train_epoch(model, train_loader, criterion, optimizer, device)\n", "    val_loss, val_acc = validate_epoch(model, val_loader, criterion, device)\n", "\n", "    train_losses.append(train_loss)\n", "    val_losses.append(val_loss)\n", "    train_accs.append(train_acc)\n", "    val_accs.append(val_acc)\n", "\n", "    if (epoch + 1) % 10 == 0:\n", "        print(f\"Epoch {epoch+1}/{num_epochs}:\")\n", "        print(f\"  Train Loss: {train_loss:.4f}, Train Acc: {train_acc:.4f}\")\n", "        print(f\"  Val Loss: {val_loss:.4f}, Val Acc: {val_acc:.4f}\")\n", "\n", "    # Early stopping\n", "    if val_acc > best_val_acc:\n", "        best_val_acc = val_acc\n", "        patience_counter = 0\n", "        if save_model:\n", "            torch.save(model.state_dict(), 'best_pointnet_plus_plus.pth')\n", "    else:\n", "        patience_counter += 1\n", "        if patience_counter >= patience:\n", "            print(f\"Early stopping at epoch {epoch+1}\")\n", "            break\n", "print(f\"Training completed. Best validation accuracy: {best_val_acc:.4f}\")"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Device: mps\n", "MPS available: True\n", "Model device: mps:0\n"]}], "source": ["# Check if MPS is actually working:\n", "print(f\"Device: {device}\")\n", "print(f\"MPS available: {torch.backends.mps.is_available()}\")\n", "print(f\"Model device: {next(model.parameters()).device}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Evaluation"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Load best model for evaluation\n", "if save_model:\n", "    model.load_state_dict(torch.load('best_pointnet_plus_plus.pth'))\n", "\n", "# Test evaluation\n", "model.eval()\n", "all_preds = []\n", "all_targets = []\n", "\n", "with torch.no_grad():\n", "    for data, target in test_loader:\n", "        data, target = data.to(device), target.to(device)\n", "        output = model(data)\n", "        pred = output.argmax(dim=1)\n", "        \n", "        all_preds.extend(pred.cpu().numpy())\n", "        all_targets.extend(target.cpu().numpy())\n", "\n", "# Calculate metrics\n", "test_accuracy = accuracy_score(all_targets, all_preds)\n", "test_precision = precision_score(all_targets, all_preds)\n", "test_recall = recall_score(all_targets, all_preds)\n", "test_f1 = f1_score(all_targets, all_preds)\n", "\n", "print(\"=== POINTNET++ TEST RESULTS ===\")\n", "print(f\"Accuracy: {test_accuracy:.4f}\")\n", "print(f\"Precision: {test_precision:.4f}\")\n", "print(f\"Recall: {test_recall:.4f}\")\n", "print(f\"F1-Score: {test_f1:.4f}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Compare with rule-based baseline\n", "print(\"\\n=== COMPARISON WITH RULE-BASED BASELINE ===\")\n", "print(f\"Rule-based F1: {BASELINE_F1:.4f}\")\n", "print(f\"PointNet++ F1: {test_f1:.4f}\")\n", "improvement = ((test_f1 - BASELINE_F1) / BASELINE_F1) * 100\n", "print(f\"Improvement: {improvement:.1f}%\")\n", "\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Save results\n", "results = {\n", "    'model': 'PointNet++',\n", "    'test_metrics': {\n", "        'accuracy': float(test_accuracy),\n", "        'precision': float(test_precision),\n", "        'recall': float(test_recall),\n", "        'f1_score': float(test_f1)\n", "    },\n", "    'training_info': {\n", "        'num_epochs': len(train_losses),\n", "        'best_val_acc': float(best_val_acc),\n", "        'final_train_acc': float(train_accs[-1]),\n", "        'final_val_acc': float(val_accs[-1])\n", "    },\n", "    'comparison': {\n", "        'rule_based_f1': float(BASELINE_F1),  \n", "        'pointnet_plus_plus_f1': float(test_f1),\n", "        'improvement_percent': float(improvement)\n", "    }\n", "}\n", "\n", "with open('pointnet_plus_plus_results.json', 'w') as f:\n", "    json.dump(results, f, indent=2)\n", "\n", "print(f\"\\nResults saved to pointnet_plus_plus_results.json\")\n", "print(\"Ready for DGCNN comparison.\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 4}