{"cells": [{"cell_type": "markdown", "metadata": {"id": "UnkeVcpAGr_W"}, "source": ["# DGCNN Pile Classification - 3D Coordinates\n", "\n", "This notebook implements Dynamic Graph CNN (DGCNN) architecture for pile detection using the same dataset as PointNet++ but focusing on 3D spatial coordinates only.\n", "\n", "**Key Differences from PointNet++:**\n", "- Uses only 3D coordinates (x, y, z) instead of 20 features\n", "- Dynamic graph construction with k-nearest neighbors\n", "- EdgeConv operations for local feature aggregation\n", "- Focuses on geometric relationships rather than feature engineering\n", "\n", "**Architecture:**\n", "- Input: (batch_size, 1024, 3) - 3D coordinates\n", "- Multi-scale EdgeConv layers\n", "- Global feature aggregation\n", "- Binary classification (pile vs non-pile)\n", "\n", "**Author**: <PERSON><PERSON><PERSON>  \n", "**Date**: July 2025  \n", "**Project**: As-Built Foundation Analysis"]}, {"cell_type": "code", "execution_count": 1, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "rOY3P6NvG910", "outputId": "6aea859e-9387-408b-a107-27f03681505a"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Drive already mounted at /content/drive; to attempt to forcibly remount, call drive.mount(\"/content/drive\", force_remount=True).\n"]}], "source": ["from google.colab import drive\n", "drive.mount('/content/drive')"]}, {"cell_type": "code", "execution_count": 6, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "glNVwMDlHDqV", "outputId": "a36dde2f-0573-4d55-d40a-cfa82f0f1ea2"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Using device: cuda\n"]}], "source": ["import torch\n", "import torch.nn as nn\n", "import torch.optim as optim\n", "import torch.nn.functional as F\n", "from torch.utils.data import Dataset, DataLoader\n", "import numpy as np\n", "import pickle\n", "import json\n", "from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score\n", "import matplotlib.pyplot as plt\n", "from tqdm import tqdm\n", "import warnings\n", "from pathlib import Path\n", "warnings.filterwarnings('ignore')\n", "\n", "\n", "device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')\n", "print(f\"Using device: {device}\")"]}, {"cell_type": "code", "execution_count": 7, "metadata": {"id": "spZHMrrQHHb5"}, "outputs": [], "source": ["# Configuration\n", "# Load and preprocess data using PointNet++ data path\n", "GDRIVE_BASE = \"/content/drive/MyDrive\"\n", "PROJECT_FOLDER = \"pointnet_pile_detection\"\n", "project_path = Path(GDRIVE_BASE) / PROJECT_FOLDER\n", "data_path = project_path / \"pointnet_data\"\n", "\n", "# Hyperparameters\n", "config = {\n", "    'batch_size': 8,\n", "    'num_epochs': 100,\n", "    'learning_rate': 0.0005,  # Same as PointNet++\n", "    'num_points': 1024,\n", "    'k': 20,\n", "    'patience': 20,\n", "    'weight_decay': 0.001,\n", "    'dropout': 0.3,\n", "    'gradient_clip': 0.5,\n", "}"]}, {"cell_type": "markdown", "metadata": {"id": "VxoHtNenHTBX"}, "source": ["## Data Load"]}, {"cell_type": "code", "execution_count": 8, "metadata": {"id": "vFuzcjxvHMGj"}, "outputs": [], "source": ["def load_pile_data(data_path):\n", "    \"\"\"Load data with all 20 features like PointNet++\"\"\"\n", "    datasets = {}\n", "    file_mapping = {\n", "        'train': 'train_pointnet.pkl',\n", "        'val': 'val_pointnet.pkl',\n", "        'test': 'test_pointnet.pkl'\n", "    }\n", "\n", "    for split, filename in file_mapping.items():\n", "        filepath = data_path / filename\n", "        with open(filepath, 'rb') as f:\n", "            data = pickle.load(f)\n", "\n", "        patches = data['points'].astype(np.float32)  # Keep all 20 features\n", "        labels = np.array(data['labels'])\n", "\n", "        datasets[split] = {\n", "            'patches': patches,\n", "            'labels': labels,\n", "            'metadata': data.get('metadata', [])\n", "        }\n", "\n", "    return datasets"]}, {"cell_type": "code", "execution_count": 9, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "uKHzvD2eHg-F", "outputId": "fc299e3d-86e1-441b-bb31-408125565d7a"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Loading PointNet++ data for DGCNN ...\n"]}], "source": ["# Load and preprocess data using PointNet++ data path\n", "print(\"Loading PointNet++ data for DGCNN ...\")\n", "datasets = load_pile_data(data_path)\n", "\n", "if datasets is None:\n", "    raise ValueError(\"Failed to load data!\")"]}, {"cell_type": "markdown", "metadata": {"id": "EWO8I_OPqeQE"}, "source": ["## Data Analysis\n", "\n", "Analyze data characteristics and class distribution."]}, {"cell_type": "code", "execution_count": 10, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "4DR-pMHnqeQE", "outputId": "87639352-d8ff-4be8-a6b7-105d55b7b6ae"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["=== CLASS DISTRIBUTION ANALYSIS ===\n", "\n", "Train set:\n", "  Non-pile: 426 (33.1%)\n", "  Pile:     861 (66.9%)\n", "\n", "Val set:\n", "  Non-pile: 131 (31.3%)\n", "  Pile:     287 (68.7%)\n", "\n", "Test set:\n", "  Non-pile: 131 (31.3%)\n", "  Pile:     287 (68.7%)\n", "\n", "Recommended class weights: Non-pile=1.511, <PERSON>le=0.747\n"]}], "source": ["print(\"=== CLASS DISTRIBUTION ANALYSIS ===\")\n", "\n", "# Convert labels to NumPy arrays\n", "label_data = {\n", "    \"train\": np.array(datasets['train']['labels']),\n", "    \"val\": np.array(datasets['val']['labels']),\n", "    \"test\": np.array(datasets['test']['labels'])\n", "}\n", "\n", "# Print distribution for each split\n", "for split, labels in label_data.items():\n", "    counts = np.bincount(labels)\n", "    total = len(labels)\n", "    print(f\"\\n{split.capitalize()} set:\")\n", "    print(f\"  Non-pile: {counts[0]} ({counts[0] / total * 100:.1f}%)\")\n", "    print(f\"  Pile:     {counts[1]} ({counts[1] / total * 100:.1f}%)\")\n", "\n", "# Calculate class weights for training\n", "train_counts = np.bincount(label_data[\"train\"])\n", "total_train = len(label_data[\"train\"])\n", "class_weights = total_train / (2 * train_counts)\n", "\n", "print(f\"\\nRecommended class weights: Non-pile={class_weights[0]:.3f}, Pile={class_weights[1]:.3f}\")"]}, {"cell_type": "code", "execution_count": 11, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "Bqx72aEiqeQF", "outputId": "27baa065-9aad-4a3b-833e-4e1c8839e8e9"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "=== GEOMETRIC CHARACTERISTICS ===\n", "Pile     - Extent: [0.13  0.129 0.13 ], Z-Var: 0.020\n", "Non-pile - Extent: [0.132 0.13  0.132], Z-Var: 0.020\n", "\n", "Height Diff: -0.002, Z-Var Diff: -0.000\n", "WARNING: Patches are too geometrically similar. Consider preserving subtle variations.\n"]}], "source": ["def analyze_geometry(patches, labels):\n", "    def stats(label):\n", "        idx = np.random.choice(np.where(labels == label)[0], 100, replace=False)\n", "        extent = [np.ptp(patches[i][:, :3], axis=0) for i in idx if len(patches[i])]\n", "        z_var = [np.std(patches[i][:, 2]) for i in idx if len(patches[i])]\n", "        return np.mean(extent, axis=0), np.mean(z_var)\n", "\n", "    pile_ext, pile_z = stats(1)\n", "    non_pile_ext, non_pile_z = stats(0)\n", "\n", "    print(\"\\n=== GEOMETRIC CHARACTERISTICS ===\")\n", "    print(f\"Pile     - Extent: {pile_ext.round(3)}, Z-Var: {pile_z:.3f}\")\n", "    print(f\"Non-pile - Extent: {non_pile_ext.round(3)}, Z-Var: {non_pile_z:.3f}\")\n", "\n", "    height_diff = pile_ext[2] - non_pile_ext[2]\n", "    z_var_diff = pile_z - non_pile_z\n", "\n", "    print(f\"\\nHeight Diff: {height_diff:.3f}, Z-Var Diff: {z_var_diff:.3f}\")\n", "    if abs(height_diff) < 0.01 and abs(z_var_diff) < 0.01:\n", "        print(\"WARNING: Patches are too geometrically similar. Consider preserving subtle variations.\")\n", "\n", "# Call after loading original dataset\n", "analyze_geometry(datasets['train']['patches'], datasets['train']['labels'])"]}, {"cell_type": "markdown", "metadata": {"id": "BfZLiXNYEnil"}, "source": ["## Preprocess Data"]}, {"cell_type": "code", "execution_count": 12, "metadata": {"id": "oy92xNBeHYcT"}, "outputs": [], "source": ["def preprocess_patches(patches, labels, num_points=1024):\n", "    \"\"\"Same preprocessing as PointNet++\"\"\"\n", "    processed_patches = []\n", "    processed_labels = []\n", "\n", "    for patch, label in zip(patches, labels):\n", "        patch = np.array(patch, dtype=np.float32)\n", "        n_points, n_features = patch.shape\n", "\n", "        if n_points >= num_points:\n", "            indices = np.random.choice(n_points, num_points, replace=False)\n", "            sampled = patch[indices]\n", "        else:\n", "            extra_indices = np.random.choice(n_points, num_points - n_points, replace=True)\n", "            extra = patch[extra_indices].copy()\n", "            if n_features >= 3:\n", "                extra[:, :3] += np.random.normal(0, 0.01, (len(extra), 3))\n", "            sampled = np.vstack([patch, extra])\n", "\n", "        # Normalize spatial coordinates only\n", "        if sampled.shape[1] >= 3:\n", "            spatial_coords = sampled[:, :3]\n", "            max_dist = np.max(np.linalg.norm(spatial_coords, axis=1))\n", "            if max_dist > 0:\n", "                sampled[:, :3] /= max_dist\n", "\n", "        processed_patches.append(sampled)\n", "        processed_labels.append(label)\n", "\n", "    return np.array(processed_patches), np.array(processed_labels)"]}, {"cell_type": "code", "execution_count": 13, "metadata": {"id": "L6POYN1WqeQF"}, "outputs": [], "source": ["train_patches, train_labels = preprocess_patches(\n", "    datasets['train']['patches'],\n", "    datasets['train']['labels'],\n", "    config['num_points']\n", ")\n", "\n", "val_patches, val_labels = preprocess_patches(\n", "    datasets['val']['patches'],\n", "    datasets['val']['labels'],\n", "    config['num_points']\n", ")\n", "\n", "test_patches, test_labels = preprocess_patches(\n", "    datasets['test']['patches'],\n", "    datasets['test']['labels'],\n", "    config['num_points']\n", ")"]}, {"cell_type": "code", "execution_count": 14, "metadata": {"id": "U4inMkURE6P5"}, "outputs": [], "source": ["class PileDataset(Dataset):\n", "    def __init__(self, points, labels):\n", "        self.points = torch.FloatTensor(points)\n", "        self.labels = torch.LongTensor(labels)\n", "\n", "    def __len__(self):\n", "        return len(self.points)\n", "\n", "    def __getitem__(self, idx):\n", "        return self.points[idx], self.labels[idx]\n"]}, {"cell_type": "code", "execution_count": 15, "metadata": {"id": "_up8hXWWEbU9"}, "outputs": [], "source": ["# Create datasets and loaders\n", "train_dataset = PileDataset(train_patches, train_labels)\n", "val_dataset = PileDataset(val_patches, val_labels)\n", "test_dataset = PileDataset(test_patches, test_labels)\n", "\n", "train_loader = DataLoader(train_dataset, batch_size=config['batch_size'], shuffle=True)\n", "val_loader = DataLoader(val_dataset, batch_size=config['batch_size'], shuffle=False)\n", "test_loader = DataLoader(test_dataset, batch_size=config['batch_size'], shuffle=False)"]}, {"cell_type": "code", "execution_count": 16, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "UQ5x3hgLHmGh", "outputId": "43c96908-aef3-477a-c5dc-02ddf2e4ed98"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Train: 66.9% pile\n", "Val: 68.7% pile\n", "Test: 68.7% pile\n", "\n", "Data verification:\n", "  Shape: (1024, 20)\n", "  Range: -0.000 to 5.589\n", "  Mean dist from origin: 0.999\n"]}], "source": ["# Class distribution check\n", "for name, labels in [('Train', train_labels), ('Val', val_labels), ('Test', test_labels)]:\n", "    print(f\"{name}: {np.mean(labels)*100:.1f}% pile\")\n", "\n", "# Data quality check on a sample\n", "print(\"\\nData verification:\")\n", "sample = train_patches[0]\n", "print(f\"  Shape: {sample.shape}\")\n", "print(f\"  Range: {sample.min():.3f} to {sample.max():.3f}\")\n", "print(f\"  Mean dist from origin: {np.mean(np.linalg.norm(sample[:, :3], axis=1)):.3f}\")"]}, {"cell_type": "markdown", "metadata": {"id": "_2K6tCZ9H9e7"}, "source": ["## DGCNN Architecture\n"]}, {"cell_type": "code", "execution_count": 17, "metadata": {"id": "DbnP8P5sH_4s"}, "outputs": [], "source": ["def knn(x, k):\n", "    \"\"\"Find k nearest neighbors\"\"\"\n", "    inner = -2 * torch.matmul(x.transpose(2, 1), x)\n", "    xx = torch.sum(x**2, dim=1, keepdim=True)\n", "    pairwise_distance = -xx - inner - xx.transpose(2, 1)\n", "    idx = pairwise_distance.topk(k=k, dim=-1)[1]\n", "    return idx\n", "\n", "def get_graph_feature(x, k=20, idx=None):\n", "    \"\"\"Create graph features from k-NN\"\"\"\n", "    batch_size = x.size(0)\n", "    num_points = x.size(2)\n", "    x = x.view(batch_size, -1, num_points)\n", "\n", "    if idx is None:\n", "        idx = knn(x, k=k)\n", "\n", "    device = x.device\n", "    idx_base = torch.arange(0, batch_size, device=device).view(-1, 1, 1) * num_points\n", "    idx = idx + idx_base\n", "    idx = idx.view(-1)\n", "\n", "    _, num_dims, _ = x.size()\n", "    x = x.transpose(2, 1).contiguous()\n", "    feature = x.view(batch_size * num_points, -1)[idx, :]\n", "    feature = feature.view(batch_size, num_points, k, num_dims)\n", "    x = x.view(batch_size, num_points, 1, num_dims).repeat(1, 1, k, 1)\n", "\n", "    feature = torch.cat((feature - x, x), dim=3).permute(0, 3, 1, 2).contiguous()\n", "    return feature"]}, {"cell_type": "code", "execution_count": 18, "metadata": {"id": "-_owMx76IEvY"}, "outputs": [], "source": ["class EdgeConv(nn.Module):\n", "    \"\"\"EdgeConv layer\"\"\"\n", "    def __init__(self, in_channels, out_channels, k=20):\n", "        super(<PERSON><PERSON><PERSON><PERSON>, self).__init__()\n", "        self.k = k\n", "        self.conv = nn.Sequential(\n", "            nn.Conv2d(in_channels * 2, out_channels, kernel_size=1, bias=False),\n", "            nn.BatchNorm2d(out_channels),\n", "            nn.LeakyReLU(negative_slope=0.2)\n", "        )\n", "\n", "    def forward(self, x):\n", "        x = get_graph_feature(x, k=self.k)\n", "        x = self.conv(x)\n", "        x = x.max(dim=-1, keepdim=False)[0]\n", "        return x\n", "\n", "class DGCNN(nn.Module):\n", "    \"\"\"Fixed DGCNN with 20 feature input\"\"\"\n", "    def __init__(self, num_classes=2, in_channels=20, k=20, dropout=0.3):\n", "        super(DGC<PERSON><PERSON>, self).__init__()\n", "        self.k = k\n", "\n", "        # Split input: 3D coords + features\n", "        self.coord_conv1 = EdgeConv(3, 64, k)\n", "        self.coord_conv2 = EdgeConv(64, 64, k)\n", "        self.coord_conv3 = EdgeConv(64, 128, k)\n", "\n", "        # Feature processing\n", "        self.feature_conv = nn.Sequential(\n", "            nn.Conv1d(in_channels - 3, 64, 1),\n", "            nn.<PERSON><PERSON><PERSON>orm1d(64),\n", "            nn.LeakyReLU(negative_slope=0.2)\n", "        )\n", "\n", "        # Combined processing\n", "        self.conv4 = EdgeConv(128 + 64, 256, k)\n", "\n", "        # Global feature aggregation\n", "        self.conv5 = nn.Sequential(\n", "            nn.Conv1d(64 + 64 + 128 + 256, 1024, kernel_size=1, bias=False),\n", "            nn.<PERSON>ch<PERSON>orm1d(1024),\n", "            nn.LeakyReLU(negative_slope=0.2)\n", "        )\n", "\n", "        # Classification head - simplified\n", "        self.classifier = nn.Sequential(\n", "            nn.<PERSON>(1024, 512),\n", "            nn.BatchNorm1d(512),\n", "            nn.LeakyReLU(negative_slope=0.2),\n", "            nn.Dropout(dropout),\n", "            nn.<PERSON><PERSON>(512, 256),\n", "            nn.BatchNorm1d(256),\n", "            nn.LeakyReLU(negative_slope=0.2),\n", "            nn.Dropout(dropout),\n", "            nn.Linear(256, num_classes)\n", "        )\n", "\n", "    def forward(self, x):\n", "        batch_size = x.size(0)\n", "\n", "        # Split coordinates and features\n", "        coords = x[:, :, :3].transpose(2, 1)  # (B, 3, N)\n", "        features = x[:, :, 3:].transpose(2, 1)  # (B, 17, N)\n", "\n", "        # Process coordinates with EdgeConv\n", "        x1 = self.coord_conv1(coords)  # (B, 64, N)\n", "        x2 = self.coord_conv2(x1)      # (B, 64, N)\n", "        x3 = self.coord_conv3(x2)      # (B, 128, N)\n", "\n", "        # Process features\n", "        feat = self.feature_conv(features)  # (B, 64, N)\n", "\n", "        # Combine and process\n", "        combined = torch.cat([x3, feat], dim=1)  # (B, 192, N)\n", "        x4 = self.conv4(combined)  # (B, 256, N)\n", "\n", "        # Concatenate all features\n", "        x = torch.cat((x1, x2, x3, x4), dim=1)  # (B, 512, N)\n", "\n", "        # Global feature extraction\n", "        x = self.conv5(x)  # (B, 1024, N)\n", "        x = F.adaptive_max_pool1d(x, 1).view(batch_size, -1)  # (B, 1024)\n", "\n", "        # Classification\n", "        x = self.classifier(x)\n", "        return x"]}, {"cell_type": "markdown", "metadata": {"id": "XM8vijOzIK9Y"}, "source": ["## Model Training"]}, {"cell_type": "code", "execution_count": 21, "metadata": {"id": "csyW86I9IOb8"}, "outputs": [], "source": ["def train_epoch(model, loader, criterion, optimizer, device):\n", "    model.train()\n", "    total_loss = 0\n", "    correct = 0\n", "    total = 0\n", "\n", "    for data, target in loader:\n", "        data, target = data.to(device), target.to(device)\n", "        optimizer.zero_grad()\n", "\n", "        output = model(data)\n", "        loss = criterion(output, target)\n", "        loss.backward()\n", "\n", "        torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=config['gradient_clip'])\n", "        optimizer.step()\n", "\n", "        total_loss += loss.item()\n", "        pred = output.argmax(dim=1)\n", "        correct += pred.eq(target).sum().item()\n", "        total += target.size(0)\n", "\n", "    return total_loss / len(loader), correct / total\n", "\n", "def validate_epoch(model, loader, criterion, device):\n", "    model.eval()\n", "    total_loss = 0\n", "    correct = 0\n", "    total = 0\n", "\n", "    with torch.no_grad():\n", "        for data, target in loader:\n", "            data, target = data.to(device), target.to(device)\n", "            output = model(data)\n", "            loss = criterion(output, target)\n", "\n", "            total_loss += loss.item()\n", "            pred = output.argmax(dim=1)\n", "            correct += pred.eq(target).sum().item()\n", "            total += target.size(0)\n", "\n", "    return total_loss / len(loader), correct / total\n"]}, {"cell_type": "code", "execution_count": 22, "metadata": {"id": "1teugZhxIYPS"}, "outputs": [], "source": ["def setup_training(model, train_labels):\n", "    \"\"\"Balanced training setup like PointNet++\"\"\"\n", "    pile_count = np.sum(train_labels)\n", "    total_count = len(train_labels)\n", "    pile_ratio = pile_count / total_count\n", "\n", "    # Light class weighting like PointNet++\n", "    if pile_ratio > 0.6:\n", "        pos_weight = 1.2\n", "        neg_weight = 0.8\n", "        class_weights = torch.FloatTensor([neg_weight, pos_weight]).to(device)\n", "        criterion = nn.CrossEntropyLoss(weight=class_weights)\n", "    else:\n", "        criterion = nn.CrossEntropyLoss()\n", "\n", "    optimizer = optim.<PERSON>(\n", "        model.parameters(),\n", "        lr=config['learning_rate'],\n", "        weight_decay=config['weight_decay']\n", "    )\n", "\n", "    scheduler = optim.lr_scheduler.StepLR(optimizer, step_size=20, gamma=0.5)\n", "    return criterion, optimizer, scheduler"]}, {"cell_type": "code", "execution_count": 23, "metadata": {"id": "rbQBTUlOFrrg"}, "outputs": [], "source": ["def run_training(model,criterion,optimizer,scheduler):\n", "    # Training loop\n", "    train_losses = []\n", "    val_losses = []\n", "    train_accs = []\n", "    val_accs = []\n", "    best_val_acc = 0\n", "    patience_counter = 0\n", "\n", "    for epoch in range(config['num_epochs']):\n", "        train_loss, train_acc = train_epoch(model, train_loader, criterion, optimizer, device)\n", "        val_loss, val_acc = validate_epoch(model, val_loader, criterion, device)\n", "\n", "        scheduler.step()\n", "\n", "        train_losses.append(train_loss)\n", "        val_losses.append(val_loss)\n", "        train_accs.append(train_acc)\n", "        val_accs.append(val_acc)\n", "\n", "        if (epoch + 1) % 10 == 0:\n", "            print(f\"Epoch {epoch+1}: Train Acc={train_acc:.4f}, Val Acc={val_acc:.4f}\")\n", "\n", "        if val_acc > best_val_acc:\n", "            best_val_acc = val_acc\n", "            patience_counter = 0\n", "            torch.save(model.state_dict(), 'best_dgcnn_fixed.pth')\n", "        else:\n", "            patience_counter += 1\n", "            if patience_counter >= config['patience']:\n", "                print(f\"Early stopping at epoch {epoch+1}\")\n", "                break\n", "\n", "    # Test evaluation\n", "    model.load_state_dict(torch.load('best_dgcnn_fixed.pth'))\n", "    model.eval()\n", "    all_preds = []\n", "    all_targets = []\n", "\n", "    with torch.no_grad():\n", "        for data, target in test_loader:\n", "            data, target = data.to(device), target.to(device)\n", "            output = model(data)\n", "            all_preds.extend(output.argmax(dim=1).cpu().numpy())\n", "            all_targets.extend(target.cpu().numpy())\n", "\n", "    # Calculate metrics\n", "    test_accuracy = accuracy_score(all_targets, all_preds)\n", "    test_f1 = f1_score(all_targets, all_preds)\n", "    test_precision = precision_score(all_targets, all_preds)\n", "    test_recall = recall_score(all_targets, all_preds)\n", "\n", "    print(f\"\\nFinal Results:\")\n", "    print(f\"Test Accuracy: {test_accuracy:.4f}\")\n", "    print(f\"Test F1-Score: {test_f1:.4f}\")\n", "    print(f\"Test Precision: {test_precision:.4f}\")\n", "    print(f\"Test Recall: {test_recall:.4f}\")\n", "\n", "    return {\n", "        'model': model,\n", "        'accuracy': test_accuracy,\n", "        'f1': test_f1,\n", "        'precision': test_precision,\n", "        'recall': test_recall,\n", "        'history': {\n", "            'train_losses': train_losses,\n", "            'val_losses': val_losses,\n", "            'train_accs': train_accs,\n", "            'val_accs': val_accs\n", "        }\n", "    }\n"]}, {"cell_type": "code", "execution_count": 24, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "_8iO6BwwFxiQ", "outputId": "5070728b-d6d5-4f6e-c590-5191e068ace8"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Epoch 10: Train Acc=0.6954, Val Acc=0.6866\n", "Epoch 20: Train Acc=0.6822, Val Acc=0.6866\n", "Epoch 30: Train Acc=0.7529, Val Acc=0.7943\n", "Epoch 40: Train Acc=0.7537, Val Acc=0.7201\n", "Epoch 50: Train Acc=0.7552, Val Acc=0.7799\n", "Epoch 60: Train Acc=0.7615, Val Acc=0.7632\n", "Epoch 70: Train Acc=0.7739, Val Acc=0.7919\n", "Epoch 80: Train Acc=0.7786, Val Acc=0.7799\n", "Epoch 90: Train Acc=0.7778, Val Acc=0.7608\n", "Epoch 100: Train Acc=0.7661, Val Acc=0.7871\n", "\n", "Final Results:\n", "Test Accuracy: 0.8349\n", "Test F1-Score: 0.8885\n", "Test Precision: 0.8283\n", "Test Recall: 0.9582\n"]}], "source": ["# Initialize model and training\n", "model = DGCNN(num_classes=2, in_channels=20, k=config['k'], dropout=config['dropout']).to(device)\n", "criterion, optimizer, scheduler = setup_training(model, train_labels)\n", "results = run_training(model, criterion, optimizer, scheduler)"]}, {"cell_type": "markdown", "metadata": {"id": "Tmqv-CrCjb13"}, "source": ["## Training History Analysis"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "HyH3-NUgjb13"}, "outputs": [], "source": ["model_name = \"DGCNN\"\n", "\n", "# Extract training metrics\n", "train_losses = results['train_loss']\n", "val_losses = results['val_loss']\n", "train_accs = results['train_acc']\n", "val_accs = results['val_acc']\n", "best_val_acc = max(val_accs)\n", "epochs = range(1, len(train_losses) + 1)\n", "\n", "# Plot training history\n", "fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(14, 5))\n", "\n", "# Loss\n", "ax1.plot(epochs, train_losses, label='Train Loss', linewidth=2)\n", "ax1.plot(epochs, val_losses, label='Val Loss', linewidth=2)\n", "ax1.set(title=f'{model_name} Loss over Epochs', xlabel='Epoch', ylabel='Loss')\n", "ax1.legend()\n", "ax1.grid(True, alpha=0.3)\n", "\n", "# Accuracy\n", "ax2.plot(epochs, train_accs, label='Train Acc', linewidth=2)\n", "ax2.plot(epochs, val_accs, label='Val Acc', linewidth=2)\n", "ax2.set(title=f'{model_name} Accuracy over Epochs', xlabel='Epoch', ylabel='Accuracy')\n", "ax2.legend()\n", "ax2.grid(True, alpha=0.3)\n", "\n", "plt.tight_layout()\n", "plt.show()\n", "\n", "# Print summary\n", "print(f\"\\n=== {model_name} TRAINING SUMMARY ===\")\n", "print(f\"Epochs: {len(train_losses)}\")\n", "print(f\"Best Val Acc: {best_val_acc:.4f}\")\n", "print(f\"Final Train Loss: {train_losses[-1]:.4f}\")\n", "print(f\"Final Val Loss: {val_losses[-1]:.4f}\")\n", "print(f\"Final Train Acc: {train_accs[-1]:.4f}\")\n", "print(f\"Final Val Acc: {val_accs[-1]:.4f}\")\n"]}, {"cell_type": "markdown", "metadata": {"id": "AYmrWX4wIfDf"}, "source": ["## Evaluation"]}, {"cell_type": "code", "execution_count": 27, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "AYPguGNPIcT0", "outputId": "ef6e162d-0ce5-4945-a316-37f0efb2168a"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Best model loaded\n", "\n", "=== DGCNN TEST RESULTS ===\n", "Accuracy: 0.8349\n", "Precision: 0.8283\n", "Recall: 0.9582\n", "F1-Score: 0.8885\n"]}], "source": ["# Load best model if saved\n", "load_best_model = True  # if you want to evaluate best checkpoint\n", "\n", "if load_best_model:\n", "    model.load_state_dict(torch.load('best_dgcnn_fixed.pth'))\n", "    print(\"Best model loaded\")\n", "\n", "# Evaluate on test set\n", "model.eval()\n", "all_preds, all_targets = [], []\n", "\n", "with torch.no_grad():\n", "    for x, y in test_loader:\n", "        x, y = x.to(device), y.to(device)\n", "        preds = model(x).argmax(dim=1)\n", "        all_preds += preds.cpu().tolist()\n", "        all_targets += y.cpu().tolist()\n", "\n", "# Metrics\n", "print(\"\\n=== DGCNN TEST RESULTS ===\")\n", "for name, score in zip(\n", "    [\"Accuracy\", \"Precision\", \"Recall\", \"F1-Score\"],\n", "    [accuracy_score(all_targets, all_preds),\n", "     precision_score(all_targets, all_preds),\n", "     recall_score(all_targets, all_preds),\n", "     f1_score(all_targets, all_preds)]\n", "):\n", "    print(f\"{name}: {score:.4f}\")"]}, {"cell_type": "code", "execution_count": 28, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 755}, "id": "TDUTBsSrZ--R", "outputId": "4c2cbc68-33aa-4ba9-ee31-77203d4a6d8f"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["=== PREDICTION ANALYSIS ===\n", "Actual pile:     287 (68.7%)\n", "Predicted pile:  332 (79.4%)\n", "Class balance OK\n", "\n", "=== PER-CLASS PERFORMANCE ===\n", "              precision    recall  f1-score   support\n", "\n", "    Non-pile       0.86      0.56      0.68       131\n", "        <PERSON><PERSON>       0.83      0.96      0.89       287\n", "\n", "    accuracy                           0.83       418\n", "   macro avg       0.84      0.76      0.79       418\n", "weighted avg       0.84      0.83      0.82       418\n", "\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 2 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["from collections import Counter\n", "from sklearn.metrics import classification_report, confusion_matrix\n", "import seaborn as sns\n", "import matplotlib.pyplot as plt\n", "\n", "# Basic prediction stats\n", "pred_dist, target_dist = Counter(all_preds), Counter(all_targets)\n", "pile_ratio = lambda c, total: f\"{c} ({c/total*100:.1f}%)\"\n", "\n", "print(\"=== PREDICTION ANALYSIS ===\")\n", "print(f\"Actual pile:     {pile_ratio(target_dist[1], len(all_targets))}\")\n", "print(f\"Predicted pile:  {pile_ratio(pred_dist[1], len(all_preds))}\")\n", "print(\"Bias detected\" if pred_dist[1]/len(all_preds) > 0.95 else \"Class balance OK\")\n", "\n", "# Classification report\n", "print(\"\\n=== PER-CLASS PERFORMANCE ===\")\n", "print(classification_report(all_targets, all_preds, target_names=['Non-pile', 'Pile']))\n", "\n", "# Confusion matrix plot\n", "cm = confusion_matrix(all_targets, all_preds)\n", "sns.heatmap(cm, annot=True, fmt='d', cmap='Blues',\n", "            xticklabels=['Non-pile', 'Pile'],\n", "            yticklabels=['Non-pile', 'Pile'])\n", "plt.title('Confusion Matrix')\n", "plt.ylabel('True')\n", "plt.xlabel('Predicted')\n", "plt.tight_layout()\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {"id": "-zrgJpCWypKr"}, "source": ["## Spatial Analysis and Predictions\n", "\n", "Analyze model predictions in spatial context using the same approach as PointNet++."]}, {"cell_type": "code", "execution_count": 29, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "HXrB-x4BypKr", "outputId": "89a63106-2479-428b-ce4a-7dd33b2a5a4c"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Using data paths:\n", "  IFC: /content/drive/MyDrive/pointnet_pile_detection/GRE.EEC.S.00.IT.P.14353.00.265_enhanced_metadata.csv\n", "  KML: /content/drive/MyDrive/pointnet_pile_detection/pile.kml\n", "  Test data: /content/drive/MyDrive/pointnet_pile_detection/pointnet_data/test_pointnet.pkl\n", "  Pile dataset: /content/drive/MyDrive/pointnet_pile_detection/harmonized_pile_dataset_final.csv\n"]}], "source": ["# Set up file paths for spatial analysis (same as PointNet++)\n", "import geopandas as gpd\n", "import pickle\n", "import numpy as np\n", "import pandas as pd\n", "import matplotlib.pyplot as plt\n", "from pathlib import Path\n", "\n", "GDRIVE_BASE = \"/content/drive/MyDrive\"\n", "PROJECT_FOLDER = \"pointnet_pile_detection\"\n", "\n", "project_path = Path(GDRIVE_BASE) / PROJECT_FOLDER\n", "data_path = project_path / \"pointnet_data\"\n", "\n", "ifc_path = project_path / \"GRE.EEC.S.00.IT.P.14353.00.265_enhanced_metadata.csv\"\n", "kml_path = project_path / \"pile.kml\"\n", "test_pkl_path = data_path / \"test_pointnet.pkl\"\n", "harmonized_pile_dataset = project_path / \"harmonized_pile_dataset_final.csv\"\n", "\n", "print(f\"Using data paths:\")\n", "print(f\"  IFC: {ifc_path}\")\n", "print(f\"  KML: {kml_path}\")\n", "print(f\"  Test data: {test_pkl_path}\")\n", "print(f\"  Pile dataset: {harmonized_pile_dataset}\")"]}, {"cell_type": "code", "execution_count": 31, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 914}, "id": "Zjz3e1RZypKr", "outputId": "918e15c2-0468-4407-99fa-728737998c09"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Reconstructed 287 coordinates from pile IDs\n", "Spatial subset accuracy: 0.958 (287 samples)\n", "Full test set accuracy: 0.835 (418 samples)\n", "Coordinate range: X[435305, 436682], Y[5010922, 5012461]\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 1000x800 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "Spatial analysis complete. Results saved to Google Drive.\n"]}], "source": ["def create_dgcnn_spatial_plot(all_preds, all_targets, test_pkl_path, results_path, metrics):\n", "    \"\"\"Create DGCNN spatial plot using the same method as PointNet++.\"\"\"\n", "\n", "    # Load reference data\n", "    ifc_df = pd.read_csv(ifc_path)\n", "    ifc_coords = ifc_df[['X', 'Y']].values\n", "\n", "    gdf_kml = gpd.read_file(kml_path, driver='KML')\n", "    gdf_kml = gdf_kml.set_crs(epsg=4326).to_crs(epsg=32632)\n", "    gdf_kml['geometry'] = gdf_kml.geometry.centroid\n", "    kml_coords = np.stack([gdf_kml.geometry.x.values, gdf_kml.geometry.y.values], axis=1)\n", "\n", "    # Load the original pile dataset\n", "    pile_df = pd.read_csv(harmonized_pile_dataset)\n", "\n", "    # Load test metadata\n", "    with open(test_pkl_path, 'rb') as f:\n", "        test_data = pickle.load(f)\n", "    test_metadata = test_data.get('metadata', [])\n", "\n", "    # Reconstruct coordinates using pile IDs\n", "    pred_coords = []\n", "    valid_indices = []\n", "\n", "    for i, meta in enumerate(test_metadata[:len(all_preds)]):\n", "        pile_id = meta.get('pile_id') if isinstance(meta, dict) else None\n", "\n", "        if pile_id and pile_id in pile_df['pile_id'].values:\n", "            pile_row = pile_df[pile_df['pile_id'] == pile_id].iloc[0]\n", "            pred_coords.append([pile_row['x'], pile_row['y']])\n", "            valid_indices.append(i)\n", "\n", "    if len(pred_coords) == 0:\n", "        print(\"ERROR: No pile IDs found in metadata!\")\n", "        return\n", "\n", "    pred_coords = np.array(pred_coords)\n", "\n", "    # Filter predictions to match valid coordinates\n", "    all_preds_filtered = [all_preds[i] for i in valid_indices]\n", "    all_targets_filtered = [all_targets[i] for i in valid_indices]\n", "\n", "    # Calculate accuracy for SPATIAL subset (for display purposes)\n", "    correct_mask = (np.array(all_targets_filtered) == np.array(all_preds_filtered))\n", "    error_mask = ~correct_mask\n", "    spatial_accuracy = np.mean(correct_mask)\n", "\n", "    # Use the TRUE accuracy from full test set for title\n", "    true_accuracy = metrics['accuracy']\n", "\n", "    print(f\"Reconstructed {len(pred_coords)} coordinates from pile IDs\")\n", "    print(f\"Spatial subset accuracy: {spatial_accuracy:.3f} ({len(pred_coords)} samples)\")\n", "    print(f\"Full test set accuracy: {true_accuracy:.3f} ({len(all_preds)} samples)\")\n", "    print(f\"Coordinate range: X[{pred_coords[:, 0].min():.0f}, {pred_coords[:, 0].max():.0f}], Y[{pred_coords[:, 1].min():.0f}, {pred_coords[:, 1].max():.0f}]\")\n", "\n", "    # Create plot exactly like PointNet++\n", "    fig, ax = plt.subplots(1, 1, figsize=(10, 8))\n", "\n", "    # Plot reference data\n", "    ax.scatter(ifc_coords[::50, 0], ifc_coords[::50, 1],\n", "              s=0.5, alpha=0.3, color='lightgray', label='IFC (subsampled)')\n", "    ax.scatter(kml_coords[:, 0], kml_coords[:, 1],\n", "              s=8, alpha=0.7, color='green', marker='s', label='KML Ground Truth')\n", "\n", "    # Plot predictions\n", "    if np.sum(correct_mask) > 0:\n", "        ax.scatter(pred_coords[correct_mask, 0], pred_coords[correct_mask, 1],\n", "                  c='blue', s=30, alpha=0.8, marker='o',\n", "                  label=f'Correct ({np.sum(correct_mask)})')\n", "\n", "    if np.sum(error_mask) > 0:\n", "        ax.scatter(pred_coords[error_mask, 0], pred_coords[error_mask, 1],\n", "                  c='red', s=50, alpha=0.9, marker='x', linewidth=2,\n", "                  label=f'Errors ({np.sum(error_mask)})')\n", "\n", "    # Use TRUE accuracy in title, but show spatial stats in legend\n", "    ax.set_title(f'DGCNN\\nAccuracy: {true_accuracy:.3f}')\n", "    ax.set_xlabel('X (UTM)')\n", "    ax.set_ylabel('Y (UTM)')\n", "    ax.legend(fontsize=8)\n", "    ax.grid(True, alpha=0.3)\n", "    ax.set_aspect('equal', adjustable='box')\n", "\n", "    # Add text box with full stats\n", "    stats_text = f'Full Test Set:\\n{len(all_preds)} samples\\n{true_accuracy:.1%} accuracy\\n\\nSpatial Subset:\\n{len(pred_coords)} samples\\n{spatial_accuracy:.1%} accuracy'\n", "    ax.text(0.02, 0.98, stats_text, transform=ax.transAxes, fontsize=8,\n", "            verticalalignment='top', bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.8))\n", "\n", "    plt.tight_layout()\n", "    plt.savefig('/content/drive/MyDrive/dgcnn_results/dgcnn_spatial_analysis.png', dpi=150, bbox_inches='tight')\n", "    plt.show()\n", "\n", "    return {\n", "        'spatial_accuracy': spatial_accuracy,\n", "        'spatial_samples': len(pred_coords),\n", "        'true_accuracy': true_accuracy,\n", "        'total_samples': len(all_preds)\n", "    }\n", "\n", "test_accuracy = accuracy_score(all_targets, all_preds)\n", "test_precision = precision_score(all_targets, all_preds, average='weighted')\n", "test_recall = recall_score(all_targets, all_preds, average='weighted')\n", "test_f1 = f1_score(all_targets, all_preds, average='weighted')\n", "\n", "# Create spatial analysis\n", "dgcnn_metrics = {\n", "    'accuracy': test_accuracy,\n", "    'precision': test_precision,\n", "    'recall': test_recall,\n", "    'f1_score': test_f1\n", "}\n", "\n", "spatial_results = create_dgcnn_spatial_plot(all_preds, all_targets, test_pkl_path,\n", "                                           Path('/content/drive/MyDrive/dgcnn_results'),\n", "                                           dgcnn_metrics)\n", "\n", "print(f\"\\nSpatial analysis complete. Results saved to Google Drive.\")"]}], "metadata": {"accelerator": "GPU", "colab": {"gpuType": "T4", "provenance": []}, "kernelspec": {"display_name": "Python 3", "name": "python3"}, "language_info": {"name": "python", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 0}