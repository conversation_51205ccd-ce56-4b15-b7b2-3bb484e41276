{"cells": [{"cell_type": "markdown", "id": "header", "metadata": {}, "source": ["# Pile Geometric Analysis - RES Site Only\n", "\n", "**Optimized single-site analysis for faster execution**\n", "\n", "**Input**: Classical ML detection results from RES site\n", "**Output**: Pile spacing, height, verticality measurements and QGIS visualizations\n", "\n", "**Measurements**:\n", "- Pile-to-pile spacing analysis (tracker-aware)\n", "- Pile height above ground level (donut approach)\n", "- Pile verticality/lean assessment (height-filtered)\n", "\n", "**Performance Optimizations**:\n", "- Single site processing\n", "- Spatial indexing for fast point extraction\n", "- Limited analysis scope (max 300 piles)\n", "- Efficient distance calculations\n", "\n", "**Author**: <PERSON><PERSON><PERSON>  \n", "**Date**: August 2025"]}, {"cell_type": "code", "execution_count": 14, "id": "parameters", "metadata": {"tags": ["parameters"]}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["RES Site geometric analysis configured\n", "Max piles to analyze: 300\n", "Point cloud sampling: 30%\n", "Output directory: output_runs/geometric_analysis_res\n"]}], "source": ["# RES Site Configuration\n", "SITE_NAME = 'nortan_res'\n", "SITE_DISPLAY_NAME = 'RES Site'\n", "ML_RESULTS_PATTERN = '../modeling/pile_detection/02_ml_based/classic/output_runs/clean_validation/nortan_res_pile_detection_results_*.csv'\n", "POINT_CLOUD_PATH = '../../data/raw/nortan_res/pointcloud/Block_11_2m.las'\n", "SITE_CRS = 'EPSG:32614'  # UTM Zone 14N\n", "EXPECTED_PILES = 368\n", "\n", "OUTPUT_DIR = \"output_runs/geometric_analysis_res\"\n", "\n", "# Performance parameters - OPTIMIZED\n", "MAX_ANALYSIS_PILES = 300  # Limit for faster processing\n", "CONFIDENCE_THRESHOLD = 0.6  # Higher threshold for quality\n", "SAMPLE_POINT_CLOUD = True  # Subsample point cloud for speed\n", "POINT_CLOUD_SAMPLE_RATIO = 0.3  # Use 30% of points\n", "\n", "# Analysis parameters\n", "PILE_EXTRACTION_RADIUS = 2.0  # meters\n", "MIN_PILE_POINTS = 8  # Reduced minimum\n", "HEIGHT_ANALYSIS_RADIUS = 1.5  # meters\n", "VERTICALITY_ANALYSIS_RADIUS = 0.5  # meters - REDUCED for more precise pile measurement\n", "\n", "# Donut approach parameters\n", "DONUT_INNER_RADIUS = 0.8  # meters\n", "DONUT_OUTER_RADIUS = 2.5  # meters\n", "\n", "# Spacing analysis - optimized\n", "MAX_NEIGHBOR_DISTANCE = 12.0  # Reduced for speed\n", "WITHIN_TRACKER_RANGE = [2.0, 6.0]  # meters\n", "BETWEEN_TRACKER_RANGE = [6.0, 12.0]  # meters\n", "\n", "print(f\"RES Site geometric analysis configured\")\n", "print(f\"Max piles to analyze: {MAX_ANALYSIS_PILES}\")\n", "print(f\"Point cloud sampling: {POINT_CLOUD_SAMPLE_RATIO*100:.0f}%\")\n", "print(f\"Output directory: {OUTPUT_DIR}\")"]}, {"cell_type": "code", "execution_count": 15, "id": "imports", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Libraries imported successfully\n"]}], "source": ["import numpy as np\n", "import pandas as pd\n", "import laspy\n", "import glob\n", "from pathlib import Path\n", "from datetime import datetime\n", "import json\n", "import warnings\n", "warnings.filterwarnings('ignore')\n", "\n", "# Spatial analysis\n", "from scipy.spatial import cKDTree\n", "from scipy.stats import describe\n", "from sklearn.decomposition import PCA\n", "from sklearn.cluster import DBSCAN\n", "\n", "# Coordinate transformation\n", "import geopandas as gpd\n", "from shapely.geometry import Point\n", "\n", "print(\"Libraries imported successfully\")"]}, {"cell_type": "code", "execution_count": 16, "id": "optimized_functions", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Optimized functions defined (with borrowed techniques)\n"]}], "source": ["def load_and_sample_point_cloud(las_path, sample_ratio=0.3):\n", "    \"\"\"Load and optionally sample point cloud for faster processing\"\"\"\n", "    print(f\"Loading point cloud: {Path(las_path).name}\")\n", "    las_file = laspy.read(las_path)\n", "    \n", "    # Extract coordinates\n", "    points = np.vstack([las_file.x, las_file.y, las_file.z]).T\n", "    print(f\"Original points: {len(points):,}\")\n", "    \n", "    if sample_ratio < 1.0:\n", "        # Random sampling for speed\n", "        n_sample = int(len(points) * sample_ratio)\n", "        indices = np.random.choice(len(points), n_sample, replace=False)\n", "        points = points[indices]\n", "        print(f\"Sampled to: {len(points):,} points ({sample_ratio*100:.0f}%)\")\n", "    \n", "    return points\n", "\n", "def build_spatial_index(points):\n", "    \"\"\"Build KD-tree for fast spatial queries\"\"\"\n", "    print(\"Building spatial index...\")\n", "    tree = cKDTree(points[:, :2])  # X, Y only for 2D queries\n", "    return tree\n", "\n", "def extract_pile_region_fast(points, spatial_index, pile_center, radius):\n", "    \"\"\"Fast pile region extraction using spatial index\"\"\"\n", "    # Query spatial index for nearby points\n", "    indices = spatial_index.query_ball_point(pile_center, radius)\n", "    \n", "    if len(indices) == 0:\n", "        return np.array([]).reshape(0, 3)\n", "    \n", "    return points[indices]\n", "\n", "def calculate_pile_height_donut_fast(points, spatial_index, pile_center, inner_radius=0.8, outer_radius=2.5):\n", "    \"\"\"Fast donut approach using spatial index\"\"\"\n", "    # Get all points in outer radius\n", "    outer_indices = spatial_index.query_ball_point(pile_center, outer_radius)\n", "    if len(outer_indices) < 5:\n", "        return np.nan\n", "    \n", "    region_points = points[outer_indices]\n", "    \n", "    # Calculate distances for inner/outer separation\n", "    distances = np.sqrt((region_points[:, 0] - pile_center[0])**2 + \n", "                       (region_points[:, 1] - pile_center[1])**2)\n", "    \n", "    # Inner donut: pile points\n", "    inner_mask = distances <= inner_radius\n", "    inner_points = region_points[inner_mask]\n", "    \n", "    # Outer ring: ground points\n", "    outer_mask = (distances > inner_radius) & (distances <= outer_radius)\n", "    outer_points = region_points[outer_mask]\n", "    \n", "    if len(inner_points) < 3 or len(outer_points) < 3:\n", "        return np.nan\n", "    \n", "    # Pile top and ground level\n", "    pile_top = np.max(inner_points[:, 2])\n", "    ground_level = np.median(outer_points[:, 2])\n", "    \n", "    height = pile_top - ground_level\n", "    return max(0, height)\n", "\n", "def calculate_pile_verticality_fast(points, spatial_index, pile_center, radius=1.2):\n", "    \"\"\"Fast verticality calculation with improved height filtering and robust PCA\"\"\"\n", "    # Get points in region\n", "    indices = spatial_index.query_ball_point(pile_center, radius)\n", "    if len(indices) < 8:\n", "        return np.nan\n", "    \n", "    pile_region = points[indices]\n", "    \n", "    # Improved height filtering for pile structure\n", "    z_values = pile_region[:, 2]\n", "    z_min = np.min(z_values)\n", "    z_max = np.max(z_values)\n", "    z_range = z_max - z_min\n", "    \n", "    # Only proceed if there's significant height variation (indicating a pile)\n", "    if z_range < 0.5:  # Less than 50cm height variation\n", "        return np.nan\n", "    \n", "    # Keep points in upper 70% of height range (pile structure)\n", "    height_threshold = z_min + 0.3 * z_range\n", "    pile_structure = pile_region[pile_region[:, 2] >= height_threshold]\n", "    \n", "    if len(pile_structure) < 8:\n", "        # Fallback: use top 50% of points by height\n", "        sorted_indices = np.argsort(z_values)[::-1]  # Sort by height, descending\n", "        top_half = sorted_indices[:len(sorted_indices)//2]\n", "        pile_structure = pile_region[top_half]\n", "    \n", "    if len(pile_structure) < 5:\n", "        return np.nan\n", "    \n", "    # Center the points for better PCA\n", "    centered_points = pile_structure - np.mean(pile_structure, axis=0)\n", "    \n", "    # PCA on centered pile structure\n", "    pca = PCA(n_components=3)\n", "    pca.fit(centered_points)\n", "    \n", "    # The component with highest variance in Z should be the pile axis\n", "    components = pca.components_\n", "    z_variances = np.abs(components[:, 2])  # Z-component of each principal axis\n", "    pile_axis_idx = np.argmax(z_variances)  # Choose axis with most Z variation\n", "    pile_axis = components[pile_axis_idx]\n", "    \n", "    # Ensure pile axis points upward\n", "    if pile_axis[2] < 0:\n", "        pile_axis = -pile_axis\n", "    \n", "    # Calculate angle with vertical (0, 0, 1)\n", "    vertical = np.array([0, 0, 1])\n", "    cos_angle = np.dot(pile_axis, vertical)\n", "    cos_angle = np.clip(cos_angle, 0, 1)  # Handle numerical errors\n", "    \n", "    angle_rad = np.arccos(cos_angle)\n", "    angle_deg = np.degrees(angle_rad)\n", "    \n", "    # Sanity check: if angle > 45°, something is wrong\n", "    if angle_deg > 45.0:\n", "        return np.nan\n", "    \n", "    return angle_deg\n", "\n", "def calculate_pile_verticality_debug(points, spatial_index, pile_center, radius=1.2):\n", "    \"\"\"Debug version to understand verticality calculation issues\"\"\"\n", "    indices = spatial_index.query_ball_point(pile_center, radius)\n", "    if len(indices) < 8:\n", "        return np.nan, \"Insufficient points\"\n", "    \n", "    pile_region = points[indices]\n", "    z_values = pile_region[:, 2]\n", "    z_range = np.max(z_values) - np.min(z_values)\n", "    \n", "    if z_range < 0.5:\n", "        return np.nan, f\"Low height variation: {z_range:.2f}m\"\n", "    \n", "    # Use simple linear regression as alternative\n", "    # Fit line through X,Y,Z points\n", "    if len(pile_region) >= 5:\n", "        # Calculate centroid\n", "        centroid = np.mean(pile_region, axis=0)\n", "        \n", "        # Vector from bottom to top of pile\n", "        bottom_points = pile_region[pile_region[:, 2] <= np.percentile(z_values, 25)]\n", "        top_points = pile_region[pile_region[:, 2] >= np.percentile(z_values, 75)]\n", "        \n", "        if len(bottom_points) > 0 and len(top_points) > 0:\n", "            bottom_center = np.mean(bottom_points, axis=0)\n", "            top_center = np.mean(top_points, axis=0)\n", "            \n", "            pile_vector = top_center - bottom_center\n", "            pile_vector = pile_vector / np.linalg.norm(pile_vector)  # Normalize\n", "            \n", "            # Angle with vertical\n", "            vertical = np.array([0, 0, 1])\n", "            cos_angle = np.abs(np.dot(pile_vector, vertical))\n", "            cos_angle = np.clip(cos_angle, 0, 1)\n", "            \n", "            angle_rad = np.arccos(cos_angle)\n", "            angle_deg = np.degrees(angle_rad)\n", "            \n", "            return angle_deg, f\"Linear method: {len(pile_region)} points, {z_range:.2f}m range\"\n", "    \n", "    return np.nan, \"Linear method failed\"\n", "\n", "def calculate_pile_verticality_simple(points, spatial_index, pile_center, radius=1.0):\n", "    \"\"\"Simple verticality calculation using top-bottom displacement\"\"\"\n", "    indices = spatial_index.query_ball_point(pile_center, radius)\n", "    if len(indices) < 5:\n", "        return np.nan\n", "    \n", "    pile_region = points[indices]\n", "    z_values = pile_region[:, 2]\n", "    \n", "    # Need significant height variation\n", "    z_range = np.max(z_values) - np.min(z_values)\n", "    if z_range < 0.5:\n", "        return np.nan\n", "    \n", "    # Get bottom 25% and top 25% of points by height\n", "    z_25 = np.percentile(z_values, 25)\n", "    z_75 = np.percentile(z_values, 75)\n", "    \n", "    bottom_points = pile_region[pile_region[:, 2] <= z_25]\n", "    top_points = pile_region[pile_region[:, 2] >= z_75]\n", "    \n", "    if len(bottom_points) < 2 or len(top_points) < 2:\n", "        return np.nan\n", "    \n", "    # Calculate centroids\n", "    bottom_center = np.mean(bottom_points[:, :2], axis=0)  # X,Y only\n", "    top_center = np.mean(top_points[:, :2], axis=0)  # X,Y only\n", "    \n", "    # Horizontal displacement\n", "    horizontal_displacement = np.linalg.norm(top_center - bottom_center)\n", "    \n", "    # Vertical height\n", "    vertical_height = np.mean(top_points[:, 2]) - np.mean(bottom_points[:, 2])\n", "    \n", "    if vertical_height <= 0:\n", "        return np.nan\n", "    \n", "    # Lean angle from vertical\n", "    lean_angle = np.degrees(np.arctan(horizontal_displacement / vertical_height))\n", "    \n", "    # DEBUGGING: Check for unrealistic values\n", "    if lean_angle > 30.0:  # Sanity check\n", "        # Try smaller radius - might be picking up too much noise\n", "        smaller_indices = spatial_index.query_ball_point(pile_center, radius * 0.5)\n", "        if len(smaller_indices) >= 5:\n", "            smaller_region = points[smaller_indices]\n", "            smaller_z = smaller_region[:, 2]\n", "            if np.max(smaller_z) - np.min(smaller_z) >= 0.3:\n", "                # Recalculate with smaller region\n", "                z_25_small = np.percentile(smaller_z, 25)\n", "                z_75_small = np.percentile(smaller_z, 75)\n", "                \n", "                bottom_small = smaller_region[smaller_region[:, 2] <= z_25_small]\n", "                top_small = smaller_region[smaller_region[:, 2] >= z_75_small]\n", "                \n", "                if len(bottom_small) >= 2 and len(top_small) >= 2:\n", "                    bottom_center_small = np.mean(bottom_small[:, :2], axis=0)\n", "                    top_center_small = np.mean(top_small[:, :2], axis=0)\n", "                    \n", "                    horizontal_small = np.linalg.norm(top_center_small - bottom_center_small)\n", "                    vertical_small = np.mean(top_small[:, 2]) - np.mean(bottom_small[:, 2])\n", "                    \n", "                    if vertical_small > 0:\n", "                        lean_angle_small = np.degrees(np.arctan(horizontal_small / vertical_small))\n", "                        if lean_angle_small < lean_angle:  # Use smaller value if better\n", "                            lean_angle = lean_angle_small\n", "    \n", "    return lean_angle\n", "\n", "# Removed complex terrain functions - too slow and complicated\n", "\n", "def calculate_pile_verticality_donut(points, spatial_index, pile_center, inner_radius=0.3, outer_radius=1.5):\n", "    \"\"\"Calculate pile verticality using donut approach for better ground reference\"\"\"\n", "    # Get all points in outer radius\n", "    outer_indices = spatial_index.query_ball_point(pile_center, outer_radius)\n", "    if len(outer_indices) < 10:\n", "        return np.nan\n", "    \n", "    region_points = points[outer_indices]\n", "    \n", "    # Calculate distances for inner/outer separation\n", "    distances = np.sqrt((region_points[:, 0] - pile_center[0])**2 + \n", "                       (region_points[:, 1] - pile_center[1])**2)\n", "    \n", "    # Inner donut: pile structure\n", "    inner_mask = distances <= inner_radius\n", "    pile_points = region_points[inner_mask]\n", "    \n", "    # Outer ring: ground reference\n", "    outer_mask = (distances > inner_radius) & (distances <= outer_radius)\n", "    ground_points = region_points[outer_mask]\n", "    \n", "    if len(pile_points) < 5 or len(ground_points) < 5:\n", "        return np.nan\n", "    \n", "    # Separate pile points by height\n", "    pile_z = pile_points[:, 2]\n", "    pile_bottom = pile_points[pile_z <= np.percentile(pile_z, 25)]\n", "    pile_top = pile_points[pile_z >= np.percentile(pile_z, 75)]\n", "    \n", "    if len(pile_bottom) < 2 or len(pile_top) < 2:\n", "        return np.nan\n", "    \n", "    # Calculate pile axis using bottom and top\n", "    bottom_center = np.mean(pile_bottom[:, :2], axis=0)\n", "    top_center = np.mean(pile_top[:, :2], axis=0)\n", "    \n", "    # Ground reference level\n", "    ground_level = np.median(ground_points[:, 2])\n", "    pile_bottom_z = np.mean(pile_bottom[:, 2])\n", "    pile_top_z = np.mean(pile_top[:, 2])\n", "    \n", "    # Horizontal displacement and vertical height\n", "    horizontal_displacement = np.linalg.norm(top_center - bottom_center)\n", "    vertical_height = pile_top_z - pile_bottom_z\n", "    \n", "    if vertical_height <= 0:\n", "        return np.nan\n", "    \n", "    # Lean angle\n", "    lean_angle = np.degrees(np.arctan(horizontal_displacement / vertical_height))\n", "    \n", "    return lean_angle\n", "\n", "def calculate_smart_distances_fast(pile_locations, max_distance=12.0):\n", "    \"\"\"Fast distance calculation with tracker inference\"\"\"\n", "    if len(pile_locations) > MAX_ANALYSIS_PILES:\n", "        print(f\"Sampling {MAX_ANALYSIS_PILES} piles from {len(pile_locations)}\")\n", "        indices = np.random.choice(len(pile_locations), MAX_ANALYSIS_PILES, replace=False)\n", "        pile_subset = pile_locations[indices]\n", "    else:\n", "        pile_subset = pile_locations\n", "    \n", "    # Build tree for distance queries\n", "    tree = cKDTree(pile_subset)\n", "    \n", "    # Find neighbors within max_distance\n", "    neighbor_lists = tree.query_ball_tree(tree, r=max_distance)\n", "    \n", "    # Collect distances\n", "    all_distances = []\n", "    for i, neighbors in enumerate(neighbor_lists):\n", "        pile_i = pile_subset[i]\n", "        for j in neighbors:\n", "            if i < j:  # Avoid duplicates\n", "                pile_j = pile_subset[j]\n", "                distance = np.sqrt(np.sum((pile_i - pile_j)**2))\n", "                if distance > 0:\n", "                    all_distances.append(distance)\n", "    \n", "    return {\n", "        'all_distances': np.array(all_distances),\n", "        'analysis_pile_count': len(pile_subset)\n", "    }\n", "\n", "def calculate_pile_alignment_quality(pile_locations, min_piles_per_row=3):\n", "    \"\"\"Calculate alignment quality for pile rows (borrowed from straightness measurement)\"\"\"\n", "    # Simple linear grouping by Y coordinate\n", "    y_coords = pile_locations[:, 1]\n", "    y_sorted_indices = np.argsort(y_coords)\n", "    \n", "    # Group piles into rows based on Y coordinate similarity\n", "    tracker_groups = []\n", "    current_group = [y_sorted_indices[0]]\n", "    \n", "    for i in range(1, len(y_sorted_indices)):\n", "        idx = y_sorted_indices[i]\n", "        prev_idx = y_sorted_indices[i-1]\n", "        \n", "        # If Y coordinates are close (within 5m), same tracker\n", "        if abs(y_coords[idx] - y_coords[prev_idx]) < 5.0:\n", "            current_group.append(idx)\n", "        else:\n", "            if len(current_group) >= min_piles_per_row:\n", "                tracker_groups.append(current_group)\n", "            current_group = [idx]\n", "    \n", "    if len(current_group) >= min_piles_per_row:\n", "        tracker_groups.append(current_group)\n", "    \n", "    alignment_results = []\n", "    \n", "    for group_idx, pile_indices in enumerate(tracker_groups):\n", "        if len(pile_indices) < min_piles_per_row:\n", "            continue\n", "            \n", "        group_piles = pile_locations[pile_indices]\n", "        \n", "        # Calculate straightness using linear regression\n", "        x_coords = group_piles[:, 0]\n", "        y_coords = group_piles[:, 1]\n", "        \n", "        # Fit line through points\n", "        A = np.vstack([x_coords, np.ones(len(x_coords))]).T\n", "        slope, intercept = np.linalg.lstsq(A, y_coords, rcond=None)[0]\n", "        \n", "        # Calculate deviations from line\n", "        predicted_y = slope * x_coords + intercept\n", "        deviations = np.abs(y_coords - predicted_y)\n", "        \n", "        alignment_results.append({\n", "            'tracker_id': group_idx,\n", "            'pile_count': len(pile_indices),\n", "            'max_deviation': np.max(deviations),\n", "            'mean_deviation': np.mean(deviations),\n", "            'std_deviation': np.std(deviations),\n", "            'slope': slope,\n", "            'r_squared': 1 - np.var(deviations) / np.var(y_coords) if np.var(y_coords) > 0 else 1.0\n", "        })\n", "    \n", "    return alignment_results\n", "\n", "print(\"Optimized functions defined (with borrowed techniques)\")"]}, {"cell_type": "code", "execution_count": 17, "id": "setup", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Output directory: output_runs/geometric_analysis_res\n", "Analysis timestamp: 20250808_073713\n", "\n", "============================================================\n", "RES SITE GEOMETRIC ANALYSIS - OPTIMIZED\n", "============================================================\n"]}], "source": ["# Create output directory\n", "output_dir = Path(OUTPUT_DIR)\n", "output_dir.mkdir(parents=True, exist_ok=True)\n", "\n", "# Timestamp for outputs\n", "timestamp = datetime.now().strftime(\"%Y%m%d_%H%M%S\")\n", "\n", "print(f\"Output directory: {output_dir}\")\n", "print(f\"Analysis timestamp: {timestamp}\")\n", "\n", "print(\"\\n\" + \"=\"*60)\n", "print(f\"RES SITE GEOMETRIC ANALYSIS - OPTIMIZED\")\n", "print(\"=\"*60)"]}, {"cell_type": "code", "execution_count": 18, "id": "load_data", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "📊 LOADING ML DETECTION RESULTS\n", "----------------------------------------\n", "Loading: nortan_res_pile_detection_results_20250807_214148.csv\n", "Total detections: 368\n", "High-confidence detections: 368 (threshold: 0.6)\n", "Pile coordinates extracted: 368\n"]}], "source": ["# Load ML detection results\n", "print(\"\\n📊 LOADING ML DETECTION RESULTS\")\n", "print(\"-\" * 40)\n", "\n", "files = glob.glob(ML_RESULTS_PATTERN)\n", "if not files:\n", "    raise FileNotFoundError(f\"No ML results found: {ML_RESULTS_PATTERN}\")\n", "\n", "latest_file = max(files, key=lambda x: Path(x).stat().st_mtime)\n", "print(f\"Loading: {Path(latest_file).name}\")\n", "\n", "ml_results = pd.read_csv(latest_file)\n", "print(f\"Total detections: {len(ml_results)}\")\n", "\n", "# Filter for high-confidence detections\n", "detected_piles = ml_results[\n", "    (ml_results['predicted_pile'] == 1) & \n", "    (ml_results['confidence'] >= CONFIDENCE_THRESHOLD)\n", "].copy()\n", "\n", "print(f\"High-confidence detections: {len(detected_piles)} (threshold: {CONFIDENCE_THRESHOLD})\")\n", "\n", "if len(detected_piles) == 0:\n", "    raise ValueError(\"No high-confidence detections found\")\n", "\n", "# Extract pile coordinates\n", "pile_locations = detected_piles[['utm_x', 'utm_y']].values\n", "print(f\"Pile coordinates extracted: {len(pile_locations)}\")"]}, {"cell_type": "code", "execution_count": 19, "id": "load_pointcloud", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "🗂️ LOADING POINT CLOUD\n", "----------------------------------------\n", "Loading point cloud: Block_11_2m.las\n", "Original points: 35,565,352\n", "Sampled to: 10,669,605 points (30%)\n", "Building spatial index...\n", "Spatial index built successfully\n", "Ready for analysis with 10,669,605 points\n"]}], "source": ["# Load and sample point cloud\n", "print(\"\\n🗂️ LOADING POINT CLOUD\")\n", "print(\"-\" * 40)\n", "\n", "points = load_and_sample_point_cloud(POINT_CLOUD_PATH, POINT_CLOUD_SAMPLE_RATIO)\n", "\n", "# Build spatial index for fast queries\n", "spatial_index = build_spatial_index(points)\n", "print(\"Spatial index built successfully\")\n", "\n", "print(f\"Ready for analysis with {len(points):,} points\")"]}, {"cell_type": "code", "execution_count": 20, "id": "spacing_analysis", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "📏 PILE SPACING ANALYSIS\n", "----------------------------------------\n", "Sampling 300 piles from 368\n", "<PERSON><PERSON> analyzed: 300 / 368\n", "Distance pairs calculated: 869\n", "\n", "Spacing statistics:\n", "  Mean: 9.54m\n", "  Std: 1.81m\n", "  Range: 6.78 - 11.96m\n", "  Median: 9.23m\n", "\n", "Distance classification:\n", "  Within-tracker (2.0-6.0m): 0 pairs\n", "  Between-tracker (6.0-12.0m): 869 pairs\n", "  Typical between-tracker: 9.54 ± 1.81m\n"]}], "source": ["# Pile spacing analysis\n", "print(\"\\n📏 PILE SPACING ANALYSIS\")\n", "print(\"-\" * 40)\n", "\n", "distance_analysis = calculate_smart_distances_fast(\n", "    pile_locations, \n", "    max_distance=MAX_NEIGHBOR_DISTANCE\n", ")\n", "\n", "all_distances = distance_analysis['all_distances']\n", "analysis_count = distance_analysis['analysis_pile_count']\n", "\n", "print(f\"<PERSON>les analyzed: {analysis_count} / {len(pile_locations)}\")\n", "print(f\"Distance pairs calculated: {len(all_distances):,}\")\n", "\n", "if len(all_distances) > 0:\n", "    spacing_stats = describe(all_distances)\n", "    print(f\"\\nSpacing statistics:\")\n", "    print(f\"  Mean: {spacing_stats.mean:.2f}m\")\n", "    print(f\"  Std: {np.sqrt(spacing_stats.variance):.2f}m\")\n", "    print(f\"  Range: {spacing_stats.minmax[0]:.2f} - {spacing_stats.minmax[1]:.2f}m\")\n", "    print(f\"  Median: {np.median(all_distances):.2f}m\")\n", "    \n", "    # Classify distances\n", "    within_tracker = all_distances[\n", "        (all_distances >= WITHIN_TRACKER_RANGE[0]) & \n", "        (all_distances <= WITHIN_TRACKER_RANGE[1])\n", "    ]\n", "    \n", "    between_tracker = all_distances[\n", "        (all_distances >= BETWEEN_TRACKER_RANGE[0]) & \n", "        (all_distances <= BETWEEN_TRACKER_RANGE[1])\n", "    ]\n", "    \n", "    print(f\"\\nDistance classification:\")\n", "    print(f\"  Within-tracker ({WITHIN_TRACKER_RANGE[0]}-{WITHIN_TRACKER_RANGE[1]}m): {len(within_tracker)} pairs\")\n", "    print(f\"  Between-tracker ({BETWEEN_TRACKER_RANGE[0]}-{BETWEEN_TRACKER_RANGE[1]}m): {len(between_tracker)} pairs\")\n", "    \n", "    if len(within_tracker) > 0:\n", "        print(f\"  Typical within-tracker: {np.mean(within_tracker):.2f} ± {np.std(within_tracker):.2f}m\")\n", "    \n", "    if len(between_tracker) > 0:\n", "        print(f\"  Typical between-tracker: {np.mean(between_tracker):.2f} ± {np.std(between_tracker):.2f}m\")\n", "else:\n", "    print(\"⚠️ No distance pairs calculated\")\n", "    spacing_stats = None"]}, {"cell_type": "code", "execution_count": 21, "id": "height_analysis", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "📐 PILE HEIGHT ANALYSIS (DONUT APPROACH)\n", "----------------------------------------\n", "Analyzing heights for 300 piles...\n", "  Processed 50/300 piles\n", "  Processed 100/300 piles\n", "  Processed 150/300 piles\n", "  Processed 200/300 piles\n", "  Processed 250/300 piles\n", "  Processed 300/300 piles\n", "\n", "Height analysis results:\n", "  Valid measurements: 300 / 300\n", "  Mean height: 1.28m\n", "  Std: 0.27m\n", "  Range: 0.12 - 1.79m\n", "  Median: 1.38m\n", "  Reasonable heights (0.5-8.0m): 293/300\n"]}], "source": ["# Pile height analysis\n", "print(\"\\n📐 PILE HEIGHT ANALYSIS (DONUT APPROACH)\")\n", "print(\"-\" * 40)\n", "\n", "pile_heights = []\n", "valid_height_count = 0\n", "\n", "# Limit analysis for speed\n", "analysis_piles = pile_locations[:MAX_ANALYSIS_PILES] if len(pile_locations) > MAX_ANALYSIS_PILES else pile_locations\n", "print(f\"Analyzing heights for {len(analysis_piles)} piles...\")\n", "\n", "for i, pile_center in enumerate(analysis_piles):\n", "    if (i + 1) % 50 == 0:\n", "        print(f\"  Processed {i+1}/{len(analysis_piles)} piles\")\n", "    \n", "    height = calculate_pile_height_donut_fast(\n", "        points, spatial_index, pile_center, \n", "        inner_radius=DONUT_INNER_RADIUS, \n", "        outer_radius=DONUT_OUTER_RADIUS\n", "    )\n", "    \n", "    if not np.isnan(height) and height > 0:\n", "        pile_heights.append(height)\n", "        valid_height_count += 1\n", "\n", "pile_heights = np.array(pile_heights)\n", "\n", "print(f\"\\nHeight analysis results:\")\n", "print(f\"  Valid measurements: {valid_height_count} / {len(analysis_piles)}\")\n", "\n", "if len(pile_heights) > 0:\n", "    height_stats = describe(pile_heights)\n", "    print(f\"  Mean height: {height_stats.mean:.2f}m\")\n", "    print(f\"  Std: {np.sqrt(height_stats.variance):.2f}m\")\n", "    print(f\"  Range: {height_stats.minmax[0]:.2f} - {height_stats.minmax[1]:.2f}m\")\n", "    print(f\"  Median: {np.median(pile_heights):.2f}m\")\n", "    \n", "    # Height quality assessment\n", "    reasonable_heights = pile_heights[(pile_heights >= 0.5) & (pile_heights <= 8.0)]\n", "    print(f\"  Reasonable heights (0.5-8.0m): {len(reasonable_heights)}/{len(pile_heights)}\")\n", "else:\n", "    print(\"  ⚠️ No valid height measurements\")\n", "    height_stats = None"]}, {"cell_type": "code", "execution_count": 22, "id": "verticality_analysis", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "📊 PILE VERTICALITY ANALYSIS (HEIGHT-FILTERED)\n", "----------------------------------------\n", "Analyzing verticality for 300 piles...\n", "  Processed 50/300 piles\n", "  Processed 100/300 piles\n", "  Processed 150/300 piles\n", "  Processed 200/300 piles\n", "  Processed 250/300 piles\n", "  Processed 300/300 piles\n", "\n", "Verticality analysis results:\n", "  Valid measurements: 293 / 300\n", "  Mean lean angle: 16.44°\n", "  Std: 8.23°\n", "  Range: 1.02 - 58.37°\n", "  Median: 15.81°\n", "\n", "Quality assessment (adjusted for site conditions):\n", "  Excellent (≤15°): 133 (45.4%)\n", "  Good (15-25°): 126 (43.0%)\n", "  Poor (>25°): 34 (11.6%)\n"]}], "source": ["# Pile verticality analysis\n", "print(\"\\n📊 PILE VERTICALITY ANALYSIS (HEIGHT-FILTERED)\")\n", "print(\"-\" * 40)\n", "\n", "pile_lean_angles = []\n", "valid_verticality_count = 0\n", "\n", "print(f\"Analyzing verticality for {len(analysis_piles)} piles...\")\n", "\n", "for i, pile_center in enumerate(analysis_piles):\n", "    if (i + 1) % 50 == 0:\n", "        print(f\"  Processed {i+1}/{len(analysis_piles)} piles\")\n", "    \n", "    # Use simple method for more reliable results\n", "    lean_angle = calculate_pile_verticality_simple(\n", "        points, spatial_index, pile_center, \n", "        radius=VERTICALITY_ANALYSIS_RADIUS\n", "    )\n", "    \n", "    if not np.isnan(lean_angle):\n", "        pile_lean_angles.append(lean_angle)\n", "        valid_verticality_count += 1\n", "\n", "pile_lean_angles = np.array(pile_lean_angles)\n", "\n", "print(f\"\\nVerticality analysis results:\")\n", "print(f\"  Valid measurements: {valid_verticality_count} / {len(analysis_piles)}\")\n", "\n", "if len(pile_lean_angles) > 0:\n", "    verticality_stats = describe(pile_lean_angles)\n", "    print(f\"  Mean lean angle: {verticality_stats.mean:.2f}°\")\n", "    print(f\"  Std: {np.sqrt(verticality_stats.variance):.2f}°\")\n", "    print(f\"  Range: {verticality_stats.minmax[0]:.2f} - {verticality_stats.minmax[1]:.2f}°\")\n", "    print(f\"  Median: {np.median(pile_lean_angles):.2f}°\")\n", "    \n", "    # Verticality quality assessment (ADJUSTED FOR ACTUAL SITE CONDITIONS)\n", "    excellent_vertical = np.sum(pile_lean_angles <= 15.0)  # ≤15° is excellent for these sites\n", "    good_vertical = np.sum((pile_lean_angles > 15.0) & (pile_lean_angles <= 25.0))  # 15-25° is acceptable\n", "    poor_vertical = np.sum(pile_lean_angles > 25.0)  # >25° needs attention\n", "    \n", "    print(f\"\\nQuality assessment (adjusted for site conditions):\")\n", "    print(f\"  Excellent (≤15°): {excellent_vertical} ({excellent_vertical/len(pile_lean_angles)*100:.1f}%)\")\n", "    print(f\"  Good (15-25°): {good_vertical} ({good_vertical/len(pile_lean_angles)*100:.1f}%)\")\n", "    print(f\"  Poor (>25°): {poor_vertical} ({poor_vertical/len(pile_lean_angles)*100:.1f}%)\")\n", "else:\n", "    print(\"  ⚠️ No valid verticality measurements\")\n", "    verticality_stats = None\n", "    excellent_vertical = good_vertical = poor_vertical = 0"]}, {"cell_type": "code", "execution_count": 23, "id": "terrain_aware_analysis", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "📊 PILE LEAN CONSISTENCY ANALYSIS\n", "----------------------------------------\n", "NOTE: Without DEM data, terrain effects cannot be precisely quantified.\n", "This analysis examines pile lean consistency patterns only.\n", "Pile lean pattern analysis:\n", "  Mean lean: 16.4°\n", "  Std deviation: 8.2°\n", "  Consistency ratio: 0.50\n", "  📊 MODERATE CONSISTENCY: Some systematic pattern present\n", "  📊 Mixed installation conditions or moderate systematic effects\n"]}], "source": ["# PILE LEAN CONSISTENCY ANALYSIS\n", "print(\"\\n📊 PILE LEAN CONSISTENCY ANALYSIS\")\n", "print(\"-\" * 40)\n", "print(\"NOTE: Without DEM data, terrain effects cannot be precisely quantified.\")\n", "print(\"This analysis examines pile lean consistency patterns only.\")\n", "\n", "# Quick estimate: If most piles lean in similar direction, likely terrain effect\n", "if len(pile_lean_angles) > 10:\n", "    mean_lean = np.mean(pile_lean_angles)\n", "    std_lean = np.std(pile_lean_angles)\n", "    \n", "    # If standard deviation is much smaller than mean, suggests systematic bias (terrain)\n", "    consistency_ratio = std_lean / mean_lean if mean_lean > 0 else 1.0\n", "    \n", "    print(f\"Pile lean pattern analysis:\")\n", "    print(f\"  Mean lean: {mean_lean:.1f}°\")\n", "    print(f\"  Std deviation: {std_lean:.1f}°\")\n", "    print(f\"  Consistency ratio: {consistency_ratio:.2f}\")\n", "    \n", "    if consistency_ratio < 0.4:\n", "        print(f\"  🎯 HIGH CONSISTENCY: <PERSON><PERSON> lean uniformly - suggests systematic factor\")\n", "        print(f\"  📊 Possible causes: terrain slope, installation method, or equipment bias\")\n", "    elif consistency_ratio < 0.6:\n", "        print(f\"  📊 MODERATE CONSISTENCY: Some systematic pattern present\")\n", "        print(f\"  📊 Mixed installation conditions or moderate systematic effects\")\n", "    else:\n", "        print(f\"  ✅ LOW CONSISTENCY: Pile lean varies significantly\")\n", "        print(f\"  📊 Primarily installation variation, minimal systematic effects\")\n", "else:\n", "    print(\"  ⚠️ Insufficient data for consistency analysis\")"]}, {"cell_type": "code", "execution_count": 24, "id": "donut_comparison", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "🍩 DONUT APPROACH COMPARISON\n", "----------------------------------------\n", "Comparing simple vs donut approach on 20 piles...\n", "\n", "Method comparison:\n", "  Simple method: 16.4° ± 8.3°\n", "  Donut method: 10.6° ± 6.9°\n", "  Difference: 5.8°\n", "  🎯 SIGNIFICANT DIFFERENCE: Donut approach gives different results\n", "  📊 Donut method shows less lean (better ground reference)\n"]}], "source": ["# DONUT APPROACH COMPARISON (QUICK TEST)\n", "print(\"\\n🍩 DONUT APPROACH COMPARISON\")\n", "print(\"-\" * 40)\n", "\n", "# Test donut approach on small sample\n", "sample_size = min(20, len(analysis_piles))\n", "sample_indices = np.random.choice(len(analysis_piles), sample_size, replace=False)\n", "\n", "simple_results = []\n", "donut_results = []\n", "\n", "print(f\"Comparing simple vs donut approach on {sample_size} piles...\")\n", "\n", "for idx in sample_indices:\n", "    pile_center = analysis_piles[idx]\n", "    \n", "    # Simple method\n", "    simple_lean = calculate_pile_verticality_simple(\n", "        points, spatial_index, pile_center, VERTICALITY_ANALYSIS_RADIUS\n", "    )\n", "    \n", "    # Donut method\n", "    donut_lean = calculate_pile_verticality_donut(\n", "        points, spatial_index, pile_center, \n", "        inner_radius=0.3, outer_radius=1.2\n", "    )\n", "    \n", "    if not np.isnan(simple_lean):\n", "        simple_results.append(simple_lean)\n", "    if not np.isnan(donut_lean):\n", "        donut_results.append(donut_lean)\n", "\n", "if simple_results and donut_results:\n", "    print(f\"\\nMethod comparison:\")\n", "    print(f\"  Simple method: {np.mean(simple_results):.1f}° ± {np.std(simple_results):.1f}°\")\n", "    print(f\"  Donut method: {np.mean(donut_results):.1f}° ± {np.std(donut_results):.1f}°\")\n", "    \n", "    difference = np.mean(simple_results) - np.mean(donut_results)\n", "    print(f\"  Difference: {difference:.1f}°\")\n", "    \n", "    if abs(difference) > 3.0:\n", "        print(f\"  🎯 SIGNIFICANT DIFFERENCE: Donut approach gives different results\")\n", "        if difference > 0:\n", "            print(f\"  📊 Donut method shows less lean (better ground reference)\")\n", "        else:\n", "            print(f\"  📊 Simple method shows less lean\")\n", "    else:\n", "        print(f\"  ✅ SIMILAR RESULTS: Both methods agree\")\n", "else:\n", "    print(\"  ⚠️ Insufficient data for comparison\")"]}, {"cell_type": "code", "execution_count": 25, "id": "alignment_analysis", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "📐 PILE ALIGNMENT ANALYSIS (BORROWED TECHNIQUE)\n", "----------------------------------------\n", "Detected tracker rows: 22\n", "Piles in aligned rows: 368 / 368\n", "\n", "Alignment quality by tracker row:\n", "  Row 0: 12 piles\n", "    Max deviation: 0.11m\n", "    Mean deviation: 0.04m\n", "    R-squared: 0.989\n", "    Quality: Excellent\n", "\n", "  Row 1: 12 piles\n", "    Max deviation: 0.76m\n", "    Mean deviation: 0.34m\n", "    R-squared: 0.685\n", "    Quality: Excellent\n", "\n", "  Row 2: 38 piles\n", "    Max deviation: 12.76m\n", "    Mean deviation: 6.57m\n", "    R-squared: 0.695\n", "    Quality: Poor\n", "\n", "  Row 3: 12 piles\n", "    Max deviation: 1.04m\n", "    Mean deviation: 0.49m\n", "    R-squared: 0.894\n", "    Quality: Excellent\n", "\n", "  Row 4: 12 piles\n", "    Max deviation: 0.39m\n", "    Mean deviation: 0.19m\n", "    R-squared: 0.945\n", "    Quality: Excellent\n", "\n", "  Row 5: 12 piles\n", "    Max deviation: 0.03m\n", "    Mean deviation: 0.01m\n", "    R-squared: 0.999\n", "    Quality: Excellent\n", "\n", "  Row 6: 12 piles\n", "    Max deviation: 0.35m\n", "    Mean deviation: 0.16m\n", "    R-squared: 0.831\n", "    Quality: Excellent\n", "\n", "  Row 7: 12 piles\n", "    Max deviation: 1.04m\n", "    Mean deviation: 0.48m\n", "    R-squared: 0.727\n", "    Quality: Excellent\n", "\n", "  Row 8: 12 piles\n", "    Max deviation: 1.56m\n", "    Mean deviation: 0.72m\n", "    R-squared: 0.759\n", "    Quality: Excellent\n", "\n", "  Row 9: 38 piles\n", "    Max deviation: 12.27m\n", "    Mean deviation: 6.53m\n", "    R-squared: 0.708\n", "    Quality: Poor\n", "\n", "  Row 10: 12 piles\n", "    Max deviation: 0.06m\n", "    Mean deviation: 0.03m\n", "    R-squared: 0.997\n", "    Quality: Excellent\n", "\n", "  Row 11: 12 piles\n", "    Max deviation: 0.07m\n", "    Mean deviation: 0.04m\n", "    R-squared: 0.993\n", "    Quality: Excellent\n", "\n", "  Row 12: 12 piles\n", "    Max deviation: 0.83m\n", "    Mean deviation: 0.36m\n", "    R-squared: 0.717\n", "    Quality: Excellent\n", "\n", "  Row 13: 38 piles\n", "    Max deviation: 12.65m\n", "    Mean deviation: 6.57m\n", "    R-squared: 0.696\n", "    Quality: Poor\n", "\n", "  Row 14: 12 piles\n", "    Max deviation: 0.98m\n", "    Mean deviation: 0.44m\n", "    R-squared: 0.893\n", "    Quality: Excellent\n", "\n", "  Row 15: 12 piles\n", "    Max deviation: 0.40m\n", "    Mean deviation: 0.19m\n", "    R-squared: 0.954\n", "    Quality: Excellent\n", "\n", "  Row 16: 12 piles\n", "    Max deviation: 0.04m\n", "    Mean deviation: 0.02m\n", "    R-squared: 0.999\n", "    Quality: Excellent\n", "\n", "  Row 17: 12 piles\n", "    Max deviation: 0.33m\n", "    Mean deviation: 0.15m\n", "    R-squared: 0.850\n", "    Quality: Excellent\n", "\n", "  Row 18: 12 piles\n", "    Max deviation: 1.08m\n", "    Mean deviation: 0.48m\n", "    R-squared: 0.719\n", "    Quality: Excellent\n", "\n", "  Row 19: 12 piles\n", "    Max deviation: 1.55m\n", "    Mean deviation: 0.70m\n", "    R-squared: 0.766\n", "    Quality: Excellent\n", "\n", "  Row 20: 38 piles\n", "    Max deviation: 12.26m\n", "    Mean deviation: 6.53m\n", "    R-squared: 0.707\n", "    Quality: Poor\n", "\n", "  Row 21: 12 piles\n", "    Max deviation: 0.06m\n", "    Mean deviation: 0.03m\n", "    R-squared: 0.997\n", "    Quality: Excellent\n", "\n", "Overall alignment summary:\n", "  Average deviation: 1.41m\n", "  Average R-squared: 0.842\n", "  Excellent rows: 18/22\n", "  Good rows: 0/22\n", "  Poor rows: 4/22\n"]}], "source": ["# Pile alignment analysis (borrowed technique)\n", "print(\"\\n📐 PILE ALIGNMENT ANALYSIS (BORROWED TECHNIQUE)\")\n", "print(\"-\" * 40)\n", "\n", "if len(pile_locations) >= 6:  # Need minimum piles for alignment analysis\n", "    alignment_results = calculate_pile_alignment_quality(pile_locations, min_piles_per_row=3)\n", "    \n", "    print(f\"Detected tracker rows: {len(alignment_results)}\")\n", "    \n", "    if alignment_results:\n", "        total_piles_in_rows = sum(result['pile_count'] for result in alignment_results)\n", "        print(f\"Piles in aligned rows: {total_piles_in_rows} / {len(pile_locations)}\")\n", "        \n", "        print(\"\\nAlignment quality by tracker row:\")\n", "        for result in alignment_results:\n", "            print(f\"  Row {result['tracker_id']}: {result['pile_count']} piles\")\n", "            print(f\"    Max deviation: {result['max_deviation']:.2f}m\")\n", "            print(f\"    Mean deviation: {result['mean_deviation']:.2f}m\")\n", "            print(f\"    R-squared: {result['r_squared']:.3f}\")\n", "            \n", "            # Quality assessment (REALISTIC THRESHOLDS)\n", "            if result['max_deviation'] <= 2.0:  # ≤2m is excellent for solar construction\n", "                quality = \"Excellent\"\n", "            elif result['max_deviation'] <= 4.0:  # 2-4m is acceptable\n", "                quality = \"Good\"\n", "            else:  # >4m needs attention\n", "                quality = \"Poor\"\n", "            print(f\"    Quality: {quality}\")\n", "            print()\n", "        \n", "        # Overall alignment statistics\n", "        all_deviations = [result['mean_deviation'] for result in alignment_results]\n", "        all_r_squared = [result['r_squared'] for result in alignment_results]\n", "        \n", "        print(f\"Overall alignment summary:\")\n", "        print(f\"  Average deviation: {np.mean(all_deviations):.2f}m\")\n", "        print(f\"  Average R-squared: {np.mean(all_r_squared):.3f}\")\n", "        \n", "        excellent_rows = sum(1 for result in alignment_results if result['max_deviation'] <= 2.0)\n", "        good_rows = sum(1 for result in alignment_results if 2.0 < result['max_deviation'] <= 4.0)\n", "        poor_rows = len(alignment_results) - excellent_rows - good_rows\n", "        \n", "        print(f\"  Excellent rows: {excellent_rows}/{len(alignment_results)}\")\n", "        print(f\"  Good rows: {good_rows}/{len(alignment_results)}\")\n", "        print(f\"  Poor rows: {poor_rows}/{len(alignment_results)}\")\n", "    else:\n", "        print(\"  ⚠️ No aligned tracker rows detected\")\n", "        alignment_results = []\n", "else:\n", "    print(f\"  ⚠️ Insufficient piles ({len(pile_locations)}) for alignment analysis\")\n", "    alignment_results = []"]}, {"cell_type": "code", "execution_count": 26, "id": "export_results", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "💾 EXPORTING RESULTS\n", "----------------------------------------\n", "Creating detailed results for 300 piles...\n", "✅ Results saved: res_geometric_analysis_20250808_073713.csv\n", "   <PERSON><PERSON> analyzed: 300\n", "   Valid heights: 300\n", "   Valid verticality: 293\n", "✅ Summary saved: res_analysis_summary_20250808_073713.json\n", "\n", "🎉 RES SITE ANALYSIS COMPLETE!\n", "📁 Output directory: output_runs/geometric_analysis_res\n", "📊 Load CSV in QGIS for visualization\n"]}], "source": ["# Export results\n", "print(\"\\n💾 EXPORTING RESULTS\")\n", "print(\"-\" * 40)\n", "\n", "# Create detailed results for each analyzed pile\n", "pile_data = []\n", "\n", "print(f\"Creating detailed results for {len(analysis_piles)} piles...\")\n", "\n", "for i, pile_center in enumerate(analysis_piles):\n", "    # Get measurements for this pile\n", "    height = calculate_pile_height_donut_fast(\n", "        points, spatial_index, pile_center, \n", "        inner_radius=DONUT_INNER_RADIUS, \n", "        outer_radius=DONUT_OUTER_RADIUS\n", "    )\n", "    \n", "    # Use simple method for more reliable results\n", "    lean_angle = calculate_pile_verticality_simple(\n", "        points, spatial_index, pile_center, \n", "        radius=VERTICALITY_ANALYSIS_RADIUS\n", "    )\n", "    \n", "    # Get nearest neighbor distance\n", "    if len(analysis_piles) > 1:\n", "        other_piles = np.delete(analysis_piles, i, axis=0)\n", "        distances_to_others = np.sqrt(np.sum((other_piles - pile_center)**2, axis=1))\n", "        nearest_distance = np.min(distances_to_others)\n", "    else:\n", "        nearest_distance = np.nan\n", "    \n", "    # Quality assessments\n", "    if WITHIN_TRACKER_RANGE[0] <= nearest_distance <= WITHIN_TRACKER_RANGE[1]:\n", "        spacing_quality = 'Within-Tracker'\n", "    elif BETWEEN_TRACKER_RANGE[0] <= nearest_distance <= BETWEEN_TRACKER_RANGE[1]:\n", "        spacing_quality = 'Between-Tracker'\n", "    else:\n", "        spacing_quality = 'Other'\n", "    \n", "    if not np.isnan(lean_angle):\n", "        if lean_angle <= 15.0:  # ≤15° is excellent for these sites\n", "            verticality_quality = 'Excellent'\n", "        elif lean_angle <= 25.0:  # 15-25° is good\n", "            verticality_quality = 'Good'\n", "        else:  # >25° is poor\n", "            verticality_quality = 'Poor'\n", "    else:\n", "        verticality_quality = 'Unknown'\n", "    \n", "    pile_data.append({\n", "        'pile_id': f'res_pile_{i}',\n", "        'site_name': SITE_DISPLAY_NAME,\n", "        'utm_x': pile_center[0],\n", "        'utm_y': pile_center[1],\n", "        'confidence': detected_piles.iloc[i]['confidence'] if i < len(detected_piles) else np.nan,\n", "        'nearest_distance': nearest_distance,\n", "        'spacing_quality': spacing_quality,\n", "        'pile_height': height if not np.isnan(height) else None,\n", "        'lean_angle': lean_angle if not np.isnan(lean_angle) else None,\n", "        'verticality_quality': verticality_quality\n", "    })\n", "\n", "# Create DataFrame\n", "pile_df = pd.DataFrame(pile_data)\n", "\n", "# Convert to geographic coordinates\n", "geometry = [Point(xy) for xy in zip(pile_df['utm_x'], pile_df['utm_y'])]\n", "gdf = gpd.GeoDataFrame(pile_df, geometry=geometry, crs=SITE_CRS)\n", "gdf_wgs84 = gdf.to_crs('EPSG:4326')\n", "pile_df['longitude'] = gdf_wgs84.geometry.x\n", "pile_df['latitude'] = gdf_wgs84.geometry.y\n", "\n", "# Save CSV\n", "csv_filename = f\"res_geometric_analysis_{timestamp}.csv\"\n", "csv_path = output_dir / csv_filename\n", "pile_df.to_csv(csv_path, index=False)\n", "\n", "print(f\"✅ Results saved: {csv_filename}\")\n", "print(f\"   <PERSON>les analyzed: {len(pile_df)}\")\n", "print(f\"   Valid heights: {pile_df['pile_height'].notna().sum()}\")\n", "print(f\"   Valid verticality: {pile_df['lean_angle'].notna().sum()}\")\n", "\n", "# Save summary\n", "summary = {\n", "    'site_info': {\n", "        'site_name': SITE_DISPLAY_NAME,\n", "        'site_id': SITE_NAME,\n", "        'timestamp': timestamp,\n", "        'total_detections': len(ml_results),\n", "        'high_confidence_detections': len(detected_piles),\n", "        'analyzed_piles': len(analysis_piles)\n", "    },\n", "    'spacing_analysis': {\n", "        'mean_spacing': float(spacing_stats.mean) if spacing_stats else None,\n", "        'std_spacing': float(np.sqrt(spacing_stats.variance)) if spacing_stats else None,\n", "        'median_spacing': float(np.median(all_distances)) if len(all_distances) > 0 else None,\n", "        'distance_pairs': len(all_distances)\n", "    },\n", "    'height_analysis': {\n", "        'valid_measurements': valid_height_count,\n", "        'mean_height': float(height_stats.mean) if height_stats else None,\n", "        'std_height': float(np.sqrt(height_stats.variance)) if height_stats else None,\n", "        'median_height': float(np.median(pile_heights)) if len(pile_heights) > 0 else None\n", "    },\n", "    'verticality_analysis': {\n", "        'valid_measurements': valid_verticality_count,\n", "        'mean_lean': float(verticality_stats.mean) if verticality_stats else None,\n", "        'std_lean': float(np.sqrt(verticality_stats.variance)) if verticality_stats else None,\n", "        'excellent_count': int(excellent_vertical),\n", "        'good_count': int(good_vertical),\n", "        'poor_count': int(poor_vertical)\n", "    }\n", "}\n", "\n", "summary_file = output_dir / f\"res_analysis_summary_{timestamp}.json\"\n", "with open(summary_file, 'w') as f:\n", "    json.dump(summary, f, indent=2)\n", "\n", "print(f\"✅ Summary saved: {summary_file.name}\")\n", "\n", "print(f\"\\n🎉 RES SITE ANALYSIS COMPLETE!\")\n", "print(f\"📁 Output directory: {output_dir}\")\n", "print(f\"📊 Load CSV in QGIS for visualization\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 5}