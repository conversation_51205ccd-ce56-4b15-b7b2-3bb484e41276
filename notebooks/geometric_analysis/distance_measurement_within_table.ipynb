{"nbformat": 4, "nbformat_minor": 0, "metadata": {"colab": {"provenance": []}, "kernelspec": {"name": "python3", "display_name": "Python 3"}, "language_info": {"name": "python"}}, "cells": [{"cell_type": "code", "execution_count": null, "metadata": {"id": "R-0em3gJkNTe"}, "outputs": [], "source": ["import math\n", "import geopandas as gpd\n", "import pandas as pd\n", "import xml.etree.ElementTree as ET\n", "import csv"]}, {"cell_type": "code", "source": ["from google.colab import files\n", "\n", "# Upload the KML file\n", "uploaded = files.upload()\n", "\n", "# Get the file name\n", "kml_file_path = list(uploaded.keys())[0]\n", "print(f\"Uploaded file: {kml_file_path}\")"], "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 90}, "id": "YTxhXllYkX6K", "outputId": "b46d8d56-383d-4540-9f7e-f0d0f74a2903"}, "execution_count": null, "outputs": [{"output_type": "display_data", "data": {"text/plain": ["<IPython.core.display.HTML object>"], "text/html": ["\n", "     <input type=\"file\" id=\"files-f56df220-399d-4633-ad11-56b651daa78e\" name=\"files[]\" multiple disabled\n", "        style=\"border:none\" />\n", "     <output id=\"result-f56df220-399d-4633-ad11-56b651daa78e\">\n", "      Upload widget is only available when the cell has been executed in the\n", "      current browser session. Please rerun this cell to enable.\n", "      </output>\n", "      <script>// Copyright 2017 Google LLC\n", "//\n", "// Licensed under the Apache License, Version 2.0 (the \"License\");\n", "// you may not use this file except in compliance with the License.\n", "// You may obtain a copy of the License at\n", "//\n", "//      http://www.apache.org/licenses/LICENSE-2.0\n", "//\n", "// Unless required by applicable law or agreed to in writing, software\n", "// distributed under the License is distributed on an \"AS IS\" BASIS,\n", "// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n", "// See the License for the specific language governing permissions and\n", "// limitations under the License.\n", "\n", "/**\n", " * @fileoverview Helpers for google.colab Python module.\n", " */\n", "(function(scope) {\n", "function span(text, styleAttributes = {}) {\n", "  const element = document.createElement('span');\n", "  element.textContent = text;\n", "  for (const key of Object.keys(styleAttributes)) {\n", "    element.style[key] = styleAttributes[key];\n", "  }\n", "  return element;\n", "}\n", "\n", "// Max number of bytes which will be uploaded at a time.\n", "const MAX_PAYLOAD_SIZE = 100 * 1024;\n", "\n", "function _uploadFiles(inputId, outputId) {\n", "  const steps = uploadFilesStep(inputId, outputId);\n", "  const outputElement = document.getElementById(outputId);\n", "  // Cache steps on the outputElement to make it available for the next call\n", "  // to uploadFilesContinue from Python.\n", "  outputElement.steps = steps;\n", "\n", "  return _uploadFilesContinue(outputId);\n", "}\n", "\n", "// This is roughly an async generator (not supported in the browser yet),\n", "// where there are multiple asynchronous steps and the Python side is going\n", "// to poll for completion of each step.\n", "// This uses a Promise to block the python side on completion of each step,\n", "// then passes the result of the previous step as the input to the next step.\n", "function _uploadFilesContinue(outputId) {\n", "  const outputElement = document.getElementById(outputId);\n", "  const steps = outputElement.steps;\n", "\n", "  const next = steps.next(outputElement.lastPromiseValue);\n", "  return Promise.resolve(next.value.promise).then((value) => {\n", "    // Cache the last promise value to make it available to the next\n", "    // step of the generator.\n", "    outputElement.lastPromiseValue = value;\n", "    return next.value.response;\n", "  });\n", "}\n", "\n", "/**\n", " * Generator function which is called between each async step of the upload\n", " * process.\n", " * @param {string} inputId Element ID of the input file picker element.\n", " * @param {string} outputId Element ID of the output display.\n", " * @return {!Iterable<!Object>} Iterable of next steps.\n", " */\n", "function* uploadFilesStep(inputId, outputId) {\n", "  const inputElement = document.getElementById(inputId);\n", "  inputElement.disabled = false;\n", "\n", "  const outputElement = document.getElementById(outputId);\n", "  outputElement.innerHTML = '';\n", "\n", "  const pickedPromise = new Promise((resolve) => {\n", "    inputElement.addEventListener('change', (e) => {\n", "      resolve(e.target.files);\n", "    });\n", "  });\n", "\n", "  const cancel = document.createElement('button');\n", "  inputElement.parentElement.appendChild(cancel);\n", "  cancel.textContent = 'Cancel upload';\n", "  const cancelPromise = new Promise((resolve) => {\n", "    cancel.onclick = () => {\n", "      resolve(null);\n", "    };\n", "  });\n", "\n", "  // Wait for the user to pick the files.\n", "  const files = yield {\n", "    promise: Promise.race([pickedPromise, cancelPromise]),\n", "    response: {\n", "      action: 'starting',\n", "    }\n", "  };\n", "\n", "  cancel.remove();\n", "\n", "  // Disable the input element since further picks are not allowed.\n", "  inputElement.disabled = true;\n", "\n", "  if (!files) {\n", "    return {\n", "      response: {\n", "        action: 'complete',\n", "      }\n", "    };\n", "  }\n", "\n", "  for (const file of files) {\n", "    const li = document.createElement('li');\n", "    li.append(span(file.name, {fontWeight: 'bold'}));\n", "    li.append(span(\n", "        `(${file.type || 'n/a'}) - ${file.size} bytes, ` +\n", "        `last modified: ${\n", "            file.lastModifiedDate ? file.lastModifiedDate.toLocaleDateString() :\n", "                                    'n/a'} - `));\n", "    const percent = span('0% done');\n", "    li.append<PERSON><PERSON>d(percent);\n", "\n", "    outputElement.appendChild(li);\n", "\n", "    const fileDataPromise = new Promise((resolve) => {\n", "      const reader = new FileReader();\n", "      reader.onload = (e) => {\n", "        resolve(e.target.result);\n", "      };\n", "      reader.readAsArrayBuffer(file);\n", "    });\n", "    // Wait for the data to be ready.\n", "    let fileData = yield {\n", "      promise: fileDataPromise,\n", "      response: {\n", "        action: 'continue',\n", "      }\n", "    };\n", "\n", "    // Use a chunked sending to avoid message size limits. See b/62115660.\n", "    let position = 0;\n", "    do {\n", "      const length = Math.min(fileData.byteLength - position, MAX_PAYLOAD_SIZE);\n", "      const chunk = new Uint8Array(fileData, position, length);\n", "      position += length;\n", "\n", "      const base64 = btoa(String.fromCharCode.apply(null, chunk));\n", "      yield {\n", "        response: {\n", "          action: 'append',\n", "          file: file.name,\n", "          data: base64,\n", "        },\n", "      };\n", "\n", "      let percentDone = fileData.byteLength === 0 ?\n", "          100 :\n", "          Math.round((position / fileData.byteLength) * 100);\n", "      percent.textContent = `${percentDone}% done`;\n", "\n", "    } while (position < fileData.byteLength);\n", "  }\n", "\n", "  // All done.\n", "  yield {\n", "    response: {\n", "      action: 'complete',\n", "    }\n", "  };\n", "}\n", "\n", "scope.google = scope.google || {};\n", "scope.google.colab = scope.google.colab || {};\n", "scope.google.colab._files = {\n", "  _uploadFiles,\n", "  _uploadFilesContinue,\n", "};\n", "})(self);\n", "</script> "]}, "metadata": {}}, {"output_type": "stream", "name": "stdout", "text": ["Saving Pile.kml to Pile.kml\n", "Uploaded file: Pile.kml\n"]}]}, {"cell_type": "code", "source": ["# Function to calculate Haversine distance between two lat/long points in meters\n", "def haversine(lat1, lon1, lat2, lon2):\n", "    print(\"Starting Haversine distance calculation...\")\n", "    R = 6371000  # Radius of Earth in meters\n", "    phi1 = math.radians(lat1)\n", "    phi2 = math.radians(lat2)\n", "    delta_phi = math.radians(lat2 - lat1)\n", "    delta_lambda = math.radians(lon2 - lon1)\n", "\n", "    a = math.sin(delta_phi / 2)**2 + math.cos(phi1) * math.cos(phi2) * math.sin(delta_lambda / 2)**2\n", "    c = 2 * math.atan2(math.sqrt(a), math.sqrt(1 - a))\n", "\n", "    distance = R * c  # Distance in meters\n", "    print(\"Calculation done! Distance:\", distance, \"meters\")\n", "    return distance"], "metadata": {"id": "R3fadE0Lkemf"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["# Function to parse KML and extract coordinates and pile descriptions\n", "def parse_kml(kml_file):\n", "    tree = ET.parse(kml_file)\n", "    root = tree.getroot()\n", "\n", "    # Define namespaces\n", "    ns = {'kml': 'http://www.opengis.net/kml/2.2'}\n", "\n", "    # Initialize list to store extracted data\n", "    data = []\n", "\n", "    # Loop through each Placemark and extract necessary information\n", "    for placemark in root.findall('.//kml:Placemark', ns):\n", "        name = placemark.find('.//kml:name', ns).text\n", "        description = placemark.find('.//kml:description', ns).text\n", "\n", "        # Extract the full Pile_no line\n", "        pile_number = \"\"\n", "        if description:\n", "            for line in description.splitlines():\n", "                if \"Pile_no:\" in line:\n", "                    pile_number = line.split(\"Pile_no:\")[-1].strip()  # Extract full line after \"Pile_no:\"\n", "\n", "        coordinates = placemark.find('.//kml:coordinates', ns).text.strip()\n", "\n", "        # Extract latitude and longitude from coordinates\n", "        lon, lat = map(float, coordinates.split(',')[:2])  # Ignore altitude\n", "\n", "        data.append({\n", "            'Table_no.': name,\n", "            'description': pile_number,  # Full Pile_no (e.g., MDP-03-405 - 3ET)\n", "            'latitude': lat,\n", "            'longitude': lon,\n", "        })\n", "\n", "    return data\n", "\n", "# Function to save the data to a CSV file with distance calculations\n", "def save_to_csv(data, output_file):\n", "    # Define the fieldnames for the CSV file\n", "    fieldnames = ['Table_no.', 'from-to','distance_from_previous_pile','description', 'latitude', 'longitude']\n", "\n", "    # Open the CSV file for writing\n", "    with open(output_file, mode='w', newline='', encoding='utf-8') as file:\n", "        writer = csv.DictWriter(file, fieldnames=fieldnames)\n", "\n", "        # Write header row\n", "        writer.writeheader()\n", "        data=data[:16]\n", "        print(data)\n", "        for i, current_pile in enumerate(data):\n", "            if i > 0:  # For subsequent piles, calculate distance\n", "                prev_pile = data[i - 1]\n", "\n", "                next_pile = data[i]\n", "                if next_pile['Table_no.']!=prev_pile['Table_no.']:\n", "                  print(next_pile['Table_no.'])\n", "                  print (prev_pile['Table_no.'])\n", "                else:\n", "                  print(next_pile['Table_no.'])\n", "                  print (prev_pile['Table_no.'])\n", "\n", "                previous_pile = data[i - 1]\n", "                distance = haversine(\n", "                    previous_pile['latitude'], previous_pile['longitude'],\n", "                    current_pile['latitude'], current_pile['longitude']\n", "                )\n", "                current_pile['distance_from_previous_pile'] = distance\n", "                current_pile['from-to'] = f\"{previous_pile['description']} {previous_pile['Table_no.']} - {current_pile['description']} {current_pile['Table_no.']}\"\n", "            else:  # For the first pile, set distance to 0\n", "                current_pile['distance_from_previous_pile'] = 0\n", "\n", "            # Write the current row to the CSV file\n", "            writer.writerow(current_pile)\n", "\n", "# Main function to execute the program\n", "def main():\n", "    # Input KML file and output CSV file paths\n", "    kml_file = kml_file_path  # Replace with your actual KML file path\n", "    output_file = 'output.csv'  # Replace with your desired output CSV file path\n", "\n", "    # Parse the KML file\n", "    data = parse_kml(kml_file)\n", "\n", "    # Save the data with Haversine distance to CSV\n", "    save_to_csv(data, output_file)\n", "\n", "    print(f\"Data successfully saved to {output_file}\")\n", "\n", "if __name__ == \"__main__\":\n", "    main()\n"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "_dqwlDMhknOd", "outputId": "24eb5ad3-8337-446f-d368-d6b62463cb7d"}, "execution_count": null, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Data successfully saved to output.csv\n"]}]}, {"cell_type": "code", "source": [], "metadata": {"id": "uTXCnvQpkq_9"}, "execution_count": null, "outputs": []}]}