{"nbformat": 4, "nbformat_minor": 0, "metadata": {"colab": {"provenance": []}, "kernelspec": {"name": "python3", "display_name": "Python 3"}, "language_info": {"name": "python"}}, "cells": [{"cell_type": "code", "execution_count": null, "metadata": {"id": "wcWp7vPYsXY1"}, "outputs": [], "source": ["import geopandas as gpd\n", "import pandas as pd\n", "import re\n", "import numpy as np\n", "from shapely.geometry import Point"]}, {"cell_type": "code", "source": ["def extract_data_from_kml(kml_file):\n", "    \"\"\"Reads a KML file and extracts Table No, Block No dynamically.\"\"\"\n", "\n", "    gdf = gpd.read_file(kml_file, driver=\"KML\")  # Read KML file into GeoDataFrame\n", "\n", "    extracted_data = []\n", "\n", "    for _, row in gdf.iterrows():\n", "        description = row.get(\"Description\", \"\")\n", "\n", "        # Extract Table No and Block No dynamically\n", "        table_match = re.search(r\"Table No:\\s*([\\w\\d_]+)\", description)\n", "        block_match = re.search(r\"Block No:\\s*(Block \\d+)\", description)\n", "        pile_match =  re.search(r'Pile No:\\s*(\\d+)', description)\n", "\n", "        table_no = table_match.group(1) if table_match else \"Unknown\"\n", "        block_no = block_match.group(1) if block_match else \"Unknown\"\n", "        pile_no =  pile_match.group(1) if pile_match else \"Unknown\"\n", "\n", "        # Extract Coordinates\n", "        lon, lat = row.geometry.x, row.geometry.y\n", "\n", "        extracted_data.append((table_no, block_no, lon, lat, pile_no))\n", "\n", "    # Convert to DataFrame\n", "    return pd.DataFrame(extracted_data, columns=[\"Table No\", \"Block No\", \"Longitude\", \"Latitude\",\"Pile No\"])\n", "\n", "def calculate_angle(lat1, lon1, lat2, lon2):\n", "    # \"\"\"Calculates the angle in degrees between two points w.r.t the x-axis.\"\"\"\n", "    # return np.degrees(np.arctan2(y2 - y1, x2 - x1))\n", "    lat1, lon1, lat2, lon2 = map(np.radians, [lat1, lon1, lat2, lon2])  # Convert to radians\n", "\n", "    delta_lon = lon2 - lon1\n", "    x = np.sin(delta_lon) * np.cos(lat2)\n", "    y = np.cos(lat1) * np.sin(lat2) - (np.sin(lat1) * np.cos(lat2) * np.cos(delta_lon))\n", "\n", "    initial_bearing = np.arctan2(x, y)\n", "    # bearing_degrees = (np.degrees(initial_bearing) + 360) % 360  # Normalize to 0-360°\n", "\n", "    return initial_bearing * (180 / np.pi)\n", "\n", "def measure_angles(df):\n", "    \"\"\"Computes angles based on the first and last pile per table.\"\"\"\n", "\n", "    results = []\n", "\n", "    for table_no, group in df.groupby(\"Table No\"):\n", "        print(group)\n", "\n", "        if len(group) < 2:\n", "            continue  # Skip tables with less than 2 piles\n", "\n", "        # First and last pile points\n", "        first = group.iloc[0]\n", "        last = group.iloc[-1]\n", "\n", "        x1, y1 = first[\"Longitude\"], first[\"Latitude\"]\n", "        x2, y2 = last[\"Longitude\"], last[\"Latitude\"]\n", "\n", "        # Compute angle\n", "        angle = calculate_angle(x1, y1, x2, y2)\n", "\n", "        results.append((f\"{table_no}-from-{first['Pile No']}-to-{last['Pile No']}\", angle))\n", "\n", "\n", "    return pd.DataFrame(results, columns=[\"Table No\", \"Angle (°)\"])\n", "\n", "# File path\n", "kml_file_path = \"/content/Pile Points.kml\"\n", "\n", "# Extract data\n", "df = extract_data_from_kml(kml_file_path)\n", "\n", "# Compute angles\n", "angle_df = measure_angles(df)\n", "\n", "# Save only <PERSON><PERSON> to Excel\n", "output_file = \"output1.xlsx\"\n", "angle_df.to_excel(output_file, sheet_name=\"Angle Data\", index=False)\n", "\n", "print(f\"Excel file created: {output_file}\")"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "ND3hvaD3AJce", "outputId": "a474db3f-12eb-4965-8886-594d5c06cd99"}, "execution_count": null, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["    Table No Block No   Longitude   Latitude   Pile No\n", "184  TABLE_1  Block 8 -100.207545  31.893962  11101215\n", "198  TABLE_1  Block 8 -100.207545  31.894037  11101214\n", "210  TABLE_1  Block 8 -100.207544  31.894120  11101213\n", "222  TABLE_1  Block 8 -100.207544  31.894202  11101212\n", "234  TABLE_1  Block 8 -100.207545  31.894284  11101211\n", "246  TABLE_1  Block 8 -100.207544  31.894367  11101210\n", "258  TABLE_1  Block 8 -100.207544  31.894449  11101209\n", "270  TABLE_1  Block 8 -100.207544  31.894530  11101208\n", "284  TABLE_1  Block 8 -100.207544  31.894610  11101207\n", "296  TABLE_1  Block 8 -100.207544  31.894693  11101206\n", "308  TABLE_1  Block 8 -100.207544  31.894775  11101205\n", "320  TABLE_1  Block 8 -100.207544  31.894857  11101204\n", "332  TABLE_1  Block 8 -100.207544  31.894940  11101203\n", "344  TABLE_1  Block 8 -100.207544  31.895022  11101202\n", "356  TABLE_1  Block 8 -100.207544  31.895099  11101201\n", "     Table No Block No   Longitude   Latitude   Pile No\n", "195  TABLE_10  Block 8 -100.208224  31.893964  11100315\n", "207  TABLE_10  Block 8 -100.208224  31.894038  11100314\n", "219  TABLE_10  Block 8 -100.208224  31.894121  11100313\n", "231  TABLE_10  Block 8 -100.208224  31.894203  11100312\n", "243  TABLE_10  Block 8 -100.208224  31.894284  11100311\n", "255  TABLE_10  Block 8 -100.208224  31.894368  11100310\n", "267  TABLE_10  Block 8 -100.208224  31.894450  11100309\n", "279  TABLE_10  Block 8 -100.208222  31.894530  11100308\n", "293  TABLE_10  Block 8 -100.208223  31.894610  11100307\n", "305  TABLE_10  Block 8 -100.208223  31.894694  11100306\n", "317  TABLE_10  Block 8 -100.208223  31.894775  11100305\n", "329  TABLE_10  Block 8 -100.208223  31.894857  11100304\n", "341  TABLE_10  Block 8 -100.208223  31.894941  11100303\n", "353  TABLE_10  Block 8 -100.208223  31.895022  11100302\n", "365  TABLE_10  Block 8 -100.208223  31.895099  11100301\n", "     Table No Block No   Longitude   Latitude   Pile No\n", "188  TABLE_11  Block 8 -100.208297  31.893963  11100217\n", "196  TABLE_11  Block 8 -100.208298  31.894024  11100216\n", "208  TABLE_11  Block 8 -100.208297  31.894086  11100215\n", "220  TABLE_11  Block 8 -100.208297  31.894162  11100214\n", "232  TABLE_11  Block 8 -100.208297  31.894234  11100213\n", "244  TABLE_11  Block 8 -100.208297  31.894313  11100212\n", "256  TABLE_11  Block 8 -100.208296  31.894385  11100211\n", "268  TABLE_11  Block 8 -100.208296  31.894456  11100210\n", "280  TABLE_11  Block 8 -100.208297  31.894530  11100209\n", "282  TABLE_11  Block 8 -100.208296  31.894605  11100208\n", "294  TABLE_11  Block 8 -100.208296  31.894675  11100207\n", "306  TABLE_11  Block 8 -100.208296  31.894749  11100206\n", "318  TABLE_11  Block 8 -100.208296  31.894826  11100205\n", "330  TABLE_11  Block 8 -100.208296  31.894899  11100204\n", "342  TABLE_11  Block 8 -100.208295  31.894974  11100203\n", "354  TABLE_11  Block 8 -100.208295  31.895037  11100202\n", "366  TABLE_11  Block 8 -100.208295  31.895099  11100201\n", "     Table No Block No   Longitude   Latitude   Pile No\n", "190  TABLE_12  Block 8 -100.208375  31.893963  11100117\n", "197  TABLE_12  Block 8 -100.208375  31.894025  11100116\n", "209  TABLE_12  Block 8 -100.208375  31.894087  11100115\n", "221  TABLE_12  Block 8 -100.208374  31.894162  11100114\n", "233  TABLE_12  Block 8 -100.208374  31.894235  11100113\n", "245  TABLE_12  Block 8 -100.208374  31.894313  11100112\n", "257  TABLE_12  Block 8 -100.208373  31.894385  11100111\n", "269  TABLE_12  Block 8 -100.208374  31.894456  11100110\n", "281  TABLE_12  Block 8 -100.208372  31.894530  11100109\n", "283  TABLE_12  Block 8 -100.208374  31.894605  11100108\n", "295  TABLE_12  Block 8 -100.208373  31.894676  11100107\n", "307  TABLE_12  Block 8 -100.208373  31.894749  11100106\n", "319  TABLE_12  Block 8 -100.208373  31.894826  11100105\n", "331  TABLE_12  Block 8 -100.208373  31.894900  11100104\n", "343  TABLE_12  Block 8 -100.208373  31.894975  11100103\n", "355  TABLE_12  Block 8 -100.208373  31.895037  11100102\n", "367  TABLE_12  Block 8 -100.208373  31.895099  11100101\n", "     Table No Block No   Longitude   Latitude   Pile No\n", "0    TABLE_13  Block 8 -100.207547  31.892760  11201215\n", "14   TABLE_13  Block 8 -100.207546  31.892835  11201214\n", "26   TABLE_13  Block 8 -100.207546  31.892917  11201213\n", "38   TABLE_13  Block 8 -100.207546  31.893000  11201212\n", "50   TABLE_13  Block 8 -100.207546  31.893082  11201211\n", "62   TABLE_13  Block 8 -100.207546  31.893165  11201210\n", "74   TABLE_13  Block 8 -100.207545  31.893247  11201209\n", "86   TABLE_13  Block 8 -100.207546  31.893327  11201208\n", "100  TABLE_13  Block 8 -100.207545  31.893407  11201207\n", "112  TABLE_13  Block 8 -100.207545  31.893490  11201206\n", "124  TABLE_13  Block 8 -100.207545  31.893572  11201205\n", "136  TABLE_13  Block 8 -100.207545  31.893655  11201204\n", "148  TABLE_13  Block 8 -100.207545  31.893737  11201203\n", "160  TABLE_13  Block 8 -100.207545  31.893820  11201202\n", "172  TABLE_13  Block 8 -100.207545  31.893896  11201201\n", "     Table No Block No   Longitude   Latitude   Pile No\n", "1    TABLE_14  Block 8 -100.207624  31.892759  11201115\n", "15   TABLE_14  Block 8 -100.207624  31.892835  11201114\n", "27   TABLE_14  Block 8 -100.207624  31.892916  11201113\n", "39   TABLE_14  Block 8 -100.207624  31.893000  11201112\n", "51   TABLE_14  Block 8 -100.207625  31.893081  11201111\n", "63   TABLE_14  Block 8 -100.207624  31.893165  11201110\n", "75   TABLE_14  Block 8 -100.207623  31.893247  11201109\n", "87   TABLE_14  Block 8 -100.207622  31.893327  11201108\n", "101  TABLE_14  Block 8 -100.207623  31.893408  11201107\n", "113  TABLE_14  Block 8 -100.207622  31.893490  11201106\n", "125  TABLE_14  Block 8 -100.207623  31.893571  11201105\n", "137  TABLE_14  Block 8 -100.207623  31.893654  11201104\n", "149  TABLE_14  Block 8 -100.207623  31.893738  11201103\n", "161  TABLE_14  Block 8 -100.207623  31.893820  11201102\n", "173  TABLE_14  Block 8 -100.207623  31.893896  11201101\n", "     Table No Block No   Longitude   Latitude   Pile No\n", "2    TABLE_15  Block 8 -100.207697  31.892760  11201015\n", "16   TABLE_15  Block 8 -100.207697  31.892835  11201014\n", "28   TABLE_15  Block 8 -100.207697  31.892918  11201013\n", "40   TABLE_15  Block 8 -100.207696  31.892999  11201012\n", "52   TABLE_15  Block 8 -100.207697  31.893083  11201011\n", "64   TABLE_15  Block 8 -100.207696  31.893165  11201010\n", "76   TABLE_15  Block 8 -100.207696  31.893247  11201009\n", "88   TABLE_15  Block 8 -100.207696  31.893327  11201008\n", "102  TABLE_15  Block 8 -100.207696  31.893408  11201007\n", "114  TABLE_15  Block 8 -100.207696  31.893490  11201006\n", "126  TABLE_15  Block 8 -100.207695  31.893573  11201005\n", "138  TABLE_15  Block 8 -100.207695  31.893656  11201004\n", "150  TABLE_15  Block 8 -100.207695  31.893737  11201003\n", "162  TABLE_15  Block 8 -100.207695  31.893820  11201002\n", "174  TABLE_15  Block 8 -100.207695  31.893896  11201001\n", "     Table No Block No   Longitude   Latitude   Pile No\n", "3    TABLE_16  Block 8 -100.207774  31.892760  11200915\n", "17   TABLE_16  Block 8 -100.207774  31.892835  11200914\n", "29   TABLE_16  Block 8 -100.207774  31.892918  11200913\n", "41   TABLE_16  Block 8 -100.207774  31.892999  11200912\n", "53   TABLE_16  Block 8 -100.207774  31.893083  11200911\n", "65   TABLE_16  Block 8 -100.207774  31.893164  11200910\n", "77   TABLE_16  Block 8 -100.207774  31.893247  11200909\n", "89   TABLE_16  Block 8 -100.207772  31.893327  11200908\n", "103  TABLE_16  Block 8 -100.207774  31.893407  11200907\n", "115  TABLE_16  Block 8 -100.207774  31.893490  11200906\n", "127  TABLE_16  Block 8 -100.207773  31.893572  11200905\n", "139  TABLE_16  Block 8 -100.207773  31.893656  11200904\n", "151  TABLE_16  Block 8 -100.207773  31.893737  11200903\n", "163  TABLE_16  Block 8 -100.207772  31.893820  11200902\n", "175  TABLE_16  Block 8 -100.207773  31.893896  11200901\n", "     Table No Block No   Longitude   Latitude   Pile No\n", "5    TABLE_17  Block 8 -100.207847  31.892761  11200815\n", "18   TABLE_17  Block 8 -100.207847  31.892835  11200814\n", "30   TABLE_17  Block 8 -100.207847  31.892919  11200813\n", "42   TABLE_17  Block 8 -100.207847  31.893000  11200812\n", "54   TABLE_17  Block 8 -100.207847  31.893082  11200811\n", "66   TABLE_17  Block 8 -100.207846  31.893165  11200810\n", "78   TABLE_17  Block 8 -100.207846  31.893247  11200809\n", "90   TABLE_17  Block 8 -100.207846  31.893328  11200808\n", "104  TABLE_17  Block 8 -100.207846  31.893408  11200807\n", "116  TABLE_17  Block 8 -100.207846  31.893491  11200806\n", "128  TABLE_17  Block 8 -100.207846  31.893573  11200805\n", "140  TABLE_17  Block 8 -100.207846  31.893655  11200804\n", "152  TABLE_17  Block 8 -100.207846  31.893738  11200803\n", "164  TABLE_17  Block 8 -100.207846  31.893820  11200802\n", "176  TABLE_17  Block 8 -100.207845  31.893897  11200801\n", "     Table No Block No   Longitude   Latitude   Pile No\n", "7    TABLE_18  Block 8 -100.207925  31.892761  11200715\n", "19   TABLE_18  Block 8 -100.207925  31.892835  11200714\n", "31   TABLE_18  Block 8 -100.207926  31.892919  11200713\n", "43   TABLE_18  Block 8 -100.207925  31.893000  11200712\n", "55   TABLE_18  Block 8 -100.207925  31.893082  11200711\n", "67   TABLE_18  Block 8 -100.207924  31.893165  11200710\n", "79   TABLE_18  Block 8 -100.207924  31.893247  11200709\n", "91   TABLE_18  Block 8 -100.207923  31.893328  11200708\n", "105  TABLE_18  Block 8 -100.207924  31.893408  11200707\n", "117  TABLE_18  Block 8 -100.207924  31.893491  11200706\n", "129  TABLE_18  Block 8 -100.207924  31.893573  11200705\n", "141  TABLE_18  Block 8 -100.207924  31.893655  11200704\n", "153  TABLE_18  Block 8 -100.207924  31.893738  11200703\n", "165  TABLE_18  Block 8 -100.207924  31.893820  11200702\n", "177  TABLE_18  Block 8 -100.207924  31.893897  11200701\n", "     Table No Block No   Longitude   Latitude   Pile No\n", "8    TABLE_19  Block 8 -100.207998  31.892760  11200615\n", "20   TABLE_19  Block 8 -100.207997  31.892835  11200614\n", "32   TABLE_19  Block 8 -100.207997  31.892918  11200613\n", "44   TABLE_19  Block 8 -100.207997  31.892999  11200612\n", "56   TABLE_19  Block 8 -100.207997  31.893083  11200611\n", "68   TABLE_19  Block 8 -100.207997  31.893164  11200610\n", "80   TABLE_19  Block 8 -100.207997  31.893246  11200609\n", "92   TABLE_19  Block 8 -100.207997  31.893328  11200608\n", "106  TABLE_19  Block 8 -100.207997  31.893408  11200607\n", "118  TABLE_19  Block 8 -100.207996  31.893491  11200606\n", "130  TABLE_19  Block 8 -100.207996  31.893573  11200605\n", "142  TABLE_19  Block 8 -100.207997  31.893655  11200604\n", "154  TABLE_19  Block 8 -100.207996  31.893738  11200603\n", "166  TABLE_19  Block 8 -100.207996  31.893820  11200602\n", "178  TABLE_19  Block 8 -100.207996  31.893896  11200601\n", "    Table No Block No   Longitude   Latitude   Pile No\n", "185  TABLE_2  Block 8 -100.207622  31.893963  11101115\n", "199  TABLE_2  Block 8 -100.207623  31.894037  11101114\n", "211  TABLE_2  Block 8 -100.207623  31.894120  11101113\n", "223  TABLE_2  Block 8 -100.207622  31.894202  11101112\n", "235  TABLE_2  Block 8 -100.207622  31.894284  11101111\n", "247  TABLE_2  Block 8 -100.207622  31.894367  11101110\n", "259  TABLE_2  Block 8 -100.207622  31.894449  11101109\n", "271  TABLE_2  Block 8 -100.207620  31.894529  11101108\n", "285  TABLE_2  Block 8 -100.207622  31.894610  11101107\n", "297  TABLE_2  Block 8 -100.207622  31.894693  11101106\n", "309  TABLE_2  Block 8 -100.207622  31.894775  11101105\n", "321  TABLE_2  Block 8 -100.207622  31.894856  11101104\n", "333  TABLE_2  Block 8 -100.207621  31.894940  11101103\n", "345  TABLE_2  Block 8 -100.207621  31.895022  11101102\n", "357  TABLE_2  Block 8 -100.207621  31.895099  11101101\n", "     Table No Block No   Longitude   Latitude   Pile No\n", "9    TABLE_20  Block 8 -100.208074  31.892760  11200515\n", "21   TABLE_20  Block 8 -100.208075  31.892835  11200514\n", "33   TABLE_20  Block 8 -100.208075  31.892918  11200513\n", "45   TABLE_20  Block 8 -100.208075  31.892999  11200512\n", "57   TABLE_20  Block 8 -100.208075  31.893083  11200511\n", "69   TABLE_20  Block 8 -100.208075  31.893165  11200510\n", "81   TABLE_20  Block 8 -100.208075  31.893247  11200509\n", "93   TABLE_20  Block 8 -100.208072  31.893328  11200508\n", "107  TABLE_20  Block 8 -100.208073  31.893408  11200507\n", "119  TABLE_20  Block 8 -100.208073  31.893491  11200506\n", "131  TABLE_20  Block 8 -100.208074  31.893573  11200505\n", "143  TABLE_20  Block 8 -100.208072  31.893656  11200504\n", "155  TABLE_20  Block 8 -100.208074  31.893738  11200503\n", "167  TABLE_20  Block 8 -100.208074  31.893820  11200502\n", "179  TABLE_20  Block 8 -100.208072  31.893896  11200501\n", "     Table No Block No   Longitude   Latitude   Pile No\n", "10   TABLE_21  Block 8 -100.208149  31.892760  11200415\n", "22   TABLE_21  Block 8 -100.208149  31.892836  11200414\n", "34   TABLE_21  Block 8 -100.208149  31.892918  11200413\n", "46   TABLE_21  Block 8 -100.208148  31.893000  11200412\n", "58   TABLE_21  Block 8 -100.208148  31.893082  11200411\n", "70   TABLE_21  Block 8 -100.208148  31.893165  11200410\n", "82   TABLE_21  Block 8 -100.208147  31.893247  11200409\n", "94   TABLE_21  Block 8 -100.208148  31.893328  11200408\n", "108  TABLE_21  Block 8 -100.208147  31.893407  11200407\n", "120  TABLE_21  Block 8 -100.208147  31.893491  11200406\n", "132  TABLE_21  Block 8 -100.208147  31.893573  11200405\n", "144  TABLE_21  Block 8 -100.208147  31.893654  11200404\n", "156  TABLE_21  Block 8 -100.208147  31.893738  11200403\n", "168  TABLE_21  Block 8 -100.208147  31.893820  11200402\n", "180  TABLE_21  Block 8 -100.208147  31.893896  11200401\n", "     Table No Block No   Longitude   Latitude   Pile No\n", "11   TABLE_22  Block 8 -100.208226  31.892761  11200315\n", "23   TABLE_22  Block 8 -100.208226  31.892836  11200314\n", "35   TABLE_22  Block 8 -100.208226  31.892919  11200313\n", "47   TABLE_22  Block 8 -100.208226  31.893000  11200312\n", "59   TABLE_22  Block 8 -100.208226  31.893082  11200311\n", "71   TABLE_22  Block 8 -100.208225  31.893165  11200310\n", "83   TABLE_22  Block 8 -100.208226  31.893247  11200309\n", "95   TABLE_22  Block 8 -100.208223  31.893328  11200308\n", "109  TABLE_22  Block 8 -100.208225  31.893408  11200307\n", "121  TABLE_22  Block 8 -100.208225  31.893492  11200306\n", "133  TABLE_22  Block 8 -100.208225  31.893573  11200305\n", "145  TABLE_22  Block 8 -100.208224  31.893655  11200304\n", "157  TABLE_22  Block 8 -100.208223  31.893738  11200303\n", "169  TABLE_22  Block 8 -100.208224  31.893820  11200302\n", "181  TABLE_22  Block 8 -100.208225  31.893897  11200301\n", "     Table No Block No   Longitude   Latitude   Pile No\n", "4    TABLE_23  Block 8 -100.208299  31.892760  11200217\n", "12   TABLE_23  Block 8 -100.208299  31.892822  11200216\n", "24   TABLE_23  Block 8 -100.208299  31.892884  11200215\n", "36   TABLE_23  Block 8 -100.208299  31.892960  11200214\n", "48   TABLE_23  Block 8 -100.208298  31.893033  11200213\n", "60   TABLE_23  Block 8 -100.208298  31.893111  11200212\n", "72   TABLE_23  Block 8 -100.208298  31.893183  11200211\n", "84   TABLE_23  Block 8 -100.208298  31.893254  11200210\n", "96   TABLE_23  Block 8 -100.208298  31.893328  11200209\n", "98   TABLE_23  Block 8 -100.208298  31.893402  11200208\n", "110  TABLE_23  Block 8 -100.208298  31.893473  11200207\n", "122  TABLE_23  Block 8 -100.208298  31.893546  11200206\n", "134  TABLE_23  Block 8 -100.208298  31.893624  11200205\n", "146  TABLE_23  Block 8 -100.208297  31.893697  11200204\n", "158  TABLE_23  Block 8 -100.208298  31.893773  11200203\n", "170  TABLE_23  Block 8 -100.208297  31.893834  11200202\n", "182  TABLE_23  Block 8 -100.208297  31.893896  11200201\n", "     Table No Block No   Longitude   Latitude   Pile No\n", "6    TABLE_24  Block 8 -100.208377  31.892760  11200117\n", "13   TABLE_24  Block 8 -100.208377  31.892822  11200116\n", "25   TABLE_24  Block 8 -100.208377  31.892883  11200115\n", "37   TABLE_24  Block 8 -100.208376  31.892960  11200114\n", "49   TABLE_24  Block 8 -100.208376  31.893033  11200113\n", "61   TABLE_24  Block 8 -100.208376  31.893110  11200112\n", "73   TABLE_24  Block 8 -100.208376  31.893184  11200111\n", "85   TABLE_24  Block 8 -100.208376  31.893254  11200110\n", "97   TABLE_24  Block 8 -100.208375  31.893328  11200109\n", "99   TABLE_24  Block 8 -100.208376  31.893402  11200108\n", "111  TABLE_24  Block 8 -100.208376  31.893473  11200107\n", "123  TABLE_24  Block 8 -100.208375  31.893547  11200106\n", "135  TABLE_24  Block 8 -100.208376  31.893624  11200105\n", "147  TABLE_24  Block 8 -100.208375  31.893697  11200104\n", "159  TABLE_24  Block 8 -100.208373  31.893773  11200103\n", "171  TABLE_24  Block 8 -100.208375  31.893835  11200102\n", "183  TABLE_24  Block 8 -100.208375  31.893897  11200101\n", "    Table No Block No   Longitude   Latitude   Pile No\n", "186  TABLE_3  Block 8 -100.207695  31.893962  11101015\n", "200  TABLE_3  Block 8 -100.207695  31.894037  11101014\n", "212  TABLE_3  Block 8 -100.207695  31.894120  11101013\n", "224  TABLE_3  Block 8 -100.207695  31.894201  11101012\n", "236  TABLE_3  Block 8 -100.207695  31.894284  11101011\n", "248  TABLE_3  Block 8 -100.207695  31.894367  11101010\n", "260  TABLE_3  Block 8 -100.207695  31.894449  11101009\n", "272  TABLE_3  Block 8 -100.207695  31.894529  11101008\n", "286  TABLE_3  Block 8 -100.207694  31.894609  11101007\n", "298  TABLE_3  Block 8 -100.207694  31.894693  11101006\n", "310  TABLE_3  Block 8 -100.207694  31.894774  11101005\n", "322  TABLE_3  Block 8 -100.207694  31.894857  11101004\n", "334  TABLE_3  Block 8 -100.207694  31.894940  11101003\n", "346  TABLE_3  Block 8 -100.207694  31.895023  11101002\n", "358  TABLE_3  Block 8 -100.207694  31.895098  11101001\n", "    Table No Block No   Longitude   Latitude   Pile No\n", "187  TABLE_4  Block 8 -100.207773  31.893962  11100915\n", "201  TABLE_4  Block 8 -100.207773  31.894037  11100914\n", "213  TABLE_4  Block 8 -100.207773  31.894120  11100913\n", "225  TABLE_4  Block 8 -100.207773  31.894202  11100912\n", "237  TABLE_4  Block 8 -100.207773  31.894285  11100911\n", "249  TABLE_4  Block 8 -100.207772  31.894367  11100910\n", "261  TABLE_4  Block 8 -100.207772  31.894450  11100909\n", "273  TABLE_4  Block 8 -100.207771  31.894529  11100908\n", "287  TABLE_4  Block 8 -100.207772  31.894609  11100907\n", "299  TABLE_4  Block 8 -100.207772  31.894693  11100906\n", "311  TABLE_4  Block 8 -100.207772  31.894774  11100905\n", "323  TABLE_4  Block 8 -100.207772  31.894858  11100904\n", "335  TABLE_4  Block 8 -100.207771  31.894940  11100903\n", "347  TABLE_4  Block 8 -100.207770  31.895022  11100902\n", "359  TABLE_4  Block 8 -100.207772  31.895098  11100901\n", "    Table No Block No   Longitude   Latitude   Pile No\n", "189  TABLE_5  Block 8 -100.207845  31.893963  11100815\n", "202  TABLE_5  Block 8 -100.207846  31.894038  11100814\n", "214  TABLE_5  Block 8 -100.207846  31.894120  11100813\n", "226  TABLE_5  Block 8 -100.207845  31.894202  11100812\n", "238  TABLE_5  Block 8 -100.207845  31.894284  11100811\n", "250  TABLE_5  Block 8 -100.207845  31.894368  11100810\n", "262  TABLE_5  Block 8 -100.207845  31.894449  11100809\n", "274  TABLE_5  Block 8 -100.207845  31.894530  11100808\n", "288  TABLE_5  Block 8 -100.207845  31.894610  11100807\n", "300  TABLE_5  Block 8 -100.207845  31.894693  11100806\n", "312  TABLE_5  Block 8 -100.207845  31.894775  11100805\n", "324  TABLE_5  Block 8 -100.207845  31.894857  11100804\n", "336  TABLE_5  Block 8 -100.207844  31.894940  11100803\n", "348  TABLE_5  Block 8 -100.207844  31.895022  11100802\n", "360  TABLE_5  Block 8 -100.207844  31.895099  11100801\n", "    Table No Block No   Longitude   Latitude   Pile No\n", "191  TABLE_6  Block 8 -100.207923  31.893963  11100715\n", "203  TABLE_6  Block 8 -100.207923  31.894038  11100714\n", "215  TABLE_6  Block 8 -100.207922  31.894119  11100713\n", "227  TABLE_6  Block 8 -100.207921  31.894202  11100712\n", "239  TABLE_6  Block 8 -100.207923  31.894284  11100711\n", "251  TABLE_6  Block 8 -100.207923  31.894368  11100710\n", "263  TABLE_6  Block 8 -100.207922  31.894449  11100709\n", "275  TABLE_6  Block 8 -100.207921  31.894530  11100708\n", "289  TABLE_6  Block 8 -100.207922  31.894610  11100707\n", "301  TABLE_6  Block 8 -100.207922  31.894693  11100706\n", "313  TABLE_6  Block 8 -100.207922  31.894775  11100705\n", "325  TABLE_6  Block 8 -100.207922  31.894857  11100704\n", "337  TABLE_6  Block 8 -100.207922  31.894940  11100703\n", "349  TABLE_6  Block 8 -100.207921  31.895023  11100702\n", "361  TABLE_6  Block 8 -100.207922  31.895099  11100701\n", "    Table No Block No   Longitude   Latitude   Pile No\n", "192  TABLE_7  Block 8 -100.207996  31.893963  11100615\n", "204  TABLE_7  Block 8 -100.207996  31.894037  11100614\n", "216  TABLE_7  Block 8 -100.207996  31.894120  11100613\n", "228  TABLE_7  Block 8 -100.207996  31.894202  11100612\n", "240  TABLE_7  Block 8 -100.207996  31.894285  11100611\n", "252  TABLE_7  Block 8 -100.207995  31.894367  11100610\n", "264  TABLE_7  Block 8 -100.207995  31.894449  11100609\n", "276  TABLE_7  Block 8 -100.207996  31.894530  11100608\n", "290  TABLE_7  Block 8 -100.207995  31.894610  11100607\n", "302  TABLE_7  Block 8 -100.207995  31.894693  11100606\n", "314  TABLE_7  Block 8 -100.207995  31.894775  11100605\n", "326  TABLE_7  Block 8 -100.207995  31.894858  11100604\n", "338  TABLE_7  Block 8 -100.207995  31.894940  11100603\n", "350  TABLE_7  Block 8 -100.207995  31.895023  11100602\n", "362  TABLE_7  Block 8 -100.207995  31.895098  11100601\n", "    Table No Block No   Longitude   Latitude   Pile No\n", "193  TABLE_8  Block 8 -100.208074  31.893963  11100515\n", "205  TABLE_8  Block 8 -100.208074  31.894037  11100514\n", "217  TABLE_8  Block 8 -100.208073  31.894120  11100513\n", "229  TABLE_8  Block 8 -100.208073  31.894202  11100512\n", "241  TABLE_8  Block 8 -100.208073  31.894286  11100511\n", "253  TABLE_8  Block 8 -100.208073  31.894367  11100510\n", "265  TABLE_8  Block 8 -100.208073  31.894449  11100509\n", "277  TABLE_8  Block 8 -100.208071  31.894530  11100508\n", "291  TABLE_8  Block 8 -100.208073  31.894610  11100507\n", "303  TABLE_8  Block 8 -100.208073  31.894693  11100506\n", "315  TABLE_8  Block 8 -100.208073  31.894775  11100505\n", "327  TABLE_8  Block 8 -100.208073  31.894858  11100504\n", "339  TABLE_8  Block 8 -100.208073  31.894940  11100503\n", "351  TABLE_8  Block 8 -100.208072  31.895022  11100502\n", "363  TABLE_8  Block 8 -100.208073  31.895098  11100501\n", "    Table No Block No   Longitude   Latitude   Pile No\n", "194  TABLE_9  Block 8 -100.208146  31.893964  11100415\n", "206  TABLE_9  Block 8 -100.208146  31.894038  11100414\n", "218  TABLE_9  Block 8 -100.208146  31.894121  11100413\n", "230  TABLE_9  Block 8 -100.208146  31.894203  11100412\n", "242  TABLE_9  Block 8 -100.208146  31.894284  11100411\n", "254  TABLE_9  Block 8 -100.208146  31.894368  11100410\n", "266  TABLE_9  Block 8 -100.208146  31.894449  11100409\n", "278  TABLE_9  Block 8 -100.208146  31.894530  11100408\n", "292  TABLE_9  Block 8 -100.208146  31.894610  11100407\n", "304  TABLE_9  Block 8 -100.208145  31.894694  11100406\n", "316  TABLE_9  Block 8 -100.208145  31.894775  11100405\n", "328  TABLE_9  Block 8 -100.208145  31.894857  11100404\n", "340  TABLE_9  Block 8 -100.208145  31.894940  11100403\n", "352  TABLE_9  Block 8 -100.208145  31.895022  11100402\n", "364  TABLE_9  Block 8 -100.208145  31.895099  11100401\n", "Excel file created: output1.xlsx\n"]}]}, {"cell_type": "code", "source": [], "metadata": {"id": "a0I3xQo4QrRZ"}, "execution_count": null, "outputs": []}]}