{"cells": [{"cell_type": "markdown", "id": "header", "metadata": {}, "source": ["# Pile Geometric Analysis - RES & RCPS Sites\n", "\n", "This notebook performs comprehensive geometric analysis of detected piles from classical ML results.\n", "\n", "**Input**: Classical ML detection results from RES and RCPS sites\n", "**Output**: Pile spacing, height, verticality measurements and QGIS visualizations\n", "\n", "**Measurements**:\n", "- Pile-to-pile spacing analysis\n", "- Pile height above ground level\n", "- Pile verticality/lean assessment\n", "- Cross-site comparison\n", "\n", "**Author**: <PERSON><PERSON><PERSON>  \n", "**Date**: August 2025"]}, {"cell_type": "code", "execution_count": null, "id": "parameters", "metadata": {"tags": ["parameters"]}, "outputs": [], "source": ["# Parameters\n", "SITES = {\n", "    'nortan_res': {\n", "        'name': 'RES Site',\n", "        'ml_results_pattern': '../modeling/pile_detection/02_ml_based/classic/output_runs/clean_validation/nortan_res_pile_detection_results_*.csv',\n", "        'point_cloud': '../../data/raw/nortan_res/pointcloud/Block_11_2m.las',\n", "        'crs': 'EPSG:32614',  # UTM Zone 14N\n", "        'expected_piles': 368\n", "    },\n", "    'althea_rcps': {\n", "        'name': 'RCPS Site', \n", "        'ml_results_pattern': '../modeling/pile_detection/02_ml_based/classic/output_runs/true_generalization/rcps_generalization_results_*.csv',\n", "        'point_cloud': '../../data/raw/althea_rpcs/pointcloud/Point_Cloud.las',\n", "        'crs': 'EPSG:32615',  # UTM Zone 15N\n", "        'expected_piles': 1359\n", "    }\n", "}\n", "\n", "OUTPUT_DIR = \"output_runs/geometric_analysis\"\n", "\n", "# Analysis parameters\n", "PILE_EXTRACTION_RADIUS = 2.0  # meters - radius around pile center\n", "MIN_PILE_POINTS = 10  # minimum points to analyze pile geometry\n", "CONFIDENCE_THRESHOLD = 0.5  # minimum confidence for geometric analysis\n", "HEIGHT_ANALYSIS_RADIUS = 1.5  # meters - radius for height measurement\n", "VERTICALITY_ANALYSIS_RADIUS = 1.0  # meters - radius for verticality analysis\n", "\n", "# Spacing analysis\n", "MAX_NEIGHBOR_DISTANCE = 50.0  # meters - maximum distance to consider as neighbor\n", "EXPECTED_SPACING_RANGE = [3.0, 12.0]  # meters - typical pile spacing range\n", "\n", "print(f\"Geometric analysis configured for {len(SITES)} sites\")\n", "print(f\"Output directory: {OUTPUT_DIR}\")"]}, {"cell_type": "code", "execution_count": null, "id": "imports", "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "import pandas as pd\n", "import laspy\n", "import glob\n", "from pathlib import Path\n", "from datetime import datetime\n", "import json\n", "import warnings\n", "warnings.filterwarnings('ignore')\n", "\n", "# Spatial analysis\n", "from scipy.spatial import cKDTree, distance_matrix\n", "from scipy.stats import describe\n", "from sklearn.linear_model import LinearRegression\n", "from sklearn.decomposition import PCA\n", "\n", "# Visualization\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "\n", "# Coordinate transformation\n", "import geopandas as gpd\n", "from shapely.geometry import Point\n", "\n", "print(\"Libraries imported successfully\")"]}, {"cell_type": "code", "execution_count": null, "id": "functions", "metadata": {}, "outputs": [], "source": ["def load_ml_detection_results(pattern):\n", "    \"\"\"Load the most recent ML detection results\"\"\"\n", "    files = glob.glob(pattern)\n", "    if not files:\n", "        raise FileNotFoundError(f\"No ML results found matching: {pattern}\")\n", "    \n", "    # Get most recent file\n", "    latest_file = max(files, key=lambda x: Path(x).stat().st_mtime)\n", "    print(f\"Loading ML results: {Path(latest_file).name}\")\n", "    \n", "    df = pd.read_csv(latest_file)\n", "    print(f\"Loaded {len(df)} detection results\")\n", "    \n", "    return df\n", "\n", "def load_point_cloud(las_path):\n", "    \"\"\"Load LAS point cloud file\"\"\"\n", "    print(f\"Loading point cloud: {Path(las_path).name}\")\n", "    las_file = laspy.read(las_path)\n", "    \n", "    # Extract coordinates\n", "    points = np.vstack([las_file.x, las_file.y, las_file.z]).T\n", "    print(f\"Loaded {len(points):,} points\")\n", "    \n", "    return points\n", "\n", "def extract_pile_region(points, pile_center, radius):\n", "    \"\"\"Extract points within radius of pile center\"\"\"\n", "    # Calculate distances to pile center\n", "    distances = np.sqrt((points[:, 0] - pile_center[0])**2 + \n", "                       (points[:, 1] - pile_center[1])**2)\n", "    \n", "    # Filter points within radius\n", "    mask = distances <= radius\n", "    pile_points = points[mask]\n", "    \n", "    return pile_points\n", "\n", "def calculate_pile_height(pile_points, ground_percentile=10):\n", "    \"\"\"Calculate pile height above ground level\"\"\"\n", "    if len(pile_points) < 3:\n", "        return np.nan\n", "    \n", "    z_values = pile_points[:, 2]\n", "    \n", "    # Estimate ground level as lower percentile\n", "    ground_level = np.percentile(z_values, ground_percentile)\n", "    \n", "    # Pile top as upper percentile\n", "    pile_top = np.percentile(z_values, 90)\n", "    \n", "    height = pile_top - ground_level\n", "    return max(0, height)  # Ensure non-negative\n", "\n", "def calculate_pile_verticality(pile_points):\n", "    \"\"\"Calculate pile lean angle from vertical\"\"\"\n", "    if len(pile_points) < 5:\n", "        return np.nan\n", "    \n", "    # Use PCA to find principal axis\n", "    pca = PCA(n_components=3)\n", "    pca.fit(pile_points)\n", "    \n", "    # First principal component is pile axis\n", "    pile_axis = pca.components_[0]\n", "    \n", "    # Calculate angle with vertical (0, 0, 1)\n", "    vertical = np.array([0, 0, 1])\n", "    \n", "    # Angle between vectors\n", "    cos_angle = np.abs(np.dot(pile_axis, vertical))\n", "    cos_angle = np.clip(cos_angle, 0, 1)  # Handle numerical errors\n", "    \n", "    angle_rad = np.arccos(cos_angle)\n", "    angle_deg = np.degrees(angle_rad)\n", "    \n", "    return angle_deg\n", "\n", "def calculate_nearest_neighbor_distances(pile_locations, max_neighbors=5):\n", "    \"\"\"Calculate distances to nearest neighbors for each pile\"\"\"\n", "    tree = cKDTree(pile_locations)\n", "    \n", "    # Find nearest neighbors (excluding self)\n", "    distances, indices = tree.query(pile_locations, k=max_neighbors+1)\n", "    \n", "    # Remove self-distance (first column)\n", "    neighbor_distances = distances[:, 1:]\n", "    \n", "    return neighbor_distances\n", "\n", "print(\"Analysis functions defined\")"]}, {"cell_type": "code", "execution_count": null, "id": "setup", "metadata": {}, "outputs": [], "source": ["# Create output directory\n", "output_dir = Path(OUTPUT_DIR)\n", "output_dir.mkdir(parents=True, exist_ok=True)\n", "\n", "# Timestamp for outputs\n", "timestamp = datetime.now().strftime(\"%Y%m%d_%H%M%S\")\n", "\n", "print(f\"Output directory created: {output_dir}\")\n", "print(f\"Analysis timestamp: {timestamp}\")\n", "\n", "# Initialize results storage\n", "all_results = {}\n", "site_summaries = {}\n", "\n", "print(\"\\n\" + \"=\"*60)\n", "print(\"PILE GEOMETRIC ANALYSIS - RES & RCPS SITES\")\n", "print(\"=\"*60)"]}, {"cell_type": "markdown", "id": "analysis_loop", "metadata": {}, "source": ["## Site-by-Site Geometric Analysis\n", "\n", "Process each site individually to extract geometric measurements from detected piles."]}, {"cell_type": "code", "execution_count": null, "id": "main_analysis", "metadata": {}, "outputs": [], "source": ["for site_id, site_config in SITES.items():\n", "    print(f\"\\n{'='*50}\")\n", "    print(f\"ANALYZING SITE: {site_config['name']} ({site_id})\")\n", "    print(f\"{'='*50}\")\n", "    \n", "    try:\n", "        # Load ML detection results\n", "        ml_results = load_ml_detection_results(site_config['ml_results_pattern'])\n", "        \n", "        # Filter for detected piles with sufficient confidence\n", "        detected_piles = ml_results[\n", "            (ml_results['predicted_pile'] == 1) & \n", "            (ml_results['confidence'] >= CONFIDENCE_THRESHOLD)\n", "        ].copy()\n", "        \n", "        print(f\"High-confidence detections: {len(detected_piles)} / {len(ml_results)}\")\n", "        print(f\"Confidence threshold: {CONFIDENCE_THRESHOLD}\")\n", "        \n", "        if len(detected_piles) == 0:\n", "            print(f\"⚠️ No high-confidence detections found for {site_id}\")\n", "            continue\n", "            \n", "        # Load point cloud\n", "        points = load_point_cloud(site_config['point_cloud'])\n", "        \n", "        # Extract pile coordinates\n", "        pile_locations = detected_piles[['utm_x', 'utm_y']].values\n", "        \n", "        print(f\"\\n📏 PILE SPACING ANALYSIS\")\n", "        print(\"-\" * 30)\n", "        \n", "        # Calculate nearest neighbor distances\n", "        neighbor_distances = calculate_nearest_neighbor_distances(pile_locations, max_neighbors=5)\n", "        \n", "        # Nearest neighbor statistics\n", "        nearest_distances = neighbor_distances[:, 0]  # Distance to closest neighbor\n", "        spacing_stats = describe(nearest_distances)\n", "        \n", "        print(f\"Nearest neighbor distances:\")\n", "        print(f\"  Mean: {spacing_stats.mean:.2f}m\")\n", "        print(f\"  Std: {np.sqrt(spacing_stats.variance):.2f}m\")\n", "        print(f\"  Min: {spacing_stats.minmax[0]:.2f}m\")\n", "        print(f\"  Max: {spacing_stats.minmax[1]:.2f}m\")\n", "        print(f\"  Median: {np.median(nearest_distances):.2f}m\")\n", "        \n", "        # Filter reasonable spacing values\n", "        reasonable_spacing = nearest_distances[\n", "            (nearest_distances >= EXPECTED_SPACING_RANGE[0]) & \n", "            (nearest_distances <= EXPECTED_SPACING_RANGE[1])\n", "        ]\n", "        \n", "        print(f\"  Reasonable spacing ({EXPECTED_SPACING_RANGE[0]}-{EXPECTED_SPACING_RANGE[1]}m): {len(reasonable_spacing)}/{len(nearest_distances)} piles\")\n", "        \n", "        if len(reasonable_spacing) > 0:\n", "            print(f\"  Typical spacing: {np.mean(reasonable_spacing):.2f} ± {np.std(reasonable_spacing):.2f}m\")\n", "        \n", "        print(f\"\\n📐 PILE HEIGHT ANALYSIS\")\n", "        print(\"-\" * 30)\n", "        \n", "        # Analyze pile heights\n", "        pile_heights = []\n", "        valid_height_count = 0\n", "        \n", "        for i, pile_center in enumerate(pile_locations):\n", "            # Extract pile region\n", "            pile_points = extract_pile_region(points, pile_center, HEIGHT_ANALYSIS_RADIUS)\n", "            \n", "            if len(pile_points) >= MIN_PILE_POINTS:\n", "                height = calculate_pile_height(pile_points)\n", "                if not np.isnan(height) and height > 0:\n", "                    pile_heights.append(height)\n", "                    valid_height_count += 1\n", "        \n", "        pile_heights = np.array(pile_heights)\n", "        \n", "        print(f\"Valid height measurements: {valid_height_count} / {len(pile_locations)}\")\n", "        \n", "        if len(pile_heights) > 0:\n", "            height_stats = describe(pile_heights)\n", "            print(f\"Pile heights:\")\n", "            print(f\"  Mean: {height_stats.mean:.2f}m\")\n", "            print(f\"  Std: {np.sqrt(height_stats.variance):.2f}m\")\n", "            print(f\"  Min: {height_stats.minmax[0]:.2f}m\")\n", "            print(f\"  Max: {height_stats.minmax[1]:.2f}m\")\n", "            print(f\"  Median: {np.median(pile_heights):.2f}m\")\n", "        else:\n", "            print(f\"⚠️ No valid height measurements obtained\")\n", "        \n", "        print(f\"\\n📊 PILE VERTICALITY ANALYSIS\")\n", "        print(\"-\" * 30)\n", "        \n", "        # Analyze pile verticality\n", "        pile_lean_angles = []\n", "        valid_verticality_count = 0\n", "        \n", "        for i, pile_center in enumerate(pile_locations):\n", "            # Extract pile region for verticality analysis\n", "            pile_points = extract_pile_region(points, pile_center, VERTICALITY_ANALYSIS_RADIUS)\n", "            \n", "            if len(pile_points) >= MIN_PILE_POINTS:\n", "                lean_angle = calculate_pile_verticality(pile_points)\n", "                if not np.isnan(lean_angle):\n", "                    pile_lean_angles.append(lean_angle)\n", "                    valid_verticality_count += 1\n", "        \n", "        pile_lean_angles = np.array(pile_lean_angles)\n", "        \n", "        print(f\"Valid verticality measurements: {valid_verticality_count} / {len(pile_locations)}\")\n", "        \n", "        if len(pile_lean_angles) > 0:\n", "            verticality_stats = describe(pile_lean_angles)\n", "            print(f\"Pile lean angles (deviation from vertical):\")\n", "            print(f\"  Mean: {verticality_stats.mean:.2f}°\")\n", "            print(f\"  Std: {np.sqrt(verticality_stats.variance):.2f}°\")\n", "            print(f\"  Min: {verticality_stats.minmax[0]:.2f}°\")\n", "            print(f\"  Max: {verticality_stats.minmax[1]:.2f}°\")\n", "            print(f\"  Median: {np.median(pile_lean_angles):.2f}°\")\n", "            \n", "            # Verticality quality assessment\n", "            excellent_vertical = np.sum(pile_lean_angles <= 2.0)\n", "            good_vertical = np.sum((pile_lean_angles > 2.0) & (pile_lean_angles <= 5.0))\n", "            poor_vertical = np.sum(pile_lean_angles > 5.0)\n", "            \n", "            print(f\"  Excellent verticality (≤2°): {excellent_vertical} ({excellent_vertical/len(pile_lean_angles)*100:.1f}%)\")\n", "            print(f\"  Good verticality (2-5°): {good_vertical} ({good_vertical/len(pile_lean_angles)*100:.1f}%)\")\n", "            print(f\"  Poor verticality (>5°): {poor_vertical} ({poor_vertical/len(pile_lean_angles)*100:.1f}%)\")\n", "        else:\n", "            print(f\"⚠️ No valid verticality measurements obtained\")\n", "        \n", "        # Store results for this site\n", "        site_results = {\n", "            'site_id': site_id,\n", "            'site_name': site_config['name'],\n", "            'total_detections': len(ml_results),\n", "            'high_confidence_detections': len(detected_piles),\n", "            'confidence_threshold': CONFIDENCE_THRESHOLD,\n", "            'spacing': {\n", "                'nearest_neighbor_distances': nearest_distances.tolist(),\n", "                'mean_spacing': float(spacing_stats.mean),\n", "                'std_spacing': float(np.sqrt(spacing_stats.variance)),\n", "                'median_spacing': float(np.median(nearest_distances)),\n", "                'reasonable_spacing_count': len(reasonable_spacing),\n", "                'reasonable_spacing_percentage': len(reasonable_spacing)/len(nearest_distances)*100\n", "            },\n", "            'heights': {\n", "                'valid_measurements': valid_height_count,\n", "                'pile_heights': pile_heights.tolist() if len(pile_heights) > 0 else [],\n", "                'mean_height': float(height_stats.mean) if len(pile_heights) > 0 else None,\n", "                'std_height': float(np.sqrt(height_stats.variance)) if len(pile_heights) > 0 else None,\n", "                'median_height': float(np.median(pile_heights)) if len(pile_heights) > 0 else None\n", "            },\n", "            'verticality': {\n", "                'valid_measurements': valid_verticality_count,\n", "                'lean_angles': pile_lean_angles.tolist() if len(pile_lean_angles) > 0 else [],\n", "                'mean_lean': float(verticality_stats.mean) if len(pile_lean_angles) > 0 else None,\n", "                'std_lean': float(np.sqrt(verticality_stats.variance)) if len(pile_lean_angles) > 0 else None,\n", "                'median_lean': float(np.median(pile_lean_angles)) if len(pile_lean_angles) > 0 else None,\n", "                'excellent_count': int(excellent_vertical) if len(pile_lean_angles) > 0 else 0,\n", "                'good_count': int(good_vertical) if len(pile_lean_angles) > 0 else 0,\n", "                'poor_count': int(poor_vertical) if len(pile_lean_angles) > 0 else 0\n", "            }\n", "        }\n", "        \n", "        all_results[site_id] = site_results\n", "        \n", "        # Create site summary\n", "        site_summaries[site_id] = {\n", "            'name': site_config['name'],\n", "            'detections': len(detected_piles),\n", "            'avg_spacing': float(spacing_stats.mean),\n", "            'avg_height': float(height_stats.mean) if len(pile_heights) > 0 else None,\n", "            'avg_lean': float(verticality_stats.mean) if len(pile_lean_angles) > 0 else None\n", "        }\n", "        \n", "        print(f\"\\n✅ {site_config['name']} analysis complete\")\n", "        \n", "    except Exception as e:\n", "        print(f\"❌ Error analyzing {site_id}: {str(e)}\")\n", "        continue\n", "\n", "print(f\"\\n{'='*60}\")\n", "print(\"SITE ANALYSIS COMPLETE\")\n", "print(f\"{'='*60}\")"]}, {"cell_type": "markdown", "id": "cross_site_comparison", "metadata": {}, "source": ["## Cross-Site Comparison\n", "\n", "Compare geometric measurements between RES and RCPS sites."]}, {"cell_type": "code", "execution_count": null, "id": "comparison", "metadata": {}, "outputs": [], "source": ["print(\"\\n\" + \"=\"*50)\n", "print(\"CROSS-SITE COMPARISON\")\n", "print(\"=\"*50)\n", "\n", "if len(site_summaries) >= 2:\n", "    print(\"\\n📊 SITE COMPARISON SUMMARY\")\n", "    print(\"-\" * 40)\n", "    \n", "    comparison_df = pd.DataFrame(site_summaries).T\n", "    print(comparison_df.to_string())\n", "    \n", "    # Detailed comparison\n", "    print(\"\\n📏 SPACING COMPARISON\")\n", "    for site_id, results in all_results.items():\n", "        spacing = results['spacing']\n", "        print(f\"{results['site_name']:12}: {spacing['mean_spacing']:.2f} ± {spacing['std_spacing']:.2f}m (median: {spacing['median_spacing']:.2f}m)\")\n", "    \n", "    print(\"\\n📐 HEIGHT COMPARISON\")\n", "    for site_id, results in all_results.items():\n", "        heights = results['heights']\n", "        if heights['mean_height'] is not None:\n", "            print(f\"{results['site_name']:12}: {heights['mean_height']:.2f} ± {heights['std_height']:.2f}m (median: {heights['median_height']:.2f}m)\")\n", "        else:\n", "            print(f\"{results['site_name']:12}: No valid height measurements\")\n", "    \n", "    print(\"\\n📊 VERTICALITY COMPARISON\")\n", "    for site_id, results in all_results.items():\n", "        verticality = results['verticality']\n", "        if verticality['mean_lean'] is not None:\n", "            print(f\"{results['site_name']:12}: {verticality['mean_lean']:.2f} ± {verticality['std_lean']:.2f}° lean\")\n", "            print(f\"{'':12}  Excellent: {verticality['excellent_count']}, Good: {verticality['good_count']}, Poor: {verticality['poor_count']}\")\n", "        else:\n", "            print(f\"{results['site_name']:12}: No valid verticality measurements\")\n", "    \n", "    # Statistical comparison\n", "    print(\"\\n🔬 STATISTICAL INSIGHTS\")\n", "    print(\"-\" * 30)\n", "    \n", "    # Compare spacing patterns\n", "    site_ids = list(all_results.keys())\n", "    if len(site_ids) == 2:\n", "        site1, site2 = site_ids\n", "        spacing1 = all_results[site1]['spacing']['mean_spacing']\n", "        spacing2 = all_results[site2]['spacing']['mean_spacing']\n", "        \n", "        spacing_diff = abs(spacing1 - spacing2)\n", "        spacing_ratio = max(spacing1, spacing2) / min(spacing1, spacing2)\n", "        \n", "        print(f\"Spacing difference: {spacing_diff:.2f}m ({spacing_ratio:.2f}x ratio)\")\n", "        \n", "        # Compare heights if available\n", "        height1 = all_results[site1]['heights']['mean_height']\n", "        height2 = all_results[site2]['heights']['mean_height']\n", "        \n", "        if height1 is not None and height2 is not None:\n", "            height_diff = abs(height1 - height2)\n", "            height_ratio = max(height1, height2) / min(height1, height2)\n", "            print(f\"Height difference: {height_diff:.2f}m ({height_ratio:.2f}x ratio)\")\n", "        \n", "        # Compare verticality if available\n", "        lean1 = all_results[site1]['verticality']['mean_lean']\n", "        lean2 = all_results[site2]['verticality']['mean_lean']\n", "        \n", "        if lean1 is not None and lean2 is not None:\n", "            lean_diff = abs(lean1 - lean2)\n", "            print(f\"Verticality difference: {lean_diff:.2f}° lean\")\n", "            \n", "            # Quality comparison\n", "            excellent1 = all_results[site1]['verticality']['excellent_count']\n", "            total1 = all_results[site1]['verticality']['valid_measurements']\n", "            excellent2 = all_results[site2]['verticality']['excellent_count']\n", "            total2 = all_results[site2]['verticality']['valid_measurements']\n", "            \n", "            if total1 > 0 and total2 > 0:\n", "                quality1 = excellent1 / total1 * 100\n", "                quality2 = excellent2 / total2 * 100\n", "                print(f\"Excellent verticality: {all_results[site1]['site_name']} {quality1:.1f}% vs {all_results[site2]['site_name']} {quality2:.1f}%\")\n", "\n", "else:\n", "    print(\"⚠️ Need at least 2 sites for comparison\")\n", "\n", "print(\"\\n✅ Cross-site comparison complete\")"]}, {"cell_type": "markdown", "id": "export_section", "metadata": {}, "source": ["## Export Results for QGIS Visualization\n", "\n", "Create CSV files with geometric measurements for spatial visualization in QGIS."]}, {"cell_type": "code", "execution_count": null, "id": "export_qgis", "metadata": {}, "outputs": [], "source": ["print(\"\\n\" + \"=\"*50)\n", "print(\"EXPORTING RESULTS FOR QGIS\")\n", "print(\"=\"*50)\n", "\n", "# Export detailed results for each site\n", "for site_id, site_config in SITES.items():\n", "    if site_id not in all_results:\n", "        continue\n", "        \n", "    print(f\"\\nExporting {site_config['name']} results...\")\n", "    \n", "    try:\n", "        # Load ML results again for coordinates\n", "        ml_results = load_ml_detection_results(site_config['ml_results_pattern'])\n", "        detected_piles = ml_results[\n", "            (ml_results['predicted_pile'] == 1) & \n", "            (ml_results['confidence'] >= CONFIDENCE_THRESHOLD)\n", "        ].copy()\n", "        \n", "        # Load point cloud for measurements\n", "        points = load_point_cloud(site_config['point_cloud'])\n", "        pile_locations = detected_piles[['utm_x', 'utm_y']].values\n", "        \n", "        # Calculate measurements for each pile\n", "        pile_data = []\n", "        \n", "        # Calculate nearest neighbor distances\n", "        neighbor_distances = calculate_nearest_neighbor_distances(pile_locations, max_neighbors=3)\n", "        nearest_distances = neighbor_distances[:, 0]\n", "        \n", "        for i, (pile_center, nearest_dist) in enumerate(zip(pile_locations, nearest_distances)):\n", "            # Extract pile region for measurements\n", "            pile_points_height = extract_pile_region(points, pile_center, HEIGHT_ANALYSIS_RADIUS)\n", "            pile_points_vert = extract_pile_region(points, pile_center, VERTICALITY_ANALYSIS_RADIUS)\n", "            \n", "            # Calculate measurements\n", "            height = calculate_pile_height(pile_points_height) if len(pile_points_height) >= MIN_PILE_POINTS else np.nan\n", "            lean_angle = calculate_pile_verticality(pile_points_vert) if len(pile_points_vert) >= MIN_PILE_POINTS else np.nan\n", "            \n", "            # Determine quality categories\n", "            spacing_quality = 'Good' if EXPECTED_SPACING_RANGE[0] <= nearest_dist <= EXPECTED_SPACING_RANGE[1] else 'Poor'\n", "            \n", "            if not np.isnan(lean_angle):\n", "                if lean_angle <= 2.0:\n", "                    verticality_quality = 'Excellent'\n", "                elif lean_angle <= 5.0:\n", "                    verticality_quality = 'Good'\n", "                else:\n", "                    verticality_quality = 'Poor'\n", "            else:\n", "                verticality_quality = 'Unknown'\n", "            \n", "            pile_data.append({\n", "                'pile_id': f'{site_id}_pile_{i}',\n", "                'site_name': site_config['name'],\n", "                'site_id': site_id,\n", "                'utm_x': pile_center[0],\n", "                'utm_y': pile_center[1],\n", "                'confidence': detected_piles.iloc[i]['confidence'],\n", "                'nearest_distance': nearest_dist,\n", "                'spacing_quality': spacing_quality,\n", "                'pile_height': height if not np.isnan(height) else None,\n", "                'lean_angle': lean_angle if not np.isnan(lean_angle) else None,\n", "                'verticality_quality': verticality_quality,\n", "                'point_count_height': len(pile_points_height),\n", "                'point_count_vert': len(pile_points_vert)\n", "            })\n", "        \n", "        # Create DataFrame\n", "        pile_df = pd.DataFrame(pile_data)\n", "        \n", "        # Convert to geographic coordinates for QGIS\n", "        geometry = [Point(xy) for xy in zip(pile_df['utm_x'], pile_df['utm_y'])]\n", "        gdf = gpd.GeoDataFrame(pile_df, geometry=geometry, crs=site_config['crs'])\n", "        \n", "        # Convert to WGS84\n", "        gdf_wgs84 = gdf.to_crs('EPSG:4326')\n", "        pile_df['longitude'] = gdf_wgs84.geometry.x\n", "        pile_df['latitude'] = gdf_wgs84.geometry.y\n", "        \n", "        # Save CSV\n", "        csv_filename = f\"{site_id}_geometric_analysis_{timestamp}.csv\"\n", "        csv_path = output_dir / csv_filename\n", "        pile_df.to_csv(csv_path, index=False)\n", "        \n", "        print(f\"  ✅ Saved: {csv_filename}\")\n", "        print(f\"     Piles: {len(pile_df)}\")\n", "        print(f\"     Columns: {', '.join(pile_df.columns)}\")\n", "        \n", "        # Summary statistics for this export\n", "        valid_heights = pile_df['pile_height'].notna().sum()\n", "        valid_verticality = pile_df['lean_angle'].notna().sum()\n", "        good_spacing = (pile_df['spacing_quality'] == 'Good').sum()\n", "        excellent_vert = (pile_df['verticality_quality'] == 'Excellent').sum()\n", "        \n", "        print(f\"     Valid heights: {valid_heights}/{len(pile_df)}\")\n", "        print(f\"     Valid verticality: {valid_verticality}/{len(pile_df)}\")\n", "        print(f\"     Good spacing: {good_spacing}/{len(pile_df)}\")\n", "        print(f\"     Excellent verticality: {excellent_vert}/{len(pile_df)}\")\n", "        \n", "    except Exception as e:\n", "        print(f\"  ❌ Error exporting {site_id}: {str(e)}\")\n", "\n", "# Save summary results\n", "summary_file = output_dir / f\"geometric_analysis_summary_{timestamp}.json\"\n", "with open(summary_file, 'w') as f:\n", "    json.dump(all_results, f, indent=2)\n", "\n", "print(f\"\\n✅ Summary saved: {summary_file.name}\")\n", "print(f\"\\n📍 QGIS VISUALIZATION READY\")\n", "print(f\"Load CSV files in QGIS with:\")\n", "print(f\"  - Coordinate system: WGS84 (EPSG:4326)\")\n", "print(f\"  - Symbology by: spacing_quality, verticality_quality\")\n", "print(f\"  - Size by: pile_height, confidence\")\n", "print(f\"  - Color by: lean_angle, nearest_distance\")\n", "\n", "print(f\"\\n🎉 GEOMETRIC ANALYSIS COMPLETE!\")\n", "print(f\"Output directory: {output_dir}\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 5}