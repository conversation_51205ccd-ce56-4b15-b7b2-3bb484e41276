{"cells": [{"cell_type": "markdown", "id": "f4036890", "metadata": {}, "source": ["# # Geometric Analysis from ML Predicted <PERSON><PERSON>\n"]}, {"cell_type": "code", "execution_count": 2, "id": "710e36f5", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Notebooks root: /Users/<USER>/Documents/GitHub/asbuilt-foundation-analysis/notebooks\n"]}], "source": ["import sys\n", "from pathlib import Path\n", "\n", "# Add the `notebooks` folder to sys.path\n", "notebooks_root = Path(__file__).resolve().parents[0] if '__file__' in globals() else Path.cwd().parents[0]\n", "print(f\"Notebooks root: {notebooks_root}\")\n", "\n", "if str(notebooks_root) not in sys.path:\n", "    sys.path.insert(0, str(notebooks_root))\n", "\n", "# Now import from shared package\n", "from shared.config import (\n", "    get_data_path,\n", "    get_output_path,\n", "    get_mlflow_tracking_uri,\n", "    find_latest_file,\n", "    get_processed_data_path\n", ")\n"]}, {"cell_type": "code", "execution_count": 3, "id": "5b168584", "metadata": {}, "outputs": [], "source": ["# Papermill parameters - these will be injected by Papermill\n", "from datetime import datetime\n", "\n", "site_name = \"trino_enel\"  # Site name for output file naming\n", "project_type = \"ENEL\"  # Options: \"ENEL\", \"USA\"\n", "method_name = \"classical_ml\"  # Method identifier for this analysis\n", "timestamp = None  # Auto-generated if None\n", "model_types = [\"random_forest\", \"gradient_boosting\", \"svm\", \"logistic_regression\"]  # Models to train\n", "\n", "# Generate timestamp if not provided\n", "if timestamp is None:\n", "    timestamp = datetime.now().strftime(\"%Y%m%d_%H%M%S\")\n", "\n", "# Standardized paths using your path functions\n", "input_data_path = get_processed_data_path(site_name, f\"modelling/{method_name}\")\n", "output_data_path = get_processed_data_path(site_name, f\"goemetric_analysis/{method_name}\")\n", "\n", "\n"]}, {"cell_type": "code", "execution_count": 4, "id": "6be8855b", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import numpy as np\n", "from sklearn.decomposition import PCA"]}, {"cell_type": "code", "execution_count": 6, "id": "c2ded018", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>sample_id</th>\n", "      <th>pile_id</th>\n", "      <th>x</th>\n", "      <th>y</th>\n", "      <th>z</th>\n", "      <th>actual_label</th>\n", "      <th>Random Forest_prediction</th>\n", "      <th>Random Forest_probability</th>\n", "      <th><PERSON> Forest_correct</th>\n", "      <th>Gradient Boosting_prediction</th>\n", "      <th>Gradient Boosting_probability</th>\n", "      <th>Gradient <PERSON>_correct</th>\n", "      <th>SVM_prediction</th>\n", "      <th>SVM_probability</th>\n", "      <th>SVM_correct</th>\n", "      <th>Logistic Regression_prediction</th>\n", "      <th>Logistic Regression_probability</th>\n", "      <th>Logistic Regression_correct</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>0</td>\n", "      <td>ifc_only_847</td>\n", "      <td>435780.184000</td>\n", "      <td>5.012103e+06</td>\n", "      <td>158.443802</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>0.571543</td>\n", "      <td>True</td>\n", "      <td>1</td>\n", "      <td>0.791723</td>\n", "      <td>True</td>\n", "      <td>0</td>\n", "      <td>0.379616</td>\n", "      <td>False</td>\n", "      <td>1</td>\n", "      <td>0.829489</td>\n", "      <td>True</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>3</td>\n", "      <td>kml_only_1264</td>\n", "      <td>435951.084288</td>\n", "      <td>5.011762e+06</td>\n", "      <td>157.518169</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>0.698655</td>\n", "      <td>True</td>\n", "      <td>1</td>\n", "      <td>0.916725</td>\n", "      <td>True</td>\n", "      <td>1</td>\n", "      <td>0.960119</td>\n", "      <td>True</td>\n", "      <td>1</td>\n", "      <td>0.596008</td>\n", "      <td>True</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>5</td>\n", "      <td>ifc_only_2975</td>\n", "      <td>436272.263000</td>\n", "      <td>5.012162e+06</td>\n", "      <td>158.283204</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>0.650405</td>\n", "      <td>True</td>\n", "      <td>1</td>\n", "      <td>0.833959</td>\n", "      <td>True</td>\n", "      <td>1</td>\n", "      <td>0.836628</td>\n", "      <td>True</td>\n", "      <td>1</td>\n", "      <td>0.843017</td>\n", "      <td>True</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>6</td>\n", "      <td>ifc_only_1108</td>\n", "      <td>436139.263000</td>\n", "      <td>5.012331e+06</td>\n", "      <td>158.419384</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>0.922643</td>\n", "      <td>True</td>\n", "      <td>1</td>\n", "      <td>0.976004</td>\n", "      <td>True</td>\n", "      <td>1</td>\n", "      <td>0.924484</td>\n", "      <td>True</td>\n", "      <td>1</td>\n", "      <td>0.857261</td>\n", "      <td>True</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>8</td>\n", "      <td>ifc_only_6923</td>\n", "      <td>435903.697000</td>\n", "      <td>5.011176e+06</td>\n", "      <td>156.696926</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>0.739202</td>\n", "      <td>True</td>\n", "      <td>1</td>\n", "      <td>0.853609</td>\n", "      <td>True</td>\n", "      <td>1</td>\n", "      <td>0.677112</td>\n", "      <td>True</td>\n", "      <td>1</td>\n", "      <td>0.732812</td>\n", "      <td>True</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   sample_id        pile_id              x             y           z  \\\n", "0          0   ifc_only_847  435780.184000  5.012103e+06  158.443802   \n", "1          3  kml_only_1264  435951.084288  5.011762e+06  157.518169   \n", "2          5  ifc_only_2975  436272.263000  5.012162e+06  158.283204   \n", "3          6  ifc_only_1108  436139.263000  5.012331e+06  158.419384   \n", "4          8  ifc_only_6923  435903.697000  5.011176e+06  156.696926   \n", "\n", "   actual_label  Random Forest_prediction  Random Forest_probability  \\\n", "0             1                         1                   0.571543   \n", "1             1                         1                   0.698655   \n", "2             1                         1                   0.650405   \n", "3             1                         1                   0.922643   \n", "4             1                         1                   0.739202   \n", "\n", "   Random Forest_correct  Gradient Boosting_prediction  \\\n", "0                   True                             1   \n", "1                   True                             1   \n", "2                   True                             1   \n", "3                   True                             1   \n", "4                   True                             1   \n", "\n", "   Gradient Boosting_probability  Gradient Boosting_correct  SVM_prediction  \\\n", "0                       0.791723                       True               0   \n", "1                       0.916725                       True               1   \n", "2                       0.833959                       True               1   \n", "3                       0.976004                       True               1   \n", "4                       0.853609                       True               1   \n", "\n", "   SVM_probability  SVM_correct  Logistic Regression_prediction  \\\n", "0         0.379616        False                               1   \n", "1         0.960119         True                               1   \n", "2         0.836628         True                               1   \n", "3         0.924484         True                               1   \n", "4         0.677112         True                               1   \n", "\n", "   Logistic Regression_probability  Logistic Regression_correct  \n", "0                         0.829489                         True  \n", "1                         0.596008                         True  \n", "2                         0.843017                         True  \n", "3                         0.857261                         True  \n", "4                         0.732812                         True  "]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["# Load predictions\n", "ml_predictions_path = input_data_path / 'predictions' / 'ml_predictions_all_models_with_coordinates.csv'\n", "predictions_df = pd.read_csv(ml_predictions_path)\n", "predictions_df.head()"]}, {"cell_type": "code", "execution_count": null, "id": "2b79925b", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>sample_id</th>\n", "      <th>pile_id</th>\n", "      <th>x</th>\n", "      <th>y</th>\n", "      <th>z</th>\n", "      <th>actual_label</th>\n", "      <th>Random Forest_prediction</th>\n", "      <th>Random Forest_probability</th>\n", "      <th><PERSON> Forest_correct</th>\n", "      <th>Gradient Boosting_prediction</th>\n", "      <th>Gradient Boosting_probability</th>\n", "      <th>Gradient <PERSON>_correct</th>\n", "      <th>SVM_prediction</th>\n", "      <th>SVM_probability</th>\n", "      <th>SVM_correct</th>\n", "      <th>Logistic Regression_prediction</th>\n", "      <th>Logistic Regression_probability</th>\n", "      <th>Logistic Regression_correct</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>0</td>\n", "      <td>ifc_only_847</td>\n", "      <td>435780.184000</td>\n", "      <td>5.012103e+06</td>\n", "      <td>158.443802</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>0.571543</td>\n", "      <td>True</td>\n", "      <td>1</td>\n", "      <td>0.791723</td>\n", "      <td>True</td>\n", "      <td>0</td>\n", "      <td>0.379616</td>\n", "      <td>False</td>\n", "      <td>1</td>\n", "      <td>0.829489</td>\n", "      <td>True</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>3</td>\n", "      <td>kml_only_1264</td>\n", "      <td>435951.084288</td>\n", "      <td>5.011762e+06</td>\n", "      <td>157.518169</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>0.698655</td>\n", "      <td>True</td>\n", "      <td>1</td>\n", "      <td>0.916725</td>\n", "      <td>True</td>\n", "      <td>1</td>\n", "      <td>0.960119</td>\n", "      <td>True</td>\n", "      <td>1</td>\n", "      <td>0.596008</td>\n", "      <td>True</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>5</td>\n", "      <td>ifc_only_2975</td>\n", "      <td>436272.263000</td>\n", "      <td>5.012162e+06</td>\n", "      <td>158.283204</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>0.650405</td>\n", "      <td>True</td>\n", "      <td>1</td>\n", "      <td>0.833959</td>\n", "      <td>True</td>\n", "      <td>1</td>\n", "      <td>0.836628</td>\n", "      <td>True</td>\n", "      <td>1</td>\n", "      <td>0.843017</td>\n", "      <td>True</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>6</td>\n", "      <td>ifc_only_1108</td>\n", "      <td>436139.263000</td>\n", "      <td>5.012331e+06</td>\n", "      <td>158.419384</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>0.922643</td>\n", "      <td>True</td>\n", "      <td>1</td>\n", "      <td>0.976004</td>\n", "      <td>True</td>\n", "      <td>1</td>\n", "      <td>0.924484</td>\n", "      <td>True</td>\n", "      <td>1</td>\n", "      <td>0.857261</td>\n", "      <td>True</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>8</td>\n", "      <td>ifc_only_6923</td>\n", "      <td>435903.697000</td>\n", "      <td>5.011176e+06</td>\n", "      <td>156.696926</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>0.739202</td>\n", "      <td>True</td>\n", "      <td>1</td>\n", "      <td>0.853609</td>\n", "      <td>True</td>\n", "      <td>1</td>\n", "      <td>0.677112</td>\n", "      <td>True</td>\n", "      <td>1</td>\n", "      <td>0.732812</td>\n", "      <td>True</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   sample_id        pile_id              x             y           z  \\\n", "0          0   ifc_only_847  435780.184000  5.012103e+06  158.443802   \n", "1          3  kml_only_1264  435951.084288  5.011762e+06  157.518169   \n", "2          5  ifc_only_2975  436272.263000  5.012162e+06  158.283204   \n", "3          6  ifc_only_1108  436139.263000  5.012331e+06  158.419384   \n", "4          8  ifc_only_6923  435903.697000  5.011176e+06  156.696926   \n", "\n", "   actual_label  Random Forest_prediction  Random Forest_probability  \\\n", "0             1                         1                   0.571543   \n", "1             1                         1                   0.698655   \n", "2             1                         1                   0.650405   \n", "3             1                         1                   0.922643   \n", "4             1                         1                   0.739202   \n", "\n", "   Random Forest_correct  Gradient Boosting_prediction  \\\n", "0                   True                             1   \n", "1                   True                             1   \n", "2                   True                             1   \n", "3                   True                             1   \n", "4                   True                             1   \n", "\n", "   Gradient Boosting_probability  Gradient Boosting_correct  SVM_prediction  \\\n", "0                       0.791723                       True               0   \n", "1                       0.916725                       True               1   \n", "2                       0.833959                       True               1   \n", "3                       0.976004                       True               1   \n", "4                       0.853609                       True               1   \n", "\n", "   SVM_probability  SVM_correct  Logistic Regression_prediction  \\\n", "0         0.379616        False                               1   \n", "1         0.960119         True                               1   \n", "2         0.836628         True                               1   \n", "3         0.924484         True                               1   \n", "4         0.677112         True                               1   \n", "\n", "   Logistic Regression_probability  Logistic Regression_correct  \n", "0                         0.829489                         True  \n", "1                         0.596008                         True  \n", "2                         0.843017                         True  \n", "3                         0.857261                         True  \n", "4                         0.732812                         True  "]}, "execution_count": 16, "metadata": {}, "output_type": "execute_result"}], "source": ["# Filter Gradient Boosting predicted piles\n", "predicted_piles = predictions_df[predictions_df['Gradient Boosting_prediction'] == 1].copy()\n", "predicted_piles.head()"]}, {"cell_type": "code", "execution_count": null, "id": "e948849f", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Predicted piles: 207\n"]}], "source": ["# Rename columns\n", "geometric_df = predicted_piles.rename(columns={\n", "    'x': 'X', 'y': 'Y', 'z': 'Z', 'pile_id': 'predicted_label'\n", "})\n", "\n", "print(f\"Predicted piles: {len(geometric_df)}\")"]}, {"cell_type": "code", "execution_count": null, "id": "76ec71c3", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "Sample pile heights:\n", "                      min_z       max_z  count  height\n", "predicted_label                                       \n", "ifc_only_10098   158.224419  158.224419      1     0.0\n", "ifc_only_10106   158.195610  158.195610      1     0.0\n", "ifc_only_10128   158.184507  158.184507      1     0.0\n", "ifc_only_10354   157.841904  157.841904      1     0.0\n", "ifc_only_10404   157.810230  157.810230      1     0.0\n"]}], "source": ["# Step 1: Height calculation per pile group\n", "pile_heights = (\n", "    geometric_df.groupby('predicted_label')\n", "    .agg(min_z=('Z', 'min'), max_z=('Z', 'max'), count=('Z', 'count'))\n", "    .assign(height=lambda d: d['max_z'] - d['min_z'])\n", ")\n", "\n", "print(\"\\nSample pile heights:\")\n", "print(pile_heights.head())"]}, {"cell_type": "code", "execution_count": null, "id": "231a6504", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "Centroids calculated: 207\n", "                     x_mean       y_mean      z_mean  pile_count\n", "predicted_label                                                 \n", "ifc_only_10098   435694.697  5012167.379  158.224419           1\n", "ifc_only_10106   435694.697  5011988.490  158.195610           1\n", "ifc_only_10128   435675.697  5012161.068  158.184507           1\n", "ifc_only_10354   435466.697  5011642.380  157.841904           1\n", "ifc_only_10404   435485.697  5011652.141  157.810230           1\n"]}], "source": ["# Step 2: Centroid calculation\n", "centroids = (\n", "    geometric_df.groupby('predicted_label')\n", "    .agg(\n", "        x_mean=('X', 'mean'),\n", "        y_mean=('Y', 'mean'),\n", "        z_mean=('Z', 'mean'),\n", "        pile_count=('X', 'count')\n", "    )\n", ")\n", "\n", "print(f\"\\nCentroids calculated: {len(centroids)}\")\n", "print(centroids.head())"]}, {"cell_type": "code", "execution_count": null, "id": "5c99f1aa", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "Distances calculated: 21321\n", "Mean: 599.69m | Min: 0.62m | Max: 1541.57m\n"]}], "source": ["# Step 3: Inter-pile distance calculation\n", "def calculate_pile_distances(centroids_df):\n", "    distances = []\n", "    ids = centroids_df.index.tolist()\n", "    \n", "    for i, id1 in enumerate(ids):\n", "        for id2 in ids[i+1:]:\n", "            x1, y1 = centroids_df.loc[id1, ['x_mean', 'y_mean']]\n", "            x2, y2 = centroids_df.loc[id2, ['x_mean', 'y_mean']]\n", "            d = np.sqrt((x2 - x1)**2 + (y2 - y1)**2)\n", "            distances.append({'pile1': id1, 'pile2': id2, 'distance': d})\n", "    \n", "    return pd.DataFrame(distances)\n", "\n", "distances_df = calculate_pile_distances(centroids)\n", "\n", "print(f\"\\nDistances calculated: {len(distances_df)}\")\n", "print(f\"Mean: {distances_df['distance'].mean():.2f}m | Min: {distances_df['distance'].min():.2f}m | Max: {distances_df['distance'].max():.2f}m\")\n"]}, {"cell_type": "code", "execution_count": null, "id": "c20ab804", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "Spatial pattern metrics:\n", "center_x: 436018.36\n", "center_y: 5011960.05\n", "mean_distance_from_center: 460.93\n", "std_distance_from_center: 186.08\n", "spatial_extent_x: 1252.07\n", "spatial_extent_y: 1538.76\n", "total_piles: 207\n"]}], "source": ["# Step 4: Spatial pattern metrics\n", "def analyze_spatial_pattern(centroids_df):\n", "    coords = centroids_df[['x_mean', 'y_mean']].values\n", "    cx, cy = coords.mean(axis=0)\n", "    dists = np.sqrt((coords[:, 0] - cx)**2 + (coords[:, 1] - cy)**2)\n", "    \n", "    return {\n", "        'center_x': cx,\n", "        'center_y': cy,\n", "        'mean_distance_from_center': dists.mean(),\n", "        'std_distance_from_center': dists.std(),\n", "        'spatial_extent_x': coords[:, 0].ptp(),\n", "        'spatial_extent_y': coords[:, 1].ptp(),\n", "        'total_piles': len(coords)\n", "    }\n", "\n", "spatial_analysis = analyze_spatial_pattern(centroids)\n", "\n", "print(\"\\nSpatial pattern metrics:\")\n", "for k, v in spatial_analysis.items():\n", "    print(f\"{k}: {v:.2f}\" if isinstance(v, float) else f\"{k}: {v}\")\n"]}, {"cell_type": "code", "execution_count": null, "id": "cb0a5c56", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "Exported:\n", "- predicted_pile_centroids.csv\n", "- inter_pile_distances.csv\n", "- spatial_pattern_analysis.csv\n"]}], "source": ["# Step 5: Export results\n", "centroids.reset_index().to_csv('predicted_pile_centroids.csv', index=False)\n", "distances_df.to_csv('inter_pile_distances.csv', index=False)\n", "pd.DataFrame([spatial_analysis]).to_csv('spatial_pattern_analysis.csv', index=False)\n", "\n", "print(\"\\nExported:\")\n", "print(\"- predicted_pile_centroids.csv\")\n", "print(\"- inter_pile_distances.csv\")\n", "print(\"- spatial_pattern_analysis.csv\")\n"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 5}